/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.visualization;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.ObjectManagerCacheStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.DASH_SEPARATOR;
import static com.mindarray.GlobalConstants.RESULT;
import static com.mindarray.TestAPIConstants.VISUALIZATION_WIDGET_API_ENDPOINT;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestVisualizationCaching
{

    private static final Logger LOGGER = new Logger(TestVisualizationCaching.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Caching Test");

    private static final String ID = CommonUtil.getString(UUID.randomUUID());

    private static final JsonArray IDS = new JsonArray();

    private static MessageConsumer<JsonObject> messageConsumer;

    private static MessageConsumer<JsonObject> consumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(GlobalConstants.ID, 10000000001050L).put(UI_EVENT_UUID, ID).put(User.USER_NAME, GlobalConstants.DEFAULT_USER)
                .put(SESSION_ID, TestUtil.getSessionId()));

        LOGGER.trace("Caching enabled:" + MotadataConfigUtil.cachingEnabled());

        LOGGER.trace("Caching time:" + MotadataConfigUtil.getVisualizationCacheRefreshTimerSeconds());

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        try
        {

            testContext.awaitCompletion(2, TimeUnit.SECONDS);
        }
        catch (Exception ignored)
        {
        }


        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSessionByWidget(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST, new JsonObject(), reply ->
        {
            if (reply.result() != null)
            {
                try
                {
                    var sessionsByWidget = reply.result().body().getJsonObject("sessions.widget");

                    Assertions.assertFalse(sessionsByWidget.isEmpty());

                    Assertions.assertTrue(sessionsByWidget.containsKey("10000000001050"));

                    Assertions.assertTrue(sessionsByWidget.getJsonObject("10000000001050").containsKey("10000000000001"));

                    Assertions.assertTrue(sessionsByWidget.getJsonObject("10000000001050").getJsonObject("10000000000001").containsKey(TestUtil.getSessionId() + GlobalConstants.COLUMN_SEPARATOR + ID));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRefreshWidget(VertxTestContext testContext) throws Exception
    {
        assertVisualizationCachingEngineTestResult(testContext);

        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(GlobalConstants.ID, 10000000001050L).put(UI_EVENT_UUID, ID).put(EVENT_CONTEXT, new JsonObject()).put(User.USER_NAME, GlobalConstants.DEFAULT_USER)
                .put(SESSION_ID, TestUtil.getSessionId()));

        for (var i = 0; i < 5; i++)
        {
            testContext.awaitCompletion(100, TimeUnit.MILLISECONDS);

            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add("1")).put("plugin", "88-linux")));

            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add("1")).put("plugin", "181-windows")));
        }
    }

    //as removed inactive event so removing it..
    /*@RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testHandleInactiveSessionEvent(VertxTestContext testContext) throws Exception
    {
        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_INACTIVE, new JsonObject().put(User.USER_NAME,GlobalConstants.SYSTEM_USER).put(SESSION_ID, TestUtil.getSessionId())
                .put(UI_EVENT_UUID, uuid).put(GlobalConstants.ID,10000000001050L));

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST,new JsonObject(),result ->
        {
            if (result.result() != null)
            {
                var sessionsByWidget = result.result().body().getJsonObject("sessions.widget");

                Assertions.assertEquals(0, sessionsByWidget.size());

                testContext.completeNow();
            }
        });
    }*/

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testRenderWidgetBySession(VertxTestContext testContext) throws Exception
    {
        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(User.USER_NAME, GlobalConstants.DEFAULT_USER).put(EVENT_TYPE, UI_ACTION_SESSION_ACTIVE).put(SESSION_ID, TestUtil.getSessionId())
                .put(UI_EVENT_UUID, ID).put(GlobalConstants.ID, 10000000001050L).put(EVENT_CONTEXT, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject()))));

        testContext.awaitCompletion(4, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST, new JsonObject(), reply ->
        {
            if (reply.result() != null)
            {
                try
                {
                    var sessionsByWidget = reply.result().body().getJsonObject("sessions.widget");

                    Assertions.assertFalse(sessionsByWidget.isEmpty());

                    Assertions.assertTrue(sessionsByWidget.containsKey("10000000001050"));

                    Assertions.assertTrue(sessionsByWidget.getJsonObject("10000000001050").containsKey("10000000000001"));

                    Assertions.assertTrue(sessionsByWidget.getJsonObject("10000000001050").getJsonObject("10000000000001").containsKey(TestUtil.getSessionId() + GlobalConstants.COLUMN_SEPARATOR + ID));

                    var sessionsByContext = reply.result().body().getJsonObject("sessions.context");

                    Assertions.assertFalse(sessionsByContext.isEmpty());

                    Assertions.assertTrue(sessionsByContext.containsKey(TestUtil.getSessionId() + GlobalConstants.COLUMN_SEPARATOR + ID));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testRefreshWidgetBySession(VertxTestContext testContext) throws Exception
    {
        assertVisualizationCachingEngineTestResult(testContext);

        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(User.USER_NAME, GlobalConstants.DEFAULT_USER).put(EVENT_TYPE, UI_ACTION_SESSION_ACTIVE).put(SESSION_ID, TestUtil.getSessionId())
                .put(UI_EVENT_UUID, ID).put(GlobalConstants.ID, 10000000001050L).put(EVENT_CONTEXT, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject()))));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        for (var i = 0; i < 5; i++)
        {
            testContext.awaitCompletion(100, TimeUnit.MILLISECONDS);

            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add("1")).put("plugin", "88-linux")));

            Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_FLUSH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(NMSConstants.OBJECT, new JsonArray().add("1")).put("plugin", "181-windows")));
        }
    }

    // BUG:- http://172.16.10.6:8080/browse/MOTADATA-768
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testNoTimeline(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var eventContext = message.body().getJsonObject(EVENT_CONTEXT);

                var result = VisualizationConstants.unpack(Buffer.buffer(eventContext.getBinary("result")), null, false, null, false, true);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": eventContext: " + eventContext.encode());

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result.encode());

                Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

                Assertions.assertTrue(eventContext.containsKey(VISUALIZATION_NAME));

                Assertions.assertTrue(eventContext.containsKey(VISUALIZATION_TIMELINE));

                Assertions.assertTrue(eventContext.containsKey(VISUALIZATION_CATEGORY));

                Assertions.assertTrue(eventContext.containsKey(VISUALIZATION_TYPE));

                Assertions.assertTrue(eventContext.containsKey(VISUALIZATION_DATA_SOURCES));

                testContext.completeNow();
            }
        });

        var promise = Promise.<Long>promise();

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_WIDGET_API_ENDPOINT, new JsonObject("{\n\"id\":-1,\n\"visualization.name\": \"asdg\",\n    \"visualization.granularity\": \"5 m\",\n    \"visualization.timeline\": {\n        \"relative.timeline\": \"today\",\n        \"visualization.time.range.inclusive\": \"no\"\n    },\n    \"visualization.category\": \"Chart\",\n    \"visualization.type\": \"Area\",\n    \"visualization.data.sources\": [\n        {\n            \"type\": \"metric\",\n            \"filters\": {\n                \"data.filter\": {},\n                \"result.filter\": {},\n                \"drill.down.filter\": {}\n            },\n            \"data.points\": [\n                {\n                    \"data.point\": \"system.cpu.percent\",\n                    \"aggregator\": \"avg\"\n                }\n            ]\n        }\n    ],\n    \"visualization.properties\": {\n        \"chart\": {\n            \"rotation.angle\": 0,\n            \"chart.legend\": \"no\",\n            \"vertical.legend\": \"no\",\n            \"line.width\": 2,\n            \"chart.label\": \"no\",\n            \"highchart.settings\": {},\n            \"sorting\": {\n                \"limit\": 10,\n                \"order\": \"desc\"\n            }\n        }\n    },\n    \"visualization.result.by\": [],\n    \"granularity\": {\n        \"value\": 5,\n        \"unit\": \"m\"\n    },\n    \"container.type\": \"dashboard\"\n}"), response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.result().bodyAsJsonObject().encode());

            promise.complete(response.result().bodyAsJsonObject().getLong("id"));
        });

        promise.future().onComplete(result ->
        {
            var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

            context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(UI_EVENT_UUID, ID);

            context.put(GlobalConstants.ID, result.result());

            context.put(VISUALIZATION_NAME, testInfo.getTestMethod().get().getName());

            TestUtil.vertx().eventBus().publish(UI_ACTION_SESSION_ACTIVE, context);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testDBQueryRequest(VertxTestContext testContext) throws InterruptedException
    {
        var queryId = 104395007325632L;

        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(VisualizationConstants.QUERY_ID, queryId).put(User.USER_NAME, GlobalConstants.DEFAULT_USER).put(EVENT_TYPE, UI_ACTION_SESSION_ACTIVE).put(SESSION_ID, TestUtil.getSessionId())
                .put(UI_EVENT_UUID, ID).put(GlobalConstants.ID, 10000000001890L).put(EVENT_CONTEXT, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject()))));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_ACKNOWLEDGEMENT, queryId);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST, new JsonObject(), reply ->
        {
            if (reply.result() != null)
            {
                var queryTickers = reply.result().body().getJsonObject("query.tickers");

                Assertions.assertFalse(queryTickers.isEmpty());

                Assertions.assertTrue(queryTickers.containsKey("104395007325632"));

                testContext.completeNow();

            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateGaugeAvailabilityWidget(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get());

        var context = new JsonObject("{\"id\":-1,\"visualization.name\":\"gauge-availability3\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Gauge\",\"visualization.type\":\"MetroTile\",\"visualization.data.sources\":[{\"type\":\"availability\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"data.points\":[{\"data.point\":\"monitor.up.count\",\"aggregator\":\"avg\"}]}],\"visualization.properties\":{\"gauge\":{\"style\":{\"chart.legend\":\"no\",\"chart.label\":\"no\",\"type\":\"number\",\"font.size\":\"small\",\"text.align\":\"left\",\"color.conditions\":[{\"color\":\"#f04e3e\"},{\"color\":\"#f58518\"},{\"color\":\"#f5bc18\"}]}}},\"visualization.result.by\":[],\"container.type\":\"dashboard\"}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - response : " + response.bodyAsJsonObject());

                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST, new JsonObject(), new DeliveryOptions().setSendTimeout(TimeUnit.MINUTES.toMillis(1)), reply ->
                    {
                        if (reply.result() != null)
                        {
                            try
                            {
                                LOGGER.info(testInfo.getTestMethod().get().getName() + " - reply : " + reply.result().body());

                                var widgets = reply.result().body().getJsonArray("realtime.widgets");

                                Assertions.assertFalse(widgets.isEmpty());

                                Assertions.assertTrue(widgets.contains(response.bodyAsJsonObject().getLong(GlobalConstants.ID)));

                                Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(GlobalConstants.ID, response.bodyAsJsonObject().getLong(GlobalConstants.ID)).put(UI_EVENT_UUID, ID).put(User.USER_NAME, GlobalConstants.DEFAULT_USER)
                                        .put(SESSION_ID, TestUtil.getSessionId()));

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                    });

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testRefreshWidgetOnAvailabilityChange(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get());

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        consumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
        {
            try
            {
                var event = message.body();

                if (event != null && event.containsKey(GlobalConstants.ID) && IDS.contains(event.getLong(GlobalConstants.ID)))
                {
                    consumer.unregister(result -> testContext.completeNow());
                }

            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });


        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH,
                new JsonObject().put(EVENT_CONTEXT, new JsonObject()
                        .put(PLUGIN, NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.AVAILABILITY.getName())))
                        .put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateHeatMapAlertWidget(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get());

        var context = new JsonObject("{\"id\":-1,\"visualization.name\":\"heatmap-alert-test\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"HeatMap\",\"visualization.type\":\"PlainHeatMap\",\"visualization.data.sources\":[{\"type\":\"policy\",\"category\":\"metric\",\"visualization.result.by\":[\"severity\"],\"filters\":{\"data.filter\":{}},\"data.points\":[{\"data.point\":\"severity\",\"aggregator\":\"count\"}]}],\"visualization.properties\":{\"map\":{\"show.counts\":\"no\"}},\"visualization.result.by\":[],\"container.type\":\"dashboard\"}");

        context.put(VisualizationConstants.VISUALIZATION_NAME, context.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - response : " + response.bodyAsJsonObject());

                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.add(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_CACHING_TEST, new JsonObject(), new DeliveryOptions().setSendTimeout(TimeUnit.MINUTES.toMillis(1)), reply ->
                    {
                        if (reply.result() != null)
                        {
                            try
                            {
                                LOGGER.info(testInfo.getTestMethod().get().getName() + " - reply : " + reply.result().body());

                                var widgets = reply.result().body().getJsonArray("realtime.widgets");

                                Assertions.assertFalse(widgets.isEmpty());

                                Assertions.assertTrue(widgets.contains(response.bodyAsJsonObject().getLong(GlobalConstants.ID)));

                                Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_SESSION_ACTIVE, new JsonObject().put(GlobalConstants.ID, response.bodyAsJsonObject().getLong(GlobalConstants.ID)).put(UI_EVENT_UUID, ID).put(User.USER_NAME, GlobalConstants.DEFAULT_USER)
                                        .put(SESSION_ID, TestUtil.getSessionId()));

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                    });

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testRefreshWidgetOnPolicyFlap(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case : " + testInfo.getTestMethod().get());

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        consumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
        {
            try
            {
                var event = message.body();

                if (event != null && event.containsKey(GlobalConstants.ID) && IDS.contains(event.getLong(GlobalConstants.ID)))
                {
                    consumer.unregister(result -> testContext.completeNow());
                }

            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(PLUGIN, DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName())).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
    }

    public void assertVisualizationCachingEngineTestResult(VertxTestContext testContext)
    {
        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }
}
