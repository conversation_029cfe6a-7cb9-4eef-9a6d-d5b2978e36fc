/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author       Notes
 *     18-Jun-2025     Ya<PERSON> T<PERSON>wari      MOTADATA-6528: Common Explorers implementation for metric, log, flow, etc | Initial Version
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Explorer;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;

import static com.mindarray.GlobalConstants.*;

public class ExplorerConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(ExplorerConfigStore.class, GlobalConstants.MOTADATA_STORE, "Explorer Config Store");

    private static final ExplorerConfigStore STORE = new ExplorerConfigStore();

    private ExplorerConfigStore()
    {
        super(DBConstants.TBL_EXPLORER, LOGGER, false);
    }

    public static ExplorerConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_EXPLORER, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                            }
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }

                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Retrieves metric explorer configurations specific to a monitor based on its ID and type.
     * This method filters through the stored metric explorer configurations and returns those that:
     * 1. Have global view disabled (NO)
     * 2. Match the specified object type
     * 3. Are either applied to the specific object ID or applied by type
     *
     * @param id   The ID of the monitor for which to retrieve metric explorer
     * @param type The type of the monitor (e.g., "server", "database", etc.)
     */
    public JsonArray getItems(long id, String type)
    {
        var items = new JsonArray();

        for (var entry : this.items.entrySet())
        {
            var item = entry.getValue();

            if (item.getString(Explorer.EXPLORER_TYPE, EMPTY_VALUE).equals(APIConstants.ExplorerType.METRIC.getName())
                    && NO.equalsIgnoreCase(item.getString(Explorer.EXPLORER_GLOBAL_VIEW_ENABLED))
                    && item.containsKey(Explorer.EXPLORER_OBJECT_TYPE)
                    && item.getString(Explorer.EXPLORER_OBJECT_TYPE).equalsIgnoreCase(type))
            {
                if (item.getLong(Explorer.EXPLORER_OBJECT_ID) == id || item.getLong(Explorer.EXPLORER_OBJECT_ID) == NOT_AVAILABLE)
                {
                    items.add(item);
                }
            }
        }

        return items;
    }
}
