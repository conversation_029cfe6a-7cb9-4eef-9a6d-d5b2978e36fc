[{"_type": "0", "id": 10000000001539, "visualization.name": "EBS Volume", "visualization.description": "EBS Volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs~volume.size.bytes", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.ebs~volume.attachment.status", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs~volume.size.bytes", "icon": {"name": "ebs", "placement": "prefix"}}, "header": {"title": "EBS Volume", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "aws.ebs~volume.size.bytes.last"}, {"label": "Attached", "value": "aws.ebs~volume.attachment.status.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001540, "visualization.name": "Large Volumes", "visualization.description": "EBS Large Volumes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs~volume.size.bytes", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs~volume.size.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Large Volumes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ebs~volume.size.bytes.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001541, "visualization.name": "Snapshots", "visualization.description": "EBS Snapshots", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.snapshot.id", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs.volume.snapshot.id", "icon": {"name": "snapshots", "placement": "prefix"}}, "header": {"title": "Snapshots", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ebs.volume.snapshot.id.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001542, "visualization.name": "EBS Volume Size", "visualization.description": "EBS Volume Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs~volume.size.bytes", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs~volume.size.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "EBS Volume Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ebs~volume.size.bytes.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001543, "visualization.name": "Avg <PERSON>", "visualization.description": "EBS Avg Queue Length", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs.volume.queue.length", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Avg <PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ebs.volume.queue.length.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001544, "visualization.name": "Average Latency", "visualization.description": "EBS Average Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.avg.read.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "aws.ebs.volume.avg.write.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ebs.volume.avg.read.latency.ms", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "Average Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.ebs.volume.avg.read.latency.ms.avg"}, {"label": "Write", "value": "aws.ebs.volume.avg.write.latency.ms.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001545, "visualization.name": "EBS Volumes By Type", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs~volume.type", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.ebs~volume.type.last"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001546, "visualization.name": "EBS Volumes By Size", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs~volume.size.bytes", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.ebs~volume.size.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001547, "visualization.name": "Top 5 Volumes By Read Bytes", "visualization.description": "Top 5 Volumes By Read Bytes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.read.bytes.per.sec", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 5, "order": "desc", "column": "aws.ebs.volume.read.bytes.per.sec.last"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001548, "visualization.name": "Top 5 Volumes By Read Operation", "visualization.description": "Top 5 Volumes By Read Operation", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.read.ops.per.sec", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "aws.ebs.volume.read.ops.per.sec.last"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001549, "visualization.name": "Top 5 Volumes By Read Latency", "visualization.description": "Top 5 Volumes By Read Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.avg.read.latency.ms", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "aws.ebs.volume.avg.read.latency.ms.last"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001550, "visualization.name": "Top 5 Volumes By Write Bytes", "visualization.description": "Top 5 Volumes By Write Bytes", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.write.bytes.per.sec", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001551, "visualization.name": "Top 5 Volumes By Write Operations", "visualization.description": "Top 5 Volumes By Write Operation", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.write.ops.per.sec", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001552, "visualization.name": "Top 5 Volumes By Write Latency", "visualization.description": "Top 5 Volumes By Write Latency", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.avg.write.latency.ms", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001553, "visualization.name": "Top 5 Volumes By Idle Time(%)", "visualization.description": "Top 5 Volumes By Idle Time(%)", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.idle.time.percent", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001554, "visualization.name": "Top 5 Volumes By Queue Length", "visualization.description": "Top 5 Volumes By Queue Length", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.queue.length", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001555, "visualization.name": "Top 5 Volumes By Burst Balance", "visualization.description": "Top 5 Volumes By Burst Balance", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.burst.balance.percent", "aggregator": "avg", "entity.type": "Group"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]