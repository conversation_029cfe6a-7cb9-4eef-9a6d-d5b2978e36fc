{"172.16.8.60": {"result": {"wildfly.version": "9.0.2.Final", "wildfly.installation.mode": "STANDALONE", "wildfly.running.mode": "NORMAL", "wildfly.server.state": "running", "started.time.sec": 28155687, "started.time": "325 days, 21 hours, 1 minute, 27 seconds", "wildfly.threads": 53, "wildfly.loaded.classes": 11081, "wildfly.unloaded.classes": 27, "wildfly.compilation.time.ms": 73697, "wildfly.collections": 95374, "wildfly.collections.time.ms": 738955, "wildfly.jdbc.pool": [{"wildfly.jdbc.pool": "ExampleDS", "wildfly.jdbc.pool.available.connections": 0, "wildfly.jdbc.pool.created.connections": 0, "wildfly.jdbc.pool.destroyed.connections": 0, "wildfly.jdbc.pool.connections": 0, "wildfly.jdbc.pool.used.connections": 0}], "wildfly.transactions": 0, "wildfly.committed.transactions": 0, "wildfly.rolledback.transactions": 0, "wildfly.application.rolledback.transactions": 0, "wildfly.heuristic.transactions": 0, "wildfly.nested.transactions": 0, "wildfly.resource.rolledback.transactions": 0, "wildfly.running.transactions": 0, "wildfly.timedout.transactions": 0, "wildfly.heap.memory.used.bytes": 128894050304, "wildfly.heap.memory.used.percent": 26.35, "wildfly.non.heap.memory.used.bytes": 128894050304, "wildfly.non.heap.memory.used.percent": 26.35, "wildfly.thread.pool": [{"wildfly.thread.pool": "default", "wildfly.thread.pool.threads": 0, "wildfly.thread.pool.max.threads": 0, "wildfly.thread.pool.busy.threads": 0, "wildfly.thread.pool.idle.threads": 0}], "wildfly.sent.bytes.rate": 0, "wildfly.received.bytes.rate": 0, "wildfly.errors": 0, "wildfly.request.latency.ms": 0, "wildfly.requests.rate": 0, "wildfly.active.sessions": 0, "wildfly.rejected.sessions": 0, "wildfly.created.sessions": 0, "wildfly.expired.sessions": 0}, "status": "succeed"}, "172.16.8.173": {"result": {"wildfly.version": "9.0.2.Final", "wildfly.installation.mode": "STANDALONE", "wildfly.running.mode": "NORMAL", "wildfly.server.state": "running", "started.time.sec": 28155687, "started.time": "325 days, 21 hours, 1 minute, 27 seconds", "wildfly.threads": 53, "wildfly.loaded.classes": 11081, "wildfly.unloaded.classes": 27, "wildfly.compilation.time.ms": 73697, "wildfly.collections": 95374, "wildfly.collections.time.ms": 738955, "wildfly.jdbc.pool": [{"wildfly.jdbc.pool": "ExampleDS", "wildfly.jdbc.pool.available.connections": 0, "wildfly.jdbc.pool.created.connections": 0, "wildfly.jdbc.pool.destroyed.connections": 0, "wildfly.jdbc.pool.connections": 0, "wildfly.jdbc.pool.used.connections": 0}], "wildfly.transactions": 0, "wildfly.committed.transactions": 0, "wildfly.rolledback.transactions": 0, "wildfly.application.rolledback.transactions": 0, "wildfly.heuristic.transactions": 0, "wildfly.nested.transactions": 0, "wildfly.resource.rolledback.transactions": 0, "wildfly.running.transactions": 0, "wildfly.timedout.transactions": 0, "wildfly.heap.memory.used.bytes": 128894050304, "wildfly.heap.memory.used.percent": 26.35, "wildfly.non.heap.memory.used.bytes": 128894050304, "wildfly.non.heap.memory.used.percent": 26.35, "wildfly.thread.pool": [{"wildfly.thread.pool": "default", "wildfly.thread.pool.threads": 0, "wildfly.thread.pool.max.threads": 0, "wildfly.thread.pool.busy.threads": 0, "wildfly.thread.pool.idle.threads": 0}], "wildfly.sent.bytes.rate": 0, "wildfly.received.bytes.rate": 0, "wildfly.errors": 0, "wildfly.request.latency.ms": 0, "wildfly.requests.rate": 0, "wildfly.active.sessions": 0, "wildfly.rejected.sessions": 0, "wildfly.created.sessions": 0, "wildfly.expired.sessions": 0}, "status": "succeed"}}