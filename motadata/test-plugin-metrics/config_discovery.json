{"************": {"_type": "1", "cli.enabled": "yes", "object.category": "Network", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "************_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "discovery", "credential.profile.name": "************", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "event.type": "plugin.engine", "plugin.engine.request": "Config", "discovery.config.credential.profiles": [{"_type": "1", "credential.profile.name": "************", "credential.profile.protocol": "SSH", "enable.password": "ospf1", "enable.prompt": "en", "file.transfer.protocol": "NONE", "object.ip": "************", "password": "ospf1", "port": 22, "prompt": "#", "timeout": 120, "username": "ospf1"}], "discovery.config.templates": [{"NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show running-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show startup-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}]}, "id": 200000000000002, "config.template.name": "Cisco"}], "enable.password": "Mind@123", "enable.prompt": "en", "file.transfer.protocol": "NONE", "config.template.name": "Cisco", "object.credential.profile": 1, "object.ip": "************", "password": "ospf1", "port": 22, "prompt": "#", "result": {"running.config": "B_10000_R_169573419.cfg", "running.config.status": "succeed", "running.config.file.content": "This is test of running config file content for ************", "startup.config": "B_10000_S_16957341.cfg", "startup.config.status": "succeed", "startup.config.file.content": "This is test of startup config file content for ************"}, "status": "succeed", "timeout": 120, "username": "motadata"}, "***********": {"_type": "1", "plugin.engine.request": "Config", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "discovery", "credential.profile.name": "***********", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "object.category": "Network", "event.type": "plugin.engine", "enable.password": "cisco", "enable.prompt": "en", "errors": [{"error.code": "MD115", "message": "Failed to execute command > terminal l"}], "file.transfer.protocol": "NONE", "config.template.name": "Cisco", "object.ip": "***********", "password": "cisco", "port": 22, "prompt": "#", "result": {"running.config": "B_10000_R_1695734109.cfg", "running.config.status": "fail", "startup.config": "B_10000_S_1695734111.cfg", "startup.config.status": "fail"}, "status": "fail", "timeout": 120, "username": "cisco"}, "fd00:1:1:1::47": {"_type": "1", "plugin.engine.request": "Config", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "discovery", "credential.profile.name": "************", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "object.category": "Network", "event.type": "plugin.engine", "discovery.config.credential.profiles": [{"_type": "1", "id": 154323314.6, "credential.profile.name": "************", "credential.profile.protocol": "SSH", "enable.password": "", "enable.prompt": "en", "file.transfer.protocol": "NONE", "object.ip": "fd00:1:1:1::47", "password": "mind123", "port": 22, "prompt": "#", "timeout": 120, "username": "mind123"}], "discovery.config.templates": [{"NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show running-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show startup-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}]}, "id": 200000000000002, "config.template.name": "Cisco"}], "enable.password": "", "enable.prompt": "en", "file.transfer.protocol": "NONE", "config.template.name": "Cisco", "object.ip": "fd00:1:1:1::47", "password": "mind123", "port": 22, "prompt": "#", "result": {"running.config": "B_10000_R_1695734119.cfg", "running.config.status": "succeed", "running.config.file.content": "This is test of running config file content: ************", "startup.config": "B_10000_S_1695734121.cfg", "startup.config.status": "succeed", "startup.config.file.content": "This is test of startup config file content: ************"}, "status": "succeed", "timeout": 120, "username": "mind123"}, "***********": {"_type": "1", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "event.timestamp": 1700819632459, "event.topic": "remote.event.processor ", "event.type": "plugin.engine", "object.category": "Network", "object.discovery.method": "REMOTE", "object.host": "ospf1.ospf.com", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf.com", "object.snmp.device.catalog": 15452526262624, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.engine.request": "Config", "snmp.check.retries": 0, "server.host": "************", "server.password": "Mind@123", "server.username": "<PERSON>ku<PERSON><PERSON>", "state": "Running", "topology.plugin.discovery": "no", "remote.address": "0:0:0:0:0:0:0:1", "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "discovery", "credential.profile.name": "***********", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "enable.password": "ospf1", "enable.prompt": "en", "file.transfer.protocol": "FTP", "config.template.name": "Cisco", "object.credential.profile": 1212, "object.ip": "***********", "password": "ospf1", "port": 22, "prompt": "#", "result": {"running.config": "B_10000_R_1695734109.cfg", "running.config.status": "succeed", "running.config.file.content": "This is test of running config file content", "startup.config": "B_10000_S_1695734111.cfg", "startup.config.status": "succeed", "startup.config.file.content": "This is test of startup config file content"}, "status": "succeed", "timeout": 120, "username": "ospf1"}, "fd00:1:1:1::47-backup": {"_type": "1", "plugin.engine.request": "Config", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "discovery", "credential.profile.name": "************", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "object.category": "Network", "event.type": "plugin.engine", "discovery.config.credential.profiles": [{"_type": "1", "id": 154323314.6, "credential.profile.name": "************", "credential.profile.protocol": "SSH", "enable.password": "", "enable.prompt": "en", "file.transfer.protocol": "NONE", "object.ip": "fd00:1:1:1::47", "password": "mind123", "port": 22, "prompt": "#", "timeout": 120, "username": "mind123"}], "discovery.config.templates": [{"NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show running-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show startup-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}]}, "id": 200000000000002, "config.template.name": "Cisco"}], "enable.password": "", "enable.prompt": "en", "file.transfer.protocol": "NONE", "config.template.name": "Cisco", "object.ip": "fd00:1:1:1::47", "password": "mind123", "port": 22, "prompt": "#", "result": {"running.config": "B_10000_R_1695734119.cfg", "running.config.status": "succeed", "running.config.file.content": "This is test of running config file content: ************", "startup.config": "B_10000_S_1695734121.cfg", "startup.config.status": "succeed", "startup.config.file.content": "This is test of startup config file content: ************"}, "status": "succeed", "timeout": 120, "username": "mind123"}, "***********-backup": {"_type": "1", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "event.timestamp": 1700819632459, "event.topic": "remote.event.processor ", "event.type": "plugin.engine", "object.category": "Network", "object.discovery.method": "REMOTE", "object.host": "ospf1.ospf.com", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf.com", "object.snmp.device.catalog": 15452526262624, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.engine.request": "Config", "snmp.check.retries": 0, "server.host": "************", "server.password": "Mind@123", "server.username": "<PERSON>ku<PERSON><PERSON>", "state": "Running", "topology.plugin.discovery": "no", "remote.address": "0:0:0:0:0:0:0:1", "config.running.baseline.file.conflict.status": "Not Applicable", "config.running.file.current.version": 1, "config.running.file.min.version": 1, "config.running.startup.file.conflict.status": "Conflict Detected", "config.startup.file.current.version": 1, "config.startup.file.min.version": 1, "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "backup", "credential.profile.name": "***********", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "discovery.config.credential.profiles": [{"_type": "1", "id": 34324134131, "credential.profile.name": "***********", "credential.profile.protocol": "SSH", "enable.password": "ospf1", "enable.prompt": "en", "file.transfer.protocol": "FTP", "object.ip": "***********", "password": "ospf1", "port": 22, "prompt": "#", "timeout": 120, "username": "ospf1"}], "discovery.config.templates": [{"NONE": {"Backup_Running": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show running-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}], "Backup_Startup": [{"operation.command": "terminal length 0", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "No Command", "operation.timeout": 2000}, {"operation.command": "show startup-config", "operation.delay.time": 100, "operation.prompt": "#", "operation.prompt.command": "LF", "operation.response.required": "yes", "operation.timeout": 2000}]}, "id": 200000000000002, "config.template.name": "Cisco"}], "enable.password": "ospf1", "enable.prompt": "en", "errors": [], "file.transfer.protocol": "FTP", "config.template.name": "Cisco", "object.credential.profile": 1212, "object.ip": "***********", "password": "ospf1", "port": 22, "prompt": "#", "status": "succeed", "timeout": 120, "username": "ospf1", "result": {"running.config": "B_10000_R_1695734109.cfg", "running.config.status": "succeed", "running.config.file.content": "This is test of running config file content: *********** - backup", "startup.config": "B_10000_S_1695734111.cfg", "startup.config.status": "succeed", "startup.config.file.content": "This is test of startup config file content: *********** - backup"}}, "***********-restore": {"_type": "1", "cli.enabled": "yes", "cli.credential.profile.status": "succeed", "config.management.status": "yes", "event.timestamp": 1700819632459, "event.topic": "remote.event.processor ", "event.type": "plugin.engine", "object.category": "Network", "object.discovery.method": "REMOTE", "object.host": "ospf1.ospf.com", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf.com", "object.snmp.device.catalog": 15452526262624, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.engine.request": "Config", "snmp.check.retries": 0, "server.host": "************", "server.password": "Mind@123", "server.username": "<PERSON>ku<PERSON><PERSON>", "state": "Running", "topology.plugin.discovery": "no", "remote.address": "0:0:0:0:0:0:0:1", "config.running.baseline.file.conflict.status": "Not Applicable", "config.running.file.current.version": 1, "config.running.file.min.version": 1, "config.running.startup.file.conflict.status": "Conflict Detected", "config.startup.file.current.version": 1, "config.startup.file.min.version": 1, "config.work.logs": {"connection": "succeed_|@@|_succeed", "enable password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "password": "_|@@|_succeed", "ping": "***********_|@@|_succeed", "port": "22_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed", "username": "ospf1_|@@|_succeed"}, "config.template": 100000000000001, "config.object": 10000, "config.operation": "restore", "credential.profile.name": "***********", "credential.profile.protocol": "SSH", "credential.profile.status": "succeed", "enable.password": "ospf1", "enable.prompt": "en", "errors": [], "file.transfer.protocol": "NONE", "config.template.name": "Cisco", "object.credential.profile": 1212, "object.ip": "***********", "password": "ospf1", "port": 22, "prompt": "#", "status": "succeed", "timeout": 120, "username": "ospf1", "result": {"status": "succeed"}}}