[{"oid.group.id": "32c03a6a-ad8d-45c4-bf27-951622826633", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "CPU & Memory Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.4.1.2021.11.10.0": "system.cpu.percent", "(.*******.4.1.2021.4.6.0 * 100) / (.*******.4.1.2021.4.5.0)": "system.memory.used.percent", ".*******.4.1.20632.2.13": "system.load.percent", ".*******.4.1.20632.2.15": "system.cpu.fan.speed", ".*******.4.1.20632.2.16": "system.cpu.temperature.celsius"}}, {"oid.group.id": "ca8f7663-144a-48d2-b579-9d4f2e9485e8", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "System Info", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 10800, "oid.group.oids": {".*******.4.1.20632.8.28": "system.serial.number", ".*******.4.1.20632.8.15": "system.operational.mode", ".*******.4.1.20632.8.25.0": "system.firmware.version", ".*******.4.1.20632.2.9": "system.encryption.enabled"}}, {"oid.group.id": "d02743d1-6980-4fee-8a81-a137d5b88956", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "Temperature Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.oids": {"*******.4.1.20632.2.16": "temperature.sensor.reading.celsius"}}, {"oid.group.id": "53994ebe-f938-4729-9b52-9a6597c313f4", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "<PERSON>", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.oids": {".*******.4.1.20632.2.14": "system.fan.speed"}}, {"oid.group.id": "a3cad454-3ab2-4fd7-a571-c6d1b2599be7", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "System Queue Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {".*******.4.1.20632.2.2": "system.inbound.waiting.messages", ".*******.4.1.20632.2.3": "system.outbound.waiting.messages", ".*******.4.1.20632.2.4": "system.deferred.messages", ".*******.4.1.20632.2.8": "system.notification.queued.messages", ".*******.4.1.20632.2.20": "system.inbound.blocked.messages", ".*******.4.1.20632.2.21": "system.inbound.blocked.daily.messages", ".*******.4.1.20632.2.22": "system.inbound.blocked.hourly.messages", ".*******.4.1.20632.2.23": "system.inbound.virus.blocked.messages", ".*******.4.1.20632.2.24": "system.inbound.virus.blocked.daily.messages", ".*******.4.1.20632.2.25": "system.inbound.virus.blocked.hourly.messages", ".*******.4.1.20632.2.26": "system.inbound.rate.controlled.messages", ".*******.4.1.20632.2.27": "system.inbound.rate.controlled.daily.messages", ".*******.4.1.20632.2.28": "system.inbound.rate.controlled.hourly.messages", ".*******.4.1.20632.2.29": "system.inbound.quarantined.messages", ".*******.4.1.20632.2.30": "system.inbound.quarantined.daily.messages", ".*******.4.1.20632.2.31": "system.inbound.quarantined.hourly.messages", ".*******.4.1.20632.2.32": "system.inbound.tagged.messages", ".*******.4.1.20632.2.33": "system.inbound.tagged.daily.messages", ".*******.4.1.20632.2.34": "system.inbound.tagged.hourly.messages", ".*******.4.1.20632.2.35": "system.inbound.allowed.messages", ".*******.4.1.20632.2.36": "system.inbound.allowed.daily.messages", ".*******.4.1.20632.2.37": "system.inbound.allowed.hourly.messages", ".*******.4.1.20632.2.38": "system.outbound.policy.blocked.messages", ".*******.4.1.20632.2.39": "system.outbound.policy.blocked.daily.messages", ".*******.4.1.20632.2.40": "system.outbound.policy.blocked.hourly.messages", ".*******.4.1.20632.2.41": "system.outbound.spam.blocked.messages", ".*******.4.1.20632.2.42": "system.outbound.spam.blocked.daily.messages", ".*******.4.1.20632.2.43": "system.outbound.spam.blocked.hourly.messages", ".*******.4.1.20632.2.44": "system.outbound.virus.blocked.messages", ".*******.4.1.20632.2.45": "system.outbound.virus.blocked.daily.messages", ".*******.4.1.20632.2.46": "system.outbound.virus.blocked.hourly.messages", ".*******.4.1.20632.2.47": "system.outbound.rate.controlled.messages", ".*******.4.1.20632.2.48": "system.outbound.rate.controlled.daily.messages", ".*******.4.1.20632.2.49": "system.outbound.rate.controlled.hourly.messages", ".*******.4.1.20632.2.50": "system.outbound.outbound.quarantined.messages", ".*******.4.1.20632.2.51": "system.outbound.outbound.quarantined.daily.messages", ".*******.4.1.20632.2.52": "system.outbound.outbound.quarantined.hourly.messages"}}, {"oid.group.id": "e7f8a9b0-1c2d-3e4f-5a6b-7c8d9e0f1a2b", "oid.group.device.type": "Email Gateway", "oid.group.type": "scalar", "oid.group.name": "Performance Metrics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {".*******.4.1.20632.2.5": "system.latency.seconds", ".*******.4.1.20632.2.11": "system.last.delivery.timestamp", ".*******.4.1.20632.2.12": "system.unique.recipients", ".*******.4.1.20632.2.53": "system.encrypted.messages", ".*******.4.1.20632.2.54": "system.encrypted.daily.messages", ".*******.4.1.20632.2.55": "system.encrypted.hourly.messages", ".*******.4.1.20632.2.56": "system.redirected.messages", ".*******.4.1.20632.2.57": "system.redirected.daily.messages", ".*******.4.1.20632.2.58": "system.redirected.hourly.messages", ".*******.4.1.20632.2.59": "system.sent.messages", ".*******.4.1.20632.2.60": "system.sent.daily.messages", ".*******.4.1.20632.2.61": "system.sent.hourly.messages", ".*******.4.1.20632.2.62": "system.domains"}}, {"oid.group.id": "b8f9c2d4-5a7e-4b9c-8d1f-3e2a5c7b9d8f", "oid.group.device.type": "Email Gateway", "oid.group.type": "scalar", "oid.group.name": "Storage Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 3600, "oid.group.oids": {".*******.4.1.20632.2.17": "system.storage.firmware.used.bytes", ".*******.4.1.20632.2.18": "system.storage.maillog.used.bytes", ".*******.4.1.20632.2.19": "system.storage.raid.status"}, "oid.group.converters": {".*******.4.1.20632.2.19": {"0": "fully_operational", "1": "degraded", "2": "rebuilding"}}}, {"oid.group.id": "a35142d1-8051-4045-aeaa-e484be1d80fc", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "VPN Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {".*******.4.1.10704.1.11": "vpn.tunnels", ".*******.4.1.10704.1.6.1.2": "vpn.tunnel.status"}, "oid.group.converters": {".*******.4.1.10704.1.6.1.2": {"-1": "down", "0": "disabled", "1": "active"}}}, {"oid.group.id": "5c214095-e344-49b0-aff2-b578418c386b", "oid.group.device.type": "Email Gateway", "oid.group.type": "tabular", "oid.group.parent.oid": ".*******.********.2.1.1", "oid.group.name": "Active Process Statistics", "oid.group.instance.count.metric.status": "yes", "oid.group.correlation.metric": "yes", "oid.group.instance.count.metric.name": "system.network.process.instances", "oid.group.polling.timeout.sec": 120, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.********.2.1.1": "system.network.process", ".*******.********.2.1.2": "system.network.process.name", ".*******.********.2.1.3": "system.network.process.id", ".*******.********.2.1.4": "system.network.process.path", ".*******.********.2.1.5": "system.network.process.description", ".*******.********.2.1.6": "system.network.process.type", ".*******.********.2.1.7": "system.network.process.status"}, "oid.group.converters": {".*******.********.2.1.6": {"1": "Unknown", "2": "OperatingSystem", "3": "DeviceDriver", "4": "Application"}, ".*******.********.2.1.7": {"1": "Running", "2": "Runnable", "3": "NotRunnable", "4": "Invalid"}}}, {"oid.group.id": "2ad202ba-94c5-41cb-94b6-fb355f3c35f9", "oid.group.device.type": "Email Gateway", "oid.group.type": "tabular", "oid.group.parent.oid": ".*******.********.3.1.1", "oid.group.name": "Installed Software Info", "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "system.softwares", "oid.group.polling.timeout.sec": 120, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.********.3.1.1": "system.software", ".*******.********.3.1.2": "system.software.name", ".*******.********.3.1.3": "system.software.id", ".*******.********.3.1.4": "system.software.type", ".*******.********.3.1.5": "system.software.installed.date"}, "oid.group.converters": {".*******.********.3.1.4": {"1": "Unknown", "2": "OperatingSystem", "3": "DeviceDriver", "4": "Application"}}}]