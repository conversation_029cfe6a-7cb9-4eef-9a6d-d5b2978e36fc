[{"id": 10000000000932, "_type": "0", "visualization.name": "IP Addresses", "visualization.description": "Windows DHCP IP Addresses", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.available.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.used.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.ip.address.pool.utilization.percent"}, {"type": "metric", "data.point": "windows.dhcp.ip.address.pool.free.percent"}, {"type": "metric", "data.point": "windows.dhcp.ip.addresses"}, {"type": "metric", "data.point": "windows.dhcp.available.ip.addresses"}, {"type": "metric", "data.point": "windows.dhcp.used.ip.addresses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.ip.addresses", "icon": {"name": "ip", "placement": "prefix"}}, "header": {"title": "IP Addresses", "style": {"font.size": "medium"}, "data.points": [{"label": "Ip Addresses", "value": "windows.dhcp.ip.addresses.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Available", "value": "windows.dhcp.available.ip.addresses.last"}, {"label": "Used", "value": "windows.dhcp.used.ip.addresses.last"}]}}}}, {"id": 10000000000933, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "Windows DHCP Scope", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.ip.address.scopes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.delay.config.scopes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.delay.offer.scopes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.ip.address.scopes"}, {"type": "metric", "data.point": "windows.dhcp.delay.config.scopes"}, {"type": "metric", "data.point": "windows.dhcp.delay.offer.scopes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.ip.address.scopes", "icon": {"name": "file-check", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Ip Address Scopes", "value": "windows.dhcp.ip.address.scopes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Config", "value": "windows.dhcp.delay.config.scopes.last"}, {"label": "Offer", "value": "windows.dhcp.delay.offer.scopes.last"}]}}}}, {"id": 10000000000934, "_type": "0", "visualization.name": "Offers", "visualization.description": "Windows DHCP Offers", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.offers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.pending.offers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.delayed.offers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.offers"}, {"type": "metric", "data.point": "windows.dhcp.pending.offers"}, {"type": "metric", "data.point": "windows.dhcp.delayed.offers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.offers", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Offers", "style": {"font.size": "medium"}, "data.points": [{"label": "DHCP Offers", "value": "windows.dhcp.offers.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Pending", "value": "windows.dhcp.pending.offers.last"}, {"label": "Delayed", "value": "windows.dhcp.delayed.offers.last"}]}}}}, {"id": 10000000000935, "_type": "0", "visualization.name": "Requests", "visualization.description": "Windows DHCP Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.dhcp.requests.last"}]}}}}, {"id": 10000000000936, "_type": "0", "visualization.name": "A<PERSON>", "visualization.description": "Windows DHCP Acks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.acks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.acks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.acks", "icon": {"name": "outgoing-messages", "placement": "prefix"}}, "header": {"title": "A<PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.dhcp.acks.last"}]}}}}, {"id": 10000000000937, "_type": "0", "visualization.name": "Nacks", "visualization.description": "Windows DHCP Nacks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.naks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.dhcp.naks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "windows.dhcp.naks", "icon": {"name": "block-messages", "placement": "prefix"}}, "header": {"title": "Nacks", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.dhcp.naks.last"}]}}}}, {"_type": "0", "id": 10000000000938, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000939, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000940, "_type": "0", "visualization.name": "IP Pool Utilization", "visualization.description": "Windows DHCP IP Pool Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.ip.address.pool.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000941, "_type": "0", "visualization.name": "DHCP Offer Details", "visualization.description": "Windows DHCP Offer Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.offers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.pending.offers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.delayed.offers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000942, "_type": "0", "visualization.name": "DHCP Requests", "visualization.description": "Windows DHCP Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000943, "_type": "0", "visualization.name": "DHCP/Negative Acknowledgement", "visualization.description": "Windows DHCP or Negative Acknowledgement", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.acks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.dhcp.naks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000944, "_type": "0", "visualization.name": "Release Count", "visualization.description": "Windows DHCP Release Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.releases", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000945, "_type": "0", "visualization.name": "DHCP Declines", "visualization.description": "Windows DHCP Declines", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.declines", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000946, "_type": "0", "visualization.name": "DHCP Discovers Count", "visualization.description": "Windows DHCP Discovers Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.dhcp.discovers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000947, "_type": "0", "visualization.name": "Scope Details", "visualization.description": "Windows DHCP Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.scope~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~subnet.mask", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~start.range", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~end.range", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~used.ip.addresses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.scope~ip.address.pool.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "dhcp.scope~subnet.mask.last", "title": "Subnet Mask", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~start.range.last", "title": "Start Range", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~end.range.last", "title": "End Range", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~used.ip.addresses.last", "title": "IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.scope~ip.address.pool.utilization.percent.last", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}]}}}, {"id": 10000000000948, "_type": "0", "visualization.name": "Client Details", "visualization.description": "Windows DHCP Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dhcp.client~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.client~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dhcp.client~scope", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.client~name.last", "title": "Client Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.client~type.last", "title": "Client Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "dhcp.client~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "ipaddress", "style": {}}, {"name": "dhcp.client~scope.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]