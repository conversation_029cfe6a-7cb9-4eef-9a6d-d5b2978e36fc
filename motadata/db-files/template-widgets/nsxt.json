[{"_type": "0", "id": 10000000004068, "visualization.name": "Distributed Firewall Details", "visualization.category": "Grid", "visualization.type": "Distributed Firewall Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.distributed.firewall.rule~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~section.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~target.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~target.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.distributed.firewall.rule~hits", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Distributed Firewall Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.distributed.firewall.rule", "title": "RULE NAME", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.distributed.firewall.rule~id.last", "title": "RULE ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.distributed.firewall.rule~name.last", "title": "RULE NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.distributed.firewall.rule~section.id.last", "title": "SECTION ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.distributed.firewall.rule~target.type.last", "title": "TARGET TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 5}, {"name": "nsxt.distributed.firewall.rule~target.name.last", "title": "TARGET NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 6}, {"name": "nsxt.distributed.firewall.rule~packets.last", "title": "PACKET COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.distributed.firewall.rule~sessions.last", "title": "SESSION COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.distributed.firewall.rule~bytes.last", "title": "BYTE COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "nsxt.distributed.firewall.rule~hits.last", "title": "HIT COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004069, "visualization.name": "Gateway Firewall Details", "visualization.category": "Grid", "visualization.type": "Gateway Firewall Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.gateway.firewall.rule~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~section.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~target.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~target.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.gateway.firewall.rule~hits", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Gateway Firewall Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.gateway.firewall.rule", "title": "RULE NAME", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.gateway.firewall.rule~name.last", "title": "RULE NAME", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.gateway.firewall.rule~id.last", "title": "RULE ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.gateway.firewall.rule~section.id.last", "title": "SECTION ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.gateway.firewall.rule~target.type.last", "title": "TARGET TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.gateway.firewall.rule~target.name.last", "title": "TARGET NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.gateway.firewall.rule~packets.last", "title": "PACKET COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.gateway.firewall.rule~sessions.last", "title": "SESSION COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.gateway.firewall.rule~bytes.last", "title": "BYTE COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "nsxt.gateway.firewall.rule~hits.last", "title": "HIT COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004070, "visualization.name": "Logical Switch Details", "visualization.category": "Grid", "visualization.type": "Logical Switch Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.logical.switch~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~admin.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~transport.zone.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~vlan", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~in.dropped.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~out.dropped.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~in.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.switch~out.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Logical Switch Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.logical.switch", "title": "ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "position": 2, "orderable": "no"}, {"name": "nsxt.logical.switch~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "nsxt.logical.switch~admin.state.last", "title": "ADMIN STATE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "type": "status", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.logical.switch~transport.zone.id.last", "title": "TRANSPORT ZONE ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.logical.switch~vlan.last", "title": "VLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.logical.switch~in.packets.last", "title": "RX PACKETS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.logical.switch~out.packets.last", "title": "TX PACKETS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.logical.switch~in.dropped.packets.last", "title": "RX DROPPED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.logical.switch~out.dropped.packets.last", "title": "TX DROPPED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "nsxt.logical.switch~in.bytes.last", "title": "RX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "nsxt.logical.switch~out.bytes.last", "title": "TX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004071, "visualization.name": "Logical Router Details", "visualization.category": "Grid", "visualization.type": "Logical Router Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.logical.router~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~edge.cluster.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~ha.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~internal.transit.network", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~external.transit.network", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.logical.router~transport.node", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Logical Router Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.logical.router", "title": "ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "position": 2, "orderable": "no"}, {"name": "nsxt.logical.router~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "nsxt.logical.router~edge.cluster.id.last", "title": "EDGE CLUSTER ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.logical.router~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.logical.router~ha.mode.last", "title": "HA MODE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.logical.router~internal.transit.network.last", "title": "INTERNAL TRANSIT NETWORK", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.logical.router~external.transit.network.last", "title": "EXTERNAL TRANSIT NETWORK", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.logical.router~transport.node.last", "title": "TRANSPORT NODE COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004072, "visualization.name": "Interface Details", "visualization.category": "Grid", "visualization.type": "Interface Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.node.network.interface~admin.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~link.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~mtu.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~in.error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~out.error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~in.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~out.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~in.dropped.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.network.interface~out.dropped.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Interface Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.node.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.node.network.interface~admin.status.last", "title": "ADMIN STATUS", "show": "yes", "sortable": "yes", "disable": "no", "type": "status", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.node.network.interface~link.status.last", "title": "LINK STATUS", "show": "yes", "sortable": "yes", "disable": "no", "type": "status", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.node.network.interface~mtu.bytes.last", "title": "MTU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.node.network.interface~mac.address.last", "title": "MAC ADDRESS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.node.network.interface~in.packets.last", "title": "RX PACKETS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.node.network.interface~out.packets.last", "title": "TX PACKETS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.node.network.interface~in.error.packets.last", "title": "RX ERRORS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.node.network.interface~out.error.packets.last", "title": "TX ERRORS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "nsxt.node.network.interface~in.bytes.last", "title": "RX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "nsxt.node.network.interface~out.bytes.last", "title": "TX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "nsxt.node.network.interface~in.dropped.packets.last", "title": "RX DROPPED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "nsxt.node.network.interface~out.dropped.packets.last", "title": "TX DROPPED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004078, "visualization.name": "Transport Node Summary", "visualization.category": "Grid", "visualization.type": "Transport Node Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.node~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~transport.zone", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~tunnel.endpoints", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~host.switches", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~tunnel.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~agent.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~physical.nics", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Transport Node Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.transport.node", "title": "ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "position": 2, "orderable": "no"}, {"name": "nsxt.transport.node~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "nsxt.transport.node~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "nsxt.transport.node~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.transport.node~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.transport.node~transport.zone.last", "title": "TRANSPORT ZONES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 6}, {"name": "nsxt.transport.node~tunnel.endpoints.last", "title": "TUNNEL ENDPOINT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 7}, {"name": "nsxt.transport.node~host.switches.last", "title": "HOST SWITCH(MULTIPLE)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 8}, {"name": "nsxt.transport.node~tunnel.status.last", "title": "TUNNEL STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 9}, {"name": "nsxt.transport.node~agent.status.last", "title": "AGENT STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 10}, {"name": "nsxt.transport.node~physical.nics.last", "title": "PHYSICAL NIC COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 11}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004073, "visualization.name": "Transport Node Performance Details", "visualization.category": "Grid", "visualization.type": "Transport Node Performance Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.node~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~connected.router", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~disk.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~memory.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~cache.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~swap.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Transport Node Performance Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.transport.node", "title": "ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 2}, {"name": "nsxt.transport.node~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "nsxt.transport.node~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "type": "status", "orderable": "yes", "position": 3}, {"name": "nsxt.transport.node~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.transport.node~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.transport.node~connected.router.last", "title": "LOGICAL ROUTERS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "port", "position": 6}, {"name": "nsxt.transport.node~disk.capacity.bytes.last", "title": "TOTAL DISK SPACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.transport.node~cpu.cores.last", "title": "CPU Cores", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.transport.node~disk.used.bytes.last", "title": "DISK USAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "nsxt.transport.node~memory.capacity.bytes.last", "title": "TOTAL MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "nsxt.transport.node~memory.used.bytes.last", "title": "USED MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "nsxt.transport.node~cache.memory.bytes.last", "title": "MEMORY CACHE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "nsxt.transport.node~swap.memory.used.bytes.last", "title": "SWAP USAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004074, "visualization.name": "Transport Zone Details", "visualization.category": "Grid", "visualization.type": "Transport Zone Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.zone~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.zone~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.zone~nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.zone~logical.switch", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Transport Zone Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.transport.zone", "title": "ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.transport.zone~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.transport.zone~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.transport.zone~nodes.last", "title": "TRANSPORT NODE COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.transport.zone~logical.switch.last", "title": "LOGICAL SWITCH COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004075, "visualization.name": "Tunnel Details", "visualization.category": "Grid", "visualization.type": "Tunnel Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.tunnel~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~transport.node.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~local.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~remote.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~remote.node.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~bfd.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~egress.interface", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.tunnel~encap", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Tunnel Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.tunnel", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.tunnel~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "type": "status", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.tunnel~transport.node.ip.address.last", "title": "TRANSPORT NODE ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.tunnel~local.ip.address.last", "title": "LOCAL IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.tunnel~remote.ip.address.last", "title": "REMOTE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.tunnel~remote.node.ip.address.last", "title": "REMOTE NODE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.tunnel~bfd.state.last", "title": "BFD STATE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "type": "status", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "nsxt.tunnel~egress.interface.last", "title": "EGRESS INTERFACE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "nsxt.tunnel~encap.last", "title": "ENCAP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004076, "visualization.name": "File System Details", "visualization.category": "Grid", "visualization.type": "File System Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.node.filesystem~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.filesystem~node.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.filesystem~mount", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.filesystem~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.filesystem~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.filesystem~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "File System Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.transport.node.filesystem", "title": "Name", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.transport.node.filesystem~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.transport.node.filesystem~node.ip.address.last", "title": "TRANSPORT NODE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.transport.node.filesystem~mount.last", "title": "MOUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.transport.node.filesystem~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.transport.node.filesystem~capacity.bytes.last", "title": "TOTAL BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.transport.node.filesystem~used.bytes.last", "title": "USED BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"id": 10000000004087, "_type": "0", "visualization.name": "CPU Utilization", "visualization.description": " CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.cpu.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004088, "_type": "0", "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.free.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.used.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.capacity.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004089, "_type": "0", "visualization.name": "File System Utilization", "visualization.description": " File System Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.file.system.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000004093, "visualization.name": "Process Details", "visualization.category": "Grid", "visualization.type": "Process Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.node.process~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.process~ppid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.process~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.process~resident.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.node.process~start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Process Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.node.process", "title": "PID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.node.process~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.node.process~ppid.last", "title": "PPID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.node.process~memory.used.bytes.last", "title": "MEMORY USED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.node.process~resident.memory.bytes.last", "title": "RESIDENT MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.node.process~start.time.last", "title": "START TIME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004079, "visualization.name": "Transport Nodes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.node.edge", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node.host", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nsxt.transport.node.edge"}, {"type": "metric", "data.point": "nsxt.transport.node.host"}]}], "visualization.properties": {"gauge": {"header": {"title": "Transport Nodes", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Edge", "value": "nsxt.transport.node.edge.last"}, {"label": "Host", "value": "nsxt.transport.node.host.last"}]}, "style": {"icon": {"name": "node-count"}, "color.data.point": "nsxt.transport.node.edge"}}}}, {"id": 10000000004080, "_type": "0", "visualization.name": "CPU CORES", "visualization.description": "NSXT CPU CORES", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nsxt.cpu.cores"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "nsxt.cpu.cores", "font.size": "medium", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "CPU Cores", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nsxt.cpu.cores.last"}]}}}}, {"_type": "0", "id": 10000000004077, "visualization.name": "BFD Summary Details", "visualization.category": "Grid", "visualization.type": "BFD Summary Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.node~bfd.down.admin.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~bfd.down.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~bfd.init.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~bfd.diagnostic.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~bfd.path.down.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.node~bfd.up.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "File System Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "nsxt.transport.node", "title": "TRANSPORT NODE", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "nsxt.transport.node~bfd.down.admin.sessions.last", "title": "BFD ADMIN DOWN COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "nsxt.transport.node~bfd.down.sessions.last", "title": "BFD DOWN COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "nsxt.transport.node~bfd.init.sessions.last", "title": "BFD INIT COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "nsxt.transport.node~bfd.diagnostic.sessions.last", "title": "BFD NODE DIAGNOSTIC COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "nsxt.transport.node~bfd.path.down.sessions.last", "title": "BFD PATH DOWN COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "nsxt.transport.node~bfd.up.sessions.last", "title": "BFD UP COUNT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004081, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.cpu.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nsxt.cpu.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nsxt.cpu.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "nsxt.cpu.used.percent"}}}}, {"_type": "0", "id": 10000000004082, "visualization.name": "File System Utilization", "visualization.description": "File System Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.file.system.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nsxt.file.system.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "File System Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nsxt.file.system.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "files"}, "color.data.point": "nsxt.file.system.used.percent"}}}}, {"_type": "0", "id": 10000000004083, "visualization.name": "Memory (%)", "visualization.description": "Memory Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.free.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.used.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nsxt.memory.used.percent"}, {"type": "metric", "data.point": "nsxt.free.memory.bytes"}, {"type": "metric", "data.point": "nsxt.used.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "memory"}, "color.data.point": "nsxt.memory.used.percent"}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Used", "value": "nsxt.used.memory.bytes.last"}, {"label": "Free", "value": "nsxt.free.memory.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nsxt.memory.used.percent.last", "type": "gauge"}]}}}}, {"id": 10000000004084, "_type": "0", "visualization.name": "Up Time", "visualization.description": "UpTime", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "started.time.seconds"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "started.time.seconds", "font.size": "medium", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "Up Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "started.time.seconds.last"}]}}}}, {"id": 10000000004090, "_type": "0", "visualization.name": "Transport Node Availability", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.up.nodes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.degraded.nodes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.down.nodes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.unknown.nodes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004091, "_type": "0", "visualization.name": "Transport Zone Availability", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.transport.up.zones", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.degraded.zones", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.down.zones", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.transport.unknown.zones", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004092, "_type": "0", "visualization.name": "Tunnel Availability", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nsxt.up.tunnels", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nsxt.down.tunnels", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000004085, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000004086, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}]