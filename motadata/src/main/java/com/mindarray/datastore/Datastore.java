/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.*;
import io.vertx.core.json.JsonObject;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Calendar;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicLong;

public class Datastore
{
    private static final Logger LOGGER = new Logger(Datastore.class, GlobalConstants.MOTADATA_EVENT_BUS, "Datastore");
    private final String path; //path for get current event directory
    private final Calendar calendar;  //calendar for get current second
    private final Map<Long, EventFile> eventFiles;//store all files by time order (milliseconds)
    private final JsonObject closedEventFiles = new JsonObject();
    private byte[] bytes;

    protected Datastore(String path)
    {
        this.path = path;

        eventFiles = new TreeMap<>();

        this.calendar = Calendar.getInstance();

        this.bytes = new byte[8 * 1024];
    }

    protected void put(JsonObject event) throws Exception
    {
        try
        {
            var timestamp = event.getLong(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());

            if (timestamp > 0)
            {
                calendar.clear();

                calendar.setTimeInMillis(timestamp * 1000); //seconds to millis

                calendar.set(Calendar.MILLISECOND, 0);

                calendar.set(Calendar.SECOND, 0);

                timestamp = calendar.getTimeInMillis();

                var eventFile = eventFiles.get(timestamp);

                if (eventFile == null)
                {
                    if (closedEventFiles.containsKey(CommonUtil.getString(timestamp)))
                    {
                        LOGGER.info(String.format("single file loaded of previous timestamp in sec : %s , timestamp : %s ", event.getLong(EventBusConstants.EVENT_TIMESTAMP), timestamp));

                        load(timestamp);

                        eventFile = eventFiles.get(timestamp);
                    }
                    else
                    {
                        var file = new File(path + GlobalConstants.PATH_SEPARATOR + timestamp + ".dat");

                        var fileOutputStream = new FileOutputStream(file, true);

                        var fileInputStream = new FileInputStream(file);

                        var bufferedOutputStream = new BufferedOutputStream(fileOutputStream, bytes.length);

                        eventFile = new EventFile(bufferedOutputStream, fileInputStream, fileOutputStream, new AtomicLong(0L), new AtomicLong(0L));

                        eventFiles.put(timestamp, eventFile);
                    }
                }

                var bufferBytes = CodecUtil.compress(event.encode());

                if (bufferBytes.length + 4 > this.bytes.length)
                {
                    this.bytes = new byte[bufferBytes.length + 4]; //if buffer bytes is larger than existing bytes then we must need to expand for the future read purpose...
                }

                ByteUtil.writeInt(this.bytes, bufferBytes.length, 0);

                eventFile.bufferedOutputStream.write(bytes, 0, 4);

                eventFile.bufferedOutputStream.write(bufferBytes, 0, bufferBytes.length);

                eventFile.bufferedOutputStream.flush();

                eventFile.events.incrementAndGet(); //increment total events by file

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("%s file pending events %s", timestamp, eventFile.events.get()));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected byte[] get() throws Exception
    {
        byte[] bufferBytes;

        try
        {
            for (var iterator = eventFiles.entrySet().iterator(); iterator.hasNext(); )
            {
                var entry = iterator.next();

                var value = entry.getValue();

                try
                {
                    var length = value.fileInputStream.read(bytes, 0, 4);

                    if (length != 4) //means invalid file ... delete it skip to next file.
                    {
                        value.fileInputStream.close();

                        value.bufferedOutputStream.close();

                        value.fileOutputStream.close();

                        Files.deleteIfExists(Paths.get(path + GlobalConstants.PATH_SEPARATOR + entry.getKey() + ".dat"));

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("%s file deleted due to file pointer reached EOF", entry.getKey()));
                        }

                        iterator.remove();

                        closedEventFiles.remove(CommonUtil.getString(entry.getKey()));
                    }
                    else
                    {
                        length = ByteUtil.readIntValue(bytes);

                        if (length > this.bytes.length)
                        {
                            this.bytes = new byte[length]; //if buffer bytes is larger than existing bytes then we must need to expand for the future read purpose...
                        }

                        if (length != value.fileInputStream.read(bytes, 0, length)) //means invalid file ... delete it skip to next file.
                        {
                            value.fileInputStream.close();

                            value.bufferedOutputStream.close();

                            value.fileOutputStream.close();

                            Files.deleteIfExists(Paths.get(path + GlobalConstants.PATH_SEPARATOR + entry.getKey() + ".dat"));

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("%s file deleted due to invalid file", entry.getKey()));
                            }

                            closedEventFiles.remove(CommonUtil.getString(entry.getKey()));

                            iterator.remove();
                        }
                        else
                        {
                            value.offset.addAndGet(length + 4L); //mark read position

                            bufferBytes = CodecUtil.toBytes(bytes, 0, length); //plain  uncompressed bytes...

                            value.events.decrementAndGet(); //increment total events by file

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("%s file remaining events %s", entry.getKey(), value.events.get()));
                            }

                            return bufferBytes;
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.warn(String.format("corrupted event received in %s", path + GlobalConstants.PATH_SEPARATOR + entry.getKey()));

                    LOGGER.error(exception);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return null;
    }

    protected long load()
    {
        var counts = 0L;

        var bookmark = new File(path + GlobalConstants.PATH_SEPARATOR + "bookmarks");

        try
        {
            if (bookmark.exists())
            {
                var value = Files.readString(bookmark.toPath(), StandardCharsets.UTF_8);

                if (CommonUtil.isNotNullOrEmpty(value))
                {
                    for (var entry : new JsonObject(value))
                    {
                        var eventFile = new File(path + GlobalConstants.PATH_SEPARATOR + CommonUtil.getString(entry.getKey()) + ".dat");

                        if (eventFile.exists())
                        {
                            try
                            {
                                var fileOutputStream = new FileOutputStream(eventFile, true);

                                var fileInputStream = new FileInputStream(eventFile);

                                var offset = CommonUtil.getLong(entry.getValue().toString().split("_")[1]);

                                var events = CommonUtil.getLong(entry.getValue().toString().split("_")[0]);

                                LOGGER.info(String.format("%s event file loaded with %s events and offset %s", entry.getKey(), events, offset));

                                counts += events;

                                fileInputStream.skip(offset);

                                this.eventFiles.put(CommonUtil.getLong(entry.getKey()), new EventFile(new BufferedOutputStream(fileOutputStream, bytes.length), fileInputStream, fileOutputStream, new AtomicLong(offset), new AtomicLong(events)));
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return counts; //return total pending events...
    }

    protected void load(long name)
    {
        var file = new File(path + GlobalConstants.PATH_SEPARATOR + CommonUtil.getString(name) + ".dat");

        if (file.exists())
        {
            try
            {
                var fileOutputStream = new FileOutputStream(file, true);

                var fileInputStream = new FileInputStream(file);

                var offset = CommonUtil.getLong(closedEventFiles.getString(CommonUtil.getString(name)).split("_")[1]);

                var events = CommonUtil.getLong(closedEventFiles.getString(CommonUtil.getString(name)).split("_")[0]);

                LOGGER.info(String.format("%s event file loaded with %s events and offset %s", CommonUtil.getString(name), events, offset));

                fileInputStream.skip(offset);

                this.eventFiles.put(name, new EventFile(new BufferedOutputStream(fileOutputStream, bytes.length), fileInputStream, fileOutputStream, new AtomicLong(offset), new AtomicLong(events)));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }
    }

    protected void save()
    {
        try
        {
            eventFiles.forEach((key, value) -> closedEventFiles.put(CommonUtil.getString(key), value.events.get() + "_" + value.offset.get()));

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("saved event files %s and bookmarks size %s at %s ", eventFiles.size(), closedEventFiles.size(), path));

            }

            Bootstrap.vertx().fileSystem().writeFileBlocking(path + GlobalConstants.PATH_SEPARATOR + "bookmarks", closedEventFiles.toBuffer());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected void close()
    {
        try
        {
            save();

            for (var entry : eventFiles.entrySet())
            {
                entry.getValue().fileInputStream.close();

                entry.getValue().bufferedOutputStream.close();

                entry.getValue().fileOutputStream.close();
            }

            eventFiles.clear();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected void remove(long eventFile)
    {
        try
        {
            if (eventFiles.containsKey(eventFile))
            {
                eventFiles.get(eventFile).fileInputStream.close();

                eventFiles.get(eventFile).bufferedOutputStream.close();

                eventFiles.get(eventFile).fileOutputStream.close();

                eventFiles.remove(eventFile);
            }

            Bootstrap.vertx().fileSystem().deleteBlocking(path + GlobalConstants.PATH_SEPARATOR + eventFile + ".dat");

            LOGGER.info(String.format("event file %s removed due to retention", eventFile));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private
    record EventFile(BufferedOutputStream bufferedOutputStream, FileInputStream fileInputStream,
                     FileOutputStream fileOutputStream, AtomicLong offset, AtomicLong events)
    {

    }
}