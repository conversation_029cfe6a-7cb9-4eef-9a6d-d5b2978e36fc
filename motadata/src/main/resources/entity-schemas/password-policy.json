{"entity": "Password Policy", "table": "tbl_config_password_policy", "version": "1.0", "author": "<PERSON>", "props": [{"name": "password.policy.password.expiry", "title": "Password Expiry", "type": "string", "rules": ["required"], "value": ["yes", "no"]}, {"name": "password.policy.password.expiry.days", "title": "Password Expiry Days", "type": "numeric", "prerequisites": [{"rule": "password.policy.password.expiry", "value": "yes"}], "rules": ["required", "range"], "value": [1, 60]}, {"name": "password.policy.uppercase.check", "title": "Uppercase Check", "type": "string", "rules": ["required"], "value": ["yes", "no"]}, {"name": "password.policy.lowercase.check", "title": "Lowercase Check", "type": "string", "rules": ["required"], "value": ["yes", "no"]}, {"name": "password.policy.special.character.check", "title": "Special Character Check", "type": "string", "rules": ["required"], "value": ["yes", "no"]}, {"name": "password.policy.number.check", "title": "Number Check", "type": "string", "rules": ["required"], "value": ["yes", "no"]}, {"name": "password.policy.password.minimum.length", "title": "Password Minimum Length", "type": "numeric", "rules": ["required", "range"], "value": [6, 15]}], "entries": [{"type": "inline", "records": [{"id": 10000000000001, "password.policy.password.expiry": "yes", "password.policy.password.expiry.days": 15, "password.policy.uppercase.check": "yes", "password.policy.lowercase.check": "yes", "password.policy.number.check": "yes", "password.policy.special.character.check": "yes", "password.policy.password.minimum.length": 8}], "version": "1.0"}]}