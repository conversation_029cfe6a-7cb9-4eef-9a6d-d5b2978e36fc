{"172.16.8.238": {"result": {"websphere.servlet": [{"websphere.servlet": "DefaultApplication#DefaultWebApplication.war", "websphere.servlet.application.name": "DefaultApplication", "websphere.servlet.name": "DefaultWebApplication.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "filetransferSecured#filetransfer.war", "websphere.servlet.application.name": "filetransferSecured", "websphere.servlet.name": "filetransfer.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "ibmasyncrsp#ibmasyncrsp.war", "websphere.servlet.application.name": "ibmasyncrsp", "websphere.servlet.name": "ibmasyncrsp.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#ISCAdminPortlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "ISCAdminPortlet.war", "websphere.servlet.created.sessions": 1, "websphere.servlet.invalidated.sessions": 1, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 1, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 1, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#WIMPortlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "WIMPortlet.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#adminredirector.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "adminredirector.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#iehs.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "iehs.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#isclite.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "isclite.war", "websphere.servlet.created.sessions": 10, "websphere.servlet.invalidated.sessions": 10, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 10, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 4, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#wasportlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "wasportlet.war", "websphere.servlet.created.sessions": 1, "websphere.servlet.invalidated.sessions": 1, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 1, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 1, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "ivtApp#ivt_app.war", "websphere.servlet.application.name": "ivtApp", "websphere.servlet.name": "ivt_app.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "perfServletApp#perfServletApp.war", "websphere.servlet.application.name": "perfServletApp", "websphere.servlet.name": "perfServletApp.war", "websphere.servlet.created.sessions": 1026, "websphere.servlet.invalidated.sessions": 1026, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 1026, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 1026, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}]}}, "172.16.9.145": {"result": {"websphere.servlet": [{"websphere.servlet": "DefaultApplication#DefaultWebApplication.war", "websphere.servlet.application.name": "DefaultApplication", "websphere.servlet.name": "DefaultWebApplication.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "filetransferSecured#filetransfer.war", "websphere.servlet.application.name": "filetransferSecured", "websphere.servlet.name": "filetransfer.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "ibmasyncrsp#ibmasyncrsp.war", "websphere.servlet.application.name": "ibmasyncrsp", "websphere.servlet.name": "ibmasyncrsp.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#ISCAdminPortlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "ISCAdminPortlet.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#WIMPortlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "WIMPortlet.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#adminredirector.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "adminredirector.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#isclite.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "isclite.war", "websphere.servlet.created.sessions": 1, "websphere.servlet.invalidated.sessions": 1, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 1, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 1, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#kc.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "kc.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "isclite#wasportlet.war", "websphere.servlet.application.name": "isclite", "websphere.servlet.name": "wasportlet.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "ivtApp#ivt_app.war", "websphere.servlet.application.name": "ivtApp", "websphere.servlet.name": "ivt_app.war", "websphere.servlet.created.sessions": 0, "websphere.servlet.invalidated.sessions": 0, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 0, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}, {"websphere.servlet": "perfServletApp#perfServletApp.war", "websphere.servlet.application.name": "perfServletApp", "websphere.servlet.name": "perfServletApp.war", "websphere.servlet.created.sessions": 717, "websphere.servlet.invalidated.sessions": 717, "websphere.servlet.discarded.sessions": 0, "websphere.servlet.cache.discarded.sessions": 717, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 717, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes.rate": 0, "websphere.servlet.session.external.write.bytes.rate": 0}]}}}