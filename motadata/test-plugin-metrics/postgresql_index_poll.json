{"fd00:1:1:1::132": {"result": {}, "errors": [{"error": "could not connect to server: Connection refused\n\tIs the server running on host \"fd00:1:1:1::132\" and accepting\n\tTCP/IP connections on port 5432?\n\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 390\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 90\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/plugins/metric/postgresqlindex/plugin.py at line 64\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/tests/unit/plugins/metric/test_postgresqlindexplugin.py at line 25\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 183\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 1641\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 162\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 311\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 215\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 126\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 109\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 348\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 323\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 269\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 316\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/config/__init__.py at line 163\n\tat /home/<USER>/pycharm/helpers/pycharm/_jb_pytest_runner.py at line 45\n", "message": "Invalid port 5432, Please verify that port 5432 is up and <PERSON><PERSON><PERSON> is able to connect", "error.code": "MD002"}]}}