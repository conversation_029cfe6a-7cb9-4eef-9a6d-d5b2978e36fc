/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;

/**
 * The AvailabilityCacheService interface provides methods for retrieving cached data related to
 * availability status, heatmaps, and drill-down information.
 * <p>
 * This service is implemented as a Vert.x service proxy, allowing for asynchronous communication
 * across the event bus. It is registered by the {@link CacheServiceProvider} verticle.
 * <p>
 * The service improves performance by retrieving pre-computed or previously stored availability data
 * instead of performing expensive operations each time the data is requested.
 *
 * @see AvailabilityCacheServiceImpl
 * @see CacheServiceProvider
 */
@ProxyGen
public interface AvailabilityCacheService
{
    /**
     * Creates a new instance of the AvailabilityCacheService.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    @GenIgnore
    static void create(Vertx vertx, Handler<AsyncResult
            <AvailabilityCacheService>> handler)
    {
        new AvailabilityCacheServiceImpl(vertx, handler);
    }

    /**
     * Creates a proxy to the AvailabilityCacheService.
     *
     * @param vertx   The Vert.x instance
     * @param address The event bus address where the service is registered
     * @return A proxy to the AvailabilityCacheService
     */
    @GenIgnore
    static AvailabilityCacheService createProxy(Vertx vertx, String address)
    {
        return new AvailabilityCacheServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getCacheServiceTimeoutMillis()));
    }

    /**
     * Retrieves availability information grouped by status.
     * <p>
     * This method returns cached availability data organized by status categories
     * (e.g., up, down, warning) for the specified entities.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    AvailabilityCacheService getAvailabilityByStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves availability heatmap data.
     * <p>
     * This method returns cached availability heatmap data, which shows availability
     * patterns over time for the specified entities.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    AvailabilityCacheService getAvailabilityHeatmap(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves active status information.
     * <p>
     * This method returns cached active status data for the specified entities,
     * indicating their current operational state.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    AvailabilityCacheService getActiveStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves application status information.
     * <p>
     * This method returns cached status data for applications, indicating
     * their current operational state.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    AvailabilityCacheService getApplicationStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves drill-down result information.
     * <p>
     * This method returns cached drill-down data, which provides detailed information
     * about specific entities or components.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    AvailabilityCacheService getDrillDownResult(JsonObject context, Handler<AsyncResult<JsonObject>> handler);
}
