{"127.0.0.1": {"210": {"metadata.fields": {"Name": "Sampleebs-env", "aws:cloudformation:logical-id": "AWSEBAutoScalingGroup", "aws:cloudformation:stack-id": "arn:aws:cloudformation:ap-south-1:************:stack/awseb-e-kypxnsq6kf-stack/480c25e0-ce85-11eb-808f-0684a2038bc2", "aws:cloudformation:stack-name": "awseb-e-kypxnsq6kf-stack", "elasticbeanstalk:environment-id": "e-kypxnsq6kf", "elasticbeanstalk:environment-name": "Sampleebs-env"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500098, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Auto Scaling", "metric.object": ***************, "metric.plugin": "awsautoscaling", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "AWS Auto Scaling", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.433 pm 14/06/2022", "object.custom.fields": {"***************": "awseb-e-kypxnsq6kf-stack", "***************": "arn:aws:cloudformation:ap-south-1:************:stack/awseb-e-kypxnsq6kf-stack/480c25e0-ce85-11eb-808f-0684a2038bc2", "***************": "Sampleebs-env", "***************": "AWSEBAutoScalingGroup", "***************": "e-kypxnsq6kf", "***************": "Sampleebs-env"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 30, "object.name": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.type": "AWS Auto Scaling", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 210, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"210": {"metadata.fields": {"Name": "Sampleebs-env", "aws:cloudformation:logical-id": "AWSEBAutoScalingGroup", "aws:cloudformation:stack-id": "arn:aws:cloudformation:ap-south-1:************:stack/awseb-e-kypxnsq6kf-stack/480c25e0-ce85-11eb-808f-0684a2038bc2", "aws:cloudformation:stack-name": "awseb-e-kypxnsq6kf-stack", "elasticbeanstalk:environment-id": "e-kypxnsq6kf", "elasticbeanstalk:environment-name": "Sampleebs-env"}}, "aws.autoscaling.group.desired.capacity": 1, "aws.autoscaling.group.in.service.capacity.units": 1, "aws.autoscaling.group.in.service.instances": 1, "aws.autoscaling.group.instances": 1, "aws.autoscaling.group.max.size": 4, "aws.autoscaling.group.min.size": 1, "aws.autoscaling.group.pending.capacity.units": 0, "aws.autoscaling.group.pending.instances": 0, "aws.autoscaling.group.provisioned.capacity.units": 1, "aws.autoscaling.group.standby.capacity.units": 0, "aws.autoscaling.group.standby.instances": 0, "aws.autoscaling.group.terminating.capacity.units": 0, "aws.autoscaling.group.terminating.instances": 0, "aws.autoscaling.health.check.type": "EC2", "aws.availability.zone": " ap-south-1b, ap-south-1a", "aws.instance.creation.time": " 363 days 1 hours 26 minutes 51 seconds", "aws.instance.creation.time.seconds": 31368411}, "status": "succeed", "timeout": 60}}