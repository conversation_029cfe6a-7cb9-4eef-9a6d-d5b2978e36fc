[{"_type": "0", "id": 10000000002857, "visualization.name": "Local Capacity", "visualization.description": "Cluster Storage capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.system.storage.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.system.storage.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.system.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.system.storage.used.percent"}, {"type": "metric", "data.point": "hpe.primera.system.storage.capacity.bytes"}, {"type": "metric", "data.point": "hpe.primera.system.storage.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Capacity", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "hpe.primera.system.storage.capacity.bytes.last"}, {"label": "Free", "value": "hpe.primera.system.storage.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.primera.system.storage.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hpe.primera.system.storage.used.percent"}}}}, {"_type": "0", "id": 10000000002822, "visualization.name": "Host", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.hosts"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "host"}, "font.size": "medium", "color.data.point": "hpe.primera.hosts"}, "header": {"title": "Host", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.primera.hosts.last"}]}}}}, {"_type": "0", "id": 10000000002823, "visualization.name": "CPG", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.cpgs", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.cpgs"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "cpg"}, "font.size": "medium", "color.data.point": "hpe.primera.cpgs"}, "header": {"title": "CPG", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.primera.cpgs.last"}]}}}}, {"_type": "0", "id": 10000000002824, "visualization.name": "Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.disks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.disks"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "disk"}, "font.size": "medium", "color.data.point": "hpe.primera.disks"}, "header": {"title": "Disk", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.primera.disks.last"}]}}}}, {"_type": "0", "id": 10000000002858, "visualization.name": "Volume", "visualization.description": "Cluster Storage Volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.virtual.volume.sets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.volumes"}, {"type": "metric", "data.point": "hpe.primera.virtual.volume.sets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Volume", "style": {"font.size": "medium"}, "data.points": [{"label": "Virtual", "value": "hpe.primera.virtual.volume.sets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.primera.volumes.last"}]}, "style": {"icon": {"name": "volume"}, "font.size": "medium", "color.data.point": "hpe.primera.volumes"}}}}, {"_type": "0", "id": 10000000002825, "visualization.name": "LUN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.luns", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.primera.luns"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "luns"}, "font.size": "medium", "color.data.point": "hpe.primera.luns"}, "header": {"title": "LUN", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.primera.luns.last"}]}}}}, {"_type": "0", "id": 10000000002826, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002827, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002828, "visualization.name": "Capacity Utilization", "visualization.description": "HPE Capacity Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.system.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.system.storage.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.system.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002829, "visualization.name": "Top 10 Node By CPU Interrupts Per Second", "visualization.description": "HPE Node", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.interrupts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002830, "visualization.name": "Top 10 Node By CPU Context Switches Per Second", "visualization.description": "HPE Node", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.context.switches.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002859, "visualization.name": "Top 10 Node By CPU User (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.user.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002860, "visualization.name": "Top 10 Node By CPU System (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.system.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002861, "visualization.name": "Top 10 Node By CPU Idle (%)", "visualization.description": "HPE Node CPU Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.node"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002831, "visualization.name": "Host Details", "visualization.category": "Grid", "visualization.type": "Host Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.host~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host~os", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host~fc.paths", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host~iscsi.paths", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 2}, {"name": "hpe.primera.host", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.host~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.host~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "hpe.primera.host~os.last", "title": "OS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.host~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.host~fc.paths.last", "title": "Associated FC Paths", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "position": 7, "type": "view_more_drawer"}, {"name": "hpe.primera.host~iscsi.paths.last", "title": "Associated ISCSI Paths", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "position": 8, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000002832, "visualization.name": "Host Set Details", "visualization.category": "Grid", "visualization.type": "Host Set Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.host.set~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host.set~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.host.set~members", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.host.set", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.host.set~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.host.set~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.host.set~members.last", "title": "Members", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000002833, "visualization.name": "Virtual Volume Set Details", "visualization.category": "Grid", "visualization.type": "Virtual Volume Set Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.virtual.volume.set~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.virtual.volume.set~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.virtual.volume.set~members", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.virtual.volume.set~vvol.storage.container.enabled", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.virtual.volume.set~qos.enabled", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.virtual.volume.set", "title": "Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.virtual.volume.set~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.virtual.volume.set~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.virtual.volume.set~members.last", "title": "Members", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "view_more_drawer"}, {"name": "hpe.primera.virtual.volume.set~vvol.storage.container.enabled.last", "title": "Container Enabled", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.virtual.volume.set~qos.enabled.last", "title": "QoS Enabled", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}]}}}, {"_type": "0", "id": 10000000002834, "visualization.name": "Node Details", "visualization.category": "Grid", "visualization.type": "Node Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node~cpu.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~cpu.system.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~cpu.idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~cpu.interrupts.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~cpu.context.switches.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~hit.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~hit.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~miss.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~miss.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~access.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~access.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~hit.io.read.ops.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~hit.io.write.ops.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~io.access.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node~lock.bulk.io.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Host Set Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.node", "title": "Node ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.node~cpu.user.percent.last", "title": "CPU User (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.node~cpu.system.percent.last", "title": "CPU System (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.node~cpu.idle.percent.last", "title": "CPU Idle (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.node~cpu.interrupts.per.sec.last", "title": "CPU Interrupts Per Second", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.node~cpu.context.switches.per.sec.last", "title": "CPU Context Switched Per Second", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.node~hit.io.read.ops.per.sec.last", "title": "Hit Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.node~hit.io.write.ops.per.sec.last", "title": "Hit Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.primera.node~miss.io.read.ops.per.sec.last", "title": "Miss Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.primera.node~miss.io.write.ops.per.sec.last", "title": "Miss Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.primera.node~access.io.read.ops.per.sec.last", "title": "Access Read IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.primera.node~access.io.write.ops.per.sec.last", "title": "Access Write IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "hpe.primera.node~hit.io.read.ops.percent.last", "title": "Hit Read IO (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}, {"name": "hpe.primera.node~hit.io.write.ops.percent.last", "title": "Hit Write IO (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14}, {"name": "hpe.primera.node~io.access.per.sec.last", "title": "Total Access IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15}, {"name": "hpe.primera.node~lock.bulk.io.per.sec.last", "title": "Lock Bulk IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16}]}}}, {"_type": "0", "id": 10000000002835, "visualization.name": "Node Page Statistics", "visualization.category": "Grid", "visualization.type": "Node Page Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.node.page~free.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node.page~clean.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node.page~write.once.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node.page~multiple.write.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node.page~scheduled.write.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.node.page~deferred.cow.pending.pages", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "<PERSON>de Page <PERSON>", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.node.page", "title": "Node ID", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.node.page~free.pages.last", "title": "Free Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.node.page~clean.pages.last", "title": "Clean Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.node.page~write.once.pages.last", "title": "Once Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.node.page~multiple.write.pages.last", "title": "Multiple Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.node.page~scheduled.write.pages.last", "title": "Scheduled Write Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.node.page~deferred.cow.pending.pages.last", "title": "Deferred COW Pending Pages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}]}}}, {"_type": "0", "id": 10000000002836, "visualization.name": "CPG Details", "visualization.category": "Grid", "visualization.type": "CPG Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.cpg~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~uuid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~shared.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~private.base.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~private.snapshot.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~failed.persistent.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~persistent.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.cpg~degraded.virtual.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "CPG Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.cpg", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.cpg~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.cpg~uuid.last", "title": "UUID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.cpg~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.cpg~capacity.bytes.last", "title": "Total Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.cpg~free.bytes.last", "title": "Free Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.cpg~used.bytes.last", "title": "Used Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.cpg~shared.bytes.last", "title": "Shared Space", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.primera.cpg~private.base.bytes.last", "title": "Private Base", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.primera.cpg~private.snapshot.bytes.last", "title": "Private Snapshot", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.primera.cpg~failed.persistent.virtual.volumes.last", "title": "Failed Persistent Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.primera.cpg~persistent.virtual.volumes.last", "title": "Total Persistent Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12}, {"name": "hpe.primera.cpg~degraded.virtual.volumes.last", "title": "Total Degraded Virtual Volumes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13}]}}}, {"_type": "0", "id": 10000000002837, "visualization.name": "Volume Details", "visualization.category": "Grid", "visualization.type": "Volume Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.volume~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~deduplication.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~copy.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~provision.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~creation.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~reserved.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.volume~cpgs", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Volume Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.volume", "title": "Volume Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.volume~id.last", "title": "Volume ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.volume~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.volume~deduplication.state.last", "title": "Deduplication State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.volume~copy.type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.volume~provision.type.last", "title": "Provision Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.volume~creation.time.last", "title": "Creation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.volume~wwn.last", "title": "WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.primera.volume~size.bytes.last", "title": "Total Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.primera.volume~reserved.bytes.last", "title": "Reserved Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.primera.volume~used.bytes.last", "title": "Used Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}, {"name": "hpe.primera.volume~cpgs.last", "title": "Associated CPG", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "type": "view_more_drawer"}]}}}, {"_type": "0", "id": 10000000002838, "visualization.name": "LUN Details", "visualization.category": "Grid", "visualization.type": "LUN Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.lun~io.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.lun~io.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.lun~io.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.lun~queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.lun~busy.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "LUN Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.lun", "title": "LUN ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.lun~io.ops.per.sec.last", "title": "Total IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.lun~io.bytes.per.sec.last", "title": "Total bps", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.lun~io.bytes.last", "title": "Total IO Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.lun~io.latency.ms.last", "title": "Service Time ms", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.lun~queue.length.last", "title": "Queue Length", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.lun~busy.percent.last", "title": "Busy (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"_type": "0", "id": 10000000002839, "visualization.name": "Disk Details", "visualization.category": "Grid", "visualization.type": "Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.disk~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~manufacturer", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~media.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.disk~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Disk Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.disk", "title": "Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.disk~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.disk~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.disk~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.disk~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.disk~manufacturer.last", "title": "Manufacture", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.disk~media.type.last", "title": "Media Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.disk~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.primera.disk~capacity.bytes.last", "title": "Total Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.primera.disk~free.bytes.last", "title": "Free Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.primera.disk~used.bytes.last", "title": "Used Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}]}}}, {"_type": "0", "id": 10000000002840, "visualization.name": "Port Details", "visualization.category": "Grid", "visualization.type": "Port Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.port~mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~link.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~node.wwn", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~label", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~configurable.bitrate", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~max.bitrate", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Port Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.port", "title": "Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.port~mode.last", "title": "Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.port~link.state.last", "title": "Link State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.port~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.port~wwn.last", "title": "WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.primera.port~node.wwn.last", "title": "Node WWN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.primera.port~label.last", "title": "Label", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "hpe.primera.port~devices.last", "title": "Devices Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "type": "view_more_drawer"}, {"name": "hpe.primera.port~configurable.bitrate.last", "title": "Configurable Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.primera.port~max.bitrate.last", "title": "Max Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.primera.port~speed.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11}]}}}, {"_type": "0", "id": 10000000002841, "visualization.name": "FC Switch Details", "visualization.category": "Grid", "visualization.type": "FC Switch Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.port.fcswitch~logical.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port.fcswitch~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port.fcswitch~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.primera.port.fcswitch~vendor", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "FC Switch Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}, {"name": "hpe.primera.port.fcswitch", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "hpe.primera.port.fcswitch~logical.name.last", "title": "Logical Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.primera.port.fcswitch~ports.last", "title": "Ports", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.primera.port.fcswitch~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.primera.port.fcswitch~vendor.last", "title": "<PERSON><PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000002842, "visualization.name": "Top 10 CPG by IO size", "visualization.description": "HPE CPG IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.cpg~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002843, "visualization.name": "Top 10 CPG by IOPS", "visualization.description": "HPE CPG IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.cpg~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002844, "visualization.name": "Top 10 CPG by Queue Length", "visualization.description": "HPE CPG", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.cpg~queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.cpg"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002845, "visualization.name": "Top 10 LUN by Read Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.read.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002846, "visualization.name": "Top 10 LUN by Read IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002847, "visualization.name": "Top 10 LUN by Read IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002848, "visualization.name": "Top 10 LUN by Write IO Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.write.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002849, "visualization.name": "Top 10 LUN by Write IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002850, "visualization.name": "Top 10 LUN by Write IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002851, "visualization.name": "Top 10 LUN by Total IO Size", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002852, "visualization.name": "Top 10 LUN by IOPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002853, "visualization.name": "Top 10 LUN by IO BPS", "visualization.description": "HPE LUN IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.lun~io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.lun"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002854, "visualization.name": "Top 10 Disk by IO Size", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.disk~io.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002855, "visualization.name": "Top 10 Disk by IOPS", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.disk~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002856, "visualization.name": "Top 10 Disk by <PERSON>ue Length", "visualization.description": "HPE Disk IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.disk~queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.disk"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002862, "visualization.name": "Top 10 Volume by Read IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.volume~io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002863, "visualization.name": "Top 10 Volume by Write IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.volume~io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002864, "visualization.name": "Top 10 Volume by Total IOPS", "visualization.description": "HPE Volume IO", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.primera.volume~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.result.by": ["hpe.primera.volume"]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "sorting": {"limit": 10, "order": "desc"}, "highchart.settings": {}}}}]