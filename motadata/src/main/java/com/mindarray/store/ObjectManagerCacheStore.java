/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  24-Mar-2025		Sankalp		        if plugin supports multiple engines, engine will be decided based on key specified in motadata.json
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Metric;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.DASH_SEPARATOR;

public class ObjectManagerCacheStore extends AbstractCacheStore
{

    private static final Logger LOGGER = new Logger(ObjectManagerCacheStore.class, GlobalConstants.MOTADATA_STORE, "Object Manager Cache Store");

    private static final ObjectManagerCacheStore STORE = new ObjectManagerCacheStore();

    private final Map<String, JsonObject> items = new ConcurrentHashMap<>();

    private final Map<String, String> metricEngineByPlugin = new ConcurrentHashMap<>();

    private final Map<Integer, String> metricNamesByPlugin = new ConcurrentHashMap<>(); // metric name by plugin id

    private ObjectManagerCacheStore()
    {
    }

    public static ObjectManagerCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
        {
            promise.fail("failed to start object manager: invalid boot sequence...");
        }

        else
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {

                try
                {
                    var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + "metrics.db");

                    if (buffer != null && buffer.length() > 0)
                    {
                        this.metricNamesByPlugin.put(NMSConstants.AVAILABILITY_PLUGIN_ID, VisualizationConstants.VisualizationDataSource.OBJECT_AVAILABILITY.getName().toLowerCase());

                        this.metricNamesByPlugin.put(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName(), VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName().toLowerCase());

                        var metrics = new JsonArray(new CipherUtil().decrypt(buffer.toString()));

                        if (!metrics.isEmpty())
                        {
                            for (var index = 0; index < metrics.size(); index++)
                            {
                                var metric = metrics.getJsonObject(index);

                                if (MotadataConfigUtil.devMode())
                                {
                                    metric.put(Metric.METRIC_POLLING_TIME, 50000);//as we do not require automated polling so in dev mode
                                }

                                var metricContext = metric.getJsonObject(Metric.METRIC_CONTEXT);

                                if (metricContext != null)
                                {
                                    Optional.ofNullable(metricContext.getJsonArray(PluginEngineConstants.PLUGIN_ENGINE_RUNTIMES))
                                            .filter(value -> value.contains(MotadataConfigUtil.getPluginEngine()))
                                            .ifPresent(value -> metric.getJsonObject(Metric.METRIC_CONTEXT).put(PluginEngineConstants.PLUGIN_ENGINE, MotadataConfigUtil.getPluginEngine()));
                                }

                                this.items.put(metric.getString(Metric.METRIC_PLUGIN), metric);

                                if (metricContext != null && metricContext.getString(PluginEngineConstants.PLUGIN_ENGINE) != null)
                                {
                                    this.metricEngineByPlugin.put(metric.getString(Metric.METRIC_PLUGIN), metricContext.getString(PluginEngineConstants.PLUGIN_ENGINE));
                                }

                                if (metricContext != null && metricContext.containsKey(GlobalConstants.PLUGIN_ID) && metricContext.getValue(GlobalConstants.PLUGIN_ID) != null)
                                {
                                    this.metricNamesByPlugin.computeIfAbsent(metricContext.getInteger(GlobalConstants.PLUGIN_ID), value -> metric.getString(Metric.METRIC_NAME).replace(" ", ".").trim().toLowerCase());
                                }
                            }
                        }

                        else
                        {
                            future.fail("failed to load metric db...");
                        }

                    }

                    else
                    {
                        future.fail("failed to load metric db...");
                    }


                    future.complete();
                }

                catch (Exception exception)
                {
                    future.fail(exception);

                    LOGGER.error(exception);
                }

            }, result ->
            {

                if (result.succeeded())
                {
                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }

                else
                {
                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }


    public JsonObject getItemByMetricPlugin(String plugin)
    {
        return items.containsKey(plugin) ? items.get(plugin).copy() : null;
    }

    public JsonArray getItems()
    {
        return new JsonArray(items.values().stream().map(JsonObject::mapFrom).collect(Collectors.toList()));
    }

    public int getPluginIdByMetricPlugin(String plugin)
    {
        var pluginId = 0;

        var metric = items.get(plugin);

        if (metric != null && metric.getJsonObject(Metric.METRIC_CONTEXT) != null)
        {
            pluginId = metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(GlobalConstants.PLUGIN_ID);
        }

        return pluginId;
    }

    public int getTimeoutByMetricPlugin(String plugin)
    {
        var metric = items.get(plugin);

        var timeout = 60;

        if (metric != null && metric.getJsonObject(Metric.METRIC_CONTEXT) != null)
        {
            timeout = metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(GlobalConstants.TIMEOUT);
        }

        return timeout;
    }

    public List<JsonObject> getItemsByObjectType(NMSConstants.Type type)
    {
        return this.items.values().stream().filter(item -> item.getString(Metric.METRIC_TYPE) != null && type.getName().equalsIgnoreCase(item.getString(Metric.METRIC_TYPE)))
                .collect(Collectors.toList());
    }

    public String getPluginEngineByMetricPlugin(String plugin)
    {
        return metricEngineByPlugin.getOrDefault(plugin, null);
    }

    public void addMetricName(int id, String metricName)
    {
        metricNamesByPlugin.computeIfAbsent(id, value -> metricName.replace(" ", ".").trim().toLowerCase());
    }

    public String getMetricName(int id)
    {
        return metricNamesByPlugin.containsKey(id) ? metricNamesByPlugin.get(id).replace(DASH_SEPARATOR, ".") : null;
    }
}
