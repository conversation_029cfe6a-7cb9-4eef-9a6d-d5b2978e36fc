/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.SystemFileConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.SYSTEM_FILE_API_ENDPOINT;
import static com.mindarray.api.SystemFile.*;
import static org.junit.jupiter.api.Assertions.assertEquals;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestSystemConfiguration
{
    private static final JsonObject DIRECTORY = new JsonObject().put(SYSTEM_FILE, "D:\\").put(SYSTEM_FILE_OS, NMSConstants.Type.WINDOWS.getName()).put(SYSTEM_FILE_TYPE, SYSTEM_FILE_TYPE_DIR);
    private static final Logger LOGGER = new Logger(TestSystemConfiguration.class, MOTADATA_API, "Test System Configuration");
    private static long id;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSystemFile(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_FILE, "D:\\").put(SYSTEM_FILE_OS, "Windows").put(SYSTEM_FILE_TYPE, "Directory");

        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemFileConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System File"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllSystemFile(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SystemFileConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetSystemFile(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, id, SystemFileConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateSystemFile(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_FILE, "D:\\home").put(SYSTEM_FILE_OS, "Linux");

        TestAPIUtil.put(SYSTEM_FILE_API_ENDPOINT + "/" + id, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemFileConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "System File"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteSystemFile(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SYSTEM_FILE_API_ENDPOINT + "/" + id,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SystemFileConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "System File"));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateSystemDirectory(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, DIRECTORY,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemFileConfigStore.getStore(), DIRECTORY, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System File"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateSystemDirectoryForDifferentOSType(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = DIRECTORY.copy().put(SYSTEM_FILE_OS, NMSConstants.Type.LINUX.getName());

        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemFileConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System File"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateSystemFileSameNameAsDirectory(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = DIRECTORY.copy().put(SYSTEM_FILE_TYPE, SYSTEM_FILE_TYPE_FILE);

        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemFileConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System File"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateDuplicateSystemDirectory(VertxTestContext testContext)
    {
        TestAPIUtil.post(SYSTEM_FILE_API_ENDPOINT, DIRECTORY,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            var body = response.bodyAsJsonObject();

                            assertEquals(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, NMSConstants.RediscoverJob.FILE_DIRECTORY.getName()), body.getString(GlobalConstants.MESSAGE));

                            assertEquals(HttpStatus.SC_BAD_REQUEST, body.getInteger(APIConstants.RESPONSE_CODE));

                            assertEquals(STATUS_FAIL, body.getString(STATUS));

                            testContext.completeNow();

                        })));
    }
}


