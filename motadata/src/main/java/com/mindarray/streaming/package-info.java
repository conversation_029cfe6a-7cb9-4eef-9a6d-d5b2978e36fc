/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The Streaming package provides real-time data streaming capabilities for the Motadata platform.
 * <p>
 * This package contains components that work together to enable live data streaming for various types of information:
 * <p>
 * <strong>Core Streaming Components:</strong>
 * <ul>
 *   <li>{@link com.mindarray.streaming.StreamingEngine} - Manages streaming sessions and routes streaming events</li>
 *   <li>{@link com.mindarray.streaming.StreamingBroadcaster} - Broadcasts streaming data to connected clients</li>
 * </ul>
 * <p>
 * <strong>Supported Streaming Types:</strong>
 * <ul>
 *   <li><strong>Log Tail</strong> - Streams log messages in real-time</li>
 *   <li><strong>Trap Tail</strong> - Streams SNMP trap messages as they arrive</li>
 *   <li><strong>Event Tracker</strong> - Streams event tracking information</li>
 *   <li><strong>Event Engine Stats</strong> - Streams statistics from the event processing engine</li>
 * </ul>
 * <p>
 * The streaming package uses Vert.x event bus for communication between components and implements
 * session management to control the number of active streaming sessions. It also provides filtering
 * capabilities to allow clients to receive only the data they are interested in.
 * <p>
 * Streaming sessions are automatically terminated after a configurable timeout period if they
 * become inactive, and the system enforces limits on the maximum number of concurrent streaming
 * sessions to prevent resource exhaustion.
 * <p>
 * This package is designed to be scalable and efficient, capable of handling multiple concurrent
 * streaming sessions while maintaining low latency for real-time data delivery.
 */
package com.mindarray.streaming;