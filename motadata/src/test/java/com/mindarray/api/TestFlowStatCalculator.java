/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.TestUtil;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.PLUGIN_ID;
import static com.mindarray.eventbus.EventBusConstants.EVENT_FLOW_STAT;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(30 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestFlowStatCalculator
{
    private static final Logger LOGGER = new Logger(TestFlowStatCalculator.class, "Stat", "Test Flow Stat Calculator");
    private static MessageConsumer<byte[]> messageConsumer;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testFlowStatCalculator(VertxTestContext testContext) throws InterruptedException
    {

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {

            var result = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if (result.getString(PLUGIN_ID).equalsIgnoreCase(DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName() + "-flow"))
            {
                LOGGER.info(result);

                Assertions.assertTrue(0L <= result.getLong(FlowEngineConstants.FLOW_VOLUME_BYTES));

                Assertions.assertTrue(0L <= result.getLong(FlowEngineConstants.FLOWS_PER_SEC));

                Assertions.assertTrue(0L <= result.getLong(FlowEngineConstants.FLOW_VOLUME_BYTES_PER_SEC));

                messageConsumer.unregister();

                testContext.completeNow();
            }
        });

        var stats = new HashMap<String, long[]>();

        stats.put("8.8.8.8", new long[]{1, 200});

        for (var index = 0; index < MotadataConfigUtil.getLogParsers(); index++)
        {
            Bootstrap.vertx().eventBus().send(EVENT_FLOW_STAT, stats);
        }

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}
