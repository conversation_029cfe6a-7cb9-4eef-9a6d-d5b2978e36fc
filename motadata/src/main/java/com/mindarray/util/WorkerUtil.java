/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  20-May-2025     Aagam           MOTADATA-5847 : Added windows build support
 *  01-Jun-2025     Harsh           MOTADATA-6386 : Added windows build support
 *
 */

package com.mindarray.util;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Discovery;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.*;

public final class WorkerUtil
{
    public static final int EXIT_CODE_TIMED_OUT = 9;

    public static final Set<Integer> ABNORMAL_PROCESS_EXIT_CODES = Set.of(EXIT_CODE_TIMED_OUT, 137, 143, Integer.MIN_VALUE, Integer.MAX_VALUE);

    public static final String EXIT_CODE = "exit.code";

    public static final String PLUGIN_CONTEXTS = "plugin.contexts";

    public static final String WORKER_CONTEXT = "worker.context";

    private static final Logger LOGGER = new Logger(WorkerUtil.class, GlobalConstants.MOTADATA_UTIL, "Worker Util");

    private static final AtomicLong COUNTER = new AtomicLong(System.nanoTime());

    private static final Map<String, JsonArray> CONTEXTS = Map.ofEntries(Map.entry(EventBusConstants.EVENT_DISCOVERY,
                    new JsonArray(new ArrayList<String>(2)).add(DISCOVERY_FAILED).add(EventBusConstants.EVENT_DISCOVERY_RESPONSE)),
            Map.entry(EventBusConstants.EVENT_METRIC_POLL, new JsonArray(new ArrayList<String>(2)).add(METRIC_POLL_FAILED).add(EventBusConstants.EVENT_METRIC_POLL_RESPONSE)),
            Map.entry(EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST, new JsonArray(new ArrayList<String>(2)).add(CREDENTIAL_PROFILE_TEST_FAILED).add(EventBusConstants.EVENT_METRIC_POLL_RESPONSE)),
            Map.entry(EventBusConstants.EVENT_REDISCOVER, new JsonArray(new ArrayList<String>(2)).add(REDISCOVER_FAILED).add(EventBusConstants.EVENT_REDISCOVER_RESPONSE)),
            Map.entry(EventBusConstants.EVENT_PLUGIN_ENGINE, new JsonArray(new ArrayList<String>(2)).add(PLUGIN_ENGINE_FAILED).add(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE)),
            Map.entry(EventBusConstants.EVENT_TOPOLOGY, new JsonArray(new ArrayList<String>(2)).add(TOPOLOGY_RUN_ERROR).add(EventBusConstants.EVENT_TOPOLOGY_RESPONSE)),
            Map.entry(EventBusConstants.EVENT_CONFIG_MANAGE, new JsonArray().add(CONFIG_REQUEST_FAILED).add(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE)));

    private static final Integer EVENT_PREFIX = 0;

    private static final Integer EVENT_RESPONSE_TOPIC = 1;

    private static final String WORKER_ERROR = "worker.error";

    private WorkerUtil()
    {
    }

    // eventId -> main event id
    // eventTrackerId -> child process event id

    public static String spawnWorker(JsonObject event, long eventId, long childEventId, String language, boolean customPlugin) throws Exception
    {
        String result = null;

        String script = null;

        String file = null;

        var errors = new StringBuilder(0);

        try
        {
            var arguments = new ArrayList<String>();

            if (language.equalsIgnoreCase(PluginEngineConstants.PluginEngine.PYTHON.getName()))
            {
                arguments.add(PYTHON_BIN);

                script = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                        CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + eventId + ".py";

                FileUtils.writeStringToFile(new File(script), CommonUtil.replaceScriptVariables(event, customPlugin), StandardCharsets.UTF_8);

                arguments.add(script);
            }
            else if (language.equalsIgnoreCase(PluginEngineConstants.PluginEngine.GO.getName()))
            {
                arguments.add(GO_BIN);

                arguments.add(GO_RUN_BIN);

                script = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                        CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + eventId + ".go";

                FileUtils.writeStringToFile(new File(script), CommonUtil.replaceScriptVariables(event, customPlugin), StandardCharsets.UTF_8);

                arguments.add(script);
            }
            else
            {
                return EMPTY_VALUE;
            }

            arguments.add("--context");

            arguments.add(UUID.randomUUID().toString());

            var context = Base64.getEncoder().encodeToString(event.put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).encode().getBytes());

            file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                    PLUGIN_CONTEXT_DIR + PATH_SEPARATOR + arguments.getLast();

            FileUtils.writeStringToFile(new File(file), context, StandardCharsets.UTF_8);

            var builder = new StringBuilder();

            var timedOut = new AtomicBoolean(false);

            var processBuilder = new ProcessBuilder(arguments);

            var worker = processBuilder.start();

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("%s script worker %s started with %s timeout and context %s", language, worker.pid(), event.containsKey(GlobalConstants.TIMEOUT) ? event.getInteger(GlobalConstants.TIMEOUT) : 30, context));
            }

            EventBusConstants.updateEvent(eventId, childEventId != DUMMY_ID ? String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_PROCESS_STARTED, childEventId, DateTimeUtil.timestamp(), worker.pid()) : String.format(InfoMessageConstants.EVENT_TRACKER_PROCESS_STARTED, worker.pid(), DateTimeUtil.timestamp()));

            WorkerUtil.setupCleanupTimer(event.containsKey(GlobalConstants.TIMEOUT) ? event.getInteger(GlobalConstants.TIMEOUT) : 30, worker, processBuilder, eventId, null, timedOut);

            try (var reader = new BufferedReader(new InputStreamReader(worker.getInputStream()));
                 var errorReader = new BufferedReader(new InputStreamReader(worker.getErrorStream())))
            {
                String line, errorLine;

                while ((line = reader.readLine()) != null)
                {
                    builder.append(line.trim());
                }

                while ((errorLine = errorReader.readLine()) != null)
                {
                    errors.append(errorLine.trim());
                }
            }

            if (!builder.isEmpty())
            {
                result = builder.toString();
            }

            if (timedOut.get())
            {
                throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
            }

            EventBusConstants.updateEvent(eventId, childEventId != DUMMY_ID ? String.format(InfoMessageConstants.EVENT_TRACKER_CHILD_PROCESS_FINISHED, childEventId, DateTimeUtil.timestamp(), worker.pid()) : String.format(InfoMessageConstants.EVENT_TRACKER_PROCESS_FINISHED, worker.pid(), DateTimeUtil.timestamp()));

            if (CommonUtil.traceEnabled())
            {
                if (!errors.isEmpty())
                {
                    LOGGER.trace(String.format("%s script worker %s errors %s", language, worker.pid(), errors));
                }

                LOGGER.trace(String.format("%s script worker %s result %s", language, worker.pid(), result));
            }
        }
        catch (Exception exception)
        {
            if (exception.getMessage() != null && !exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {

                LOGGER.error(exception);
            }
            throw exception;
        }
        finally
        {
            if (script != null)
            {
                FileUtils.deleteQuietly(new File(script));
            }

            if (file != null)
            {
                FileUtils.deleteQuietly(new File(file));
            }
        }

        return result != null ? result : EMPTY_VALUE;
    }

    public static JsonObject spawnWorker(Map<Long, JsonObject> events, JsonObject event, List<Long> eventIds, int timeout, boolean liveStreaming, PluginEngineConstants.PluginEngine pluginEngine, long latency, boolean reply) throws Exception
    {
        var builder = new StringBuilder(0);

        var errorCode = ErrorCodes.ERROR_CODE_INTERNAL_ERROR;

        var eventType = event.getString(EventBusConstants.EVENT_TYPE); // request type (discovery/rediscovery/metric.poll/plugin.engine/topology etc..)

        var customPlugin = PluginEngineConstants.isCustomPlugin(event);

        var props = CONTEXTS.get(eventType);

        var output = new JsonObject();

        InputStream inputStream = null;

        BufferedReader bufferedReader = null;

        Process worker = null;

        try
        {
            if (customPlugin) // means we have to spawn process based on script language type
            {
                builder.append(spawnWorker(event, event.getLong(EventBusConstants.EVENT_ID), DUMMY_ID, event.getString(PluginEngineConstants.SCRIPT_LANGUAGE), true));

                // reply is true for all engine request and for test event came from ui event handler has false value so for that we will not send reply based on buffer reading as ui event handler will do that based on their requirement
                if (reply)
                {
                    if (!builder.isEmpty()) // if result available then send else send internal error
                    {
                        output.mergeIn(new JsonObject(new String(Base64.getDecoder().decode(builder.toString().trim().replace(VALUE_SEPARATOR, EMPTY_VALUE)))));

                        send(output, eventType, liveStreaming, events, event, true, props, latency);
                    }
                    else
                    {
                        send(events, errorCode, INTERNAL_ERROR, null, STATUS_FAIL, props, liveStreaming, eventType);
                    }
                }
            }
            else
            {
                var arguments = new ArrayList<String>();

                var size = events.size();

                var context = Base64.getEncoder().encodeToString(new JsonObject().put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).put(INSTALLATION_TYPE, MotadataConfigUtil.getInstallationType()).put("maximum.cpu.cores", MotadataConfigUtil.getPluginEngineCPUCores()).put(PLUGIN_CONTEXTS, new JsonArray(new ArrayList<>(events.values()))).encode().getBytes());

                // For debugging purpose : if you need to know because of which event process was forcefully killed
                JsonArray ids = null;

                if (CommonUtil.traceEnabled())
                {
                    ids = new JsonArray();

                    for (var item : events.values())
                    {
                        ids.add(item.getLong(EventBusConstants.EVENT_ID));
                    }
                }

                var fileName = CommonUtil.getString(COUNTER.getAndIncrement());

                FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + PLUGIN_CONTEXT_DIR + PATH_SEPARATOR + fileName), context, StandardCharsets.UTF_8);

                if (pluginEngine == PluginEngineConstants.PluginEngine.PYTHON)
                {
                    arguments.add(PYTHON_BIN);

                    arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + PYTHON_PLUGIN_ENGINE_DIR + PATH_SEPARATOR + PYTHON_PLUGIN_ENGINE_BIN);
                }
                else if (pluginEngine == PluginEngineConstants.PluginEngine.GO)
                {
                    arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GO_PLUGIN_ENGINE_DIR + PATH_SEPARATOR + GO_PLUGIN_ENGINE_BIN);
                }
                else if (pluginEngine == PluginEngineConstants.PluginEngine.DOTNET)
                {
                    arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + DOTNET_ENGINE_DIR + PATH_SEPARATOR + DOTNET_PLUGIN_ENGINE_BIN);
                }

                arguments.add(fileName);

                var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

                var errors = new StringBuilder(0);

                worker = processBuilder.start();

                var pid = worker.pid();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("%s engine %s worker %s started with %s timeout and %s context %s", pluginEngine.getName(), eventType, pid, timeout, size, context));
                }

                if (liveStreaming) // for live streaming need to send worker context to streaming engine.. why ?? -> after any failed result or timeout streaming engine will close this process forcefully
                {
                    Bootstrap.vertx().eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_STREAMING_BROADCAST,
                            new JsonObject().mergeIn(event)
                                    .put(WORKER_CONTEXT, CodecUtil.toBytes(worker)));
                }

                eventIds.forEach(eventId -> EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_STARTED, pid, DateTimeUtil.timestamp())));

                var forcefully = new AtomicBoolean(false);

                setupCleanupTimer(timeout, worker, processBuilder, NOT_AVAILABLE, ids, forcefully);

                inputStream = worker.getInputStream();

                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

                String line;

                while ((line = bufferedReader.readLine()) != null)
                {
                    builder.append(line.trim());

                    if (reply && !builder.isEmpty())
                    {
                        var response = builder.toString();

                        if (response.contains(VALUE_SEPARATOR))
                        {
                            var values = response.split(VALUE_SEPARATOR_WITH_ESCAPE);

                            // if result is pending then it may not contains value separator at the end so send total data-1 response
                            var length = response.endsWith(VALUE_SEPARATOR) ? values.length : values.length - 1;

                            for (var index = 0; index < length; index++)
                            {
                                output.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(values[index].trim().replace("\\n", EMPTY_VALUE).replace(VALUE_SEPARATOR, EMPTY_VALUE)))));

                                if (ids != null)
                                {
                                    ids.remove(output.getLong(EventBusConstants.EVENT_ID));
                                }

                                // means file read/open or contexts decode issue in bootstrap of plugin engine so in that case we will put particular errors in all requested events
                                if (output.containsKey(WORKER_ERROR) && output.getString(WORKER_ERROR).equalsIgnoreCase(YES))
                                {
                                    send(events, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, INTERNAL_ERROR, output.getString(ERROR),
                                            STATUS_FAIL, props, liveStreaming, eventType);

                                    // will set length to total result length so it will not read another response...
                                    length = values.length;

                                    break;
                                }
                                else
                                {
                                    send(output, eventType, liveStreaming, events, event, false, props, latency);
                                }
                            }

                            // after sending response if length is greater than zero clear builder for next response
                            if (length > 0)
                            {
                                builder.setLength(0);
                            }

                            // means pending result in current buffer read so need to append it again
                            if (length != values.length)
                            {
                                builder.append(values[length]);
                            }
                        }
                        else
                        {
                            errors.append(response);
                        }
                    }
                }

                var iterator = eventIds.iterator();

                while (iterator.hasNext())
                {
                    EventBusConstants.updateEvent(iterator.next(), String.format(EVENT_TRACKER_PLUGIN_PROCESS_FINISHED, pid, DateTimeUtil.timestamp()));

                    iterator.remove();
                }

                if (forcefully.get())
                {
                    errorCode = ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT;

                    if (!reply) // throw only when any engine want's to perform operation based on their requirement like ui event handler...for rest engine do not need to throw exception as it will automatically handle timeout status in pending events
                    {
                        throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
                    }
                }

                if (CommonUtil.traceEnabled() && !errors.isEmpty())
                {
                    LOGGER.trace(String.format("%s engine %s worker %s errors %s", pluginEngine.getName(), eventType, worker.pid(), errors));
                }

                if (reply)
                {
                    var response = builder.toString();

                    if (!builder.isEmpty() && response.contains(VALUE_SEPARATOR)) // send results
                    {
                        for (var result : response.split(VALUE_SEPARATOR_WITH_ESCAPE))
                        {
                            output.clear().mergeIn(new JsonObject(new String(Base64.getDecoder().decode(result.trim().replace(VALUE_SEPARATOR, EMPTY_VALUE)))));

                            if (output.containsKey(WORKER_ERROR) && output.getString(WORKER_ERROR).equalsIgnoreCase(YES))
                            {
                                send(events, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, INTERNAL_ERROR, output.getString(ERROR),
                                        STATUS_FAIL, props, liveStreaming, eventType);

                                break;
                            }
                            else
                            {
                                send(output, eventType, liveStreaming, events, event, false, props, latency);
                            }
                        }
                    }

                    // for pending events send response if error code is timeout then timeout else mark it as an internal error..
                    // internal error would be anything in plugin side like null pointer exception or any runtime exception or process issue
                    send(events, errorCode, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : INTERNAL_ERROR, null,
                            errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL, props, liveStreaming, eventType);
                }
            }
        }
        catch (Exception exception)
        {
            if (worker != null)
            {
                LOGGER.warn(String.format("getting exception for %s worker %s", eventType, worker.pid()));

                for (var eventId : eventIds)
                {
                    EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_FINISHED, worker.pid(), DateTimeUtil.timestamp()));
                }
            }

            LOGGER.error(exception);

            if (reply) // if any exception occurred here then send response and complete event
            {
                // for custom plugin we throw exception so here check that if exception is timeout then change it
                if (!errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) && exception.getMessage() != null && exception.getMessage().contains(PROCESS_TIMED_OUT))
                {
                    errorCode = ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT;
                }

                send(events, errorCode, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : INTERNAL_ERROR,
                        CommonUtil.formatStackTrace(exception.getStackTrace()), errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL, props, liveStreaming, eventType);
            }
        }
        finally
        {
            if (inputStream != null)
            {
                inputStream.close();
            }

            if (bufferedReader != null)
            {
                bufferedReader.close();
            }

            if (!reply) // for ui event handler will return result and exit code
            {
                if (worker != null && !worker.isAlive())
                {
                    output.put(EXIT_CODE, worker.exitValue());
                }
                else
                {
                    output.put(EXIT_CODE, EXIT_CODE_TIMED_OUT);
                }

                output.put(RESULT, builder.toString().trim());
            }
        }

        return output;
    }

    // for agent based process spawner
    public static JsonObject spawnWorker(JsonObject event, int timeout, boolean streaming, boolean customPlugin, PluginEngineConstants.PluginEngine pluginEngine) throws Exception
    {
        var builder = new StringBuilder(0);

        var result = new JsonObject();

        InputStream inputStream = null;

        BufferedReader bufferedReader = null;

        var eventId = event.getLong(EventBusConstants.EVENT_ID);

        Process worker = null;

        if (customPlugin)
        {
            builder.append(spawnWorker(event, eventId));
        }
        else
        {
            try
            {
                EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

                var arguments = new ArrayList<String>();

                var qualifiedContext = Base64.getEncoder().encodeToString(new JsonObject().put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).put(INSTALLATION_TYPE, MotadataConfigUtil.getInstallationType()).put("maximum.cpu.cores", MotadataConfigUtil.getPluginEngineCPUCores()).put(PLUGIN_CONTEXTS, new JsonArray(new ArrayList<JsonObject>(1)).add(event)).encode().getBytes());

                FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + PLUGIN_CONTEXT_DIR + PATH_SEPARATOR + event.getLong(EventBusConstants.EVENT_ID)), qualifiedContext, StandardCharsets.UTF_8);

                if (pluginEngine == PluginEngineConstants.PluginEngine.GO)
                {
                    arguments.add(CURRENT_DIR + PATH_SEPARATOR + GO_PLUGIN_ENGINE_DIR + PATH_SEPARATOR + GO_PLUGIN_ENGINE_BIN);
                }
                else if (pluginEngine == PluginEngineConstants.PluginEngine.DOTNET)
                {
                    arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + DOTNET_ENGINE_DIR + PATH_SEPARATOR + DOTNET_PLUGIN_ENGINE_BIN);
                }
                else
                {
                    arguments.add(GlobalConstants.PYTHON_BIN);

                    arguments.add(CURRENT_DIR + PATH_SEPARATOR +
                            GlobalConstants.PYTHON_PLUGIN_ENGINE_DIR + PATH_SEPARATOR + GlobalConstants.PYTHON_PLUGIN_ENGINE_BIN);
                }

                arguments.add(CommonUtil.getString(event.getLong(EventBusConstants.EVENT_ID)));

                var processBuilder = new ProcessBuilder(arguments);

                worker = processBuilder.start();

                EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_STARTED, worker.pid(), DateTimeUtil.timestamp()));

                if (streaming)
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().mergeIn(event).put(WORKER_CONTEXT, CodecUtil.toBytes(worker)));
                }

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("%s plugin engine worker %s started for %s", pluginEngine.getName(), worker.pid(), qualifiedContext));
                }

                setupCleanupTimer(timeout, worker);

                inputStream = worker.getInputStream();

                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

                String line;

                while ((line = bufferedReader.readLine()) != null)
                {
                    builder.append(line);

                    if (streaming)
                    {
                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().mergeIn(event).mergeIn(new JsonObject(new String(Base64.getDecoder().decode(line.trim().replace("\\n", GlobalConstants.EMPTY_VALUE).replace(VALUE_SEPARATOR, GlobalConstants.EMPTY_VALUE))))));
                    }
                }

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("received output %s for worker %s", builder, worker.pid()));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                throw exception;
            }
            finally
            {
                EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_FINISHED, worker != null ? worker.pid() : DUMMY_ID, DateTimeUtil.timestamp()));

                if (inputStream != null)
                {
                    inputStream.close();
                }

                if (bufferedReader != null)
                {
                    bufferedReader.close();
                }
            }
        }

        return result.put(RESULT, builder.toString().trim());
    }

    public static String spawnWorker(JsonObject event, long eventId) throws Exception
    {
        var builder = new StringBuilder();

        String script = null;

        String file = null;

        InputStream inputStream = null;

        BufferedReader bufferedReader;

        Process worker = null;

        try
        {
            EventBusConstants.startEvent(eventId, Thread.currentThread().getName());

            var arguments = new ArrayList<String>();

            if (event.getString(PluginEngineConstants.SCRIPT_LANGUAGE).equalsIgnoreCase(PluginEngineConstants.PluginEngine.PYTHON.getName()))
            {
                arguments.add(PYTHON_BIN);

                script = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                        CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + eventId + ".py";
            }
            else if (event.getString(PluginEngineConstants.SCRIPT_LANGUAGE).equalsIgnoreCase(PluginEngineConstants.PluginEngine.GO.getName()))
            {
                arguments.add(GO_BIN);

                arguments.add(GO_RUN_BIN);

                script = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                        CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + eventId + ".go";
            }
            else
            {
                return EMPTY_VALUE;
            }

            FileUtils.writeStringToFile(new File(script), event.containsKey(PluginEngineConstants.SCRIPT) ? event.getString(PluginEngineConstants.SCRIPT) : event.getString(PluginEngineConstants.PARSING_SCRIPT), StandardCharsets.UTF_8);

            arguments.add(script);

            arguments.add("--context");

            arguments.add(UUID.randomUUID().toString());

            var context = Base64.getEncoder().encodeToString(event.encode().getBytes());

            file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR +
                    PLUGIN_CONTEXT_DIR + PATH_SEPARATOR + arguments.getLast();

            FileUtils.writeStringToFile(new File(file), context, StandardCharsets.UTF_8);

            var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

            worker = processBuilder.start();

            EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_STARTED, worker.pid(), DateTimeUtil.timestamp()));

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("worker %s started for %s", worker.pid(), context));
            }

            setupCleanupTimer(event.containsKey(TIMEOUT) ? event.getInteger(TIMEOUT) : 60, worker);

            inputStream = worker.getInputStream();

            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

            String line;

            while ((line = bufferedReader.readLine()) != null)
            {
                builder.append(line);
            }

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("received output %s for worker %s", builder, worker.pid()));
            }
        }
        catch (Exception exception)
        {
            if (exception.getMessage() != null && !exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {
                LOGGER.error(exception);
            }
            throw exception;
        }
        finally
        {
            EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PLUGIN_PROCESS_FINISHED, worker != null ? worker.pid() : DUMMY_ID, DateTimeUtil.timestamp()));

            if (script != null)
            {
                FileUtils.deleteQuietly(new File(script));
            }

            if (file != null)
            {
                FileUtils.deleteQuietly(new File(file));
            }

            if (inputStream != null)
            {
                inputStream.close();
            }

        }

        return builder.toString();
    }

    public static void setupCleanupTimer(int seconds, Process worker, ProcessBuilder processBuilder, long eventId, JsonArray eventIDs, AtomicBoolean forcefully)
    {
        Bootstrap.vertx().setTimer(seconds * 1000L, timer ->
        {

            Bootstrap.vertx().cancelTimer(timer);

            if (worker.isAlive())
            {
                try
                {

                    if (CommonUtil.traceEnabled() && eventIDs != null)
                    {
                        LOGGER.trace(String.format("worker having PID %s got timeout for following events %s", worker.pid(), eventIDs));
                    }

                    if (forcefully != null)
                    {
                        forcefully.set(true);
                    }

                    if (eventId != NOT_AVAILABLE)
                    {
                        EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PROCESS_KILLED, processBuilder.command(), DateTimeUtil.timestamp()));
                    }

                    // this will kill all child processes in the process tree of this worker process
                    var childProcesses = worker.descendants().toList();

                    LOGGER.warn(String.format("killing child processes %s forcibly...", childProcesses));

                    childProcesses.forEach(ProcessHandle::destroyForcibly);

                    LOGGER.warn(String.format("worker %s has to be killed forcibly...", worker.pid()));

                    /*
                    In the case of windows, closing the InputStream before destroying the process gets blocked, hence
                    first we need to destroy the process, and then will close the input and output streams
                    (destroy internally closes the input and output stream still for a safer side it's recommended to
                    close the streams)
                     */
                    worker.destroyForcibly();

                    worker.getOutputStream().close();

                    worker.getInputStream().close();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }

        });

    }

    private static void setupCleanupTimer(int seconds, Process worker)
    {
        Bootstrap.vertx().setTimer(seconds * 1000L, timer ->
        {

            Bootstrap.vertx().cancelTimer(timer);

            if (worker.isAlive())
            {
                try
                {
                    // this will kill all child processes in the process tree of this worker process
                    var childProcesses = worker.descendants().toList();

                    LOGGER.warn(String.format("killing child processes %s forcibly...", childProcesses));

                    childProcesses.forEach(ProcessHandle::destroyForcibly);

                    LOGGER.warn(String.format("worker %s has to be killed forcibly...", worker.pid()));

                    /*
                    In the case of windows, closing the InputStream before destroying the process gets blocked, hence
                    first we need to destroy the process, and then will close the input and output streams
                    (destroy internally closes the input and output stream still for a safer side it's recommended to
                    close the streams)
                     */
                    worker.destroyForcibly();

                    worker.getOutputStream().close();

                    worker.getInputStream().close();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }

        });

    }

    private static void send(JsonObject result, String eventType, boolean streaming, Map<Long, JsonObject> events, JsonObject event, boolean customPlugin, JsonArray props, long latency)
    {
        try
        {
            extractError(result);

            if (customPlugin)
            {
                result = events.remove(event.getLong(EventBusConstants.EVENT_ID)).mergeIn(result);
            }
            else if (streaming) //for live streaming do not remove events context
            {
                result = events.get(event.getLong(EventBusConstants.EVENT_ID)).mergeIn(result);
            }
            else
            {
                events.remove(result.getLong(EventBusConstants.EVENT_ID));
            }

            result.put(EventBusConstants.EVENT_LATENCY, CommonUtil.getFloat(System.currentTimeMillis() - latency));

            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
            {
                if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_REDISCOVER))
                {
                    // rediscovered objects
                    if (result.getJsonArray(NMSConstants.OBJECTS) != null && !result.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                    {
                        result.put(RESULT, new JsonObject()
                                .put(NMSConstants.OBJECTS, result.getJsonArray(NMSConstants.OBJECTS)));

                        result.remove(NMSConstants.OBJECTS);
                    }
                    else
                    {
                        extractErrorCode(result.put(STATUS, STATUS_FAIL), props.getString(EVENT_PREFIX), null, null, null);
                    }
                }
                else if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY)) // for discovery result append progress and credential profile name
                {
                    result.put(Discovery.DISCOVERY_PROGRESS, 100.00).put(MESSAGE, result.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) != null
                            ? String.format(DISCOVERY_CREDENTIAL_PROFILE_SUCCEEDED, result.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)) : DISCOVERY_OBJECT_DISCOVERED);
                }
                else
                {
                    result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                }
            }
            else
            {
                if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY) && CommonUtil.isNotNullOrEmpty(result.getString(AIOpsObject.OBJECT_TYPE)) && NMSConstants.APPLICATION_TYPES.contains(result.getString(AIOpsObject.OBJECT_TYPE)))
                {
                    var errors = result.getJsonArray(ERRORS);

                    if (errors != null && !errors.isEmpty() && errors.getJsonObject(0).getString(ERROR_CODE) != null)
                    {
                        result.put(ERROR_CODE, errors.getJsonObject(0).getString(ERROR_CODE)).put(MESSAGE, extractMessage(errors));
                    }
                    else
                    {
                        result.put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, UNKNOWN);
                    }
                }
                else
                {
                    var updatedProps = extractProps(eventType, result);

                    extractErrorCode(result.put(STATUS, STATUS_FAIL), streaming ? ErrorMessageConstants.LIVE_STREAMING_FAILED : updatedProps != null ? updatedProps.getString(EVENT_PREFIX) : props.getString(EVENT_PREFIX), null, null, eventType.equalsIgnoreCase(EventBusConstants.EVENT_PLUGIN_ENGINE) ? updatedProps != null ? null : result.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST) : null);
                }

                if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY))
                {
                    result.put(Discovery.DISCOVERY_PROGRESS, 100.00);
                }
            }

            send(result, streaming ? EventBusConstants.EVENT_STREAMING_BROADCAST : props.getString(EVENT_RESPONSE_TOPIC));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            extract(result, CommonUtil.formatStackTrace(exception.getStackTrace()), INTERNAL_ERROR, props, streaming, STATUS_FAIL, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, eventType);
        }
    }

    private static void send(Map<Long, JsonObject> events, String errorCode, String message, String error, String status, JsonArray props, boolean liveStreaming, String eventType)
    {
        try
        {
            var iterator = events.keySet().iterator();

            while (iterator.hasNext())
            {
                extract(events.get(iterator.next()), error, message, props, liveStreaming, status, errorCode, eventType);

                iterator.remove();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private static void extract(JsonObject result, String error, String message, JsonArray props, boolean streaming, String status, String errorCode, String eventType)
    {
        if (error != null)
        {
            result.put(ERROR, error).put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR, error).put(ERROR_CODE, errorCode).put(MESSAGE, message)));
        }
        else
        {
            result.put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, message).put(ERROR_CODE, errorCode)));
        }

        var updatedProps = extractProps(eventType, result);

        var prefix = updatedProps != null ? updatedProps.getString(EVENT_PREFIX) : props.getString(EVENT_PREFIX);

        result.put(MESSAGE, result.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_PLUGIN_ENGINE) ?
                updatedProps != null ? String.format(prefix, message) : String.format(prefix, result.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST), message)
                : String.format(prefix, message));

        if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_DISCOVERY))
        {
            result.put(Discovery.DISCOVERY_PROGRESS, 100.00);
        }

        send(result.put(STATUS, status).put(ERROR_CODE, errorCode), streaming ? EventBusConstants.EVENT_STREAMING_BROADCAST : props.getString(EVENT_RESPONSE_TOPIC));
    }

    private static void send(JsonObject result, String responseType)
    {
        EventBusConstants.updateEvent(result.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        if (CommonUtil.parsingRequired(result))
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE_PROCESSOR, result.put(GlobalConstants.TIMEOUT, 30));
        }
        else if (responseType.equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL_RESPONSE))
        {
            if (MotadataConfigUtil.devMode())
            {
                Bootstrap.vertx().eventBus().publish(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE, result);
            }
            else
            {
                Bootstrap.vertx().eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE, result);
            }
        }
        else
        {
            Bootstrap.vertx().eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : responseType, result);
        }
    }

    public static int getWorkers(int pendingMetrics, int batchSize, int pendingWorkers)
    {
        var workers = pendingMetrics > 0 ? pendingMetrics > batchSize ? (pendingMetrics % batchSize) > 0 ? (pendingMetrics / batchSize) + 1 : (pendingMetrics / batchSize) : 1 : 0;

        if (workers > pendingWorkers)
        {
            workers = pendingWorkers;
        }

        return workers;
    }

    /**
     * Added method to override the default message
     *
     * @param eventType Event Type
     * @param event     Event
     */
    private static JsonArray extractProps(String eventType, JsonObject event)
    {
        try
        {
            if (event != null && EventBusConstants.EVENT_PLUGIN_ENGINE.equals(eventType))
            {
                var pluginEngineRequestType = event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST);

                if (PluginEngineConstants.PluginEngineRequest.valueOfName(pluginEngineRequestType) == PluginEngineConstants.PluginEngineRequest.CONFIG)
                {
                    return CONTEXTS.get(EventBusConstants.EVENT_CONFIG_MANAGE);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return null;
    }
}
