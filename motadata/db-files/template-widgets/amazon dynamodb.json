[{"_type": "0", "id": 10000000000089, "visualization.name": "Table Size", "visualization.description": "DynamoDB Table Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.table.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.table.size.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.table.size.bytes", "icon": {"name": "table", "placement": "prefix"}}, "header": {"title": "Table Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.dynamodb.table.size.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000090, "visualization.name": "Throttled Requests", "visualization.description": "DynamoDB Throttled Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.throttled.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.throttled.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.throttled.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Throttled Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.dynamodb.throttled.requests.last"}]}}}}, {"_type": "0", "id": 10000000000091, "visualization.name": "Avg Latency", "visualization.description": "DynamoDB Avg Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.query.request.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.put.request.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.scan.request.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.query.request.latency.ms"}, {"type": "metric", "data.point": "aws.dynamodb.put.request.latency.ms"}, {"type": "metric", "data.point": "aws.dynamodb.scan.request.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.query.request.latency.ms", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "Avg Latency", "style": {"font.size": "medium"}, "data.points": [{"label": "Request Latency", "value": "aws.dynamodb.query.request.latency.ms.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Put", "value": "aws.dynamodb.put.request.latency.ms.last"}, {"label": "<PERSON><PERSON>", "value": "aws.dynamodb.scan.request.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000000092, "visualization.name": "Provisioned", "visualization.description": "DynamoDB Provisioned", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.provisioned.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.provisioned.writes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.provisioned.reads.per.sec"}, {"type": "metric", "data.point": "aws.dynamodb.provisioned.writes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.provisioned.writes.per.sec", "icon": {"name": "provision", "placement": "prefix"}}, "header": {"title": "Provisioned", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.dynamodb.provisioned.reads.per.sec.last"}, {"label": "Write", "value": "aws.dynamodb.provisioned.writes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000093, "visualization.name": "User Errors", "visualization.description": "DynamoDB User Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.user.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.user.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.user.errors", "icon": {"name": "user-error", "placement": "prefix"}}, "header": {"title": "User Errors", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.dynamodb.user.errors.last"}]}}}}, {"_type": "0", "id": 10000000000094, "visualization.name": "Query System Error", "visualization.description": "DynamoDB System Error", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.query.system.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.dynamodb.query.system.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.dynamodb.query.system.errors", "icon": {"name": "error-connections", "placement": "prefix"}}, "header": {"title": "Query System Error", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.dynamodb.query.system.errors.last"}]}}}}, {"_type": "0", "id": 10000000000095, "visualization.name": "Provisioned Reads/Writes", "visualization.description": "DynamoDB Provisioned Reads/Writes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.provisioned.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.provisioned.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000096, "visualization.name": "Provisioned Read/Write Utilization", "visualization.description": "DynamoDB Provisioned Read/Write Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.provisioned.read.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.provisioned.write.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000097, "visualization.name": "DynamoDB Reads/Writes Per Sec", "visualization.description": "DynamoDB DynamoDB Reads/Writes Per Sec", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000098, "visualization.name": "Returned Items", "visualization.description": "DynamoDB Returned Items", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.query.returned.items", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.dynamodb.scan.returned.items", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000099, "visualization.name": "Table Size", "visualization.description": "DynamoDB Table Size Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.table.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000100, "visualization.name": "User Errors", "visualization.description": "DynamoDB User Errors Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.user.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000101, "visualization.name": "Throttled Read", "visualization.description": "DynamoDB Throttled Read", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.read.throttled.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000102, "visualization.name": "Throttled Write", "visualization.description": "DynamoDB Throttled Write", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.dynamodb.write.throttled.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]