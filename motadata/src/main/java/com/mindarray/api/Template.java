/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	18-Feb-2025		Sankalp		        MOTADATA-5203: Stack template name - In case of stack devices routing protocol (L3) will not be appended in template name
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  11-Jun-2025		Jenil <PERSON>a	Added condition while rendering instance tab to prevent attachment of unwanted tabs in categories other than server.
 *  23-Jun-2025     Darshan Parmar      MOTADATA-6583 : Container Orchestration Category support added
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

import java.util.HashSet;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;


public class Template extends AbstractAPI
{
    public static final String TEMPLATE_NAME = "template.name";
    public static final String TEMPLATE_TABS = "template.tabs";
    public static final String TEMPLATE_TAB_ID = "tab.id";
    public static final String TEMPLATE_TEXT = "text";

    private static final Logger LOGGER = new Logger(Template.class, MOTADATA_API, "Template API");

    public Template()
    {
        super("templates", TemplateConfigStore.getStore(), LOGGER);
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var context = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            var item = ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID));

            switch (NMSConstants.Category.valueOfName(item.getString(AIOpsObject.OBJECT_CATEGORY)))
            {
                case SERVER ->
                {

                    if (context.containsKey(NMSConstants.APPLICATION))
                    {
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(context.getString(NMSConstants.APPLICATION))))));

                    }

                    else
                    {
                        if (item.containsKey(AIOpsObject.OBJECT_AGENT) && item.getLong(AIOpsObject.OBJECT_AGENT) != null && AgentConfigStore.getStore().getItemByValue(ID, item.getLong(AIOpsObject.OBJECT_AGENT)) != null)
                        {
                            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, true)).put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE) + " Agent")))));
                        }

                        else
                        {
                            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, true)).put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));
                        }
                    }
                }

                case NETWORK ->
                {

                    var objectType = item.getString(AIOpsObject.OBJECT_TYPE);

                    if (objectType.equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) ||
                            objectType.equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()) ||
                            objectType.equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName()))
                    {
                        if (context.containsKey(NMSConstants.Type.ACCESS_POINT.getName()))
                        {
                            var templateId = 0L;

                            if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()))
                            {
                                templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.CISCO_WIRELESS_ACCESS_POINT.getName()));
                            }

                            else if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()))
                            {
                                templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.ARUBA_WIRELESS_ACCESS_POINT.getName()));
                            }

                            else
                            {
                                templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.RUCKUS_WIRELESS_ACCESS_POINT.getName()));
                            }

                            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, templateId).put(TEMPLATE_TABS, getTemplateTabs(item, false))));
                        }
                        else
                        {
                            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(objectType))).put(TEMPLATE_TABS, getTemplateTabs(item, false))));
                        }
                    }
                    else
                    {
                        var metrics = MetricConfigStore.getStore().getMetricNamesByObject(context.getLong(ENTITY_ID));

                        var templateName = item.getString(AIOpsObject.OBJECT_VENDOR) + " " + item.getString(AIOpsObject.OBJECT_TYPE) + " " + item.getString(AIOpsObject.OBJECT_MAKE_MODEL);

                        if (CommonUtil.getLong(VisualizationConstants.Template.getTemplate(templateName)) == VisualizationConstants.DEFAULT_NETWORK_TEMPLATE)
                        {
                            templateName = item.getString(AIOpsObject.OBJECT_VENDOR) + " " + item.getString(AIOpsObject.OBJECT_TYPE);

                            if (!objectType.equalsIgnoreCase(NMSConstants.Type.HARDWARE_SENSOR.getName()) && !objectType.equalsIgnoreCase(NMSConstants.Type.EMAIL_GATEWAY.getName()))
                            {
                                // While creating template name Stack will be given priority over Routing Protocol (L3)
                                templateName += (metrics.stream().map(Object::toString).anyMatch(value -> value.contains("Stack")) ? " Stack" : (metrics.contains("Routing Protocol") ? " L3" : EMPTY_VALUE));
                            }
                        }

                        var result = new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(templateName)));

                        filterMetrics(MetricPluginConfigStore.getStore().getItemsByValue(MetricPlugin.METRIC_PLUGIN_PROTOCOL, NMSConstants.Protocol.CUSTOM.getName()), item).forEach(entry ->
                        {
                            if (!result.containsKey(MetricPlugin.METRIC_PLUGIN_PROTOCOL) && !metrics.contains(entry.getKey()))
                            {
                                result.put(MetricPlugin.METRIC_PLUGIN_PROTOCOL, entry.getValue());
                            }
                        });

                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.put(TEMPLATE_TABS, getTemplateTabs(item, false))));//default template id of snmp
                    }
                }

                case VIRTUALIZATION ->
                {

                    if (context.containsKey(NMSConstants.Type.VIRTUAL_MACHINE.getName()))
                    {
                        var templateId = 0L;

                        if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.VMWARE_ESXI.getName()))
                        {
                            templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.VMWARE_ESXI_VM.getName()));
                        }

                        else if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CITRIX_XEN.getName()))
                        {
                            templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.CITRIX_XEN_VM.getName()));
                        }

                        else
                        {
                            templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.HYPER_V_VM.getName()));
                        }

                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, false)).put(VisualizationConstants.TEMPLATE_ID, templateId)));
                    }
                    else
                    {
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, false)).put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));
                    }
                }

                case CLOUD ->
                {

                    if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.AWS_ELB.getName()))
                    {
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE) + " " + StringUtils.capitalize(item.getString(AIOpsObject.OBJECT_MAKE_MODEL).toLowerCase()))))));
                    }

                    else
                    {
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));
                    }
                }

                case SDN ->
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, false)).put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));

                case SERVICE_CHECK, OTHER, STORAGE, CONTAINER_ORCHESTRATION ->
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));

                case HCI ->
                {
                    if (context.containsKey(NMSConstants.Type.VIRTUAL_MACHINE.getName()))
                    {
                        var templateId = 0L;

                        if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.NUTANIX.getName()))
                        {
                            templateId = CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.NUTANIX_VM.getName()));
                        }

                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, false)).put(VisualizationConstants.TEMPLATE_ID, templateId)));

                    }
                    else
                    {
                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject().put(TEMPLATE_TABS, getTemplateTabs(item, false)).put(VisualizationConstants.TEMPLATE_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(item.getString(AIOpsObject.OBJECT_TYPE))))));
                    }
                }

                default ->
                {
                    // do nothing
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    private JsonObject filterMetrics(JsonArray items, JsonObject item)
    {
        var metrics = new JsonObject();

        for (var index = 0; index < items.size(); index++)
        {

            var context = items.getJsonObject(index);

            if (context.containsKey(MetricPlugin.METRIC_PLUGIN_VENDOR)
                    && context.getString(MetricPlugin.METRIC_PLUGIN_VENDOR).equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_VENDOR))
                    && context.getString(MetricPlugin.METRIC_PLUGIN_TYPE).equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_TYPE)))
            {
                metrics.put(context.getString(MetricPlugin.METRIC_PLUGIN_NAME), context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).getString(PluginEngineConstants.SCRIPT_PROTOCOL));
            }
        }

        return metrics;
    }

    /*
     *  this method used to get template tabs of applications if any application has been provisioned on SERVER.
     *  and event templates if entity qualified as event.source of any log, flow or trap.
     * */
    private JsonArray getTemplateTabs(JsonObject item, boolean hasApps)
    {
        var tabs = new JsonArray();

        var tabIds = new HashSet<Long>();

        var entity = EventSourceConfigStore.getStore().getItemByHost(item.getString(AIOpsObject.OBJECT_IP), false);

        if (entity != null && entity.containsKey(EventBusConstants.EVENT_TYPE) && !entity.getJsonArray(EventBusConstants.EVENT_TYPE).isEmpty())
        {
            var eventTypes = entity.getJsonArray(EventBusConstants.EVENT_TYPE);

            if (eventTypes.contains(EventBusConstants.EVENT_LOG) && (DBConstants.ENTITY_TYPE_USER.equalsIgnoreCase(entity.getString(DBConstants.FIELD_TYPE)) || (entity.getJsonArray(LogEngineConstants.EVENT_CATEGORY) != null && !entity.getJsonArray(LogEngineConstants.EVENT_CATEGORY).isEmpty())))
            {
                tabs.add(new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.LOG_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.LOG_ANALYTICS.getName()))));
            }

            if (eventTypes.contains(EventBusConstants.EVENT_TRAP))
            {
                tabs.add(new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.TRAP_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.TRAP_ANALYTICS.getName()))));
            }

            if (eventTypes.contains(EventBusConstants.EVENT_FLOW))
            {
                var template = TemplateConfigStore.getStore().getItem(CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.FLOW_ANALYTICS.getName())));

                var tab = new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.FLOW_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, template.getLong(ID));

                if (template.containsKey(TEMPLATE_TABS) && !template.getJsonArray(TEMPLATE_TABS).isEmpty())
                {
                    tab.put(TEMPLATE_TABS, template.getJsonArray(TEMPLATE_TABS));   // adding child templates
                }

                tabs.add(tab);
            }
        }

        if (hasApps)
        {
            // it will return list of provisioned applications
            var apps = NMSConstants.getServerApps(MetricConfigStore.getStore().getItemsByObject(item.getLong(ID))).getJsonArray(NMSConstants.APPS);

            if (!apps.isEmpty())
            {
                for (var index = 0; index < apps.size(); index++)
                {
                    var tab = new JsonObject();

                    var app = apps.getJsonObject(index);

                    var tabId = VisualizationConstants.Template.getTemplate(app.getString(Metric.METRIC_TYPE));

                    var appTabs = TemplateConfigStore.getStore().getItem(tabId).getJsonArray(TEMPLATE_TABS, new JsonArray());

                    tab.put(TEMPLATE_TEXT, app.getString(Metric.METRIC_TYPE)).put(TEMPLATE_TAB_ID, tabId);

                    // need to attach list of child tabs to show drop-down.
                    if (appTabs.size() == 1 && appTabs.getJsonObject(0).getLong(TEMPLATE_TAB_ID).equals(tabId))
                    {
                        appTabs.remove(0); // in case there is only one tab which is representing parent tab... need to remove redundant tab
                    }

                    tab.put(TEMPLATE_TABS, appTabs);

                    tabIds.add(tabId);

                    tabs.add(tab);
                }
            }
        }

        var metrics = MetricConfigStore.getStore().getItemsByObject(item.getLong(ID));

        // To attach tab of instance of server (i.e. docker) we have to get metrics of object.
        // We have to check for only server category, because in case of categories other than server, it will attach unwanted tabs (i.e. attach access.point tab in wireless template, attach vm tab in HCI).
        if (item.containsKey(AIOpsObject.OBJECT_CATEGORY) && item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && !metrics.isEmpty())
        {
            for (var metric : metrics)
            {
                var tab = new JsonObject();

                var tabId = VisualizationConstants.Template.getTemplate(metric.getString(Metric.METRIC_NAME));

                if (!tabIds.contains(tabId) && tabId != VisualizationConstants.DEFAULT_NETWORK_TEMPLATE && !metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_TYPE)))

                {

                    var items = TemplateConfigStore.getStore().getItem(tabId).getJsonArray(TEMPLATE_TABS, new JsonArray());

                    tab.put(TEMPLATE_TEXT, metric.getString(Metric.METRIC_NAME)).put(TEMPLATE_TAB_ID, tabId);

                    // need to attach list of child tabs to show drop-down.
                    if (items.size() == 1 && items.getJsonObject(0).getLong(TEMPLATE_TAB_ID).equals(tabId))
                    {
                        items.remove(0); // in case there is only one tab which is representing parent tab... need to remove redundant tab
                    }

                    tab.put(TEMPLATE_TABS, items);

                    tabIds.add(tabId);

                    tabs.add(tab);

                }
            }
        }

        return tabs;
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        return Future.succeededFuture();
    }

}