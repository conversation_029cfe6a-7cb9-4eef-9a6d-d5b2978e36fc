/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added method to get user groups by id
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.PersonalAccessToken;
import com.mindarray.api.User;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;

import java.util.List;
import java.util.Map;

import static com.mindarray.GlobalConstants.DEFAULT_ID;
import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.api.APIConstants.*;

public class UserConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(UserConfigStore.class, GlobalConstants.MOTADATA_STORE, "User Config Store");

    private static final UserConfigStore STORE = new UserConfigStore();

    private UserConfigStore()
    {
        super(DBConstants.TBL_USER, LOGGER, false, List.of(
                Map.of(
                        REFERENCE_ENTITY, Entity.PERSONAL_ACCESS_TOKEN,
                        REFERENCE_ENTITY_PROPERTY, PersonalAccessToken.PERSONAL_ACCESS_TOKEN_USER,
                        REFERENCE_ENTITY_STORE, ConfigStore.PERSONAL_ACCESS_TOKEN,
                        REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                )
        ));
    }

    public static UserConfigStore getStore()
    {
        return STORE;
    }

    public JsonArray getUserGroupsById(Long id)
    {
        if (id.equals(DEFAULT_ID))//default
        {
            return GroupConfigStore.getStore().flatItems(ID);
        }
        else
        {
            return UserConfigStore.getStore().getItem(id).getJsonArray(User.USER_GROUPS);
        }
    }
}
