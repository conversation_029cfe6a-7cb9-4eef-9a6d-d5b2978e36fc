{"127.0.0.1": {"_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [{"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_limit, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_limit, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_used, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_used, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\""}, {"error": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_consumption_percent, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\"", "error.code": "MD031", "message": "insights.MetricsClient#List: Failure responding to request: StatusCode=400 -- Original Error: autorest/azure: Service returned an error. Status=400 Code=\"BadRequest\" Message=\"Failed to find metric configuration for provider: Microsoft.Sql, resource Type: servers/databases, metric: dwu_consumption_percent, Valid metrics: cpu_percent,physical_data_read_percent,log_write_percent,dtu_consumption_percent,storage,connection_successful,connection_failed,blocked_by_firewall,deadlock,storage_percent,xtp_storage_percent,workers_percent,sessions_percent,dtu_limit,dtu_used,sqlserver_process_core_percent,sqlserver_process_memory_percent,tempdb_data_size,tempdb_log_size,tempdb_log_used_percent,allocated_data_storage,ledger_digest_upload_success,ledger_digest_upload_failed\""}], "event.id": 176993700500089, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure SQL Database", "metric.object": ***************, "metric.plugin": "azuresqldatabase", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Azure SQL Database", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.788 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 52, "object.name": "aqasde(DefaultResourceGroup-EUS2)", "object.resource.group": "DefaultResourceGroup-EUS2", "object.state": "ENABLE", "object.target": "aqasde(DefaultResourceGroup-EUS2)", "object.type": "Azure SQL Database", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 16, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"azure.location": "East US 2", "azure.sql.database": "aqasde", "azure.sql.database.cpu.percent": 0, "azure.sql.database.creation.time": " 363 days 6 hours 51 minutes 22 seconds", "azure.sql.database.creation.time.seconds": ********, "azure.sql.database.data.io.percent": 0, "azure.sql.database.data.storage.allocated.bytes": 33554432, "azure.sql.database.data.storage.used.bytes": 8978432, "azure.sql.database.data.storage.used.percent": 0, "azure.sql.database.deadlocks": 0, "azure.sql.database.dtu.limit": 5, "azure.sql.database.dtu.utilization.percent": 0, "azure.sql.database.earliest.restore.date": "2022-06-07T10:55:46.9576385Z", "azure.sql.database.elastic.pool.name": "", "azure.sql.database.failed.connections": 0, "azure.sql.database.failover.group.id": "", "azure.sql.database.firewall.blocked.connections": 0, "azure.sql.database.id": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/DefaultResourceGroup-EUS2/providers/Microsoft.Sql/servers/newmotadatasqlserver/databases/aqasde", "azure.sql.database.in.memory.oltp.storage.used.percent": 0, "azure.sql.database.log.io.percent": 0, "azure.sql.database.replication.links": 0, "azure.sql.database.resource.group": "DefaultResourceGroup-EUS2", "azure.sql.database.server": "newmotadatasqlserver", "azure.sql.database.server.fqdn": "newmotadatasqlserver.database.windows.net", "azure.sql.database.session.utilization.percent": 0, "azure.sql.database.storage.size.bytes": **********, "azure.sql.database.successful.connections": 0, "azure.sql.database.used.dtu": 0, "azure.sql.database.worker.utilization.percent": 0, "azure.status": "Online", "status": "Up", "cloud.metrics": [{"azure.sql.database.cpu.percent": 0, "azure.sql.database.data.io.percent": 0, "azure.sql.database.data.storage.allocated.bytes": 33554432, "azure.sql.database.data.storage.used.bytes": 26673152, "azure.sql.database.data.storage.used.percent": 1, "azure.sql.database.deadlocks": 0, "azure.sql.database.dtu.limit": 5, "azure.sql.database.dtu.utilization.percent": 0, "azure.sql.database.failed.connections": 0, "azure.sql.database.firewall.blocked.connections": 0, "azure.sql.database.in.memory.oltp.storage.used.percent": 0, "azure.sql.database.log.io.percent": 0, "azure.sql.database.session.utilization.percent": 0, "azure.sql.database.successful.connections": 0, "azure.sql.database.used.dtu": 0, "azure.sql.database.worker.utilization.percent": 0, "event.timestamp": 1698400020, "system.tags": ["Environment:Test", "Tag:<PERSON><PERSON>", "type:vm"]}, {"azure.sql.database.cpu.percent": 0, "azure.sql.database.data.io.percent": 0, "azure.sql.database.data.storage.allocated.bytes": 33554432, "azure.sql.database.data.storage.used.bytes": 26673152, "azure.sql.database.data.storage.used.percent": 1, "azure.sql.database.deadlocks": 0, "azure.sql.database.dtu.limit": 5, "azure.sql.database.dtu.utilization.percent": 0, "azure.sql.database.failed.connections": 0, "azure.sql.database.firewall.blocked.connections": 0, "azure.sql.database.in.memory.oltp.storage.used.percent": 0, "azure.sql.database.log.io.percent": 0, "azure.sql.database.session.utilization.percent": 0, "azure.sql.database.successful.connections": 0, "azure.sql.database.used.dtu": 0, "azure.sql.database.worker.utilization.percent": 0, "event.timestamp": 1698400080}], "azure.service.queue": [{"azure.service.queue": "demoq1", "azure.service.queue.active.messages": 4, "azure.service.queue.dead.letter.messages": 0, "azure.service.queue.default.message.ttl": "1.00:00:00", "azure.service.queue.max.deliveries": 2, "azure.service.queue.max.in.bytes": 1073741824, "azure.service.queue.messages": 4, "azure.service.queue.scheduled.messages": 0, "azure.service.queue.size.in.bytes": 752, "azure.service.queue.status": "Active", "azure.service.queue.transferred.dead.letter.messages": 0, "azure.service.queue.transferred.messages": 0, "cloud.metrics": [{"azure.sql.database.cpu.percent": 0, "azure.sql.database.data.io.percent": 0, "azure.sql.database.data.storage.allocated.bytes": 33554432, "azure.sql.database.data.storage.used.bytes": 26673152, "azure.sql.database.data.storage.used.percent": 1, "azure.sql.database.deadlocks": 0, "azure.sql.database.dtu.limit": 5, "azure.sql.database.dtu.utilization.percent": 0, "azure.sql.database.failed.connections": 0, "azure.sql.database.firewall.blocked.connections": 0, "azure.sql.database.in.memory.oltp.storage.used.percent": 0, "azure.sql.database.log.io.percent": 0, "azure.sql.database.session.utilization.percent": 0, "azure.sql.database.successful.connections": 0, "azure.sql.database.used.dtu": 0, "azure.sql.database.worker.utilization.percent": 0, "event.timestamp": 1698400020, "system.tags": ["Environment:Test", "Tag:<PERSON><PERSON>", "type:vm"]}]}]}, "status": "succeed", "timeout": 60}}