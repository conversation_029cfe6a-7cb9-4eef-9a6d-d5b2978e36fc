[{"_type": "0", "id": 10000000000305, "visualization.name": "Connection", "visualization.description": "ServiceBus Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.opened.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.closed.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.active.connections"}, {"type": "metric", "data.point": "azure.psb.opened.connections"}, {"type": "metric", "data.point": "azure.psb.closed.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "Active Connection", "value": "azure.psb.active.connections.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Closed", "value": "azure.psb.closed.connections.last"}, {"label": "Opened", "value": "azure.psb.opened.connections.last"}]}}}}, {"_type": "0", "id": 10000000000306, "visualization.name": "Active Message", "visualization.description": "ServiceBus Active Message", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.active.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.active.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.active.messages", "icon": {"name": "success-message", "placement": "prefix"}}, "header": {"title": "Active Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.psb.active.messages.last"}]}}}}, {"_type": "0", "id": 10000000000307, "visualization.name": "Dead Letter", "visualization.description": "ServiceBus Dead Letter", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.dead.lettered.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.dead.lettered.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.dead.lettered.messages", "icon": {"name": "file-close", "placement": "prefix"}}, "header": {"title": "Dead Letter", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.psb.dead.lettered.messages.last"}]}}}}, {"_type": "0", "id": 10000000000308, "visualization.name": "Server Errors", "visualization.description": "ServiceBus Server Errors", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.server.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.server.errors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.server.errors", "icon": {"name": "errors", "placement": "prefix"}}, "header": {"title": "Server Errors", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.psb.server.errors.last"}]}}}}, {"_type": "0", "id": 10000000000309, "visualization.name": "Messages", "visualization.description": "ServiceBus Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.incoming.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.outgoing.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.incoming.messages"}, {"type": "metric", "data.point": "azure.psb.outgoing.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.incoming.messages", "icon": {"name": "messages", "placement": "prefix"}}, "header": {"title": "Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Incoming", "value": "azure.psb.incoming.messages.last"}, {"label": "Outgoing", "value": "azure.psb.outgoing.messages.last"}]}}}}, {"_type": "0", "id": 10000000000310, "visualization.name": "Requests", "visualization.description": "ServiceBus Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.incoming.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.successful.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.psb.incoming.requests"}, {"type": "metric", "data.point": "azure.psb.successful.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.psb.incoming.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Incoming", "value": "azure.psb.incoming.requests.last"}, {"label": "Successful", "value": "azure.psb.successful.requests.last"}]}}}}, {"_type": "0", "id": 10000000000311, "visualization.name": "Connections", "visualization.description": "ServiceBus Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.closed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.opened.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000312, "visualization.name": "Messages", "visualization.description": "ServiceBus Messages Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.abandoned.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.active.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.scheduled.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000313, "visualization.name": "Requests", "visualization.description": "ServiceBus Requests Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.incoming.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.successful.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000314, "visualization.name": "Service Queue Messages", "visualization.description": "ServiceBus Service Queue Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["azure.service.queue"], "data.points": [{"data.point": "azure.service.queue~active.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}, "visualization.result.by": ["azure.service.queue"]}}, {"_type": "0", "id": 10000000000315, "visualization.name": "Message Stats", "visualization.description": "ServiceBus Message Stats", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.incoming.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.psb.outgoing.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000316, "visualization.name": "Server Errors", "visualization.description": "ServiceBus Server Errors Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.server.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000317, "visualization.name": "Traffic Volume", "visualization.description": "ServiceBus Traffic Volume", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000318, "visualization.name": "Throttled Requests", "visualization.description": "ServiceBus Throttled Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.throttled.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000319, "visualization.name": "User Errors", "visualization.description": "ServiceBus User Errors", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.psb.user.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]