/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ArtifactConfigStore;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;

public class DiagnosticUtil extends AbstractVerticle
{
    public static final String MOTADATA_DIAGNOSTIC = "Motadata Diagnostic Logs ";
    private static final JsonObject STATS = new JsonObject();
    private static final String DIAGNOSTIC_LOGS = "diagnostic-logs";
    private static final String PATH = CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS + PATH_SEPARATOR;
    private static final Logger LOGGER = new Logger(DiagnosticUtil.class, GlobalConstants.MOTADATA_UTIL, "Diagnostic Util");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.setPeriodic(MotadataConfigUtil.getEventEngineStatFlushTimerSeconds() * 1000L, timer ->
        {

            if (!STATS.isEmpty())
            {
                vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + EventBusConstants.EVENT_DIR + PATH_SEPARATOR + "engine-stats.json", STATS.toBuffer());
            }
        });

        /*
         *  First, we will create statistics directory and store all the statistics data in it,
         *  after collection we will zip the directory and move it to diagnostics directory,
         *  and we will remove statistics director
         * */
        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_DIAGNOSTIC, message ->
        {
            var event = message.body();

            var app = Bootstrap.getRegistrationId().equalsIgnoreCase(event.getString(REMOTE_EVENT_PROCESSOR_UUID)) && event.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(EVENT_APP);

            if (!Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS))
            {
                Bootstrap.vertx().fileSystem().mkdirs(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS);
            }

            var futures = new ArrayList<Future<Void>>();

            futures.add(dumpStats(event.getString(REMOTE_EVENT_PROCESSOR_TYPE))); // will collect last two hours logs, engine-stats, heap-stats, thread-dump

            if (app)//in case of master App will be collecting config level data
            {
                futures.add(dumpConfigs());//will collect remote event processor config store and artifact config store in file

                futures.add(dumpFaultyObjects());//will collect failed monitors
            }

            Future.join(futures).onComplete(result ->
            {
                LOGGER.info("All Stats are collected, Zipping the Folder...");

                try
                {
                    if (app)
                    {
                        ZipUtil.pack(new File(CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS), new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + MOTADATA_DIAGNOSTIC + Bootstrap.bootstrapType() + ".zip"), true);
                    }
                    else
                    {
                        ZipUtil.pack(new File(CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS), new File(CURRENT_DIR + PATH_SEPARATOR + MOTADATA_DIAGNOSTIC + Bootstrap.bootstrapType() + ".zip"), true);
                    }

                    FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + DIAGNOSTIC_LOGS));

                    if (app)
                    {
                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_DIAGNOSTIC_ZIP_AVAILABLE, new JsonObject().put(GlobalConstants.FILE_NAME, MOTADATA_DIAGNOSTIC + Bootstrap.bootstrapType() + ".zip").put(STATUS, STATUS_SUCCEED).put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

        });

        /*
         *  All engine stats are received from health-util
         * */
        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_DIAGNOSTIC_STATS, message ->
        {
            try
            {
                var event = message.body();

                if (event != null && !event.isEmpty() && event.containsKey(ENGINE_TYPE))
                {
                    STATS.put(event.getString(ENGINE_TYPE), event);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    private Future<Void> dumpFaultyObjects()
    {
        var promise = Promise.<Void>promise();

        NMSConstants.getFaultyObjects(false).onComplete(result ->
        {

            if (result.succeeded())
            {
                var items = result.result();

                if (!items.isEmpty())
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(PATH + "faulty-objects.json", Buffer.buffer(items.encodePrettily()));

                    LOGGER.info("Collected Faulty Objects...");

                    promise.complete();
                }
                else
                {
                    promise.fail("No Faulty Objects are available...");
                }
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> dumpStats(String bootstrapType)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var processBuilder = new ProcessBuilder(MotadataConfigUtil.devMode() ? "java" : GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "jdk" + GlobalConstants.PATH_SEPARATOR + "bin" + GlobalConstants.PATH_SEPARATOR + "java", "-jar", GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "diagnostic-utils.jar", bootstrapType);

            var process = processBuilder.start();

            //will kill the utility process after 25 sec...
            WorkerUtil.setupCleanupTimer(25, process, processBuilder, NOT_AVAILABLE, null, null);

            var exited = process.waitFor(20, TimeUnit.SECONDS);

            if (exited)
            {
                promise.complete();
            }
            else
            {
                LOGGER.warn("Error while spawning utility...");

                promise.fail("Error while spawning utility...");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> dumpConfigs()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = RemoteEventProcessorConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    item.mergeIn(RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID)));

                    if (item.containsKey(DURATION))
                    {
                        item.put(DURATION, DateTimeUtil.convertTime(item.getLong(DURATION)));
                    }

                    item.put(DISABLED, RemoteEventProcessorCacheStore.getStore().getStateDuration(item.getLong(ID)) > 0 ? YES : NO);
                }

                Bootstrap.vertx().fileSystem().writeFile(PATH + "deployment-settings.json", Buffer.buffer(items.encodePrettily())).onComplete(result ->
                {

                    if (result.succeeded())
                    {
                        LOGGER.info("Collected RemoteEventProcessor ConfigStore...");
                    }
                    else
                    {
                        LOGGER.warn("Error while Collecting RemoteEventProcessor ConfigStore...");

                        promise.fail(result.cause());
                    }
                });

            }

            items = ArtifactConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {
                Bootstrap.vertx().fileSystem().writeFile(PATH + "artifacts.json", Buffer.buffer(ArtifactConfigStore.getStore().getItems().encodePrettily())).onComplete(result ->
                {

                    if (result.succeeded())
                    {
                        LOGGER.info("Collected Artifact ConfigStore...");
                    }
                    else
                    {
                        LOGGER.warn("Error while Collecting Artifact ConfigStore...");

                        promise.fail(result.cause());
                    }
                });
            }

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}