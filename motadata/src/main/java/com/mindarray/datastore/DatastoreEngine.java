/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		<PERSON><PERSON>		MOTADATA-4878: refactored eventQueueSize initialization
 *  26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.HealthUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.HA_SYNC_TYPE;

public class DatastoreEngine
{
    private final AtomicBoolean hasMoreEvent = new AtomicBoolean(true);
    private final AtomicInteger queuedEvents = new AtomicInteger(0);
    private final AtomicInteger finishedEvents = new AtomicInteger(0);
    private Logger logger;
    private String eventType;
    private boolean persistEventOffset;
    private Handler<JsonObject> eventHandler;
    private String DatastoreEventEngineHelper;
    private int eventQueueSize = MotadataConfigUtil.getMetricDatastoreEngineQueueSize();
    private int eventSegments;
    private boolean status = true;

    public DatastoreEngine setEventType(String eventType)
    {
        this.eventType = eventType;
        return this;
    }

    public DatastoreEngine setPersistEventOffset(boolean persistEventOffset)
    {
        this.persistEventOffset = persistEventOffset;

        return this;
    }

    public DatastoreEngine setEventHandler(Handler<JsonObject> handler)
    {
        this.eventHandler = handler;
        return this;
    }

    public DatastoreEngine setStatus(boolean status)
    {
        this.status = status;
        return this;
    }

    public DatastoreEngine setEventQueueSize(int events)
    {
        this.eventQueueSize = events;
        return this;
    }

    public DatastoreEngine setLogger(Logger logger)
    {
        this.logger = logger;
        return this;
    }

    public DatastoreEngine start(Vertx vertx, Promise<Void> promise)
    {

        // check boot sequence to validate engine startup parameters

        try
        {
            if (CommonUtil.isNullOrEmpty(eventType))
            {
                promise.fail("Event Type is missing...");

                return this;
            }

            if (eventHandler == null) //why is odd condition ??? if event is getting handled by external event handler then no means of queuing events and event subscribers.
            {
                promise.fail("Event Handler is missing...");

                return this;
            }

            final var eventConsumerAddress = eventType + ".consume";

            final var eventProducerAddress = eventType + ".produce";

            vertx.eventBus().<byte[]>localConsumer(eventConsumerAddress, message ->
            {
                try
                {
                    eventHandler.handle(new JsonObject(Buffer.buffer(message.body())));
                }
                catch (Exception exception)
                {
                    logger.error(exception);
                }

                finally
                {
                    finishedEvents.incrementAndGet(); // for monitoring purpose

                    if (queuedEvents.decrementAndGet() == 0) // ask for more work, no need of timer
                    {
                        // send emit message...

                        vertx.eventBus().send(eventProducerAddress, eventQueueSize);
                    }
                }
            }).exceptionHandler(logger::error);

            startDatastoreEngineHelper(promise); //start producer as event writer...
        }

        catch (Exception exception)
        {
            logger.error(exception);

            promise.fail(exception);

            logger.fatal(String.format("failed to start event engine %s...", eventType));
        }

        return this;
    }

    public void stop(Vertx vertx, Promise<Void> promise)
    {
        try
        {
            hasMoreEvent.set(false);

            if (CommonUtil.isNotNullOrEmpty(DatastoreEventEngineHelper) && vertx.deploymentIDs().contains(DatastoreEventEngineHelper))
            {
                vertx.undeploy(DatastoreEventEngineHelper, result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        logger.error(result.cause());

                        promise.fail(result.cause());
                    }
                });
            }

            // no need to undeploy nested verticle, as undeploying parent verticle it will first undeploy all it's nested verticle.
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);

            promise.fail(exception);
        }
    }

    public void stop(Vertx vertx)
    {
        try
        {
            hasMoreEvent.set(false);

            if (CommonUtil.isNotNullOrEmpty(DatastoreEventEngineHelper) && vertx.deploymentIDs().contains(DatastoreEventEngineHelper))
            {
                vertx.undeploy(DatastoreEventEngineHelper, result ->
                {
                    if (result.failed())
                    {
                        logger.debug(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    private Future<Void> startDatastoreEngineHelper(Promise<Void> promise)
    {
        try
        {
            if (eventSegments < 1)
            {
                eventSegments = 2;
            }

            Bootstrap.vertx().deployVerticle(new DatastoreEngineHelper().setEventType(eventType)
                    .setPersist(persistEventOffset)
                    .setLogger(logger), new DeploymentOptions().setThreadingModel(ThreadingModel.WORKER).setWorkerPoolSize(1).setWorkerPoolName("wp." + eventType + ".helper"), result ->
            {
                if (result.succeeded())
                {
                    promise.complete();

                    DatastoreEventEngineHelper = result.result();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            logger.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    private class DatastoreEngineHelper extends AbstractVerticle
    {
        private Logger logger;
        private String eventType;
        private boolean persist;
        private Datastore eventStore;
        private long pendingEvents;
        private boolean consumerActive;
        private boolean dirty;

        public DatastoreEngineHelper setEventType(String eventType)
        {
            this.eventType = eventType;

            return this;
        }

        public DatastoreEngineHelper setPersist(boolean persist)
        {
            this.persist = persist;

            return this;
        }

        public DatastoreEngineHelper setLogger(Logger logger)
        {
            this.logger = logger;

            return this;
        }

        @Override
        public void start(Promise<Void> promise)
        {
            try
            {
                final var eventProducerAddress = eventType + ".produce";

                var storeDir = new File(CURRENT_DIR + PATH_SEPARATOR + EventBusConstants.EVENT_DIR + PATH_SEPARATOR + EventBusConstants.replace(eventType));

                if (!storeDir.exists())
                {
                    storeDir.mkdirs();
                }

                eventStore = new Datastore(storeDir.getPath());

                vertx.eventBus().<JsonObject>localConsumer(eventType, message ->
                {
                    try
                    {
                        eventStore.put(message.body());

                        pendingEvents++;

                        dirty = true; // avoid unnecessary bookmarks flush

                        if (!consumerActive)  //if consume is not active then we have to awake it by sending 1 event
                        {
                            consumerActive = true;

                            vertx.eventBus().send(eventProducerAddress, 1);
                        }
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                }).exceptionHandler(logger::error);

                final var eventConsumerAddress = eventType + ".consume";

                vertx.eventBus().<Integer>localConsumer(eventProducerAddress, message ->  // emmit events either by activating consumer or consumer will ask if queued event counter goes 0
                {
                    try
                    {
                        if (pendingEvents > 0)
                        {
                            if (status)
                            {
                                byte[] bytes;

                                var size = message.body() > pendingEvents ? pendingEvents : message.body(); // if pending event is smaller than capacity, then adjust event emitter counter

                                for (var i = 0; i < size; i++)
                                {
                                    try
                                    {
                                        bytes = eventStore.get(); // raw bytes or uncompressed bytes as per consumer type...

                                        if (bytes != null)
                                        {
                                            queuedEvents.incrementAndGet();  // increment queued events counter for looping and monitoring purpose...

                                            vertx.eventBus().send(eventConsumerAddress, bytes); //notify consumer for the event

                                            pendingEvents--;
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        logger.error(exception);
                                    }
                                }

                                if (queuedEvents.get() == 0) //todo: confusion: needs to find out why bytes are null
                                {
                                    consumerActive = false; // no more events means consumer will become inactive
                                }

                                vertx.eventBus().send(eventType + ".inactive", 1);
                            }
                            else
                            {
                                consumerActive = false;
                            }
                        }
                        else
                        {
                            consumerActive = false; // no more events means consumer will become inactive
                        }
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                }).exceptionHandler(logger::error);

                //observer and config active this consumer for first time event
                vertx.eventBus().<String>localConsumer(eventType + ".active", message ->
                {
                    if (pendingEvents == 0)
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            logger.trace(String.format(" pending event is zero. so sending ack for %s ", message.body()));
                        }

                        vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_OBSERVER, new JsonObject()
                                .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(EVENT_COPY_REQUIRED, false)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, message.body())
                                .put(EVENT_TYPE, EVENT_HA_ACKNOWLEDGEMENT)
                                .put(HA_SYNC_TYPE, HAConstants.HASyncType.CONFIG.getName()));
                    }
                    else
                    {
                        status = true;

                        vertx.eventBus().send(eventProducerAddress, eventQueueSize);
                    }
                });

                vertx.eventBus().<Long>localConsumer(eventType + ".close", message ->
                {
                    try
                    {
                        eventStore.close();

                        logger.info(String.format("event store closed event-type : %s ", eventType));
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                });

                vertx.eventBus().<Long>localConsumer(eventType + ".load", message ->
                {
                    try
                    {
                        eventStore.load();

                        logger.info(String.format("store loaded again, pending event : %s for event type ; %s ", pendingEvents, eventType));

                        if (!consumerActive)
                        {
                            consumerActive = true;

                            vertx.eventBus().send(eventProducerAddress, 1);
                        }
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                });

                vertx.eventBus().<Long>localConsumer(EVENT_DATASTORE_RETENTION_TRIGGER + DOT_SEPARATOR + eventType, message -> eventStore.remove(message.body()));

                vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
                        vertx.eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS).put(EventBusConstants.ENGINE_TYPE, eventType).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                        .put(HealthUtil.HEALTH_STATS, new JsonObject().put(PENDING_EVENTS, pendingEvents)
                                                .put(FINISHED_EVENTS, finishedEvents.get())
                                                .put(QUEUED_EVENTS, queuedEvents.get())
                                                .put(ENGINE_CATEGORY, "default"))));

                if (persist)
                {
                    pendingEvents = eventStore.load();

                    logger.info(String.format("pending event found : %s for event type ; %s ", pendingEvents, eventType));

                    vertx.setPeriodic(30 * 1000, timer -> eventStore.save());

                    if (CommonUtil.debugEnabled())
                    {
                        logger.debug(String.format("event store %s loaded...", eventType));
                    }

                    // for the older event , we must activate consumer...
                    //if consume is not active then we have to awake it by sending 1 event
                    if (pendingEvents > 0 && !consumerActive)
                    {
                        consumerActive = true;

                        vertx.eventBus().send(eventProducerAddress, 1);
                    }
                }

                promise.complete();
            }

            catch (Exception exception)
            {
                logger.error(exception);

                logger.fatal(String.format("failed to start event engine helper %s...", eventType));

                promise.fail(exception);

            }
        }

        @Override
        public void stop(Promise<Void> promise) throws Exception
        {
            try
            {
                hasMoreEvent.set(false);

                if (persist)
                {
                    eventStore.save();
                }

                if (eventStore != null)
                {
                    eventStore.close();
                }

                promise.complete();
            }
            catch (Exception exception)
            {
                logger.error(exception);

                logger.fatal(String.format("failed to stop event engine helper %s...", eventType));

                promise.fail(exception);
            }
        }
    }
}
