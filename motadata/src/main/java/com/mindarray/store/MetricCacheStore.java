/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MetricCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(MetricCacheStore.class, GlobalConstants.MOTADATA_STORE, "Metric Cache Store");
    private static final MetricCacheStore STORE = new MetricCacheStore();
    private final Map<Long, Integer> metrics = new ConcurrentHashMap<>(); // only available this map when boot sequence is either default or master
    private final Map<Long, JsonObject> queuedMetrics = new ConcurrentHashMap<>();
    private final Map<Long, Long> unhealthyMetrics = new ConcurrentHashMap<>();
    private final Map<Long, JsonObject> deletedMetrics = new ConcurrentHashMap<>();
    private final Map<Long, Integer> latencies = new ConcurrentHashMap<>();
    private final Map<Long, JsonObject> pollingTimestamps = new ConcurrentHashMap<>(); // update metric poll times

    private MetricCacheStore()
    {
    }

    public static MetricCacheStore getStore()
    {
        return STORE;
    }

    public void addMetric(long metric, int interval)
    {
        this.metrics.put(metric, interval);
    }

    public void updateMetricInterval(int value, List<Long> metrics)
    {
        var iterator = this.metrics.entrySet().iterator();

        try
        {
            while (iterator.hasNext())
            {
                var metric = iterator.next();

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("metric %s has remaining seconds %s to be polled...", metric.getKey(), metric.getValue()));
                }

                var interval = metric.getValue() - value;

                if (interval <= 0)
                {
                    var context = MetricConfigStore.getStore().getItem(metric.getKey());

                    if (context != null && !context.isEmpty())
                    {
                        metric.setValue(context.getInteger(Metric.METRIC_POLLING_TIME));

                        metrics.add(metric.getKey());

                    }

                    else
                    {
                        //means metric group removed so... delete from cache too....

                        LOGGER.info(String.format("metric %s removed ...", metric.getKey()));

                        iterator.remove();

                    }
                }

                else
                {
                    metric.setValue(interval);
                }

            }

            if (!metrics.isEmpty() && CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("qualified metrics to be polled: %s", metrics));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    public long queue(JsonObject request)
    {
        var eventId = CommonUtil.getLong(GlobalConstants.NOT_AVAILABLE);

        var id = request.getLong(GlobalConstants.ID);

        if (!queuedMetrics.containsKey(id))
        {
            queuedMetrics.put(id, request);

        }

        else
        {
            eventId = queuedMetrics.get(id).getLong(EventBusConstants.EVENT_ID);
        }

        return eventId;
    }

    public boolean queuedMetric(long id)
    {
        return queuedMetrics.containsKey(id);
    }

    public boolean existMetric(long id)
    {
        return metrics.containsKey(id);
    }

    public JsonObject dequeue(long id)
    {
        return queuedMetrics.remove(id);
    }

    public JsonObject getDeletedMetric(long id)
    {
        return deletedMetrics.remove(id);
    }

    public int getQueuedMetrics()
    {
        return queuedMetrics.size();
    }

    public void deleteMetric(long metricId)
    {
        if (queuedMetrics.containsKey(metricId))
        {
            deletedMetrics.put(metricId, queuedMetrics.remove(metricId));

        }

        latencies.remove(metricId);

        unhealthyMetrics.remove(metricId);

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
        {
            metrics.remove(metricId);
        }

    }

    public void updateMetricPollTimestamp(long id, String metric, long timestamp)
    {
        pollingTimestamps.computeIfAbsent(id, value -> new JsonObject()).put(metric, timestamp).put(EventBusConstants.EVENT_TIMESTAMP, timestamp);
    }

    public JsonObject getMetricPollTimestamps(long id)
    {
        return pollingTimestamps.containsKey(id) ? pollingTimestamps.get(id).copy() : new JsonObject();
    }
}
