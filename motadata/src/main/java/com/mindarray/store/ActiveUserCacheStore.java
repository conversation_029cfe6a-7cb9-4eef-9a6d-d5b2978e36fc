/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.api.APIConstants;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ActiveUserCacheStore extends AbstractCacheStore
{
    private static final ActiveUserCacheStore STORE = new ActiveUserCacheStore();
    private final Map<String, JsonObject> items = new ConcurrentHashMap<>();

    public static ActiveUserCacheStore getStore()
    {
        return STORE;
    }

    public Map<String, JsonObject> getItems()
    {
        return new HashMap<>(items);
    }

    public void updateItem(String sessionId, JsonObject context)
    {
        items.put(sessionId, context);
    }

    public void updateTimestamp(String sessionId, long timestamp)
    {
        if (items.containsKey(sessionId) && items.get(sessionId) != null)
        {
            items.put(sessionId, items.get(sessionId).put(APIConstants.LAST_ACTIVE_TIMESTAMP, timestamp));
        }
    }

    public void deleteItem(String sessionId)
    {
        items.remove(sessionId);
    }

    public JsonObject getItem(String sessionId)
    {
        return items.getOrDefault(sessionId, new JsonObject());
    }
}

