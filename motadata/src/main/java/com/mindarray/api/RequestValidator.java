/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.AbstractConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;


public class RequestValidator
{
    private static final Logger LOGGER = new Logger(RequestValidator.class, GlobalConstants.MOTADATA_API, "Request Validator");

    private RequestValidator()
    {

    }

    /* This method validates parameters passed for creating and updating parameters as per the rule defined in the schema file. */

    public static boolean validateRequestBody(RoutingContext routingContext)
    {
        var result = false;

        try
        {
            if (routingContext.body().asJsonObject().isEmpty())
            {
                result = true;

                sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_REQUEST_BODY_MISSING));

            }
        }

        catch (Exception exception)
        {
            result = true;

            APIUtil.sendResponse(exception, routingContext);
        }

        return !result;
    }

    public static boolean validateRequest(String endpoint, String requestType, RoutingContext routingContext, JsonObject requestParameters, boolean setBody, AbstractConfigStore configStore)
    {
        var result = false;

        try
        {

            APIUtil.removeDefaultParameters(requestParameters);

            /*retrieve entity schema as per the endpoint. If entity schema is not there than its bad request.*/

            var entitySchema = CommonUtil.getEntitySchema(endpoint);

            if (entitySchema != null && !entitySchema.isEmpty())
            {
                /* retrieve property from the schema to validate each request parameter.*/

                var properties = entitySchema.getJsonArray(APIConstants.ENTITY_PROPERTY);

                if (properties != null && !properties.isEmpty())
                {
                    removeProperties(requestType, properties, requestParameters);

                    APIUtil.removeSpace(requestParameters, routingContext, setBody);

                    result = validateRequestParams(routingContext, properties, requestParameters, entitySchema, requestType, configStore);
                }
            }

            else
            {
                result = true;

                sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, UNKNOWN)));

            }

        }

        catch (Exception exception)
        {
            result = true;

            APIUtil.sendResponse(exception, routingContext);
        }

        return result;
    }

    /*This method will send response to client and end the request.*/

    public static void sendResponse(RoutingContext routingContext, JsonObject response)
    {
        routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(response.getInteger(RESPONSE_CODE)).end(response.encodePrettily());
    }

    private static boolean validateRequestParams(RoutingContext routingContext, JsonArray properties, JsonObject requestParameters, JsonObject entitySchema, String requestType, AbstractConfigStore configStore)
    {
        var result = false;

        var uniqueRuleContext = new JsonObject();

        var schemaFields = new JsonArray();

        var responseContext = new JsonObject();

        try
        {
            /*Go through every property and apply rule defined in the schema file.
             *
             * If any rule validation is failed then send bad request response to client and do not check remaining rule
             * for same property and for the remaining property.
             *
             * For unique rule and exist rule we need asynchronous database service call, so it will be checked at last.
             * We will build context for that rule. */


            for (var index = 0; index < properties.size(); index++)
            {
                if (responseContext.isEmpty())
                {
                    var parameters = properties.getJsonObject(index);

                    /*build schema field name context to use it later.*/

                    schemaFields.add(parameters.getString(ENTITY_PROPERTY_NAME));

                    var prerequisites = parameters.getJsonArray(ENTITY_PROPERTY_PREREQUISITES);

                    /*if prerequisite is satisfied than check rules otherwise send bad request response.*/

                    var prerequisiteFulfilled = true;

                    if (prerequisites != null && !prerequisites.isEmpty())
                    {
                        prerequisiteFulfilled = APIUtil.testPrerequisites(prerequisites, requestParameters, parameters.getString(ENTITY_PROPERTY_TYPE));
                    }

                    if (!prerequisiteFulfilled)
                    {
                        if (requestParameters.getValue(parameters.getString(ENTITY_PROPERTY_NAME)) != null)
                        {
                            responseContext.getMap().putAll(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_NOT_REQUIRED, parameters.getString(ENTITY_PROPERTY_TITLE))).getMap());
                        }
                    }
                    else
                    {

                        var response = validatePropertyType(parameters, requestParameters);

                        if (response != null)
                        {
                            responseContext.getMap().putAll(response.getMap());
                        }


                        if (responseContext.isEmpty())
                        {
                            response = validatePropertyRules(requestType, parameters, requestParameters, uniqueRuleContext, entitySchema);

                            if (!response.isEmpty())
                            {
                                responseContext.getMap().putAll(response.getMap());
                            }
                        }

                        // This will check that value is same as values context for static dropdown

                        if (responseContext.isEmpty() && parameters.getValue(ENTITY_PROPERTY_VALUES) != null && requestParameters.containsKey(parameters.getString(ENTITY_PROPERTY_NAME)))
                        {
                            response = APIUtil.testValueExist(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getString(ENTITY_PROPERTY_TYPE), requestParameters, (JsonArray) parameters.getValue(ENTITY_PROPERTY_VALUES));

                            if (response != null)
                            {
                                responseContext.getMap().putAll(response.getMap());
                            }
                        }
                    }
                }
            }

            if (responseContext.isEmpty() && !uniqueRuleContext.isEmpty())
            {
                /* Waiting for the unique rule task to complete.
                 *
                 * We have async call here so in callback we have to send response back either move to next handler or send bad request response.
                 *
                 * so we will set result to true and let async call to decide either move to next handler or send bad request response.*/

                result = true;

                APIUtil.testUniqueRule(routingContext, requestType, uniqueRuleContext.getString(DBConstants.FIELD_NAME), uniqueRuleContext.getString(ENTITY_PROPERTY_TITLE), requestParameters, configStore)
                        .onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                /*On succeed handler will return json object. If there is not empty json object means validation is failed.
                                 *
                                 * So need to send response to client. if result is empty than validation is success and we need to move to next handler.
                                 *
                                 * */

                                var ruleResult = asyncResult.result();

                                if (ruleResult != null && !ruleResult.isEmpty())
                                {
                                    sendResponse(routingContext, ruleResult);
                                }
                                else
                                {
                                    routingContext.next();
                                }
                            }
                            else
                            {
                                /* if future fail mean there is database service related error, validation could not be done, so, sending failure response.*/

                                sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, UNKNOWN)));
                            }
                        });
            }

            if (!result && !responseContext.isEmpty())
            {
                result = true;

                sendResponse(routingContext, responseContext);
            }
        }
        catch (Exception exception)
        {
            result = true;

            APIUtil.sendResponse(exception, routingContext);
        }

        return result;
    }

    private static JsonObject validatePropertyType(JsonObject parameters, JsonObject requestParameters)
    {
        return switch (parameters.getString(ENTITY_PROPERTY_TYPE))
        {
            case FIELD_TYPE_NUMERIC ->
                    APIUtil.testNumericRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), requestParameters);

            case FIELD_TYPE_LIST ->
                    APIUtil.testListRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), requestParameters);

            case FIELD_TYPE_MAP ->
                    APIUtil.testMapRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), requestParameters);

            /*There is no default case to handle*/
            default -> null;
        };
    }

    private static JsonObject validatePropertyRules(String requestType, JsonObject parameters, JsonObject requestParameters, JsonObject uniqueRuleContext, JsonObject entitySchema)
    {
        var validation = new JsonObject();

        try
        {
            var rules = parameters.getJsonArray(ENTITY_PROPERTY_RULES);

            if (rules != null)
            {
                for (var index = 0; index < rules.size(); index++)
                {
                    var rule = rules.getString(index);

                    if (validation.isEmpty())
                    {
                        JsonObject response = null;

                        switch (CommonUtil.getString(rule))
                        {
                            case RULE_REQUIRED ->
                            {
                                /* For update request we will apply required rule for the secure field.*/

                                if (!(requestType.equalsIgnoreCase(REQUEST_UPDATE) && parameters.getString(ENTITY_PROPERTY_TYPE).equalsIgnoreCase(FIELD_TYPE_SECURED)))
                                {
                                    response = APIUtil.testRequiredRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getString(ENTITY_PROPERTY_TYPE), requestParameters);
                                }
                            }

                            case RULE_MAXIMUM ->
                                    response = APIUtil.testMaximumRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getLong(VALUE), requestParameters);

                            case RULE_RANGE ->
                                    response = APIUtil.testRangeRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getJsonArray(VALUE), requestParameters);

                            case RULE_RANGE_DIVISIBLE ->
                                    response = APIUtil.testRangeDivideRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getJsonArray(VALUE), requestParameters, parameters.getInteger(ENTITY_PROPERTY_DIVIDE_VALUE));

                            case RULE_MINIMUM ->
                                    response = APIUtil.testMinimumRule(parameters.getString(ENTITY_PROPERTY_NAME), parameters.getString(ENTITY_PROPERTY_TITLE), parameters.getLong(VALUE), requestParameters);

                            case RULE_UNIQUE -> uniqueRuleContext
                                    .put(DBConstants.FIELD_NAME, parameters.getString(ENTITY_PROPERTY_NAME))
                                    .put(ENTITY_TABLE, entitySchema.getString(ENTITY_TABLE))
                                    .put(ENTITY_PROPERTY_TITLE, parameters.getString(ENTITY_PROPERTY_TITLE));

                            default ->
                            {
                                // do nothing
                            }
                        }

                        if (response != null)
                        {
                            validation.getMap().putAll(response.getMap());
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            validation.mergeIn(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }

        return validation;
    }

    private static void removeProperties(String requestType, JsonArray properties, JsonObject requestParameters)
    {
        if (REQUEST_UPDATE.equalsIgnoreCase(requestType))
        {
            var iterator = properties.iterator();

            while (iterator.hasNext())
            {
                var property = JsonObject.mapFrom(iterator.next());

                if (property != null && !property.isEmpty() && !requestParameters.containsKey(property.getString(ENTITY_PROPERTY_NAME)))
                {
                    iterator.remove();
                }
            }
        }
    }

}
