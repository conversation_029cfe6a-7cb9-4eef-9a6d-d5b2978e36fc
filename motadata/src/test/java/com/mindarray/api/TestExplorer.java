/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.aiops.DependencyQueryProcessor;
import com.mindarray.store.ExplorerConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestExplorer
{
    private static final String ENTITY_NAME = "Explorer";

    private static final Logger LOGGER = new Logger(TestExplorer.class, MOTADATA_API, "Test Explorer");

    private static final Map<String, Long> views = new HashMap<>();

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testCreateMetricExplorer1(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.name\":\"System Metrics Explorer 1\",\"explorer.type\":\"metric\",\"explorer.description\":\"System Metrics Explorer 1\",\"explorer.access.type\":\"public\",\"explorer.users\":[],\"explorer.global.view.enabled\":\"no\",\"explorer.object.type\":\"server\",\"explorer.object.id\":102,\"explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testCreateMetricExplorer2(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.name\":\"System Metrics Explorer 2\",\"explorer.type\":\"metric\",\"explorer.description\":\"System Metrics Explorer 2\",\"explorer.access.type\":\"public\",\"explorer.users\":[],\"explorer.global.view.enabled\":\"yes\",\"explorer.object.type\":\"server\",\"explorer.object.id\":-1,\"explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testCreateMetricExplorer3(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.name\":\"System Metrics Explorer 3\",\"explorer.type\":\"metric\",\"explorer.description\":\"System Metrics Explorer 3\",\"explorer.access.type\":\"public\",\"explorer.users\":[],\"explorer.global.view.enabled\":\"no\",\"explorer.object.type\":\"server\",\"explorer.object.id\":-1,\"explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testCreateMetricExplorer4(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.name\":\"System Metrics Explorer 4\",\"explorer.type\":\"metric\",\"explorer.description\":\"System Metrics Explorer 4\",\"explorer.access.type\":\"public\",\"explorer.users\":[],\"explorer.global.view.enabled\":\"no\",\"explorer.object.type\":\"network\",\"explorer.object.id\":103,\"explorer.context\":{}}");

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testGetMetricExplorerGlobal(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT + "?filter=" + new JsonObject("{\"explorer.type\":\"metric\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals("System Metrics Explorer 2", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getString(Explorer.EXPLORER_NAME));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testGetMetricExplorerByObject(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT + "?filter=" + new JsonObject("{\"explorer.object.id\":102,\"explorer.object.type\":\"server\",\"explorer.type\":\"metric\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals(2, response.bodyAsJsonObject().getJsonArray(RESULT).size());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void testGetMetricExplorerByType(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT + "?filter=" + new JsonObject("{\"explorer.object.id\":103,\"explorer.object.type\":\"network\",\"explorer.type\":\"metric\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertEquals(1, response.bodyAsJsonObject().getJsonArray(RESULT).size());

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    public void testCreateTopologyExplorerPublicView(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var source = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertNotNull(source);

        var context = new JsonObject("{\"explorer.type\":\"topology\",\"explorer.name\":\"Topology Explorer\",\"explorer.access.type\":\"public\",\"explorer.global.view.enabled\":\"yes\",\"explorer.object.id\":-1,\"explorer.context\":{\"topology.filter.target.type\":\"exclude-ip.address\",\"topology.filter.targets\":[\"***********\"]},\"explorer.group.name\":\"Switches:Exclude\",\"entity.id\":78169847546}");

        context.put(APIConstants.ENTITY_ID, source);

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

            views.put("exclude-ip-address", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    public void testCreateTopologyExplorerPrivateView(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.type\":\"topology\",\"explorer.name\":\"Topology Explorer private\",\"explorer.access.type\":\"public\",\"explorer.global.view.enabled\":\"yes\",\"explorer.object.id\":-1,\"explorer.context\":{\"topology.filter.target.type\":\"include-group\",\"topology.filter.targets\":[10000000000002]},\"explorer.group.name\":\"Switches:Exclude\",\"entity.id\":78169847546}");

        context.put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********"));

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

            views.put("include-group", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    public void testCreateTopologyExplorerView3(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.type\":\"topology\",\"explorer.name\":\"exclude-range\",\"explorer.access.type\":\"public\",\"explorer.global.view.enabled\":\"yes\",\"explorer.object.id\":-1,\"explorer.context\":{\"topology.filter.target.type\":\"exclude-ip.address.range\",\"topology.filter.targets\":[\"**********-4\"]},\"explorer.group.name\":\"Switches:Exclude\",\"entity.id\":78169847546}");

        context.put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********"));

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

            views.put("exclude-ip-ranges", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    public void testCreateTopologyExplorerView4(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"explorer.type\":\"topology\",\"explorer.name\":\"exclude-tag\",\"explorer.access.type\":\"public\",\"explorer.global.view.enabled\":\"yes\",\"explorer.object.id\":-1,\"explorer.context\":{\"topology.filter.target.type\":\"exclude-tag\",\"topology.filter.targets\":[\"topology\"]},\"explorer.group.name\":\"Switches:Exclude\",\"entity.id\":78169847546}");

        context.put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********"));

        TestAPIUtil.post(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            TestAPIUtil.assertCreateEntityTestResult(ExplorerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

            Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

            views.put("exclude-tag", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    public void testGetAllTopologyExplorerViews(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.get(TestAPIConstants.VISUALIZATION_EXPLORER_API_ENDPOINT +  "?filter=" + new JsonObject("{\"explorer.type\":\"topology\"}").encode(), testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

            Assertions.assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty());

            Assertions.assertTrue(response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).containsKey("Switches"));

            var group = response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getJsonObject("Switches");

            Assertions.assertTrue(group.containsKey(Explorer.EXPLORER_GROUPS) && group.containsKey(Explorer.EXPLORER_VIEWS));

            Assertions.assertTrue(group.getJsonObject(Explorer.EXPLORER_GROUPS).containsKey("Exclude"));

            Assertions.assertTrue(group.getJsonObject(Explorer.EXPLORER_GROUPS).getJsonObject("Exclude").containsKey("Topology Explorer") && group.getJsonObject(Explorer.EXPLORER_GROUPS).getJsonObject("Exclude").containsKey("Topology Explorer private"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAssertTopologyViewFilterExcludeIPAddresses(VertxTestContext testContext) throws Exception
    {
        var method = DependencyQueryProcessor.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new DependencyQueryProcessor(), new JsonObject().put(Explorer.ID, views.get("exclude-ip-address")).put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********")));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertEquals(1, values.size());

        Assertions.assertTrue(values.contains("***********"));

        values = (Set<String>) method.invoke(new DependencyQueryProcessor(), new JsonObject().put(Explorer.ID, views.get("exclude-ip-ranges")).put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********")));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertEquals(2, values.size());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testAssertTopologyViewFilterIncludeGroups(VertxTestContext testContext) throws Exception
    {
        var method = DependencyQueryProcessor.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new DependencyQueryProcessor(), new JsonObject().put(Explorer.ID, views.get("include-group")).put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********")));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("**********") && values.contains("************"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testAssertTopologyViewFilterExcludeTag(VertxTestContext testContext) throws Exception
    {
        var method = DependencyQueryProcessor.class.getDeclaredMethod("setFilterTargets", JsonObject.class);

        method.setAccessible(true);

        var values = (Set<String>) method.invoke(new DependencyQueryProcessor(), new JsonObject().put(Explorer.ID, views.get("exclude-tag")).put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********")));

        Assertions.assertNotNull(values);

        Assertions.assertFalse(values.isEmpty());

        Assertions.assertTrue(values.contains("************"));

        testContext.completeNow();
    }
}