/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
*   30-Jun-2025     Ya<PERSON>         MOTADATA-6528 : send change notification to DQP for CRUD operations
*/

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DependencyMapperConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class DependencyMapper extends AbstractAPI
{
    public static final String DEPENDENCY_MAPPER_PARENT = "dependency.mapper.parent";
    public static final String DEPENDENCY_MAPPER_PARENT_INTERFACE = "dependency.mapper.parent.interface"; // dependency.mapper.context
    public static final String DEPENDENCY_MAPPER_PARENT_INTERFACE_NAME = "dependency.mapper.parent.interface.name"; // dependency.mapper.context
    public static final String DEPENDENCY_MAPPER_CHILD = "dependency.mapper.child";
    public static final String DEPENDENCY_MAPPER_CHILD_INTERFACE = "dependency.mapper.child.interface"; // dependency.mapper.context
    public static final String DEPENDENCY_MAPPER_CHILD_INTERFACE_NAME = "dependency.mapper.child.interface.name"; // dependency.mapper.context
    public static final String DEPENDENCY_MAPPER_ARCHIVED = "dependency.mapper.archived";
    public static final String DEPENDENCY_MAPPER_LINK_LAYER = "dependency.mapper.link.layer"; // dependency.mapper.context
    public static final String DEPENDENCY_MAPPER_CONTEXT = "dependency.mapper.context";
    public static final String DEPENDENCY_MAPPER_TYPE = "dependency.mapper.type";
    public static final String PARENTS = "parents";
    public static final String CHILDREN = "children";
    public static final String EXISTING_CHILDREN = "existing.children";
    private static final Logger LOGGER = new Logger(DependencyMapper.class, MOTADATA_API, "Dependency Mapper API");

    public DependencyMapper()
    {
        super("dependency-mappers", DependencyMapperConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.post("/" + endpoint + "/instances").handler(this::getInstances);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var items = DependencyMapperConfigStore.getStore().getItems();

            if (!items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var context = item.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);

                    if (context != null && CommonUtil.isNotNullOrEmpty(context.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE)))
                    {
                        var id = ObjectConfigStore.getStore().getItemByIP(item.getString(DEPENDENCY_MAPPER_PARENT));

                        var metric = id != null ? MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())) : null;

                        if (metric != null)
                        {
                            var interfaceName = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), NMSConstants.INTERFACE_INDEX, NMSConstants.INTERFACE_NAME).getString(context.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE));

                            if (CommonUtil.isNotNullOrEmpty(interfaceName))
                            {
                                context.put(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE_NAME, interfaceName);
                            }
                        }

                        if (CommonUtil.isNotNullOrEmpty(context.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)) && CommonUtil.isNotNullOrEmpty(item.getString(DEPENDENCY_MAPPER_CHILD)))
                        {
                            id = ObjectConfigStore.getStore().getItemByIP(item.getString(DEPENDENCY_MAPPER_CHILD));

                            metric = id != null ? MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())) : null;

                            if (metric != null)
                            {
                                var interfaceName = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), NMSConstants.INTERFACE_INDEX, NMSConstants.INTERFACE_NAME).getString(context.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE));

                                if (CommonUtil.isNotNullOrEmpty(interfaceName))
                                {
                                    context.put(DependencyMapper.DEPENDENCY_MAPPER_CHILD_INTERFACE_NAME, interfaceName);
                                }
                            }
                        }
                    }
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getInstances(RoutingContext routingContext)
    {
        try
        {
            var id = ObjectConfigStore.getStore().getItemByIP(routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_IP));

            if (id != null && routingContext.body().asJsonObject().getString(DEPENDENCY_MAPPER_TYPE).equalsIgnoreCase(DependencyMapperType.NETWORK.getName()))
            {
                Bootstrap.configDBService().getById(DBConstants.TBL_METRIC, MetricConfigStore.getStore().getItemsByObject(id).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).map(item -> item.getLong(ID)).findFirst().orElse(0L),
                        result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.succeeded() ? result.result() : new JsonObject())));
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_CREATE);
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            Bootstrap.configDBService().delete(DBConstants.TBL_DEPENDENCY_MAPPER,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                    DEFAULT_USER, routingContext.request().remoteAddress().host(),
                    result ->
                    {
                        if (result.succeeded() && !result.result().isEmpty())
                        {
                            LOGGER.info(String.format("parent %s interface %s is deleted", DependencyMapperConfigStore.getStore().getItem(id).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT), DependencyMapperConfigStore.getStore().getItem(id).getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT).getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT_INTERFACE)));

                            var existingItem = DependencyMapperConfigStore.getStore().getItem(id);

                            DependencyMapperConfigStore.getStore().deleteItem(id);

                            var level = AIOpsConstants.getDependencyLevel(existingItem, true);

                            removeOldDependencies(new JsonObject(), AIOpsConstants.getDependencyLevel(existingItem, true), existingItem, existingItem);

                            // remove parent - child link from main entry (ex.from ip)
                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, existingItem.getString(DEPENDENCY_MAPPER_PARENT))
                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, existingItem.getString(DEPENDENCY_MAPPER_CHILD))
                                            .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                            .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName()));

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, existingItem.getString(DEPENDENCY_MAPPER_CHILD))
                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, existingItem.getString(DEPENDENCY_MAPPER_PARENT))
                                            .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                            .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName()));

                            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_DEPENDENCY_MAPPER.name()).put(ID, id));

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                    .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.DEPENDENCY_MAPPER.getName())).put(ID, id));
                        }

                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL)
                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, APIConstants.Entity.DEPENDENCY_MAPPER.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN));

                        }
                    });
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void update(RoutingContext routingContext)
    {
        try
        {
            qualify(routingContext, APIConstants.REQUEST_UPDATE).onComplete(result ->
            {
                if (result.succeeded())
                {
                    var item = result.result();

                    var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                    Bootstrap.configDBService().update(DBConstants.TBL_DEPENDENCY_MAPPER,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                            item,
                            routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                            asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    var existingItem = DependencyMapperConfigStore.getStore().getItem(id);

                                    DependencyMapperConfigStore.getStore().updateItem(id).onComplete(response ->
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.DEPENDENCY_MAPPER.getName())));

                                        var level = AIOpsConstants.getDependencyLevel(item, true);

                                        var context = new JsonObject();

                                        if (existingItem.containsKey(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT))
                                        {
                                            existingItem.mergeIn(existingItem.getJsonObject(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT));

                                            existingItem.remove(DependencyMapper.DEPENDENCY_MAPPER_CONTEXT);
                                        }

                                        removeOldDependencies(context, level, existingItem, item);

                                        // now if updated parameters archived flag is no then and only save new dependency
                                        if (item.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO))
                                        {
                                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                                    context.clear().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(DEPENDENCY_MAPPER_PARENT))
                                                            .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                                            .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName())
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, item.getString(DEPENDENCY_MAPPER_CHILD))
                                                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, item.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                            .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, item.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                                            .put(AIOpsConstants.DEPENDENCY_PARENT, item.getString(DEPENDENCY_MAPPER_PARENT))
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level));

                                            if (CommonUtil.isNotNullOrEmpty(item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)))
                                            {
                                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                                                        context.clear().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(DEPENDENCY_MAPPER_CHILD))
                                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, item.getString(DEPENDENCY_MAPPER_PARENT))
                                                                .put(AIOpsConstants.DEPENDENCY_PARENT, item.getString(DEPENDENCY_MAPPER_PARENT))
                                                                .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION_PORT, item.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                                                .put(AIOpsConstants.DEPENDENCY_SOURCE_PORT, item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                                .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                                                                .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.ADD.getName()));
                                            }

                                        }

                                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION,
                                                new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_DEPENDENCY_MAPPER.name()).put(ID, id));

                                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_DEPENDENCY_MAPPER.name()).put(ID, id));
                                    });
                                }

                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, APIConstants.Entity.DEPENDENCY_MAPPER.getName(), asyncResult.cause())));

                                }
                            });
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private Future<JsonObject> qualify(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var message = EMPTY_VALUE;

            //as of now we have only network dependency mapper
            if (requestBody.getString(DEPENDENCY_MAPPER_TYPE).equalsIgnoreCase(DependencyMapperType.NETWORK.getName()))
            {
                var id = DUMMY_ID;

                if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE))
                {
                    requestBody.put(DEPENDENCY_MAPPER_ARCHIVED, NO);
                }
                else
                {
                    id = CommonUtil.getLong(routingContext.request().getParam(ID));

                    requestBody.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER);
                }

                var items = DependencyMapperConfigStore.getStore().getItemsByObject(requestBody.getString(DEPENDENCY_MAPPER_PARENT));

                var context = new JsonObject().mergeIn(requestBody);

                var level = AIOpsConstants.getDependencyLevel(context, true);

                if (!(requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE) && context.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(YES) && DependencyMapperConfigStore.getStore().getItem(id).getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO)))
                {
                    for (var item : items.get(PARENTS))
                    {
                        // 1. if not archived
                        // 2. if same level dependency
                        // 3. provisioned child is same with requested child (here parent is already same)
                        // in all that case we don't create/update dependency mapper as same is already exist
                        if (item.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO) && AIOpsConstants.getDependencyLevel(item, true).equals(level)
                                && item.getString(DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_PARENT))
                                && item.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                && (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || !item.getLong(ID).equals(id)))
                        {
                            message = String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED_DUPLICATE_ERROR);

                            break;
                        }
                    }

                    if (message.equalsIgnoreCase(EMPTY_VALUE))
                    {
                        for (var item : items.get(CHILDREN))
                        {
                            // 1. if not archived
                            // 2. if same level dependency
                            // 3. if requested child and provisioned parent is same
                            // 4. if provisioned dependencies doesn't having child interface or that child interface is same is requested dependencies parent interface
                            // in that case it's cyclic dependency
                            if (item.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO) && AIOpsConstants.getDependencyLevel(item, true).equals(level)
                                    && context.getString(DEPENDENCY_MAPPER_CHILD).equalsIgnoreCase(item.getString(DEPENDENCY_MAPPER_PARENT))
                                    && (CommonUtil.isNullOrEmpty(item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)) || item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE)))
                                    && (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || !item.getLong(ID).equals(id)))
                            {
                                message = String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED_CYCLIC_ERROR);

                                break;
                            }
                        }
                    }

                    if (message.equalsIgnoreCase(EMPTY_VALUE))
                    {
                        items = DependencyMapperConfigStore.getStore().getItemsByObject(context.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD));

                        for (var item : items.get(CHILDREN))
                        {
                            // 1. if not archived
                            // 2. if same level dependency
                            // 3. if provisioned dependencies doesn't having child interface or requested dependencies doesn't having child interface or provisioned dependencies and requested dependencies child interface is same
                            // in all that case we don't create/update dependency mapper as same is already exist
                            if (item.getJsonObject(DEPENDENCY_MAPPER_CONTEXT).getString(DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE)) && item.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO)
                                    && AIOpsConstants.getDependencyLevel(item, true).equals(level)
                                    && (CommonUtil.isNullOrEmpty(item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)) || item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)))
                                    && (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || !item.getLong(ID).equals(id)))
                            {
                                message = String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED_DUPLICATE_ERROR);

                                break;
                            }
                        }
                    }

                    // #24868

                    if (message.equalsIgnoreCase(EMPTY_VALUE))
                    {

                        for (var item : items.get(PARENTS))
                        {

                            if (item.getString(DEPENDENCY_MAPPER_ARCHIVED).equalsIgnoreCase(NO) && AIOpsConstants.getDependencyLevel(item, true).equals(level)
                                    && item.getString(DEPENDENCY_MAPPER_PARENT).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_CHILD))
                                    && item.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE).equalsIgnoreCase(context.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                    && (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || !item.getLong(ID).equals(id)))
                            {

                                message = String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED_DUPLICATE_ERROR);

                                break;
                            }
                        }
                    }
                }
            }

            if (message.equalsIgnoreCase(EMPTY_VALUE))
            {
                promise.complete(requestBody);
            }

            else
            {
                promise.fail(message);

                this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, message));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject response, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION,
                new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_DEPENDENCY_MAPPER.name()).put(ID, response.getLong(ID)));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_DEPENDENCY_MAPPER.name()).put(ID, response.getLong(ID)));

        return super.afterCreate(response, routingContext);
    }

    @Override
    protected Future<Void> afterDeleteAll(JsonObject response, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_DEPENDENCY_MAPPER.name()).put(ID, response.getLong(ID)));

        return super.afterDeleteAll(response, routingContext);
    }

    private void removeOldDependencies(JsonObject context, byte level, JsonObject existingItem, JsonObject item)
    {
        // first remove old dependencies
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                context.clear().put(AIOpsConstants.DEPENDENCY_SOURCE, existingItem.getString(DEPENDENCY_MAPPER_PARENT) + VALUE_SEPARATOR + existingItem.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                        .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                        .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, CommonUtil.isNotNullOrEmpty(existingItem.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                                ? existingItem.getString(DEPENDENCY_MAPPER_CHILD) + VALUE_SEPARATOR + existingItem.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE) : existingItem.getString(DEPENDENCY_MAPPER_CHILD))
                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName()));

        // remove old dependency parent
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                context.clear().put(AIOpsConstants.DEPENDENCY_SOURCE, existingItem.getString(DEPENDENCY_MAPPER_PARENT) + VALUE_SEPARATOR + existingItem.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                        .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.MINUS_ONE.getName())
                        .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, existingItem.getString(DEPENDENCY_MAPPER_PARENT))
                        .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                        .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName()));

        // remove old end device dependency with parent
        if (CommonUtil.isNotNullOrEmpty(item.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE)))
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                    context.clear().put(AIOpsConstants.DEPENDENCY_SOURCE, existingItem.getString(DEPENDENCY_MAPPER_CHILD) + VALUE_SEPARATOR + existingItem.getString(DEPENDENCY_MAPPER_CHILD_INTERFACE))
                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, CommonUtil.isNotNullOrEmpty(existingItem.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE))
                                    ? existingItem.getString(DEPENDENCY_MAPPER_PARENT) + VALUE_SEPARATOR + existingItem.getString(DEPENDENCY_MAPPER_PARENT_INTERFACE) : existingItem.getString(DEPENDENCY_MAPPER_PARENT))
                            .put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName())
                            .put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE_MULTIPLES.getName()));

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                    context
                            .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, existingItem.getString(DEPENDENCY_MAPPER_PARENT))
                            .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.MINUS_ONE.getName()));
        }
    }

    public enum DependencyMapperType
    {
        NETWORK("Network"),

        APPLICATION("Application");

        private static final Map<String, DependencyMapperType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyMapperType::getName, e -> e)));
        private final String name;

        DependencyMapperType(String name)
        {
            this.name = name;
        }

        public static DependencyMapperType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    // for application dependency mapper we will introduce link layer later
    public enum DependencyMapperLinkLayer
    {
        L2("L2"),

        L3("L3");

        private static final Map<String, DependencyMapperLinkLayer> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DependencyMapperLinkLayer::getName, e -> e)));
        private final String name;

        DependencyMapperLinkLayer(String name)
        {
            this.name = name;
        }

        public static DependencyMapperLinkLayer valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
