/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.policy;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.api.Tag;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.ACCESS_TOKEN_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.METRIC_POLICY_API_ENDPOINT;
import static com.mindarray.aiops.AIOpsConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PASSWORD;
import static com.mindarray.db.DBConstants.FIELD_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_TIMESTAMP;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(120 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestMetricPolicy
{
    private static final JsonObject IDS = new JsonObject();
    private static final MetricPolicyInspector METRIC_POLICY_INSPECTOR = new MetricPolicyInspector();
    private static final Logger LOGGER = new Logger(TestMetricPolicy.class, MOTADATA_POLICY, "Test Metric Policy");
    private static final AtomicBoolean UNSUPPRESSED = new AtomicBoolean(false);
    private static final AtomicBoolean SOUND_NOTIFICATION_TRIGGERED = new AtomicBoolean(false);
    private static final JsonArray notifications = new JsonArray();
    private static MessageConsumer<JsonObject> messageConsumer;
    private static MessageConsumer<JsonObject> consumer;
    private static MessageConsumer<JsonObject> messageReceiver;
    private static JsonObject object = new JsonObject();
    private static JsonObject networkObject = new JsonObject();
    private static JsonObject items = null;
    private static Long userId = DUMMY_ID;
    private static String sessionId = EMPTY_VALUE;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var deployedVerticles = new AtomicInteger();

        var promise = Promise.<Void>promise();

        for (var index = 0; index < MotadataConfigUtil.getMetricPolicyWorkers(); index++)
        {
            Bootstrap.undeployVerticle(EventBusConstants.EVENT_METRIC_POLICY + "." + index).onComplete(undeploy ->
            {
                deployedVerticles.getAndIncrement();

                if (deployedVerticles.get() == MotadataConfigUtil.getVisualizationManagerInstances())
                {
                    promise.complete();
                }
            });
        }

        promise.future().onComplete(asyncResult ->
                Bootstrap.vertx().deployVerticle(METRIC_POLICY_INSPECTOR, new DeploymentOptions().setConfig(new JsonObject().put(EventBusConstants.EVENT_TYPE, "metric.policy.test")), handler ->
                {
                    object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132");

                    networkObject = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

                    MotadataConfigUtil.loadConfigs(new JsonObject().put("policy.timer", 5));

                    var items = MetricPolicyConfigStore.getStore().getItems();

                    var retries = new AtomicInteger();

                    for (var i = 0; i < items.size(); i++)
                    {
                        TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + items.getJsonObject(i).getLong(ID), testContext.succeeding(response ->
                                testContext.verify(() ->
                                {
                                    retries.getAndIncrement();

                                    if (retries.get() == items.size())
                                    {
                                        consumer = TestUtil.vertx().eventBus().localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                                        {
                                            if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UNSUPPRESS_POLICY && message.body().getString(POLICY_TYPE).equalsIgnoreCase("Metric Threshold"))
                                            {
                                                consumer.unregister(result -> UNSUPPRESSED.set(true));
                                            }
                                        });

                                        testContext.completeNow();
                                    }
                                })));
                    }

                    messageReceiver = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
                    {
                        try
                        {
                            var event = message.body();

                            if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                            {
                                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_SOUND_NOTIFICATION))
                                {
                                    LOGGER.info(String.format("received event %s", event.encode()));

                                    Assertions.assertNotNull(eventContext.getString(SEVERITY));

                                    Assertions.assertNotNull(eventContext.getString(POLICY_KEY));

                                    SOUND_NOTIFICATION_TRIGGERED.set(true);

                                    messageReceiver.unregister();
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });

                }));
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        var valid = false;

        if (UNSUPPRESSED.get())
        {
            valid = true;
        }
        else
        {
            testContext.failNow("policy unsuppress operation is not successful");
        }

        UserConfigStore.getStore().deleteItem(userId);

        items.clear();

        notifications.clear();

        if (valid)
        {
            if (SOUND_NOTIFICATION_TRIGGERED.get())
            {
                testContext.completeNow();
            }

            else
            {
                testContext.failNow("sound notification not received!");
            }
        }
    }

    private static void assertMetricPolicyInspectionTestResult(VertxTestContext testContext, long entityId, long policyId, String metric, String policyType, String instance, String severity) throws Exception
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var field = MetricPolicyInspector.class.getDeclaredField("triggeredPolicies");

        field.setAccessible(true);

        var triggeredPolicies = (Map<String, JsonObject>) field.get(METRIC_POLICY_INSPECTOR);

        Assertions.assertFalse(triggeredPolicies.isEmpty());

        var policyKey = EMPTY_VALUE;

        if (!instance.isEmpty())
        {
            policyKey = entityId + SEPARATOR + policyType + SEPARATOR + metric + SEPARATOR + instance;

            if (instance.equalsIgnoreCase("system.process~virtual.memory.bytes"))
            {
                LOGGER.info(String.format("triggered policy : %s ", triggeredPolicies));
            }
        }
        else
        {
            policyKey = entityId + SEPARATOR + policyType + SEPARATOR + metric;
        }

        LOGGER.info(String.format("policy key : %s and severity : %s", policyKey, severity));

        Assertions.assertTrue(triggeredPolicies.containsKey(policyKey));

        var policy = triggeredPolicies.get(policyKey);

        Assertions.assertEquals(policyId, policy.getLong(ID));

        Assertions.assertEquals(severity, policy.getString(SEVERITY));

        testContext.completeNow();
    }

    private static void assertMetricPolicyInspectionTestResult(VertxTestContext testContext, JsonObject context, long entityId, long policyId, String severity) throws Exception
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var field = MetricPolicyInspector.class.getDeclaredField("triggeredPolicies");

        field.setAccessible(true);

        var triggeredPolicies = (Map<String, JsonObject>) field.get(METRIC_POLICY_INSPECTOR);

        Assertions.assertFalse(triggeredPolicies.isEmpty());

        var policyKey = EMPTY_VALUE;

        policyKey = entityId + SEPARATOR + "Metric Threshold" + SEPARATOR + "system.memory.available.bytes";

        Assertions.assertTrue(triggeredPolicies.containsKey(policyKey));

        var policy = triggeredPolicies.get(policyKey);

        Assertions.assertEquals(policyId, policy.getLong(ID));

        Assertions.assertEquals(severity, policy.getString(SEVERITY));

        Assertions.assertEquals(CommonUtil.getString(Math.round(CommonUtil.getDouble(context.getJsonObject(RESULT).getString("system.memory.available.bytes")))), policy.getString(VALUE));

        testContext.completeNow();
    }

    private static void assertSuppressPolicyTestResult(VertxTestContext testContext, JsonObject context)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.SUPPRESS_POLICY && message.body().getString(POLICY_TYPE).equalsIgnoreCase("Metric Threshold"))
            {

                assertEquals(context.getLong(AIOpsObject.OBJECT_ID), message.body().getLong(ENTITY_ID));

                assertEquals(context.getLong(POLICY_ID), message.body().getLong(POLICY_ID));

                messageConsumer.unregister(response -> testContext.completeNow());
            }
        });

    }

    private static boolean assertActiveNotificationTestResult(JsonObject event)
    {
        Assertions.assertTrue(event.containsKey(RESULT));

        var items = event.getJsonArray(RESULT);

        Assertions.assertNotNull(items);

        Assertions.assertFalse(items.isEmpty());

        var qualifiedObjects = ObjectConfigStore.getStore().getObjectIdsByGroups(UserConfigStore.getStore().getItem(userId).getJsonArray(User.USER_GROUPS));

        LOGGER.info(String.format("qualified objects : %s ", qualifiedObjects.encode()));

        Assertions.assertNotNull(qualifiedObjects);

        Assertions.assertFalse(qualifiedObjects.isEmpty());

        var valid = true;

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if (!(qualifiedObjects.contains(ObjectConfigStore.getStore().getObjectId(item.getLong(APIConstants.ENTITY_ID)))))
            {
                LOGGER.info(String.format("invalid object received : %s , invalid alert : %s ", ObjectConfigStore.getStore().getItem(item.getLong(APIConstants.ENTITY_ID)).encode(), item.encode()));

                valid = false;
            }
        }

        return valid;
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateMetricPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 10, \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateMetricPolicyHavingObject(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{ \"policy.name\": \"Test CPU 1\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("latest.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetMetricPolicyReferencesByMonitor(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case: " + testInfo.getTestMethod().get().getName());

        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("latest.cpu") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var objects = body.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName());

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateMetricPolicyHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU 2\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entity.type\": \"Group\", \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("update.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetMetricPolicyReferencesByGroup(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("update.cpu") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var objects = body.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName());

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSuppressPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU MP\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] } } }");

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("mp.cpu", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    assertSuppressPolicyTestResult(testContext, context);

                    context.put(AIOpsObject.OBJECT_ID, object.getLong(ID)).put(PolicyEngineConstants.POLICY_SUPPRESSION_TIME, 10).put(POLICY_ID, IDS.getLong("mp.cpu"));

                    TestUtil.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_POLICY_SUPPRESS).put(SESSION_ID, TestUtil.getSessionId())
                            .put(EVENT_CONTEXT, context));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateAvailabilityPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Availability\", \"policy.type\": \"Availability\", \"policy.rolling.window\": null, \"policy.context\": { \"entities\": [], \"metric\": \"status\", \"policy.metric.plugins\": [ 0 ], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.trigger.time\": 30, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": { \"Notification\": { \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } } } }");

        context.put(POLICY_NAME, "availabilit" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("availability", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateAvailabilityPolicyHavingObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Availability 2\", \"policy.type\": \"Availability\", \"policy.rolling.window\": null, \"policy.context\": { \"entity.type\": \"Monitor\", \"entities\": [ 75187798272743 ], \"metric\": \"status\", \"policy.metric.plugins\": [ 0 ], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.trigger.time\": 30, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Notification\": { \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } } } }");

        context.put(POLICY_NAME, "availabilit" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateAvailabilityPolicyHavingGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test Availability 3\", \"policy.type\": \"Availability\", \"policy.rolling.window\": null, \"policy.context\": { \"entity.type\": \"Group\", \"entities\": [ 75187798272743 ], \"metric\": \"status\", \"policy.metric.plugins\": [ 0 ], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.trigger.time\": 30, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": { \"Notification\": { \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } } } }");

        context.put(POLICY_NAME, "availabilit" + System.currentTimeMillis());
        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L).add(10000000000017L));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreateMetricPolicyHavingInstanceFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Instance Filter\",\"policy.type\":\"Metric Threshold\",\"policy.rolling.window\":null,\"policy.context\":{\"entities\":[],\"metric\":\"interface~packets\",\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":15,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"100000\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"90000\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"80000\"},\"CLEAR\":{\"policy.condition\":\"<\",\"policy.threshold\":\"80000\"}},\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"interface~link.type\",\"operator\":\"=\",\"value\":\"WAN\"}]}]}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{}}");

        context.put(POLICY_NAME, "metricfilter" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("instance.filter", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetAllMetricPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var items = body.getJsonArray(RESULT);

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetMetricPolicyReferences(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("cpu") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName());

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testGetMetricPolicyReferencesDataSecurity(VertxTestContext testContext)
    {

        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "MinimalAccess");

        Assertions.assertNotNull(item);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                .put(USER_PASSWORD, item.getString(USER_PASSWORD)), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var response = asyncResult.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("cpu") + "/references",
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        testContext.succeeding(asyncResponse ->
                                testContext.verify(() ->
                                {
                                    LOGGER.info("testGetMetricPolicyReferencesDataSecurity : response: " + asyncResponse.bodyAsJsonObject().encode());

                                    assertEquals(SC_OK, asyncResponse.statusCode());

                                    var responseBody = asyncResponse.bodyAsJsonObject();

                                    Assertions.assertNotNull(responseBody);

                                    assertEquals(SC_OK, responseBody.getInteger(RESPONSE_CODE));

                                    var items = responseBody.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName());

                                    Assertions.assertNotNull(items);

                                    Assertions.assertFalse(items.isEmpty());

                                    testContext.completeNow();

                                })));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                testContext.failNow(asyncResult.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreateAvailabilityPolicyHavingInstanceFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Instance Filter Availability\",\"policy.type\":\"Availability\",\"policy.rolling.window\":null,\"policy.context\":{\"entity.type\":\"Monitor\",\"entities\":[],\"metric\":\"interface~status\",\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":30,\"policy.trigger.time\":30,\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{}}");

        context.put(POLICY_NAME, "availabilitfilter" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("availability.instance", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testUpdateMetricPolicyHavingGroup(VertxTestContext testContext)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU 2\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entity.type\": \"Group\", \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] } } }");

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000018L));

        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("update.cpu"), context, testContext.succeeding(response ->
                //                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore() , response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.METRIC_POLICY.getName()));
                testContext.verify(testContext::completeNow)));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testDisableMetricPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("cpu") + "/state", new JsonObject().put(POLICY_STATE, GlobalConstants.NO), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Disabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testEnableMetricPolicy(VertxTestContext testContext)
    {
        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("cpu") + "/state", new JsonObject().put(POLICY_STATE, YES), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Enabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testInspectCPUMetricPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("update.cpu"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testInspectInterfacePacketMetricPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        LOGGER.info(testInfo.getTestMethod().get().getName() + ": " + MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID))));

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("instance.filter"), "interface~packets", "Metric Threshold", "63", Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testCreateMetricPolicy1(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] }, \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"WARNING\": {}, \"CLEAR\": {} } } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, "Monitor").put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.new", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testCheckMultipleCPUMetricPolicy(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testCpuMetricPolicyLast");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.new"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.WARNING.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testCheckMetricPolicyAutoClear(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.new"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.CLEAR.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testCreateMetricPolicy2(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Instance Filter\", \"policy.type\": \"Metric Threshold\", \"policy.rolling.window\": null, \"policy.context\": { \"entities\": [], \"metric\": \"interface~packets\", \"policy.metric.plugins\": [ 201 ], \"instance.type\": \"interface\", \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 30, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"100000\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"90000\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"80000\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"80000\" } }, \"filters\": { \"data.filter\": {} }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": { \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] }, \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } } } } }");

        context.put(POLICY_NAME, "metricfilter" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("instance.without.filter", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testCheckMultipleInterfacePacketMetricPolicy(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectInterfacePacketMetricPolicy");

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("instance.without.filter"), "interface~packets", "Metric Threshold", "63", Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testCheckMetricPolicyRenotification(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var context = TestConstants.prepareParams("testInspectInterfacePacketMetricPolicy");


        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("instance.without.filter"), "interface~packets", "Metric Threshold", "63", Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCreateMetricPolicy3(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 2, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, "Monitor").put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.occurrence", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testInspectCPUMetricPolicy1(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testCpuMetricPolicyLast");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.occurrence"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.WARNING.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testCreateMetricPolicy4(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 5, \"policy.trigger.occurrences\": 2, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, "Monitor").put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("cpu.breach.time", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testInspectCPUMetricPolicy2(VertxTestContext testContext) throws Exception
    {
        LOGGER.info("testInspectCPUMetricPolicy2 is running ...");

        var context = TestConstants.prepareParams("testCpuMetricPolicyLast");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.breach.time"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.WARNING.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testClearPolicy(VertxTestContext testContext)
    {
        LOGGER.info("testClearPolicy is running ....");

        var context = new JsonObject("{\"acknowledge\":\"yes\",\"policy.id\":103906020250283,\"policy.type\":0,\"object.id\":103906020250450,\"user.id\":10000000000001,\"user.name\":\"admin\",\"session-id\":\"5dcda078-ddcf-4623-a9fa-aa9eadf26555\"}");

        context.put(METRIC, "system.cpu.percent").put(AIOpsObject.OBJECT_ID, object.getLong(ID)).put(ENTITY_ID, object.getLong(ID)).put(POLICY_ID, IDS.getLong("cpu.breach.time")).put("action.type", "clear");

        Bootstrap.vertx().eventBus().publish(EVENT_METRIC_POLICY_CLEAR, context.put(POLICY_NOTE, String.format(InfoMessageConstants.POLICY_NOTED, MetricPolicyConfigStore.getStore().getItem(IDS.getLong("cpu.breach.time")).getString(POLICY_NAME), "admin", "Test Note")).put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, "admin"));

        LOGGER.trace(String.format("event request sent : %s ", context.encode()));

        Bootstrap.vertx().eventBus().send(EVENT_METRIC_POLICY_COMMENT_UPDATE, context);

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
        {
            try
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
                {
                    if (reply.succeeded())
                    {
                        try
                        {
                            var result = reply.result().body();

                            Assertions.assertNotNull(result, "Policy duration is invalid");

                            var key = object.getLong(ID) + SEPARATOR + IDS.getLong("cpu.breach.time") + SEPARATOR + context.getString(METRIC);

                            Assertions.assertTrue(result.containsKey(key));

                            LOGGER.info(String.format("policy duration : %s of policy key : %s ", result.getJsonObject(key).encode(), key));

                            Assertions.assertTrue(result.getJsonObject(key).containsKey(POLICY_NOTE));

                            Assertions.assertEquals(result.getJsonObject(key).getString(POLICY_NOTE), context.getString(POLICY_NOTE));

                            assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.breach.time"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.CLEAR.name());
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        testContext.failNow(reply.cause());
                    }
                });
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testCreateMetricPolicy5(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Test CPU\", \"policy.type\": \"Metric Threshold\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 5, \"policy.trigger.occurrences\": 2, \"policy.auto.clear.timer.seconds\": 15, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" }, \"CLEAR\": { \"policy.condition\": \"<\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.monitor.polling.failed.notification.status\": \"yes\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000001 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        context.put(POLICY_NAME, "cpu" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, "Monitor").put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    @Timeout(90 * 1000)
    void testInspectInterfaceAvailabilityPolicy2(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectInterfacePacketMetricPolicy");

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var objects = MetricPolicyCacheStore.getStore().getSeverities().getJsonObject("object.severity");

        Assertions.assertFalse(objects.isEmpty());

        LOGGER.info(String.format("object id : %s and objects %s ", networkObject.getLong(ID), objects.encodePrettily()));

        Assertions.assertEquals(Severity.CRITICAL.name(), CommonUtil.getString(objects.getValue(CommonUtil.getString(networkObject.getLong(ID)))));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testInspectCPUMetricPolicyInvalidPolling(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @Timeout(90 * 1000)
    @Disabled
    void testInspectInterfaceAvailabilityPolicy(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectInterfacePacketMetricPolicy");

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("availability.instance"), "interface~status", "Availability", "63", Severity.CLEAR.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    @Timeout(90 * 1000)
    @Disabled
    void testInspectObjectAvailabilityPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("availability"), STATUS, "Availability", EMPTY_VALUE, Severity.CLEAR.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testAcknowledgePolicy(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"acknowledge\":\"yes\",\"policy.id\":103906020250283,\"policy.type\":0,\"object.id\":103906020250450,\"instance\":\"Gi1/0/21-28\",\"user.id\":10000000000001,\"user.name\":\"admin\",\"session-id\":\"5dcda078-ddcf-4623-a9fa-aa9eadf26555\"}");

        Assertions.assertNotNull(networkObject);

        context.put(AIOpsObject.OBJECT_ID, networkObject.getLong(ID)).put(POLICY_ID, IDS.getLong("availability.instance")).put("action.type", ACKNOWLEDGED);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_POLICY_ACKNOWLEDGE).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, context));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testInspectCPUMetricPolicy4(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testInspectCPUMetricPolicy");

        var windowsObject = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(windowsObject);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(windowsObject.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testCreateMetricPolicy6(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Instance Filter\",\"policy.type\":\"Metric Threshold\",\"policy.rolling.window\":null,\"policy.context\":{\"entities\":[],\"metric\":\"interface~packets\",\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":15,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"100000\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"90000\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"80000\"},\"CLEAR\":{\"policy.condition\":\"<\",\"policy.threshold\":\"80000\"}},\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"exclude\",\"groups\":[{\"filter\":\"exclude\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"interface~link.type\",\"operator\":\"=\",\"value\":\"WAN\"}]}]}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{}}");

        context.put(POLICY_NAME, "metricfilter" + System.currentTimeMillis());

        var policyIds = new JsonArray();

        IDS.getMap().values().forEach(policyIds::add);

        var retries = new AtomicInteger();

        for (var i = 0; i < policyIds.size(); i++)
        {
            TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + policyIds.getLong(i), testContext.succeeding(deleteResponse ->
                    testContext.verify(() ->
                    {
                        retries.getAndIncrement();

                        if (retries.get() == policyIds.size())
                        {
                            TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                                    testContext.verify(() ->
                                    {
                                        TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                                        IDS.put("instance.exclude.filter", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                                        testContext.completeNow();
                                    })));
                        }
                    })));
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testInspectInterfacePacketMetricPolicy2(VertxTestContext testContext) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(2), id ->
        {
            try
            {
                var context = TestConstants.prepareParams("testInspectInterfacePacketMetricPolicy");

                Assertions.assertNotNull(networkObject);

                var result = new JsonObject();

                var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

                for (var i = 0; i < items.size(); i++)
                {
                    var item = items.getJsonObject(i);

                    if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
                    {
                        result.mergeIn(item);

                        break;
                    }
                }

                Assertions.assertFalse(result.isEmpty());

                context.mergeIn(result);

                Bootstrap.vertx().eventBus().send("metric.policy.test", context);

                assertMetricPolicyInspectionTestResult(testContext, networkObject.getLong(ID), IDS.getLong("instance.exclude.filter"), "interface~packets", "Metric Threshold", "61", Severity.CRITICAL.name());
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testCreateMetricPolicy7(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.type\":\"Metric Threshold\",\"id\":10000000000003,\"policy.name\":\"Disk Utilization\",\"policy.context\":{\"entity.type\":\"Monitor\",\"entities\":[5573909582074],\"metric\":\"system.disk.volume~used.percent\",\"policy.metric.plugins\":[92],\"instance.type\":\"system.disk.volume\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">=\",\"policy.threshold\":\"1\"},\"MAJOR\":{\"policy.condition\":\">=\",\"policy.threshold\":\"95\"},\"WARNING\":{\"policy.condition\":\">=\",\"policy.threshold\":\"85\"}},\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"exclude\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"system.disk.volume~instance.name\",\"operator\":\"in\",\"value\":[\"/dev/nvme0n1p2\"]}]}]}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{},\"policy.creation.time\":1709208105,\"policy.state\":\"yes\",\"_type\":\"1\",\"policy.rolling.window\":null,\"policy.scheduled\":\"no\",\"policy.monitor.polling.failed.notification.timer.seconds\":0}");

        context.put(POLICY_NAME, "Disk utilization filter test " + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITY_TYPE, "Monitor").put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("instance.exclude.not.in.filter", response.bodyAsJsonObject().getLong(ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testInspectDiskVolumeExcludeFilter(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectDiskVolumeExcludeFilter");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var policy = MetricPolicyConfigStore.getStore().getItem(IDS.getLong("instance.exclude.not.in.filter"));

        var excludedInstances = policy.getJsonObject(POLICY_CONTEXT).getJsonObject(FILTERS).getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0).getJsonArray(CONDITIONS).getJsonObject(0).getJsonArray(VALUE).getString(0);

        var instances = context.getJsonObject(RESULT).getJsonArray("system.disk.volume");

        var field = MetricPolicyInspector.class.getDeclaredField("triggeredPolicies");

        field.setAccessible(true);

        var triggeredPolicies = (Map<String, JsonObject>) field.get(METRIC_POLICY_INSPECTOR);

        Assertions.assertFalse(triggeredPolicies.isEmpty());

        for (var index = 0; index < instances.size(); index++)
        {
            var instance = instances.getJsonObject(index).getString("system.disk.volume");

            // excluded instance should not be in triggeredPolicies. Other instance must be in triggeredPolicies
            if (instance.equalsIgnoreCase(excludedInstances))
            {
                Assertions.assertFalse(triggeredPolicies.containsKey(object.getLong(ID) + SEPARATOR + "Metric Threshold" + SEPARATOR + "system.disk.volume~used.percent" + SEPARATOR + instance));
            }
            else
            {
                Assertions.assertTrue(triggeredPolicies.containsKey(object.getLong(ID) + SEPARATOR + "Metric Threshold" + SEPARATOR + "system.disk.volume~used.percent" + SEPARATOR + instance));
            }
        }

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testGetAllPolicyCacheSeverity(VertxTestContext testContext)
    {
        var entries = MetricPolicyCacheStore.getStore().getSeverities();

        Assertions.assertTrue(entries != null && !entries.isEmpty());

        Assertions.assertTrue(entries.containsKey("object.severity"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testGetAllMetricPolicy2(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var items = body.getJsonArray(RESULT);

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testCreateAvailableMemoryBytesMetricPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"memory_available\",\"policy.type\":\"Metric Threshold\",\"policy.rolling.window\":null,\"policy.scheduled\":\"no\",\"policy.context\":{\"entities\":[],\"metric\":\"system.memory.available.bytes\",\"policy.metric.plugins\":[3],\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\">\",\"policy.threshold\":\"400000000\"},\"MAJOR\":{\"policy.condition\":\"=\",\"policy.threshold\":\"100000000\"},\"WARNING\":{\"policy.condition\":\"=\",\"policy.threshold\":\"50000000\"}},\"filters\":{\"data.filter\":{}}},\"policy.email.notification.recipients\":[],\"policy.monitor.polling.failed.notification.timer.seconds\":0,\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.actions\":{}}");

        context.put(POLICY_NAME, "memory_available" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("memory.available", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testInspectMetricPolicyForLargeIntegerValue(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectMetricPolicyForLargeIntegerValue");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        // MOTADATA-1415 : Inspecting Policy for value "8.345616384E8" with threshold value "400000000"

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, context, object.getLong(ID), IDS.getLong("memory.available"), Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testCreateAlwaysCriticalPolicyForCPU(VertxTestContext testContext, TestInfo testInfo)
    {
        var policy = new JsonObject("{ \"policy.name\": \"ALWAYS-CRITICAL-MEMORY\", \"policy.type\": \"Metric Threshold\", \"policy.rolling.window\": null, \"policy.scheduled\": \"no\", \"policy.context\": { \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 3 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">\", \"policy.threshold\": \"10\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$-$$$object.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$seveirty$$$ state with value $$$value$$$\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"CLEAR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" }, { \"recipient\": \"user1\", \"type\": \"user\" } ] }, \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } }, \"Renotification\": { \"renotify.acknowledged\": \"yes\", \"CRITICAL\": { \"timer.seconds\": 30, \"recipients\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } } } }");

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, policy, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put("ALWAYS-CRITICAL-MEMORY", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testNotificationForTriggeredPolicy(VertxTestContext testContext) throws Exception  // test case for send notification for triggered or flap change policy
    {
        var context = TestConstants.prepareParams("testInspectMetricPolicyForLargeIntegerValue");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        var count = new AtomicInteger();

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {
            var notification = message.body();

            if (!notification.isEmpty() && notification.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))   // check notification received for triggered policy or flap changed
            {
                Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                if (count.incrementAndGet() >= 2)
                {
                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testReNotificationForTriggeredPolicy(VertxTestContext testContext, TestInfo testInfo) throws Exception     // test case for renotify user for staying policy in the same severity for the given time period
    {
        var context = TestConstants.prepareParams("testInspectMetricPolicyForLargeIntegerValue");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        testContext.awaitCompletion(30, TimeUnit.SECONDS);  // wait for policy stay in critical for 30 seconds to renotify

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            var notification = message.body();

            if (!notification.isEmpty() && notification.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))    // check email received for renotification
            {
                Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testNotifyUserForFlapChange(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var context = TestConstants.prepareParams("testInspectMetricPolicyForLargeIntegerValue");

        context.getJsonObject(RESULT).put("system.memory.used.percent", 0); // flap change from critical to clear

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result);

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "user1");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
        {

            var notification = message.body();

            if (!notification.isEmpty() && notification.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains(user.getString(User.USER_EMAIL)))   // check notification received for triggered policy or flap changed
            {
                Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                messageConsumer.unregister(asyncResult -> testContext.completeNow());
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testCreateMetricPolicyHavingTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = TagConfigStore.getStore().addItems(new JsonArray().add("10.46 Test"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);

        var item = new JsonObject().mergeIn(object);

        if (!ObjectConfigStore.getStore().getItem(object.getLong(ID)).getJsonArray(AIOpsObject.OBJECT_TAGS, new JsonArray()).contains(id.getLong(0)))
        {
            item.put(AIOpsObject.OBJECT_TAGS, object.getJsonArray(AIOpsObject.OBJECT_TAGS, new JsonArray()).add("10.46 Test"));
        }

        TestAPIUtil.put(TestAPIConstants.OBJECT_API_ENDPOINT + "/" + object.getLong(ID), item, result ->
        {
            if (result.succeeded())
            {
                Assertions.assertNotNull(result.result().bodyAsJsonObject());

                assertEquals(String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()).trim(), result.result().bodyAsJsonObject().getString(GlobalConstants.MESSAGE).trim());

                assertEquals(HttpStatus.SC_OK, result.result().bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                var tagId = TagConfigStore.getStore().getTag("10.46 Test");

                Assertions.assertNotEquals(DUMMY_ID, tagId);

                Assertions.assertTrue(TagConfigStore.getStore().existItem(tagId));

                var context = new JsonObject("{ \"policy.name\" : \"Test Object Tags\", \"policy.type\" : \"Metric Threshold\", \"policy.rolling.window\" : null, \"policy.scheduled\" : \"no\", \"policy.context\" : { \"entity.type\" : \"Tag\", \"entities\" : [ ], \"metric\" : \"system.cpu.percent\", \"policy.metric.plugins\" : [ 504, 88 ], \"policy.trigger.time\" : 300, \"policy.trigger.occurrences\" : 1, \"policy.auto.clear.timer.seconds\" : 0, \"policy.severity\" : { \"CRITICAL\" : { \"policy.condition\" : \">=\", \"policy.threshold\" : \"0\" }, \"MAJOR\" : { \"policy.condition\" : \">=\", \"policy.threshold\" : \"1\" } }, \"filters\" : { \"data.filter\" : { } } }, \"policy.email.notification.subject\" : \"$$$severity$$$ - $$$object.name$$$\", \"policy.message\" : \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on $$$object.host$$$($$$object.ip$$$)\", \"policy.renotify\" : \"no\", \"policy.renotify.acknowledged\" : \"yes\", \"policy.actions\" : { }, \"policy.notification.context\" : [ { \"policy.email.notification.recipients\" : [ ], \"policy.user.notification.recipients\" : [ ], \"policy.notification.severity\" : [ \"CRITICAL\", \"MAJOR\", \"WARNING\", \"CLEAR\" ] } ], \"policy.sound.notification\" : \"no\" }");

                context.put(POLICY_NAME, context.getString(POLICY_NAME) + System.currentTimeMillis());

                context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add("10.46 Test"));

                TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put("cpu.policy.tag", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                            testContext.completeNow();
                        })));
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testInspectCPUMetricPolicyHavingTag(VertxTestContext testContext) throws Exception
    {
        var context = TestConstants.prepareParams("testInspectCPUMetricPolicy");

        Assertions.assertNotNull(object);

        LOGGER.info(String.format("object : %s ", object.encode()));

        var result = new JsonObject();

        var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID)));

        for (var index = 0; index < metrics.size(); index++)
        {
            var metric = metrics.getJsonObject(index);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        context.mergeIn(result).put(TAGS, ObjectConfigStore.getStore().getItem(object.getLong(ID)).getJsonArray(AIOpsObject.OBJECT_TAGS));

        LOGGER.info(String.format("context send : %s ", context.encode()));

        Bootstrap.vertx().eventBus().send("metric.policy.test", context);

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        assertMetricPolicyInspectionTestResult(testContext, object.getLong(ID), IDS.getLong("cpu.policy.tag"), "system.cpu.percent", "Metric Threshold", EMPTY_VALUE, Severity.CRITICAL.name());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testQueryMetricSeverity(VertxTestContext testContext)
    {
        var event = new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(UI_EVENT_UUID, "4ad1e808-2ce3-4994-8fc6-b598e54b2f16");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_SEVERITY_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("%s event received Severity Query %s ", EVENT_SEVERITY_QUERY, context.encode()));

                assertNotNull(context);

                assertTrue(context.containsKey("object.severity"));

                assertFalse(context.getJsonObject("object.severity").isEmpty());

                assertTrue(context.containsKey("instance.severity"));

                assertFalse(context.getJsonObject("instance.severity").isEmpty());

                assertTrue(context.containsKey("application.severity"));

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_SEVERITY_QUERY, event);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testProcessMetricSeverityChangeEvent(VertxTestContext testContext)
    {
        var event = new JsonObject("{\"id\":72691822610,\"policy.key\":72691822610,\"severity\":\"Clear\",\"severity.type\":\"object.severity\",\"user.name\":\"admin\"}");

        TestUtil.vertx().eventBus().send(EVENT_SEVERITY_CHANGE, event);

        event = new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_SEVERITY_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("%s event received in Severity Change %s ", EVENT_SEVERITY_QUERY, context.encode()));

                assertNotNull(context);

                assertTrue(context.containsKey("object.severity"));

                assertFalse(context.getJsonObject("object.severity").isEmpty());

                messageConsumer.unregister(response -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().publish(EVENT_USER_PING, event);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testMailRunbookOutput(VertxTestContext testContext, TestInfo testInfo) throws Exception  // test case for runbook output mail notification once we removed emails from policy
    {
        var params = new JsonObject("{\"policy.renotify\": \"no\",\"policy.notification.context\": []}");

        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("ALWAYS-CRITICAL-MEMORY"), params, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(MetricPolicyConfigStore.getStore(), params, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    var context = TestConstants.prepareParams("testInspectMetricPolicyForLargeIntegerValue");

                    Assertions.assertNotNull(object);

                    var metric = new JsonObject();

                    var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID)));

                    for (var i = 0; i < items.size(); i++)
                    {
                        var item = items.getJsonObject(i);

                        if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
                        {
                            metric.mergeIn(item);

                            break;
                        }
                    }

                    Assertions.assertFalse(metric.isEmpty());

                    context.mergeIn(metric);

                    Bootstrap.vertx().eventBus().send("metric.policy.test", context);

                    var retries = new AtomicInteger();

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
                    {
                        var event = message.body();

                        if (!event.isEmpty() && event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))   // check notification received for triggered policy or flap changed
                        {
                            Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                            retries.incrementAndGet();
                        }
                    });

                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(80), timer ->
                    {
                        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("ALWAYS-CRITICAL-MEMORY"), new JsonObject("{\"policy.renotify\": \"no\", \"policy.notification.context\": [ { \"policy.email.notification.recipients\": [ \"<EMAIL>\" ], \"policy.user.notification.recipients\": [], \"policy.notification.severity\": [ \"CRITICAL\", \"MAJOR\", \"WARNING\" ] }, { \"policy.email.notification.recipients\": [ \"<EMAIL>\" ], \"policy.user.notification.recipients\": [ \"user1\" ], \"policy.notification.severity\": [ \"CLEAR\" ] } ] }"),
                                testContext.succeeding(asyncResult -> testContext.verify(() -> LOGGER.info(asyncResult.bodyAsJsonObject()))));

                        if (retries.get() == 0) // if no mail received withing 80 seconds
                        {
                            testContext.completeNow();
                        }
                    });
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testGetMetricPolicyReferencesByTag(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("cpu.policy.tag") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var objects = body.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName());

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testDeletePolicy(VertxTestContext testContext)
    {
        var objects = MetricPolicyCacheStore.getStore().getSeverities().getJsonObject("object.severity");

        var severity = objects.getString(CommonUtil.getString(object.getLong(ID)));

        Assertions.assertEquals(Severity.CRITICAL.name(), severity);

        TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + IDS.getLong("instance.filter"), response ->
        {
            if (response.succeeded())
            {
                Assertions.assertEquals(SC_OK, response.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_SUCCEED, response.result().bodyAsJsonObject().getString(STATUS));

                try
                {
                    testContext.awaitCompletion(7, TimeUnit.SECONDS);
                }
                catch (Exception ignored)
                {
                }

                var items = MetricPolicyCacheStore.getStore().getSeverities().getJsonObject("object.severity");

                var updatedSeverity = items.getString(CommonUtil.getString(networkObject.getLong(ID)));

                Assertions.assertEquals(Severity.CLEAR.name(), updatedSeverity);

                testContext.completeNow();

            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testPolicyWithSamePreviousFlapTimestamp(VertxTestContext testContext)  //MOTADATA-558
    {
        var clearFlapTimestamp = new AtomicLong(0L);

        var criticalFlapTimestamp = new AtomicLong(0L);

        var timestamp = DateTimeUtil.currentSeconds();

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        var objectContext = ObjectConfigStore.getStore().getItem(id);

        var key = id + SEPARATOR + DEFAULT_ID + SEPARATOR + "system.cpu.percent";

        var policyType = "Metric Threshold";

        var policyKey = id + SEPARATOR + policyType + SEPARATOR + "system.cpu.percent";

        var policyContext = new JsonObject().put(SEVERITY, "CRITICAL").put("policy.threshold", "1").put("metric.type", "Linux").put("event.timestamp", timestamp - 600).put("object.category", "Server").put("plugin.id", 88).put("id", DEFAULT_ID).put("entity.id", id).put("policy.key", policyKey).put("object.ip", objectContext.getString("object.ip")).put("object.name", objectContext.getString("object.name")).put("value", 17).put(METRIC, "system.cpu.percent");

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE, policyContext);

        TestUtil.vertx().setTimer(1000, handler ->
        {
            var autoClearPolicyContext = new JsonObject().put(SEVERITY, "CLEAR").put("policy.threshold", "1").put("metric.type", "Linux").put("event.timestamp", timestamp).put("object.category", "Server").put("plugin.id", 88).put("id", DEFAULT_ID).put("entity.id", id).put("policy.key", policyKey).put("object.ip", objectContext.getString("object.ip")).put("object.name", objectContext.getString("object.name")).put("value", "Auto CLear").put(PolicyEngineConstants.DURATION, 10).put(METRIC, "system.cpu.percent");

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE, autoClearPolicyContext);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
                    clearFlapTimestamp.set(reply.result().body().getJsonObject(key).getLong(PREVIOUS_FLAP_TIMESTAMP)));
        });

        TestUtil.vertx().setTimer(3 * 1000, handler ->
        {
            var criticalPolicyContext = new JsonObject().put(SEVERITY, "CRITICAL").put("policy.threshold", "1").put("metric.type", "Linux").put("event.timestamp", timestamp).put("object.category", "Server").put("plugin.id", 88).put("id", DEFAULT_ID).put("entity.id", id).put("policy.key", policyKey).put("object.ip", objectContext.getString("object.ip")).put("object.name", objectContext.getString("object.name")).put("value", 10).put(METRIC, "system.cpu.percent");

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE, criticalPolicyContext);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
                    criticalFlapTimestamp.set(reply.result().body().getJsonObject(key).getLong(PREVIOUS_FLAP_TIMESTAMP)));

            Bootstrap.vertx().setTimer(1000, timerHandler ->
            {

                assertNotEquals(clearFlapTimestamp.get(), criticalFlapTimestamp.get());

                assertNotEquals(criticalFlapTimestamp.get(), timestamp);

                if (clearFlapTimestamp.get() != criticalFlapTimestamp.get() || !criticalFlapTimestamp.equals(timestamp))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow("previous.flap.timestamp are same for both flaps");
                }
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testInstanceSeverityQuery(VertxTestContext testContext)
    {
        var contexts = new JsonArray();

        var items = MetricConfigStore.getStore().getItemsByObject(networkObject.getLong(ID));

        var instances = new JsonArray();

        for (var item : items)
        {
            if (item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
            {
                var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS);

                for (var index = 0; index < objects.size(); index++)
                {
                    contexts.add(new JsonObject().put(INSTANCE, objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME)).put(ENTITY_ID, networkObject.getLong(ID)));

                    instances.add(objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME));
                }

                break;
            }
        }

        Assertions.assertFalse(instances.isEmpty());

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_INSTANCE_SEVERITY_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertTrue(event.containsKey(RESULT));

                event = event.getJsonObject(RESULT);

                for (var index = 0; index < instances.size(); index++)
                {
                    Assertions.assertTrue(event.containsKey(networkObject.getLong(ID) + SEPARATOR + instances.getString(index)));
                }

                testContext.completeNow();

                messageConsumer.unregister();
            }
        });

        Bootstrap.vertx().eventBus().send(UI_ACTION_INSTANCE_SEVERITY_QUERY, new JsonObject().put(EVENT_TYPE, UI_ACTION_INSTANCE_SEVERITY_QUERY).put(INSTANCE, true).put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_CONTEXT, contexts));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testCheckStatusFlapsDumpFile(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + NMSConstants.STATUS_FLAPS);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer.length() == 0)
            {
                testContext.failNow("status flap file is empty");
            }
            else
            {
                testContext.completeNow();
            }
        }
        else
        {
            testContext.failNow("status flap file doesn't exist");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testCorrelateObjectDownResult(VertxTestContext testContext)
    {
        var policy = new JsonObject("{\"policy.name\":\"Availability\",\"policy.type\":\"Availability\",\"policy.tags\":[\"Availability\"],\"policy.context\":{\"metric\":\"status\",\"entity.type\":\"Group\",\"entities\":[],\"policy.trigger.time\":30,\"policy.trigger.occurrences\":1,\"filters\":{\"data.filter\":{}}},\"policy.deprovision.time\":0,\"policy.email.notification.recipients\":[],\"policy.renotify\":\"yes\",\"policy.monitor.polling.failed.notification.status\":\"no\",\"policy.renotification.timer.seconds\":0,\"policy.actions\":{},\"policy.creation.time\":1653989532,\"policy.state\":\"yes\",\"_type\":\"1\",\"id\":95275420783154,\"policy.archived\":\"yes\"}");

        policy.put(POLICY_NAME, "correlation-availability" + System.currentTimeMillis());

        policy.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(10000000000002L));

        var context = TestConstants.prepareParams("DownAvailabilityPoll");

        Assertions.assertNotNull(networkObject);

        var result = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(context.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(item);

                break;
            }
        }

        context.mergeIn(result);

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, policy, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_AVAILABILITY_CORRELATION, message ->
                    {
                        try
                        {
                            var event = message.body();

                            event.put(AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS, new JsonArray().add(object.getLong(ID)));

                            event.put(AIOpsConstants.CORRELATED_DOWN_OBJECTS, new JsonArray().add(networkObject.getLong(ID)));

                            LOGGER.info(String.format("reply sent %s ", event.encodePrettily()));

                            message.reply(event);

                            messageConsumer.unregister(handler -> testContext.completeNow());
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            messageConsumer.unregister(handler -> testContext.failNow(exception));
                        }
                    });

                    for (var index = 0; index < 5; index++)
                    {
                        Bootstrap.vertx().eventBus().send("metric.policy.test", context);
                    }

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testDuplicateCorrelationRequest(VertxTestContext testContext) throws Exception
    {
        var params = TestConstants.prepareParams("DownAvailabilityPoll");

        Assertions.assertNotNull(networkObject);

        var context = new JsonObject();

        var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(networkObject.getLong(ID)));

        for (var i = 0; i < items.size(); i++)
        {
            var item = items.getJsonObject(i);

            if (item.getString(Metric.METRIC_NAME).equalsIgnoreCase(params.getString(Metric.METRIC_NAME)))
            {
                context.mergeIn(item);

                break;
            }
        }

        params.mergeIn(context);

        var tick = DateTimeUtil.currentSeconds();

        params.put(EVENT_TIMESTAMP, tick);

        Bootstrap.vertx().eventBus().send("metric.policy.test", params);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var field = MetricPolicyCacheStore.class.getDeclaredField("timestamps");

        field.setAccessible(true);

        var ticks = (Map<Integer, Long>) field.get(MetricPolicyCacheStore.getStore());

        assertEquals(tick, ticks.get((networkObject.getLong(ID) + SEPARATOR + PolicyType.AVAILABILITY.getName() + SEPARATOR + STATUS).hashCode()));  // when system receives down status of already Correlated object, it will update the timestamp as per normal flow.

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testQueryTriggeredDuration(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
        {
            if (reply.succeeded())
            {
                items = reply.result().body();

                sessionId = UUID.randomUUID().toString();

                LOGGER.info(String.format("durations : %s and session-id : %s ", items.size(), sessionId));

                Assertions.assertFalse(items.isEmpty());

                Assertions.assertFalse(sessionId.isEmpty());

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(reply.cause());

                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testUpdateActiveNotificationPreference(VertxTestContext testContext)
    {
        try
        {
            userId = Objects.equals(userId, DUMMY_ID) ? CommonUtil.newId() : userId;

            var admin = UserConfigStore.getStore().getItem(DEFAULT_ID).copy();

            admin.put(User.USER_NAME, "testDummyUser");

            var userPreference = admin.getJsonObject(User.USER_PREFERENCES);

            admin.put(User.USER_PREFERENCES, userPreference.put(User.USER_PREFERENCE_NOTIFICATION_POPUP_STATUS, NMSConstants.State.ENABLE.name()));

            admin.put(User.USER_GROUPS, new JsonArray().add(10000000000002L).add(10000000000003L).add(10000000000004L).add(10000000000005L).add(10000000000048L));

            UserConfigStore.getStore().addItem(userId, admin.put(ID, userId));

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, userId).put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_USER));

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_USER_PING, new JsonObject().put(SESSION_ID, sessionId).put(User.USER_NAME, "testDummyUser"));

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            var qualifiedObjects = ObjectConfigStore.getStore().getObjectIdsByGroups(UserConfigStore.getStore().getItem(userId).getJsonArray(User.USER_GROUPS));

            for (var entry : items.getMap().entrySet())
            {
                if (notifications.size() < 5)
                {
                    var item = (JsonObject) entry.getValue();

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION, item.put(PolicyEngineConstants.SEVERITY_TYPE, "object.severity").put(POLICY_ID, item.getLong(ID)));

                    if (qualifiedObjects.contains(ObjectConfigStore.getStore().getObjectId(item.getLong(APIConstants.ENTITY_ID))) && CommonUtil.isNullOrEmpty(item.getString(INSTANCE)) && !NMSConstants.APPLICATION_TYPES.contains(item.getString(Metric.METRIC_TYPE)))
                    {
                        notifications.add(item.put(POLICY_ID, item.getLong(ID)).put(PolicyEngineConstants.SEVERITY_TYPE, "object.severity"));
                    }
                }
                else
                {
                    break;
                }
            }

            LOGGER.info(String.format("alerts send : %s ", notifications.encode()));

            LOGGER.info(String.format("dummy user : %s ", admin.encode()));

            LOGGER.info(String.format("qualified objects : %s ", qualifiedObjects.encode()));

            Assertions.assertFalse(notifications.isEmpty());

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testValidateActiveNotifications(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + sessionId, message ->
        {
            var event = message.body();

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_ACTIVE_NOTIFICATION_QUERY))
            {
                event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                if (assertActiveNotificationTestResult(event))
                {
                    messageConsumer.unregister(result -> testContext.completeNow());
                }
                else
                {
                    messageConsumer.unregister(result -> testContext.failNow("received alert which user didn't have the access"));
                }
            }
        });

        Bootstrap.vertx().eventBus().send(EVENT_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(SESSION_ID, sessionId).put(User.USER_NAME, "testDummyUser"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testClearActiveNotifications(VertxTestContext testContext)
    {
        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + sessionId, message ->
            {
                var event = message.body();

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY))
                {
                    event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                    LOGGER.info(String.format("result received : %s ", event.encode()));

                    Assertions.assertTrue(event.containsKey(APIConstants.ENTITY_PROPERTY_COUNT));

                    Assertions.assertEquals(0, event.getInteger(APIConstants.ENTITY_PROPERTY_COUNT));

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            });

            Bootstrap.vertx().eventBus().send(UI_NOTIFICATION_ACTIVE_NOTIFICATION_CLEAR, new JsonObject().put(SESSION_ID, sessionId).put(User.USER_NAME, "testDummyUser"));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testQueryRecentNotifications(VertxTestContext testContext)
    {
        try
        {
            LOGGER.info("running testRecentAlertByUser");

            for (var index = 0; index < notifications.size(); index++)
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION, notifications.getJsonObject(index));
            }

            LOGGER.info("alerts send again : " + notifications.encode());

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + sessionId, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_SEVERITY_CHANGE))
                    {
                        event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                        LOGGER.info(String.format("result received at severity change : %s", event.encode()));

                        if (assertActiveNotificationTestResult(event))
                        {
                            messageConsumer.unregister(result -> testContext.completeNow());
                        }
                        else
                        {
                            messageConsumer.unregister(result -> testContext.failNow("received alert which user didn't have the access"));
                        }
                    }
                    else if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY))
                    {
                        event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                        LOGGER.info(String.format("result received at active notification query: %s ", event.encode()));

                        Assertions.assertTrue(event.containsKey(APIConstants.ENTITY_PROPERTY_COUNT));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }

            });

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_USER_PING, new JsonObject().put(SESSION_ID, sessionId).put(User.USER_NAME, "testDummyUser"));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testQueryActiveNotifications(VertxTestContext testContext)
    {
        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + sessionId, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY))
                    {
                        event = CodecUtil.toJSONObject(event.getBinary(EVENT_CONTEXT));

                        LOGGER.info(String.format("result received : %s ", event.encode()));

                        Assertions.assertTrue(event.containsKey(APIConstants.ENTITY_PROPERTY_COUNT));

                        Assertions.assertTrue(event.getInteger(APIConstants.ENTITY_PROPERTY_COUNT) > 0);

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }

            });

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_USER_PING, new JsonObject().put(SESSION_ID, sessionId).put(User.USER_NAME, "testDummyUser"));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(3 * 1000 * 60)
    @Order(67)
    void testEmailNotificationHavingInstanceMacros(VertxTestContext testContext) throws Exception
    {
        try
        {
            var context = new JsonObject("{\"severity\":\"CRITICAL\",\"policy.threshold\":\"0\",\"object.id\":10,\"metric.type\":\"Router\",\"event.timestamp\":1743757943,\"object.category\":\"Network\",\"plugin.id\":201,\"entity.id\":70950223951,\"metric.id\":70950224231,\"metric\":\"interface~in.traffic.utilization.percent\",\"id\":70959452575,\"policy.type\":\"Metric Threshold\",\"instance\":\"Et1/0-5\",\"value\":\"0.0\",\"instance.metric.context\":{\"interface~sent.discard.packets\":0,\"interface~traffic.utilization.percent\":\"0.0\",\"interface~error.packets\":0,\"interface~packets\":76127,\"interface\":\"Et1/0-5\",\"interface~type\":\"ethernetCsmacd (6)\",\"interface~address\":\"AA:BB:CC:00:04:01\",\"interface~out.traffic.bytes.per.sec\":18.73,\"interface~status\":\"Up\",\"interface~discard.packets\":0,\"interface~in.packets\":0,\"interface~admin.status\":\"up\",\"interface~received.error.packets\":0,\"interface~ip.address\":\"********\",\"interface~in.traffic.utilization.percent\":\"0.0\",\"interface~out.traffic.utilization.percent\":0.0,\"interface~last.change\":\" 0 day 0 hour 0 minute 6 seconds\",\"interface~instance.name\":\"Et1/0-5\",\"interface~out.packets\":76127,\"interface~received.discard.packets\":0,\"interface~in.traffic.bytes.per.sec\":7.23,\"interface~traffic.bytes.per.sec\":25.96,\"interface~operational.status\":\"up\",\"interface~local.ip.address\":\"fe80::a8bb:ccff:fe00:401\",\"interface~received.octets\":7101142,\"interface~alias\":\"\",\"interface~link.type\":\"LAN\",\"interface~sent.octets\":11526162,\"interface~sent.error.packets\":0,\"interface~description\":\"Ethernet1/0\",\"interface~index\":\"5\",\"interface~name\":\"Et1/0\"},\"policy.key\":\"70950223951``||``Metric Threshold``||``interface~in.traffic.utilization.percent``||``Et1/0-5\",\"policy.id\":70959452575,\"object.name\":\"g4.g4.com\",\"object.type\":\"Router\",\"object.ip\":\"************\",\"runbook.plugin.notification.email.recipients\":[\"<EMAIL>\"],\"runbook.plugin.notification.email.subject\":\"critical - g4.g4.com\",\"policy.message\":\"interface~in.traffic.utilization.percent has entered into warning state with value 0.0 on instance Et1/0-5 with interface alias  and description Ethernet1/0 for \\n parent object g4.g4.com(************) At the same time interface out utilization was 0.0\"}");

            var policy = new JsonObject("{\"policy.name\":\"Interface In Percent\",\"policy.type\":\"Metric Threshold\",\"policy.rolling.window\":null,\"policy.scheduled\":\"no\",\"policy.context\":{\"entities\":[],\"metric\":\"interface~in.traffic.utilization.percent\",\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"policy.severity\":{\"CRITICAL\":{\"policy.condition\":\"=\",\"policy.threshold\":\"50\"},\"MAJOR\":{\"policy.condition\":\"=\",\"policy.threshold\":\"30\"},\"WARNING\":{\"policy.condition\":\"=\",\"policy.threshold\":\"0\"}},\"filters\":{\"data.filter\":{}}},\"policy.title\":\"$$$severity$$$ - $$$object.name$$$\",\"policy.message\":\"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on instance $$$instance$$$ with interface alias $$$interface.alias$$$ and description $$$interface.description$$$ for \\n parent object $$$object.host$$$($$$object.ip$$$) At the same time interface out utilization was $$$interface.out.traffic.utilization.percent$$$\",\"policy.actions\":{\"Notification\":{\"Email\":{\"CRITICAL\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\",\"index\":0,\"severity\":\"CRITICAL\"}],\"WARNING\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\",\"index\":0,\"severity\":\"WARNING\"}],\"MAJOR\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\",\"index\":0,\"severity\":\"MAJOR\"}],\"CLEAR\":[{\"recipient\":\"<EMAIL>\",\"type\":\"email\",\"index\":0,\"severity\":\"CLEAR\"}]},\"channels\":{}},\"Integration\":{}},\"policy.renotify\":\"no\",\"policy.archived\":\"no\",\"policy.creation.time\":1743751987,\"policy.state\":\"yes\",\"_type\":\"1\",\"id\":70959452575}");

            var method = PolicyEngineConstants.class.getDeclaredMethod("triggerAction", String.class, boolean.class, JsonObject.class, JsonObject.class, Set.class, Set.class, Map.class, long.class, String.class, Logger.class, Set.class, StringBuilder.class);

            method.setAccessible(true);

            context.put(APIConstants.ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("************"));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
            {

                var event = message.body();

                if (!event.isEmpty() && event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))   // check notification received for triggered policy or flap changed
                {
                    Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                    Assertions.assertNotNull(event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT));

                    var content = event.getJsonObject(Notification.EMAIL_NOTIFICATION_CONTENT).toString();

                    Assertions.assertFalse(content.isEmpty());

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            });

            method.invoke(method, "CRITICAL", true, policy, context, new HashSet<String>(), new HashSet<String>(), new HashMap<String, Set<String>>(), ObjectConfigStore.getStore().getItemByIP("************"), "interface~in.traffic.utilization.percent Et1/0-5 over g4.g4.com was = 50 during last 5 mins with 1 abnormality occurrences.", LOGGER, new HashSet<String>(), new StringBuilder(EMPTY_VALUE));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testDeleteDefaultMetricPolicy(VertxTestContext testContext)
    {
        MetricPolicyConfigStore.getStore().getItem(DEFAULT_ID, false).put(FIELD_TYPE, 0);

        TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + DEFAULT_ID, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                LOGGER.info(String.format("API response : %s", result.encodePrettily()));

                Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                MetricPolicyConfigStore.getStore().getItem(DEFAULT_ID, false).put(FIELD_TYPE, 1);

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testCreateInterfacePolicyWithFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"policy.name\":\"Interface \",\"policy.type\":\"Metric Threshold\",\"policy.rolling.window\":null,\"policy.scheduled\":\"no\",\"policy.context\":{\"entity.type\":\"Monitor\",\"metric\":\"interface~packets\",\"policy.severity\":{\"WARNING\":{\"policy.condition\":\"=\",\"policy.threshold\":\"10000000\"}},\"entities\":[],\"policy.metric.plugins\":[201],\"instance.type\":\"interface\",\"policy.trigger.time\":300,\"policy.trigger.occurrences\":1,\"policy.auto.clear.timer.seconds\":0,\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"interface~name\",\"operator\":\"=\",\"value\":\"tunnel_3\"}]}]}}},\"policy.title\":\"$$$severity$$$ - $$$object.name$$$\",\"policy.message\":\"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on $$$object.host$$$($$$object.ip$$$)\",\"policy.actions\":{\"Notification\":{\"Email\":{},\"channels\":{}},\"Integration\":{}},\"policy.renotify\":\"no\"}");

        context.put(POLICY_NAME, "Interface" + System.currentTimeMillis());

        context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************")).getLong(ID)));

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }
}
