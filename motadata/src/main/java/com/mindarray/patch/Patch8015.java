/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;


import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.OAuthUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Scheduler.SCHEDULER_CONTEXT;
import static com.mindarray.api.Scheduler.SCHEDULER_JOB_TYPE;
import static com.mindarray.db.DBConstants.*;

public class Patch8015 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8015.class, MOTADATA_PATCH, "Patch 8.0.15");

    private static final String VERSION = "8.0.15";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.15");

        futures.add(updateTopologySchedulers());

        futures.add(updateMonitorNames());

        futures.add(executeServiceNowPatch());

        futures.add(executeIntegrationProfilePatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                ObjectConfigStore.getStore().initStore().onComplete(asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        promise.complete();

                        LOGGER.info("successfully executed patch 8.0.15");
                    }
                    else
                    {
                        promise.fail(asyncResult.cause());

                        LOGGER.error(asyncResult.cause());
                    }
                });
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> updateTopologySchedulers()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var ids = SchedulerConfigStore.getStore().flatItems(SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName(), ID);

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < ids.size(); index++)
            {
                try
                {
                    if (!Objects.equals(ids.getLong(index), 10000000000012L))
                    {
                        var item = SchedulerConfigStore.getStore().getItem(ids.getLong(index));

                        if (item.containsKey(SCHEDULER_CONTEXT) && item.getJsonObject(SCHEDULER_CONTEXT).containsKey("topology.plugin.exclude.targets")
                                && CommonUtil.isNotNullOrEmpty(item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray("topology.plugin.exclude.targets")))
                        {
                            var context = item.getJsonObject(SCHEDULER_CONTEXT);

                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            context.put("topology.plugin.filter.targets", context.getJsonArray("topology.plugin.exclude.targets"))
                                    .put("topology.plugin.filter.target.type", "exclude-" + NMSConstants.TopologyFilterType.valueOfName(context.getString("topology.plugin.exclude.target.type")).getName());


                            Bootstrap.configDBService().update(TBL_SCHEDULER, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                    new JsonObject().put(SCHEDULER_CONTEXT, context), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                    result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            SchedulerConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    LOGGER.info(String.format("item updated successfully for scheduler %s", item));

                                                    future.complete();
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("Failed to update scheduler for %s with reason %s", item, asyncResult.cause()));

                                                    future.fail(asyncResult.cause());
                                                }
                                            });
                                        }
                                        else
                                        {
                                            LOGGER.error(result.cause());

                                            future.fail(result.cause());
                                        }
                                    });
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("topology scheduler updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to update topology schedulers : " + result.cause());

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> updateMonitorNames()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = ObjectConfigStore.getStore().getItems();

            var monitorNames = new HashSet<String>();

            var futures = new ArrayList<Future<Void>>();

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var update = false;

                    var objectName = item.getString(AIOpsObject.OBJECT_NAME);

                    if (item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
                    {
                        objectName = item.getString(AIOpsObject.OBJECT_TARGET);

                        update = true;
                    }

                    /* If duplicate monitor name exist then we will update any one object name.
                       So unique key will be used i.e. object name + entity ID.
                    */
                    if (monitorNames.contains(objectName))
                    {
                        objectName += "(" + CommonUtil.getString(item.getLong(ID)) + ")";

                        update = true;
                    }

                    item.put(AIOpsObject.OBJECT_NAME, objectName);

                    monitorNames.add(objectName);

                    if (update)
                    {
                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        Bootstrap.configDBService().update(TBL_OBJECT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    LOGGER.info(String.format("object having target %s updated successfully", item.getString(AIOpsObject.OBJECT_TARGET)));

                                                    future.complete();
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("Failed to update object having target %s, possible reason : %s", item.getString(AIOpsObject.OBJECT_TARGET), asyncResult.cause().getMessage()));

                                                    future.fail(asyncResult.cause());
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                LOGGER.warn("exception occurred while updating object target : " + item.getString(AIOpsObject.OBJECT_TARGET));

                                                future.fail(exception.getCause());
                                            }
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.error(result.cause());

                                        LOGGER.warn("exception occurred while updating object target : " + item.getString(AIOpsObject.OBJECT_TARGET));

                                        future.fail(result.cause());
                                    }
                                });
                    }
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("update monitor names updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to update monitor names : " + result.cause());

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> executeServiceNowPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var item = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICENOW.getName());

            var credentialProfile = new JsonObject();

            if (item.containsKey(Integration.INTEGRATION_CONTEXT)
                    && item.getJsonObject(Integration.INTEGRATION_CONTEXT) != null
                    && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty()
                    && item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET)
                    && CommonUtil.isNotNullOrEmpty(item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET))
                    && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE))
            {
                credentialProfile.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "ServiceNow Credential Profile")
                        .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, NMSConstants.Protocol.HTTP_HTTPS.getName())
                        .put(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, new JsonObject()
                                .put(OAuthUtil.AUTHENTICATION_TYPE, NMSConstants.AuthenticationType.BASIC.getName())
                                .put(USERNAME, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(USERNAME, EMPTY_VALUE))
                                .put(PASSWORD, item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(PASSWORD, EMPTY_VALUE)));

                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(USERNAME);

                item.getJsonObject(Integration.INTEGRATION_CONTEXT).remove(PASSWORD);

                Bootstrap.configDBService().save(DBConstants.TBL_CREDENTIAL_PROFILE, credentialProfile,
                        DEFAULT_USER, MOTADATA_SYSTEM, response ->
                        {
                            if (response.succeeded())
                            {
                                CredentialProfileConfigStore.getStore().addItem(response.result()).onComplete(asyncResponse ->
                                {
                                    if (asyncResponse.succeeded())
                                    {
                                        LOGGER.info(String.format("updated Credential Profile : %s : profile name: %s", response.result(), CredentialProfileConfigStore.getStore().getItem(response.result()).getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)));

                                        item.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, response.result());

                                        Bootstrap.configDBService.update(DBConstants.TBL_INTEGRATION,
                                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                item,
                                                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        IntegrationConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                LOGGER.info(String.format("updated ServiceNow integration Configuration : %s : item: %s", item.getLong(ID), IntegrationConfigStore.getStore().getItem(item.getLong(ID))));

                                                                promise.complete();
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(asyncResult.cause().getMessage());

                                                                promise.fail(asyncResult.cause());
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to update ServiceNow integration Configuration : %s because of : %s ", item.getLong(ID), result.cause()));

                                                        promise.fail(result.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        promise.fail(asyncResponse.cause());
                                    }

                                });
                            }
                            else
                            {
                                promise.fail(response.cause());
                            }
                        });
            }
            else
            {
                promise.complete();
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> executeIntegrationProfilePatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = IntegrationProfileConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var valid = false;

                if (!item.containsKey(IntegrationProfile.INTEGRATION_CATEGORY))
                {
                    if (Objects.equals(IntegrationConstants.IntegrationId.MICROSOFT_TEAMS.getName(), item.getLong(IntegrationProfile.INTEGRATION)))
                    {
                        item.put(IntegrationProfile.INTEGRATION_CATEGORY, IntegrationConstants.IntegrationCategory.NOTIFICATION.getName());
                    }
                    else
                    {
                        item.put(IntegrationProfile.INTEGRATION_CATEGORY, IntegrationConstants.IntegrationCategory.INCIDENT.getName());
                    }

                    valid = true;
                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(TBL_INTEGRATION_PROFILE, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)), item,
                            DEFAULT_USER, MOTADATA_SYSTEM, result ->
                            {
                                if (result.succeeded())
                                {
                                    IntegrationProfileConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            LOGGER.info(String.format("updated Integration Profile : %s : item: %s", result.result(), IntegrationProfileConfigStore.getStore().getItem(item.getLong(ID))));

                                            future.complete();
                                        }
                                        else
                                        {
                                            future.fail(response.cause());
                                        }

                                    });
                                }
                                else
                                {
                                    future.fail(result.cause());
                                }
                            });
                }

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Integration profile updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to update Integration profile : " + result.cause());

                    promise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
