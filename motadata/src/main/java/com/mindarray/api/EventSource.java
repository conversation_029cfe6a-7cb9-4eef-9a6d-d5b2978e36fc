/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.LogParser.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogEngineConstants.ASSIGNED_LOG_PARSERS;
import static com.mindarray.log.LogEngineConstants.UNASSIGNED_LOG_PARSERS;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class EventSource extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(EventSource.class, GlobalConstants.MOTADATA_API, "Event Source API");

    public EventSource()
    {
        super("event-sources", EventSourceConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router, Set.of(REQUEST_DELETE));

        router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

        router.put("/" + endpoint + "/:id/assign").handler(this::assign);

        router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        this.beforeGet().compose(handler ->

                Future.<JsonObject>future(promise ->
                {
                    var item = this.configStore.getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                    if (item != null && !item.isEmpty())
                    {
                        var parsers = LogParserConfigStore.getStore().getItemsByMultiValueField(LOG_PARSER_ENTITIES, item.getString(EVENT_SOURCE));

                        if (!parsers.isEmpty())
                        {
                            item.put(ASSIGNED_LOG_PARSERS, parsers.stream().map(parser -> new JsonObject().put(ID, ((JsonObject) parser).getLong(ID))
                                            .put(LOG_PARSER_NAME, ((JsonObject) parser).getString(LOG_PARSER_NAME))
                                            .put(LOG_PARSER_SOURCE_TYPE, ((JsonObject) parser).getString(LOG_PARSER_SOURCE_TYPE)))
                                    .collect(Collectors.toList()));
                        }

                        promise.complete(CommonUtil.removeSensitiveFields(item, true));
                    }
                    else
                    {
                        promise.complete(new JsonObject());
                    }
                }).compose(entity -> this.afterGet(entity, routingContext)));
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            this.beforeGetAll(routingContext).compose(handler ->

                    Future.<JsonArray>future(promise ->

                            this.configStore.getReferenceCountsByItem().onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    this.getEntityCountPreHook(result.result()).onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            var items = this.configStore.getItemsByMultiValueField(EVENT_TYPE, EVENT_LOG);

                                            var entities = new JsonArray();

                                            for (var index = 0; index < items.size(); index++)
                                            {
                                                var item = CommonUtil.removeSensitiveFields(items.getJsonObject(index), true);

                                                if (DBConstants.ENTITY_TYPE_USER.equalsIgnoreCase(item.getString(DBConstants.FIELD_TYPE)) || (item.getJsonArray(LogEngineConstants.EVENT_CATEGORY) != null && !item.getJsonArray(LogEngineConstants.EVENT_CATEGORY).isEmpty()))
                                                {
                                                    if (response.result() != null && response.result().containsKey(CommonUtil.getString(item.getLong(ID))))
                                                    {
                                                        item.put(ENTITY_PROPERTY_COUNT, response.result().getInteger(CommonUtil.getString(item.getLong(ID))));
                                                    }

                                                    entities.add(item);
                                                }
                                            }

                                            promise.complete(entities);
                                        }
                                        else
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, response.cause().getMessage()))
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(response.cause().getStackTrace())));

                                            promise.fail(response.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                    promise.fail(result.cause());
                                }
                            })).compose(entities -> this.afterGetAll(entities, routingContext)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var source = routingContext.body().asJsonObject().getString(EVENT_SOURCE);

        var item = this.configStore.getItemByValue(EVENT_SOURCE, source);

        if (item != null && item.getJsonArray(EVENT_TYPE).contains(EVENT_LOG))
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                    .put(RESPONSE_CODE, SC_BAD_REQUEST)
                    .put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, source)));

            promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, source));
        }
        else
        {
            promise.complete(routingContext.body().asJsonObject().put(EVENT_TYPE, new JsonArray().add(EVENT_LOG))
                    .put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(ASSIGNED_LOG_PARSERS)));
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                routingContext.body().asJsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ASSIGN_LOG_PARSER.name())
                        .put(AIOpsObject.OBJECT_IP, routingContext.body().asJsonObject().getString(EVENT_SOURCE)));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_SOURCE.name()));

        return super.afterCreate(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        routingContext.body().asJsonObject().put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(ASSIGNED_LOG_PARSERS));

        return super.beforeUpdate(routingContext);
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                routingContext.body().asJsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ASSIGN_LOG_PARSER)
                        .put(AIOpsObject.OBJECT_IP, routingContext.body().asJsonObject().getString(EVENT_SOURCE)));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_SOURCE.name()));

        return super.afterUpdate(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var items = EventSourceConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                response.put(CommonUtil.getString(item.getLong(ID)), LogParserConfigStore.getStore().getItemsByMultiValueField(LOG_PARSER_ENTITIES, item.getString(EVENT_SOURCE)).size());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var source = EventSourceConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var parsers = LogParserConfigStore.getStore().getItemsByMultiValueField(LOG_PARSER_ENTITIES, source.getString(EVENT_SOURCE));

            if (!parsers.isEmpty())
            {
                response.put(ASSIGNED_LOG_PARSERS, parsers.stream().map(parser -> new JsonObject().put(ID, ((JsonObject) parser).getLong(ID))
                                .put(LOG_PARSER_NAME, ((JsonObject) parser).getString(LOG_PARSER_NAME))
                                .put(LOG_PARSER_SOURCE_TYPE, ((JsonObject) parser).getString(LOG_PARSER_SOURCE_TYPE)))
                        .collect(Collectors.toList()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var item = this.configStore.getItem(id);

                if (item != null)
                {
                    Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                            item.put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ASSIGN_LOG_PARSER)
                                    .put(AIOpsObject.OBJECT_IP, item.getString(EVENT_SOURCE))
                                    .put(ASSIGNED_LOG_PARSERS, routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS)));

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ID, id));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var item = this.configStore.getItem(id);

                if (item != null)
                {
                    Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                            item.put(AIOpsObject.OBJECT_IP, item.getString(EVENT_SOURCE))
                                    .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UNASSIGN_LOG_PARSER)
                                    .put(UNASSIGNED_LOG_PARSERS, routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS)));

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ID, id));
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
