/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  25-Mar-2025		Chopra Deven 		MOTADATA-5299: Modified forecast policy inspection by updating request and response handling
 */

package com.mindarray.policy;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.MetricPolicyCacheStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.MetricPolicy.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * AIOpsMetricPolicyInspector is responsible for evaluating AI operations metrics against defined policies.
 * <p>
 * This class handles specialized policy types for advanced analytics:
 * 1. Anomaly Detection - Identifies abnormal patterns in metric data
 * 2. Forecasting - Predicts future metric values and evaluates against thresholds
 * 3. Baseline - Compares current metrics against established baselines
 * <p>
 * The inspector runs as a Vert.x verticle and processes events from AI operations
 * components through the event bus.
 */
public class AIOpsMetricPolicyInspector extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(AIOpsMetricPolicyInspector.class, GlobalConstants.MOTADATA_POLICY, "AIOps Metric Policy Inspector");
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getPolicyCleanupTimerSeconds();
    private final Map<Long, JsonObject> policies = new HashMap<>();

    private final Map<String, JsonObject> contexts = new HashMap<>();

    private final Map<String, JsonObject> triggeredPolicies = new HashMap<>();

    private final JsonObject columns = new JsonObject();
    private final StringBuilder builder = new StringBuilder(0);
    private EventEngine eventEngine;
    private Set<String> mappers;

    /**
     * Initializes the AIOpsMetricPolicyInspector verticle.
     * <p>
     * This method:
     * 1. Loads AI operations metric policies from the configuration store
     * 2. Sets up event bus handlers for policy management and queries
     * 3. Initializes periodic timers for policy cleanup
     * 4. Registers with the event engine for event processing
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

            var items = MetricPolicyConfigStore.getStore().getItems();

            try
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if (AIOPS_POLICY_TYPES.contains(policy.getString(POLICY_TYPE)) && (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)))
                    {
                        policies.put(policy.getLong(ID), policy);
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
                contexts.forEach((key, value) ->
                {
                    if (value.containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS))
                    {
                        value.put(POLICY_AUTO_CLEAR_TIMER_SECONDS, value.getInteger(POLICY_AUTO_CLEAR_TIMER_SECONDS) - INTERVAL_SECONDS);

                        if (value.getInteger(POLICY_AUTO_CLEAR_TIMER_SECONDS) <= 0)
                        {
                            PolicyEngineConstants.processPolicyFlap(value.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(SEVERITY, Severity.CLEAR.name()).put(VALUE, "Auto Clear"), policies.get(value.getLong(ID)), true, triggeredPolicies, contexts, LOGGER, mappers, builder);
                        }
                    }

                    if (value.containsKey(POLICY_ACTIONS) && !value.getJsonObject(POLICY_ACTIONS).isEmpty()
                            && value.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                            && !value.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty())
                    {
                        var context = value.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName());

                        for (var severity : Severity.values())
                        {
                            if (context.containsKey(severity.name()))
                            {
                                context.getJsonObject(severity.name()).put(PolicyEngineConstants.TIMER_SECONDS, context.getJsonObject(severity.name()).getInteger(PolicyEngineConstants.TIMER_SECONDS) - INTERVAL_SECONDS);
                            }
                        }
                    }
                }));

        vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLICY_CLEAR, message ->
        {
            var event = message.body();

            if (!event.isEmpty() && policies.containsKey(event.getLong(POLICY_ID)))
            {
                var key = event.containsKey(INSTANCE) ? event.getLong(ENTITY_ID) + SEPARATOR + policies.get(event.getLong(POLICY_ID)).getString(POLICY_TYPE) + SEPARATOR + event.getString(METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getLong(ENTITY_ID) + SEPARATOR + policies.get(event.getLong(POLICY_ID)).getString(POLICY_TYPE) + SEPARATOR + event.getString(METRIC);

                if (triggeredPolicies.containsKey(key))
                {
                    PolicyEngineConstants.processPolicyFlap(new JsonObject().mergeIn(triggeredPolicies.get(key)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(SEVERITY, Severity.CLEAR.name()).put(VALUE, "Manual Clear"), policies.get(event.getLong(POLICY_ID)), false, triggeredPolicies, contexts, LOGGER, mappers, builder);
                }
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
            {
                case ADD_POLICY, UPDATE_POLICY ->
                {
                    var policy = MetricPolicyConfigStore.getStore().getItem(event.getLong(ID));

                    if (policy != null && AIOPS_POLICY_TYPES.contains(policy.getString(POLICY_TYPE)))
                    {
                        policies.put(event.getLong(ID), MetricPolicyConfigStore.getStore().getItem(event.getLong(ID)));
                    }
                }

                case DELETE_POLICY ->
                {
                    policies.remove(event.getLong(ID));

                    triggeredPolicies.values().removeIf(item -> item.getLong(ID).equals(event.getLong(ID)));

                    contexts.values().removeIf(item -> item.getLong(ID).equals(event.getLong(ID)));
                }

                case DELETE_OBJECT ->
                {

                    if (!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                    {
                        triggeredPolicies.entrySet().removeIf(entrySet -> CommonUtil.getLong(entrySet.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                        contexts.entrySet().removeIf(entrySet -> CommonUtil.getLong(entrySet.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                        MetricPolicyCacheStore.getStore().deleteItem(event.getLong(ID));
                    }
                }

                default ->
                {
                    // do nothing
                }

            }

        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::inspect).start(vertx, promise);
    }

    /**
     * Processes incoming AI operations events and routes them to specific inspection methods.
     * This method determines the policy type and delegates to the appropriate specialized inspector.
     *
     * @param event The AI operations event to inspect, containing metric values and metadata
     */
    private void inspect(JsonObject event)
    {
        try
        {

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("Event received for policy inspection::" + event);
            }

            var result = new JsonObject();

            var policy = policies.get(event.getLong(POLICY_ID));

            var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(event.getInteger(AIOpsObject.OBJECT_ID)));

            var metricPlugin = new JsonObject();

            for (var metric : MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)))
            {
                if (metric.containsKey(Metric.METRIC_CONTEXT) && metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PLUGIN_ID).equals(event.getValue(PLUGIN_ID)))
                {
                    metricPlugin.mergeIn(metric);

                    break;
                }
            }

            if (!metricPlugin.isEmpty())
            {
                if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.ANOMALY.getName()))
                {
                    inspectAnomaly(event, policy, result);
                }
                else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()))
                {
                    inspectForecast(event, policy, result);
                }
                else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()) || policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
                {
                    inspect(event, policy, result);
                }

                if (!result.isEmpty())
                {

                    result.put(INSTANCE, event.getValue(INSTANCE, null)).put(Metric.METRIC_TYPE, metricPlugin.getString(Metric.METRIC_TYPE)).put(EVENT_TIMESTAMP, event.containsKey(EventBusConstants.EVENT_TIMESTAMP) ? event.getLong(EventBusConstants.EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds()).put(AIOpsObject.OBJECT_CATEGORY, metricPlugin.containsKey(Metric.METRIC_CATEGORY) ? metricPlugin.getString(Metric.METRIC_CATEGORY) : object.getString(AIOpsObject.OBJECT_CATEGORY))
                            .put(GlobalConstants.PLUGIN_ID, event.getInteger(GlobalConstants.PLUGIN_ID)).put(ENTITY_ID, object.getLong(ID))
                            .put(POLICY_KEY, object.getLong(ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !event.getString(INSTANCE).isEmpty() ? policy.getString(POLICY_TYPE) + SEPARATOR + event.getString(GlobalConstants.METRIC) + SEPARATOR + event.getValue(INSTANCE) : policy.getString(POLICY_TYPE) + SEPARATOR + event.getString(GlobalConstants.METRIC)))
                            .put(METRIC, event.getString(METRIC)).put(POLICY_TYPE, policy.getString(POLICY_TYPE)).put(ID, policy.getLong(ID));

                    PolicyEngineConstants.processPolicyFlap(result, policy, false, triggeredPolicies, contexts, LOGGER, mappers, builder);
                }

                if (MotadataConfigUtil.devMode())
                {
                    vertx.eventBus().publish(EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST, result);
                }
            }
            else
            {
                LOGGER.warn("Failed to qualify metric, policy inspection failed for " + object.getString(AIOpsObject.OBJECT_NAME));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Evaluates anomaly detection results against policy thresholds.
     * This method calculates the percentage of anomalous points and determines
     * if a policy should be triggered based on severity thresholds.
     *
     * @param event  The anomaly detection event containing results
     * @param policy The policy configuration with severity thresholds
     * @param result The result object to populate with evaluation results
     */
    private void inspectAnomaly(JsonObject event, JsonObject policy, JsonObject result)
    {
        try
        {
            var rows = event.getJsonArray(RESULT);// anomaly result

            var violations = 0;

            var value = 0F;

            var key = (event.containsKey(INSTANCE) && event.getValue(INSTANCE) != null && !event.getString(INSTANCE).isEmpty() ? event.getInteger(AIOpsObject.OBJECT_ID) + "###" + event.getValue(INSTANCE) : event.getInteger(AIOpsObject.OBJECT_ID)) + CARET_SEPARATOR + event.getString(METRIC) + CARET_SEPARATOR + DatastoreConstants.AggregationType.AVG.getName() + CARET_SEPARATOR + "anomaly";

            //results checking whether any anomaly points detected or not..
            for (var index = 0; index < rows.size(); index++)
            {
                var row = rows.getJsonObject(index);

                if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), "1", row.getValue(key)))
                {
                    violations++;
                }
            }


            if (violations > 0)
            {
                //violated points > 0 then calculating violation percentage
                value = CommonUtil.getFloat((violations * 100) / rows.size());

                var conditions = policy.getJsonObject(POLICY_CONTEXT).getJsonObject(POLICY_SEVERITY);

                if (conditions != null)
                {
                    for (var index = 0; index < POLICY_SEVERITIES.size(); index++)
                    {
                        var severity = POLICY_SEVERITIES.getString(index);

                        var condition = conditions.getJsonObject(severity, null);

                        if (condition != null && PolicyEngineConstants.evaluateCondition(Boolean.TRUE, condition.getString(POLICY_CONDITION), condition.getValue(POLICY_THRESHOLD), value))
                        {
                            result.put(VALUE, value);

                            result.put(SEVERITY, Severity.valueOf(severity));

                            result.put(POLICY_THRESHOLD, violations);

                            break;
                        }
                    }
                }
            }

            if (result.isEmpty())
            {
                result.put(VALUE, value).put(SEVERITY, Severity.CLEAR.name()).put(POLICY_THRESHOLD, violations);
            }

        /*    DBConstants.write(new JsonObject()
                    .put(POLICY_EVENT_TRIGGER_ID,policy.getLong(ID)+"_"+ch)
                    .put(POLICY_EVENT_TRIGGER_VALUE, new JsonObject().put(RESULT,rows).encode())
                    .put(POLICY_EVENT_TRIGGER_POLICY_ID, policy.getLong(ID)).put(GlobalConstants.PLUGIN_ID, DBConstants.PluginId.POLICY_RESULT.getName())
                    .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                    .put(DBConstants.EVENT_DB_TYPE, DBConstants.EventDBType.POLICY_RESULT.ordinal()),VisualizationConstants.VisualizationDataSource.POLICY_RESULT.getName());
*/
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    /**
     * Evaluates forecast results against policy thresholds.
     * This method checks if forecasted metric values will breach thresholds in the future
     * and triggers policies with appropriate severity levels and time-to-breach information.
     *
     * @param event  The forecast event containing predicted metric values
     * @param policy The policy configuration with severity thresholds
     * @param result The result object to populate with evaluation results
     */
    private void inspectForecast(JsonObject event, JsonObject policy, JsonObject result)
    {
        try
        {
            var records = event.getJsonArray(RESULT);  // forecast result

            var context = policy.getJsonObject(POLICY_CONTEXT);

            var threshold = EMPTY_VALUE;

            if (context != null)
            {
                var conditions = context.getJsonObject(POLICY_SEVERITY);

                if (conditions != null)
                {
                    var trigger = true;

                    for (var index = 0; index < POLICY_SEVERITIES.size(); index++)
                    {
                        var severity = POLICY_SEVERITIES.getString(index);

                        var condition = conditions.getJsonObject(severity, null);

                        if (condition != null)
                        {
                            threshold = condition.getString(POLICY_THRESHOLD);

                            //results checking whether any forecasted value breach the threshold or not..
                            for (var i = 0; i < records.size(); i++)
                            {
                                var record = records.getJsonObject(i);

                                var value = record.getValue(event.getString(METRIC));

                                if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, condition.getString(POLICY_CONDITION), threshold, value))
                                {
                                    if (record.getLong(TIME_STAMP) <= System.currentTimeMillis()) // if threshold already breached than we will not trigger policy
                                    {
                                        trigger = false;
                                    }
                                    else
                                    {
                                        result.put(VALUE, DateTimeUtil.millisToDuration(record.getLong(TIME_STAMP) - System.currentTimeMillis()));

                                        result.put(SEVERITY, Severity.valueOf(severity));

                                        result.put(POLICY_THRESHOLD, condition.getString(POLICY_THRESHOLD));
                                    }

                                    break;
                                }
                            }
                        }
                    }

                    if (trigger && result.isEmpty())
                    {
                        result.put(VALUE, EMPTY_VALUE).put(SEVERITY, Severity.CLEAR.name()).put(POLICY_THRESHOLD, threshold);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Evaluates baseline metrics against policy thresholds.
     * This method compares current metric values against established baselines
     * and triggers policies based on relative or absolute deviations.
     *
     * @param event  The baseline event containing current and baseline metric values
     * @param policy The policy configuration with severity thresholds
     * @param result The result object to populate with evaluation results
     */
    private void inspect(JsonObject event, JsonObject policy, JsonObject result)
    {
        try
        {
            var metricValue = event.getValue(event.getString(METRIC) + CARET_SEPARATOR + DatastoreConstants.AggregationType.AVG.getName() + CARET_SEPARATOR + "baseline");

            var value = event.getValue(event.getString(METRIC) + CARET_SEPARATOR + DatastoreConstants.AggregationType.AVG.getName());

            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (context != null)
            {
                var conditions = context.getJsonObject(POLICY_SEVERITY);

                if (conditions != null)
                {
                    for (var index = 0; index < POLICY_SEVERITIES.size(); index++)
                    {
                        var severity = POLICY_SEVERITIES.getString(index);

                        var condition = conditions.getJsonObject(severity, null);

                        if (condition != null)
                        {
                            var threshold = calculateThreshold(condition.getString(POLICY_CONDITION), conditions.getJsonObject(severity, null), metricValue, context.getString(THRESHOLD_TYPE, ThresholdType.RELATIVE.getName()));

                            if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, condition.getString(POLICY_CONDITION), threshold, value))
                            {
                                result.put(VALUE, value);

                                result.put(SEVERITY, Severity.valueOf(severity));

                                result.put(POLICY_THRESHOLD, threshold);

                                break;
                            }
                        }
                    }

                    if (result.isEmpty())
                    {
                        result.put(VALUE, value).put(SEVERITY, Severity.CLEAR.name()).put(POLICY_THRESHOLD, metricValue);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    /**
     * Calculates threshold values based on policy configuration.
     * Supports both absolute and relative (percentage-based) threshold calculations
     * for both "above" and "below" conditions.
     *
     * @param policyCondition The condition type ("above" or "below")
     * @param context         The policy context containing threshold configuration
     * @param value           The baseline value to calculate threshold from
     * @param thresholdType   The type of threshold calculation ("absolute" or "relative")
     * @return The calculated threshold value
     */
    private Object calculateThreshold(String policyCondition, JsonObject context, Object value, String thresholdType)
    {
        Object thresholdValue;

        if (policyCondition.equals(Operator.ABOVE.getName()))
        {
            thresholdValue = thresholdType.equalsIgnoreCase(ThresholdType.ABSOLUTE.getName()) ? CommonUtil.getDouble(value) + CommonUtil.getDouble(context.getValue(POLICY_THRESHOLD)) : CommonUtil.getDouble(value) + (CommonUtil.getDouble(value) * CommonUtil.getDouble(context.getValue(POLICY_THRESHOLD)) / 100);
        }
        else
        {
            thresholdValue = thresholdType.equalsIgnoreCase(ThresholdType.ABSOLUTE.getName()) ? CommonUtil.getDouble(value) - CommonUtil.getDouble(context.getValue(POLICY_THRESHOLD)) : CommonUtil.getDouble(value) - (CommonUtil.getDouble(value) * CommonUtil.getDouble(context.getValue(POLICY_THRESHOLD)) / 100);
        }

        return Math.round(CommonUtil.getDouble(thresholdValue));
    }

    /**
     * Stops the AIOpsMetricPolicyInspector verticle.
     * This method cleans up resources and stops the event engine
     * when the verticle is undeployed.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
