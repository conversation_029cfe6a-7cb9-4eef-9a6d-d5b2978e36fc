/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.GeoDBUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.WebClientUtil;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.auth.authentication.UsernamePasswordCredentials;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;
import java.util.zip.ZipInputStream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_PROXY_SERVICE_REQUEST;

/**
 * The GeoDBSyncJob class is responsible for synchronizing geographical database files in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs regular updates of the geographical
 * database by downloading the latest version from a remote server. The geographical database is used
 * for IP-to-location mapping and other geographical features in the platform.
 * <p>
 * The job performs the following tasks:
 * <ol>
 *   <li>Downloads the latest geographical database zip file from a remote server</li>
 *   <li>Writes the downloaded file to a temporary location</li>
 *   <li>Unzips the file to the appropriate directory</li>
 *   <li>Restarts the Geo DB stores to use the new data</li>
 *   <li>Cleans up temporary files</li>
 * </ol>
 * <p>
 * The job is scheduled to run weekly (every Sunday at 2:00 AM) to ensure the geographical
 * database is kept up-to-date.
 */
public class GeoDBSyncJob implements Job
{
    /**
     * CRON expression for scheduling this job to run every Sunday at 2:00 AM
     */
    public static final String GEO_DB_SYNC_JOB_CRON_EXPRESSION = "0 0 2 ? * SUN *";   // At second :00 of minute :00 of :02 hour every Sunday

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(GeoDBSyncJob.class, GlobalConstants.MOTADATA_JOB, "GeoDB Sync Job");

    /**
     * URI of the remote geographical database zip file
     */
    private static final String GEO_DB_URI = "https://geodb.motadata.ai/geo-location.zip";

    /**
     * Username for authenticating with the remote server
     */
    private static final String URI_USERNAME = "geodb_user";

    /**
     * Password for authenticating with the remote server
     */
    private static final String URI_PASSWORD = "Trace#12Org$89Mind%^T";

    /**
     * Timeout in milliseconds for the download request
     */
    private static final long TIMEOUT_MILLIS = MotadataConfigUtil.getGeoDBRequestTimeoutSeconds() * 1000L;

    /**
     * Executes the geographical database synchronization job.
     * <p>
     * This method initiates the download of the latest geographical database from a remote server.
     * It supports two download methods:
     * <ol>
     *   <li>Using a proxy service if proxy is enabled in the system configuration</li>
     *   <li>Using a direct web client connection if no proxy is configured</li>
     * </ol>
     * <p>
     * After the download is complete, the method processes the downloaded data by calling
     * the {@link #process(Buffer, Promise)} method.
     *
     * @param jobExecutionContext the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext)
    {
        try
        {
            LOGGER.debug("Starting Geo DB Sync job");

            // Check if proxy is enabled in the system configuration
            if (WebClientUtil.proxyEnabled())
            {
                LOGGER.info("Using Proxy Webclient to sync...");

                // Use the proxy service to download the file
                // This sends a request to the proxy service via the event bus
                Bootstrap.vertx().eventBus().<JsonObject>request(
                        EVENT_PROXY_SERVICE_REQUEST,
                        new JsonObject()
                                .put(URI, GEO_DB_URI)
                                .put(USERNAME, URI_USERNAME)
                                .put(PASSWORD, URI_PASSWORD),
                        new DeliveryOptions().setSendTimeout(TIMEOUT_MILLIS),
                        reply ->
                        {
                            if (reply.succeeded())
                            {
                                // Process the downloaded data in a blocking context
                                Bootstrap.vertx().executeBlocking(future ->
                                                process(reply.result().body().getBuffer(RESULT), future))
                                        .onComplete(result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                LOGGER.debug("Geo DB Sync job completed");
                                            }
                                            else
                                            {
                                                LOGGER.error(result.cause());
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.error(reply.cause());
                            }
                        });
            }
            else
            {
                // Use direct web client connection if no proxy is configured
                Bootstrap.vertx().executeBlocking(future ->
                {
                    LOGGER.info("Using Webclient to sync...");

                    // Create a web client request to download the file
                    WebClientUtil.getWebClient()
                            .getAbs(GEO_DB_URI)
                            .authentication(new UsernamePasswordCredentials(URI_USERNAME, URI_PASSWORD))
                            .timeout(TIMEOUT_MILLIS)
                            .send(result ->
                            {
                                if (result.succeeded())
                                {
                                    // Process the downloaded data
                                    process(result.result().body(), future);
                                }
                                else
                                {
                                    future.fail(result.cause());
                                }
                            });
                }).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.debug("Geo DB Sync job completed");
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });
            }


        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes the downloaded geographical database file.
     * <p>
     * This method performs the following operations:
     * <ol>
     *   <li>Writes the downloaded buffer to a temporary file with a unique name</li>
     *   <li>Unzips the file to the appropriate directory</li>
     *   <li>Restarts the Geo DB stores to use the new data</li>
     * </ol>
     * <p>
     * The method completes the provided future when processing is complete, or fails it
     * if an error occurs during processing.
     *
     * @param buffer the downloaded file content as a buffer
     * @param future the promise to complete when processing is done
     */
    private void process(Buffer buffer, Promise<Object> future)
    {
        // Generate a unique filename for the temporary download file
        var fileName = CURRENT_DIR + PATH_SEPARATOR + DOWNLOADS + PATH_SEPARATOR + UUID.randomUUID();

        // Write the downloaded buffer to the temporary file
        Bootstrap.vertx().fileSystem().writeFile(fileName, buffer, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    LOGGER.info(fileName + " downloaded successfully.");

                    // Unzip the downloaded file to the geographical database directory
                    unzip(fileName, CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR);

                    LOGGER.info("Restarting Geo DB Stores...");

                    // Close existing Geo DB stores
                    GeoDBUtil.close();

                    // Initialize Geo DB stores with the new data
                    GeoDBUtil.init(Bootstrap.vertx());

                    // Complete the future to indicate successful processing
                    future.complete();
                }
                catch (Exception exception)
                {
                    // Fail the future if an error occurs during processing
                    future.fail(exception);
                }
            }
            else
            {
                // Fail the future if writing the file fails
                future.fail(result.cause());
            }
        });
    }

    /**
     * Unzips a file to a specified directory.
     * <p>
     * This method extracts all entries from a zip file to the specified output directory.
     * It handles both file and directory entries appropriately:
     * <ul>
     *   <li>For directory entries, it creates the corresponding directories</li>
     *   <li>For file entries, it writes the file content to the output directory</li>
     * </ul>
     * <p>
     * After extraction is complete, the method deletes the original zip file to clean up
     * temporary storage.
     *
     * @param inputPath the path to the zip file to extract
     * @param outputDir the directory where the contents should be extracted
     */
    private void unzip(String inputPath, String outputDir)
    {
        try (var inputStream = new ZipInputStream(new FileInputStream(inputPath)))
        {
            // Get the first entry in the zip file
            var entry = inputStream.getNextEntry();

            // Process each entry in the zip file
            while (entry != null)
            {
                // Determine the destination path for this entry
                var destination = outputDir + entry.getName();

                if (entry.isDirectory())
                {
                    // For directory entries, create the directory structure
                    new File(destination).mkdirs();
                }
                else
                {
                    // For file entries, write the file content
                    try (var outputStream = new BufferedOutputStream(new FileOutputStream(destination)))
                    {
                        // Use a buffer for efficient file writing
                        var bytes = new byte[10240];
                        var length = 0;

                        // Read and write the file content in chunks
                        while ((length = inputStream.read(bytes)) > 0)
                        {
                            outputStream.write(bytes, 0, length);
                        }
                    }
                }

                // Close the current entry and move to the next one
                inputStream.closeEntry();
                entry = inputStream.getNextEntry();
            }

            LOGGER.info(inputPath + " unzipped to " + outputDir);
        }
        catch (IOException exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            try
            {
                // Clean up by deleting the original zip file
                Files.delete(Path.of(inputPath));
                LOGGER.debug(inputPath + " deleted.");
            }
            catch (IOException ignored)
            {
                // Ignore exceptions during cleanup
                // This ensures that cleanup failures don't affect the main operation
            }
        }
    }

}
