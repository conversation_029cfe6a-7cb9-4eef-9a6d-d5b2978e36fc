[{"id": 10000000000949, "_type": "0", "visualization.name": "Query", "visualization.description": "Windows DNS Query", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.queries.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.sent.responses.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dns.received.queries.per.sec"}, {"type": "metric", "data.point": "dns.received.tcp.queries"}, {"type": "metric", "data.point": "dns.received.tcp.queries.per.sec"}, {"type": "metric", "data.point": "dns.received.udp.queries"}, {"type": "metric", "data.point": "dns.received.udp.queries.per.sec"}, {"type": "metric", "data.point": "dns.sent.responses.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Query", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Received", "value": "dns.received.queries.per.sec.last"}, {"label": "<PERSON><PERSON>", "value": "dns.sent.responses.per.sec.last"}]}}}}, {"id": 10000000000950, "_type": "0", "visualization.name": "Recursive Queries", "visualization.description": "Windows DNS Recursive Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.sent.recursive.timeouts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.recursive.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.recursive.query.failures", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dns.recursive.queries.per.sec"}, {"type": "metric", "data.point": "dns.recursive.query.failures.per.sec"}, {"type": "metric", "data.point": "dns.recursive.timeouts.per.sec"}, {"type": "metric", "data.point": "dns.sent.recursive.timeouts"}, {"type": "metric", "data.point": "dns.recursive.queries"}, {"type": "metric", "data.point": "dns.recursive.query.failures"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Recursive Queries", "style": {"font.size": "medium"}, "data.points": [{"label": "Recursive Timeouts", "value": "dns.sent.recursive.timeouts.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Queries", "value": "dns.recursive.queries.last"}, {"label": "Failures", "value": "dns.recursive.query.failures.last"}]}}}}, {"id": 10000000000951, "_type": "0", "visualization.name": "Zone Transfer", "visualization.description": "Windows DNS Zone Transfer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.zone.transfer.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.succeeded.zone.transfers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.zone.transfer.failures", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dns.sent.zone.transfer.soa.requests"}, {"type": "metric", "data.point": "dns.received.axfr.responses"}, {"type": "metric", "data.point": "dns.axfr.success.receives"}, {"type": "metric", "data.point": "dns.axfr.success.sends"}, {"type": "metric", "data.point": "dns.ixfr.success.receives"}, {"type": "metric", "data.point": "dns.received.zone.transfer.requests"}, {"type": "metric", "data.point": "dns.succeeded.zone.transfers"}, {"type": "metric", "data.point": "dns.zone.transfer.failures"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "location-pin", "placement": "prefix"}}, "header": {"title": "Zone Transfer", "style": {"font.size": "medium"}, "data.points": [{"label": "Zone Transfer", "value": "dns.received.zone.transfer.requests.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Success", "value": "dns.succeeded.zone.transfers.last"}, {"label": "Failures", "value": "dns.zone.transfer.failures.last"}]}}}}, {"id": 10000000000952, "_type": "0", "visualization.name": "Dynamic Updates", "visualization.description": "Windows DNS Dynamic Updates", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.dynamic.update.timeouts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.dynamic.update.receives", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.dynamic.update.rejects", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dns.dynamic.update.noops"}, {"type": "metric", "data.point": "dns.dynamic.queued.updates"}, {"type": "metric", "data.point": "dns.dynamic.update.receives.per.sec"}, {"type": "metric", "data.point": "dns.dynamic.update.database.writes"}, {"type": "metric", "data.point": "dns.dynamic.update.database.writes.per.sec"}, {"type": "metric", "data.point": "dns.dynamic.update.timeouts"}, {"type": "metric", "data.point": "dns.dynamic.update.receives"}, {"type": "metric", "data.point": "dns.dynamic.update.rejects"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Dynamic Updates", "style": {"font.size": "medium"}, "data.points": [{"label": "Timeouts", "value": "dns.dynamic.update.timeouts.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Receives", "value": "dns.dynamic.update.receives.last"}, {"label": "Rejects", "value": "dns.dynamic.update.rejects.last"}]}}}}, {"id": 10000000000953, "_type": "0", "visualization.name": "Secure Updates", "visualization.description": "Windows DNS Secure Updates", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.dynamic.update.receives.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.secure.update.receives", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "dns.secure.update.failures", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "dns.secure.update.receives.per.sec"}, {"type": "metric", "data.point": "dns.dynamic.update.receives.per.sec"}, {"type": "metric", "data.point": "dns.secure.update.receives"}, {"type": "metric", "data.point": "dns.secure.update.failures"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "secure", "placement": "prefix"}}, "header": {"title": "Secure Updates", "style": {"font.size": "medium"}, "data.points": [{"label": "Updates", "value": "dns.dynamic.update.receives.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Success", "value": "dns.secure.update.receives.last"}, {"label": "Failure", "value": "dns.secure.update.failures.last"}]}}}}, {"_type": "0", "id": 10000000000954, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000955, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000956, "_type": "0", "visualization.name": "Memory Details", "visualization.description": "Windows DNS Memory Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.database.node.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.nbstat.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.record.flow.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.caching.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000957, "_type": "0", "visualization.name": "Query", "visualization.description": "Windows DNS Query Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.queries.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.sent.responses.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000958, "_type": "0", "visualization.name": "Notification Details", "visualization.description": "Windows DNS Notification Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.sent.notifications", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000959, "_type": "0", "visualization.name": "Dynamic Update Statistics", "visualization.description": "Windows DNS Dynamic Update Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.dynamic.update.noops", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.dynamic.update.noops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000960, "_type": "0", "visualization.name": "Recursive Queries", "visualization.description": "Windows DNS Recursive Queries Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.recursive.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.recursive.query.failures", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.sent.recursive.timeouts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000961, "_type": "0", "visualization.name": "Zone Transfers", "visualization.description": "Windows DNS Zone Transfers", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.zone.transfer.failures", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.succeeded.zone.transfers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.zone.transfer.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000962, "_type": "0", "visualization.name": "Active Worker", "visualization.description": "Windows DNS TCP Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.tcp.message.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000963, "_type": "0", "visualization.name": "AXFR Statistics", "visualization.description": "Windows DNS AXFR Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.sent.axfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.axfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.axfr.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000964, "_type": "0", "visualization.name": "IXFR Statistics", "visualization.description": "Windows DNS IXFR Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.sent.ixfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.ixfr.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.ixfr.responses", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000965, "_type": "0", "visualization.name": "UDP Statistics", "visualization.description": "Windows DNS UDP Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.wins.lookups.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "dns.received.wins.reverse.lookups.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]