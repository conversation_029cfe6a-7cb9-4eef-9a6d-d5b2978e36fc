/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.agent;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.api.Agent;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_SUCCEEDED;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.agent.AgentConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.AIOpsObject.OBJECT_STATE;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.Metric.METRIC_CREDENTIAL_PROFILE;
import static com.mindarray.api.Metric.METRIC_PLUGIN;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.manager.MotadataAppManager.PATCH_ARTIFACT_FILE;
import static com.mindarray.nms.NMSConstants.SERVICES;
import static com.mindarray.util.CommonUtil.isNotNullOrEmpty;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(300 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "AGENT")
public class TestAgent
{
    public static final Map<Long, VertxTestContext> DISCOVERY_ITEMS = new HashMap<>();
    private static final Logger LOGGER = new Logger(TestAgent.class, GlobalConstants.MOTADATA_AGENT, "Agent Test");
    private static final String CONFIG_DIRECTORY = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR;
    private static final JsonObject metricPluginContext = new JsonObject();
    private static final Vertx VERTX = TestUtil.vertx();
    private static Process agentManagerProcess;
    private static String agentUUID;
    private static long agentDiscoveryId;
    private static String configurationValue;
    private static long discoveryPortAgentId;
    private static long discoveryPingAgentId;
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        assertDiscoveryConsumerSetup();

        TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

        changeAgentConfigs(AgentConstants.AGENT, "agent.deactivation.status", NO, Boolean.TRUE);

        changeConfigurationParams("AGENT");

        testContext.completeNow();
    }

    @AfterAll
    static void clean(VertxTestContext testContext)
    {
        if (agentManagerProcess != null && agentManagerProcess.isAlive())
        {
            killProcess(agentManagerProcess.toHandle());
        }

        changeAgentConfigs(AgentConstants.AGENT, "agent.deactivation.status", NO, Boolean.TRUE);

        LOGGER.info(String.format("Agent config store :: %s", AgentConfigStore.getStore().getItems()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    private static Future<Void> copyCurrentConfigToUploadDirectory()
    {
        var promise = Promise.<Void>promise();

        VERTX.fileSystem().delete(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "config.json", handler ->
        {
            VERTX.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "config.json", Buffer.buffer(Json.encodePrettily(readConfigs("agent.json"))));

            promise.complete();
        });

        return promise.future();
    }

    private static void assertDiscoveryConsumerSetup()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (eventContext.containsKey(EVENT_NAME) && eventContext.getString(EVENT_NAME).equalsIgnoreCase("discovery"))
                {
                    var context = eventContext.getJsonObject(EVENT_CONTEXT);

                    if (DISCOVERY_ITEMS.containsKey(context.getLong(ID)) && eventContext.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED))
                    {
                        LOGGER.info(String.format("Discovery run successfully %s", message.body().encode()));

                        DISCOVERY_ITEMS.get(context.getLong(ID)).verify(() ->
                        {
                            assertEquals(EVENT_STATE_COMPLETED, eventContext.getString(EVENT_STATE));

                            DISCOVERY_ITEMS.get(context.getLong(ID)).completeNow();

                            DISCOVERY_ITEMS.remove(context.getLong(ID));
                        });
                    }
                }
            }
        });
    }

    private static void killProcess(ProcessHandle process)
    {
        process.descendants().forEach(TestAgent::killProcess);

        process.destroy();
    }

    private static void assertProvisionTestResult(long discoveryId, VertxTestContext testContext)
    {
        try
        {
            var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

            LOGGER.info(String.format("Discovery context for provision %s", discovery.encode()));

            Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + discovery.getLong(ID), asyncResponse ->
            {
                try
                {
                    if (asyncResponse.failed() || asyncResponse.result().isEmpty())
                    {
                        testContext.failNow(String.format("failed to get discovery object %s", discovery.getString(Discovery.DISCOVERY_NAME)));
                    }
                    else
                    {
                        var futures = new ArrayList<Future<Void>>();

                        var probes = asyncResponse.result();

                        var errors = new StringBuilder(0);

                        if (discovery.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                        {
                            discovery.remove(ID);
                        }

                        for (var index = 0; index < probes.size(); index++)
                        {
                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            var objectId = NOT_AVAILABLE;

                            var probe = probes.getJsonObject(index);

                            var type = NMSConstants.Type.valueOfName(probe.getString(AIOpsObject.OBJECT_TYPE));

                            probe.mergeIn(discovery).put(SESSION_ID, TestUtil.getSessionId());

                            assertNotNull(type);

                            assertTrue(Arrays.asList(NMSConstants.Type.values()).contains(type));

                            if (type.toString().equals("PORT") || type.toString().equals("URL"))
                            {
                                var objects = ObjectConfigStore.getStore().flatItemsByMapValueField(type, AIOpsObject.OBJECT_IP, probe.getString(AIOpsObject.OBJECT_TARGET), AIOpsObject.OBJECT_CONTEXT, PORT, probe.getJsonObject(Discovery.DISCOVERY_CONTEXT).getInteger(PORT));

                                objectId = !objects.isEmpty() ? objects.getJsonObject(0).getInteger(AIOpsObject.OBJECT_ID) : objectId;
                            }
                            else
                            {
                                objectId = ObjectConfigStore.getStore().getObjectIdByIP(probe.getString(AIOpsObject.OBJECT_IP), type);

                                if (objectId == NOT_AVAILABLE)
                                {
                                    objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(probe.getString(AIOpsObject.OBJECT_NAME));
                                }
                            }

                            if (objectId != NOT_AVAILABLE)
                            {
                                errors.append("Object ").append(probe.getString(AIOpsObject.OBJECT_TARGET)).append(" is already provisioned").append(SEPARATOR);

                                future.fail(errors.toString());
                            }
                            else
                            {
                                LOGGER.trace(String.format("Request send for object provision: %s", probe.getString(AIOpsObject.OBJECT_IP)));

                                TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_PROVISION, probe.put(EVENT_REPLY, YES),
                                        new DeliveryOptions().setSendTimeout(300000L), reply ->
                                        {
                                            try
                                            {
                                                if (reply.failed())
                                                {
                                                    future.fail(reply.cause());
                                                }
                                                else
                                                {
                                                    var result = reply.result().body();

                                                    Assertions.assertNotNull(result);

                                                    Assertions.assertTrue(result.containsKey(STATUS));

                                                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                                    LOGGER.info(String.format("%s : %s object provisioned successfully", result.getString(AIOpsObject.OBJECT_IP), result.getString(AIOpsObject.OBJECT_NAME)));

                                                    future.complete();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                future.fail(exception);
                                            }

                                        });
                            }
                        }

                        Future.all(futures).onComplete(asyncResult ->
                        {

                            if (asyncResult.succeeded())
                            {
                                testContext.completeNow();
                            }
                            else
                            {
                                testContext.failNow(asyncResult.cause());
                            }

                        });

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);

            LOGGER.error(exception);
        }
    }

    private static JsonObject changeAgentConfigs(String agent, String key, String value, boolean dumpToFile)
    {
        var configs = readConfigs("agent.json");

        var agentConfigs = configs.getJsonObject(agent);

        if (agentConfigs != null)
        {
            agentConfigs.put(key, value);
        }

        if (dumpToFile)
        {
            VERTX.fileSystem().writeFileBlocking(CONFIG_DIRECTORY + "agent.json", Buffer.buffer(Json.encodePrettily(configs)));
        }

        return configs;
    }

    private static void changeConfigurationParams(String value)
    {
        var configs = readConfigs("motadata.json");

        configs.put("system.bootstrap.type", value);

        configs.put("manager.id", "MOTADATA_AGENT");

        VERTX.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json", Buffer.buffer(Json.encodePrettily(configs)));

    }

    private static JsonObject readConfigs(String fileName)
    {
        return new JsonObject().mergeIn(VERTX.fileSystem().readFileBlocking(CONFIG_DIRECTORY + fileName).toJsonObject());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentRegistration(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_UI, event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_REGISTRATION))
            {
                assertEquals(STATUS_UP, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                assertNotNull(event.getJsonObject(EVENT_CONTEXT).getString(AGENT_UUID));

                agentUUID = event.getJsonObject(EVENT_CONTEXT).getString(AGENT_UUID);

                agentDiscoveryId = event.getJsonObject(EVENT_CONTEXT).getLong(ID);

                assertFalse(ObjectConfigStore.getStore().getAgents().isEmpty());

                assertFalse(ObjectConfigStore.getStore().getItemsByAgentIds(new JsonArray().add(agentDiscoveryId)).isEmpty());

                messageConsumer.unregister(result -> testContext.completeNow());

            }
        });

        ProcessUtil.start("motadata-manager", List.of("./motadata-manager"), GlobalConstants.CURRENT_DIR).onComplete(result -> agentManagerProcess = result.result());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    @Timeout(value = 90, timeUnit = TimeUnit.SECONDS)
    void testStartAgentInfra(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var agents = new LinkedList<>(Arrays.asList("./motadata-manager", "./motadata-agent", "./motadata-metric-agent"));

        assertAgentProcessStatusTestResult(agents, Boolean.TRUE).onComplete(result ->
                testContext.verify(() ->
                {
                    assertTrue(agents.isEmpty());

                    testContext.completeNow();
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @Timeout(value = 65, timeUnit = TimeUnit.SECONDS)
    void testAgentManagerEventAcknowledgement(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_MANAGER_RESPONSE_PROCESSOR, event ->
        {
            if (event.getString(EVENT_TYPE) != null
                    && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_ACKNOWLEDGEMENT) && event.getString(SYSTEM_BOOTSTRAP_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                testContext.verify(() ->
                {
                    assertEquals(event.getString(AGENT_UUID), agentUUID);

                    messageConsumer.unregister(result -> testContext.completeNow());
                });

            }
        });

        // send heartbeat to agent manager
        publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(AGENT_UUID, agentUUID)
                .put(ID, agentDiscoveryId).put(EVENT_TYPE, EVENT_MOTADATA_MANAGER_HEARTBEAT));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    @Timeout(value = 65, timeUnit = TimeUnit.SECONDS)
    void testAgentEventAcknowledgement(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_AGENT, event ->
        {
            if (event.getString(EVENT_TYPE) != null
                    && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_ACKNOWLEDGEMENT))
            {
                testContext.verify(() ->
                {
                    assertEquals(event.getString(AGENT_UUID), agentUUID);

                    messageConsumer.unregister(result -> testContext.completeNow());
                });

            }
        });

        // send heartbeat to agent
        publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(AGENT_UUID, agentUUID)
                .put(ID, agentDiscoveryId).put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testAgentHeartBeat(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_HEARTBEAT))
            {
                testContext.verify(() ->
                {
                    assertFalse(event.getJsonObject(EVENT_CONTEXT).isEmpty());

                    assertFalse(event.getJsonObject(EVENT_CONTEXT).getJsonArray(RESULT).isEmpty());

                    var result = event.getJsonObject(EVENT_CONTEXT).getJsonArray(RESULT);

                    for (var index = 0; index < result.size(); index++)
                    {
                        var agent = result.getJsonObject(index);

                        if (agent.getLong(ID) == agentDiscoveryId)
                        {
                            messageConsumer.unregister(asyncResult -> testContext.completeNow());
                        }
                    }
                });
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_AGENT_HEARTBEAT + ".status.query", new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT + ".status.query"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testAgentProcessRediscoveryAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        assertFalse(object.isEmpty());

        var schedulers = SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB,
                NMSConstants.RediscoverJob.PROCESS.getName());

        assertFalse(schedulers.isEmpty());

        var scheduler = schedulers.getJsonObject(0);

        scheduler.put(NMSConstants.OBJECTS, new JsonArray().add(object.getLong(ID)));

        TestNMSUtil.testRediscover(testContext, scheduler, new JsonObject().put(NMSConstants.AUTO_PROVISION_STATUS, YES), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testAgentDeleteFileDirectoryObjects(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        assertFalse(object.isEmpty());

        var plugins = new JsonArray().add(NMSConstants.MetricPlugin.LINUX_FILE.getName()).add(NMSConstants.MetricPlugin.LINUX_DIR.getName())
                .add(NMSConstants.MetricPlugin.WINDOWS_DIR.getName()).add(NMSConstants.MetricPlugin.WINDOWS_FILE.getName());

        var metrics = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID))
                .stream().filter(item -> plugins.contains(item.getString(METRIC_PLUGIN)))
                .map(JsonObject::mapFrom).toList();

        assertFalse(metrics.isEmpty());

        var futures = new ArrayList<Future<Void>>();

        for (var metric : metrics)
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            metric.getJsonObject(Metric.METRIC_CONTEXT).put(NMSConstants.OBJECTS, new JsonArray());

            Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                    metric, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    handler -> MetricConfigStore.getStore().updateItem(metric.getLong(ID)).onComplete(result -> promise.complete()));
        }

        Future.join(futures).onComplete(result -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testAgentFileDirectoryRediscoveryAutoProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        assertFalse(object.isEmpty());

        var schedulers = SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB,
                NMSConstants.RediscoverJob.FILE_DIRECTORY.getName());

        assertFalse(schedulers.isEmpty());

        var scheduler = schedulers.getJsonObject(0);

        scheduler.put(NMSConstants.OBJECTS, new JsonArray().add(object.getLong(ID)));

        LOGGER.trace(String.format("After put Scheduler is :: %s", scheduler));

        TestNMSUtil.testRediscover(testContext, scheduler, new JsonObject().put(NMSConstants.AUTO_PROVISION_STATUS, YES), false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testAgentRediscoveredObjects(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

            LOGGER.info(String.format("Object is : %s", object.encode()));

            Assertions.assertNotNull(object);

            assertFalse(object.isEmpty());

            var plugins = new JsonArray().add(NMSConstants.MetricPlugin.LINUX_FILE.getName()).add(NMSConstants.MetricPlugin.LINUX_DIR.getName())
                    .add(NMSConstants.MetricPlugin.WINDOWS_DIR.getName()).add(NMSConstants.MetricPlugin.WINDOWS_FILE.getName());

            var metrics = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID))
                    .stream().filter(item -> plugins.contains(item.getString(METRIC_PLUGIN)))
                    .map(JsonObject::mapFrom).toList();

            LOGGER.info(String.format("metrics is : %s", metrics));

            assertFalse(metrics.isEmpty());

            var valid = false;

            for (var metric : metrics)
            {
                if (!metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).isEmpty())
                {
                    valid = true;
                }
            }

            if (valid)
            {
                testContext.completeNow();
            }
            else
            {
                testContext.failNow(new Exception("failed to auto provision rediscovered objects..."));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentConfigChange(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var configs = readConfigs("agent.json");

            if (configs.getJsonObject("log.agent") != null && configs.getJsonObject("log.agent").getString("multiline.log.status") != null)
            {
                configurationValue = (configs.getJsonObject("log.agent").getString("multiline.log.status").equals(YES)) ? NO : YES;
            }

            configs = changeAgentConfigs("log.agent", "multiline.log.status", configurationValue, Boolean.FALSE);

            var modifiedConfigs = configs;

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_CONFIGURATION_CHANGE))
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    assertAgentConfigTestResult("log.agent", "multiline.log.status", configurationValue);

                    messageConsumer.unregister(result ->
                            TestAPIUtil.put(AGENT_API_ENDPOINT + 1234L + "/configure", new JsonObject().put(AGENT_CONFIGS, modifiedConfigs.encode()),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));
                }
            });

            TestAPIUtil.put(AGENT_API_ENDPOINT + agentDiscoveryId + "/configure", new JsonObject().put(AGENT_RESTART_REQUIRED, YES).put(AGENT_CONFIGS, configs.encode()), handler ->
            {
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentConfigChangeBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var configs = readConfigs("agent.json");

            if (configs.getJsonObject("metric.agent") != null && configs.getJsonObject("metric.agent").getString("cpu.memory.metric.status") != null)
            {
                configurationValue = (configs.getJsonObject("metric.agent").getString("cpu.memory.metric.status").equals(YES)) ? NO : YES;
            }

            configs = changeAgentConfigs("metric.agent", "cpu.memory.metric.status", configurationValue, Boolean.FALSE);

            var modifiedConfigs = configs;

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_CONFIGURATION_CHANGE))
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    assertAgentConfigTestResult("metric.agent", "cpu.memory.metric.status", configurationValue);

                    messageConsumer.unregister(result ->
                            TestAPIUtil.post(AGENT_API_ENDPOINT + "configure", new JsonObject().put(AGENT_CONFIGS, modifiedConfigs.encode()).put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));
                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + "configure", new JsonObject().put(AGENT_RESTART_REQUIRED, YES).put(AGENT_CONFIGS, configs.encode()).put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), handler ->
            {
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentExportConfiguration(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(AGENT_API_ENDPOINT + agentDiscoveryId + "/configuration", result ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, result.result().statusCode());

                    assertNotNull(result.result().bodyAsJsonObject());

                    assertNotNull(result.result().bodyAsJsonObject().getJsonObject(AgentConstants.AGENT));

                    TestAPIUtil.get(AGENT_API_ENDPOINT + 1234L + "/configuration",
                            testContext.succeeding(response ->
                                    testContext.verify(() ->
                                    {
                                        assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                        assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                        testContext.completeNow();
                                    })));
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentImportConfiguration(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_CONFIGURATION_CHANGE))
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    assertEquals(readConfigs("agent.json"), VERTX.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "config.json").toJsonObject());

                    messageConsumer.unregister(result ->
                            TestAPIUtil.post(AGENT_API_ENDPOINT + 1234L + "/configuration", new JsonObject().put("agent.configuration.file", "config.json"),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));
                }
            });

            copyCurrentConfigToUploadDirectory().onComplete(result ->
                    TestAPIUtil.post(AGENT_API_ENDPOINT + agentDiscoveryId + "/configuration", new JsonObject().put("agent.configuration.file", "config.json"), response ->
                            testContext.verify(() ->
                            {
                                assertEquals(SC_OK, response.result().statusCode());

                                assertNotNull(response.result().bodyAsJsonObject());
                            })));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentImportConfigurationBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_CONFIGURATION_CHANGE))
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    assertEquals(readConfigs("agent.json"), VERTX.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "config.json").toJsonObject());

                    messageConsumer.unregister(result ->
                            TestAPIUtil.post(AGENT_API_ENDPOINT + "configuration", new JsonObject().put("agent.configuration.file", "config.json").put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));
                }
            });

            copyCurrentConfigToUploadDirectory().onComplete(result ->
                    TestAPIUtil.post(AGENT_API_ENDPOINT + "configuration", new JsonObject().put("agent.configuration.file", "config.json").put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), response ->
                            testContext.verify(() ->
                            {
                                assertEquals(SC_OK, response.result().statusCode());

                                assertNotNull(response.result().bodyAsJsonObject());
                            })));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentDownloadLogs(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_DOWNLOAD_LOG))
            {

                assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                VERTX.fileSystem().delete(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getJsonObject(EVENT_CONTEXT).getString(RESULT), result ->
                {
                    assertTrue(result.succeeded());

                    messageConsumer.unregister(response ->
                            TestAPIUtil.get(AGENT_API_ENDPOINT + 1234L + "/log", httpResponse ->
                                    testContext.verify(() ->
                                    {
                                        assertEquals(SC_BAD_REQUEST, httpResponse.result().statusCode());

                                        testContext.completeNow();
                                    })));

                });
            }
        });

        TestAPIUtil.get(AGENT_API_ENDPOINT + agentDiscoveryId + "/log", result ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, result.result().statusCode());

                    assertNotNull(result.result().bodyAsJsonObject());
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentAppDiscovery(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        LOGGER.info(String.format("Object is : %s", object.encode()));

        assertNotNull(object);

        assertFalse(object.isEmpty());

        var context = new JsonObject().put(ID, DUMMY_ID).put(Metric.METRIC_TYPE, "Apache HTTP").put(Discovery.DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT.name())
                .put(Discovery.DISCOVERY_CONTEXT, new JsonObject().put("port", 80).put("timeout", 60)).put("credential.profile.protocol", "http").put(Discovery.DISCOVERY_TARGET, new JsonArray().add(object.getLong(ID)));

        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

        assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
        {
            try
            {
                event = event.getJsonObject(EVENT_CONTEXT);

                if (event.getJsonObject(EVENT_CONTEXT) != null && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_DISCOVERY))
                {
                    assertEquals(ErrorCodes.ERROR_CODE_SUCCESS, event.getString(ERROR_CODE));

                    assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    messageConsumer.unregister(result -> vertxTestContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                vertxTestContext.failNow(exception);
            }
        });

        TestAPIUtil.post(APPLICATION_DISCOVERY_API_ENDPOINT, context, vertxTestContext.succeeding(discoveryResponse ->
                vertxTestContext.verify(() -> assertEquals(HttpStatus.SC_OK, discoveryResponse.statusCode()))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentAppPolling(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var probe = new AtomicInteger(0);

        VERTX.setPeriodic(500, timer ->
        {
            var metric = MetricConfigStore.getStore().getItemByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.APACHE_HTTP.getName());

            probe.getAndIncrement();

            if (metric != null)
            {
                VERTX.cancelTimer(timer);

                var context = metric.put(METRIC_CREDENTIAL_PROFILE, -1).put(Metric.METRIC_STATE, NMSConstants.State.ENABLE.name());

                ObjectStatusCacheStore.getStore().updateItem(ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId).getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

                TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + context.getLong(GlobalConstants.ID), new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(context)), response ->
                        vertxTestContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.result().statusCode());

                            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
                            {
                                if (event.getJsonObject(EVENT_CONTEXT) != null
                                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).contains("Monitor Poll")
                                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED))
                                {
                                    assertEquals(ErrorCodes.ERROR_CODE_SUCCESS, event.getJsonObject(EVENT_CONTEXT).getString(ERROR_CODE));

                                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                                    messageConsumer.unregister(result -> vertxTestContext.completeNow());

                                }
                            });

                            var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                            object.mergeIn(metric).put(EVENT_ID, CommonUtil.newEventId()).put(PluginEngineConstants.PLUGIN_ENGINE, PluginEngineConstants.getPluginEngine(metric.getString(Metric.METRIC_PLUGIN)));

                            EventCacheStore.getStore().addItem(object.getLong(EventBusConstants.EVENT_ID));

                            var metricPollContext = TestNMSUtil.prepareMetricPollContext(object);

                            if (metricPollContext.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS) == null || metricPollContext.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS).isEmpty())
                            {
                                var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

                                Assertions.assertNotNull(item);

                                Assertions.assertTrue(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()));

                                metricPollContext.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(item.getLong(ID)));
                            }

                            VERTX.eventBus().send(EVENT_ROUTER, metricPollContext);
                        }));
            }

            if (probe.get() > 10)
            {
                VERTX.cancelTimer(timer);

                vertxTestContext.failNow(new Exception(String.format("Failed to find %s metric", NMSConstants.Type.APACHE_HTTP.getName())));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    //bug-3740,4653
    @Timeout(value = 60, timeUnit = TimeUnit.SECONDS)
    void testAgentUpdateBusinessHour(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var businessHour = BusinessHourConfigStore.getStore().getItemByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

            Assertions.assertNotNull(businessHour);

            var businessHourContext = new JsonObject().mergeIn(businessHour).put(BusinessHour.BUSINESS_HOUR_NAME, "Motadata Agent Business Hour (24 * 7)");

            TestAPIUtil.post(BUSINESS_HOUR_API_ENDPOINT, businessHourContext, testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        TestAPIUtil.assertCreateEntityTestResult(BusinessHourConfigStore.getStore(), businessHourContext, response.bodyAsJsonObject(),
                                String.format(InfoMessageConstants.ENTITY_CREATED, "Monitoring Hour"), null, LOGGER, testInfo.getTestMethod().get().getName());

                        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

                        assertNotNull(object);

                        var context = new JsonObject().put(AGENT_BUSINESS_HOUR_PROFILE, response.bodyAsJsonObject().getLong(ID))
                                .put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                                .put(AGENT_STATUS_TYPE, AgentStatusType.HEARTBEAT.getName()).put(AGENT_STATUS, STATUS_UP);

                        context.mergeIn(object);

                        TestAPIUtil.put(AGENT_API_ENDPOINT + "/" + agentDiscoveryId,
                                context,
                                testContext.succeeding(apiResponse ->
                                        testContext.verify(() ->
                                        {
                                            assertEquals(SC_OK, apiResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                            assertEquals(String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName()), apiResponse.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                            var retries = new AtomicInteger(0);

                                            VERTX.setPeriodic(2000, timer ->
                                            {
                                                retries.getAndIncrement();

                                                TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT,
                                                        testContext.succeeding(businessHourResponse ->
                                                                testContext.verify(() ->
                                                                {
                                                                    assertNotNull(businessHourResponse.bodyAsJsonObject());

                                                                    assertEquals(SC_OK, businessHourResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                                                    var items = businessHourResponse.bodyAsJsonObject().getJsonArray(RESULT);

                                                                    assertFalse(items.isEmpty());

                                                                    var valid = false;

                                                                    for (var index = 0; index < items.size(); index++)
                                                                    {
                                                                        if (items.getJsonObject(index).getLong(ID).equals(response.bodyAsJsonObject().getLong(ID)) && items.getJsonObject(index).getInteger(ENTITY_PROPERTY_COUNT) > 0)
                                                                        {
                                                                            valid = true;

                                                                            break;
                                                                        }
                                                                    }

                                                                    if (valid)
                                                                    {
                                                                        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID) + "/references",
                                                                                testContext.succeeding(httpResponse -> testContext.verify(() ->
                                                                                {
                                                                                    assertEquals(SC_OK, httpResponse.statusCode());

                                                                                    var body = httpResponse.bodyAsJsonObject();

                                                                                    Assertions.assertNotNull(body);

                                                                                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                                                                                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                                                                                    testContext.completeNow();

                                                                                })));
                                                                    }
                                                                })));
                                            });
                                        })));
                    })));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    @Timeout(value = 70, timeUnit = TimeUnit.SECONDS)
        //bug - 3824
        //need to write metric plugin test case here as after this we deactivate agent and in metric plugin test case it's not available
    void testAgentMetricPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

            assertNotNull(object);

            assertFalse(object.isEmpty());

            var context = TestConstants.prepareParams("testCreateCustomMetricPluginHavingObject");

            context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(object.getLong(ID)));

            if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
            {
                context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, NOT_AVAILABLE);
            }

            context.put(ID, object.getLong(ID)).put(Metric.METRIC_TYPE, NMSConstants.Type.CUSTOM.getName()).put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.TIMEOUT, 20);

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                var eventContext = event.getJsonObject(EVENT_CONTEXT);

                if (event.getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_METRIC_PLUGIN_TEST)
                        && eventContext.containsKey(STATUS) && eventContext.containsKey(MESSAGE))
                {
                    testContext.verify(() ->
                    {
                        assertEquals(InfoMessageConstants.METRIC_PLUGIN_TEST_SUCCEEDED, eventContext.getString(MESSAGE));

                        assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    });
                }
            });

            VERTX.eventBus().send(UI_ACTION_METRIC_PLUGIN_TEST, context);

            metricPluginContext.mergeIn(context).put(ID, CommonUtil.newId());
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
        //bug-4206
    void testAgentAppDiscoveryDownAgent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        if (object != null)
        {
            ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_DOWN, DateTimeUtil.currentSeconds());

            var context = new JsonObject().put(Discovery.DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT.name())
                    .put(Metric.METRIC_TYPE, NMSConstants.Type.LINUX.getName()).put(Discovery.DISCOVERY_TARGET, new JsonArray().add(object.getLong(ID)))
                    .put(Discovery.DISCOVERY_CONTEXT, new JsonObject().put(PORT, 23));

            TestAPIUtil.post(DISCOVERY_API_ENDPOINT + "/application/run", context, response ->
                    testContext.verify(() ->
                    {
                        assertEquals(SC_BAD_REQUEST, response.result().statusCode());

                        var result = response.result().bodyAsJsonObject();

                        assertEquals(result.getString(MESSAGE), String.format(ErrorMessageConstants.NO_MONITOR_AVAILABLE, Entity.AGENT.getName()));

                        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

                        testContext.completeNow();
                    }));
        }
        else
        {
            testContext.failNow(new Exception("object is null...."));
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    @Timeout(value = 80, timeUnit = TimeUnit.SECONDS)
        //bug-4471
    void testAgentAppProvision(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        assertNotNull(object);

        var metricContext = new JsonObject("{\n  \"metric.name\": \"Apache HTTP\",\n  \"metric.timeout\": 60,\n  \"metric.polling.time\": 120,\n  \"metric.plugin.id\": 3,\n  \"metric.plugin\": \"apache\",\n  \"metric.state\": \"ENABLE\",\n  \"metric.type\": \"Apache HTTP\",\n  \"metric.object\": 32156014301798,\n  \"metric.category\": \"Web Server\",\n  \"metric.context\": {\n    \"port\": 80,\n    \"url.protocol\": \"http\"\n  },\n  \"metric.credential.profile\": -1,\n  \"metric.discovery.method\": \"AGENT\",\n  \"_type\": \"1\",\n  \"id\": 33926050518247\n}");

        metricContext.put(Metric.METRIC_OBJECT, object.getLong(ID)).put(PluginEngineConstants.PLUGIN_ENGINE, PluginEngineConstants.getPluginEngine(NMSConstants.MetricPlugin.APACHE_HTTP.getName()));

        Bootstrap.configDBService().save(DBConstants.TBL_METRIC, metricContext, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, response ->
        {
            if (response.succeeded())
            {
                MetricConfigStore.getStore().addItem(response.result()).onComplete(result ->
                {
                    var context = new JsonObject().put(ID, DUMMY_ID).put(Metric.METRIC_TYPE, "Apache HTTP").put(Discovery.DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT.name())
                            .put(Discovery.DISCOVERY_CONTEXT, new JsonObject().put("port", 80).put("timeout", 60)).put("credential.profile.protocol", "http").put(Discovery.DISCOVERY_TARGET, new JsonArray().add(object.getLong(ID)));

                    ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

                    assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
                    {
                        try
                        {

                            if (event.getJsonObject(EVENT_CONTEXT) != null && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE) != null
                                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).contains(InetAddress.getLocalHost().getHostName())
                                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED))
                            {

                                assertEquals(ErrorCodes.ERROR_CODE_SUCCESS, event.getJsonObject(EVENT_CONTEXT).getString(ERROR_CODE));

                                assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                                messageConsumer.unregister(asyncResult ->
                                {
                                    var metrics = MetricConfigStore.getStore().getItems(MetricConfigStore.getStore().getItemsByObjectId(object.getLong(ID)));

                                    assertNotNull(metrics);

                                    var instances = 0;

                                    for (var index = 0; index < metrics.size(); index++)
                                    {
                                        if (metrics.getJsonObject(index).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.APACHE_HTTP.getName()))
                                        {
                                            instances++;
                                        }
                                    }

                                    if (instances > 1)
                                    {
                                        vertxTestContext.failNow(new Exception("Duplicate Application Metric Apache HTTP provisioned again..."));
                                    }
                                    else
                                    {
                                        vertxTestContext.completeNow();
                                    }
                                });

                            }
                        }
                        catch (Exception exception)
                        {
                            messageConsumer.unregister(asyncResult -> vertxTestContext.failNow(new Exception(exception)));
                        }
                    });

                    TestAPIUtil.post(APPLICATION_DISCOVERY_API_ENDPOINT, context, vertxTestContext.succeeding(discoveryResponse ->
                            vertxTestContext.verify(() -> assertEquals(HttpStatus.SC_OK, discoveryResponse.statusCode()))));
                });
            }
            else
            {
                vertxTestContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    @Timeout(value = 65, timeUnit = TimeUnit.SECONDS)
        //bug-4471
    void testAgentRediscoveredAppProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var schedulerId = SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName()).getJsonObject(0).getLong(ID);

        SchedulerCacheStore.getStore().clearSchedulerContext(schedulerId);

        assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
        {
            try
            {
                if (event.getString(EVENT_TYPE) != null && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_REDISCOVER_PROGRESS) && event.getJsonObject(EVENT_CONTEXT) != null)
                {
                    var eventContext = event.getJsonObject(EVENT_CONTEXT);

                    if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(NMSConstants.Type.APACHE_HTTP.getName()))
                    {
                        testContext.failNow(new Exception("Provisioned Application found in new scanning.."));
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        VERTX.eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER)
                .put(ID, schedulerId));

        VERTX.setTimer(60000, handler -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    @Timeout(value = 65, timeUnit = TimeUnit.SECONDS)
        //bug-http://172.16.10.6:8080/browse/MOTADATA-4160
    void testAgentRediscoveredAppProvisionNotification(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        assertNotNull(object);

        var context = new JsonObject("{ \"_type\": \"1\", \"agent.id\": \"" + agentUUID + "\", \"discovery.category\": \"Server\", \"discovery.context\": { \"object\": { \"object.name\": \"TermService\", \"object.type\": \"system.service\", \"status\": \"Up\", \"system.service.description\": \"Allows users to connect interactively to a remote computer. Remote Desktop and Remote Desktop Session Host Server depend on this service.  To prevent remote use of this computer, clear the checkboxes on the Remote tab of the System properties control panel item.\" } }, \"discovery.method\": \"AGENT\", \"discovery.target\": [ 60923132900 ], \"event.id\": 60923134315, \"event.scheduler\": 10000000000002, \"event.topic\": \"agent \", \"id\": 0, \"metric.name\": \"Windows Service\", \"metric.object\": 60923132900, \"metric.plugin\": \"windowsrdp\", \"metric.type\": \"Windows RDP\", \"object\": { \"object.name\": \"TermService\", \"object.type\": \"system.service\", \"status\": \"Up\", \"system.service.description\": \"Allows users to connect interactively to a remote computer. Remote Desktop and Remote Desktop Session Host Server depend on this service.  To prevent remote use of this computer, clear the checkboxes on the Remote tab of the System properties control panel item.\" }, \"object.agent\": ***********, \"object.business.hour.profile\": 10000000000001, \"object.category\": \"Server\", \"object.creation.time\": \"2024/12/06 08:43:15\", \"object.creation.time.seconds\": 1733454795, \"object.discovery.method\": \"AGENT\", \"object.groups\": [ 10000000000018, 10000000000017 ], \"object.host\": \"localhost\", \"object.id\": 73, \"object.ip\": \"127.0.0.1\", \"object.name\": \"WIN-RC9AF0C6234\", \"object.state\": \"ENABLE\", \"object.target\": \"WIN-RC9AF0C6234\", \"object.type\": \"Windows RDP\", \"objects\": [ { \"object.ip\": \"127.0.0.1\" } ], \"plugin.engine\": \"go\", \"rediscover.job\": \"Application\", \"remote.event.processor.uuid\": \"9874F495D7284E4E9C1C173DB8C0B457\", \"session-id\": \"f519d43b-e057-4d5e-b24f-414e2cd23cd2\", \"status\": \"succeed\", \"system.log.level\": 2, \"discovery.progress\": 100.0, \"error.code\": \"MD000\", \"message\": \"Device discovery successful\", \"copy.required\": false, \"event.type\": \"discovery\" }");

        context.put(Metric.METRIC_OBJECT, object.getLong(ID)).put(ID, AgentConfigStore.getStore().getAgentId(agentUUID));

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer("event.agent.test", message ->
        {
            var event = message.body();

            LOGGER.trace("received event " + event.encode());

            assertNotNull(event);

            assertFalse(event.isEmpty());

            assertEquals(EVENT_AGENT_CONFIGURATION_CHANGE, event.getString(EventBusConstants.EVENT_TYPE, EMPTY_VALUE));

            assertTrue(event.containsKey(AGENT_CONFIGS));

            var configs = new JsonObject(event.getString(AGENT_CONFIGS));

            assertTrue(configs.getJsonObject(Agent.AGENT_METRIC).containsKey(SERVICES));

            assertFalse(configs.getJsonObject(Agent.AGENT_METRIC).getJsonArray(SERVICES).isEmpty());

            testContext.completeNow();
        });

        Bootstrap.vertx().eventBus().send(EVENT_OBJECT_PROVISION, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    @Timeout(value = 130, timeUnit = TimeUnit.SECONDS)
        // bug-4079
    void testAgentDeleteActiveMetricPlugin(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertFalse(metricPluginContext.isEmpty());

            LOGGER.info(String.format("Metric Plugin Context is: %s", metricPluginContext));

            metricPluginContext.put(MetricPlugin.METRIC_PLUGIN_NAME, "Custom Metric Plugin for Agent");

            TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, metricPluginContext,
                    testContext.succeeding(apiResponse ->
                            testContext.verify(() ->
                            {
                                LOGGER.debug("Custom metric plugin add : " + apiResponse.bodyAsJsonObject().encodePrettily());

                                assertEquals(SC_OK, apiResponse.statusCode());

                                metricPluginContext.put(ID, apiResponse.bodyAsJsonObject().getLong(ID));

                                VERTX.setPeriodic(1000, timer ->
                                {
                                    var metric = MetricConfigStore.getStore().getItemByValue(Metric.METRIC_NAME, "Custom Metric Plugin for Agent");

                                    LOGGER.debug("metric :" + metric);

                                    if (metric != null)
                                    {
                                        VERTX.cancelTimer(timer);

                                        TestAPIUtil.delete(AGENT_API_ENDPOINT + agentDiscoveryId, response ->
                                                testContext.verify(() ->
                                                {
                                                    LOGGER.debug("Custom metric plugin delete : " + response.result().bodyAsJsonObject());

                                                    assertEquals(SC_BAD_REQUEST, response.result().statusCode());

                                                    assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.AGENT.getName()), response.result().bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                                    assertEquals(SC_BAD_REQUEST, response.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.result().bodyAsJsonObject().getString(GlobalConstants.STATUS));

                                                    assertTrue(response.result().bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).containsKey(Entity.METRIC.getName()));

                                                    var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

                                                    LOGGER.debug("Object failed to delete " + object);

                                                    assertNotNull(object);

                                                    TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + metricPluginContext.getLong(ID) + "/unassign", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(object.getLong(ID))), testContext.succeeding(asyncResult ->
                                                            testContext.verify(() ->
                                                            {
                                                                LOGGER.debug("Custom metric plugin unassign : " + asyncResult.bodyAsJsonObject());

                                                                assertEquals(SC_OK, asyncResult.statusCode());

                                                                VERTX.setPeriodic(2000, periodicTimer ->
                                                                {
                                                                    var item = MetricConfigStore.getStore().getItemByValue(Metric.METRIC_NAME, metricPluginContext.getString(MetricPlugin.METRIC_PLUGIN_NAME));

                                                                    if (item == null)
                                                                    {
                                                                        VERTX.cancelTimer(periodicTimer);

                                                                        TestAPIUtil.delete(METRIC_PLUGIN_API_ENDPOINT + "/" + metricPluginContext.getLong(ID), result ->
                                                                                testContext.verify(() ->
                                                                                {
                                                                                    LOGGER.debug("Custom metric plugin unassign and delete : " + result.result().bodyAsJsonObject());

                                                                                    assertEquals(SC_OK, result.result().statusCode());

                                                                                    testContext.completeNow();
                                                                                }));
                                                                    }
                                                                });
                                                            })));
                                                }));
                                    }
                                });
                            })));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    @Disabled
    @Timeout(value = 250, timeUnit = TimeUnit.SECONDS)
    void testAgentHealth(VertxTestContext testContext, TestInfo testInfo) // TODO:
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertAgentEventTestResult(EVENT_UI, event ->
        {
            if (event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_HEALTH))
            {
                testContext.verify(() ->
                {
                    assertNotNull(event.getJsonObject(EVENT_CONTEXT).getJsonObject(RESULT));

                    assertNotNull(event.getJsonObject(EVENT_CONTEXT).getJsonObject(RESULT).getJsonObject(AgentConstants.Agent.METRIC.getName()));

                    assertNotNull(event.getJsonObject(EVENT_CONTEXT).getJsonObject(RESULT).getJsonObject(AgentConstants.Agent.METRIC.getName()).getString(SEVERITY));

                    messageConsumer.unregister(result -> testContext.completeNow());
                });
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    @Timeout(value = 300, timeUnit = TimeUnit.SECONDS)
    void testAgentStopEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_STOP)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.STOP_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    Assertions.assertTrue(event.getJsonObject(EVENT_CONTEXT).containsKey(AGENT_STATE));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.FALSE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResponse ->
                        {
                            var retries = new AtomicInteger(0);

                            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), heartbeat ->
                            {
                                if (heartbeat.getJsonObject(EVENT_CONTEXT) != null
                                        && heartbeat.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                                        && heartbeat.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_HEARTBEAT))
                                {
                                    testContext.verify(() ->
                                    {
                                        retries.getAndIncrement();

                                        assertFalse(heartbeat.getJsonObject(EVENT_CONTEXT).getJsonArray(RESULT).isEmpty());

                                        var agentStatus = heartbeat.getJsonObject(EVENT_CONTEXT).getJsonArray(RESULT);

                                        var eventContext = new JsonObject();

                                        for (var index = 0; index < agentStatus.size(); index++)
                                        {
                                            if (agentStatus.getJsonObject(index).getLong(ID) == agentDiscoveryId)
                                            {
                                                eventContext.mergeIn(agentStatus.getJsonObject(index));
                                            }
                                        }

                                        if (eventContext.getString(MotadataAppManager.HEARTBEAT_STATE) != null && (eventContext.getString(MotadataAppManager.HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE)
                                                || eventContext.getString(MotadataAppManager.HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING)))
                                        {
                                            messageConsumer.unregister(asyncResult ->
                                                    TestAPIUtil.post(AGENT_API_ENDPOINT + 1234L + "/stop",
                                                            testContext.succeeding(response ->
                                                                    testContext.verify(() ->
                                                                    {
                                                                        assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                                        assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                                        // Task - 21314 - Agent Action enable automatically after 90 seconds if response not received from agent side and allow user to perform another actions
                                                                        assertAgentEventTestResult(EVENT_UI, message ->
                                                                        {
                                                                            if (message.getJsonObject(EVENT_CONTEXT) != null
                                                                                    && message.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                                                                                    && message.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_ACTION)
                                                                                    && message.getJsonObject(EVENT_CONTEXT).getString(AGENT_DISABLE).equalsIgnoreCase(NO))
                                                                            {
                                                                                messageConsumer.unregister(handlerResult -> testContext.completeNow());
                                                                            }
                                                                        });

                                                                        TestAPIUtil.get(AGENT_API_ENDPOINT + agentDiscoveryId + "/log", handlerResult ->
                                                                                testContext.verify(() -> assertEquals(SC_OK, handlerResult.result().statusCode())));
                                                                    }))));
                                        }
                                        else if (retries.get() > 6)
                                        {
                                            messageConsumer.unregister(asyncResult -> testContext.failNow(new Exception("failed to receive heartbeat after agent stop...")));
                                        }
                                    });
                                }
                            });

                            TestUtil.vertx().eventBus().send(EVENT_AGENT_HEARTBEAT + ".status.query", new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT + ".status.query"));
                        });
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + agentDiscoveryId + "/stop", result ->
                    testContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentStartEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_START)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.START_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    Assertions.assertTrue(event.getJsonObject(EVENT_CONTEXT).containsKey(AGENT_STATE));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.TRUE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResult ->
                                TestAPIUtil.post(AGENT_API_ENDPOINT + 1234L + "/start",
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                    testContext.completeNow();
                                                }))));
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + agentDiscoveryId + "/start", handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentRestartEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_RESTART)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.RESTART_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.TRUE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResult ->
                                TestAPIUtil.post(AGENT_API_ENDPOINT + 1234L + "/restart",
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                    testContext.completeNow();
                                                }))));
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + agentDiscoveryId + "/restart", handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentResetEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_CONFIGURATION_CHANGE)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    messageConsumer.unregister(result ->
                            TestAPIUtil.post(AGENT_API_ENDPOINT + 1234L + "/reset",
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + agentDiscoveryId + "/reset", new JsonObject().put(AGENT_RESTART_REQUIRED, YES), handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentStopEventBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_STOP)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.STOP_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.FALSE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResult ->
                                TestAPIUtil.post(AGENT_API_ENDPOINT + "stop", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                    testContext.completeNow();
                                                }))));
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + "stop", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentStartEventBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_START)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.START_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.TRUE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResult ->
                                TestAPIUtil.post(AGENT_API_ENDPOINT + "start", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                    testContext.completeNow();
                                                }))));
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + "start", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentRestartEventBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_RESTART)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE),
                            String.format(InfoMessageConstants.RESTART_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    var agents = new LinkedList<>(Arrays.asList("./motadata-agent", "./motadata-metric-agent"));

                    assertAgentProcessStatusTestResult(agents, Boolean.TRUE).onComplete(result ->
                    {
                        Assertions.assertTrue(agents.isEmpty());

                        messageConsumer.unregister(asyncResult ->
                                TestAPIUtil.post(AGENT_API_ENDPOINT + "restart", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                    assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                    testContext.completeNow();
                                                }))));
                    });

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + "restart", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentResetEventBulkOps(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equals(EVENT_AGENT_CONFIGURATION_CHANGE)
                        && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
                {
                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    Assertions.assertEquals(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE), String.format(AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(agentDiscoveryId)));

                    messageConsumer.unregister(asyncResult ->
                            TestAPIUtil.post(AGENT_API_ENDPOINT + "reset", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(1234L)),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            }))));

                }
            });

            TestAPIUtil.post(AGENT_API_ENDPOINT + "/reset", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId)), handler ->
                    testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testAgentUpgradeEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        FileUtils.copyFile(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE + "-BACKUP"));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var currentMajorVersion = CommonUtil.getInteger(MotadataConfigUtil.getVersion().split("\\.")[0].trim());

            TestUtil.createZipFile(new String[]{"upgrade1.json", "upgrade2.json", "VERSION"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "restore.zip", "linux", (currentMajorVersion + 1) + ".0.0");

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                if (event.getJsonObject(EVENT_CONTEXT) != null
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).contains("Agent Upgrade")
                        && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED))
                {
                    assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                    assertEquals(ErrorCodes.ERROR_CODE_SUCCESS, event.getJsonObject(EVENT_CONTEXT).getString(ERROR_CODE));

                    messageConsumer.unregister(result ->
                            TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/" + 1234L + "/upgrade", new JsonObject().put(PATCH_ARTIFACT_FILE, "restore.zip").put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()),
                                    testContext.succeeding(response ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

                                                FileUtils.deleteQuietly(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE));

                                                FileUtils.moveFile(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE + "-BACKUP"), new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE));

                                                testContext.completeNow();
                                            }))));

                }
            });

            TestAPIUtil.post(MOTADATA_APP_ENDPOINT + "/" + agentDiscoveryId + "/upgrade", new JsonObject().put(PATCH_ARTIFACT_FILE, "restore.zip").put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()), handler ->
                    testContext.verify(() ->
                    {
                        LOGGER.info("agent upgrade response : " + handler.result().bodyAsJsonObject().encodePrettily());

                        assertEquals(SC_OK, handler.result().statusCode());
                    }));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testGetAllAgent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(AGENT_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testGetAgent(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(AGENT_API_ENDPOINT + agentDiscoveryId
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            assertNotNull(body);

                            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                            body = body.getJsonObject(RESULT);

                            assertNotNull(body);

                            assertFalse(body.isEmpty());

                            assertEquals(agentDiscoveryId, body.getLong(GlobalConstants.ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testUpdateState(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject()
                .put(OBJECT_STATE, NMSConstants.State.DISABLE.name());

        TestAPIUtil.put(AGENT_API_ENDPOINT + agentDiscoveryId + "/state", context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(AgentConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            TestAPIUtil.put(AGENT_API_ENDPOINT + 12345L + "/state", context,
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, httpResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, httpResponse.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testUpdateAllState(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.post(AGENT_API_ENDPOINT + "/state", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(agentDiscoveryId))
                        .put(OBJECT_STATE, NMSConstants.State.ENABLE.name()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.AGENT.getName());

                            TestAPIUtil.post(AGENT_API_ENDPOINT + "/state", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(12345L))
                                            .put(OBJECT_STATE, NMSConstants.State.ENABLE.name()),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_BAD_REQUEST, httpResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(STATUS_FAIL, httpResponse.bodyAsJsonObject().getString(STATUS));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testAgentPacketEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_APPLICATION_MAPPER.name()));

        testContext.awaitCompletion(15, TimeUnit.SECONDS);

        Assertions.assertTrue(true);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testAgentPortServiceCheckDiscovery(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Port for agent");

            LOGGER.info(String.format("Discovery Context is : %s", context.encode()));

            discoveryPortAgentId = context.getLong(ID);

            var agent = AgentConfigStore.getStore().getItemByValue(AGENT_UUID, agentUUID);

            Assertions.assertNotNull(agent);

            LOGGER.info(String.format("Agent is : %s", agent.encode()));

            context.put(Discovery.DISCOVERY_AGENTS, new JsonArray().add(agent.getLong(GlobalConstants.ID)));

            TestAPIUtil.put(DISCOVERY_API_ENDPOINT + "/" + discoveryPortAgentId, context, handler -> testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), timer ->
            {
                LOGGER.info(String.format("After Update Context is : %s", DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Port for agent").encode()));

                DISCOVERY_ITEMS.put(discoveryPortAgentId, testContext);

                TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryPortAgentId), new JsonObject(), handler -> testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testAgentPingServiceCheckDiscovery(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Ping for agent");

            LOGGER.info(String.format("Discovery Context is : %s", context.encode()));

            discoveryPingAgentId = context.getLong(ID);

            var agent = AgentConfigStore.getStore().getItemByValue(AGENT_UUID, agentUUID);

            Assertions.assertNotNull(agent);

            LOGGER.info(String.format("Agent is : %s", agent.encode()));

            context.put(Discovery.DISCOVERY_AGENTS, new JsonArray().add(agent.getLong(GlobalConstants.ID)));

            TestAPIUtil.put(DISCOVERY_API_ENDPOINT + "/" + discoveryPingAgentId, context, handler -> testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), timer ->
            {
                LOGGER.info(String.format("After Update Context is : %s", DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, "Ping for agent").encode()));

                DISCOVERY_ITEMS.put(discoveryPingAgentId, testContext);

                TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryPingAgentId), new JsonObject(), handler -> testContext.verify(() -> assertEquals(SC_OK, handler.result().statusCode())));
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    @Timeout(value = 50, timeUnit = TimeUnit.SECONDS)
    void testAgentDeleteActiveDiscovery(VertxTestContext testContext)
    {

        TestAPIUtil.delete(AGENT_API_ENDPOINT + agentDiscoveryId, result ->
                testContext.verify(() ->
                {
                    assertEquals(SC_BAD_REQUEST, result.result().statusCode());

                    assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.AGENT.getName()), result.result().bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(SC_BAD_REQUEST, result.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_FAIL, result.result().bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    assertTrue(result.result().bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).containsKey("Discovery Profile"));

                    testContext.completeNow();
                }));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testAgentPingObjectProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertProvisionTestResult(discoveryPingAgentId, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testGetProvisionedPingServiceCheckDiscoveryResult(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(15, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discoveryPingAgentId + "/result"
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.debug(response.bodyAsJsonObject());

                            assertAgentDiscoveryTestResult(response, NMSConstants.State.PROVISION.name(), NMSConstants.Type.PING.getName(), testContext, object);
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testDeletePingAgentDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(DISCOVERY_API_ENDPOINT + "/" + discoveryPingAgentId, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(DiscoveryConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "Discovery"));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testAgentPortObjectProvision(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        assertProvisionTestResult(discoveryPortAgentId, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testUpdateAgentAvailabilityType(VertxTestContext testContext, TestInfo testInfo)
    {
        var agent = AgentConfigStore.getStore().getItem(agentDiscoveryId);

        Assertions.assertNotNull(agent);

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        var context = object.mergeIn(agent).put(AGENT_STATUS_TYPE, AgentStatusType.PING.getName()).put(OBJECT_IP, ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)).getString(OBJECT_IP));

        TestAPIUtil.put(AGENT_API_ENDPOINT + "/" + agentDiscoveryId,
                context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(AgentConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testGetProvisionedPortServiceCheckDiscoveryResult(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(15, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discoveryPortAgentId + "/result"
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.debug(response.bodyAsJsonObject());

                            assertAgentDiscoveryTestResult(response, NMSConstants.State.PROVISION.name(), NMSConstants.Type.PORT.getName(), testContext, object);
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testDeletePortAgentDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(DISCOVERY_API_ENDPOINT + "/" + discoveryPortAgentId, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(DiscoveryConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "Discovery"));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testAgentAvailabilityByPing(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                if (event.containsKey(AIOpsObject.OBJECT_AGENT) && event.getLong(AIOpsObject.OBJECT_AGENT).equals(agentDiscoveryId)
                        && event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                {
                    Assertions.assertTrue(event.containsKey(STATUS));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentDiscoverRemoteApp(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(CommonUtil.getIPAddresses().getFirst()));

        assertNotNull(object);

        assertFalse(object.isEmpty());

        var credential = TestConstants.prepareParams("testLinuxInvalidCredentialDiscovery");

        Assertions.assertNotNull(credential);

        credential.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "dummy " + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, vertxTestContext).onComplete(result ->
        {
            var credentialId = result.result();

            assertNotNull(credentialId);

            Assertions.assertTrue(credentialId > 0L);

            var context = new JsonObject().put(ID, DUMMY_ID).put(Metric.METRIC_TYPE, "Apache HTTP")
                    .put(Discovery.DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name())
                    .put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId))
                    .put(Discovery.DISCOVERY_CONTEXT, new JsonObject().put("port", 80).put("timeout", 60)).put("credential.profile.protocol", "http").put(Discovery.DISCOVERY_TARGET, new JsonArray().add(object.getLong(ID)));

            ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

            assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
            {
                try
                {
                    if (event.getJsonObject(EVENT_CONTEXT) != null && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE) != null
                            && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).contains(InetAddress.getLocalHost().getHostName())
                            && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_STATE).equals(EVENT_STATE_COMPLETED))
                    {
                        Assertions.assertTrue(event.getJsonObject(EVENT_CONTEXT).containsKey(STATUS));

                        assertEquals(STATUS_SUCCEED, event.getJsonObject(EVENT_CONTEXT).getString(STATUS));

                        messageConsumer.unregister(asyncResult -> vertxTestContext.completeNow());
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(asyncResult -> vertxTestContext.failNow(exception));
                }
            });

            TestAPIUtil.post(APPLICATION_DISCOVERY_API_ENDPOINT, context, vertxTestContext.succeeding(discoveryResponse ->
                    vertxTestContext.verify(() -> assertEquals(HttpStatus.SC_OK, discoveryResponse.statusCode()))));
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testAgentCacheStore(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        AgentCacheStore.getStore().initStore().onComplete(result ->
        {
            if (result.failed())
            {
                testContext.failNow(result.cause());
            }
            else
            {
                var cache = AgentCacheStore.getStore().getItem(agentDiscoveryId);

                Assertions.assertNotNull(cache);

                assertFalse(cache.isEmpty());

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
        //bug-20140
    void testAgentFileDirObjectPolling(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        assertFalse(object.isEmpty());

        var plugins = new JsonArray().add(NMSConstants.MetricPlugin.LINUX_FILE.getName()).add(NMSConstants.MetricPlugin.LINUX_DIR.getName())
                .add(NMSConstants.MetricPlugin.WINDOWS_FILE.getName()).add(NMSConstants.MetricPlugin.WINDOWS_DIR.getName());

        var metrics = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID))
                .stream().filter(item -> plugins.contains(item.getString(Metric.METRIC_PLUGIN)))
                .map(JsonObject::mapFrom).toList();

        assertFalse(metrics.isEmpty());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    @Timeout(value = 60, timeUnit = TimeUnit.SECONDS)
        // bug - 20273
    void testAgentDeleteActiveRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var item = RunbookPluginConfigStore.getStore().getItem();

        Assertions.assertNotNull(item);

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        assertFalse(object.isEmpty());

        item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).add(object.getLong(ID));

        Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        RunbookPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                TestAPIUtil.delete(AGENT_API_ENDPOINT + agentDiscoveryId, future ->
                                        testContext.verify(() ->
                                        {

                                            assertEquals(SC_BAD_REQUEST, future.result().statusCode());

                                            assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.AGENT.getName()), future.result().bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                            assertEquals(SC_BAD_REQUEST, future.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                            assertEquals(STATUS_FAIL, future.result().bodyAsJsonObject().getString(GlobalConstants.STATUS));

                                            assertTrue(future.result().bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).containsKey(Entity.RUNBOOK_PLUGIN.getName()));

                                            item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).remove(object.getLong(ID));

                                            Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                    item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                    handler -> RunbookPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(futureResult -> testContext.completeNow()));
                                        }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testAgentRegistrationDuplicateObjectError(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var object = new JsonObject("{\"object.ip\":\"*******\",\"object.host\":\"agent-local-demo\",\"object.name\":\"agent-local-demo\",\"object.groups\":[10000000000001],\"object.type\":\"Windows\",\"object.context\":{\"ping.check.status\":\"yes\",\"port\":5985},\"object.discovery.method\":\"REMOTE\",\"object.target\":\"*******\",\"object.state\":\"ENABLE\",\"_type\":\"1\",\"remote.address\":\"127.0.0.1\",\"user.name\":\"admin\",\"event.id\":21689704564610,\"object.creation.time\":\"02:05:54 PM 30/11/2020\",\"object.business.hour.profile\":10000000000001,\"object.category\":\"Server\",\"object.id\":1000}");

        Bootstrap.configDBService().save(DBConstants.TBL_OBJECT, object, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                ObjectConfigStore.getStore().addItem(asyncResult.result()).onComplete(result ->
                {
                    var objectId = ObjectConfigStore.getStore().getObjectIdByIP(object.getString(AIOpsObject.OBJECT_IP), NMSConstants.Type.valueOfName(object.getString(AIOpsObject.OBJECT_TYPE)));

                    if (objectId == NOT_AVAILABLE)
                    {
                        objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(object.getString(AIOpsObject.OBJECT_NAME));
                    }

                    assertNotEquals(NOT_AVAILABLE, objectId);

                    testContext.completeNow();
                });
            }
            else
            {
                testContext.failNow(asyncResult.cause());
            }
        });
    }

    // MOTADATA-2112
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testUpdateAgentIP(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var agent = AgentConfigStore.getStore().getItem(agentDiscoveryId);

        Assertions.assertNotNull(agent);

        var object = ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId);

        Assertions.assertNotNull(object);

        var context = object.mergeIn(agent).put(OBJECT_IP, "************");

        LOGGER.info("before update agent ip : " + ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId));

        TestAPIUtil.put(AGENT_API_ENDPOINT + "/" + agentDiscoveryId,
                context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            try
                            {
                                LOGGER.info("result : " + response.bodyAsString());

                                var responseBody = response.bodyAsJsonObject();

                                assertEquals(String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName()), responseBody.getString(GlobalConstants.MESSAGE).trim());

                                assertEquals(HttpStatus.SC_OK, responseBody.getInteger(APIConstants.RESPONSE_CODE));

                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    LOGGER.info("after update agent ip : " + ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId));

                                    assertEquals("************", ObjectConfigStore.getStore().getItemByAgentId(agentDiscoveryId).getString(OBJECT_IP));

                                    testContext.completeNow();
                                });
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testAgentDeactivateEvent(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var agent = AgentConfigStore.getStore().getItemByValue(AGENT_UUID, agentUUID);

        Assertions.assertNotNull(agent);

        assertAgentEventTestResult(EVENT_USER + TestUtil.getSessionId(), event ->
        {
            LOGGER.info(String.format("Event received :: %s", event.encode()));

            if (event.getString(EVENT_TYPE) != null && event.getJsonObject(EVENT_CONTEXT) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE) != null
                    && event.getJsonObject(EVENT_CONTEXT).getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_DELETE)
                    && event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE) != null)
            {
                testContext.verify(() ->
                {
                    Assertions.assertTrue(event.getJsonObject(EVENT_CONTEXT).getString(MESSAGE).contains(String.format(InfoMessageConstants.DELETE_SUCCEEDED, Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID)))));

                    var configs = new JsonObject().mergeIn(TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + "agent.json").toJsonObject());

                    if (configs.getJsonObject(AgentConstants.AGENT) != null && configs.getJsonObject(AgentConstants.AGENT).getString("agent.deactivation.status") != null)
                    {
                        assertEquals(GlobalConstants.YES, configs.getJsonObject(AgentConstants.AGENT).getString("agent.deactivation.status"));
                    }

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                });
            }
        });

        TestAPIUtil.delete(AGENT_API_ENDPOINT + agent.getLong(ID), result ->
        {
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    @Timeout(value = 90, timeUnit = TimeUnit.SECONDS)
    void testStartMetricAgentDefaultSequence(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        killProcess(agentManagerProcess.toHandle());

        changeConfigurationParams("APP");

        var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent.json");

        if (file.exists())
        {
            try
            {
                AgentConfigUtil.getAgentConfigs().mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)).getJsonObject(AgentConstants.AGENT));

                ProcessUtil.start(AgentConstants.Agent.METRIC.getName(), EventBusConstants.EVENT_AGENT_START);

                TestUtil.vertx().setPeriodic(1000, timer ->
                {

                    if (getProcess("./motadata-metric-agent"))
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.completeNow();
                    }
                });
            }
            catch (Exception exception)
            {
                LOGGER.warn("failed to init agent config....");

                LOGGER.error(exception);

                testContext.failNow(exception.getCause());
            }
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    @Timeout(value = 90, timeUnit = TimeUnit.SECONDS)
    void testStopMetricAgentDefaultSequence(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        if (ProcessUtil.stop(AgentConstants.Agent.METRIC.getName()))
        {
            testContext.completeNow();
        }
        else
        {
            testContext.failNow("failed to stop metric agent");
        }
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    private void assertAgentDiscoveryTestResult(HttpResponse<Buffer> response, String state, String type, VertxTestContext testContext, JsonObject object)
    {
        assertEquals(SC_OK, response.statusCode());

        assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

        var items = response.bodyAsJsonObject().getJsonArray(RESULT);

        Assertions.assertNotNull(items);

        assertFalse(items.isEmpty());

        var entry = items.getJsonObject(0);

        Assertions.assertTrue(entry.containsKey(OBJECT_STATE));

        Assertions.assertEquals(state.toLowerCase(), entry.getString(OBJECT_STATE).toLowerCase());

        if (state.equalsIgnoreCase(NMSConstants.State.PROVISION.name()))
        {
            var item = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_TYPE, type)
                    .stream().filter(result -> JsonObject.mapFrom(result).containsKey(AIOpsObject.OBJECT_AGENT)
                            && (JsonObject.mapFrom(result).getLong(AIOpsObject.OBJECT_AGENT).equals(agentDiscoveryId) ||
                            JsonObject.mapFrom(result).getLong(AIOpsObject.OBJECT_AGENT).equals(object.getLong(ID))))
                    .map(JsonObject::mapFrom)
                    .findFirst().orElse(null);

            Assertions.assertNotNull(item);

            var attempts = new AtomicInteger();

            VERTX.setPeriodic(3000, timer ->
            {
                attempts.incrementAndGet();

                /* unprovisioned successfully */
                if (ObjectConfigStore.getStore().getItem(item.getLong(ID)) == null)
                {
                    Assertions.assertNull(ObjectConfigStore.getStore().getItem(item.getLong(ID)));

                    var discoveryId = type.equalsIgnoreCase(NMSConstants.Type.PING.getName()) ? discoveryPingAgentId : discoveryPortAgentId;

                    var entries = new JsonArray().add(entry.put(OBJECT_STATE, NMSConstants.State.PROVISION.name()));

                    Bootstrap.configDBService().drop(DBConstants.TBL_DISCOVERY_RESULT + discoveryId,
                            result -> Bootstrap.configDBService().saveAll(DBConstants.TBL_DISCOVERY_RESULT + discoveryId,
                                    entries, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                    asyncResult -> DiscoveryConfigStore.getStore().updateItem(discoveryId).onComplete(asyncResponse ->
                                    {
                                        VERTX.cancelTimer(timer);

                                        testContext.completeNow();
                                    })));
                }
                else if (attempts.get() > 3)
                {
                    VERTX.cancelTimer(timer);

                    if (!testContext.completed())
                    {
                        testContext.failNow("Unable to unprovision !");
                    }
                }

            });

            TestUtil.vertx().eventBus().send(EVENT_OBJECT_UNPROVISION,
                    new JsonObject().put(ID, item.getLong(ID)));
        }
        else
        {
            testContext.completeNow();
        }
    }

    private void assertAgentEventTestResult(String address, Handler<JsonObject> handler)
    {
        messageConsumer = VERTX.eventBus().localConsumer(address, message ->
        {
            if (message.body().getString(EVENT_TYPE) != null && !message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_RESPONSE_STREAM))
            {
                var context = message.body().getValue(EVENT_CONTEXT);

                if (context instanceof String)
                {
                    try
                    {
                        message.body().put(EVENT_CONTEXT, CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)));

                        VERTX.<Void>executeBlocking(future ->
                        {
                            var event = new JsonObject("{\"event.type\":\"agent.response.stream\",\"agent.id\":\"" + agentUUID + "\",\"agent.type\":\"metric\",\"event.context\":\"1C9seyJzeXN0ZW0uY3B1LnBlcmNlbnQiOiAwLjIsIBEbMGxvYWQuYXZnMS5taW4FHQAwRh0AADUZHQAxRh4AADEdHxkeKG1lbW9yeS51c2VkHXwIMjUuHUIuJABAYnl0ZXMiOiA3NDEyNTcyMTZCSwAMZnJlZRUnHDIxMzgxNTI5WicAGe4MNzQuOUJLABxhdmFpbGFibBlQIDMwODY5OTk1NSEciGV2ZW50LnRpbWVzdGFtcCI6IDE2NjE5NDI5NDB9X3xAI3xfNVw4cnVubmluZy5wcm9jZXNzBcQEMTc9CRR0aHJlYWQB2wgyODQZigR1cAFgDVsUMTQ5MDk3LnsBNGNvbnRleHQuc3dpdGNoBVYcMjM0MDY2MzYdzyBpbnRlcnJ1cHQFXxwzMzI5NzUzOBllEGRpc2suAX85OBw0ODUwODI2Mh2LBSYMd3JpdDkPJDQwNTU5NjI4MjgdTi2vHGNhcGFjaXR5FVQgNDEyMjI1MTI2HVQQYmxvY2sh-z0XPRVUb3BlbmVkLmZpbGUuZGVzY3JpcHRvcgHGCDE0MB1zQboMdXNlcj3RMr8CASAMbmljZTIgAB1oASAUa2VybmVsbiIACGlkbC5CAAg5OS46gwAEaW9yPwAxhm4lAAxzdGVhYoUADHN3YXBiXwEcMjA1NzMwMjAuEwMuMQA6YwMAMC4AAS4oAGVAeRkEMTB2KgBxkZJ_ABEtPXsYdmlydHVhbBHXBaYRJxQzMTY1MzhFXK5sAyRuZXR3b3JrLmluCUoMLnJhdGFDHDIzMTk5Mzg2BVGN9BEsCG91dDYtABwxODgzODMyMX1HESwFnxVVBDUwBScEMDd5UBEpAVUUcGFja2V0FS8YMzEzNzA2OEpWAARpbj4sABQxNTM0Mjk9LBFYPlQAFDI5MDQ5N1ZUABBlcnJvchEyobpWpwCKJwCKIwAcaW4uZHJvcHBh0gn4GCI6IDI2OTMurQMRxiEeRi4ASqAARiUAXlMAZWMIZmFjIe4AW7V8EXEuHgAcImVuczE2MCJGDgEVJgWhMuwBHDE2MjI0MTgwof06QAIZNkJ2AhwyOTM3OTcyN93SQpIABWwtM1FaFDEwMjE2M0oQATJsADo1ABgzMDg1ODAyUgwCBHRlJRwBopLMAREwIamyMQCyBwIZzgFoRuwBcgEBMp8BJDI0NTYwMzkwNzNu1gE-MQEUMTg3OTY1cjEBsv0ARsIAgq4CJC5pcC5hZGRyZXNhMCwiMTcyLjE2LjguNjFyoAI4bmV0bWFzayI6ICIyNTUuBQQcNDguMCJ9LCB2_QIEbG9yWQAhiTZaARQ2MTQxNDFK4wQ5vkL4Aoo0AAFpOpABDiEIADZy9AFGKQWSNACyxQEBmbIxAFLzAnLHAAFjRiwCcjMAMpQBBDUyDngJADMqTQlGVgQ6XAEUMTAyNTMycmMAsvUAur8AOuYCGDI3LjAuMC6y5AIBMRQwIn1dLCCyEggaXwoMIjogW747ABL2ChB2b2x1bTZVBh0YRusKJDE5ODk2MzUyNzYqIgouMgAy5wggMTIwMDUzNTE0Qm4LCXgWkwkl_RgiOiA2ODU0DtUMclsAJsAJCDYzLn23LoQAOukJCDM2Lr04HSmBYFgvZGV2L21hcHBlci91YnVudHUtLXZnLRELBGx2MnQEHT1KHAEYMDIwNzAyNy6IAi4xADIbARwyMTM1Njk1MyrADS4sAAXDMRkYNzM2NjY5Ni7sDS4sAAVYORgMMjIuNdknLikABVUZKQQ3N1opADUYDHNkYTKmAAEYNTgzMjcwNH2GLn0ABaYR0nYrAAWoEStuTwAZzDrNCy55AAVOGSoy9QsdKBX1DGxvb3A2agYdJkb2AXbLAP72AP72ALL2AKrsARg2NTAxMTcxKjAKLiUBMvYAdisAJZ7-7AHy7AEAMabiAnbLAP72AP72ALL2AAAzpvYAFDcxMTcyMHJ-BDHBdisA_uwB7uwBKewANab2ABg0OTI4MzA3juICdisA_vYA7vYACfYANKb2ABQ3NDg0MjGS2AN2KwD-9gDu9gAJ9gA2pvYAdsEBMtgDdisA_vYA7vYACfYANxZEChrFD6WVQrAUJDIxNDA4MzEzMzQqZAsFKzLrACAxMjcxMDE3ODgueRMFJzLnABA3NTkxMA6pDFJNAPl5CDU5Lj4iAAVIGSIINDAuDg8JAGVufhY\"}");

                            try
                            {
                                for (var value : new String(CodecUtil.toBytes(event.getBinary(EVENT_CONTEXT))).split(VALUE_SEPARATOR_WITH_ESCAPE))
                                {
                                    if (AgentConstants.Agent.valueOfName(event.getString(AGENT_TYPE)) == AgentConstants.Agent.METRIC)
                                    {
                                        VERTX.eventBus().send(EVENT_METRIC_AGENT, event.put(EVENT_CONTEXT, value));
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                            future.complete();
                        }, false, result ->
                        {
                        });


                        VERTX.<Void>executeBlocking(future ->
                        {
                            var event = new JsonObject("{\"event.type\":\"agent.response.stream\",\"agent.id\":\"" + agentUUID + "\",\"agent.type\":\"metric\",\"event.context\":\"xwlseyJzeXN0ZW0uY3B1LnBlcmNlbnQiOiAwLjQsIBEbMGxvYWQuYXZnMS5taW4FHQgwMSxCHgAANRkeRh0AADFSHgAobWVtb3J5LnVzZWQdfAwxNy4zGUIuJABAYnl0ZXMiOiA0MjA0MjU3MjhCJwAMZnJlZRUnIDI3NDg5MDc1MlYnABnuDDgyLjdCJAAcYXZhaWxhYmwZUCAzNDExMDIxODIhHIhldmVudC50aW1lc3RhbXAiOiAxNjYyNzk0MjEwfV98QCN8XzVcOHJ1bm5pbmcucHJvY2VzcwXECDE3NhlzFHRocmVhZAHbCDI4ORkXBHVwAWAJWxwxMTQ5MDk3Lj1dNGNvbnRleHQuc3dpdGNoBVYcNDU4MTIwMzABoi3ZIGludGVycnVwdAFfHDQ2NTM4OTQxHe8QZGlzay4Bfzk4HDkzNzMxOTkzHaIFJgx3cml0OQ8kNzMyODYzMDc4NB2ULa8cY2FwYWNpdHkVVCA0MTIyMjUxMjYdmhBibG9jayH7AHI5Fx1LVG9wZW5lZC5maWxlLmRlc2NyaXB0b3IBxgQxNS7hAEG6DHVzZXI90QAwLkICASAMbmljZTIgAB1oASAUa2VybmVsMiIAXeMBIghpZGwuQgAIOTkuPR4FIQBvMj8AOmEANYZuJQAMc3RlYTaFAB1GDHN3YXBiXwEgMjA1NzMwMjAxHZUuMQA6YwMAMF5ZAABmYUB5GQQxMHYqAHGRIDIwNTY1MDMyOW5_ABEtEDc5ODcyHdkYdmlydHVhbBHcBasRLBQzMDE3OTNl6XJxAw\"}");

                            try
                            {
                                VERTX.eventBus().send(EventBusConstants.EVENT_AGENT, event);

                                VERTX.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(AGENT_TYPE, AgentConstants.Agent.LOG.getName()));

                                VERTX.eventBus().send(EventBusConstants.EVENT_AGENT, event.put(AGENT_TYPE, AgentConstants.Agent.PACKET.getName()));
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                            future.complete();
                        }, false, result ->
                        {
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
                handler.handle(message.body());
            }
        });
    }

    private void assertAgentConfigTestResult(String agent, String key, String expectedValue)
    {
        var configs = readConfigs("agent.json");

        if (configs.getJsonObject(agent) != null && configs.getJsonObject(agent).getString(key) != null)
        {
            assertEquals(configs.getJsonObject(agent).getString(key), expectedValue);
        }
    }

    private Future<Void> assertAgentProcessStatusTestResult(List<String> processes, boolean processStatus)
    {
        var promise = Promise.<Void>promise();

        VERTX.setPeriodic(1000, timer ->
        {
            var iterator = processes.iterator();

            while (iterator.hasNext())
            {
                var agent = iterator.next();

                if (getProcess(agent) == processStatus)
                {
                    try
                    {
                        iterator.remove();
                    }
                    catch (Exception ignore)
                    {
                    }
                }
            }

            if (processes.isEmpty())
            {
                VERTX.cancelTimer(timer);

                promise.complete();
            }
        });

        return promise.future();

    }

    private void publishEvent(JsonObject event)
    {
        if (!event.isEmpty() && isNotNullOrEmpty(event.getString(EVENT_TYPE)) && isNotNullOrEmpty(event.getString(AGENT_UUID)))
        {
            VERTX.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, event);
        }
    }

    private boolean getProcess(String processName)
    {
        var result = false;

        try
        {
            var process = OS_WINDOWS ? Runtime.getRuntime().exec(System.getenv("windir") + "\\system32\\" + "tasklist.exe /fo csv /nh")
                    : Runtime.getRuntime().exec(new String[]{"/bin/bash", "-c", "ps -few | awk '{print $8}'"});

            if (process != null)
            {
                var input = new BufferedReader(new InputStreamReader(process.getInputStream()));

                String line;

                while ((line = input.readLine()) != null)
                {
                    if (line.trim().equalsIgnoreCase(processName))
                    {
                        result = Boolean.TRUE;
                    }
                }

                input.close();
            }

        }
        catch (Exception ignored)
        {

        }

        return result;
    }
}
