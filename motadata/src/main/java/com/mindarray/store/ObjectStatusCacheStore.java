/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  21-Apr-2025     <PERSON><PERSON>        Added Support for Status Flap Metric
 * */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CONTEXT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.nms.NMSConstants.*;

public class ObjectStatusCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(ObjectStatusCacheStore.class, GlobalConstants.MOTADATA_STORE, "Object Status Cache Store");

    private static final ObjectStatusCacheStore STORE = new ObjectStatusCacheStore();
    private static final int STATUS_DOWN_FLAPS = MotadataConfigUtil.getStatusDownFlaps();
    private final Map<Long, String> items = new ConcurrentHashMap<>();
    private final Map<Long, ConcurrentMap<String, Long>> timestamps = new ConcurrentHashMap<>();

    private final Map<Long, ConcurrentMap<String, String>> flapReasons = new ConcurrentHashMap<>();
    private final Map<Long, ConcurrentMap<String, String>> instances = new ConcurrentHashMap<>();

    private final Set<Long> objects = new HashSet<>();
    private final Map<Long, ConcurrentMap<String, Integer>> downFlaps = new ConcurrentHashMap<>();


    private final AtomicBoolean updated = new AtomicBoolean();
    private final AtomicBoolean dirty = new AtomicBoolean();


    private ObjectStatusCacheStore()
    {
    }

    public static ObjectStatusCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        loadItems();

        promise.complete();

        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        return promise.future();
    }

    public Map<Long, String> getItems()
    {
        return new HashMap<>(items);
    }

    public JsonArray getItemsByStatus(String status)
    {
        var items = new JsonArray();

        for (var entry : this.items.entrySet())
        {
            if (entry.getValue().equalsIgnoreCase(status))
            {
                var object = ObjectConfigStore.getStore().getItem(entry.getKey());

                if (object != null)
                {
                    items.add(new JsonObject().put(ID, entry.getKey()).put(OBJECT_NAME, object.getString(OBJECT_NAME)).put(OBJECT_IP, object.getString(OBJECT_IP, object.getString(OBJECT_TARGET))).put(STATUS, status).put(OBJECT_ID, object.getInteger(OBJECT_ID)));
                }
            }
        }

        return items;
    }

    public Map<Long, Map<String, String>> getInstanceItems()
    {
        return new HashMap<>(instances);
    }

    public JsonArray getInstanceItems(JsonArray ids)
    {
        var items = new JsonArray();

        for (var i = 0; i < ids.size(); i++)
        {
            var id = ids.getLong(i);

            if (instances.containsKey(id))
            {
                items.add(instances.get(id));
            }
        }

        return items;
    }

    public Map<String, String> getInstanceItems(long id, boolean copy)
    {
        return copy ? new HashMap<>(instances.get(id)) : instances.getOrDefault(id, null);
    }

    public boolean existItem(long id)
    {
        return items.containsKey(id);
    }

    public boolean eligible(long id, String object)
    {
        return downFlaps.get(id) == null || !downFlaps.get(id).containsKey(object);
    }

    public void updateItem(long id, String status, long timestamp)
    {
        updateItem(id, GlobalConstants.EMPTY_VALUE, status, timestamp);
    }

    private void updatePollTime(long id, String object, long timestamp, boolean flap)
    {
        if (flap || !timestamps.containsKey(id) || !timestamps.get(id).containsKey(object))
        {
            updated.set(true);

            timestamps.computeIfAbsent(id, value -> new ConcurrentHashMap<>()).put(object, timestamp);
        }
    }

    public String getItem(long id, String object)
    {
        return instances.containsKey(id) ? instances.get(id).get(object) : null;
    }

    public String getItemByReason(long id, String object)
    {
        return flapReasons.containsKey(id) && flapReasons.get(id).containsKey(object) ? flapReasons.get(id).get(object) : GlobalConstants.EMPTY_VALUE;
    }

    public String getItem(long id)
    {
        return items.getOrDefault(id, null);
    }

    public void deleteItem(long id)
    {
        items.remove(id);

        instances.remove(id);

        flapReasons.remove(id);
    }

    public void deleteItem(long id, JsonArray objects)
    {
        var item = instances.get(id);

        if (item != null)
        {
            objects.forEach(item::remove);
        }

        item = flapReasons.get(id);

        if (item != null)
        {
            objects.forEach(item::remove);
        }
    }

    public void updateItem(long id, String object, String status, long timestamp)
    {
        var flap = false;

        var update = false;

        var valid = true;

        /* If the status is down, increment the down flap count for this object instance.
           OBJECT_STATUS_DOWN_FLAP_COUNT will be used for the main object, otherwise use INSTANCE_STATUS_DOWN_FLAP_COUNT for instances.
        */
        if (status.equalsIgnoreCase(STATUS_DOWN))
        {
            downFlaps.computeIfAbsent(id, value -> new ConcurrentHashMap<>()).merge(object, 1, Integer::sum);

            // Check if the down flap count has reached the configured threshold for this object or instance.
            valid = downFlaps.get(id).get(object) >= STATUS_DOWN_FLAPS;
        }

        /* If the status is not down or unreachable, remove the down flap count entry for this object instance.
           Because when object goes down then instances status will be updated to unreachable & we only want to remove only when we get status up/unknown etc.
        */
        else if (downFlaps.get(id) != null && !status.equalsIgnoreCase(STATUS_UNREACHABLE))
        {
            downFlaps.get(id).remove(object);
        }

        if (object.equalsIgnoreCase(GlobalConstants.EMPTY_VALUE))
        {
            update = true;

            var pluginId = ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.AVAILABILITY.getName());

            if (valid && items.containsKey(id) && !items.get(id).equalsIgnoreCase(status))
            {
                flap = true;

                updated.set(true);

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_UPDATE, new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(GlobalConstants.ID, id).put(NMSConstants.INSTANCE_NAME, object).put(VisualizationConstants.VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName(), GlobalConstants.YES).put(PREVIOUS_FLAP_STATUS, items.get(id)).put(GlobalConstants.STATUS, status).put(PREVIOUS_FLAP_TIMESTAMP, DateTimeUtil.currentSeconds()));

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_CHANGE, new JsonObject().put(ID, id).put(STATUS_KEY, id).put(STATUS, status).put("status.type", "object.status"));

                // if status change then notify to refresh respective widgets
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(pluginId)).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
            }

            if (valid && items.put(id, status) == null) // first time status
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_CHANGE, new JsonObject().put(ID, id).put(STATUS_KEY, id).put(STATUS, status).put("status.type", "object.status"));

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, pluginId + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(pluginId)).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
            }
        }
        else
        {
            if (object.contains(INSTANCE_SEPARATOR))
            {
                update = true;

                var tokens = object.split(INSTANCE_SEPARATOR);

                if (valid && instances.containsKey(id) && instances.get(id).containsKey(object) && !instances.get(id).get(object).equalsIgnoreCase(status))
                {
                    flap = true;

                    updated.set(true);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_UPDATE, new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(GlobalConstants.ID, id).put(NMSConstants.INSTANCE_NAME, object).put(VisualizationConstants.VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName(), GlobalConstants.YES).put(PREVIOUS_FLAP_STATUS, instances.get(id).get(object)).put(GlobalConstants.STATUS, status).put(PREVIOUS_FLAP_TIMESTAMP, DateTimeUtil.currentSeconds()));

                    if (tokens[0].equalsIgnoreCase(NMSConstants.HYPERV_VM) || tokens[0].equalsIgnoreCase(NMSConstants.CITRIX_XEN_VM) || tokens[0].equalsIgnoreCase(NMSConstants.ESXI_VM) || tokens[0].equalsIgnoreCase(NUTANIX_VM) || tokens[0].equalsIgnoreCase(NMSConstants.SYSTEM_SERVICE) || tokens[0].equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS) ||
                            tokens[0].equalsIgnoreCase(NMSConstants.ARUBA_WIRELESS_ACCESS_POINT) || tokens[0].equalsIgnoreCase(NMSConstants.CISCO_WIRELESS_ACCESS_POINT) || tokens[0].equalsIgnoreCase(NMSConstants.RUCKUS_WIRELESS_ACCESS_POINT))
                    {
                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_CHANGE, new JsonObject().put(ID, id).put(STATUS_KEY, id + SEPARATOR + object.split(INSTANCE_SEPARATOR)[1]).put(STATUS, status).put("status.type", "instance.status"));
                    }

                    // if status change then notify to refresh respective widgets
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, tokens[2] + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(CommonUtil.getInteger(tokens[2]))).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
                }

                if (valid && instances.computeIfAbsent(id, value -> new ConcurrentHashMap<>()).put(object, status) == null)
                {
                    if (tokens[0].equalsIgnoreCase(NMSConstants.HYPERV_VM) || tokens[0].equalsIgnoreCase(NMSConstants.CITRIX_XEN_VM) || tokens[0].equalsIgnoreCase(NMSConstants.ESXI_VM) || tokens[0].equalsIgnoreCase(NUTANIX_VM) || tokens[0].equalsIgnoreCase(NMSConstants.SYSTEM_SERVICE) || tokens[0].equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS) ||
                            tokens[0].equalsIgnoreCase(NMSConstants.ARUBA_WIRELESS_ACCESS_POINT) || tokens[0].equalsIgnoreCase(NMSConstants.CISCO_WIRELESS_ACCESS_POINT) || tokens[0].equalsIgnoreCase(NMSConstants.RUCKUS_WIRELESS_ACCESS_POINT))
                    {
                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_STATUS_CHANGE, new JsonObject().put(ID, id).put(STATUS_KEY, id + SEPARATOR + object.split(INSTANCE_SEPARATOR)[1]).put(STATUS, status).put("status.type", "instance.status"));
                    }

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, tokens[2] + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(CommonUtil.getInteger(tokens[2]))).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(id))))));
                }
            }
        }

        if (update)
        {
            updateFlapReason(id, object, status);

            updatePollTime(id, object, timestamp, flap);
        }
    }

    private void updateFlapReason(long id, String object, String reason)
    {
        flapReasons.computeIfAbsent(id, value -> new ConcurrentHashMap<>()).put(object, reason);
    }

    public JsonArray getObjects()
    {
        return new JsonArray(objects.stream().toList());
    }

    public void addObject(long id)
    {
        objects.add(id);
    }

    public boolean exist(long id)
    {
        return !objects.contains(id);
    }

    public void deleteObject(long id)
    {
        objects.remove(id);
    }

    public long getTimestamp(long id)
    {
        return timestamps.containsKey(id) ? timestamps.get(id).get(GlobalConstants.EMPTY_VALUE) : null;
    }

    public long getTimestamp(long id, String object)
    {
        return timestamps.containsKey(id) && timestamps.get(id).containsKey(object) ? timestamps.get(id).get(object) : null;
    }

    public void loadItems()
    {
        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + NMSConstants.STATUS_FLAPS);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                    ((Map<String, Object>) (context.get("items"))).forEach((key, value) -> items.put(CommonUtil.getLong(key), CommonUtil.getString(value)));

                    ((Map<String, Object>) (context.get("timestamps"))).forEach((key, value) ->
                    {
                        var time = new ConcurrentHashMap<String, Long>();

                        timestamps.computeIfAbsent(CommonUtil.getLong(key), val -> time);

                        ((Map<String, Object>) value).forEach((object, timestamp) -> time.put(object, CommonUtil.getLong(timestamp)));

                    });

                    ((Map<String, Object>) (context.get("item.instances"))).forEach((key, value) ->
                    {
                        var instance = new ConcurrentHashMap<String, String>();

                        instances.computeIfAbsent(CommonUtil.getLong(key), val -> instance);

                        ((Map<String, Object>) value).forEach((instanceName, status) -> instance.put(instanceName, CommonUtil.getString(status)));

                    });

                    ((Map<String, Object>) (context.get("flap.reasons"))).forEach((key, value) ->
                    {
                        var flapReason = new ConcurrentHashMap<String, String>();

                        flapReasons.computeIfAbsent(CommonUtil.getLong(key), val -> flapReason);

                        ((Map<String, Object>) value).forEach((instanceName, reason) -> flapReason.put(instanceName, CommonUtil.getString(reason)));

                    });

                    LOGGER.info("loaded object cache items");
                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }


    public void dump(String path)
    {
        if (!items.isEmpty() && updated.get())
        {
            Bootstrap.vertx().<Void>executeBlocking(result ->
            {
                try
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(path,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", new HashMap<>(items)).put("item.instances", new HashMap<>(instances)).put("timestamps", new HashMap<>(timestamps)).put("flap.reasons", new HashMap<>(flapReasons)).encode().getBytes())));

                    updated.set(false);

                    dirty.set(true);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                result.complete();
            });
        }
    }
}
