/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*   Change Logs:
 *  Date			Author			    Notes
 *  28-Feb-2025		Bharat Chaudhari    MOTADATA-5233: Mail Subject constant added
 *  28-Feb-2025     Smit Prajapati      MOTADATA-4956: Rule Based Tagging
 *  5-Mar-2025      Bharat              MOTADATA-4740: Two factor authentication 2FA
 *  25-Mar-2025		Chopra Deven 		MOTADATA-5299: Added default messages for forecast policy
 *  20-Feb-2025     Pruthviraj          MOTADATA-4904: Message const added for netroute API
 *  7-Apr-2025      Vismit              MOTADATA-5613: Changed config discovery notification message
 */


package com.mindarray;

public final class InfoMessageConstants
{
    public static final String ENTITY_CREATED = "%s created successfully";
    public static final String AUDIT_ENTITY_CREATED = "%s %s created successfully";
    public static final String ENTITY_UPDATED = "%s updated successfully";
    public static final String AUDIT_ENTITY_UPDATED = "%s %s modified successfully";
    public static final String ENTITY_DELETED = "%s deleted successfully";
    public static final String AUDIT_ENTITY_DELETED = "%s %s deleted successfully";
    public static final String START_REQUESTED = "Request initiated to start %s";
    public static final String START_ALL_REQUESTED = "Request initiated to start %s";
    public static final String STOP_REQUESTED = "Request initiated to stop %s";
    public static final String STOP_ALL_REQUESTED = "Request initiated to stop %s";
    public static final String RESTART_REQUESTED = "Request initiated to restart %s";
    public static final String RESTART_ALL_REQUESTED = "Request initiated to restart %s";
    public static final String AGENT_RESET_REQUESTED = "Request initiated to reset agent";
    public static final String AGENT_RESET_ALL_REQUESTED = "Request initiated to reset agent";
    public static final String AGENT_CONFIGURATION_CHANGE_REQUESTED = "Request initiated to change agent configuration";
    public static final String AGENT_REGISTRATION_SUCCEEDED = "Agent %s registered successfully";
    public static final String AGENT_CONFIGURATION_CHANGE_ALL_REQUESTED = "Request initiated to change agent configuration in bulk";
    public static final String AGENT_CONFIGURATION_CHANGE_SUCCEEDED = "Agent configuration for %s changed successfully";
    public static final String AGENT_DISABLED_SUCCEEDED = "Agent %s disabled successfully";
    public static final String AGENT_ENABLED_SUCCEEDED = "Agent %s enabled successfully";
    public static final String START_SUCCEEDED = "%s %s started successfully";
    public static final String RESTART_SUCCEEDED = "%s %s restarted successfully";
    public static final String DELETE_SUCCEEDED = "%s %s deleted successfully";
    public static final String UPGRADE_SUCCEEDED = "%s %s upgraded successfully";
    public static final String STOP_SUCCEEDED = "%s %s stopped successfully";
    public static final String AGENT_DELETE_REQUESTED = "Request initiated to delete agent";
    public static final String AGENT_DOWNLOAD_LOG_REQUESTED = "Request initiated to download agent log";
    public static final String UPGRADE_REQUESTED = "Request initiated to upgrade %s in bulk";
    public static final String BULK_UPGRADE_REQUESTED = "Bulk %s upgrade requested";
    public static final String CREDENTIAL_PROFILE_TEST_SUCCEEDED = "Successful";
    public static final String LOGIN = "Please log in again";
    public static final String RESET_PASSWORD_SUCCEEDED = "Password for user %s has been successfully reset";
    public static final String RESET_2FA_SUCCEEDED = "Two Factor Authentication for user %s has been successfully reset";
    public static final String PASSWORD_EXPIRED = "Your password has expired";
    public static final String LDAP_SERVER_TEST_SUCCEEDED = "LDAP server: %s connection test successful";
    public static final String LDAP_SERVER_SYNC_SUCCEEDED = "LDAP server: %s synced successfully";
    public static final String LDAP_SERVER_SYNC_STARTED = "LDAP server sync started. You'll be notified when it's done.";
    public static final String MAIL_SERVER_TEST_SUCCEEDED = "Email server connection test successful";
    public static final String MAIL_SENT_SUCCEEDED = "Email sent to %s successfully";
    public static final String SMS_SENT_SUCCEEDED = "SMS sent to %s successfully";
    public static final String MAIL_SERVICE_RESTORED = "Email service restored successfully";
    public static final String SMS_SERVICE_RESTORED = "SMS service is restored successfully";
    public static final String INTEGRATION_SYNC_SUCCEEDED = "%s's attributes synced successfully";
    public static final String TEAMS_SERVICE_RESTORED = "Teams service is restored successfully";
    public static final String PROXY_SERVICE_RESTORED = "Proxy service is restored successfully";
    public static final String SMS_GATEWAY_TEST_SUCCEEDED = "SMS gateway tested successfully";
    public static final String PROXY_SERVER_TEST_SUCCEEDED = "Proxy server tested successfully";
    public static final String TWO_FACTOR_AUTHENTICATION_OTP_SENT_SUCCEEDED = "OTP has been sent to designated mail successfully";
    public static final String TWO_FACTOR_AUTHENTICATION_ALREADY_REGISTERED_USER = "This user is already registered with MFA. Please proceed with TOTP";

    public static final String EMAIL_ID_VALIDATED = "Email ID has been validated successfully.";
    public static final String OTP_MAIL_SUBJECT = "Your One-Time Password";
    public static final String SNMP_OID_GROUP_TEST_SUCCEEDED = "SNMP OID Group tested successfully";
    public static final String METRIC_PLUGIN_TEST_SUCCEEDED = "Metric Plugin tested successfully";
    public static final String RUNBOOK_PLUGIN_TEST_SUCCEEDED = "Runbook Plugin tested successfully";
    public static final String TOPOLOGY_PLUGIN_TEST_SUCCEEDED = "Topology Plugin tested successfully";
    public static final String INTEGRATION_TEST_SUCCEEDED = "%s integration test succeeded";
    public static final String INTEGRATION_PROFILE_TEST_SUCCEEDED = "Integration Profile tested successfully";
    public static final String INTEGRATION_TEST_FAILED = "%s integration test failed : %s";
    public static final String CREDENTIAL_PROFILE_TEST_PING_SUCCEEDED = "Host/IP %s is up";
    public static final String CREDENTIAL_PROFILE_TEST_PORT_CONNECTED = "Connected to port %s";
    public static final String DNS_SERVER_TEST_SUCCEEDED = "DNS server tested successfully";
    public static final String PING_IS_DISABLED = "ping is disabled for %s monitor";
    public static final String DISCOVERY_PING_SUCCEEDED = "Ping successful";
    public static final String DISCOVERY_INTERNET_CONNECTION_SUCCEEDED = "Internet connection successful";
    public static final String DISCOVERY_OBJECT_DISCOVERED = "Device discovery successful";
    public static final String DISCOVERY_PORT_REACHABLE = "Port is reachable";
    public static final String DISCOVERY_CREDENTIAL_PROFILE_SUCCEEDED = "Credential profile %s successfully assigned";
    public static final String LOG_FORWARDER_CONNECTION_ESTABLISHED_SUCCEEDED = "Log Forwarder connection established successfully";
    public static final String DISCOVERY_PROGRESS_DB_RESULT_SAVE_MESSAGE = "Please wait while Motadata processes the discovery results";
    public static final String DISCOVERY_RESULT_UPDATE_SUCCEEDED = "Discovery profile %s updated successfully";
    public static final String DISCOVERY_OBJECT_CALCULATION_STARTED = "Calculating discovery range";
    public static final String DISCOVERY_ABORT_SUCCEEDED = "Discovery %s aborted successfully";
    public static final String DISCOVERY_START_SUCCEEDED = "Discovery %s started successfully";
    public static final String DISCOVERY_APP_START_SUCCEEDED = "Application Discovery started successfully";
    public static final String DISCOVERY_APP_SUCCEEDED = "Application Discovery for %s successfully completed on %s";
    public static final String OBJECT_PROVISION_START_SUCCEEDED = "Object provisioning started successfully";
    public static final String OBJECT_ARCHIVED_SUCCEEDED = "Object archived successfully";
    public static final String REDISCOVER_JOB_OBJECT_FOUND = "%s found on %s";
    public static final String REDISCOVER_JOB_OBJECT_PROVISIONED = "%s provisioned on %s";
    public static final String REDISCOVER_JOB_OBJECT_REDISCOVERED = "%s rediscovered on %s";
    public static final String SNMP_TRAP_AUTO_CLEAR_BY_OID = "Trap %s entered into clear state due to %s trap";
    public static final String SNMP_TRAP_AUTO_CLEAR_BY_SYSTEM = "Trap %s was auto cleared by the motadata system";
    public static final String OBJECT_PROVISION_SUCCEEDED = "Monitor %s provisioned successfully...";
    public static final String METRIC_VM_PROVISION_SUCCEEDED = "VM %s provisioned successfully...";
    public static final String METRIC_ACCESS_POINT_PROVISION_SUCCEEDED = "Access Point %s provisioned successfully...";
    public static final String SNMP_DEVICE_CATALOG_ASSIGN_SUCCEEDED = "Monitor assigned to SNMP Device Catalog successfully";
    public static final String OBJECT_METRIC_UPDATE_SUCCEEDED = "Metrics for monitor %s updated successfully";
    public static final String ASSIGN_SUCCEEDED = "%s assigned successfully";
    public static final String UNASSIGNED_SUCCEEDED = "%s unassigned successfully";
    public static final String TOPOLOGY_ABORT_SUCCEEDED = "Topology aborted successfully";
    public static final String EVENT_TRACKER_PLUGIN_PROCESS_STARTED = "Plugin engine process %s started at %s";
    public static final String EVENT_TRACKER_PLUGIN_PROCESS_FINISHED = "Plugin engine process %s exited at %s";
    public static final String EVENT_TRACKER_PROCESS_STARTED = "Process %s started at %s";
    public static final String EVENT_TRACKER_PROCESS_FINISHED = "Process %s exited at %s";
    public static final String EVENT_TRACKER_CHILD_PROCESS_STARTED = "Process %s started at %s for event %s";
    public static final String EVENT_TRACKER_CHILD_PROCESS_FINISHED = "Process %s exited at %s for event %s";
    public static final String EVENT_TRACKER_WORKER_THREAD_ASSIGNED = "Worker thread %s assigned at %s";
    public static final String EVENT_TRACKER_PING_CHECKED = "Ping check ran at %s";
    public static final String EVENT_TRACKER_PORT_CHECKED = "Port check ran at %s";
    public static final String EVENT_TRACKER_EVENT_QUEUED = "Event queued at %s";
    public static final String EVENT_TRACKER_ACKNOWLEDGEMENT_RECEIVED = "Event acknowledgement received from %s at %s";
    public static final String EVENT_TRACKER_EVENT_DISPATCHED = "Event dispatched to %s at %s";
    public static final String EVENT_TRACKER_EVENT_RECEIVED = "Event received by %s at %s";
    public static final String EVENT_TRACKER_EVENT_QUALIFIED = "Event qualified for running at %s";
    public static final String EVENT_TRACKER_RESPONSE_SENT = "Response sent at %s";
    public static final String EVENT_TRACKER_RESPONSE_RECEIVED = "Response received at %s";
    public static final String EVENT_TRACKER_EVENT_STARTED = "Event started at %s";
    public static final String EVENT_TRACKER_EVENT_COMPLETED = "Event completed at %s with status %s";
    public static final String EVENT_TRACKER_CHILD_EVENT_FORKED = "Child event %s forked at %s";
    public static final String POLL_EVENT_QUEUED = "Polling request is queued, Please wait...";
    public static final String WAN_LINK_CONFIGURATION_INITIALIZED = "Initializing WAN-Link configuration on source: %s";
    public static final String EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED = "Event dispatched for query id %d at %s";
    public static final String EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED = "Event received with query id %d with status %s and progress %d, overall progress %d at %s";
    public static final String AUDIT_USER_LOGIN = "User %s logged in from the remote machine [%s]";
    public static final String AUDIT_USER_LOGOUT = "User %s logged out from the remote machine [%s]";
    public static final String DISCOVERY_NOTIFICATION_MESSAGE = "Motadata Notification: \n Discovery Name : %s, \n Total Objects : %s, \n Discovered Objects : %s, \n Failed Objects : %s, \n Discovery Progress : %s ";
    public static final String DISCOVERY_NOTIFICATION_SUBJECT = "Motadata Notification \n Discovery: %s Completed";
    public static final String REDISCOVERY_NOTIFICATION_SUBJECT = "Motadata Notification \n Rediscover Job: %s Completed";
    public static final String REDISCOVERY_NOTIFICATION_MESSAGE = "Motadata Notification: \n Rediscover Job : %s, \n Rediscovered Objects : %s";
    public static final String TOPOLOGY_NOTIFICATION_SUBJECT = "Motadata Notification \n Topology Discovery Job Completed";
    public static final String DATABASE_BACKUP_NOTIFICATION_MESSAGE = "Hi User, \n The %s backup is completed successfully.  ";
    public static final String DATABASE_BACKUP_COMPLETED_NOTIFICATION_SUBJECT = "Motadata Notification \n Backup Job : %s Completed";

    public static final String RUNBOOK_EXECUTION_NOTIFICATION_SUBJECT = "Motadata Notification \n %s Runbook Execution Completed.";
    public static final String RUNBOOK_EXECUTION_NOTIFICATION_MESSAGE = "The runbook scheduler - %s of %s has been successfully executed.";

    public static final String COMPLIANCE_POLICY_NOTIFICATION_SUBJECT_SUCCESSFUL = "Successful – %s Compliance Policy";
    public static final String COMPLIANCE_POLICY_NOTIFICATION_SUBJECT_FAILED = "Failed – %s Compliance Policy";
    public static final String COMPLIANCE_POLICY_NOTIFICATION_SUCCESSFUL_MESSAGE = "Compliance policy %s was scanned on %s by %s, achieving a %d compliance score. Scan results: %d vulnerable, %d poor, %d moderate, and %d secure.";

    public static final String REPORT_NOTIFICATION_SUBJECT = "Report: %s";
    public static final String WIDGET_SHARE_NOTIFICATION_SUBJECT = "Motadata: ${visualization.name}";
    public static final String ALERT_SHARE_NOTIFICATION_SUBJECT = "Motadata: ${policy.name}";
    public static final String METRIC_EXPLORER_SHARE_NOTIFICATION_SUBJECT = "Motadata: Metric Explorer";
    public static final String SEARCH_QUERY_SHARE_NOTIFICATION_SUBJECT = "Motadata: Search";
    public static final String INTEGRATION_SUCCESSFUL_NOTIFICATION_SUBJECT = "Motadata %s Integration – Successful";
    public static final String TOPOLOGY_NOTIFICATION_MESSAGE = "Motadata Notification: \n Entry Points : %s \n Discovery Protocols : %s \n Total Running time : %s (sec)";
    public static final String DISCOVERY_USER_NOTIFICATION_MESSAGE = "Discovery Completed: \n Discovery Name : %s, \n Total Objects : %s, \n Discovered Objects : %s, \n Failed Objects : %s, \n Discovery Progress : %s";
    public static final String METRIC_POLLING_USER_NOTIFICATION_MESSAGE = "%s %s %s";//monitorname metricname pollingfailedreason
    public static final String REDISCOVERY_USER_NOTIFICATION_MESSAGE = "Rediscovery Completed for %s: \n Rediscover Job : %s, \n Rediscovered Objects : %s";
    public static final String TEMPORARY_PASSWORD_CHANGE_MESSAGE = "A request has been received to change the password for your Motadata account %s with Temporary Password %s";
    public static final String TEMPORARY_PASSWORD_CHANGE_SUBJECT = "Request for password change";
    public static final String OAUTH_REQUESTING_ACCESS_TOKEN_MESSAGE = "Requesting an access token";

    public static final String MOTADATA_ENGINE_START_SUCCEEDED = "howdy, motadata engine %s started successfully... :)";
    public static final String METRIC_THRESHOLD_POLICY_DEFAULT_MESSAGE = "%s %s over %s was %s %s during last %s mins with %s abnormality occurrences.";
    public static final String METRIC_AIOPS_ANOMALY_POLICY_DEFAULT_MESSAGE = "%s%s of %s has entered into %s state, as %s of samples were %s predicted value.";
    public static final String METRIC_AIOPS_FORECAST_POLICY_TRIGGER_MESSAGE = "%s %s is trending towards a %s state, expected to surpass %s within %s";
    public static final String METRIC_AIOPS_FORECAST_POLICY_CLEAR_MESSAGE = "%s %s is no longer expected to breach the %s threshold of %s within forecast period";
    public static final String METRIC_AIOPS_BASELINE_POLICY_DEFAULT_MESSAGE = "%s%s of %s has entered into %s state, as current metric value %s %s the auto-detected baseline %s";
    public static final String NETROUTE_POLICY_HOP_BY_HOP_DEFAULT_MESSAGE = "%s has entered into %s state indicating one or more hops exceeded threshold value %s %s";
    public static final String METRIC_THRESHOLD_POLICY_AUTO_CLEAR_MESSAGE = "%s %s over %s has been Auto Cleared";
    public static final String POLICY_UNKNOWN_DEFAULT_MESSAGE = "%s %s of %s has entered into %s state due to %s";
    public static final String AVAILABILITY_POLICY_DEFAULT_MESSAGE = "%s %s is now %s";
    public static final String POLICY_DEFAULT_EMAIL_SUBJECT = "$$$severity$$$ - $$$object.name$$$";
    public static final String POLICY_DEFAULT_SMS = "$$$severity$$$ - $$$object.name$$$ \n $$$policy.message$$$";
    public static final String POLICY_ACKNOWLEDGED = "Policy %s acknowledged by %s";
    public static final String POLICY_UNACKNOWLEDGED = "Policy %s unacknowledged by %s";
    public static final String POLICY_NOTED = "Policy %s noted by %s. Policy Note : %s ";
    public static final String POLICY_CLEARED = "Policy %s cleared by %s";
    public static final String POLICY_SUPPRESSED = "Policy %s suppressed by %s till %s";
    public static final String TRAP_ACKNOWLEDGED = "Trap %s acknowledged by %s";
    public static final String TRAP_UNACKNOWLEDGED = "Trap %s unacknowledged by %s";
    public static final String PORT_IS_ALREADY_IN_USE = "Port %s is already in use.";
    public static final String POLICY_EVENT_DEFAULT_EMAIL_SUBJECT = "$$$severity$$$ - Policy $$$policy.name$$$ has been triggered";
    public static final String POLICY_EVENT_DEFAULT_SMS = "$$$severity$$$ - Policy $$$policy.name$$$ \n $$$policy.message$$$";
    public static final String POLICY_NETROUTE_DEFAULT_EMAIL_SUBJECT = "$$$severity$$$ - $$$netroute.name$$$";
    public static final String MOTADATA_ENGINE_RESTART_SUCCEEDED = "Motadata engine %s restarted";
    public static final String RESTART_REQUIRED_TO_UPDATE_LICENSE = "License updated successful. In order to apply the new license and bring it into effect, please restart the AIOps server";

    public static final String LICENSE_DAILY_QUOTA_LIMIT_REACHED = "Attention – %s Volume Limit Exceeded";
    public static final String LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED = "Attention – Approaching %s Volume Daily Limit";
    public static final String LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED_NOTIFICATION_SMS = "Hi User, \n Today's $$$event.type$$$ volume has reached $$$event.user.limit$$$GB, which is over 90% of your $$$event.license.limit$$$GB Daily Quota.";
    public static final String LICENSE_DAILY_QUOTA_LIMIT_REACHED_NOTIFICATION_SMS = "Hi User," + "\n" + "Your daily $$$event.type$$$ volume has exceeded the allocated daily limit of $$$event.license.limit$$$GB, reaching 100%.";

    public static final String TAG_RULE_RUN_SUCCEEDED = "Tags have been applied Successfully!";
    public static final String TAG_RULE_RUN_FAILED = "Failed to Apply Tags!";
    public static final String TAG_RULE_CONDITION_SATISFIED = "Rule Condition satisfied";
    public static final String TAG_RULE_CONDITION_NOT_SATISFIED = "Rule Condition not satisfied";


    //Configuration specific constants

    public static final String CONFIG_REQUEST_QUEUED = "Config %s operation queued, Please wait...";
    public static final String STORAGE_PROFILE_TEST_SUCCEEDED = "Storage Profile tested successfully";
    public static final String CONFIG_BASELINE_ASSIGNED_SUCCESSFULLY = "Baseline version successfully attached";
    public static final String CONFIG_BASELINE_REMOVED_SUCCESSFULLY = "Baseline version removed successfully";
    public static final String CONFIG_BACKUP_FILE_CONTENT_RETRIEVED_SUCCESSFULLY = "Backup file content retrieved successfully";
    public static final String CONFIG_DISCOVERY_NOTIFICATION_SUBJECT = "Information – Network Config %s Executed";
    public static final String CONFIG_DISCOVERY_NOTIFICATION_MESSAGE = "The Network Config ${config.operation} was performed on ${timestamp}, with ${succeeded} devices successful and ${failed} devices failed, by ${user.name}.";

    public static final String SSH_CONNECTION_CLOSED = "Connection to %s is closed";


    private InfoMessageConstants()
    {
    }

}
