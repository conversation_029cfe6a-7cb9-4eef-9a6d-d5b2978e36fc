/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  25-Mar-2025     Smit Prajapati           MOTADATA-5435: Flow back-pressure mechanism.
 *  23-May-2025     Smit Prajapati           MOTADATA-6334: added tmp_asa_bi_flow key as true for nflow.
 */

package com.mindarray.flow;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.store.FlowSettingsConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.ProcessUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.CompositeFuture;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

/**
 * Listens for incoming network flow data and forwards it for processing.
 * <p>
 * The FlowListener is responsible for receiving network flow data from various sources,
 * such as sFlow and NetFlow, and forwarding it to the FlowProcessor for further processing.
 * It acts as the entry point for flow data into the system.
 * <p>
 * Key responsibilities of this class include:
 * <ul>
 *   <li>Starting and managing flow collector processes for different flow types (sFlow, NetFlow)</li>
 *   <li>Configuring the flow collectors based on settings from the FlowSettingsConfigStore</li>
 *   <li>Managing the lifecycle of flow collector processes</li>
 *   <li>Handling port configurations for different flow protocols</li>
 *   <li>Ensuring proper cleanup of resources when stopping</li>
 * </ul>
 * <p>
 * The FlowListener supports multiple flow protocols and can be configured to listen on
 * different ports for each protocol. It also supports BGP-enabled flow collection for
 * enhanced routing information.
 * <p>
 * This class is deployed as a Vert.x verticle and integrates with the event bus system
 * for communication with other components.
 */
public class FlowListener extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlowListener.class, GlobalConstants.MOTADATA_FLOW, "Flow Listener");
    private static final int SFLOW_PORT = FlowSettingsConfigStore.getStore().getsFlowPort();
    private static final int NET_FLOW_PORT = FlowSettingsConfigStore.getStore().getNetFlowPort();
    private static final int BGP_SFLOW_PORT = FlowSettingsConfigStore.getStore().getBGPsFlowPort();
    private static final int BGP_NET_FLOW_PORT = FlowSettingsConfigStore.getStore().getBGPNetFlowPort();

    /**
     * Initializes and starts the flow listener processes for different flow types.
     * <p>
     * This method starts the flow collector processes for different flow protocols:
     * <ul>
     *   <li>sFlow: A sampling technology for monitoring high-speed networks</li>
     *   <li>NetFlow: A network protocol for collecting IP traffic information</li>
     * </ul>
     * <p>
     * If BGP (Border Gateway Protocol) flow collection is enabled in the settings,
     * additional BGP-enabled flow collectors are started for enhanced routing information.
     * <p>
     * The method uses the startFlowListener helper method to start each flow collector
     * process with the appropriate configuration. It handles the asynchronous nature of
     * process startup using Vert.x Futures and CompositeFuture to coordinate multiple
     * process startups.
     * <p>
     * The flow collector processes are configured based on settings from the
     * FlowSettingsConfigStore, including port numbers and BGP enablement.
     *
     * @param promise the promise to complete when all flow listeners have started
     * @throws Exception if an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        CompositeFuture.all
                (
                        startFlowListener("sfacctd", "sFlow", SFLOW_PORT),
                        startFlowListener("nfacctd", "NetFlow", NET_FLOW_PORT)
                ).onComplete(result ->
        {
            if (result.succeeded())
            {
                if (FlowSettingsConfigStore.getStore().getBGPFlowEnabled())
                {
                    LOGGER.info("BGP enabled: " + FlowSettingsConfigStore.getStore().getBGPFlowEnabled());

                    CompositeFuture.all
                            (
                                    startFlowListener("sfacctd", "bgp_sFlow", BGP_SFLOW_PORT),
                                    startFlowListener("nfacctd", "bgp_NetFlow", BGP_NET_FLOW_PORT)

                            ).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete();
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to start flow engine, reason: %s", asyncResult.cause()));

                            promise.fail(asyncResult.cause());
                        }

                    });
                }
                else
                {
                    promise.complete();
                }

            }
            else
            {
                LOGGER.fatal(String.format("failed to start flow engine, reason: %s", result.cause()));

                promise.fail(result.cause());
            }
        });
    }

    /**
     * Starts a specific flow listener process with the appropriate configuration.
     * <p>
     * This method creates and starts a flow collector process for a specific flow type
     * (such as sFlow or NetFlow) on the specified port. It performs the following steps:
     * <ul>
     *   <li>Creates a configuration file for the flow collector process</li>
     *   <li>Configures parameters such as port, log settings, and aggregation settings</li>
     *   <li>Starts the flow collector process with the generated configuration</li>
     *   <li>Monitors the process to ensure it starts successfully</li>
     *   <li>Stores the process ID for later management</li>
     * </ul>
     * <p>
     * The method handles different flow types with appropriate configurations, including
     * special handling for BGP-enabled flow collectors. It uses the pmacct tools (sfacctd
     * for sFlow and nfacctd for NetFlow) as the underlying flow collection processes.
     * <p>
     * The configuration includes settings for:
     * <ul>
     *   <li>Network ports to listen on</li>
     *   <li>Output format and location</li>
     *   <li>Aggregation parameters and intervals</li>
     *   <li>Logging configuration</li>
     *   <li>Protocol-specific settings</li>
     * </ul>
     *
     * @param process  the process type to start (sfacctd for sFlow, nfacctd for NetFlow)
     * @param flowType the type of flow data to collect (sFlow, NetFlow, bgp_sFlow, bgp_NetFlow)
     * @param port     the port number to listen on for flow data
     * @return a Future that completes when the flow listener process has started
     */
    private Future<Void> startFlowListener(String process, String flowType, int port)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var configFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + flowType + ".conf");

            FileUtils.deleteQuietly(configFile);    // every time rewrite the config file

            var parameters = new ArrayList<String>();

            parameters.add("plugins: print[" + flowType + "]");

            parameters.add(process + "_port:" + port);

            parameters.add("zmq_address: tcp://localhost:" + CommonUtil.getEventSubscriberPort());

            parameters.add("logfile: " + GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + Logger.LOG_DIRECTORY + GlobalConstants.PATH_SEPARATOR + GlobalConstants.MOTADATA_FLOW + GlobalConstants.PATH_SEPARATOR + flowType + "_" + System.currentTimeMillis() + ".log");

            parameters.add("loglevel: " + MotadataConfigUtil.getFlowLogLevel());

            parameters.add("snaplen: 65535");

            parameters.add("use_ip_next_hop: true");

            parameters.add("classifier_num_roots: 10240");   //https://github.com/pmacct/pmacct/issues/741

            if ("bgp_sFlow".equals(flowType) || "bgp_NetFlow".equals(flowType))
            {
                parameters.add("aggregate[" + flowType + "]: src_host, dst_host, peer_src_ip, peer_dst_ip, in_iface, out_iface, src_port, dst_port, proto, sampling_rate, flows, tcpflags, tag, export_proto_version, tos, class, src_as, dst_as, peer_dst_as");
            }
            else
            {
                parameters.add("aggregate[" + flowType + "]: src_host, dst_host, peer_src_ip, peer_dst_ip, in_iface, out_iface, src_port, dst_port, proto, sampling_rate, flows, tcpflags, tag, export_proto_version, tos, class");
            }

            parameters.add("print_output_file[" + flowType + "]:" + GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.FLOW_CACHE_DIR + GlobalConstants.PATH_SEPARATOR + "%Y%m%d%H%M");

            parameters.add("print_output[" + flowType + "]: json");

            if (FlowSettingsConfigStore.getStore().getItems().isEmpty())
            {
                parameters.add("print_history[" + flowType + "]: " + MotadataConfigUtil.getFlowEngineAggregationTimerMinutes() + "m");
            }
            else
            {
                parameters.add("print_history[" + flowType + "]: " + FlowSettingsConfigStore.getStore().getFlowEngineAggregationTimerMinutes() + "m");
            }

            parameters.add("print_history_roundoff[" + flowType + "]: m");

            parameters.add("print_refresh_time[" + flowType + "]: " + MotadataConfigUtil.getFlowEngineRefreshTimerSeconds());

            parameters.add("print_output_file_append: true");

            parameters.add("timestamps_secs: true");

            parameters.add("plugin_pipe_zmq: true");

            parameters.add("print_num_protos: true");

            parameters.add("pre_tag_map: " + GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "tag.rule");

            parameters.add("print_cache_entries[" + flowType + "]: " + MotadataConfigUtil.getFlowEngineCacheEntries());

            switch (flowType)
            {
                case "NetFlow" ->
                {
                    parameters.add("nfacctd_time_new: true");       //https://github.com/pmacct/pmacct/issues/680

                    parameters.add("tmp_asa_bi_flow : true");
                }

                case "bgp_NetFlow" ->
                {
                    parameters.add("tmp_asa_bi_flow : true");

                    parameters.add("nfacctd_net : netflow");

                    parameters.add("nfacctd_as : netflow");

                    parameters.add("nfacctd_peer_as : true");

                    parameters.add("nfacctd_time_new: true");       //https://github.com/pmacct/pmacct/issues/680
                }

                case "sFlow" ->
                        parameters.add("sfacctd_time_new: true");       //https://github.com/pmacct/pmacct/issues/680

                case "bgp_sFlow" ->
                {
                    parameters.add("sfacctd_net : sflow");

                    parameters.add("sfacctd_as : sflow");

                    parameters.add("sfacctd_peer_as : true");

                    parameters.add("sfacctd_time_new: true");       //https://github.com/pmacct/pmacct/issues/680
                }
            }

            parameters.add("daemonize: true");                      //need to Daemonize the process, for multiple process spawning

            parameters.add("pidfile: " + GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + flowType);  // we have to store Daemonize process PIDs, to track the daemonize process

            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().send("test.flow.config", parameters);
            }

            FileUtils.writeLines(configFile, parameters);

            LOGGER.info(process + " config file:" + FileUtils.readFileToString(configFile, StandardCharsets.UTF_8));

            var arguments = new ArrayList<String>();

            arguments.add(process);

            arguments.add("-f");

            arguments.add(flowType + ".conf");

            new Thread(() -> ProcessUtil.startDaemon(process, arguments, GlobalConstants.CURRENT_DIR).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            promise.complete();

                        }

                        else
                        {
                            LOGGER.fatal(String.format("failed to start flow engine %s, reason: %s", flowType, result.cause().getMessage()));

                            promise.fail(result.cause());
                        }
                    }
            ), flowType).start();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Stops all flow listener processes and cleans up resources.
     * <p>
     * This method is called when the verticle is undeployed. It stops all running
     * flow collector processes (sFlow, NetFlow, and their BGP-enabled variants)
     * by reading their process IDs from files and terminating them.
     * <p>
     * The method ensures a clean shutdown by forcibly terminating any running
     * flow collector processes to prevent resource leaks and ensure that the
     * network ports are properly released.
     * <p>
     * Error handling is implemented to ensure that the method completes even if
     * some processes cannot be terminated, with errors being logged for diagnostic
     * purposes.
     *
     * @throws Exception if an error occurs during shutdown
     */
    @Override
    public void stop() throws Exception
    {

        try
        {
            if (Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "sFlow"))
            {
                ProcessHandle.of(CommonUtil.getLong(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "sFlow")))).orElseThrow(() -> new IllegalArgumentException("Invalid PID")).destroyForcibly();
            }

            if (Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "NetFlow"))
            {


                ProcessHandle.of(CommonUtil.getLong(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "NetFlow")))).orElseThrow(() -> new IllegalArgumentException("Invalid PID")).destroyForcibly();
            }

            if (Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "bgp_sFlow"))
            {


                ProcessHandle.of(CommonUtil.getLong(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "bgp_sFlow")))).orElseThrow(() -> new IllegalArgumentException("Invalid PID")).destroyForcibly();
            }

            if (Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "bgp_NetFlow"))
            {

                ProcessHandle.of(CommonUtil.getLong(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "bgp_NetFlow")))).orElseThrow(() -> new IllegalArgumentException("Invalid PID")).destroyForcibly();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
