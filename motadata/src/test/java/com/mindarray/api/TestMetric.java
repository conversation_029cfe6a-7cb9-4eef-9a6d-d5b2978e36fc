/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricCacheStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.MetricPolicyCacheStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CHANGE_NOTIFICATION;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestMetric
{
    private static final Logger LOGGER = new Logger(TestMetric.class, MOTADATA_API, "Test Metric");

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testDeleteApplication(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", GlobalConstants.ID);

        Assertions.assertFalse(objects.isEmpty());

        var metrics = MetricConfigStore.getStore().getItemsByObject(objects.getLong(0));

        Assertions.assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        var id = metrics.getFirst().getLong(Metric.METRIC_OBJECT);

        var type = NMSConstants.Type.IBM_DB2.getName();

        TestAPIUtil.delete(TestAPIConstants.METRIC_API_ENDPOINT + "/" + id + "?" + Metric.METRIC_TYPE + "=" + type, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(HttpStatus.SC_OK, response.statusCode());

            var result = response.bodyAsJsonObject();

            Assertions.assertNotNull(result);

            assertEquals(HttpStatus.SC_OK, result.getInteger(APIConstants.RESPONSE_CODE));

            var retries = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(2000, timer ->
            {
                var items = new JsonArray(MetricConfigStore.getStore().getItemsByObject(objects.getLong(0))
                        .stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_TYPE).equalsIgnoreCase(type))
                        .map(JsonObject::mapFrom)
                        .collect(Collectors.toList()));

                if (items.isEmpty())
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
                else if (retries.get() > 10)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow(new Exception("failed to unprovision application"));
                }
                else
                {
                    retries.getAndIncrement();
                }
            });
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetMetricInvalidRequestParameters(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_API_ENDPOINT + "?filter=" + new JsonObject(), testContext.succeeding(response ->
        {
            assertEquals(SC_BAD_REQUEST, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

            assertEquals(ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS, body.getString(MESSAGE));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetMetric(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_API_ENDPOINT + "?filter=" + new JsonObject().put(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS.getName()),
                testContext.succeeding(response ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var entities = body.getJsonArray(RESULT);

                    Assertions.assertNotNull(entities);

                    Assertions.assertFalse(entities.isEmpty());

                    testContext.completeNow();
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testStoreInit(VertxTestContext testContext)
    {
        MetricConfigStore.getStore().updateStore().onComplete(result ->
        {
            if (result.failed())
            {
                testContext.failNow(result.cause());
            }
            else
            {
                var item = MetricConfigStore.getStore().getItem();

                Assertions.assertNotNull(item);

                Assertions.assertFalse(item.isEmpty());

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteBaseInstanceMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

//        var plugins = new JsonArray(NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.keySet().stream().map(CommonUtil::getString).collect(Collectors.toList()));

//        var metrics = new JsonArray(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, plugins).stream().filter(object -> !ObjectConfigStore.getStore().getItem(JsonObject.mapFrom(object).getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("**********")).collect(Collectors.toList()));

        var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_PROCESS.getName());

        Assertions.assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        var metric = metrics.getJsonObject(0);

        LOGGER.info(testInfo.getTestMethod().get().getName() + ": metric: " + metric.encode());

        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            if (message.body() != null && message.body().containsKey(EventBusConstants.CHANGE_NOTIFICATION_TYPE) && message.body().getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.DELETE_METRIC.name()))
            {
                try
                {
                    Assertions.assertFalse(MetricCacheStore.getStore().existMetric(message.body().getLong(ID)));

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_UNPROVISION,
                new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.ID, metric.getLong(GlobalConstants.ID)).put(User.USER_NAME, DEFAULT_USER));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteApplicationMetric(VertxTestContext testContext)
    {
        var plugins = new JsonArray(NMSConstants.APPLICATION_PLUGINS.stream().map(CommonUtil::getString).collect(Collectors.toList()));

        var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, plugins);

        Assertions.assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        var metric = metrics.getJsonObject(0);

        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            if (message.body() != null && message.body().containsKey(EventBusConstants.CHANGE_NOTIFICATION_TYPE) && message.body().getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.DELETE_METRIC.name()))
            {
                try
                {
                    Assertions.assertFalse(MetricCacheStore.getStore().existMetric(message.body().getLong(ID)));

                    testContext.completeNow();

                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_UNPROVISION,
                new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(GlobalConstants.ID, metric.getLong(GlobalConstants.ID)).put(User.USER_NAME, DEFAULT_USER));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteInterface(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************"));

        LOGGER.trace(String.format("Item : %s", item.encode()));

        Assertions.assertNotNull(item);

        var id = item.getLong(ID);

        var metricInstance = item.getJsonArray(NMSConstants.DISCOVERED_OBJECTS).getJsonObject(0).getString(NMSConstants.INTERFACE);

        LOGGER.trace(String.format("metricInstance : %s", metricInstance));

        TestUtil.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, id).put(NMSConstants.METRIC_INSTANCES, metricInstance).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_OBJECT));

        var counter = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(2 * 1000, timer ->
        {
            if (counter.incrementAndGet() >= 10)
            {
                testContext.failNow("time out, could not complete test case in given time");
            }
            else
            {
                var policies = MetricPolicyCacheStore.getStore().getItems(new JsonArray().add(id));

                LOGGER.trace(String.format("policies size : %s", policies.size()));

                if (!policies.isEmpty())
                {
                    testContext.completeNow();
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testUpdateAllSuspendedMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("************");

        var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.LINUX_FILE.getName()));

        Assertions.assertNotNull(item);

        item.put(Metric.METRIC_STATE, STATUS_SUSPEND);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + id,
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), handler ->
                            {
                                Assertions.assertEquals(STATUS_SUSPEND, MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.LINUX_FILE.getName())).getString(Metric.METRIC_STATE));

                                var context = new JsonObject().put(Metric.METRIC_STATE, "ENABLE")
                                        .put(Metric.METRIC_NAME, "Linux File")
                                        .put(Metric.METRIC_POLLING_TIME, 5000001).put(TIMEOUT, 60)
                                        .put("ids", new JsonArray().add(id).add(ObjectConfigStore.getStore().getItemByIP("************")));

                                TestAPIUtil.post(TestAPIConstants.METRIC_API_ENDPOINT,
                                        context,
                                        testContext.succeeding(result -> testContext.verify(() ->
                                                {
                                                    TestAPIUtil.assertValidResponseTestResult(result, LOGGER, testInfo.getTestMethod().get().getName());

                                                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), asyncHandler ->
                                                    {
                                                        LOGGER.debug(String.format("linux file object %s ", MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.LINUX_FILE.getName())).encode()));

                                                        Assertions.assertEquals(STATUS_SUSPEND, MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.LINUX_FILE.getName())).getString(Metric.METRIC_STATE));

                                                        Assertions.assertEquals(5000001, MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.LINUX_FILE.getName())).getInteger(Metric.METRIC_POLLING_TIME));

                                                        testContext.completeNow();
                                                    });
                                                })
                                        ));
                            });
                        })
                ));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testUpdateNetworkInterfacePollingTime(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("************");

        var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

        Assertions.assertNotNull(item);

        item.put(Metric.METRIC_POLLING_TIME, 90);

        TestAPIUtil.put(TestAPIConstants.METRIC_API_ENDPOINT + "/" + id,
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)),
                testContext.succeeding(response -> testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var retries = new AtomicInteger();

                            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
                            {
                                try
                                {
                                    if (MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).getInteger(Metric.METRIC_POLLING_TIME) == 90)
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                    else if (retries.get() > 5)
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.failNow("Metric Polling time is not yet updated even after 5 retries");
                                    }
                                    else
                                    {
                                        retries.incrementAndGet();
                                    }
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        })
                ));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testDeleteApplicationHavingRunbookPlugin(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::132");

        Assertions.assertNotNull(id);

        TestAPIUtil.delete(TestAPIConstants.METRIC_API_ENDPOINT + "/" + id + "?" + Metric.METRIC_TYPE + "=" + NMSConstants.Type.POSTGRESQL.getName(), testContext.succeeding(response -> testContext.verify(() ->
        {
            try
            {
                assertEquals(SC_BAD_REQUEST, response.statusCode());

                var result = response.bodyAsJsonObject();

                Assertions.assertNotNull(result);

                assertEquals(SC_BAD_REQUEST, result.getInteger(APIConstants.RESPONSE_CODE));

                Assertions.assertTrue(result.getJsonObject(RESULT).containsKey(APIConstants.Entity.RUNBOOK_PLUGIN.getName()));

                Assertions.assertFalse(result.getJsonObject(RESULT).getJsonArray(APIConstants.Entity.RUNBOOK_PLUGIN.getName()).isEmpty());

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        })));
    }
}
