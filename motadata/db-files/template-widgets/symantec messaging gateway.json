[{"_type": "0", "id": 10000000001847, "visualization.name": "Connected Users", "visualization.description": "Connected Users", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.connected.users", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.connected.users"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.connected.users", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Connected Users", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.connected.users.last"}]}}}}, {"_type": "0", "id": 10000000001848, "visualization.name": "CPU", "visualization.description": "CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.cpu.percent", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001849, "visualization.name": "Disk Used", "visualization.description": "Disk Used", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.disk.used.percent", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.disk.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.disk.used.percent", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.disk.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001850, "visualization.name": "Memory", "visualization.description": "Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.physical.memory.percent", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.physical.memory.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.physical.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.physical.memory.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001851, "visualization.name": "Running Process", "visualization.description": "Running Process", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.running.processes", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.running.processes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.running.processes", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "Running Process", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.running.processes.last"}]}}}}, {"_type": "0", "id": 10000000001852, "visualization.name": "Swap Memory", "visualization.description": "Swap Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.swap.memory.bytes", "aggregator": "last", "entity.type": "Monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "smg.swap.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.swap.memory.bytes", "icon": {"name": "swap", "placement": "prefix"}}, "header": {"title": "Swap Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.swap.memory.bytes.last"}]}}}}, {"id": 10000000001853, "visualization.name": "Queue Messages Per Second", "visualization.description": "Queue Messages Per Second", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.queue~messages.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000001854, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.user.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.system.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.idle.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000001855, "visualization.name": "Disk Volume Details", "visualization.description": "Disk Volume Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.disk.volume~free.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.disk.volume~used.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~capacity.bytes.last", "alias": "system.disk.volume~capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~free.bytes.last", "alias": "system.disk.volume~free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~used.bytes.last", "alias": "system.disk.volume~used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000001856, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.physical.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.virtual.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.cached.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}]