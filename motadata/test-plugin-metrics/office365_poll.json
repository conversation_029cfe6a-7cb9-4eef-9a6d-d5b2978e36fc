{"127.0.0.1": {"result": {"office365.consumed.units": 1, "office365.suspended.units": 1, "office365.warning.units": 0, "office365.licenses": 1, "office365.users": 100, "office365.user": [{"office365.user": "45979746-b28e-4071-bb5a-db8a388b6f9e", "office365.user.name": "Aachal Panchal", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee <PERSON><PERSON> Developer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "63882ec5-2740-4351-8d5a-5a32d598e517", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "8f569ef7-b221-4a23-aa49-d451bcd36928", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "0fad8a08-9368-44e5-9ea9-612606922cde", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d58951a0-79cb-4b6d-a2bf-259f808bb748", "office365.user.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "5ec30fa2-f20f-4bf1-ac98-9a9f354cea5e", "office365.user.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "b53fa7fa-cc90-41fb-8616-f4df7eb588f0", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Account Executive", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d158dab6-8c22-46de-922c-f59d6e7b6be2", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "2813d9b2-6f1e-44c7-ac9e-9b6e7e684d83", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "f6ea8d1c-2f58-4f77-89b4-43bde5f806df", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e8f538a0-f433-444d-9a00-eaa99211db34", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Presales Consultant", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "8bc57eff-84d7-4967-bff1-15a26adeac48", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "ad86079a-644a-405a-9693-1f94ce5c9873", "office365.user.name": "Alpesh Dhamelia", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "413a34de-6159-4e1b-8018-0efe8bc1480d", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Manager - Product Marketing", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "7c93e0e4-450e-45d7-9b09-702722f2f053", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Content Writer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d548a679-17b8-44c7-879c-f0c1a3cc4a8e", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e6a612da-f77f-47b6-bcde-a7b59833dcc9", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "AVP Engineering", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "abbabdc0-afe1-472c-8784-3929b0af8077", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Territory Sales Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "dd4a5d8e-0ff7-473e-963e-1cd1cc16789b", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "<PERSON><PERSON> Engineer", "office365.mobile.number": "8238272056", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "4d13caa6-fd0c-4a7c-945a-e4f6962aa272", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior DevOps Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "29714b4b-c79f-4fa4-8327-8955489588ca", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee - Product Analyst", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "aa0548e1-05e4-4f02-9ad5-1534d11b5e80", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Team Lead - Engineering", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "c2191af6-c27c-495e-8bcb-e2f54b4b2d47", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Regional Head - North & East", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "2509988e-0a42-4a0f-a89e-8176938f07c1", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Head - ITSM", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "2254b661-ffb6-48bc-8337-99768d5e455f", "office365.user.name": "<PERSON><PERSON> ch<PERSON>y", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Regional Sales Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "46ff7942-9bad-4898-81a9-f77bc8f0a839", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee - Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d686163d-813b-4000-867a-7c09eaf916c5", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "7c5ad165-2de9-4ecd-a676-ca2e00695c07", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Team Lead", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a333c7da-c331-41d8-a334-46360dccc107", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Territory Sales Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "3f9b3ace-9ad6-409b-a72d-11008953e337", "office365.user.name": "bhavesh.parmar", "office365.user.email": "<EMAIL>"}, {"office365.user": "25d24462-713c-49fa-9ad4-93ef54e1bb97", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "fcdf0c5a-1458-40fa-97c9-6e0d9f624343", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Executive - MIS & Commercial", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "cecaba60-6686-4726-868d-2c953af9881c", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Assistant Manager - H<PERSON>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "1819ea98-3e60-4466-be59-5eceba783418", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Team Lead", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "7951ad12-60b5-4cc7-baea-a4f4d963a75f", "office365.user.name": "Chetan Tu<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Chief operating officer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "99aa1f4c-9b66-425c-8078-8708bfde7652", "office365.user.name": "csgohel", "office365.user.email": "<EMAIL>"}, {"office365.user": "31cf7961-a6a9-4708-875d-bf93c3c01154", "office365.user.name": "<EMAIL>", "office365.user.email": "<EMAIL>"}, {"office365.user": "e67d375f-71cf-4239-9ea7-0d9fbbea8529", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "6b6389ae-edbd-4103-8846-525e73e94a62", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. HR Executive", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e5d2a5e1-5ac5-491b-a66f-672caf089f85", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "42759fc0-3355-47cd-908b-79b819b84af3", "office365.user.name": "Demo Request", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a5a31f5e-e0ef-4a83-87db-17f689307277", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "cb6a9a0d-3e42-4ab5-b7cb-72934a48b375", "office365.user.name": "<EMAIL>", "office365.user.email": "<EMAIL>"}, {"office365.user": "accec937-655d-4e38-b0e4-61203aac1f0c", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "b8547063-c19c-4cd7-b536-8de427f916d8", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Creative Designer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "dbe67069-9a05-44dc-bba3-1bd2b83f4a67", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Regional Sales Head - South", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "52730d3d-437f-4105-8f7c-8f63f9146a19", "office365.user.name": "ekanshu.gupta", "office365.user.email": "<EMAIL>"}, {"office365.user": "625e8802-828b-449f-a422-76fe17ada04d", "office365.user.name": "Gaurang <PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "VP - International Business", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "63edb44a-7be8-4e47-af12-4470357305b3", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Digital Marketing Executive", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e2919525-f3f9-4867-aeba-e49d3746101d", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Customer Success Engineer - IOT", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "9649f625-6fc7-48ca-bcd6-6d6cfa87b9de", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "b0e346a2-f268-44cf-ae4e-86f249856db2", "office365.user.name": "Motadata Helpdesk", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "ef431f3b-dffc-4865-8ea8-72d89d9c2a02", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Lead Presales", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "2e69decc-3f86-4fbc-90c3-71871859fe06", "office365.user.name": "<PERSON><PERSON> mori", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Presales Consultant", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a918294e-a899-417a-9f03-5df53f64d31c", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "57f0c673-3f89-4868-9719-3d8b3ace089e", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "f13cdaa0-afd8-42d5-ac48-a4298ad32f70", "office365.user.name": "hiren", "office365.user.email": "<EMAIL>"}, {"office365.user": "16f94f50-412a-4618-bc7d-1f1a62543ecd", "office365.user.name": "HR Motadata", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "97a267a8-6c3c-4879-be8f-a1402f380bd9", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d449d856-03fe-4f1d-831a-2924327135ac", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Technical Content Writer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "23d1aaf7-b584-4f9e-9961-3e858ac8edd7", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Customer Success Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a59a6598-1d35-494f-a24c-3302338ab580", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Principal Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "195a428e-af54-45e1-9895-a35c7b035c83", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Bid Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "62c20018-3c35-4595-a981-3e254b5527c1", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "b2e02905-53f0-431f-a2a3-db5b1fbd42c0", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. HR Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "f95b2092-feb5-4447-ab40-b5460ddbbf4d", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Customer Success Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e1e8a6cf-79c8-43b6-a2a3-cd5c108324fe", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Inside Sales", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "95eef487-8e11-4aa9-a848-7203d67926a4", "office365.user.name": "Kalpesh Moradiya", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Presales Consultant", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "96c1030c-330f-44f9-857c-7b3a1ed3580f", "office365.user.name": "Kalpesh patat", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Team Lead", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "60217abf-46b8-4815-9222-f7bd52ff19c5", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "65ade5b7-18d0-47b9-a419-c9ae8116c151", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Customer Success Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "7d20b924-e9a9-46d2-b137-1c90d74f64fd", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr Marketing Specialist", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "e4deebd9-fd6d-423e-a8a2-2a2fca9369c0", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Product Analyst", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "b967f20f-47f0-4c47-a9ef-0cf3c845e94f", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee - Technical Solution", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "3b82a3f7-e6e2-4177-bc83-5467763122c1", "office365.user.name": "<PERSON>", "office365.user.email": "kevin.gard<PERSON><EMAIL>", "office365.user.job.title": "Front-End Lead", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "51a73476-411d-4402-928b-5eb26deb362d", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Flutter Developer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "8cce2bcc-8ecd-4811-9e97-6db149015ecf", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "2e36c018-0598-442f-8deb-80475b6e681b", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Associate Implementation Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a175bc07-10cb-4c1b-b6ce-e28ed7200c8b", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "349fa1a3-4375-4687-a1a8-4d1e70d025d2", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "dca4affa-0656-45cd-9e30-2f099b1e2b51", "office365.user.name": "<PERSON><PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "6da0be48-1518-40b7-a377-e18285da28fa", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Technical Lead", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "aa6f5cc4-35d0-4eb8-a6fb-8158c24e7a5a", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Chief Strategy Officer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "60388250-65f1-444e-8427-58a1800050c9", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Regional Sales Manager - North", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "c7cc7878-db35-470a-b6a8-c8df0ce4a079", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>"}, {"office365.user": "de08fb40-61b1-41a7-a31d-a27d0666f9c3", "office365.user.name": "<PERSON><PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Territory Sales Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "07599ebb-47ea-482b-8ed0-34af14968f58", "office365.user.name": "kunal.sinha8", "office365.user.email": "<EMAIL>"}, {"office365.user": "9e461adb-4b10-40a7-bc48-357612b0e9f4", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Customer Success Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "9edcc768-1cc3-4acd-8547-d072d7778948", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Lead Presales", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "5dd21c8e-ebbe-493b-ba09-2d5430c8d265", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Principal QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "fbe6b9a5-d40a-49ac-a292-d36626138979", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee - QA Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "83bceba1-63cd-4f20-b3de-5804285264bb", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "manish.prajapa<PERSON>@motadata.com", "office365.user.job.title": "Security Analyst", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "d5b7aebd-4bc4-4deb-83fa-282de9125ae6", "office365.user.name": "Manish She<PERSON>ia", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "0c44a944-1931-4530-8750-863b50bd9b39", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Channel Sales Manager", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "f206e9f5-2347-4f3f-9630-41eee8fe9d72", "office365.user.name": "Marketing Team", "office365.user.email": "<EMAIL>", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "3ebde467-3327-4260-a16a-2c8118640999", "office365.user.name": "<PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Sr. Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "6aef3f69-f0ab-4d20-ae58-a62983e1ba2b", "office365.user.name": "Meeral Pithwa", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Presales Consultant", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "1176b8bf-a5ed-44ef-a6cb-82c6e3a14e1f", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Trainee - Software Engineer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "5e73a9e1-50e0-48e0-a8bf-ce055eb8bc56", "office365.user.name": "<PERSON><PERSON><PERSON>", "office365.user.email": "<EMAIL>", "office365.user.job.title": "Senior Content Writer", "office365.exchange.subscription": "True", "office365.onedrive.subscription": "True", "office365.sharepoint.subscription": "True", "office365.teams.subscription": "True"}, {"office365.user": "a0ff125c-60db-43e4-ad60-46b60efb1f8d", "office365.user.name": "<PERSON>", "office365.user.email": "<EMAIL>"}], "office365.unlicensed.users": 0, "office365.active.users": 185, "office365.unassigned.licenses": 0, "tenant.name": "motadataindia"}}}