/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.MACScannerConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.TestAPIConstants.MAC_SCANNER_API_ENDPOINT;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestMACScanner
{
    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestMACScanner.class, MOTADATA_API, "Test MACScanner");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllMACScanners(VertxTestContext testContext)
    {
        TestAPIUtil.get(MAC_SCANNER_API_ENDPOINT,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, MACScannerConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateMACScanner(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(MACScanner.MAC_SCANNER_ADDRESS, "12:AS:ER:09:W2:Q1")
                .put(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS, "*******")
                .put(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS, "*******")
                .put(MACScanner.MAC_SCANNER_INTERFACE, "1");

        TestAPIUtil.post(MAC_SCANNER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(MACScannerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "MAC Scanner"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestMACScanner.CONTEXT.put("mac.scanner", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateMACScanner(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = MACScannerConfigStore.getStore().getItem(TestMACScanner.CONTEXT.getLong("mac.scanner"));

        Assertions.assertNotNull(item);

        TestAPIUtil.put(MAC_SCANNER_API_ENDPOINT + "/" + TestMACScanner.CONTEXT.getLong("mac.scanner"), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(MACScannerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "MAC Scanner"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testDeleteMACScanner(VertxTestContext testContext)
    {
        TestAPIUtil.delete(MAC_SCANNER_API_ENDPOINT + "/" + TestMACScanner.CONTEXT.getLong("mac.scanner"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertDeleteEntityTestResult(MACScannerConfigStore.getStore(), response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_DELETED, "MAC Scanner"));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testStoreInit(VertxTestContext testContext)
    {
        MACScannerConfigStore.getStore().updateStore().onComplete(result ->
        {
            if (result.failed())
            {
                testContext.failNow(result.cause());
            }
            else
            {
                Assertions.assertNotNull(MACScannerConfigStore.getStore().getItem());

                testContext.completeNow();
            }
        });
    }
}
