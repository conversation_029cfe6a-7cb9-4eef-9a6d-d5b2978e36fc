/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.CustomJob;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.CronExpressionUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.quartz.JobBuilder;
import org.quartz.TriggerBuilder;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.CustomMonitoringField.CUSTOM_MONITORING_FIELD_NAME;
import static com.mindarray.api.Metric.*;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_METRIC_INSTANCE_UNPROVISION;
import static com.mindarray.flow.FlowEngineConstants.PROTOCOL;
import static com.mindarray.nms.NMSConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(120 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestObjectSettings
{
    private static final Map<String, Object> CONTEXT = new HashMap<>();

    private static final JsonArray CUSTOM_FIELDS = new JsonArray();

    private static final String SCHEDULER_ENTITY_NAME = "Scheduler";

    private static final Logger LOGGER = new Logger(TestRemoteEventProcessor.class, MOTADATA_API, "Object Setting Test");

    private static final String PATTERN = "dd-MM-yyyy";

    private static final JsonArray CATEGORIES = new JsonArray()
            .add(Category.CLOUD.getName()).add(Category.SERVICE_CHECK.getName())
            .add(Category.NETWORK.getName()).add(Category.SERVER.getName()).add(Category.VIRTUALIZATION.getName())
            .add(Category.HCI.getName()).add(Category.OTHER.getName());

    private static final JsonArray IDS = new JsonArray();

    private static final JsonArray NETWORK_INTERFACES = new JsonArray();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(Type.LINUX.getName())
                .add(Type.WINDOWS_CLUSTER.getName()).add(Type.FIREWALL.getName()));

        Assertions.assertNotNull(objects);

        assertFalse(objects.isEmpty());

        IDS.addAll(new JsonArray(objects.stream().filter(item -> JsonObject.mapFrom(item).getString(OBJECT_TYPE).equalsIgnoreCase(Type.WINDOWS_CLUSTER.getName()))
                .map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList())));

        assertFalse(IDS.isEmpty());

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(value = 60, timeUnit = TimeUnit.SECONDS)
    void testGetAllCustomMonitoringField(VertxTestContext testContext)
    {

        TestAPIUtil.get(CUSTOM_MONITORING_FIELD_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, CustomMonitoringFieldConfigStore.getStore(), null);

                            //#3541
                            CUSTOM_FIELDS.addAll(response.bodyAsJsonObject().getJsonArray(RESULT));

                            CONTEXT.put("custom.monitoring.field", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllBusinessHour(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, BusinessHourConfigStore.getStore(), null);

                            CONTEXT.put("business.hour.profile", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetAllCollector(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("collector", ((JsonObject) response.bodyAsJsonObject().getJsonArray(RESULT).stream().filter(object -> ((JsonObject) object).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name())).findFirst().get()).getLong(ID));

                            LOGGER.info(((JsonObject) response.bodyAsJsonObject().getJsonArray(RESULT).stream().filter(object -> ((JsonObject) object).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name())).findFirst().get()).encode());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("group", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + OBJECT_CATEGORY + "=" + CATEGORIES + "&" + OBJECT_DISCOVERY_METHOD + "=" + new JsonArray().add(DiscoveryMethod.REMOTE.name())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var object = ObjectConfigStore.getStore().getItemByValue(OBJECT_TYPE, Type.WINDOWS_CLUSTER.getName());

                            Assertions.assertNotNull(object);

                            assertFalse(object.isEmpty());

                            CONTEXT.put(OBJECT_ID, object.getLong(ID));

                            CONTEXT.put(OBJECT_IP, object.getString(OBJECT_IP));

                            CONTEXT.put(OBJECT_NAME, object.getString(OBJECT_NAME));

                            CONTEXT.put(OBJECT_HOST, object.getString(OBJECT_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDisableObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), State.ENABLE.name())
                .stream().map(metric -> metric.getLong(ID)).toList();

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)) + "/state", new JsonObject()
                        .put(OBJECT_STATE, State.DISABLE.name()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("object " + CONTEXT.get(OBJECT_NAME) + " disabled successfully...", response.bodyAsJsonObject().getString(MESSAGE));

                            if (!items.isEmpty())
                            {
                                var done = new AtomicBoolean(false);

                                var retries = new AtomicInteger();

                                Bootstrap.vertx().setPeriodic(2000, timer ->
                                {
                                    var metrics = MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), State.DISABLE.name()).stream()
                                            .filter(metric -> items.contains(metric.getLong(ID)))
                                            .map(metric -> metric.getLong(ID)).toList();

                                    retries.getAndIncrement();

                                    if (metrics.isEmpty())
                                    {
                                        done.set(true);
                                    }

                                    if (retries.get() >= 5 && !done.get() && !metrics.isEmpty())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("enabled metric is disabled..."));
                                    }

                                    if (done.get())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            }
                            else
                            {
                                testContext.completeNow();
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                            body = body.getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            assertFalse(body.isEmpty());

                            assertEquals(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), body.getLong(GlobalConstants.ID));

                            var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

                            Assertions.assertNotNull(item);

                            assertFalse(item.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testOnMaintenanceObject(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)) + "/state", new JsonObject()
                        .put(OBJECT_STATE, State.MAINTENANCE.name()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("object " + CONTEXT.get(OBJECT_NAME) + " entered into maintenance state successfully...", response.bodyAsJsonObject().getString(MESSAGE));

//                            TimeUnit.SECONDS.sleep(2);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testEnableObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), State.DISABLE.name())
                .stream().map(metric -> metric.getLong(ID)).toList();

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)) + "/state", new JsonObject()
                        .put(OBJECT_STATE, State.ENABLE.name()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("object " + CONTEXT.get(OBJECT_NAME) + " enabled successfully...", response.bodyAsJsonObject().getString(MESSAGE));

                            if (!items.isEmpty())
                            {
                                var done = new AtomicBoolean(false);

                                var retries = new AtomicInteger();

                                Bootstrap.vertx().setPeriodic(2000, timer ->
                                {
                                    var metrics = MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), State.ENABLE.name()).stream()
                                            .filter(metric -> items.contains(metric.getLong(ID)))
                                            .map(metric -> metric.getLong(ID)).toList();

                                    retries.getAndIncrement();

                                    if (metrics.isEmpty())
                                    {
                                        done.set(Boolean.TRUE);
                                    }

                                    if (retries.get() >= 5 && !done.get() && !metrics.isEmpty())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("disabled metric is enabled..."));
                                    }

                                    if (done.get())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            }
                            else
                            {
                                testContext.completeNow();
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testUpdateObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(OBJECT_CUSTOM_FIELDS, new JsonObject()).put(OBJECT_EVENT_PROCESSORS, new JsonArray().add(TestObjectSettings.CONTEXT.get("collector")))
                .put(OBJECT_GROUPS, new JsonArray().add(TestObjectSettings.CONTEXT.get("group"))).put(OBJECT_HOST, TestObjectSettings.CONTEXT.get(OBJECT_HOST)).put(OBJECT_IP, TestObjectSettings.CONTEXT.get(OBJECT_IP))
                .put(OBJECT_BUSINESS_HOUR_PROFILE, TestObjectSettings.CONTEXT.get("business.hour.profile")).put(OBJECT_NAME, TestObjectSettings.CONTEXT.get(OBJECT_NAME))
                .put("object.scheduler.status", NO).put(OBJECT_STATE, State.ENABLE.name()).put(OBJECT_TARGET, TestObjectSettings.CONTEXT.get(OBJECT_NAME));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(TestObjectSettings.CONTEXT.get(OBJECT_ID)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(ObjectConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetCollectorUsedCount(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertEntityUsedCountTestResult(response, CommonUtil.getLong(CONTEXT.get("collector")), 1, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testDisableAllObject(VertxTestContext testContext)
    {
        var items = new ArrayList<Long>();

        for (var id : IDS)
        {
            MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(id), State.ENABLE.name()).stream().map(metric -> metric.getLong(ID)).forEach(items::add);
        }

        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/state", new JsonObject()
                        .put("ids", IDS).put(OBJECT_STATE, State.DISABLE.name()),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.OBJECT.getName());

                            assertFalse(ObjectConfigStore.getStore().getItemsByState(State.DISABLE).isEmpty());

                            assertFalse(ObjectConfigStore.getStore().getItemsByStates(new JsonArray().add(State.DISABLE)).isEmpty());

                            if (!items.isEmpty())
                            {
                                var done = new AtomicBoolean(false);

                                var retries = new AtomicInteger();

                                Bootstrap.vertx().setPeriodic(2000, timer ->
                                {
                                    var metrics = MetricConfigStore.getStore().getItemsByObjectState(IDS.getLong(0), State.DISABLE.name()).stream()
                                            .filter(metric -> items.contains(metric.getLong(ID)))
                                            .map(metric -> metric.getLong(ID)).toList();

                                    retries.getAndIncrement();

                                    if (metrics.isEmpty())
                                    {
                                        done.set(Boolean.TRUE);
                                    }

                                    if (retries.get() >= 5 && !done.get() && !metrics.isEmpty())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("enabled metric is disabled..."));
                                    }

                                    if (done.get())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            }
                            else
                            {
                                testContext.completeNow();
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testOnMaintenanceAllObject(VertxTestContext context)
    {
        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/state", new JsonObject()
                        .put(REQUEST_PARAM_IDS, IDS).put(OBJECT_STATE, State.MAINTENANCE.name()),

                context.succeeding(response ->
                        context.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.OBJECT.getName());

                            context.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testEnableAllObject(VertxTestContext testContext)
    {
        var items = new ArrayList<Long>();

        for (var id : IDS)
        {
            MetricConfigStore.getStore().getItemsByObjectState(CommonUtil.getLong(id), State.DISABLE.name()).stream().map(metric -> metric.getLong(ID)).forEach(items::add);
        }

        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/state", new JsonObject()
                        .put("ids", IDS).put(OBJECT_STATE, State.ENABLE.name()),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.OBJECT.getName());

                            if (!items.isEmpty())
                            {
                                var done = new AtomicBoolean(false);

                                var retries = new AtomicInteger();

                                Bootstrap.vertx().setPeriodic(2000, timer ->
                                {
                                    var metrics = MetricConfigStore.getStore().getItemsByObjectState(IDS.getLong(0), State.ENABLE.name()).stream()
                                            .filter(metric -> items.contains(metric.getLong(ID)))
                                            .map(metric -> metric.getLong(ID)).toList();

                                    retries.getAndIncrement();

                                    if (metrics.isEmpty())
                                    {
                                        done.set(Boolean.TRUE);
                                    }

                                    if (retries.get() >= 5 && !done.get() && !metrics.isEmpty())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("disabled metric is enabled..."));
                                    }

                                    if (done.get())
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            }
                            else
                            {
                                testContext.completeNow();
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testUpdateAllObjectCollector(VertxTestContext testContext)
    {
        var collectors = new JsonArray().add(CommonUtil.getLong(TestObjectSettings.CONTEXT.get("collector")));

        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/update", new JsonObject()
                        .put(REQUEST_PARAM_IDS, IDS).put(OBJECT_EVENT_PROCESSORS, collectors),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.OBJECT.getName());

                            Bootstrap.vertx().setPeriodic(2000, timer ->
                            {
                                var objects = ObjectConfigStore.getStore().getItems(IDS);

                                var done = true;

                                if (objects != null && !objects.isEmpty())
                                {
                                    for (var index = 0; index < objects.size(); index++)
                                    {
                                        if (!objects.getJsonObject(index).getJsonArray(OBJECT_EVENT_PROCESSORS).equals(collectors))
                                        {
                                            done = false;

                                            break;
                                        }
                                    }

                                    if (done)
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                }
                            });
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testUpdateAllObjectMonitoringHour(VertxTestContext testContext)
    {
        var monitorHourProfile = CommonUtil.getLong(TestObjectSettings.CONTEXT.get("business.hour.profile"));

        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/update", new JsonObject()
                        .put(REQUEST_PARAM_IDS, IDS).put(OBJECT_BUSINESS_HOUR_PROFILE, monitorHourProfile),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.OBJECT.getName());

                            Bootstrap.vertx().setPeriodic(2000, timer ->
                            {
                                var objects = ObjectConfigStore.getStore().getItems(IDS);

                                var done = true;

                                if (objects != null && !objects.isEmpty())
                                {
                                    for (var index = 0; index < objects.size(); index++)
                                    {
                                        if (!objects.getJsonObject(index).getLong(OBJECT_BUSINESS_HOUR_PROFILE).equals(monitorHourProfile))
                                        {
                                            done = false;

                                            break;
                                        }
                                    }

                                    if (done)
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                }
                            });
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testUpdateObjectCustomMonitoringField(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(OBJECT_CUSTOM_FIELDS, new JsonObject().put(CommonUtil.getString(TestObjectSettings.CONTEXT.get("custom.monitoring.field")), "test"))
                .put(OBJECT_EVENT_PROCESSORS, new JsonArray().add(CommonUtil.getLong(TestObjectSettings.CONTEXT.get("collector")))).put(OBJECT_GROUPS, new JsonArray().add(CommonUtil.getLong(TestObjectSettings.CONTEXT.get("group"))))
                .put(OBJECT_HOST, TestObjectSettings.CONTEXT.get(OBJECT_HOST)).put(OBJECT_IP, TestObjectSettings.CONTEXT.get(OBJECT_IP))
                .put(OBJECT_BUSINESS_HOUR_PROFILE, CommonUtil.getLong(TestObjectSettings.CONTEXT.get("business.hour.profile"))).put(OBJECT_NAME, "demo")
                .put("object.scheduler.status", NO).put(OBJECT_STATE, State.ENABLE.name()).put(OBJECT_TARGET, TestObjectSettings.CONTEXT.get(OBJECT_NAME));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(TestObjectSettings.CONTEXT.get(OBJECT_ID)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(ObjectConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testGetObjectMetric(VertxTestContext testContext)
    {
        try
        {
            var objects = ObjectConfigStore.getStore().flatItems(OBJECT_IP, "**********", ID);

            Assertions.assertNotNull(objects);

            assertFalse(objects.isEmpty());

            var objectId = objects.getLong(0);

            CONTEXT.put("network.object.id", objectId);

            TestAPIUtil.get(METRIC_API_ENDPOINT + "/" + objectId, testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        LOGGER.info("testGetObjectMetric: response: " + response.bodyAsJsonObject().encode());

                        assertEquals(SC_OK, response.statusCode());

                        var body = response.bodyAsJsonObject();

                        Assertions.assertNotNull(body);

                        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                        body = JsonObject.mapFrom(body.getValue(RESULT));

                        assertTrue(body != null && !body.isEmpty());

                        Assertions.assertTrue(body.containsKey(Type.SWITCH.getName()));

                        NETWORK_INTERFACES.addAll(body.getJsonArray("Network Interface"));

                        testContext.completeNow();
                    })));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGetAllCredentialProfile(VertxTestContext testContext)
    {
        TestAPIUtil.get(CREDENTIAL_PROFILE_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, CredentialProfileConfigStore.getStore(), null);

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            assertFalse(items.isEmpty());

                            CONTEXT.put("credential.profile", items.getJsonObject(1).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testUpdateMetricCollectionTime(VertxTestContext testContext)
    {
        try
        {
            var objects = ObjectConfigStore.getStore().flatItems(OBJECT_IP, "**********", ID);

            assertFalse(objects.isEmpty());

            var metrics = MetricConfigStore.getStore().getItemsByValue(METRIC_OBJECT, objects.getLong(0));

            Assertions.assertNotNull(metrics);

            assertFalse(metrics.isEmpty());

            var metricContext = new JsonObject();

            var metricId = DUMMY_ID;

            for (var index = 0; index < metrics.size(); index++)
            {
                if (metrics.getJsonObject(index).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
                {
                    metricId = metrics.getJsonObject(index).getLong(ID);

                    metricContext = metrics.getJsonObject(index).copy();

                    metricContext.put(METRIC_POLLING_TIME, 130);

                    metricContext.put(METRIC_CREDENTIAL_PROFILE, CommonUtil.getLong(CONTEXT.get("credential.profile")));

                    break;
                }
            }

            var finalMetricId = metricId;

            TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + objects.getLong(0), new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                    testContext.succeeding(response ->
                            testContext.verify(() ->
                            {
                                LOGGER.info("testUpdateMetricCollectionTime: response: " + response.bodyAsJsonObject().encode());

                                assertEquals(String.format(InfoMessageConstants.OBJECT_METRIC_UPDATE_SUCCEEDED, ObjectConfigStore.getStore().getObjectName(objects.getLong(0))),
                                        response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                Bootstrap.vertx().setPeriodic(2000, timer ->
                                {
                                    var updatedMetric = MetricConfigStore.getStore().getItem(finalMetricId);

                                    if (updatedMetric != null && !updatedMetric.isEmpty() && updatedMetric.getInteger(METRIC_POLLING_TIME).equals(130) && updatedMetric.getLong(METRIC_CREDENTIAL_PROFILE).equals(CommonUtil.getLong(CONTEXT.get("credential.profile"))))
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            })));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testCreateMaintenanceScheduler(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.MAINTENANCE.getName())
                .put(SCHEDULER_CONTEXT, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_STATE, State.ENABLE.name()).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:00"))).add(new JsonObject().put(AIOpsObject.OBJECT_STATE, State.DISABLE.name()).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("9726133498")).put(SCHEDULER_TIMES, new JsonArray().add("00:05")))).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), SCHEDULER_ENTITY_NAME);

                                    CONTEXT.put("scheduler", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testGetAllMaintenanceScheduler(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject().put(ID, CommonUtil.getLong(CONTEXT.get(OBJECT_ID))).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.MAINTENANCE.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            assertTrue(items != null && !items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testGetMaintenanceScheduler(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get("scheduler")) + "?filter=" + new JsonObject().put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.MAINTENANCE.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {

                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testGetMetric(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(METRIC_API_ENDPOINT + "?filter=" + new JsonObject().put(OBJECT_TYPE, Type.SWITCH.getName())
                        .put(METRIC_NAME, Type.AVAILABILITY.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testUpdateAllObjectMetricCollectionTime(VertxTestContext testContext)
    {
        var context = new JsonObject().put(REQUEST_PARAM_IDS, IDS)
                .put(METRIC_NAME, Type.AVAILABILITY.getName()).put(METRIC_POLLING_TIME, 600).put(METRIC_STATE, State.ENABLE.name());

        TestAPIUtil.post(METRIC_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertMultiUpdateEntityTestResult(response, Entity.METRIC.getName());

                            Bootstrap.vertx().setPeriodic(2000, timer ->
                            {
                                var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, IDS);

                                var done = false;

                                if (metrics != null && !metrics.isEmpty())
                                {
                                    for (var index = 0; index < metrics.size(); index++)
                                    {
                                        var metric = metrics.getJsonObject(index);

                                        if (metric.containsKey(METRIC_CONTEXT))
                                        {
                                            metric.mergeIn(metric.getJsonObject(METRIC_CONTEXT));
                                        }

                                        if (metric.getString(METRIC_NAME).equalsIgnoreCase(Type.AVAILABILITY.getName())
                                                && metric.getLong(METRIC_POLLING_TIME) == 600)
                                        {
                                            done = true;

                                            break;
                                        }
                                    }

                                    if (done)
                                    {
                                        Bootstrap.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                }
                            });
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testGetAllCloudObject(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + OBJECT_CATEGORY + "=" + new JsonArray().add(Category.CLOUD.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            JsonArray items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            assertTrue(items != null && !items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testGetAllServiceCheckObject(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + OBJECT_CATEGORY + "=" + new JsonArray().add(Category.SERVICE_CHECK.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            JsonArray items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            assertTrue(items != null && !items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testUpdateNetworkInterface(VertxTestContext testContext)
    {
        Assertions.assertNotNull(NETWORK_INTERFACES);

        assertFalse(NETWORK_INTERFACES.isEmpty());

        var updatedContext = NETWORK_INTERFACES.getJsonObject(0);

        var objects = updatedContext.getJsonObject(METRIC_CONTEXT).getJsonArray(OBJECTS);

        updatedContext.getJsonObject(METRIC_CONTEXT).put(OBJECTS, new JsonArray().add(objects.getJsonObject(0)));

        var context = new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(updatedContext));

        TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + updatedContext.getLong(METRIC_OBJECT), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info("testUpdateNetworkInterface: response: " + response.bodyAsJsonObject().encode());

                            testContext.awaitCompletion(10, TimeUnit.SECONDS);

                            assertEquals(String.format(InfoMessageConstants.OBJECT_METRIC_UPDATE_SUCCEEDED, ObjectConfigStore.getStore().getObjectName(updatedContext.getLong(METRIC_OBJECT))),
                                    response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            Bootstrap.vertx().setPeriodic(500, timer ->
                            {
                                var metrics = MetricConfigStore.getStore().getItem(updatedContext.getLong(ID));

                                if (metrics != null && !metrics.isEmpty() && metrics.equals(updatedContext))
                                {
                                    Bootstrap.vertx().cancelTimer(timer);

                                    testContext.completeNow();
                                }
                            });
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testScheduleOnMaintenanceJob(VertxTestContext testContext, TestInfo testInfo)
    {
        var jobKey = "hourly-baseline-job-test";

        var jobDetail = JobBuilder.newJob(CustomJob.class).withIdentity(jobKey, "temp-job").usingJobData(ID, CommonUtil.getLong(CONTEXT.get("scheduler"))).usingJobData(AIOpsObject.OBJECT_STATE, State.MAINTENANCE.name()).build();

        try
        {
            var field = JobScheduler.class.getDeclaredField("scheduler");

            field.setAccessible(true);

            var scheduler = (org.quartz.Scheduler) field.get(JobScheduler.class);

            scheduler.scheduleJob(jobDetail, TriggerBuilder.newTrigger().withIdentity(jobKey + ".trigger", "temp-job").build());

            AtomicInteger counter = new AtomicInteger();

            TestUtil.vertx().setPeriodic(3 * 1000, timer ->
            {
                if (counter.get() >= 3)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow("time out, could not complete test case in given time");
                }
                else
                {
                    TestAPIUtil.get(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID))
                            , testContext.succeeding(response ->
                                    testContext.verify(() ->
                                    {
                                        assertEquals(SC_OK, response.statusCode());

                                        var body = response.bodyAsJsonObject();

                                        Assertions.assertNotNull(body);

                                        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                                        body = body.getJsonObject(RESULT);

                                        Assertions.assertNotNull(body);

                                        assertFalse(body.isEmpty());

                                        assertEquals(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), body.getLong(GlobalConstants.ID));

                                        var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

                                        Assertions.assertNotNull(item);

                                        assertFalse(item.isEmpty());

                                        Assertions.assertEquals(State.MAINTENANCE.name(), response.bodyAsJsonObject().getJsonObject(RESULT).getValue(OBJECT_STATE));

                                        if (response.bodyAsJsonObject().getJsonObject(RESULT).getString(OBJECT_STATE).equalsIgnoreCase(State.MAINTENANCE.name()))
                                        {
                                            TestUtil.vertx().cancelTimer(timer);

                                            testContext.completeNow();
                                        }
                                    })));

                    counter.getAndIncrement();
                }
            });


        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testScheduleOffMaintenanceJob(VertxTestContext testContext, TestInfo testInfo)
    {

        var jobKey = "hourly-baseline-job-test";

        var jobDetail = JobBuilder.newJob(CustomJob.class).withIdentity(jobKey, "temp-job").usingJobData(ID, CommonUtil.getLong(CONTEXT.get("scheduler"))).usingJobData(AIOpsObject.OBJECT_STATE, State.ENABLE.name()).build();

        try
        {
            var field = JobScheduler.class.getDeclaredField("scheduler");

            field.setAccessible(true);

            var scheduler = (org.quartz.Scheduler) field.get(JobScheduler.class);

            scheduler.scheduleJob(jobDetail, TriggerBuilder.newTrigger().withIdentity(jobKey + ".trigger", "temp-job").build());

            AtomicInteger counter = new AtomicInteger();

            TestUtil.vertx().setPeriodic(3 * 1000, timer ->
            {
                if (counter.get() >= 3)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow("time out, could not complete test case in given time");
                }
                else
                {
                    TestAPIUtil.get(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID))
                            , testContext.succeeding(response ->
                                    testContext.verify(() ->
                                    {
                                        assertEquals(SC_OK, response.statusCode());

                                        var body = response.bodyAsJsonObject();

                                        Assertions.assertNotNull(body);

                                        assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                                        body = body.getJsonObject(RESULT);

                                        Assertions.assertNotNull(body);

                                        assertFalse(body.isEmpty());

                                        assertEquals(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), body.getLong(GlobalConstants.ID));

                                        var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

                                        Assertions.assertNotNull(item);

                                        assertFalse(item.isEmpty());

                                        Assertions.assertEquals(State.ENABLE.name(), response.bodyAsJsonObject().getJsonObject(RESULT).getValue(OBJECT_STATE));

                                        if (response.bodyAsJsonObject().getJsonObject(RESULT).getString(OBJECT_STATE).equalsIgnoreCase(State.ENABLE.name()))
                                        {
                                            TestUtil.vertx().cancelTimer(timer);

                                            testContext.completeNow();
                                        }

                                    })));

                    counter.getAndIncrement();
                }
            });
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testUpdateMaintenanceScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_MONTHLY).put(SCHEDULER_DATES, new JsonArray().add("1")).put(SCHEDULER_MONTHS, new JsonArray().add("January")).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.MAINTENANCE.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_STATE, State.ENABLE.name()).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:00"))).add(new JsonObject().put(AIOpsObject.OBJECT_STATE, State.DISABLE.name()).put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05").add("00:10")))).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)))));

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get("scheduler")), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                {
                                    TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_UPDATED, SCHEDULER_ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testUpdateMaintenanceSchedulerState(VertxTestContext testContext, TestInfo testInfo)
    {
        var item = new JsonObject().put(SCHEDULER_STATE, NO).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.MAINTENANCE.getName());

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get("scheduler")), item,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                {
                                    TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_UPDATED, SCHEDULER_ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testDeleteMaintenanceScheduler(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SCHEDULER_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get("scheduler")) + "?" + SCHEDULER_JOB_TYPE + "=" + JobScheduler.JobType.MAINTENANCE.getName(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                {
                                    TestAPIUtil.assertDeleteEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_DELETED, SCHEDULER_ENTITY_NAME));

                                    testContext.completeNow();
                                }))));
    }

    //#3541
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testAddCustomFieldObject(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_CUSTOM_FIELDS, new JsonObject().put(CUSTOM_FIELDS.getJsonObject(0).getString(CUSTOM_MONITORING_FIELD_NAME), 5));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testUpdatePollingErrorNotification(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS, "yes").put(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(OBJECT_SMS_NOTIFICATION_RECIPIENTS, new JsonArray().add("9876543211")).put(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS, 900);

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

            testContext.completeNow();
        })));
    }

    //#3541
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testAddExtraCustomFieldObject(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_CUSTOM_FIELDS, parameters.getJsonObject(OBJECT_CUSTOM_FIELDS).put(CUSTOM_FIELDS.getJsonObject(1).getString(CUSTOM_MONITORING_FIELD_NAME), 5));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

            testContext.completeNow();
        })));
    }

    //#3541
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testRenameCustomFieldObject(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_CUSTOM_FIELDS, new JsonObject().put(CUSTOM_FIELDS.getJsonObject(2).getString(CUSTOM_MONITORING_FIELD_NAME), 7).put(CUSTOM_FIELDS.getJsonObject(1).getString(CUSTOM_MONITORING_FIELD_NAME), 7));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

            testContext.completeNow();
        })));
    }

    //#3541
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testRemoveCustomFieldObject(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_CUSTOM_FIELDS, new JsonObject().put(CUSTOM_FIELDS.getJsonObject(1).getString(CUSTOM_MONITORING_FIELD_NAME), 7));

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

            testContext.completeNow();
        })));
    }

    //#3541
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testRemoveAllCustomFieldObject(VertxTestContext testContext)
    {
        var parameters = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(CONTEXT.get(OBJECT_ID)));

        parameters.put(OBJECT_CUSTOM_FIELDS, new JsonObject());

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), parameters, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            assertEquals(result.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName()));

            assertEquals(result.getLong(ID), CONTEXT.get(OBJECT_ID));

//            TimeUnit.SECONDS.sleep(10);

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
        // bug - 6104
    void testGetSNMPMonitorCredential(VertxTestContext testContext)
    {
        var qualifiedMetrics = MetricConfigStore.getStore().getItemsByValues(METRIC_CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.SNMPV1V2c.getName()).add(Protocol.SNMPV3.getName()));

        Assertions.assertNotNull(qualifiedMetrics);

        assertFalse(qualifiedMetrics.isEmpty());

        var metrics = new JsonObject();

        for (var index = 0; index < qualifiedMetrics.size(); index++)
        {
            var metric = qualifiedMetrics.getJsonObject(index);

            if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN)))
            {
                metrics.put(metric.getString(METRIC_CREDENTIAL_PROFILE_PROTOCOL), metric.getLong(METRIC_CREDENTIAL_PROFILE));
            }
        }

        Assertions.assertTrue(metrics.containsKey(Protocol.SNMPV1V2c.getName()));

        Assertions.assertTrue(metrics.containsKey(Protocol.SNMPV3.getName()));

        var snmpv1v2cContext = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.SNMPV1V2c.getName())).put(ID, metrics.getLong(Protocol.SNMPV1V2c.getName()));

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + snmpv1v2cContext, testContext.succeeding(snmpv1v2cResponse -> testContext.verify(() ->
        {
            LOGGER.info("testGetSNMPMonitorCredential: snmpv1v2cResponse: " + snmpv1v2cResponse.bodyAsJsonObject().encode());

            Assertions.assertNotNull(snmpv1v2cResponse.bodyAsJsonObject());

            assertEquals(SC_OK, snmpv1v2cResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, snmpv1v2cResponse.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertNotNull(snmpv1v2cResponse.bodyAsJsonObject().getJsonObject(RESULT));

            assertFalse(snmpv1v2cResponse.bodyAsJsonObject().getJsonObject(RESULT).isEmpty());

            Assertions.assertNotNull(snmpv1v2cResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT));

            Assertions.assertTrue(snmpv1v2cResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).containsKey(Entity.OBJECT.getName()));

            assertFalse(snmpv1v2cResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName()).isEmpty());

            var snmpV3Context = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.SNMPV3.getName())).put(ID, metrics.getLong(Protocol.SNMPV3.getName()));

            TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + snmpV3Context, testContext.succeeding(snmpV3Response -> testContext.verify(() ->
            {
                LOGGER.info("testGetSNMPMonitorCredential: innerGet: " + snmpV3Response.bodyAsJsonObject().encode());

                Assertions.assertNotNull(snmpV3Response.bodyAsJsonObject());

                assertEquals(SC_OK, snmpV3Response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                assertEquals(STATUS_SUCCEED, snmpV3Response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                Assertions.assertNotNull(snmpV3Response.bodyAsJsonObject().getJsonObject(RESULT));

                assertFalse(snmpV3Response.bodyAsJsonObject().getJsonObject(RESULT).isEmpty());

                Assertions.assertNotNull(snmpV3Response.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT));

                Assertions.assertTrue(snmpV3Response.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).containsKey(Entity.OBJECT.getName()));

                assertFalse(snmpV3Response.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName()).isEmpty());

                testContext.completeNow();
            })));
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
        // bug - 6113
    void testUpdateCloudMonitorCredential(VertxTestContext testContext)
    {
        var qualifiedMetrics = MetricConfigStore.getStore().getItemsByValue(METRIC_CREDENTIAL_PROFILE_PROTOCOL, Protocol.CLOUD.getName());

        Assertions.assertNotNull(qualifiedMetrics);

        assertFalse(qualifiedMetrics.isEmpty());

        var metrics = new JsonObject();

        for (var index = 0; index < qualifiedMetrics.size(); index++)
        {
            var metric = qualifiedMetrics.getJsonObject(index);

            if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN)))
            {
                metrics.put(metric.getString(METRIC_TYPE), metric);
            }
        }

        Assertions.assertTrue(metrics.containsKey(Type.AWS_CLOUD.getName()));

        Assertions.assertTrue(metrics.containsKey(Type.AZURE_CLOUD.getName()));

        var awsContext = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.CLOUD.getName())).put(ID, metrics.getJsonObject(Type.AWS_CLOUD.getName()).getLong(ID));

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + awsContext, testContext.succeeding(awsResponse -> testContext.verify(() ->
        {
            LOGGER.info("testUpdateCloudMonitorCredential: awsResponse: " + awsResponse.bodyAsJsonObject().encode());

            Assertions.assertNotNull(awsResponse.bodyAsJsonObject());

            assertEquals(SC_OK, awsResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, awsResponse.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertNotNull(awsResponse.bodyAsJsonObject().getJsonObject(RESULT));

            assertFalse(awsResponse.bodyAsJsonObject().getJsonObject(RESULT).isEmpty());

            Assertions.assertNotNull(awsResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT));

            Assertions.assertTrue(awsResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).containsKey(Entity.OBJECT.getName()));

            Assertions.assertTrue(awsResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName()).size() > 2);

            var azureContext = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.CLOUD.getName())).put(ID, metrics.getJsonObject(Type.AZURE_CLOUD.getName()).getLong(ID));

            TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + azureContext, testContext.succeeding(azureResponse -> testContext.verify(() ->
            {
                LOGGER.info("testUpdateCloudMonitorCredential: innerGet: " + azureResponse.bodyAsJsonObject().encode());

                Assertions.assertNotNull(azureResponse.bodyAsJsonObject());

                assertEquals(SC_OK, azureResponse.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                assertEquals(STATUS_SUCCEED, azureResponse.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                Assertions.assertNotNull(azureResponse.bodyAsJsonObject().getJsonObject(RESULT));

                assertFalse(azureResponse.bodyAsJsonObject().getJsonObject(RESULT).isEmpty());

                Assertions.assertNotNull(azureResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT));

                Assertions.assertTrue(azureResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).containsKey(Entity.OBJECT.getName()));

                Assertions.assertTrue(azureResponse.bodyAsJsonObject().getJsonObject(RESULT).getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName()).size() > 2);

                var context = new JsonObject().put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, Protocol.CLOUD.getName())
                        .put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metrics.getJsonObject(Type.AWS_CLOUD.getName()).put(METRIC_CREDENTIAL_PROFILE, metrics.getJsonObject(Type.AZURE_CLOUD.getName()).getLong(ID))));

                TestAPIUtil.post(METRIC_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testUpdateCloudMonitorCredential: innerPost: " + response.bodyAsJsonObject().encode());

                    Assertions.assertNotNull(response.bodyAsJsonObject());

                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    Bootstrap.vertx().setPeriodic(1000, timer ->
                    {
                        var items = MetricConfigStore.getStore().getItemsByValue(METRIC_TYPE, Type.AWS_CLOUD.getName());

                        if (items != null && !items.isEmpty())
                        {
                            for (var index = 0; index < items.size(); index++)
                            {
                                var item = items.getJsonObject(index);

                                if (item.getLong(METRIC_CREDENTIAL_PROFILE).equals(metrics.getJsonObject(Type.AZURE_CLOUD.getName()).getLong(ID)))
                                {
                                    Bootstrap.vertx().cancelTimer(timer);

                                    testContext.completeNow();

                                    break;
                                }
                            }
                        }
                    });
                })));
            })));
        })));
    }

    //#3604
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testDeleteObject(VertxTestContext testContext)
    {
        TestAPIUtil.delete(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(CONTEXT.get(OBJECT_ID)), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info("testDeleteObject: response: " + response.bodyAsJsonObject().encode());

            assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, "Monitor"), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(org.apache.http.HttpStatus.SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            testContext.completeNow();
        })));
    }

    //#3604
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Disabled
    @Order(43)
    void testDeleteUnusedMonitor(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItemsByValues(OBJECT_TYPE, new JsonArray().add(Type.AWS_CLOUD.getName())
                .add(Type.AZURE_CLOUD.getName()), ID);

        Assertions.assertNotNull(objects);

        assertFalse(objects.isEmpty());

        TestAPIUtil.deleteAll(OBJECT_API_ENDPOINT, new JsonObject()
                .put(REQUEST_PARAM_IDS, objects), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info("testDeleteUnusedMonitor: response: " + response.bodyAsJsonObject().encode());

            assertEquals(InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED, response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testGetCredentialsWithInvalidFilterParameters(VertxTestContext testContext)
    {
        TestAPIUtil.get(CREDENTIAL_PROFILE_API_ENDPOINT + "?filter=" + new JsonObject().put(KEY, "1"), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info("testGetCredentialsWithInvalidFilterParameters: response: " + response.bodyAsJsonObject().encode());

            assertEquals(SC_BAD_REQUEST, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

            Assertions.assertNotNull(body.getString(MESSAGE));

            Assertions.assertEquals(ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS, body.getString(MESSAGE));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testDeleteSystemGeneratedCredentials(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItemByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

        Assertions.assertNotNull(item);

        TestAPIUtil.delete(CREDENTIAL_PROFILE_API_ENDPOINT + "/" + item.getLong(ID), testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info("testDeleteSystemGeneratedCredentials: response: " + response.bodyAsJsonObject().encode());

            assertEquals(SC_BAD_REQUEST, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

            Assertions.assertNotNull(body.getString(MESSAGE));

            Assertions.assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, Entity.CREDENTIAL_PROFILE.getName()), body.getString(MESSAGE));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testUpdateCredentialsWithOutRequestParameters(VertxTestContext testContext)
    {
        var item = CredentialProfileConfigStore.getStore().getItem();

        Assertions.assertNotNull(item);

        TestAPIUtil.put(CREDENTIAL_PROFILE_API_ENDPOINT + "/" + item.getLong(ID), new JsonObject(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info("testUpdateCredentialsWithOutRequestParameters: response: " + response.bodyAsJsonObject().encode());

                            assertEquals(SC_BAD_REQUEST, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

                            assertEquals(ErrorMessageConstants.API_REQUEST_BODY_MISSING, body.getString(MESSAGE));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testGetObjectBusinessHourReferences(VertxTestContext testContext)
    {
        var item = BusinessHourConfigStore.getStore().getItemByValue(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM);

        Assertions.assertNotNull(item);

        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT + "/" + item.getLong(ID) + "/references",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetObjectBusinessHourReferences: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testGetMonitorsWithFilter(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(KEY, OBJECT_CATEGORY).put(VALUE, new JsonArray().add(Category.NETWORK.getName())),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetMonitorsWithFilter: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonArray(RESULT));

                    assertFalse(body.getJsonArray(RESULT).isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testGetMonitorsWithInvalidFilterParameters(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(VALUE, new JsonArray().add(Category.NETWORK.getName())),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetMonitorsWithInvalidFilterParameters: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_BAD_REQUEST, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS, body.getString(MESSAGE));

                    assertEquals(SC_BAD_REQUEST, body.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_FAIL, body.getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testGetMonitorsWithCredentialProfileAndMetricType(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(METRIC_TYPE, new JsonArray().add(Type.SSH.getName()).add(Type.POWERSHELL.getName())
                                .add(Type.HTTP.getName()).add(Type.DATABASE.getName()).add(Type.CUSTOM.getName()))
                        .put(OBJECT_TYPE, new JsonArray().add(Type.LINUX.getName()))
                        .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.SNMPV1V2c.getName())
                                .add(Protocol.SNMPV3.getName()).add(Protocol.SSH.getName())
                                .add(Protocol.POWERSHELL.getName())
                                .add(Protocol.CLOUD.getName())
                                .add(Protocol.HTTP_HTTPS.getName())
                                .add(Protocol.JDBC.getName())),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetMonitorsWithCredentialProfileAndMetricType: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonArray(RESULT));

                    assertFalse(body.getJsonArray(RESULT).isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testGetMonitorsWithCredentialProfile(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?filter=" + new JsonObject()
                        .put(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(Protocol.SSH.getName())
                                .add(Protocol.POWERSHELL.getName())
                                .add(Protocol.CLOUD.getName())
                                .add(Protocol.HTTP_HTTPS.getName())
                                .add(Protocol.JDBC.getName())),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetMonitorsWithCredentialProfile: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                    assertFalse(body.getJsonObject(RESULT).isEmpty());

                    body = body.getJsonObject(RESULT).getJsonObject(RESULT);

                    Assertions.assertNotNull(body);

                    assertFalse(body.isEmpty());

                    Assertions.assertTrue(body.containsKey(Entity.METRIC.getName()));

                    assertFalse(body.getJsonArray(Entity.METRIC.getName()).isEmpty());

                    Assertions.assertTrue(body.containsKey(Entity.OBJECT.getName()));

                    assertFalse(body.getJsonArray(Entity.OBJECT.getName()).isEmpty());

                    Assertions.assertTrue(body.containsKey(NMSConstants.APPS));

                    assertFalse(body.getJsonArray(APPS).isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testGetApplicationMetricMonitor(VertxTestContext testContext)
    {
        var metrics = MetricConfigStore.getStore().getItemByValue(METRIC_TYPE, NMSConstants.Type.MYSQL.getName());

        Assertions.assertNotNull(metrics);

        assertFalse(metrics.isEmpty());

        var id = metrics.getLong(Metric.METRIC_OBJECT);

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/" + id,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetApplicationMetricMonitor: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertNotNull(body);

                    assertFalse(body.isEmpty());

                    Assertions.assertTrue(body.containsKey(NMSConstants.APPS));

                    assertFalse(body.getJsonArray(APPS).isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testGetMonitorInvalidId(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/" + 1234L,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info("testGetMonitorInvalidId: response: " + response.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertNotNull(body);

                    Assertions.assertEquals(0, body.size());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testStoreInit(VertxTestContext testContext)
    {
        ObjectConfigStore.getStore().updateStore().onComplete(result ->
        {
            if (result.failed())
            {
                testContext.failNow(result.cause());
            }
            else
            {
                var item = ObjectConfigStore.getStore().getItem();

                Assertions.assertNotNull(item);

                assertFalse(item.isEmpty());

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    @Disabled
    void testProvisionRediscoverObjectsInvalidMetric(VertxTestContext testContext)
    {
        var context = new JsonObject().put(ID, 12345L).put(SESSION_ID, TestUtil.getSessionId())
                .put(User.USER_NAME, DEFAULT_USER)
                .put(OBJECT_NAME, "object").put(METRIC_NAME, "metric")
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(OBJECTS, new JsonArray()).put(NMSConstants.REDISCOVER_JOB, RediscoverJob.PROCESS.getName());

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROVISION_PROGRESS))
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(context.getLong(ID)))
                    {
                        LOGGER.info("testProvisionRediscoverObjectsInvalidMetric: eventContext: " + eventContext.encode());

                        Assertions.assertEquals(String.format("either metric %s deleted or object %s disabled", context.getString(Metric.METRIC_NAME), context.getString(AIOpsObject.OBJECT_NAME)),
                                eventContext.getString(MESSAGE));

                        Assertions.assertEquals(ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, eventContext.getString(ERROR_CODE));

                        Assertions.assertEquals(STATUS_FAIL, eventContext.getString(STATUS));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testProvisionMonitorInvalidId(VertxTestContext testContext)
    {
        var context = new JsonObject().put(ID, 12345L).put(SESSION_ID, TestUtil.getSessionId())
                .put(User.USER_NAME, DEFAULT_USER)
                .put(OBJECT_NAME, "object")
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(NMSConstants.OBJECT, new JsonObject().put(OBJECT_NAME, "object"))
                .put(NMSConstants.REDISCOVER_JOB, RediscoverJob.APP.getName());

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS))
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(context.getLong(ID)))
                    {
                        LOGGER.info("testProvisionMonitorInvalidId: eventContext: " + eventContext.encode());

                        Assertions.assertEquals(String.format(PROVISION_OBJECT_FAILED_PARENT_OBJECT_NOT_FOUND, "object"),
                                eventContext.getString(MESSAGE));

                        Assertions.assertEquals(ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_PARENT, eventContext.getString(ERROR_CODE));

                        Assertions.assertEquals(STATUS_FAIL, eventContext.getString(STATUS));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testProvisionMonitorNoCredential(VertxTestContext testContext)
    {
        var context = new JsonObject().put(ID, 12345L).put(SESSION_ID, TestUtil.getSessionId())
                .put(User.USER_NAME, DEFAULT_USER)
                .put(OBJECT_NAME, "object")
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(NMSConstants.OBJECT, new JsonObject()).put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, 12345L);

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS))
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(context.getLong(ID)))
                    {
                        LOGGER.info("testProvisionMonitorNoCredential: eventContext: " + eventContext.encode());

                        Assertions.assertEquals(String.format(PROVISION_OBJECT_FAILED_CREDENTIAL_PROFILE_NOT_FOUND, "object"),
                                eventContext.getString(MESSAGE));

                        Assertions.assertEquals(ErrorCodes.ERROR_CODE_OBJECT_PROVISION_NO_CREDENTIAL_PROFILE, eventContext.getString(ERROR_CODE));

                        Assertions.assertEquals(STATUS_FAIL, eventContext.getString(STATUS));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testProvisionDuplicateMonitor(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************"));

        Assertions.assertNotNull(object);

        object.put(SESSION_ID, TestUtil.getSessionId())
                .put(User.USER_NAME, DEFAULT_USER)
                .put(EventBusConstants.EVENT_REPLY, YES);

        LOGGER.debug(String.format("Provisioning %s again", object.encodePrettily()));

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS))
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(object.getLong(ID)))
                    {
                        LOGGER.info("testProvisionDuplicateMonitor: eventContext: " + eventContext.encode());

                        Assertions.assertEquals(String.format(PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT, object.getString(OBJECT_NAME)),
                                eventContext.getString(MESSAGE));

                        Assertions.assertEquals(ErrorCodes.ERROR_CODE_OBJECT_PROVISION_DUPLICATE, eventContext.getString(ERROR_CODE));

                        Assertions.assertEquals(STATUS_FAIL, eventContext.getString(STATUS));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, object);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testCheckDataSecurityObject(VertxTestContext testContext, TestInfo testInfo)
    {

        var items = ObjectConfigStore.getStore().getItems();

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + UserRole.ADMIN_ROLE + "=" + YES, response ->
        {

            if (response.succeeded())
            {

                TestAPIUtil.assertValidResponseTestResult(response.result(), LOGGER, testInfo.getTestMethod().get().getName());

                Assertions.assertEquals(items.size(), response.result().bodyAsJsonObject().getJsonArray(RESULT).size());

                testContext.completeNow();

            }
            else
            {

                testContext.failNow("Object API response failed ...");

            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testCheckDataSecurityGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT + "?" + UserRole.ADMIN_ROLE + "=" + YES, result ->
        {

            if (result.succeeded())
            {

                TestAPIUtil.assertValidResponseTestResult(result.result(), LOGGER, testInfo.getTestMethod().get().getName());

                testContext.completeNow();

            }
            else
            {

                testContext.failNow("Group API response failed ...");

            }
        });
    }

    //24349
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testDeleteInstanceMetrics(VertxTestContext testContext, TestInfo testInfo)
    {

        var object = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertTrue(object > 0);

        assertNotEquals(NOT_AVAILABLE, MetricConfigStore.getStore().getItemByMetricPlugin(object, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

        var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

        Assertions.assertNotNull(item);

        LOGGER.info(String.format("metrics : %s", item.encode()));

        var metricContext = item.getJsonObject(METRIC_CONTEXT).put(METRIC_POLLING_TIME, 130)
                .put(METRIC_CREDENTIAL_PROFILE, CommonUtil.getLong(CONTEXT.get("credential.profile")));

        Assertions.assertTrue(metricContext.containsKey(OBJECTS) && !metricContext.getJsonArray(OBJECTS).isEmpty());

        var interfaceName = metricContext.getJsonArray(OBJECTS).stream().map(instance -> JsonObject.mapFrom(instance).getString(INTERFACE_NAME)).findFirst().get();

        Assertions.assertNotNull(interfaceName);

        metricContext.put(METRIC_INSTANCES, new JsonArray().add(interfaceName));

        LOGGER.info(testInfo.getTestMethod().get().getName() + ": metricContext: " + metricContext.encode());

        TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + object, new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(item)).put(REQUEST_PARAM_TYPE, REQUEST_DELETE),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info("testDeleteInstanceMetrics: response: " + response.bodyAsJsonObject().encodePrettily());

                            assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertTrue(response.bodyAsJsonObject().containsKey(MESSAGE) && response.bodyAsJsonObject().getString(MESSAGE).contains("updated successfully"));

                            testContext.completeNow();

                        })));

    }

    /*
     *  In ObjectCacheStore we will store instance ips of access.points, vms, and interfaces
     *  In this testcase we will validate whether out cache is updating properly or not through mocking poll data
     * */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testGetInstanceIPByObject(VertxTestContext testContext)
    {

        //network device ********** has instance named Lo0-52 and has ip ***********
        var objectId = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertEquals("***********", ObjectCacheStore.getStore().getInstanceIP(objectId, "Lo0-52"));

        //Access point controller ********** has instance named 1st_Flr-AP2 and has ip ***********
        objectId = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertEquals("***********", ObjectCacheStore.getStore().getInstanceIP(objectId, "1st_Flr-AP2"));

        //Esxi Hypervisor ************ has instance named Vivek_Sahu_Radwin_rpe_8.177 and has ip ************
        objectId = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertEquals("************", ObjectCacheStore.getStore().getInstanceIP(objectId, "Vivek_Sahu_Radwin_rpe_8.177"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testGetAllObjectTypes(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/types",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    var types = result.getJsonArray(RESULT);

                    assertNotNull(types);

                    assertFalse(types.isEmpty());

                    var items = new HashSet<String>();

                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(items::add);

                    assertEquals(types.size(), items.size());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testGetObjectTypesByProtocol(VertxTestContext testContext)
    {
        var filter = new JsonObject("{\"key\":\"protocol\", \"value\":[\"SSH\",\"Powershell\"]}");

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/types?filter=" + filter.encode(),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    var types = result.getJsonArray(RESULT);

                    assertNotNull(types);

                    assertFalse(types.isEmpty());

                    var items = new HashSet<String>();

                    var filters = filter.getJsonArray("value").stream().map(CommonUtil::getString).map(Protocol::valueOfName)
                            .collect(Collectors.toSet());

                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .filter(object -> filters.contains(Protocol.valueOf(object.getString(PROTOCOL))))
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(items::add);

                    assertArrayEquals(items.toArray(), types.stream().toArray());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testGetObjectTypesByCategory(VertxTestContext testContext)
    {
        var filter = new JsonObject("{\"key\":\"object.category\", \"value\":[\"Server\"]}");

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "/types?filter=" + filter.encode(),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

                    assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    var types = result.getJsonArray(RESULT);

                    assertNotNull(types);

                    assertFalse(types.isEmpty());

                    var items = new HashSet<String>();

                    var filters = filter.getJsonArray("value").stream().map(CommonUtil::getString).map(Category::valueOfName)
                            .collect(Collectors.toSet());

                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .filter(object -> filters.contains(Category.valueOf(object.getString(OBJECT_CATEGORY))))
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(items::add);

                    assertArrayEquals(items.toArray(), types.stream().toArray());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testUnprovisionInstanceMetrics(VertxTestContext testContext, TestInfo testInfo)
    {
        var object = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertTrue(object > 0);

        assertNotEquals(NOT_AVAILABLE, MetricConfigStore.getStore().getItemByMetricPlugin(object, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

        var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

        Assertions.assertNotNull(item);

        LOGGER.info(String.format("metrics : %s", item.encode()));

        var metricContext = item.getJsonObject(METRIC_CONTEXT);

        Assertions.assertTrue(metricContext.containsKey(OBJECTS) && !metricContext.getJsonArray(OBJECTS).isEmpty());

        LOGGER.info(testInfo.getTestMethod().get().getName() + ": metricContext: " + metricContext.encode());

        var instances = metricContext.getJsonArray(OBJECTS).size();

        var downInstances = new AtomicInteger();

        metricContext.getJsonArray(OBJECTS).stream().filter(instance -> JsonObject.mapFrom(instance).getString(STATUS).equalsIgnoreCase(STATUS_DOWN)).forEach(instance ->
        {
            // unprovision all interfaces which are down
            TestUtil.vertx().eventBus().send(EVENT_METRIC_INSTANCE_UNPROVISION, new JsonObject().put(ID, item.getLong(ID)).put(METRIC_INSTANCES, new JsonArray().add(JsonObject.mapFrom(instance).getString(INTERFACE))));

            downInstances.incrementAndGet();
        });

        Assertions.assertTrue(downInstances.get() > 0);

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
        {
            try
            {
                var context = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName())).getJsonObject(METRIC_CONTEXT);

                assertEquals(context.getJsonArray(OBJECTS).size(), (instances - downInstances.get()));

                Assertions.assertTrue(context.getJsonArray(OBJECTS).stream().noneMatch(entry -> JsonObject.mapFrom(entry).getString(STATUS).equals(STATUS_DOWN)));

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }
}

