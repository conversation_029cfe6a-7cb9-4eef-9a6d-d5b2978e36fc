/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.FlowIPMapperConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.FLOW_IP_MAPPER_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.FlowIPMapper.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestFlowIPMapper
{
    private static final String ENTITY_NAME = "Flow IP Mapper";

    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestFlowIPMapper.class, "Test Flow IP Mapper", "Test Flow IP Mapper");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var csv = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "main" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "samples" + GlobalConstants.PATH_SEPARATOR + "flow-ip-mapping-sample.csv");

            if (csv.exists())
            {
                FileUtils.copyFile(csv, new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + "flow-ip-mapping-sample.csv"));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow("csv does not exist in samples directory");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateFlowIPMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_IP_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_IP_MAPPER_DESCRIPTION, "This is for test").put(FLOW_IP_MAPPER_SOURCE, MANUAL_MAPPING).put(FLOW_IP_MAPPER_TARGET, "flow-ip-mapping-sample.csv");

        TestAPIUtil.post(FLOW_IP_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowIPMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestFlowIPMapper.CONTEXT.put("flow.ip.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateDuplicateFlowIPMapper(VertxTestContext testContext)
    {
        var context = new JsonObject().put(FLOW_IP_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_IP_MAPPER_DESCRIPTION, "This is for test").put(FLOW_IP_MAPPER_SOURCE, MANUAL_MAPPING).put(FLOW_IP_MAPPER_TARGET, "flow-ip-mapping-sample.csv");

        TestAPIUtil.post(FLOW_IP_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Profile Name"),
                                    FlowIPMapperConfigStore.getStore(), FLOW_IP_MAPPER_PROFILE_NAME, context.getString(FLOW_IP_MAPPER_PROFILE_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetFlowIPMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(FLOW_IP_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.ip.mapper"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("flow.ip.mapper"), FlowIPMapperConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetFlowIPMapperReference(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(FLOW_IP_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.ip.mapper") + "/users", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertNotNull(body);

                    assertFalse(body.isEmpty());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllFlowIPMapper(VertxTestContext testContext)
    {
        TestAPIUtil.get(FLOW_IP_MAPPER_API_ENDPOINT,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, FlowIPMapperConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testUpdateFlowIPMapper(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject().put(FLOW_IP_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_IP_MAPPER_DESCRIPTION, "This is for test 2").put(FLOW_IP_MAPPER_SOURCE, MANUAL_MAPPING).put(FLOW_IP_MAPPER_TARGET, "flow-ip-mapping-sample.csv");

        TestAPIUtil.put(FLOW_IP_MAPPER_API_ENDPOINT + TestFlowIPMapper.CONTEXT.getLong("flow.ip.mapper"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(FlowIPMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteFlowIPMapper(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_IP_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.ip.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(FlowIPMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_DELETED, ENTITY_NAME));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteFlowIPMapperNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_IP_MAPPER_API_ENDPOINT + 1234, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, ENTITY_NAME, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.FLOW_IP_MAPPER.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateSecondFlowIPMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_IP_MAPPER_PROFILE_NAME, "Motadata Test 2").put(FLOW_IP_MAPPER_DESCRIPTION, "This is for test").put(FLOW_IP_MAPPER_SOURCE, MANUAL_MAPPING).put(FLOW_IP_MAPPER_TARGET, "flow-ip-mapping-sample.csv");

        TestAPIUtil.post(FLOW_IP_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowIPMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }
}
