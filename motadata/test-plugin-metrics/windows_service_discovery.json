{"************": {"object.ip": "************", "objects": [{"object.name": "Dhcp", "object.type": "system.service", "status": "Up", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start."}, {"object.name": "DHCPServer", "object.type": "system.service", "status": "Up", "system.service.description": "Performs TCP/IP configuration for DHCP clients, including dynamic assignments of IP addresses, specification of the WINS and DNS servers, and connection-specific DNS names. If this service is stopped, the DHCP server will not perform TCP/IP configuration for clients. If this service is disabled, any services that explicitly depend on it will fail to start."}], "password": "Mind@123", "port": 5985, "status": "succeed", "timeout": 20, "username": "Administrator"}, "***********": {"object.ip": "***********", "objects": [{"object.name": "Dhcp", "object.type": "system.service", "status": "Up", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start."}], "password": "Mind@#123", "port": 5985, "status": "succeed", "timeout": 20, "username": "Administrator"}, "************": {"object.ip": "************", "objects": [{"object.name": "Dhcp", "object.type": "system.service", "status": "Up", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start."}], "password": "mind@123", "port": 5985, "status": "succeed", "timeout": 20, "username": "readonly"}, "************": {"object.ip": "************", "objects": [{"object.name": "Dhcp", "object.type": "system.service", "status": "Up", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start."}], "password": "Mind@123", "port": 5985, "status": "succeed", "timeout": 20, "username": "Administrator"}}