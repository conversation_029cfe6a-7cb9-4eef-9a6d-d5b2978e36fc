/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


/*   Change Logs:
 *   Date          Author              Notes
 *   11-Feb-2025    Chandresh           MOTADATA-446   Event Policy Template related enhancements
 *   28-Feb-2025    Bharat              MOTADATA-5233: Added template for different type of share.
 *   4-Mar-2025     Bharat              MOTADATA-4740: Two factor authentication 2FA
 *   5-Mar-2025     Pruthviraj          MOTADATA-5331 : Notification templates added for netroute policy
 *   9-Apr-2025     Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *   7-Apr-2025     Vismit              MOTADATA-5613: Changed config email notification html template.
 */


package com.mindarray.notification;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.SNMPTrapForwarder;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.EMPTY_VALUE;

/**
 * Abstract base class for handling various types of notifications in the Motadata system.
 * <p>
 * This class provides:
 * 1. Constants for notification-related fields and templates
 * 2. Static utility methods for sending different types of notifications (email, SMS, SNMP trap, etc.)
 * 3. Methods for retrieving appropriate notification templates based on notification type and context
 * 4. Enums for notification types, content types, and share types
 * <p>
 * The class uses Vert.x EventBus to publish notification events that are handled by
 * appropriate notification handlers elsewhere in the system.
 * <p>
 * Concrete notification implementations should extend this class and implement
 * the abstract notify method.
 */
public abstract class Notification
{

    public static final String NOTIFICATION_TYPE = "notification.type";
    public static final String SMS_NOTIFICATION_RECIPIENTS = "sms.notification.recipients";
    public static final String EMAIL_NOTIFICATION_RECIPIENTS = "email.notification.recipients";
    public static final String EMAIL_NOTIFICATION_SENDER = "email.notification.sender";
    public static final String EMAIL_NOTIFICATION_SUBJECT = "email.notification.subject";
    public static final String EMAIL_NOTIFICATION_MESSAGE = "email.notification.message";
    public static final String EMAIL_NOTIFICATION_CONTENT = "email.notification.content";
    public static final String EMAIL_NOTIFICATION_ATTACHMENTS = "email.notification.attachments";
    public static final String EMAIL_NOTIFICATION_ATTACHMENT_TYPE = "email.notification.attachment.type";
    public static final String EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE = "email.notification.attachment.disposition.type";
    public static final String SMS_NOTIFICATION_MESSAGE = "sms.notification.message";
    public static final String CHANNEL_NOTIFICATION_CONTENT = "channel.notification.content";
    public static final String TEMPLATE_NAME = "template.name";


    public static final String EMAIL_USER_OTP_VERIFICATION_HTML_TEMPLATE = "user-otp-verification-template.ftl";
    public static final String EMAIL_NOTIFICATION_DATABASE_BACKUP_SUCCESSFUL_HTML_TEMPLATE = "database-backup-success-template.ftl";
    public static final String EMAIL_NOTIFICATION_DATABASE_BACKUP_FAILED_HTML_TEMPLATE = "database-backup-failed-template.ftl";
    public static final String EMAIL_NOTIFICATION_DISCOVERY_HTML_TEMPLATE = "discovery-template.ftl";
    public static final String EMAIL_NOTIFICATION_WIDGET_HTML_TEMPLATE = "widget-template.ftl";
    public static final String EMAIL_NOTIFICATION_METRIC_EXPLORER_HTML_TEMPLATE = "metric-explorer-template.ftl";
    public static final String EMAIL_NOTIFICATION_SEARCH_QUERY_HTML_TEMPLATE = "search-query-template.ftl";
    public static final String EMAIL_INTEGRATION_FAILED_HTML_TEMPLATE = "integration-failed-template.ftl";
    public static final String EMAIL_NOTIFICATION_REDISCOVERY_HTML_TEMPLATE = "rediscovery-template.ftl";
    public static final String EMAIL_NOTIFICATION_RUNBOOK_PLUGIN_TEMPLATE = "runbook-plugin-template.ftl";
    public static final String EMAIL_NOTIFICATION_TOPOLOGY_HTML_TEMPLATE = "topology-template.ftl";
    public static final String EMAIL_RUNBOOK_NOTIFICATION_HTML_TEMPLATE = "runbook-notification-template.ftl";
    public static final String EMAIL_NOTIFICATION_METRIC_POLICY_HTML_TEMPLATE = "metric-policy-template.ftl";
    public static final String EMAIL_RENOTIFICATION_METRIC_POLICY_HTML_TEMPLATE = "renotification-metric-policy-template.ftl";
    public static final String EMAIL_NOTIFICATION_EVENT_POLICY_HTML_TEMPLATE = "event-policy-template.ftl";
    public static final String EMAIL_NOTIFICATION_TRAP_POLICY_HTML_TEMPLATE = "trap-policy-template.ftl";
    public static final String EMAIL_NOTIFICATION_POLLING_ERROR_HTML_TEMPLATE = "polling-error-template.ftl";
    public static final String EMAIL_NOTIFICATION_NETROUTE_POLICY_SOURCE_TO_DESTINATION_HTML_TEMPLATE = "netroute-policy-source-to-destination-template.ftl";
    public static final String EMAIL_NOTIFICATION_NETROUTE_POLICY_HOP_BY_HOP_HTML_TEMPLATE = "netroute-policy-hop-by-hop-template.ftl";
    public static final String EMAIL_RENOTIFICATION_NETROUTE_POLICY_HTML_TEMPLATE = "renotification-netroute-policy-template.ftl";
    public static final String EMAIL_SHARE_METRIC_POLICY_HTML_TEMPLATE = "share-metric-policy-template.ftl";
    public static final String EMAIL_SHARE_EVENT_POLICY_HTML_TEMPLATE = "share-event-policy-template.ftl";
    public static final String EMAIL_SHARE_TRAP_POLICY_HTML_TEMPLATE = "trap-policy-template.ftl";
    public static final String EMAIL_NOTIFICATION_REPORT_HTML_TEMPLATE = "report-template.ftl";
    public static final String EMAIL_NOTIFICATION_CONFIG_HTML_TEMPLATE = "config-template.ftl";
    public static final String EMAIL_NOTIFICATION_DAILY_LIMIT_NEARLY_REACHED_TEMPLATE = "daily-limit-nearly-reached-template.ftl";
    public static final String EMAIL_NOTIFICATION_DAILY_LIMIT_REACHED_TEMPLATE = "daily-limit-reached-template.ftl";
    public static final String EMAIL_NOTIFICATION_DISK_UTILIZATION_TEMPLATE = "disk-utilization-template.ftl";
    public static final String EMAIL_NOTIFICATION_COMPLIANCE_POLICY_SUCCESSFUL_HTML_TEMPLATE = "compliance-policy-successful-template.ftl";
    public static final String EMAIL_NOTIFICATION_PASSWORD_ABOUT_TO_EXPIRE_HTML_TEMPLATE = "password-about-to-expire-template.ftl";
    public static final String EMAIL_NOTIFICATION_PASSWORD_EXPIRED_HTML_TEMPLATE = "password-expired-template.ftl";


    //
    public static final JsonArray EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS = new JsonArray().add("fb.png").add("logo.png").add("linkedin.png").add("twitter.png");
    public static final String TEAMS_NOTIFICATION_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_TRAP_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Source:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.source}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.condition}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_EVENT_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Event Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Evaluation Window:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${evaluation.window}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.field}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.condition}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.mode}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image\\\\\\/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_RENOTIFICATION_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Active Since:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_NOTIFICATION_SEARCH_QUERY_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msteams\\\": {\\n        \\\"widget\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"Motadata Search\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"59px\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Query:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${search.query}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        },\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"auto\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Message:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${message}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"59px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\" ${user.name}\\\",\\n                                    \\\"wrap\\\": true\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_NOTIFICATION_WIDGET_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msteams\\\": {\\n        \\\"widget\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"${visualization.name}\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"Container\\\",\\n                    \\\"items\\\": [\\n                        {\\n                            \\\"type\\\": \\\"ColumnSet\\\",\\n                            \\\"columns\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"62px\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"Message:\\\",\\n                                            \\\"wrap\\\": true,\\n                                            \\\"weight\\\": \\\"Bolder\\\"\\n                                        }\\n                                    ]\\n                                },\\n                                {\\n                                    \\\"type\\\": \\\"Column\\\",\\n                                    \\\"width\\\": \\\"stretch\\\",\\n                                    \\\"items\\\": [\\n                                        {\\n                                            \\\"type\\\": \\\"TextBlock\\\",\\n                                            \\\"text\\\": \\\"${message}\\\",\\n                                            \\\"wrap\\\": true\\n                                        }\\n                                    ]\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        },\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\" ${user.name}\\\",\\n                                    \\\"wrap\\\": true\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_NOTIFICATION_METRIC_EXPLORER_SHARE_JSON_TEMPLATE = "{\n    \"subject\": null,\n    \"body\": {\n        \"contentType\": \"html\",\n        \"content\": \"<div class=\\\"motadata-msg\\\"><attachment id=\\\"motadata\\\"></attachment><img  src=\\\"../hostedContents/1/$value\\\" style=\\\"width: auto; height: auto;\\\"></div>\"\n    },\n    \"attachments\": [\n        {\n            \"id\": \"motadata\",\n            \"contentType\": \"application/vnd.microsoft.card.adaptive\",\n            \"contentUrl\": null,\n            \"content\": \"{\\n    \\\"type\\\": \\\"AdaptiveCard\\\",\\n    \\\"$schema\\\": \\\"https://adaptivecards.io/schemas/adaptive-card.json\\\",\\n    \\\"version\\\": \\\"1.5\\\",\\n    \\\"msTeams\\\": {\\n        \\\"width\\\": \\\"full\\\"\\n    },\\n    \\\"body\\\": [\\n        {\\n            \\\"type\\\": \\\"Container\\\",\\n            \\\"spacing\\\": \\\"None\\\",\\n            \\\"items\\\": [\\n                {\\n                    \\\"type\\\": \\\"TextBlock\\\",\\n                    \\\"text\\\": \\\"Metric Explorer\\\",\\n                    \\\"wrap\\\": true,\\n                    \\\"weight\\\": \\\"Bolder\\\",\\n                    \\\"color\\\": \\\"Accent\\\",\\n                    \\\"spacing\\\": \\\"None\\\"\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"spacing\\\": \\\"Small\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Message:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"${message}\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                },\\n                {\\n                    \\\"type\\\": \\\"ColumnSet\\\",\\n                    \\\"spacing\\\": \\\"Small\\\",\\n                    \\\"columns\\\": [\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"62px\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"Sent By:\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"weight\\\": \\\"Bolder\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        },\\n                        {\\n                            \\\"type\\\": \\\"Column\\\",\\n                            \\\"width\\\": \\\"stretch\\\",\\n                            \\\"items\\\": [\\n                                {\\n                                    \\\"type\\\": \\\"TextBlock\\\",\\n                                    \\\"text\\\": \\\"${user.name}\\\",\\n                                    \\\"wrap\\\": true,\\n                                    \\\"spacing\\\": \\\"None\\\",\\n                                    \\\"spacing\\\": \\\"None\\\"\\n                                }\\n                            ]\\n                        }\\n                    ]\\n                }\\n            ]\\n        }\\n    ]\\n}\",\n            \"name\": null,\n            \"thumbnailUrl\": null\n        }\n    ],\n    \"hostedContents\": [\n        {\n            \"@microsoft.graph.temporaryId\": \"1\",\n            \"contentBytes\": \"${content}\",\n            \"contentType\": \"image/png\"\n        }\n    ]\n}";
    public static final String TEAMS_SHARE_METRIC_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\",\\\"color\\\":\\\"${severity.color}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${object.name} | ${object.target}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Object Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${object.type}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true,\\\"color\\\":\\\"${severity.color}\\\"}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Counter:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${metric}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Triggered Time:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${Timestamp}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Action:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.action}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Active Since:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT\\\\\\/bpqBf9skNwAAAAAA1B75\\\\\\/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_SHARE_EVENT_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Event Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Evaluation Window:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${evaluation.window}\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\"}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${event.field}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${triggerCondition}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Type:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${trigger.mode}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Value:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${value}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Last Seen:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${active.since}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${alert.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final String TEAMS_SHARE_TRAP_POLICY_JSON_TEMPLATE = "{\"subject\":null,\"body\":{\"contentType\":\"html\",\"content\":\"<attachment id=\\\"motadata\\\"><\\/attachment>\"},\"attachments\":[{\"id\":\"motadata\",\"contentType\":\"application\\/vnd.microsoft.card.adaptive\",\"contentUrl\":null,\"content\":\"{\\\"type\\\":\\\"AdaptiveCard\\\",\\\"speak\\\":\\\"Version 2.2 performance optimization\\\",\\\"body\\\":[{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${message}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"Table\\\",\\\"columns\\\":[{\\\"width\\\":\\\"3px\\\"},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"|\\\",\\\"wrap\\\":true,\\\"size\\\":\\\"Large\\\",\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity} - ${policy.name}\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"size\\\":\\\"Large\\\"}],\\\"showBorder\\\":false}],\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"style\\\":\\\"default\\\"}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false}]},{\\\"type\\\":\\\"Table\\\",\\\"targetWidth\\\":\\\"AtLeast:Narrow\\\",\\\"columns\\\":[{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1},{\\\"width\\\":1}],\\\"rows\\\":[{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Policy Name:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.name}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"showBorder\\\":false,\\\"roundedCorners\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Severity:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${severity}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Source:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${eventSource}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Metric:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${policy.type}\\\",\\\"wrap\\\":true}]}]},{\\\"type\\\":\\\"TableRow\\\",\\\"cells\\\":[{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Trigger Condition:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${triggerCondition}\\\",\\\"wrap\\\":true}]},{\\\"type\\\":\\\"TableCell\\\",\\\"rtl\\\":false,\\\"verticalContentAlignment\\\":\\\"Top\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\",\\\"text\\\":\\\"Last Seen:\\\"}]},{\\\"type\\\":\\\"TableCell\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"wrap\\\":true,\\\"text\\\":\\\"${active.since}\\\"}]}]}],\\\"firstRowAsHeaders\\\":false,\\\"horizontalAlignment\\\":\\\"Left\\\",\\\"horizontalCellContentAlignment\\\":\\\"Left\\\",\\\"verticalCellContentAlignment\\\":\\\"Center\\\",\\\"showGridLines\\\":false},{\\\"type\\\":\\\"Container\\\",\\\"showBorder\\\":false,\\\"items\\\":[{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"}]},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"auto\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"Alert Message:\\\",\\\"wrap\\\":true,\\\"weight\\\":\\\"Bolder\\\"}]},{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"TextBlock\\\",\\\"text\\\":\\\"${alert.message}\\\",\\\"wrap\\\":true}]}]}]},{\\\"type\\\":\\\"Image\\\",\\\"url\\\":\\\"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAABVgAAAABCAYAAAArQ8VJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAgSURBVHgB7cAxAQAACAIwrUT/bpqBf9skNwAAAAAA1B75/gGXfzlCzgAAAABJRU5ErkJggg==\\\"},{\\\"type\\\":\\\"Container\\\",\\\"items\\\":[{\\\"type\\\":\\\"ColumnSet\\\",\\\"columns\\\":[{\\\"type\\\":\\\"Column\\\",\\\"width\\\":\\\"stretch\\\",\\\"items\\\":[{\\\"type\\\":\\\"ActionSet\\\",\\\"actions\\\":[{\\\"type\\\":\\\"Action.OpenUrl\\\",\\\"iconUrl\\\":\\\"icon:Alert\\\",\\\"title\\\":\\\"View Alert in AIOps\\\",\\\"style\\\":\\\"positive\\\",\\\"url\\\":\\\"${policy.url}\\\"}],\\\"horizontalAlignment\\\":\\\"Left\\\"}]}]}],\\\"horizontalAlignment\\\":\\\"Left\\\"}],\\\"version\\\":\\\"1.5\\\",\\\"msTeams\\\":{\\\"width\\\":\\\"full\\\"},\\\"$schema\\\":\\\"https://adaptivecards.io/schemas/adaptive-card.json\\\"}\",\"name\":null,\"thumbnailUrl\":null}]}";
    public static final Set<String> SMS_GATEWAY_ERROR_CODES = new HashSet<>(MotadataConfigUtil.getSMSGatewayErrors());

    /**
     * Sends an email notification with the specified subject, message, and recipients.
     * <p>
     * This method creates a notification context with the required fields and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param subject    The subject line of the email
     * @param message    The body content of the email
     * @param recipients A JsonArray containing the email addresses of the recipients
     */
    public static void sendEmail(String subject, String message, JsonArray recipients)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName()).put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, recipients)
                .put(Notification.EMAIL_NOTIFICATION_MESSAGE, message).put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject));
    }

    /**
     * Sends an email notification using a pre-configured context object.
     * <p>
     * This method adds the EMAIL notification type to the context and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param context A JsonObject containing all the necessary email notification parameters
     *                (recipients, subject, message, attachments, etc.)
     */
    public static void sendEmail(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context.put(NOTIFICATION_TYPE, NotificationType.EMAIL.getName()));
    }


    /**
     * Sends an SMS notification with the specified message to the given recipients.
     * <p>
     * This method creates a notification context with the required fields and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param message    The text message content to be sent
     * @param recipients A JsonArray containing the phone numbers of the recipients
     */
    public static void sendSMS(String message, JsonArray recipients)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(NOTIFICATION_TYPE, NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, recipients)
                .put(Notification.SMS_NOTIFICATION_MESSAGE, message));
    }

    /**
     * Sends an SNMP trap notification using the provided event data and forwarder configuration.
     * <p>
     * This method adds the SNMP_TRAP notification type and target information to the event
     * and publishes it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param event     A JsonObject containing the event data to be sent as an SNMP trap
     * @param forwarder A JsonObject containing the SNMP trap forwarder configuration
     *                  (destination host, port, etc.)
     */
    public static void sendSNMPTrap(JsonObject event, JsonObject forwarder)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, event.put(Notification.NOTIFICATION_TYPE, Notification.NotificationType.SNMP_TRAP.getName())
                .put(GlobalConstants.TARGET, forwarder.getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_HOST) + "/" + forwarder.getInteger(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_PORT)));
    }

    /**
     * Sends an SNMP trap notification using the provided event data map and forwarder configuration.
     * <p>
     * This method adds the SNMP_TRAP notification type and target information to the event map
     * and publishes it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param event     A Map containing the event data to be sent as an SNMP trap
     * @param forwarder A JsonObject containing the SNMP trap forwarder configuration
     *                  (destination host, port, etc.)
     */
    public static void sendSNMPTrap(Map<String, Object> event, JsonObject forwarder)
    {
        event.put(Notification.NOTIFICATION_TYPE, Notification.NotificationType.SNMP_TRAP.getName());
        event.put(GlobalConstants.TARGET, forwarder.getString(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_HOST) + "/" + forwarder.getInteger(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_DESTINATION_PORT));
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, event);
    }

    /**
     * Sends a sound notification using the provided context.
     * <p>
     * This method adds the SOUND notification type to the context and publishes
     * it to the EVENT_NOTIFICATION address on the Vert.x EventBus.
     *
     * @param context A JsonObject containing the sound notification parameters
     */
    public static void playSound(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context.put(NOTIFICATION_TYPE, NotificationType.SOUND.getName()));
    }

    /**
     * Sends a generic notification using the provided context.
     * <p>
     * This method publishes the context directly to the EVENT_NOTIFICATION address
     * on the Vert.x EventBus without modifying it. The context should already
     * contain all necessary notification parameters, including the notification type.
     *
     * @param context A JsonObject containing all the necessary notification parameters,
     *                including the notification type
     */
    public static void send(JsonObject context)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_NOTIFICATION, context);
    }

    /**
     * Retrieves the appropriate notification template for a channel based on event type and channel type.
     * <p>
     * This method selects the correct template based on:
     * 1. The channel type (currently only Microsoft Teams is supported)
     * 2. The event type (TRAP, LOG, FLOW, or METRIC)
     * 3. Whether this is a renotification (for metric policies)
     *
     * @param eventType   The type of event (TRAP, LOG, FLOW, or METRIC)
     * @param channelType The notification channel type (e.g., Microsoft Teams)
     * @param renotify    Flag indicating whether this is a renotification
     * @return The appropriate template string for the notification, or EMPTY_VALUE if no matching template is found
     */
    public static String getChannelNotificationTemplate(String eventType, String channelType, boolean renotify)
    {
        var template = EMPTY_VALUE;

        if (channelType.equalsIgnoreCase(NotificationType.MICROSOFT_TEAMS.getName()))
        {
            if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(eventType))
            {
                template = TEAMS_NOTIFICATION_TRAP_POLICY_JSON_TEMPLATE;
            }
            else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(eventType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(eventType))
            {
                template = TEAMS_NOTIFICATION_EVENT_POLICY_JSON_TEMPLATE;
            }
            else
            {
                template = renotify ? TEAMS_RENOTIFICATION_METRIC_POLICY_JSON_TEMPLATE : TEAMS_NOTIFICATION_METRIC_POLICY_JSON_TEMPLATE;
            }
        }

        return template;
    }

    /**
     * Retrieves the appropriate notification subject based on notification type and share type.
     * <p>
     * This method selects the correct subject string based on:
     * 1. The notification type (currently only EMAIL is supported)
     * 2. The share type (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     *
     * @param notificationType The type of notification (e.g., EMAIL)
     * @param type             The type of share (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * @return The appropriate subject string for the notification, or EMPTY_VALUE if no matching subject is found
     */
    public static String getNotificationSubject(String notificationType, String type)
    {
        var subject = EMPTY_VALUE;

        if (notificationType.equalsIgnoreCase(NotificationType.EMAIL.getName()))
        {
            subject = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT -> InfoMessageConstants.ALERT_SHARE_NOTIFICATION_SUBJECT;

                case METRIC_EXPLORER -> InfoMessageConstants.METRIC_EXPLORER_SHARE_NOTIFICATION_SUBJECT;

                case SEARCH_QUERY -> InfoMessageConstants.SEARCH_QUERY_SHARE_NOTIFICATION_SUBJECT;

                case WIDGET -> InfoMessageConstants.WIDGET_SHARE_NOTIFICATION_SUBJECT;
            };
        }

        return subject;
    }

    /**
     * Retrieves the appropriate notification template based on notification type, share type, and alert type.
     * <p>
     * This method selects the correct template based on:
     * 1. The notification type (EMAIL or MICROSOFT_TEAMS)
     * 2. The share type (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * 3. For ALERT share type, the alert type (TRAP, LOG, FLOW, or METRIC)
     *
     * @param notificationType The type of notification (EMAIL or MICROSOFT_TEAMS)
     * @param type             The type of share (ALERT, METRIC_EXPLORER, SEARCH_QUERY, or WIDGET)
     * @param alertType        The type of alert (TRAP, LOG, FLOW, or METRIC), used only when type is ALERT
     * @return The appropriate template string for the notification, or EMPTY_VALUE if no matching template is found
     */
    public static String getNotificationTemplate(String notificationType, String type, String alertType)
    {
        var template = EMPTY_VALUE;

        if (notificationType.equalsIgnoreCase(NotificationType.MICROSOFT_TEAMS.getName()))
        {
            template = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT ->
                {
                    if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(alertType))
                    {
                        yield TEAMS_SHARE_TRAP_POLICY_JSON_TEMPLATE;
                    }
                    else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(alertType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(alertType))
                    {
                        yield TEAMS_SHARE_EVENT_POLICY_JSON_TEMPLATE;
                    }
                    else
                    {
                        yield TEAMS_SHARE_METRIC_POLICY_JSON_TEMPLATE;
                    }
                }

                case METRIC_EXPLORER -> TEAMS_NOTIFICATION_METRIC_EXPLORER_SHARE_JSON_TEMPLATE;

                case SEARCH_QUERY -> TEAMS_NOTIFICATION_SEARCH_QUERY_SHARE_JSON_TEMPLATE;

                case WIDGET -> TEAMS_NOTIFICATION_WIDGET_SHARE_JSON_TEMPLATE;

            };

        }
        else if (notificationType.equalsIgnoreCase(NotificationType.EMAIL.getName()))
        {
            template = switch (Notification.ShareType.valueOfName(type))
            {
                case ALERT ->
                {
                    if (PolicyEngineConstants.PolicyType.TRAP.getName().equalsIgnoreCase(alertType))
                    {
                        yield EMAIL_SHARE_TRAP_POLICY_HTML_TEMPLATE;
                    }
                    else if (PolicyEngineConstants.PolicyType.LOG.getName().equalsIgnoreCase(alertType) || PolicyEngineConstants.PolicyType.FLOW.getName().equalsIgnoreCase(alertType))
                    {
                        yield EMAIL_SHARE_EVENT_POLICY_HTML_TEMPLATE;
                    }
                    else
                    {
                        yield EMAIL_SHARE_METRIC_POLICY_HTML_TEMPLATE;
                    }
                }

                case METRIC_EXPLORER -> EMAIL_NOTIFICATION_METRIC_EXPLORER_HTML_TEMPLATE;

                case SEARCH_QUERY -> EMAIL_NOTIFICATION_SEARCH_QUERY_HTML_TEMPLATE;

                case WIDGET -> EMAIL_NOTIFICATION_WIDGET_HTML_TEMPLATE;

            };
        }

        return template;
    }

    /**
     * Abstract method that must be implemented by concrete notification classes.
     * <p>
     * This method is responsible for processing and sending a notification based on
     * the provided event data. The implementation should handle all aspects of
     * notification delivery, including formatting, addressing, and transmission.
     *
     * @param event A JsonObject containing the event data and notification parameters
     * @return A Promise that resolves to a JsonObject containing the result of the notification attempt
     */
    abstract Promise<JsonObject> notify(JsonObject event);

    /**
     * Enumeration of content types used for notification attachments and content.
     * <p>
     * These content types follow the MIME type standards as defined by IANA.
     *
     * @see <a href="https://www.iana.org/assignments/media-types/media-types.xhtml">IANA Media Types</a>
     */
    public enum ContentType
    {
        PDF("application/pdf"),
        CSV("text/csv"),
        HTML("text/html"),
        PNG("image/png"),
        JSON("application/json"),
        EXCEL("application/vnd.ms-excel"),
        JPG("image/jpg"),
        TEXT("text/plain");


        private final String type;

        ContentType(String type)
        {
            this.type = type;
        }

        public static String getType(String extension)
        {
            return switch (extension.toLowerCase(Locale.ROOT))
            {
                case "pdf" -> PDF.type;

                case "csv" -> CSV.type;

                case "png" -> PNG.type;

                case "json" -> JSON.type;

                case "xlsx" -> EXCEL.type;

                case "jpg" -> JPG.type;

                case "txt" -> TEXT.type;

                default -> HTML.type;
            };
        }
    }

    /**
     * Enumeration of supported notification types in the system.
     * <p>
     * This enum defines all the notification channels that can be used to deliver
     * notifications to users or external systems. Each notification type has a
     * display name that is used in the user interface and API.
     */
    public enum NotificationType
    {
        /**
         * Email notifications sent via SMTP
         */
        EMAIL("Email"),

        /**
         * SMS text message notifications
         */
        SMS("SMS"),

        /**
         * HTTP webhook notifications to external systems
         */
        WEBHOOK("Webhook"),

        /**
         * Push notifications to mobile devices or browsers
         */
        PUSH("Push"),

        /**
         * SNMP trap notifications to network management systems
         */
        SNMP_TRAP("SNMP Trap"),

        /**
         * Syslog notifications to logging systems
         */
        SYSLOG("Syslog"),

        /**
         * Sound alerts played on the local system
         */
        SOUND("Sound"),

        /**
         * Microsoft Teams channel notifications
         */
        MICROSOFT_TEAMS("Microsoft Teams");

        private static final Map<String, NotificationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(NotificationType::getName, e -> e)));
        private final String name;

        NotificationType(String name)
        {
            this.name = name;
        }

        public static NotificationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }


    /**
     * Enumeration of supported share types for notifications.
     * <p>
     * This enum defines the different types of content that can be shared via
     * notifications. Each share type has specific templates and handling logic
     * associated with it.
     */
    public enum ShareType
    {
        /**
         * Metric Explorer dashboard or view
         */
        METRIC_EXPLORER("Metric Explorer"),

        /**
         * Search query results
         */
        SEARCH_QUERY("Search Query"),

        /**
         * Alert information from policy violations
         */
        ALERT("Alert"),

        /**
         * Dashboard widget
         */
        WIDGET("Widget");

        private static final Map<String, ShareType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ShareType::getName, e -> e)));
        private final String name;

        ShareType(String name)
        {
            this.name = name;
        }

        public static ShareType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
