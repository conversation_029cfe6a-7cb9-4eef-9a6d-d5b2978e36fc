/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventPublisher;
import com.mindarray.eventbus.EventSubscriber;
import com.mindarray.ha.Observer;
import com.mindarray.job.JobScheduler;
import com.mindarray.util.LogUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.util.ProcessUtil;

import static com.mindarray.Bootstrap.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_PUBLICATION_MOTADATA_OBSERVER;

public class BootstrapObserver
{
    private static final Logger LOGGER = new Logger(BootstrapObserver.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Observer");

    public void start()
    {
        try
        {
            DatastoreConstants.loadConfigs();

            Bootstrap.logSystemConfig();

            ProcessUtil.setProcessors();

            startEngine(new EventPublisher(MotadataConfigUtil.getMotadataObserverEventPublisherPort(), "Motadata Observer Publisher"), EventPublisher.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventPublisherPort(), null, EVENT_PUBLICATION_MOTADATA_OBSERVER)
                    .compose(future -> startWorkerEngine(new EventSubscriber(MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), "Motadata Observer Subscriber"), EventSubscriber.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataObserverEventSubscriberPort(), null))
                    .compose(future -> startEngine(new Observer(), Observer.class.getSimpleName(), null))
                    .onComplete(response ->
                    {
                        if (response.succeeded())
                        {
                            LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                            EventBusConstants.setManagerRegistrationId();

                            JobScheduler.init();

                            LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, GlobalConstants.BootstrapType.OBSERVER));
                        }
                        else
                        {
                            stop(response.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
