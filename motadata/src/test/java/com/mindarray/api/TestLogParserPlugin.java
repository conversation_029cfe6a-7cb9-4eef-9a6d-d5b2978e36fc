/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.LogParserPluginConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.ErrorCodes.ERROR_CODE_BAD_REQUEST;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.LOG_PARSER_PLUGIN_API_ENDPOINT;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestLogParserPlugin
{
    private static final Logger LOGGER = new Logger(TestLogParserPlugin.class, "Test Log Parser Plugin", "Test Log Parser Plugin");
    private static String id;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testGetAll(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(LOG_PARSER_PLUGIN_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
                {

                    Assertions.assertEquals(SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty(), "expecting to have at least 1 default parser");

                    testContext.completeNow();
                }
        )));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testCreateLogParserPlugin(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("""
                {
                  "log.parser.plugin.name": "TestLogParserPlugin",
                  "log.parser.plugin.context": {
                    "log.parser.plugin.script": "package plugins;\\nimport io.vertx.core.json.JsonObject;\\n\\npublic class TestLogParserPlugin implements LogParserPlugin {\\n    public String parse(JsonObject event) \\n    {\\n        return null;\\n    }\\n}"
                  }
                }""");

        TestAPIUtil.post(LOG_PARSER_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    Assertions.assertEquals(SC_OK, response.statusCode());

                    Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    Assertions.assertEquals("Log Parser Plugin created successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    Assertions.assertNotNull(LogParserPluginConfigStore.getStore().getItemByValue(LogParserPlugin.LOG_PARSER_PLUGIN_NAME, "TestLogParserPlugin"));

                    id = response.bodyAsJsonObject().getString(ID);

                    testContext.completeNow();
                }
        )));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testCreateLogParserPluginInvalidScript(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("""
                {
                  "log.parser.plugin.name": "TestLogParserPlugin1",
                  "log.parser.plugin.context": {
                    "log.parser.plugin.script": "package plugins;\\nimport io.vertx.core.json.JsonObject;\\n\\npublic class TestLogParserPlugin1 implements LogParserPlugin {\\n    public String parse(JsonObject event)\\n    {\\n        return event123\\n    }\\n}"
                  }
                }""");

        TestAPIUtil.post(LOG_PARSER_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
                {

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    Assertions.assertEquals(SC_BAD_REQUEST, response.statusCode());

                    Assertions.assertEquals(GlobalConstants.STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    Assertions.assertEquals(ERROR_CODE_BAD_REQUEST, response.bodyAsJsonObject().getString(GlobalConstants.ERROR_CODE));

                    Assertions.assertFalse(response.bodyAsJsonObject().getString(MESSAGE).isEmpty());

                    testContext.completeNow();
                }
        )));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testCreateLogParserPluginInvalidMethod(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("""
                {
                  "log.parser.plugin.name": "test",
                  "log.parser.plugin.context": {
                    "log.parser.plugin.script": "package plugins;\\nimport io.vertx.core.json.JsonObject;\\n\\npublic class test {\\n    public static JsonObject function(JsonObject event)\\n    {\\n     return event;\\n    }\\n}"
                  }
                }""");

        TestAPIUtil.post(LOG_PARSER_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    Assertions.assertEquals(SC_BAD_REQUEST, response.statusCode());

                    Assertions.assertEquals(GlobalConstants.STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    Assertions.assertEquals("Failed to find parse method in the script", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    Assertions.assertEquals(ERROR_CODE_BAD_REQUEST, response.bodyAsJsonObject().getString(GlobalConstants.ERROR_CODE));

                    testContext.completeNow();
                }
        )));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testDeleteLogParserPlugin(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.delete(LOG_PARSER_PLUGIN_API_ENDPOINT + PATH_SEPARATOR + id, testContext.succeeding(response -> testContext.verify(() ->
                {

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    Assertions.assertEquals(SC_OK, response.statusCode());

                    Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    Assertions.assertEquals("Log Parser Plugin deleted successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    Assertions.assertNull(LogParserPluginConfigStore.getStore().getItemByValue(LogParserPlugin.LOG_PARSER_PLUGIN_NAME, "TestLogParserPlugin"));

                    testContext.completeNow();
                }
        )));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}


