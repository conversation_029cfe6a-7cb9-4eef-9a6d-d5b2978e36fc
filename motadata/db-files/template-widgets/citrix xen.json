[{"_type": "0", "id": 10000000000822, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.load.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.load.percent"}, {"type": "metric", "data.point": "citrix.xen.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "data.points": [{"label": "Load <PERSON>", "value": "citrix.xen.load.percent.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "citrix.xen.cpu.percent"}}}}, {"_type": "0", "id": 10000000000823, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.memory.used.bytes"}, {"type": "metric", "data.point": "citrix.xen.memory.free.bytes"}, {"type": "metric", "data.point": "citrix.xen.memory.installed.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "data.points": [{"label": "Capacity", "value": "citrix.xen.memory.installed.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "citrix.xen.memory.used.bytes.last"}, {"label": "Free", "value": "citrix.xen.memory.free.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "citrix.xen.memory.used.bytes"}}}}, {"_type": "0", "id": 10000000000824, "visualization.name": "Disk Utilization", "visualization.description": "Disk Utilization Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.disk.used.bytes"}, {"type": "metric", "data.point": "citrix.xen.disk.free.bytes"}, {"type": "metric", "data.point": "citrix.xen.disk.free.percent"}, {"type": "metric", "data.point": "citrix.xen.disk.capacity.bytes"}, {"type": "metric", "data.point": "citrix.xen.disk.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "data.points": [{"label": "Capacity", "value": "citrix.xen.disk.capacity.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "citrix.xen.disk.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "citrix.xen.disk.used.percent"}}}}, {"_type": "0", "id": 10000000000825, "visualization.name": "Overview", "visualization.description": "Overview Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.halted.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.virtual.machines"}, {"type": "metric", "data.point": "citrix.xen.running.virtual.machines"}, {"type": "metric", "data.point": "citrix.xen.halted.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Halted", "data.points": [{"label": "Halted", "value": "citrix.xen.halted.virtual.machines.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "VM", "value": "citrix.xen.virtual.machines.last"}, {"label": "Running", "value": "citrix.xen.running.virtual.machines.last"}]}, "style": {"icon": {"name": "cube"}, "color.data.point": "citrix.xen.running.virtual.machines"}}}}, {"_type": "0", "id": 10000000000826, "visualization.name": "Network", "visualization.description": "Network Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.network.in.bytes.per.sec"}, {"type": "metric", "data.point": "citrix.xen.network.out.bytes.per.sec"}, {"type": "metric", "data.point": "citrix.xen.network.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "data.points": [{"label": "Network", "value": "citrix.xen.network.bytes.per.sec.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "citrix.xen.network.in.bytes.per.sec.last"}, {"label": "Out", "value": "citrix.xen.network.out.bytes.per.sec.last"}]}, "style": {"icon": {"name": "cube"}, "color.data.point": "citrix.xen.network.out.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000827, "visualization.name": "API Memory", "visualization.description": "MAPI emory Utilization Citrix Xen", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.api.memory.allocated.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.api.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.api.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.api.memory.live.bytes"}, {"type": "metric", "data.point": "citrix.xen.api.used.memory.bytes"}, {"type": "metric", "data.point": "citrix.xen.api.memory.allocated.bytes"}, {"type": "metric", "data.point": "citrix.xen.api.memory.free.bytes"}, {"type": "metric", "data.point": "citrix.xen.api.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "API Memory", "data.points": [{"label": "Allocated", "value": "citrix.xen.api.memory.allocated.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "citrix.xen.api.memory.used.bytes.last"}, {"label": "Free", "value": "citrix.xen.api.memory.free.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "citrix.xen.api.memory.used.bytes"}}}}, {"_type": "0", "id": 10000000000828, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000829, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000830, "visualization.name": "Datastore Details", "visualization.description": "Citrix Xen Datastore  Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.datastore~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~allocation.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "citrix.xen.datastore", "title": "Data Store", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "citrix.xen.datastore~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "citrix.xen.datastore~capacity.bytes.last", "title": "Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "citrix.xen.datastore~allocation.bytes.last", "title": "Allocation", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "citrix.xen.datastore~used.bytes.last", "title": "Used Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "citrix.xen.datastore~used.percent.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"inline.chart": {"type": "gauge"}}}]}}}, {"_type": "0", "id": 10000000000831, "visualization.name": "Top 10 VM's By CPU Utilization", "visualization.description": "Top 10 VM's By CPU Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "citrix.xen.vm~cpu.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.vm~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000832, "visualization.name": "Top 10 VM's By Network Traffic", "visualization.description": "Top 10 VM's By Network Traffic Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~network.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~network.bytes.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "citrix.xen.vm~network.bytes.per.sec.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm~network.bytes.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.vm~network.bytes.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000833, "visualization.name": "Top 10 VM's By Memory Utilization", "visualization.description": "Top 10 VM's By Memory Capacity Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~memory.used.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "citrix.xen.vm~memory.used.bytes.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.vm~memory.used.bytes.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.vm~memory.used.bytes.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000834, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization (TimeSeries) Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000835, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization (TimeSeries) Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.memory.installed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000836, "visualization.name": "Disk Utilization", "visualization.description": "Disk Utilization (TimeSeries) Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.disk.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.disk.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000837, "visualization.name": "Top 10 Datastore By IOPS", "visualization.description": "Top 10 Datastore By IOPS Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.datastore~io.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~io.ops.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore", "title": "Datastore", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore~io.ops.per.sec.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore~io.ops.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.datastore~io.ops.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000838, "visualization.name": "Network Interface Details", "visualization.description": "Network Interface Details Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.network.interface~in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.network.interface~out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.network.interface~bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.network.interface~in.bytes.per.sec.avg", "title": "In Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "citrix.xen.network.interface~out.bytes.per.sec.avg", "title": "Out Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "citrix.xen.network.interface~bytes.per.sec.avg", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000000839, "visualization.name": "Top 10 Datastore By Write IO Latency", "visualization.description": "Top 10 Datastore By Write IO Latency Citrix Xen", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.datastore~io.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.datastore~io.write.latency.ms", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore", "title": "Datastore", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore~io.write.latency.ms.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.datastore~io.write.latency.ms.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.datastore~io.write.latency.ms.avg"}}, "sparkline": "yes"}}]