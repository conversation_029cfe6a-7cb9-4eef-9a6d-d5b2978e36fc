/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.nms;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.SNMPTrapForwarder;
import com.mindarray.api.SNMPTrapProfile;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.snmp4j.PDU;
import org.snmp4j.PDUv1;
import org.snmp4j.mp.SnmpConstants;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.TRAP_MESSAGE_TOO_BIG_ERROR;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.INTERFACE;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;

/**
 * The SNMPTrapProcessor class is responsible for processing SNMP trap messages received from network devices.
 * <p>
 * This class handles the following operations:
 * <ul>
 *   <li>Initializing and starting the SNMP trap listener</li>
 *   <li>Processing incoming SNMP trap messages</li>
 *   <li>Parsing trap PDUs and extracting relevant information</li>
 *   <li>Forwarding traps to configured destinations</li>
 *   <li>Generating alerts based on link up/down traps</li>
 *   <li>Maintaining a cache of interface names for correlation</li>
 * </ul>
 * <p>
 * The processor supports SNMP versions 1, 2c, and 3, and handles various trap types including
 * link up/down, cold/warm start, authentication failure, and enterprise-specific traps.
 */
public class SNMPTrapProcessor extends AbstractVerticle
{
    /**
     * Key for the generic name of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_GENERIC_NAME = "trap.generic.name";
    /**
     * Key for the vendor of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_VENDOR = "trap.vendor";
    /**
     * Key for the OID (Object Identifier) of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_OID = "trap.oid";
    /**
     * Key for the raw message content of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_RAW_MESSAGE = "trap.raw.message";
    /**
     * Key for the formatted message of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_MESSAGE = "trap.message";
    /**
     * Key for the variables contained in the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_VARIABLES = "trap.variables";
    /**
     * Key for the acknowledgment status of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_ACK_STATUS = "trap.ack.status";
    /**
     * Key for the acknowledgments of the SNMP trap in JSON objects
     */
    public static final String SNMP_TRAP_ACKS = "trap-acks";
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(SNMPTrapProcessor.class, GlobalConstants.MOTADATA_NMS, "SNMP Trap Processor");
    /**
     * Key for the enterprise ID of the SNMP trap in JSON objects
     */
    private static final String SNMP_TRAP_ENTERPRISE_ID = "trap.enterprise.id";
    /**
     * Key for the enterprise-specific information of the SNMP trap in JSON objects
     */
    private static final String SNMP_TRAP_ENTERPRISE = "trap.enterprise";
    /**
     * Key for the community string of the SNMP trap in JSON objects
     */
    private static final String SNMP_TRAP_COMMUNITY = "trap.community";
    /**
     * Key for the type of the SNMP trap in JSON objects
     */
    private static final String SNMP_TRAP_TYPE = "trap.type";
    /**
     * Default value for unknown or unspecified trap types
     */
    private static final String OTHER = "Other";

    /**
     * Maximum size in bytes for trap messages to prevent memory issues
     */
    private static final int MAX_TRAP_SIZE_BYTES = MotadataConfigUtil.getMaxTrapSizeBytes();
    /**
     * Index value of trap-variable-bindings, in which we receive Down or Up interface index.
     * By default, we receive interface index value in the 2nd index of trap variable bindings.
     */
    private static final int INTERFACE_INDEX_MAPPING = 2;
    /**
     * Map to store trap types by their OIDs
     */
    private final Map<String, String> trapTypes = new HashMap<>();
    /**
     * Map to store forwarder profiles by trap profile ID
     */
    private final Map<Long, JsonArray> forwarderProfiles = new HashMap<>();
    /**
     * StringBuilder used for building messages
     */
    private final StringBuilder builder = new StringBuilder(0);
    /**
     * Map to store interface names by source IP address
     */
    private final Map<String, Map<String, Long>> interfaceNamesBySource = new HashMap<>();
    /**
     * Set of source IP addresses from which traps have been received
     */
    private Set<String> sources;
    /**
     * Event engine for handling trap events
     */
    private EventEngine eventEngine;
    /**
     * Set of mapper names for data transformation
     */
    private Set<String> mappers;
    /**
     * Flag indicating whether streaming is enabled
     */
    private boolean streaming = false;

    /**
     * Initializes and starts the SNMP trap processor.
     * <p>
     * This method performs the following operations:
     * <ul>
     *   <li>Initializes collections for sources and mappers</li>
     *   <li>Loads trap types from the database</li>
     *   <li>Loads SNMP trap forwarder profiles</li>
     *   <li>Sets up interface name mappings for correlation</li>
     *   <li>Registers event bus handlers for change notifications</li>
     *   <li>Initializes and starts the SNMP trap listener</li>
     *   <li>Sets up the event engine for processing trap events</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        sources = new HashSet<>();

        mappers = new HashSet<>();

        vertx.<Void>executeBlocking(future ->
        {
            try
            {

                var cipherUtil = new CipherUtil();

                for (var entry : new JsonObject(cipherUtil.decrypt(vertx.fileSystem()
                        .readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + "trap-types.db").toString()))
                        .getMap().entrySet())
                {
                    if (entry.getValue() instanceof String)
                    {
                        trapTypes.put(entry.getKey(), CommonUtil.getString(entry.getValue()));
                    }
                }

            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            future.complete();
        }, false, result ->
        {
        });

        var items = SNMPTrapForwarderConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            loadSNMPTrapForwarderProfiles(items.getJsonObject(index));
        }

        //will be loading interface index with its metric id along with object ip as key
        items = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());

        for (var index = 0; index < items.size(); index++)
        {
            setInterfaceName(items.getJsonObject(index));
        }

        //update trap forwarder cache when change notification event received
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                switch (EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case SNMP_TRAP_FORWARDER ->
                    {
                        if (event.containsKey(GlobalConstants.ID))
                        {
                            loadSNMPTrapForwarderProfiles(SNMPTrapForwarderConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID)));
                        }

                        else
                        {
                            var entities = SNMPTrapForwarderConfigStore.getStore().getItems();

                            for (var index = 0; index < entities.size(); index++)
                            {
                                loadSNMPTrapForwarderProfiles(entities.getJsonObject(index));
                            }
                        }
                    }

                    case START_TRAP_TAIL -> streaming = true;

                    case STOP_TRAP_TAIL -> streaming = false;
                    case ADD_METRIC ->
                    {

                        var item = MetricConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                        if (NMSConstants.MetricPlugin.SNMP_INTERFACE.getName().equalsIgnoreCase(item.getString(Metric.METRIC_PLUGIN)))
                        {
                            setInterfaceName(item);
                        }
                    }

                    case DELETE_OBJECT ->
                    {
                        if (!event.containsKey(NMSConstants.METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                        {
                            var item = ObjectConfigStore.getStore().getItem(event.getLong(GlobalConstants.ID));

                            if (NMSConstants.Category.NETWORK.getName().equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                            {
                                interfaceNamesBySource.remove(item.getString(AIOpsObject.OBJECT_IP));
                            }
                        }
                    }

                }

            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        //as auto clear not required so commenting it
        // trap auto clear timer execute on every 10 sec to check particular trap profile which is qualified for auto clear
          /*  vertx.setPeriodic(10000,handler ->
            {
                for(var item : SNMPTrapProfileConfigStore.getStore().getAutoClearProfileByTimer().entrySet())
                {
                    try
                    {
                        if(contexts.containsKey(item.getKey()) && ( DateTimeUtil.currentSeconds() - contexts.get(item.getKey()).getLong(EventBusConstants.EVENT_TIMESTAMP)) >= (item.getValue() * 60))
                        {
                            // remove trap cache
                            DBConstants.write(enrich(contexts.remove(item.getKey())
                                    .put(SNMP_TRAP_SEVERITY, GlobalConstants.Severity.CLEAR.name())
                                    .put(SNMP_TRAP_MESSAGE, String.format(InfoMessageConstants.SNMP_TRAP_AUTO_CLEAR_BY_SYSTEM, SNMPTrapProfileConfigStore.getStore().getItem(item.getKey()).getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_OID))))
                                    .put(PluginEngineConstants.PLUGIN_ID, DBConstants.PluginId.TRAP_EVENT.getName())
                                    .put(EVENT_DB_TYPE, DBConstants.EventDBType.TRAP.ordinal())
                                    .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()), serializationUtil);
                        }
                    }
                    catch (Exception exception)
                    {
                        logger.error(exception);
                    }
                }
            });*/

        SNMPTrapListener.init();

        SNMPTrapListener.start();

        eventEngine = new EventEngine().setEventType(EVENT_TRAP).setPersistEventOffset(true)
                .setLogger(LOGGER).setEventHandler(event ->
                {
                    try
                    {
                        var pdu = (PDU) CodecUtil.toObject(event.getBinary(EVENT));

                        if (pdu != null)
                        {
                            var trap = new JsonObject();

                            var variables = new JsonObject();

                            var variableValues = new JsonArray();

                            var trapVariables = new JsonObject();

                            var version = TrapVersion.V1.getName();

                            if (event.getInteger(NMSConstants.SNMP_TRAP_VERSION) == SnmpConstants.version3)
                            {
                                version = TrapVersion.V3.getName();
                            }

                            else if (event.getInteger(NMSConstants.SNMP_TRAP_VERSION) == SnmpConstants.version2c)
                            {
                                version = TrapVersion.V2C.getName();
                            }

                            if (pdu.getType() == PDU.V1TRAP)
                            {
                                var pduV1 = (PDUv1) pdu;

                                trap.put(SNMP_TRAP_ENTERPRISE_ID, pduV1.getEnterprise().toString());

                                if (pduV1.getGenericTrap() == PDUv1.LINKUP)
                                {
                                    trap.put(SNMP_TRAP_OID, SnmpConstants.linkUp.toString());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Link Up");
                                }

                                else if (pduV1.getGenericTrap() == PDUv1.LINKDOWN)
                                {
                                    trap.put(SNMP_TRAP_OID, SnmpConstants.linkDown.toString());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Link Down");
                                }

                                else if (pduV1.getGenericTrap() == PDUv1.COLDSTART)
                                {
                                    trap.put(SNMP_TRAP_OID, SnmpConstants.coldStart.toString());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Cold Start");
                                }

                                else if (pduV1.getGenericTrap() == PDUv1.WARMSTART)
                                {
                                    trap.put(SNMP_TRAP_OID, SnmpConstants.warmStart.toString());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Warm Start");
                                }

                                else if (pduV1.getGenericTrap() == PDUv1.AUTHENTICATIONFAILURE)
                                {
                                    trap.put(SNMP_TRAP_OID, SnmpConstants.authenticationFailure.toString());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Authentication Failure");
                                }

                                else if (pduV1.getGenericTrap() == PDUv1.ENTERPRISE_SPECIFIC)
                                {
                                    trap.put(SNMP_TRAP_OID, pduV1.getEnterprise().toString());

                                    trap.put(SNMP_TRAP_ENTERPRISE, pduV1.getSpecificTrap());

                                    trap.put(SNMP_TRAP_GENERIC_NAME, "Enterprise Specific");
                                }
                            }

                            for (var index = 0; index < pdu.size(); index++)
                            {
                                var variableBinding = pdu.get(index);

                                var oid = CommonUtil.getString(variableBinding.getOid());

                                var value = CommonUtil.getString(variableBinding.getVariable());

                                variables.put(oid, value);

                                variableValues.add(value);

                                if (variableBinding.getOid().equals(SnmpConstants.snmpTrapEnterprise))
                                {
                                    trap.put(SNMP_TRAP_ENTERPRISE_ID, variables.getString(SnmpConstants.snmpTrapEnterprise.toString()));
                                }

                                if (variableBinding.getOid().equals(SnmpConstants.snmpTrapCommunity))
                                {
                                    trap.put(SNMP_TRAP_COMMUNITY, variables.getString(SnmpConstants.snmpTrapCommunity.toString()));
                                }

                                if (variableBinding.getOid().equals(SnmpConstants.snmpTrapOID))
                                {
                                    trap.put(SNMP_TRAP_OID, value);
                                }

                                if (!oid.startsWith("."))
                                {
                                    oid = "." + oid;
                                }

                                trapVariables.put(trapTypes.getOrDefault(oid, oid), value);
                            }

                            if (trap.getString(SNMP_TRAP_OID) != null)
                            {
                                var profileOID = !trap.getString(SNMP_TRAP_OID).startsWith(".") ? "." + trap.getString(SNMP_TRAP_OID) : trap.getString(SNMP_TRAP_OID);

                                trap.put(SNMP_TRAP_OID, profileOID);

                                if (SNMPTrapProfileConfigStore.getStore().getFilterProfileId(profileOID).equals(GlobalConstants.DUMMY_ID))
                                {
                                    var profile = SNMPTrapProfileConfigStore.getStore().getTrapProfiles(profileOID);

                                    var translator = profile != null && CommonUtil.getString(profile.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_TRANSLATOR)) != null ? profile.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_TRANSLATOR) : GlobalConstants.EMPTY_VALUE;

                                    if (!translator.trim().isEmpty())
                                    {
                                        var index = 1; //as discussed we will start with index 1...n

                                        if (!trapVariables.isEmpty())
                                        {
                                            for (var variable : trapVariables.getMap().values())
                                            {
                                                if (translator.contains("$" + index))
                                                {
                                                    translator = translator.replace("$" + index, CommonUtil.getString(variable));
                                                }

                                                index++;
                                            }
                                        }
                                    }

                                    else
                                    {
                                        translator = String.format("Trap %s received from %s at %s ", profileOID, event.getString(EVENT_SOURCE), DateTimeUtil.timestamp(event.getLong(EVENT_TIMESTAMP) * 1000));

                                        if (!trapVariables.isEmpty())
                                        {
                                            translator = translator.concat(trapVariables.encodePrettily());
                                        }
                                    }

                                    var tokens = profileOID.trim().split("\\.");    // MOTADATA-3022

                                    if (variables.toString().length() > MAX_TRAP_SIZE_BYTES)
                                    {
                                        trap.put(SNMP_TRAP_RAW_MESSAGE, variables.toString().substring(0, MAX_TRAP_SIZE_BYTES));

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace(String.format(TRAP_MESSAGE_TOO_BIG_ERROR, variables, MAX_TRAP_SIZE_BYTES));
                                        }
                                    }
                                    else
                                    {
                                        trap.put(SNMP_TRAP_RAW_MESSAGE, variables.encodePrettily());
                                    }

                                    trap.put(NMSConstants.SNMP_TRAP_VERSION, version)
                                            .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                            .put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                                            .put(SNMP_TRAP_TYPE, trapTypes.get(profileOID) != null ? trapTypes.get(profileOID) : OTHER)
                                            .put(SNMP_TRAP_MESSAGE, translator)
                                            .put(SNMP_TRAP_VENDOR, (tokens.length > 7 && SNMPTrapVendorCacheStore.getStore().getItem(tokens[7]) != null) ? SNMPTrapVendorCacheStore.getStore().getItem(tokens[7]) : OTHER);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("received trap event %s", CommonUtil.getString(trap)));
                                    }

                                    TrapCacheStore.getStore().updateItem(event.getString(EVENT_SOURCE) + DASH_SEPARATOR + trap.getString(SNMP_TRAP_OID), GlobalConstants.NO);

                                    DatastoreConstants.write(trap.put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.TRAP_EVENT.getName())
                                            .put(DATASTORE_TYPE, DatastoreConstants.DatastoreType.TRAP.ordinal())
                                            .put(EVENT_SOURCE, event.getString(EVENT_SOURCE)), VisualizationConstants.VisualizationDataSource.TRAP.getName(), mappers, builder);

                                    if (streaming)
                                    {
                                        vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.TRAP_TAIL.getName()).put(EVENT_CONTEXT, new JsonObject().put(SNMP_TRAP_OID, trap.getString(SNMP_TRAP_OID)).put(SNMP_TRAP_MESSAGE, translator).put(SNMP_TRAP_RAW_MESSAGE, variables.encodePrettily()).put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                                                .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))));
                                    }

                                    if (!sources.contains(event.getString(EVENT_SOURCE)))
                                    {
                                        sources.add(event.getString(EVENT_SOURCE));

                                        // Register Event to Event Object Manager
                                        vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                                                new JsonObject().put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                                                        .put(EVENT, EVENT_TRAP)
                                                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE)
                                                        .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.TRAP_EVENT.getName()));
                                    }

                                    if (profile != null) //forward trap for created profile onlxy...
                                    {
                                        var forwarders = SNMPTrapForwarderConfigStore.getStore().getItems(forwarderProfiles.get(profile.getLong(GlobalConstants.ID)));

                                        if (forwarders != null)
                                        {
                                            for (var index = 0; index < forwarders.size(); index++)
                                            {
                                                Notification.sendSNMPTrap(event, forwarders.getJsonObject(index));
                                            }
                                        }

                                        /*contexts.put(profile.getLong(GlobalConstants.ID), trap);

                                        If auto clear oid received in trap than clear that trap
                                            if(contexts.containsKey(SNMPTrapProfileConfigStore.getStore().getAutoClearProfileByOID(profileOID)))
                                            {
                                                DBConstants.write(enrich(contexts.get(SNMPTrapProfileConfigStore.getStore().getAutoClearProfileByOID(profileOID))
                                                        .put(SNMP_TRAP_SEVERITY, GlobalConstants.Severity.CLEAR.name())
                                                        .put(SNMP_TRAP_MESSAGE, String.format(InfoMessageConstants.SNMP_TRAP_AUTO_CLEAR_BY_OID,profileOID,trap.getString(SNMPTrapProfile.SNMP_TRAP_PROFILE_AUTO_CLEAR_OID))))
                                                        .put(PluginEngineConstants.PLUGIN_ID, DBConstants.PluginId.TRAP_EVENT.getName())
                                                        .put(EVENT_DB_TYPE, DBConstants.EventDBType.TRAP.ordinal())
                                                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                                        .put(EVENT_SOURCE, event.get(EVENT_SOURCE)), serializationUtil);

                                                contexts.remove(SNMPTrapProfileConfigStore.getStore().getAutoClearProfileByOID(profileOID));
                                            }*/
                                    }

                                    //need to send list of trap variables in policy to validate policy filters
                                    vertx.eventBus().send(EventBusConstants.EVENT_EVENT_POLICY_QUALIFY, trap.put(EVENT, EVENT_TRAP).put(SNMP_TRAP_VARIABLES, variableValues));

                                    //if we receive any linkDown or linkUp trap we will generate Alert according to it...

                                    var status = getLinkStatus(profileOID);

                                    if (!EMPTY_VALUE.equalsIgnoreCase(status))
                                    {
                                        if (interfaceNamesBySource.containsKey(trap.getString(EVENT_SOURCE)))
                                        {
                                            var context = interfaceNamesBySource.get(trap.getString(EVENT_SOURCE));

                                            if (!variableValues.isEmpty() && variableValues.size() >= INTERFACE_INDEX_MAPPING && context.containsKey(variableValues.getString(INTERFACE_INDEX_MAPPING)))
                                            {
                                                var metric = MetricConfigStore.getStore().getItem(context.get(variableValues.getString(INTERFACE_INDEX_MAPPING)));

                                                var entries = new JsonArray();

                                                var item = new JsonObject();

                                                item.put(NMSConstants.INTERFACE, variableValues.getString(INTERFACE_INDEX_MAPPING));

                                                item.put(STATUS, status);

                                                entries.add(item);

                                                //sending poll response to response processor, it will redirect this event to Metric Policy...
                                                vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)).mergeIn(metric.put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(RESULT, new JsonObject().put(INTERFACE, entries)).put(PLUGIN_ID, metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PLUGIN_ID))));

                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace("Interface: " + variableValues.getString(INTERFACE_INDEX_MAPPING) + " Status: " + status + " Event Source: " + trap.getString(EVENT_SOURCE));
                                                }
                                            }
                                            else
                                            {
                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace("Does not qualify Interface dose not qualify, " + trap);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace(trap.getString(EVENT_SOURCE) + ", event source is not provisioned, " + trap);
                                            }
                                        }
                                    }
                                }

                                else
                                {
                                    LOGGER.info(String.format("trap dropped due to filter: %s", profileOID));
                                }
                            }
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                }).start(vertx, promise);
    }

    /**
     * Loads SNMP trap forwarder profiles from the configuration.
     * <p>
     * This method processes a trap forwarder configuration item and updates the internal
     * forwarderProfiles map, which associates trap profile IDs with the forwarders that
     * should receive traps matching those profiles.
     *
     * @param item The SNMP trap forwarder configuration item to process
     */
    private void loadSNMPTrapForwarderProfiles(JsonObject item)
    {
        if (item != null && item.containsKey(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_PROFILES))
        {
            var trapProfiles = item.getJsonArray(SNMPTrapForwarder.SNMP_TRAP_FORWARDER_PROFILES);

            for (var index = 0; index < trapProfiles.size(); index++)
            {
                var trapProfile = trapProfiles.getLong(index);

                if (!forwarderProfiles.containsKey(trapProfile))
                {
                    forwarderProfiles.put(trapProfile, new JsonArray(new ArrayList<>(1)).add(item.getLong(GlobalConstants.ID)));
                }
                else if (!forwarderProfiles.get(trapProfile).contains(item.getLong(GlobalConstants.ID)))
                {
                    forwarderProfiles.get(trapProfile).add(item.getLong(GlobalConstants.ID));
                }
            }
        }
    }

    /**
     * Stops the SNMP trap processor.
     * <p>
     * This method performs the following operations:
     * <ul>
     *   <li>Stops the SNMP trap listener</li>
     *   <li>Stops the event engine for processing trap events</li>
     * </ul>
     *
     * @param promise Promise to be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        SNMPTrapListener.stop();

        eventEngine.stop(vertx, promise);
    }

    /**
     * Sets up interface name mappings for a metric item.
     * <p>
     * This method processes a metric item (typically an SNMP interface metric) and builds
     * a mapping between interface indices and metric IDs. This mapping is stored in the
     * interfaceNamesBySource map, indexed by the source IP address of the device.
     * <p>
     * These mappings are used to correlate interface-related traps (like linkUp/linkDown)
     * with the corresponding interface metrics.
     *
     * @param item The metric item containing interface information
     */
    private void setInterfaceName(JsonObject item)
    {
        try
        {
            var context = new HashMap<String, Long>();

            var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

            if (objects != null && !objects.isEmpty())
            {
                for (var j = 0; j < objects.size(); j++)
                {
                    context.put(objects.getJsonObject(j).getString(NMSConstants.INTERFACE_INDEX), item.getLong(ID));
                }

                interfaceNamesBySource.put(ObjectConfigStore.getStore().getItem(item.getLong(Metric.METRIC_OBJECT), false).getString(AIOpsObject.OBJECT_IP), context);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Determines the link status based on the trap OID.
     * <p>
     * This method maps standard SNMP trap OIDs for linkUp and linkDown to
     * the corresponding status values used in the system.
     * <p>
     * The following mappings are used:
     * <ul>
     *   <li>.*******.*******.5.3 (linkDown) -> STATUS_DOWN</li>
     *   <li>.*******.*******.5.4 (linkUp) -> STATUS_UP</li>
     * </ul>
     *
     * @param oid The SNMP trap OID to check
     * @return The corresponding link status or an empty value if the OID is not related to link status
     */
    private String getLinkStatus(String oid)
    {
        return switch (oid)
        {
            case ".*******.*******.5.3" -> STATUS_DOWN;

            case ".*******.*******.5.4" -> STATUS_UP;

            default -> EMPTY_VALUE;
        };
    }

    /**
     * Enumeration of supported SNMP trap versions.
     * <p>
     * This enum represents the different versions of SNMP traps that can be processed:
     * <ul>
     *   <li>V1: SNMP version 1 traps</li>
     *   <li>V2C: SNMP version 2c traps</li>
     *   <li>V3: SNMP version 3 traps</li>
     * </ul>
     */
    public enum TrapVersion
    {
        /**
         * SNMP version 1 traps
         */
        V1("v1"),
        /**
         * SNMP version 2c traps
         */
        V2C("v2c"),
        /**
         * SNMP version 3 traps
         */
        V3("v3");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, TrapVersion> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TrapVersion::getName, e -> e)));
        /**
         * The string representation of the trap version
         */
        private final String name;

        /**
         * Constructor for TrapVersion enum.
         *
         * @param name The string representation of the trap version
         */
        TrapVersion(String name)
        {
            this.name = name;
        }

        /**
         * Gets a TrapVersion enum value by its string name.
         *
         * @param name The string name of the trap version
         * @return The corresponding TrapVersion enum value, or null if not found
         */
        public static TrapVersion valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string representation of this trap version.
         *
         * @return The string name of the trap version
         */
        public String getName()
        {
            return name;
        }
    }
}
