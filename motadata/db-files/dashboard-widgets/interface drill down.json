[{"_type": "0", "id": 10000000001813, "visualization.name": "Interface Overview", "visualization.category": "Grid", "visualization.type": "Interface", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~operational.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~admin.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~speed.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"layout": "overview", "searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 2, "type": "interface", "style": {}}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "interface~admin.status.last", "title": "Admin Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "interface~operational.status.last", "title": "Operational Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "interface~traffic.utilization.percent.last", "title": "Traffic Utilization", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~in.traffic.utilization.percent.last", "title": "IN (%)", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.last", "title": "OUT (%)", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~type.last", "title": "Port Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "interface~error.packets.last", "title": "Error", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "times-circle", "classes": ["text-secondary-red"]}}}, {"name": "interface~discard.packets.last", "title": "Discarded", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "trash", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 12, "style": {}}, {"name": "interface~name.last", "show": "no", "sortable": "no", "disable": "no", "resizable": "no", "selectable": "no", "orderable": "no", "position": 13}, {"name": "vlan", "title": "VLAN", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 14, "type": "interface", "style": {}}, {"name": "vlan~name.last", "title": "Assigned VLAN", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 15, "style": {"classes": ["font-bold"], "icon": {"name": "assign-vlan", "classes": []}}}, {"name": "interface~ip.address.last", "title": "Interface IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16, "style": {}}, {"name": "vlan~port.last", "title": "VLAN Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 17, "style": {}}, {"name": "interface~speed.bits.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 18, "style": {}}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 19, "style": {}}]}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001814, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": [], "drill.down.type": "yes"}, {"_type": "0", "id": 10000000002668, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "interface~uptime.percent.avg"}}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001815, "visualization.name": "Interface Traffic", "visualization.description": "Interface Traffic Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~in.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Interface Traffic", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~in.traffic.bits.per.sec.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~out.traffic.bits.per.sec.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~in.traffic.utilization.percent.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~out.traffic.utilization.percent.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~in.packets.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~out.packets.avg", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001818, "visualization.name": "Interface Error/Discard Packets", "visualization.description": "Interface Error/Discard Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedVerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~received.discard.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~sent.discard.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~received.error.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~sent.error.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "stack.groups": [{"data.points": ["interface~received.error.packets.avg", "interface~sent.error.packets.avg"]}, {"data.points": ["interface~received.discard.packets.avg", "interface~sent.discard.packets.avg"]}]}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001819, "visualization.name": "Interface In/Out Traffic", "visualization.description": "Interface In/Out Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001820, "visualization.name": "Interface Flap Availability", "visualization.category": "Chart", "visualization.type": "Status Flap", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "status.flap", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~duration", "aggregator": "", "entity.type": "Monitor"}, {"data.point": "interface~status.flap.history", "aggregator": "", "entity.type": "Monitor"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "asc"}}}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 10000000001821, "visualization.name": "Active Alerts", "visualization.description": "Active Alerts Interface", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy.stream", "category": "metric", "instance.type": "interface", "filters": {"data.filter": {}}, "data.points": [{"aggregator": "last", "data.point": "severity"}, {"aggregator": "last", "data.point": "policy.name"}, {"aggregator": "last", "data.point": "instance"}, {"aggregator": "last", "data.point": "policy.type"}, {"aggregator": "last", "data.point": "policy.id"}, {"aggregator": "last", "data.point": "metric"}, {"aggregator": "last", "data.point": "value"}, {"aggregator": "last", "data.point": "object.id"}, {"aggregator": "last", "data.point": "duration"}, {"data.point": "policy.first.trigger.tick", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Alerts", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "policy.type", "title": "Policy Type", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "object.id", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 3}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5}, {"name": "metric", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "value", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "policy.id", "title": "Policy Id", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "policy.first.trigger.tick", "title": "First Seen", "show": "yes", "cellRender": "ms_datetime", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}]}}, "container.type": "template", "drill.down.type": "yes"}]