/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.BackupProfileConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECTS;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(240 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestBackupProfile
{
    private static final Logger LOGGER = new Logger(TestBackupProfile.class, MOTADATA_API, "Test Backup Profile");

    private static MessageConsumer<JsonObject> messageConsumer = null;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllBackupProfiles(VertxTestContext testContext)
    {
        TestAPIUtil.get(TestAPIConstants.BACKUP_PROFILE_API_ENDPOINT, result ->
        {
            if (result.succeeded())
            {
                Assertions.assertNotNull(result.result());

                var item = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(item);

                Assertions.assertEquals(HttpStatus.SC_OK, result.result().statusCode());

                Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, item.getString(GlobalConstants.STATUS));

                Assertions.assertTrue(item.containsKey(RESULT));

                Assertions.assertEquals(2, item.getJsonArray(RESULT).size());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateReportDBProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running ... " + testInfo.getTestMethod().get().getName());

        var item = BackupProfileConfigStore.getStore().getItem(10000000000002L);

        TestAPIUtil.put(TestAPIConstants.BACKUP_PROFILE_API_ENDPOINT + "/" + 10000000000002L,
                new JsonObject("{\"backup.profile.context\" : { \"datastore.types\" : [ \"metric\", \"log\", \"config.history\", \"trap\", \"alert\" ] }}"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(BackupProfileConfigStore.getStore(), item, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Backup Profile"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testRunConfigDBBackupScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running ... " + testInfo.getTestMethod().get().getName());

        var id = 10000000000014L;

        var valid = new AtomicBoolean();

        TestUtil.vertx().eventBus().<JsonObject>localConsumer(EVENT_UI, message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_BACKUP_START))
                {
                    LOGGER.trace(eventContext.encode());

                    Assertions.assertTrue(eventContext.containsKey(EVENT_SCHEDULER));

                    Assertions.assertEquals(id, eventContext.getLong(EVENT_SCHEDULER));

                    Assertions.assertTrue(eventContext.containsKey(OBJECTS));

                    Assertions.assertFalse(eventContext.getJsonArray(OBJECTS).isEmpty());

                    Assertions.assertEquals(10000000000001L, eventContext.getJsonArray(OBJECTS).getLong(0));

                    valid.set(true);
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_BACKUP_STOP))
                {
                    LOGGER.trace(eventContext.encode());

                    Assertions.assertTrue(eventContext.containsKey(EVENT_SCHEDULER));

                    Assertions.assertEquals(id, eventContext.getLong(EVENT_SCHEDULER));

                    Assertions.assertTrue(eventContext.containsKey(OBJECTS));

                    Assertions.assertFalse(eventContext.getJsonArray(OBJECTS).isEmpty());

                    Assertions.assertEquals(10000000000001L, eventContext.getJsonArray(OBJECTS).getLong(0));

                    messageConsumer.unregister().onComplete(result ->
                    {
                        if (valid.get())
                        {
                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.warn("backup start event not received!");
                        }
                    });
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, id));
    }
}
