/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.health;

import com.mindarray.Bootstrap;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Agent;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.util.Calendar;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.HealthUtil.*;
import static com.mindarray.util.JVMStatUtil.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestHealthUtil
{
    private static final Logger LOGGER = new Logger(TestHealthUtil.class, MOTADATA_UTIL, "Test Health Util");

    public static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        if (Bootstrap.getDeployedVerticles().containsKey(HealthUtil.class.getSimpleName()) && TestUtil.vertx().deploymentIDs().contains(Bootstrap.getDeployedVerticles().get(HealthUtil.class.getSimpleName())))
        {
            TestUtil.vertx().undeploy(Bootstrap.getDeployedVerticles().get(HealthUtil.class.getSimpleName()), result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Health util Un-deployed successfully");

                    Bootstrap.getDeployedVerticles().remove(HealthUtil.class.getSimpleName());

                    TestUtil.vertx().deployVerticle(new HealthUtil()).onComplete(response ->
                    {
                        if (response.succeeded())
                        {
                            LOGGER.info(String.format("Health Util is redeployed manually... : %s ", response.result()));

                            Bootstrap.getDeployedVerticles().put(HealthUtil.class.getSimpleName(), response.result());

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.info("Failed to redeploy health util");

                            testContext.failNow(response.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.info("Failed to undeploy health util");

                    testContext.failNow(result.cause());
                }
            });
        }
        else
        {
            LOGGER.info("Health util is not deployed.");

            TestUtil.vertx().deployVerticle(new HealthUtil()).onComplete(response ->
            {
                if (response.succeeded())
                {
                    LOGGER.info("Health Util was not deployed so deployed it manually...");

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(response.cause());
                }
            });
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testInspectEventEngineHealthStatsCategoryCore(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS, new JsonObject().put(ENGINE_TYPE, EventBusConstants.EVENT_ENGINE_STATS));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_HEALTH_STATISTICS))
                {
                    Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    Assertions.assertEquals("core", eventContext.getString(ENGINE_CATEGORY));

                    var results = eventContext.getJsonArray(RESULT);

                    Assertions.assertFalse(results.isEmpty());

                    for (var index = 0; index < results.size(); index++)
                    {
                        var result = results.getJsonObject(index);

                        Assertions.assertNotNull(result);

                        Assertions.assertTrue(result.containsKey(HealthUtil.PENDING_EVENTS));
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        TestUtil.vertx().setTimer(3000, timer -> TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_HEALTH_STATISTICS, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(ENGINE_CATEGORY, "core")));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testInspectEventEngineHealthStatsCategoryDefault(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_HEALTH_STATISTICS))
                {
                    Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    Assertions.assertEquals("default", eventContext.getString(ENGINE_CATEGORY));

                    var results = eventContext.getJsonArray(RESULT);

                    Assertions.assertFalse(results.isEmpty());

                    for (var index = 0; index < results.size(); index++)
                    {
                        var result = results.getJsonObject(index);

                        Assertions.assertNotNull(result);

                        Assertions.assertTrue(result.containsKey(HealthUtil.PENDING_EVENTS));
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_HEALTH_STATISTICS, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(ENGINE_CATEGORY, "default"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testInspectDatastoreEngineHealthStats(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_HEALTH_STATISTICS))
                {
                    Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    Assertions.assertEquals("default", eventContext.getString(ENGINE_CATEGORY));

                    var results = eventContext.getJsonArray(RESULT);

                    Assertions.assertFalse(results.isEmpty());

                    for (var index = 0; index < results.size(); index++)
                    {
                        var result = results.getJsonObject(index);

                        Assertions.assertNotNull(result);

                        Assertions.assertTrue(result.containsKey(HealthUtil.PENDING_EVENTS));
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_HEALTH_STATISTICS, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(ENGINE_CATEGORY, "default").put(ENGINE_TYPE, "datastore"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^PRIMARY$")
    void testHAHeartbeatStatusQuery(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_OBSERVER_HEARTBEAT))
                {
                    Assertions.assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    Assertions.assertTrue(eventContext.containsKey(RESULT));

                    var results = eventContext.getJsonArray(RESULT);

                    for (var index = 0; index < results.size(); index++)
                    {
                        var result = results.getJsonObject(index);

                        Assertions.assertTrue(result.containsKey(STATUS));

                        Assertions.assertTrue(result.containsKey(DURATION));

                        Assertions.assertTrue(result.containsKey(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE));

                        Assertions.assertTrue(result.containsKey(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE));

                        Assertions.assertTrue(result.containsKey(DURATION));

                        Assertions.assertTrue(result.containsKey(AIOpsObject.OBJECT_IP));

                        Assertions.assertTrue(result.containsKey(AIOpsObject.OBJECT_HOST));
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_OBSERVER_HEARTBEAT, new JsonObject().put(AIOpsObject.OBJECT_IP, CommonUtil.getIPAddresses().getFirst()).put(AIOpsObject.OBJECT_HOST, CommonUtil.getHostName()).put(DURATION, new JsonObject().put(Bootstrap.getRegistrationId(), new JsonObject()
                .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(STATUS, NMSConstants.STATE_RUNNING).put(DURATION, DateTimeUtil.currentSeconds()))));

        TestUtil.vertx().eventBus().send(EVENT_OBSERVER_HEARTBEAT + ".status.query", new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_TYPE, EVENT_OBSERVER_HEARTBEAT).put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testInspectDatastoreHealthStats(VertxTestContext testContext)
    {
        var currentTimestamp = DateTimeUtil.currentSeconds();

        var calendar = Calendar.getInstance();

        var context = new JsonObject()
                .put(PLUGIN_ID, 500015)
                .put(EVENT_SOURCE, "************")
                .put(EVENT_TIMESTAMP, currentTimestamp)
                .put(HEALTH_STATS, new JsonObject()
                        .put("cache.entires", 0)
                        .put("cache.hits", 1)
                        .put("cache.misses", 0)
                        .put("cache.evictions", 10)
                        .put("cache.expires", 3)
                        .put("cache.lookups", 2));

        calendar.clear();

        calendar.setTimeInMillis(currentTimestamp * 1000);

        calendar.set(Calendar.MILLISECOND, 0);

        calendar.set(Calendar.SECOND, 0);

        var timestamp = new AtomicLong(calendar.getTimeInMillis());

        TestUtil.vertx().eventBus().send(EVENT_DATASTORE_STATS_RESPONSE, context);

        TestUtil.vertx().setTimer(10 * 1000, timer ->
        {
            try
            {
                var events = new File(CURRENT_DIR + PATH_SEPARATOR + EVENT_DIR + PATH_SEPARATOR);

                for (File file : Objects.requireNonNull(events.listFiles()))
                {
                    if (file.getName().startsWith("datastore-write-1"))
                    {
                        var directory = new File(file.getAbsolutePath());

                        for (File dirFile : Objects.requireNonNull(directory.listFiles()))
                        {
                            if (dirFile.getName().contains(CommonUtil.getString(timestamp.get()) + ".dat"))
                            {
                                TestUtil.vertx().cancelTimer(timer);

                                testContext.completeNow();

                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testNotifyDiskUtilizationExceededWarning(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"event.type\":\"agent.response.stream\",\"agent.id\":\"abc\",\"copy.required\":false,\"agent.type\":\"metric\",\"object.type\":\"Linux\",\"object.category\":\"Server\",\"object.groups\":[10000000000019,10000000000017],\"object.business.hour.profile\":10000000000001,\"object.ip\":\"************\",\"object.host\":\"yash-ThinkPad-T14-Gen-1\",\"object.name\":\"yash-ThinkPad-T14-Gen-1\",\"object.discovery.method\":\"AGENT\",\"object.target\":\"yash-ThinkPad-T14-Gen-1\",\"object.agent\":***********,\"object.creation.time\":\"2024/08/14 16:21:35\",\"object.creation.time.seconds\":**********,\"object.state\":\"ENABLE\",\"object.id\":1,\"_type\":\"1\",\"id\":***********,\"event.timestamp\":**********,\"result\":{\"system.disk.capacity.bytes\":257954852864,\"system.disk.used.bytes\":***********,\"system.disk.free.bytes\":175190618112,\"system.disk.used.percent\":82,\"system.disk.free.percent\":72.9,\"system.current.directory.used.percent\":82.9,\"system.root.directory.used.percent\":82.9},\"metric.plugin\":\"linux\",\"metric.name\":\"Linux\",\"metric.type\":\"Linux\",\"metric.polling.time\":300,\"metric.context\":{\"plugin.id\":88,\"plugin.engine\":\"go\",\"metric.polling.min.time\":300,\"timeout\":60},\"metric.state\":\"ENABLE\",\"metric.category\":\"Server\",\"metric.object\":***********}");

        context.put(Agent.AGENT_UUID, Bootstrap.getRegistrationId()).put(SESSION_ID, TestUtil.getSessionId());

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var event = message.body();

                if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION))
                    {
                        Assertions.assertNotNull(eventContext.getString(SEVERITY));

                        Assertions.assertEquals(Severity.WARNING.name(), eventContext.getString(SEVERITY));

                        Assertions.assertNotNull(eventContext.getString(MESSAGE));

                        Assertions.assertTrue(eventContext.getString(MESSAGE).contains("above the 80 threshold"));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception.getMessage());
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_AGENT_HEALTH_STATS, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testNotifyDiskUtilizationExceededMajor(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"event.type\":\"agent.response.stream\",\"agent.id\":\"abc\",\"copy.required\":false,\"agent.type\":\"metric\",\"object.type\":\"Linux\",\"object.category\":\"Server\",\"object.groups\":[10000000000019,10000000000017],\"object.business.hour.profile\":10000000000001,\"object.ip\":\"************\",\"object.host\":\"yash-ThinkPad-T14-Gen-1\",\"object.name\":\"yash-ThinkPad-T14-Gen-1\",\"object.discovery.method\":\"AGENT\",\"object.target\":\"yash-ThinkPad-T14-Gen-1\",\"object.agent\":***********,\"object.creation.time\":\"2024/08/14 16:21:35\",\"object.creation.time.seconds\":**********,\"object.state\":\"ENABLE\",\"object.id\":1,\"_type\":\"1\",\"id\":***********,\"event.timestamp\":**********,\"result\":{\"system.disk.capacity.bytes\":257954852864,\"system.disk.used.bytes\":***********,\"system.disk.free.bytes\":175190618112,\"system.disk.used.percent\":86,\"system.disk.free.percent\":72.9,\"system.current.directory.used.percent\":86.9,\"system.root.directory.used.percent\":86.9},\"metric.plugin\":\"linux\",\"metric.name\":\"Linux\",\"metric.type\":\"Linux\",\"metric.polling.time\":300,\"metric.context\":{\"plugin.id\":88,\"plugin.engine\":\"go\",\"metric.polling.min.time\":300,\"timeout\":60},\"metric.state\":\"ENABLE\",\"metric.category\":\"Server\",\"metric.object\":***********}");

        context.put(Agent.AGENT_UUID, Bootstrap.getRegistrationId()).put(SESSION_ID, TestUtil.getSessionId());

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var event = message.body();

                if (event.getBinary(EVENT_CONTEXT) != null && event.containsKey(EVENT_COMPRESSION_TYPE) && event.getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION))
                    {
                        Assertions.assertNotNull(eventContext.getString(SEVERITY));

                        Assertions.assertEquals(Severity.MAJOR.name(), eventContext.getString(SEVERITY));

                        Assertions.assertNotNull(eventContext.getString(MESSAGE));

                        Assertions.assertTrue(eventContext.getString(MESSAGE).contains("above the 85 threshold"));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception.getMessage());
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_AGENT_HEALTH_STATS, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testInspectJVMStats(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_JVM_STATS_RESPONSE, message ->
        {
            var event = message.body();

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": body: " + event.encode());

            var stats = event.getJsonObject(HEALTH_STATS);

            assertNotNull(stats);

            assertTrue(event.containsKey(EVENT_SOURCE));

            assertTrue(stats.containsKey(JVM_STATS) && !stats.getJsonObject(JVM_STATS).isEmpty());

            assertTrue(stats.containsKey(JVM_GC_STATS) && !stats.getJsonArray(JVM_GC_STATS).isEmpty());

            for (var i = 0; i < stats.getJsonArray(JVM_GC_STATS).size(); i++)
            {
                var stat = stats.getJsonArray(JVM_GC_STATS).getJsonObject(i);

                assertTrue(stat.containsKey(JVM_GC_NAME) && stat.getString(JVM_GC_NAME) != null);

                assertTrue(stat.containsKey(JVM_GC_COLLECTIONS) && stat.getValue(JVM_GC_COLLECTIONS) != null);

                assertTrue(stat.containsKey(JVM_GC_COLLECTION_TIME_MS) && stat.getValue(JVM_GC_COLLECTION_TIME_MS) != null);
            }

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_THREADS) && stats.getJsonObject(JVM_STATS).getValue(JVM_THREADS) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_PEAK_THREADS) && stats.getJsonObject(JVM_STATS).getValue(JVM_PEAK_THREADS) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_DAEMON_THREADS) && stats.getJsonObject(JVM_STATS).getValue(JVM_DAEMON_THREADS) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_HEAP_MEMORY_COMMITTED_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_HEAP_MEMORY_COMMITTED_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_HEAP_MEMORY_USED_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_HEAP_MEMORY_USED_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_HEAP_MEMORY_MAX_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_HEAP_MEMORY_MAX_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_HEAP_MEMORY_INIT_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_HEAP_MEMORY_INIT_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_NON_HEAP_MEMORY_COMMITTED_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_NON_HEAP_MEMORY_COMMITTED_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_NON_HEAP_MEMORY_USED_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_NON_HEAP_MEMORY_USED_BYTES) != null);

            assertTrue(stats.getJsonObject(JVM_STATS).containsKey(JVM_NON_HEAP_MEMORY_INIT_BYTES) && stats.getJsonObject(JVM_STATS).getValue(JVM_NON_HEAP_MEMORY_INIT_BYTES) != null);

            messageConsumer.unregister(result -> testContext.completeNow());
        });

        Assertions.assertTrue(testContext.awaitCompletion(120, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testRunDiagnostic(VertxTestContext testContext) throws InterruptedException
    {
        TestUtil.vertx().fileSystem().delete(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + DiagnosticUtil.MOTADATA_DIAGNOSTIC + Bootstrap.bootstrapType() + ".zip", handler ->
        {

            TestUtil.vertx().eventBus().send(EVENT_DIAGNOSTIC, new JsonObject().put(UI_EVENT_UUID, UUID.randomUUID().toString()).put(SESSION_ID, TestUtil.getSessionId()).put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name()).put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()));

            TestUtil.vertx().setPeriodic(5000, timer ->
            {

                if (TestUtil.vertx().fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + DiagnosticUtil.MOTADATA_DIAGNOSTIC + Bootstrap.bootstrapType() + ".zip"))
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
            });
        });

        Assertions.assertTrue(testContext.awaitCompletion(90, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }
}
