{"172.16.8.196": {"result": {"rabbitmq.exchange": [{"rabbitmq.exchange": "AMQP default", "rabbitmq.exchange.type": "direct", "rabbitmq.exchange.out.publishes.per.sec": 0.0, "rabbitmq.exchange.in.publishes.per.sec": 0.0}, {"rabbitmq.exchange": "amq.direct", "rabbitmq.exchange.type": "direct"}, {"rabbitmq.exchange": "amq.fanout", "rabbitmq.exchange.type": "fanout"}, {"rabbitmq.exchange": "amq.headers", "rabbitmq.exchange.type": "headers"}, {"rabbitmq.exchange": "amq.match", "rabbitmq.exchange.type": "headers"}, {"rabbitmq.exchange": "amq.rabbitmq.trace", "rabbitmq.exchange.type": "topic"}, {"rabbitmq.exchange": "amq.topic", "rabbitmq.exchange.type": "topic"}]}}, "172.16.9.145": {"result": {"rabbitmq.exchange": [{"rabbitmq.exchange": "AMQP default", "rabbitmq.exchange.type": "direct", "rabbitmq.exchange.out.publishes.per.sec": 0.0, "rabbitmq.exchange.in.publishes.per.sec": 0.0}, {"rabbitmq.exchange": "amq.direct", "rabbitmq.exchange.type": "direct"}, {"rabbitmq.exchange": "amq.fanout", "rabbitmq.exchange.type": "fanout"}, {"rabbitmq.exchange": "amq.headers", "rabbitmq.exchange.type": "headers"}, {"rabbitmq.exchange": "amq.match", "rabbitmq.exchange.type": "headers"}, {"rabbitmq.exchange": "amq.rabbitmq.trace", "rabbitmq.exchange.type": "topic"}, {"rabbitmq.exchange": "amq.topic", "rabbitmq.exchange.type": "topic"}]}}}