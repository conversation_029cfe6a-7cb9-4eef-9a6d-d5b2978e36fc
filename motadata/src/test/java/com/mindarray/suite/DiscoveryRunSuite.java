/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.suite;

import com.mindarray.TestInit;
import com.mindarray.discovery.*;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

@Suite
@SelectClasses({TestInit.class, TestOtherObjectDiscovery.class, TestServiceCheckDiscovery.class, TestWirelessObjectDiscovery.class, TestServerObjectDiscovery.class, TestNetworkObjectDiscovery.class, TestVirtualizationObjectDiscovery.class, TestHCIObjectDiscovery.class, TestSDNObjectDiscovery.class, TestStorageObjectDiscovery.class, TestContainerOrchestrationObjectDiscovery.class, TestCloudObjectDiscovery.class, TestCSVRangeObjectDiscovery.class})
public class DiscoveryRunSuite
{

}