[{"_type": "0", "id": 10000000002313, "visualization.name": "Uptime", "visualization.description": "Uptime Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Uptime", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.access.point~started.time.seconds.last"}]}, "style": {"icon": {"name": "stopwatch"}, "color.data.point": "aruba.wireless.access.point~started.time.seconds"}}}}, {"_type": "0", "id": 10000000002312, "visualization.name": "Clients", "visualization.description": "Clients Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.access.point~clients"}]}], "visualization.properties": {"gauge": {"header": {"title": "Clients", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.access.point~clients.last"}]}, "style": {"icon": {"name": "users"}, "color.data.point": "aruba.wireless.access.point~clients"}}}}, {"_type": "0", "id": 10000000002330, "visualization.name": "Radios", "visualization.description": "Radios Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~slots", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.access.point~slots"}]}], "visualization.properties": {"gauge": {"header": {"title": "Radios", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.access.point~slots.last"}]}, "style": {"icon": {"name": "radios"}, "color.data.point": "aruba.wireless.access.point~slots"}}}}, {"_type": "0", "id": 10000000002310, "visualization.name": "Sent Traffic", "visualization.description": "Traffic Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "instance.type.filter": {"aruba.wireless.access.point": "aruba.wireless.client~ap"}, "visualization.result.by": ["monitor"], "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.client~traffic.sent.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Sent Traffic", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.client~traffic.sent.bytes.per.sec.avg"}]}, "style": {"icon": {"name": "long-arrow-right"}, "color.data.point": "aruba.wireless.client~traffic.sent.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000002311, "visualization.name": "Received Traffic", "visualization.description": "Received Traffic Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "instance.type.filter": {"aruba.wireless.access.point": "aruba.wireless.client~ap"}, "visualization.result.by": ["monitor"], "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~traffic.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.client~traffic.received.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Received Traffic", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.client~traffic.received.bytes.per.sec.avg"}]}, "style": {"icon": {"name": "long-arrow-left"}, "color.data.point": "aruba.wireless.client~traffic.received.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000002309, "visualization.name": "Warm Reboots", "visualization.description": "Warm Reboots Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~warm.reboots", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.access.point~warm.reboots"}]}], "visualization.properties": {"gauge": {"header": {"title": "Warm Reboots", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.access.point~warm.reboots.last"}]}, "style": {"icon": {"name": "reboot"}, "color.data.point": "aruba.wireless.access.point~warm.reboots"}}}}, {"_type": "0", "id": 10000000002314, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002315, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aruba.wireless.access.point~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002318, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "instance.type.filter": {"aruba.wireless.access.point": "aruba.wireless.client~ap"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "aruba.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "aruba.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002317, "visualization.name": "Client Details Aruba Wireless Access Point", "visualization.description": "Client Details Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "instance.type.filter": {"aruba.wireless.access.point": "aruba.wireless.client~ap"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~ap.mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "aruba.wireless.client~status.last", "width.percent": 10, "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 4, "formula": {"operation": "combine", "columns": ["aruba.wireless.client~traffic.sent.bytes.per.sec.last", "aruba.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"width.percent": 20, "classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.client~traffic.sent.bytes.per.sec.last", "title": "Sent Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.client~traffic.received.bytes.per.sec.last", "title": "Received Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.client~ap.ip.address.last", "title": "IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 8, "style": {"classes": ["font-bold"], "icon": {"name": "network", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "aruba.wireless.client~ap.mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.client~started.time.last", "title": "Connected Since", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"]}}]}}}]