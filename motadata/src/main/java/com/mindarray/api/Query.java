/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.API_FIELD_REQUIRED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.QUERY_RULES;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.eventbus.EventBusConstants.EVENT_VISUALIZATION_QUERY;
import static com.mindarray.visualization.VisualizationConstants.VISUALIZATION_CATEGORY;
import static com.mindarray.visualization.VisualizationConstants.VisualizationCategory;

public class Query extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(Query.class, GlobalConstants.MOTADATA_API, "Query API");

    public Query()
    {
        super("query", null, LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            router.get("/visualization/:id").handler(this::executeVisualizationQuery);

            router.post("/metric/histogram").handler(this::executeHistogramQuery);

            router.post("/metric/aggregations").handler(this::executeAggregationQuery);

            router.get("/objects").handler(this::getObjects);

            router.get("/objects/:id/status").handler(this::getObjectStatus);

            router.get("/objects/:id/poll-info").handler(this::getPollInfo);

            router.get("/objects/:id/instances").handler(this::getInstances);

            router.get("/objects/:id/group").handler(this::getObjectsByGroup);

            router.get("/objects/status").handler(this::getObjectsByStatus);

            router.get("/objects/severity").handler(this::getObjectsBySeverity);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void getObjects(RoutingContext routingContext)
    {
        try
        {
            var user = UserConfigStore.getStore().getItem(routingContext.user().principal().getLong(ID));

            if (user.getLong(ID).equals(DEFAULT_ID))
            {
                user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));
            }

            if (routingContext.request().params().contains(FILTER))
            {
                var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (requestParameters != null)
                {
                    if (requestParameters.containsKey(KEY) && requestParameters.containsKey(VALUE))
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                .put(GlobalConstants.RESULT, ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS), requestParameters.getString(KEY), requestParameters.getJsonArray(VALUE)))));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                            .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS)))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getObjectsByGroup(RoutingContext routingContext)
    {
        try
        {
            var items = ObjectConfigStore.getStore().getItemsByGroup(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var objects = new JsonArray();

            for (var index = 0; index < items.size(); index++)
            {
                var id = CommonUtil.getLong(items.getValue(index));

                objects.add(new JsonObject().put(ID, id).put(AIOpsObject.OBJECT_NAME, ObjectConfigStore.getStore().getObjectName(id)));
            }

            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, objects));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getObjectsBySeverity(RoutingContext routingContext)
    {
        if (!routingContext.queryParams().isEmpty() && routingContext.queryParams().contains(SEVERITY))
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, MetricPolicyCacheStore.getStore().getItemsBySeverity(routingContext.queryParams().get(SEVERITY))));
        }
        else
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, SEVERITY)));
        }
    }

    private void getObjectsByStatus(RoutingContext routingContext)
    {
        if (!routingContext.queryParams().isEmpty() && routingContext.queryParams().contains(STATUS))
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, ObjectStatusCacheStore.getStore().getItemsByStatus(routingContext.queryParams().get(STATUS))));
        }
        else
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(API_FIELD_REQUIRED, STATUS)));
        }
    }

    private void getInstances(RoutingContext routingContext)
    {
        try
        {

            var entities = new JsonObject();

            var items = MetricConfigStore.getStore().getItemsByObject(CommonUtil.getLong(routingContext.request().getParam(ID)));

            for (var item : items)
            {
                if (item.containsKey(Metric.METRIC_CONTEXT))
                {
                    var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null && !objects.isEmpty())
                    {
                        var instanceName = NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.get(item.getString(Metric.METRIC_PLUGIN));

                        if (CommonUtil.isNotNullOrEmpty(instanceName))
                        {
                            entities.put(instanceName, objects);
                        }
                    }
                }
            }

            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, entities));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getPollInfo(RoutingContext routingContext)
    {
        var pollInfo = MetricCacheStore.getStore().getMetricPollTimestamps(CommonUtil.getLong(routingContext.request().getParam(ID)));

        pollInfo.remove(EVENT_TIMESTAMP);

        this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, pollInfo));
    }

    private void getObjectStatus(RoutingContext routingContext)
    {
        var status = ObjectStatusCacheStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

        if (status != null)
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, new JsonObject().put(STATUS, status)));
        }
        else
        {
            this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, CommonUtil.getLong(routingContext.request().getParam(ID)))));
        }
    }

    // this API can be use , by just passing widgetId
    private void executeVisualizationQuery(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            if (id > DUMMY_ID)
            {
                var item = WidgetConfigStore.getStore().getItem(id);

                if (item != null && !item.isEmpty())
                {
                    var category = item.getString(VisualizationConstants.VISUALIZATION_CATEGORY);

                    // only support grid/stream/active-alert
                    if (category.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HISTOGRAM.getName()) || category.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.STREAM.getName()) || category.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()) || category.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.ACTIVE_ALERT.getName()))
                    {
                        var queryParam = routingContext.queryParam(VisualizationConstants.DISCARD_DUMMY_ROWS);                                //will be ignoring dummy rows in case where no data or dummy data provided by DB

                        var discardDummyRow = queryParam == null || queryParam.isEmpty() || queryParam.getFirst().equalsIgnoreCase(YES);

                        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_QUERY, new JsonObject().put(ID, item.getLong(ID)).put(VisualizationConstants.DISCARD_DUMMY_ROWS, discardDummyRow).put(User.USER_NAME, routingContext.user().principal().getString(User.USER_NAME)), reply ->
                        {
                            try
                            {
                                if (reply.succeeded())
                                {
                                    if (reply.result() != null && !reply.result().body().isEmpty())
                                    {
                                        var result = reply.result().body();

                                        if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.getJsonObject(RESULT).getJsonArray(RESULT)));
                                        }
                                        else
                                        {
                                            if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.INTERNAL_ERROR))
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                            }
                                            else if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.TIMED_OUT))
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_REQUEST_TIMEOUT).put(GlobalConstants.STATUS, STATUS_TIME_OUT).put(ERRORS, result.getJsonArray(ERRORS)));
                                            }
                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_NOT_FOUND).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                                    }
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                APIUtil.sendResponse(exception, routingContext);
                            }
                        });
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, String.format(ErrorMessageConstants.API_INVALID_VALUE_RULE, VisualizationConstants.VISUALIZATION_CATEGORY)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_INVALID_VALUE_RULE, VisualizationConstants.VISUALIZATION_CATEGORY)))));
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, ErrorMessageConstants.ITEM_NOT_FOUND).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, ErrorMessageConstants.ITEM_NOT_FOUND))));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, String.format(API_FIELD_REQUIRED, ID)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(API_FIELD_REQUIRED, ID)))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    // this API can be use , by passing api payload.
    private void executeHistogramQuery(RoutingContext routingContext)
    {
        var message = APIUtil.testQueryRequest(routingContext.body().asJsonObject().put(VISUALIZATION_CATEGORY, VisualizationCategory.HISTOGRAM.getName()), QUERY_RULES.get(VisualizationCategory.HISTOGRAM.getName()).keySet());

        if (message.equalsIgnoreCase(EMPTY_VALUE))
        {
            var queryParams = routingContext.queryParam(VisualizationConstants.DISCARD_DUMMY_ROWS);                                //will be ignoring dummy rows in case where no data or dummy data provided by DB

            var discardDummyRow = queryParams == null || queryParams.isEmpty() || queryParams.getFirst().equalsIgnoreCase(YES);

            Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_QUERY, new JsonObject().mergeIn(routingContext.body().asJsonObject()).put(VisualizationConstants.DISCARD_DUMMY_ROWS, discardDummyRow).put(VISUALIZATION_CATEGORY, VisualizationCategory.HISTOGRAM.getName()).put(User.USER_NAME, routingContext.user().principal().getString(User.USER_NAME)), reply ->
            {
                try
                {
                    if (reply.succeeded())
                    {
                        if (reply.result() != null && !reply.result().body().isEmpty())
                        {
                            var result = reply.result().body();

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.getJsonObject(RESULT).getJsonArray(RESULT)));
                            }
                            else
                            {
                                if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.INTERNAL_ERROR))
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                                else if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.TIMED_OUT))
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_REQUEST_TIMEOUT).put(GlobalConstants.STATUS, STATUS_TIME_OUT).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                            }
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_NOT_FOUND).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                        }
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    APIUtil.sendResponse(exception, routingContext);
                }
            });
        }
        else
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, message));
        }
    }

    private void executeAggregationQuery(RoutingContext routingContext)
    {
        var message = APIUtil.testQueryRequest(routingContext.body().asJsonObject().put(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), QUERY_RULES.get(VisualizationCategory.GRID.getName()).keySet());

        if (message.equalsIgnoreCase(EMPTY_VALUE))
        {
            var queryParams = routingContext.queryParam(VisualizationConstants.DISCARD_DUMMY_ROWS);                                //will be ignoring dummy rows in case where no data or dummy data provided by DB

            var discardDummyRow = queryParams == null || queryParams.isEmpty() || queryParams.getFirst().equalsIgnoreCase(YES);

            Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_VISUALIZATION_QUERY, new JsonObject().mergeIn(routingContext.body().asJsonObject()).put(VisualizationConstants.DISCARD_DUMMY_ROWS, discardDummyRow).put(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()).put(User.USER_NAME, routingContext.user().principal().getString(User.USER_NAME)), reply ->
            {
                try
                {
                    if (reply.succeeded())
                    {
                        if (reply.result() != null && !reply.result().body().isEmpty())
                        {
                            var result = reply.result().body();

                            if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result.getJsonObject(RESULT).getJsonArray(RESULT)));
                            }
                            else
                            {
                                if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.INTERNAL_ERROR))
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                                else if (result.containsKey(MESSAGE) && result.getString(MESSAGE).equalsIgnoreCase(ErrorMessageConstants.TIMED_OUT))
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_REQUEST_TIMEOUT).put(GlobalConstants.STATUS, STATUS_TIME_OUT).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, result.getJsonArray(ERRORS)));
                                }
                            }
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_NOT_FOUND).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                        }
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, UNKNOWN).put(ERROR_CODE, UNKNOWN).put(MESSAGE, UNKNOWN))));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    APIUtil.sendResponse(exception, routingContext);
                }
            });
        }
        else
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, message));
        }
    }
}
