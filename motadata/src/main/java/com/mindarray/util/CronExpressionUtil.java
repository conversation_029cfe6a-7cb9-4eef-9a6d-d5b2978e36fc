/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.GlobalConstants;
import io.vertx.core.json.JsonObject;

import static com.mindarray.api.Scheduler.*;

public class CronExpressionUtil
{
    public static final String CRON_SEPARATOR = "@@";
    public static final String CRON_DAILY = "Daily";
    public static final String CRON_WEEKLY = "Weekly";
    public static final String CRON_MONTHLY = "Monthly";
    public static final String CRON_ONCE = "Once";
    private static final Logger LOGGER = new Logger(CronExpressionUtil.class, GlobalConstants.MOTADATA_UTIL, "Cron Expression");

    private CronExpressionUtil()
    {
    }

    public static String getCronExpression(JsonObject schedulerContext)
    {
        String minutes;

        String hours;

        String seconds;

        var cronExpression = GlobalConstants.EMPTY_VALUE;

        String[] tokens;

        try
        {
            var builder = new StringBuilder();

            for (var triggerTime : schedulerContext.getJsonArray(SCHEDULER_TIMES))
            {
                tokens = triggerTime.toString().split(":");

                hours = tokens[0];

                minutes = tokens[1].split(" ")[0];

                seconds = "0";

                if (tokens.length == 3)
                {
                    seconds = tokens[2].split(" ")[0];
                }

                switch (schedulerContext.getString(SCHEDULER_TIMELINE))
                {
                    case CRON_DAILY ->
                    {
                        cronExpression = "0 " + minutes + " " + hours + " ? * *";

                        builder.append(cronExpression).append(CRON_SEPARATOR);
                    }

                    case CRON_WEEKLY ->
                    {
                        for (var weekDay : schedulerContext.getJsonArray(SCHEDULER_DAYS))
                        {
                            cronExpression = "0 " + minutes + " " + hours + " ? * " + weekDay.toString();

                            builder.append(cronExpression).append(CRON_SEPARATOR);
                        }
                    }

                    case CRON_MONTHLY ->
                    {
                        for (var dayValue : schedulerContext.getJsonArray(SCHEDULER_DATES))
                        {
                            for (var monthValue : schedulerContext.getJsonArray(SCHEDULER_MONTHS))
                            {
                                cronExpression = "0 " + minutes + " " + hours + " " + dayValue.toString() + " " + monthValue.toString() + " ? *";

                                builder.append(cronExpression).append(CRON_SEPARATOR);
                            }
                        }
                    }

                    case CRON_ONCE ->
                    {
                        tokens = schedulerContext.getString(SCHEDULER_START_DATE).split("-");

                        var day = tokens[0];

                        var month = tokens[1];

                        var year = tokens[2];

                        if (day.charAt(0) == '0')
                        {
                            day = day.substring(1);
                        }

                        if (month.charAt(0) == '0')
                        {
                            month = month.substring(1);
                        }

                        cronExpression = seconds + " " + minutes + " " + hours + " " + day + " " + month + " ? " + year;

                        builder.append(cronExpression).append(CRON_SEPARATOR);
                    }

                    default ->
                    {
                    }
                }
            }

            if (!builder.isEmpty())
            {
                builder.delete(builder.length() - CRON_SEPARATOR.length(), builder.length());
            }

            cronExpression = builder.toString();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return cronExpression;
    }

}
