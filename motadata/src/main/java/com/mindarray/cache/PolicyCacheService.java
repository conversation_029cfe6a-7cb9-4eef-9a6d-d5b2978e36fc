/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;

/**
 * The PolicyCacheService interface provides methods for retrieving cached data related to
 * policies, including active policies, policy groups, and policy heatmaps.
 * <p>
 * This service is implemented as a Vert.x service proxy, allowing for asynchronous communication
 * across the event bus. It is registered by the {@link CacheServiceProvider} verticle.
 * <p>
 * The service improves performance by retrieving pre-computed or previously stored policy data
 * instead of performing expensive operations each time the data is requested.
 *
 * @see PolicyCacheServiceImpl
 * @see CacheServiceProvider
 */
@ProxyGen
public interface PolicyCacheService
{
    /**
     * Creates a new instance of the PolicyCacheService.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    @GenIgnore
    static void create(Vertx vertx, Handler<AsyncResult
            <PolicyCacheService>> handler)
    {
        new PolicyCacheServiceImpl(vertx, handler);
    }

    /**
     * Creates a proxy to the PolicyCacheService.
     *
     * @param vertx   The Vert.x instance
     * @param address The event bus address where the service is registered
     * @return A proxy to the PolicyCacheService
     */
    @GenIgnore
    static PolicyCacheService createProxy(Vertx vertx, String address)
    {
        return new PolicyCacheServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getCacheServiceTimeoutMillis()));
    }

    /**
     * Retrieves active policies information.
     * <p>
     * This method returns cached data about policies that are currently active,
     * including their status and configuration.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    PolicyCacheService getActivePolicies(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves policies grouped by their group.
     * <p>
     * This method returns cached policy data organized by policy groups,
     * allowing for easier analysis of related policies.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    PolicyCacheService getPoliciesByGroup(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves policy heatmap data.
     * <p>
     * This method returns cached policy heatmap data, which shows policy
     * activity patterns over time.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    PolicyCacheService getPolicyHeatmap(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

    /**
     * Retrieves the last triggered ticks for policies.
     * <p>
     * This method returns cached data about when policies were last triggered,
     * providing information about recent policy activity.
     *
     * @param context The context containing the parameters for the query
     * @param handler The handler to be called with the result
     * @return This instance for fluent API usage
     */
    @Fluent
    PolicyCacheService getPolicyLastTriggeredTicks(JsonObject context, Handler<AsyncResult<JsonObject>> handler);

}
