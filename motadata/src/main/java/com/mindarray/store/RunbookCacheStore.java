/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Cache store for tracking runbook execution counts.
 * <p>
 * This class maintains a cache of runbook execution counts, tracking how many
 * pending executions exist for each runbook. It is used by the RunbookEngine
 * to determine when all executions of a runbook have completed.
 * <p>
 * The store is implemented as a singleton to ensure a single source of truth
 * for runbook execution counts across the system.
 */
public class RunbookCacheStore extends AbstractCacheStore
{
    private static final RunbookCacheStore STORE = new RunbookCacheStore();
    private final Map<Long, Integer> items = new ConcurrentHashMap<>();

    private RunbookCacheStore()
    {
    }

    /**
     * Gets the singleton instance of the RunbookCacheStore.
     *
     * @return The singleton instance of the RunbookCacheStore
     */
    public static RunbookCacheStore getStore()
    {
        return STORE;
    }

    /**
     * Updates the count of pending executions for a runbook.
     *
     * @param id    The ID of the runbook
     * @param count The new count of pending executions
     */
    public void updateItem(long id, Integer count)
    {
        this.items.put(id, count);
    }

    /**
     * Gets the count of pending executions for a runbook.
     *
     * @param id The ID of the runbook
     * @return The count of pending executions, or NOT_AVAILABLE if the runbook is not in the cache
     */
    public Integer getItem(long id)
    {
        return this.items.getOrDefault(id, GlobalConstants.NOT_AVAILABLE);
    }

    /**
     * Marks a runbook as complete by removing it from the cache.
     * <p>
     * This method is called when all executions of a runbook have completed,
     * indicating that the runbook is no longer running.
     *
     * @param id The ID of the runbook to mark as complete
     */
    public void complete(long id)
    {
        this.items.remove(id);
    }
}
