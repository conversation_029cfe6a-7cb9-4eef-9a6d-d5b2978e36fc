{"fd00:1:1:1::132": {"errors": [], "object.ip": "fd00:1:1:1::132", "password": "Mind@123", "port": 5985, "result": {"windows.scheduler.failed.tasks": 5, "windows.scheduler.running.tasks": 2, "windows.scheduler.task": [{"windows.scheduler.task": "GoogleUpdateTaskMachineCore", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 2:41:42 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 2:41:41 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineUA", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 9:41:42 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 10:41:41 AM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "OneDrive Reporting Task-S-1-5-21-118591132-946707192-1347333022-1001", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 7:06:45 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 7:06:45 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "OneDrive Reporting Task-S-1-5-21-118591132-946707192-1347333022-1027", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 6:14:43 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 6:14:43 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "OneDrive Standalone Update Task-S-1-5-21-118591132-946707192-1347333022-1001", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 7:48:12 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 7:38:00 PM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147160572, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FFB11FC"}, {"windows.scheduler.task": "OneDrive Standalone Update Task-S-1-5-21-118591132-946707192-1347333022-1027", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 5:15:08 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 8:28:16 PM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147160572, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FFB11FC"}, {"windows.scheduler.task": "OneDrive Standalone Update Task-S-1-5-21-118591132-946707192-1347333022-1028", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "7/26/2021 6:05:13 PM", "windows.scheduler.task.missed.runs": 317, "windows.scheduler.task.next.runtime": "6/9/2022 8:05:59 PM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147160572, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FFB11FC"}, {"windows.scheduler.task": "oracle-lock-1", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 9:46:06 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 10:46:05 AM", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "oracle-lock-2", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 9:46:52 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 10:46:50 AM", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "orcl1", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 10:04:58 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 11:04:57 AM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147020576, "windows.scheduler.task.state": "Running", "windows.scheduler.task.state.code": 4, "windows.scheduler.task.status.code": "0x-7FF8EF20"}, {"windows.scheduler.task": "orcl2", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 10:05:40 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 11:05:38 AM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147020576, "windows.scheduler.task.state": "Running", "windows.scheduler.task.state.code": 4, "windows.scheduler.task.status.code": "0x-7FF8EF20"}], "windows.scheduler.tasks": 11}, "timeout": 60, "username": "admin"}, "***********": {"errors": [], "object.ip": "***********", "password": "Mind#@123", "port": 5985, "result": {"windows.scheduler.failed.tasks": 1, "windows.scheduler.running.tasks": 0, "windows.scheduler.task": [{"windows.scheduler.task": "GoogleUpdateTaskUserS-1-5-21-2755421794-3397887287-2739095413-500Core", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/7/2022 11:01:10 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/8/2022 11:01:10 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskUserS-1-5-21-2755421794-3397887287-2739095413-500UA", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 9:01:10 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/8/2022 10:01:10 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-2755421794-3397887287-2739095413-500", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "7/17/2021 4:23:27 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "12/30/1899 12:00:00 AM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Test Task_26072020", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 12:05:01 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 12:05:00 PM", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": 259, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x103"}, {"windows.scheduler.task": "update-S-1-5-21-2755421794-3397887287-2739095413-500", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 6:08:00 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/8/2022 10:08:00 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "update-sys", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 7:47:00 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/8/2022 11:47:00 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}], "windows.scheduler.tasks": 6}, "timeout": 60, "username": "Administrator"}, "***********": {"errors": [], "object.ip": "***********", "password": "Mind@123", "port": 5985, "result": {"windows.scheduler.failed.tasks": 1, "windows.scheduler.running.tasks": 0, "windows.scheduler.task": [{"windows.scheduler.task": "cleanup", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/8/2022 11:01:13 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/12/2022 1:10:01 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "CreateExplorerShellUnelevatedTask", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "5/13/2022 12:53:07 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "12/30/1899 12:00:00 AM", "windows.scheduler.task.result": "Task is terminated", "windows.scheduler.task.result.code": 267014, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x41306"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineCore", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 11:58:07 AM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/10/2022 11:58:06 AM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineUA", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "6/9/2022 2:58:07 PM", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "6/9/2022 3:58:06 PM", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}], "windows.scheduler.tasks": 4}, "timeout": 60, "username": "Administrator"}, "************": {"errors": [], "object.ip": "************", "password": "mind@123", "port": 5985, "result": {"windows.scheduler.failed.tasks": 11, "windows.scheduler.running.tasks": 2, "windows.scheduler.task": [{"windows.scheduler.task": "abcd", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "14-06-2022 17:00:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 17:00:00", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": 259, "windows.scheduler.task.state": "Running", "windows.scheduler.task.state.code": 4, "windows.scheduler.task.status.code": "0x103"}, {"windows.scheduler.task": "Adobe Acrobat Update Task", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 14:42:43", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 18:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Adobe Flash Player Updater", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 15:12:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:12:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Chaitas Test", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "08-07-2016 11:10:00", "windows.scheduler.task.missed.runs": 52036, "windows.scheduler.task.next.runtime": "15-06-2022 16:10:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "export task detail", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "07-07-2016 17:10:01", "windows.scheduler.task.missed.runs": 2168, "windows.scheduler.task.next.runtime": "15-06-2022 16:55:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineCore", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 12:59:29", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "16-06-2022 12:59:29", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineUA", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 15:59:29", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:59:29", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "max-lock-1", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 16:07:16", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:12:16", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "max-lock-2", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 16:07:42", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:12:42", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Motadata Universal Host Agent", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "12-03-2018 17:55:36", "windows.scheduler.task.missed.runs": 1555, "windows.scheduler.task.next.runtime": "15-06-2022 17:55:36", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "MSCERT", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 11:30:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "16-06-2022 11:30:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "New Test Scenario", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "08-07-2016 11:38:30", "windows.scheduler.task.missed.runs": 2168, "windows.scheduler.task.next.runtime": "15-06-2022 16:55:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-2853100195-2127850869-4235494741-1292", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-2853100195-2127850869-4235494741-1295", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "29-12-2021 16:10:16", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-2853100195-2127850869-4235494741-4298", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "10-03-2021 15:36:29", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-2853100195-2127850869-4235494741-4299", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Optimize Start <PERSON><PERSON>-S-1-5-21-2853100195-2127850869-4235494741-500", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "04-03-2022 20:46:58", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "priyank test123", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "07-07-2016 10:57:15", "windows.scheduler.task.missed.runs": 2169, "windows.scheduler.task.next.runtime": "16-06-2022 10:50:03", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147024894, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x-7FF8FFFE"}, {"windows.scheduler.task": "priyank-test", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 15:55:56", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:55:56", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147024894, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FF8FFFE"}, {"windows.scheduler.task": "Service Restart Checking", "windows.scheduler.task.enabled": "False", "windows.scheduler.task.last.runtime": "12-03-2018 18:02:21", "windows.scheduler.task.missed.runs": 288, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "File not found", "windows.scheduler.task.result.code": 2, "windows.scheduler.task.state": "Disabled", "windows.scheduler.task.state.code": 1, "windows.scheduler.task.status.code": "0x2"}, {"windows.scheduler.task": "syntax", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "12-07-2016 13:22:49", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147020576, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FF8EF20"}, {"windows.scheduler.task": "test 1207", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "12-07-2016 13:20:15", "windows.scheduler.task.missed.runs": 1, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "test new 1207", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "12-07-2016 13:10:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Test-Backup", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "06-07-2016 18:00:41", "windows.scheduler.task.missed.runs": 1, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147024894, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FF8FFFE"}, {"windows.scheduler.task": "Test_Task_1", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 12:33:02", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "16-06-2022 12:33:02", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147020576, "windows.scheduler.task.state": "Running", "windows.scheduler.task.state.code": 4, "windows.scheduler.task.status.code": "0x-7FF8EF20"}, {"windows.scheduler.task": "tetstfail", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "07-07-2016 11:34:25", "windows.scheduler.task.missed.runs": 1, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "", "windows.scheduler.task.result.code": -2147024894, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x-7FF8FFFE"}], "windows.scheduler.tasks": 26}, "timeout": 60, "username": "readonly"}, "************": {"errors": [], "object.ip": "************", "password": "Mind@123", "port": 5985, "result": {"windows.scheduler.failed.tasks": 2, "windows.scheduler.running.tasks": 0, "windows.scheduler.task": [{"windows.scheduler.task": "GoogleUpdateTaskMachineCore", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 12:26:51", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "16-06-2022 12:26:51", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "GoogleUpdateTaskMachineUA", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "15-06-2022 15:26:51", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "15-06-2022 16:26:51", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-1746860950-3987134673-4265843255-1121", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-1746860950-3987134673-4265843255-1122", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Unknown function called", "windows.scheduler.task.result.code": 1, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x1"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-1746860950-3987134673-4265843255-1124", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "01-06-2022 13:03:41", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-1746860950-3987134673-4265843255-500", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "01-06-2022 13:03:41", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}, {"windows.scheduler.task": "Optimize <PERSON> <PERSON><PERSON>-S-1-5-21-3271448756-3489217763-4068289136-500", "windows.scheduler.task.enabled": "True", "windows.scheduler.task.last.runtime": "01-06-2022 13:03:41", "windows.scheduler.task.missed.runs": 0, "windows.scheduler.task.next.runtime": "30-12-1899 00:00:00", "windows.scheduler.task.result": "Completed Successfully", "windows.scheduler.task.result.code": 0, "windows.scheduler.task.state": "Ready", "windows.scheduler.task.state.code": 3, "windows.scheduler.task.status.code": "0x0"}], "windows.scheduler.tasks": 7}, "timeout": 60, "username": "Administrator"}}