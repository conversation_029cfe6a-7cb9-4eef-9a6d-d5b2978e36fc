/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.SystemServiceConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class SystemService extends AbstractAPI
{
    public static final String SYSTEM_SERVICE_OS = "system.service.os";

    public static final String SYSTEM_SERVICE_APP_TYPE = "system.service.app.type";

    public static final String SYSTEM_SERVICE = "system.service";

    public SystemService()
    {
        super("system-services", SystemServiceConfigStore.getStore(), new Logger(SystemService.class, MOTADATA_API, "System Service API"));
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var item = SystemServiceConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

        if (!routingContext.body().asJsonObject().containsKey(SYSTEM_SERVICE_APP_TYPE))
        {
            item.put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(SYSTEM_SERVICE_APP_TYPE));
        }
        item.mergeIn(routingContext.body().asJsonObject());

        promise.complete(item);

        return promise.future();
    }
}
