/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.store.FlowGeolocationMapperConfigStore;
import com.mindarray.util.Logger;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class FlowGeolocationMapper extends AbstractAPI
{
    public static final String FLOW_GEOLOCATION_MAPPER_PROFILE_NAME = "flow.geolocation.mapper.profile.name";

    public static final String FLOW_GEOLOCATION_MAPPER_COUNTRY = "flow.geolocation.mapper.country";

    public static final String FLOW_GEOLOCATION_MAPPER_DESCRIPTION = "flow.geolocation.mapper.description";

    public static final String FLOW_GEOLOCATION_MAPPER_CITY = "flow.geolocation.mapper.city";

    public static final String FLOW_GEOLOCATION_MAPPER_GROUP = "flow.geolocation.mapper.group";

    private static final Logger LOGGER = new Logger(FlowGeolocationMapper.class, MOTADATA_API, "Flow Geolocation Mapper API");

    public FlowGeolocationMapper()
    {
        super("flow-geolocation-mappers", FlowGeolocationMapperConfigStore.getStore(), LOGGER);
    }
}
