/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.GlobalConstants;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.serviceproxy.ServiceBinder;

import java.util.ArrayList;

/**
 * The CacheServiceProvider is a Vert.x verticle responsible for starting and registering
 * all cache services in the Motadata platform.
 * <p>
 * This verticle creates instances of the following services and registers them with the Vert.x event bus:
 * <ul>
 *   <li>{@link CacheService} - Registered at "cache.service"</li>
 *   <li>{@link AvailabilityCacheService} - Registered at "availability.cache.service"</li>
 *   <li>{@link ConfigCacheService} - Registered at "config.cache.service"</li>
 *   <li>{@link PolicyCacheService} - Registered at "policy.cache.service"</li>
 * </ul>
 * <p>
 * Each service is created and registered asynchronously, and the verticle's start method
 * completes only when all services have been successfully started.
 *
 * @see CacheService
 * @see AvailabilityCacheService
 * @see ConfigCacheService
 * @see PolicyCacheService
 */
public class CacheServiceProvider extends AbstractVerticle
{
    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(CacheServiceProvider.class, GlobalConstants.MOTADATA_CACHE, "Cache Service Provider");

    /**
     * Starts the CacheServiceProvider verticle by creating and registering all cache services.
     * <p>
     * This method creates instances of all cache services and registers them with the Vert.x event bus.
     * It completes the promise only when all services have been successfully started.
     *
     * @param promise The promise to complete when the verticle has been started
     * @throws Exception If an error occurs during startup
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Create a list to hold futures for each service startup
        var futures = new ArrayList<Future<Void>>();

        // Start each cache service and add its future to the list
        futures.add(startCacheService());
        futures.add(startAvailabilityCacheService());
        futures.add(startConfigCacheService());
        futures.add(startPolicyCacheService());

        // Join all futures and complete the promise when all services have started
        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause());
            }
        });
    }

    /**
     * Starts the CacheService and registers it with the Vert.x event bus.
     * <p>
     * This method creates an instance of the CacheService and registers it at the address
     * "cache.service" using the ServiceBinder.
     *
     * @return A future that will be completed when the service has been started
     */
    private Future<Void> startCacheService()
    {
        var promise = Promise.<Void>promise();

        // Create the CacheService instance
        CacheService.create(vertx, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // Create a ServiceBinder and register the service
                    var binder = new ServiceBinder(vertx);
                    binder.setAddress("cache.service").registerLocal(CacheService.class, result.result());

                    // Complete the promise
                    promise.complete();
                }
                catch (Exception exception)
                {
                    // Log the error and fail the promise
                    LOGGER.error(exception);
                    LOGGER.fatal("failed to start cache service...");
                    promise.fail(exception.getCause());
                }
            }
            else
            {
                // Log the error and fail the promise
                LOGGER.error(result.cause());
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Starts the AvailabilityCacheService and registers it with the Vert.x event bus.
     * <p>
     * This method creates an instance of the AvailabilityCacheService and registers it at the address
     * "availability.cache.service" using the ServiceBinder.
     *
     * @return A future that will be completed when the service has been started
     */
    private Future<Void> startAvailabilityCacheService()
    {
        var promise = Promise.<Void>promise();

        // Create the AvailabilityCacheService instance
        AvailabilityCacheService.create(vertx, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // Create a ServiceBinder and register the service
                    var binder = new ServiceBinder(vertx);
                    binder.setAddress("availability.cache.service").registerLocal(AvailabilityCacheService.class, result.result());

                    // Complete the promise
                    promise.complete();
                }
                catch (Exception exception)
                {
                    // Log the error and fail the promise
                    LOGGER.error(exception);
                    LOGGER.fatal("failed to start availability cache service...");
                    promise.fail(exception.getCause());
                }
            }
            else
            {
                // Log the error and fail the promise
                LOGGER.error(result.cause());
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Starts the ConfigCacheService and registers it with the Vert.x event bus.
     * <p>
     * This method creates an instance of the ConfigCacheService and registers it at the address
     * "config.cache.service" using the ServiceBinder.
     *
     * @return A future that will be completed when the service has been started
     */
    private Future<Void> startConfigCacheService()
    {
        var promise = Promise.<Void>promise();

        // Create the ConfigCacheService instance
        ConfigCacheService.create(vertx, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // Create a ServiceBinder and register the service
                    var binder = new ServiceBinder(vertx);
                    binder.setAddress("config.cache.service").registerLocal(ConfigCacheService.class, result.result());

                    // Complete the promise
                    promise.complete();
                }
                catch (Exception exception)
                {
                    // Log the error and fail the promise
                    LOGGER.error(exception);
                    LOGGER.fatal("failed to start config cache service...");
                    promise.fail(exception.getCause());
                }
            }
            else
            {
                // Log the error and fail the promise
                LOGGER.error(result.cause());
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Starts the PolicyCacheService and registers it with the Vert.x event bus.
     * <p>
     * This method creates an instance of the PolicyCacheService and registers it at the address
     * "policy.cache.service" using the ServiceBinder.
     *
     * @return A future that will be completed when the service has been started
     */
    private Future<Void> startPolicyCacheService()
    {
        var promise = Promise.<Void>promise();

        // Create the PolicyCacheService instance
        PolicyCacheService.create(vertx, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // Create a ServiceBinder and register the service
                    var binder = new ServiceBinder(vertx);
                    binder.setAddress("policy.cache.service").registerLocal(PolicyCacheService.class, result.result());

                    // Complete the promise
                    promise.complete();
                }
                catch (Exception exception)
                {
                    // Log the error and fail the promise
                    LOGGER.error(exception);
                    LOGGER.fatal("failed to start policy cache service...");
                    promise.fail(exception.getCause());
                }
            }
            else
            {
                // Log the error and fail the promise
                LOGGER.error(result.cause());
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }
}
