/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*Change Logs
 *   Date           Author              Notes
 *   28-Feb-2025    Umang Sharma        CompliancePolicyCacheStore Items for the Scheduler statuses
 *   30-Jul-2025    Vismit		        MOTADATA-6312 Refactored regarding modifications to the AIOps scheduling workflow
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.HashSet;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.Entity;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;

public class CompliancePolicy extends AbstractAPI
{
    public static final String COMPLIANCE_POLICY_NAME = "compliance.policy.name";
    public static final String COMPLIANCE_POLICY_JOB = "compliance.policy.job";
    public static final String COMPLIANCE_POLICY_DESCRIPTION = "compliance.policy.description";
    public static final String COMPLIANCE_POLICY_TAGS = "compliance.policy.tags";
    public static final String COMPLIANCE_POLICY_CREATION_TIME = "compliance.policy.creation.time";
    public static final String COMPLIANCE_POLICY_BENCHMARK = "compliance.policy.benchmark";
    public static final String COMPLIANCE_POLICY_CONTEXT = "compliance.policy.context";
    public static final String COMPLIANCE_POLICY_CONFIG_FILE_TYPE = "compliance.policy.config.file.type";
    public static final String COMPLIANCE_POLICY_CATEGORY = "compliance.policy.category";
    public static final String COMPLIANCE_POLICY_SCHEDULER = "compliance.policy.scheduler";
    public static final String COMPLIANCE_POLICY_ENTITIES = "compliance.policy.entities";
    private static final Logger LOGGER = new Logger(CompliancePolicy.class, MOTADATA_API, "Compliance Policy API");

    protected CompliancePolicy()
    {
        super("compliance-policies", CompliancePolicyConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var context = CompliancePolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT);

            JsonArray items;

            if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
            {
                items = ObjectConfigStore.getStore().getItems(context.getJsonArray(ENTITIES));

                if (!items.isEmpty())
                {
                    response.put(Entity.OBJECT.getName(), items);
                }

            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
            {
                items = GroupConfigStore.getStore().getItems(context.getJsonArray(ENTITIES));

                if (!items.isEmpty())
                {
                    response.put(Entity.GROUP.getName(), items);
                }
            }
            else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
            {
                items = new JsonArray();

                var entities = TagConfigStore.getStore().getIdsByItems(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                var counts = new HashSet<Long>();

                var objects = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS, new JsonArray(entities));

                for (var index = 0; index < objects.size(); index++)
                {
                    var object = objects.getJsonObject(index);

                    if (counts.add(object.getLong(ID)))
                    {
                        items.add(object);
                    }
                }

                if (!items.isEmpty())
                {
                    response.put(Entity.OBJECT.getName(), items);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var ids = CompliancePolicyConfigStore.getStore().getIds();

            for (var index = 0; index < ids.size(); index++)
            {
                var context = CompliancePolicyConfigStore.getStore().getItem(ids.getLong(index)).getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT);

                if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.OBJECT.getName()))
                {
                    response.put(CommonUtil.getString(ids.getLong(index)), context.getJsonArray(ENTITIES).size());
                }
                else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.GROUP.getName()))
                {
                    response.put(CommonUtil.getString(ids.getLong(index)), GroupConfigStore.getStore().getItems(context.getJsonArray(ENTITIES)).size());
                }
                else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                {
                    var entities = TagConfigStore.getStore().getIdsByItems(context.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                    var counts = new HashSet<>();

                    var objects = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS, new JsonArray(entities));

                    for (var i = 0; i < objects.size(); i++)
                    {
                        counts.add(objects.getJsonObject(i).getInteger(AIOpsObject.OBJECT_ID));
                    }

                    response.put(CommonUtil.getString(ids.getLong(index)), counts.size());
                }
            }

            promise.complete(response);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_COMPLIANCE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = CompliancePolicyConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT,
                                item.put(NMSConstants.STATE, CompliancePolicyCacheStore.getStore().getItem(id) != NOT_AVAILABLE ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING)
                                        .put(COMPLIANCE_POLICY_SCHEDULER, SchedulerConfigStore.getStore().containsObject(Scheduler.SCHEDULER_CONTEXT, COMPLIANCE_POLICY_ENTITIES, id, JobScheduler.JobType.COMPLIANCE_POLICY.getName()) ? YES : NO)
                                        .put(COMPLIANCE_POLICY_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_POLICY_TAGS)))));

            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = CompliancePolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                var entities = item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).containsKey(ENTITIES) ? item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES) : new JsonArray(new ArrayList<>(1));

                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                var update = false;

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (!entities.contains(id))
                    {
                        entities.add(id);

                        update = true;
                    }
                }

                if (update)
                {
                    updateItem(item, entities, routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, Entity.COMPLIANCE_POLICY.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, Entity.COMPLIANCE_POLICY.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = CompliancePolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                var entities = item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getJsonArray(ENTITIES);

                var update = false;

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (entities.contains(id))
                    {
                        update = true;

                        entities.remove(id);
                    }
                }

                if (update)
                {
                    updateItem(item, entities, routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, Entity.COMPLIANCE_POLICY.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private Future<Void> updateItem(JsonObject item, JsonArray entities, RoutingContext routingContext)
    {
        var promise = Promise.<Void>promise();

        item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).put(ENTITIES, entities);

        Bootstrap.configDBService().update(DBConstants.TBL_COMPLIANCE_POLICY,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT, item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT)),
                routingContext.user().principal().getString(USER_NAME) != null ? routingContext.user().principal().getString(USER_NAME) : DEFAULT_USER,
                routingContext.request().remoteAddress().host() != null ? routingContext.request().remoteAddress().host() : SYSTEM_REMOTE_ADDRESS,
                result -> CompliancePolicyConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                {
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                            .put(GlobalConstants.ID, item.getLong(GlobalConstants.ID))
                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_COMPLIANCE_POLICY.name()));

                    promise.complete();
                }));

        return promise.future();
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_COMPLIANCE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        var schedulers = SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, COMPLIANCE_POLICY_ENTITIES, CompliancePolicyConfigStore.getStore().getIds(), JobScheduler.JobType.COMPLIANCE_POLICY.getName());

        var items = new JsonArray();

        for (var index = 0; index < entities.size(); index++)
        {
            var item = entities.getJsonObject(index);

            item.put(COMPLIANCE_POLICY_SCHEDULER, schedulers.contains(item.getLong(GlobalConstants.ID)) ? GlobalConstants.YES : GlobalConstants.NO).
                    put(NMSConstants.STATE, CompliancePolicyCacheStore.getStore().getItem(item.getLong(ID)) != NOT_AVAILABLE ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

            item.put(COMPLIANCE_POLICY_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_POLICY_TAGS)));

            items.add(item);
        }

        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_COMPLIANCE_POLICY.name()));

        JobScheduler.deleteSchedulers(entity, JobScheduler.JobType.COMPLIANCE_POLICY.getName());

        CompliancePolicyCacheStore.getStore().deleteItem(entity.getLong(ID));

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        qualify(routingContext, APIConstants.REQUEST_CREATE);

        var requestBody = routingContext.body().asJsonObject();

        requestBody.put(COMPLIANCE_POLICY_CREATION_TIME, DateTimeUtil.currentMilliSeconds());

        return Future.succeededFuture(requestBody);
    }

    private Future<JsonObject> qualify(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        var requestBody = routingContext.body().asJsonObject();

        if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE))
        {
            requestBody.put(CompliancePolicy.COMPLIANCE_POLICY_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_TAGS), Tag.TagType.COMPLIANCE.getName(), DBConstants.ENTITY_TYPE_USER));

            promise.complete(requestBody);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_UPDATE);
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            item = new JsonObject().mergeIn(item);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(COMPLIANCE_POLICY_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_POLICY_TAGS)))));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }
}
