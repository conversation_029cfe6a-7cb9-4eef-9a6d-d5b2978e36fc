/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  25-Mar-2025		Chopra Deven 		MOTADATA-5299: Modified forecast policy inspection by updating request and response handling
 *
 */

package com.mindarray.policy;

/**
 * AIOpsMetricPolicyManager is responsible for managing AI operations metric policies.
 * <p>
 * This class:
 * 1. Loads and manages AI operations metric policies from the configuration store
 * 2. Assigns policies to objects, groups, and tags
 * 3. Schedules and executes policy evaluations at appropriate intervals
 * 4. Handles anomaly detection, baseline comparison, and forecasting policies
 * 5. Coordinates with visualization components to retrieve metric data
 * 6. Forwards policy evaluation results to the AIOpsMetricPolicyInspector
 * <p>
 * The manager runs as a Vert.x verticle and communicates with other components
 * through the event bus to coordinate policy evaluation.
 */

import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;
import static com.mindarray.policy.PolicyEngineConstants.*;

public class AIOpsMetricPolicyManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(AIOpsMetricPolicyManager.class, GlobalConstants.MOTADATA_POLICY, "AIOps Metric Policy Manager");
    private static final int ANOMALY_TRIGGER_INTERVAL_SECONDS = MotadataConfigUtil.getAnomalyPolicyTriggerTimerSeconds();
    private static final int BASELINE_TRIGGER_INTERVAL_SECONDS = MotadataConfigUtil.getBaselinePolicyTriggerTimerSeconds();
    private static final Map<String, String> FORECAST_TIMELINES = Map.ofEntries(
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_12_HOURS.getName(), "15 m"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_24_HOURS.getName(), "30 m"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_48_HOURS.getName(), "1 h"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_7_DAYS.getName(), "3 h"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_14_DAYS.getName(), "6 h"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_30_DAYS.getName(), "12 h"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_60_DAYS.getName(), "1 d"),
            Map.entry(VisualizationConstants.VisualizationTimeline.LAST_90_DAYS.getName(), "1 d"));
    private final Map<Long, JsonObject> policies = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByObject = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByGroup = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByTag = new HashMap<>();
    private final Map<Integer, Set<Integer>> pluginsByObject = new HashMap<>();
    private final Map<Integer, Map<String, Set<String>>> instancesByObject = new HashMap<>();
    private final List<Long> runningQueries = new ArrayList<>();
    private final List<Long> queuedQueries = new ArrayList<>();
    private final Map<Long, JsonObject> queryContexts = new HashMap<>();
    private final Map<Long, Set<Long>> suppressedPolicies = new HashMap<>();
    private final JsonObject columns = new JsonObject();
    private EventEngine eventEngine;

    /**
     * Initializes the AIOpsMetricPolicyManager verticle.
     * <p>
     * This method:
     * 1. Loads AI operations metric policies from the configuration store
     * 2. Sets up event bus handlers for policy management and queries
     * 3. Initializes periodic timers for anomaly detection and baseline comparison
     * 4. Registers with the event engine for event processing
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

            var items = MetricPolicyConfigStore.getStore().getItems();

            try
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if (AIOPS_POLICY_TYPES.contains(policy.getString(POLICY_TYPE)) && (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)))
                    {
                        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                        if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                        {
                            policy.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                        }

                        policies.put(policy.getLong(ID), policy);
                    }
                }

                assign();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_COLUMN_MAPPER_UPDATE, message -> updateColumnMapper(message.body()));

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().<JsonObject>localConsumer(EVENT_AIOPS_METRIC_POLICY_MANAGER_TEST, message -> message.reply(new JsonObject().put("plugin.objects", JsonObject.mapFrom(pluginsByObject)).put("instance.objects", JsonObject.mapFrom(instancesByObject))));
        }

        vertx.setPeriodic(MotadataConfigUtil.getAIOpsPolicyInspectionTimerSeconds() * 1000L, timer ->
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("AIOps policy inspection plugins qualified by object::" + pluginsByObject);
            }

            try
            {
                var plugins = pluginsByObject.entrySet().iterator();

                var qualifiedPolicies = new HashMap<String, List<Long>>();

                while (plugins.hasNext())
                {
                    var entry = plugins.next();

                    var entityId = ObjectConfigStore.getStore().getIdByObjectId(entry.getKey());

                    if (entityId > 0)
                    {
                        var object = ObjectConfigStore.getStore().getItem(entityId);

                        var tags = object.getJsonArray(AIOpsObject.OBJECT_TAGS, new JsonArray()).addAll(TagCacheStore.getStore().getInstanceTagsByObjectId(object.getInteger(AIOpsObject.OBJECT_ID)));

                        for (var plugin : entry.getValue())
                        {
                            qualify(object.getJsonArray(AIOpsObject.OBJECT_GROUPS), tags, entityId, CommonUtil.getInteger(plugin)).forEach((metricKey, value) ->
                            {
                                if (!value.isEmpty())
                                {
                                    var metric = metricKey.split(SEPARATOR_WITH_ESCAPE)[1];

                                    var policy = policies.get(value.getFirst());

                                    if (policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && (!suppressedPolicies.containsKey(entityId) || (suppressedPolicies.containsKey(entityId) && !suppressedPolicies.get(entityId).contains(value.getFirst()))))
                                    {
                                        qualifiedPolicies.computeIfAbsent(policy.getLong(ID) + SEPARATOR + metric + SEPARATOR + plugin, val -> new ArrayList<>()).add(entityId);
                                    }
                                }
                            });
                        }
                    }

                    plugins.remove();
                }

                if (!qualifiedPolicies.isEmpty())
                {
                    executeQuery(qualifiedPolicies);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("AIOps Policies qualified for inspection:" + qualifiedPolicies);
                    }
                }

                else
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("No policies qualified for inspection");
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_AIOPS_METRIC_POLICY_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("AIOps Metric Policy Response Received::" + event);
                }

                if (message.body() != null)
                {
                    if (event.getInteger(VisualizationConstants.QUERY_PROGRESS) >= 100)//means query process complete remove it from queue
                    {
                        runningQueries.remove(event.getLong(REQUEST_ID));

                        if (!queuedQueries.isEmpty())
                        {
                            send();
                        }
                    }

                    var policy = policies.get(event.getLong(POLICY_ID));

                    var result = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(RESULT)), LOGGER, false, event, !policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()), true);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("AIOps Metric Policy Response Received::" + result);
                    }

                    if (result.containsKey(RESULT) && !result.getJsonArray(RESULT).isEmpty())
                    {
                        var instance = result.getString(INSTANCE);

                        var rows = result.getJsonArray(RESULT);

                        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()))
                        {
                            var response = new HashMap<String, JsonArray>();

                            // predictionIndex represent forecasted data and (predictionIndex - 2) represent recently saved data in db.
                            var predictionIndex = result.getInteger("prediction.index", 0) > 0 ? result.getInteger("prediction.index") - 2 : 0;

                            /*
                             *   As object ID is required (routing based on object.id) to send event for the inspection,
                             *   first we need to extract object.id from the result and then send event for each object.id
                             */

                            // prepare result for each object/instance
                            for (var index = predictionIndex; index < rows.size(); index++)
                            {
                                var row = rows.getJsonObject(index);

                                row.getMap().forEach((key, value) ->
                                {
                                    if (!key.equalsIgnoreCase(TIME_STAMP))
                                    {
                                        var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE);

                                        if (tokens.length == 3)
                                        {
                                            response.computeIfAbsent(tokens[0], val -> new JsonArray()).add(new JsonObject().put(TIME_STAMP, row.getLong(VisualizationConstants.TIMESTAMP)).put(tokens[1], value));
                                        }
                                    }
                                });
                            }

                            // response contains forecasted data for each object.id and instance, in case of instance we need to split the key to get object.id and instance and send event for each instance
                            if (!response.isEmpty())
                            {
                                for (var entry : response.entrySet())
                                {
                                    vertx.eventBus().send(EVENT_AIOPS_METRIC_POLICY,
                                            new JsonObject().put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                                    .put(METRIC, event.getString(METRIC))
                                                    .put(PLUGIN_ID, CommonUtil.getInteger(event.getValue(PLUGIN_ID)))
                                                    .put(RESULT, entry.getValue())
                                                    .put(AIOpsObject.OBJECT_ID, entry.getKey().contains(GlobalConstants.GROUP_SEPARATOR) ? CommonUtil.getInteger(entry.getKey().split(GlobalConstants.GROUP_SEPARATOR)[0]) : CommonUtil.getInteger(entry.getKey()))
                                                    .put(INSTANCE, entry.getKey().contains(GlobalConstants.GROUP_SEPARATOR) ? entry.getKey().split(GlobalConstants.GROUP_SEPARATOR)[1] : EMPTY_VALUE)
                                                    .put(POLICY_ID, event.getLong(POLICY_ID)));

                                }
                            }

                        }
                        else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
                        {
                            for (var i = 0; i < rows.size(); i++)
                            {
                                var row = rows.getJsonObject(i);

                                row.put(AIOpsObject.OBJECT_ID, CommonUtil.getInteger(row.remove("monitor"))).put(INSTANCE, !instance.isEmpty() ? CommonUtil.getString(row.getValue(instance)) : EMPTY_VALUE);

                                vertx.eventBus().send(EVENT_AIOPS_METRIC_POLICY, row.put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(METRIC, event.getString(METRIC)).put(PLUGIN_ID, CommonUtil.getInteger(event.getValue(PLUGIN_ID))).put(POLICY_ID, event.getLong(POLICY_ID)));
                            }
                        }
                        else
                        {

                            var objectId = NOT_AVAILABLE;

                            var instanceValue = EMPTY_VALUE;

                            var row = rows.getJsonObject(0);

                            for (var key : row.getMap().keySet())
                            {
                                if (!key.equalsIgnoreCase(TIME_STAMP))
                                {
                                    if (!instance.isEmpty())
                                    {
                                        var tokens = key.split(CARET_SEPARATOR_WITH_ESCAPE)[0];

                                        objectId = CommonUtil.getInteger(tokens.split("###")[0]);

                                        instanceValue = tokens.split("###")[1].trim();
                                    }

                                    else
                                    {
                                        objectId = CommonUtil.getInteger(key.split(CARET_SEPARATOR_WITH_ESCAPE)[0]);
                                    }

                                    break;
                                }
                            }

                            vertx.eventBus().send(EVENT_AIOPS_METRIC_POLICY, new JsonObject().put(INSTANCE, instanceValue).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(RESULT, rows).put(METRIC, event.getString(METRIC)).put(PLUGIN_ID, CommonUtil.getInteger(event.getValue(PLUGIN_ID))).put(POLICY_ID, event.getLong(POLICY_ID)).put(AIOpsObject.OBJECT_ID, objectId));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DATASTORE_FLUSH, message ->
        {
            var context = message.body().getJsonObject(EVENT_CONTEXT);

            var pluginId = CommonUtil.getInteger(context.getString("plugin").split(DASH_SEPARATOR)[0]);

            if (pluginId < 10000)//for metric plugin only
            {
                context.getJsonArray(NMSConstants.OBJECT).forEach(objectId ->
                {
                    var object = ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(objectId));

                    if (object != null)
                    {
                        var tags = TagCacheStore.getStore().getInstanceTagsByObjectId(CommonUtil.getInteger(objectId)).addAll(object.getJsonArray(AIOpsObject.OBJECT_TAGS));

                        if (policiesByObject.containsKey(object.getLong(ID)) || object.getJsonArray(AIOpsObject.OBJECT_GROUPS).stream().map(CommonUtil::getLong).anyMatch(policiesByGroup::containsKey) || tags.stream().map(CommonUtil::getLong).anyMatch(policiesByTag::containsKey))
                        {
                            pluginsByObject.computeIfAbsent(object.getInteger(AIOpsObject.OBJECT_ID), value -> new HashSet<>()).add(pluginId);
                        }
                    }
                });
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_METRIC_POLICY_SUPPRESS, message ->
        {
            var event = message.body();

            message.reply(message.body() != null && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)) && suppressedPolicies.get(event.getLong(ENTITY_ID)).contains(event.getLong(POLICY_ID)));
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
            {
                case ADD_POLICY, UPDATE_POLICY ->
                {
                    var policy = MetricPolicyConfigStore.getStore().getItem(event.getLong(ID));

                    if (policy != null && AIOPS_POLICY_TYPES.contains(policy.getString(POLICY_TYPE)))
                    {
                        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                        if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                        {
                            policy.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                        }

                        policies.put(event.getLong(ID), policy);

                        assign();
                    }
                }

                case DELETE_POLICY ->
                {
                    policies.remove(event.getLong(ID));

                    policiesByObject.values().forEach(items ->
                    {
                        for (var objectPolicies : items.values())
                        {
                            objectPolicies.remove(event.getLong(ID));
                        }
                    });

                    policiesByGroup.values().forEach(items ->
                    {
                        for (var groupPolicies : items.values())
                        {
                            groupPolicies.remove(event.getLong(ID));
                        }
                    });

                    policiesByTag.values().forEach(items ->
                    {
                        for (var tagPolicies : items.values())
                        {
                            tagPolicies.remove(event.getLong(ID));
                        }
                    });

                    MetricPolicyCacheStore.getStore().cleanup(event.getLong(ID));

                    sort();
                }

                case DELETE_OBJECT ->
                {

                    if (!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                    {
                        policiesByObject.remove(event.getLong(ID));

                        MetricPolicyCacheStore.getStore().deleteItem(event.getLong(ID));
                    }
                }

                case ADD_OBJECT -> assign();

                case SUPPRESS_POLICY ->
                {
                    if (message.body() != null && event.containsKey(ENTITY_ID) && policies.containsKey(event.getLong(POLICY_ID)))
                    {
                        var objectId = event.getLong(ENTITY_ID);

                        if (!suppressedPolicies.containsKey(objectId))
                        {
                            suppressedPolicies.put(objectId, new HashSet<>());
                        }

                        suppressedPolicies.get(objectId).add(event.getLong(POLICY_ID));
                    }

                }

                case UNSUPPRESS_POLICY ->
                {
                    if (message.body() != null && event.containsKey(ENTITY_ID) && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)))
                    {
                        var objectId = event.getLong(ENTITY_ID);

                        suppressedPolicies.get(objectId).remove(event.getLong(POLICY_ID));

                        if (suppressedPolicies.get(objectId).isEmpty())
                        {
                            suppressedPolicies.remove(objectId);
                        }
                    }
                }

                default ->
                {
                }

            }

        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(false).setLogger(LOGGER).setEventHandler(this::updateContext).start(vertx, promise);

    }

    /**
     * Updates the context for policy evaluation with the latest event data.
     * This method processes incoming events and prepares them for policy evaluation.
     *
     * @param event The event containing updated context information
     */
    private void updateContext(JsonObject event)
    {
        try
        {
            if (event.getJsonObject(RESULT) != null && !event.getJsonObject(RESULT).isEmpty())
            {
                var result = event.getJsonObject(RESULT);

                var context = new JsonObject().put(Metric.METRIC_TYPE, event.getString(Metric.METRIC_TYPE)).put(EVENT_TIMESTAMP, event.containsKey(EventBusConstants.EVENT_TIMESTAMP) ? event.getLong(EventBusConstants.EVENT_TIMESTAMP) : DateTimeUtil.currentSeconds()).put(AIOpsObject.OBJECT_CATEGORY, event.containsKey(Metric.METRIC_CATEGORY) ? event.getString(Metric.METRIC_CATEGORY) : event.getString(AIOpsObject.OBJECT_CATEGORY)).put(GlobalConstants.PLUGIN_ID, event.getInteger(GlobalConstants.PLUGIN_ID)).put(ENTITY_ID, event.getLong(Metric.METRIC_OBJECT));

                var item = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT));

                if (item != null)
                {
                    PolicyEngineConstants.qualify(event.getJsonArray(AIOpsObject.OBJECT_GROUPS, new JsonArray()), event.getJsonArray(TAGS), event.getLong(Metric.METRIC_OBJECT), policies, policiesByObject, policiesByGroup, policiesByTag).forEach((key, value) ->
                    {
                        if (!value.isEmpty())
                        {
                            var metric = key.split(SEPARATOR_WITH_ESCAPE)[1];

                            var policyId = value.stream().findFirst().get();

                            var policy = policies.get(policyId);

                            if (policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && (!suppressedPolicies.containsKey(event.getLong(Metric.METRIC_OBJECT)) || (suppressedPolicies.containsKey(event.getLong(Metric.METRIC_OBJECT)) && !suppressedPolicies.get(event.getLong(Metric.METRIC_OBJECT)).contains(policyId))))
                            {
                                var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                                context.put(AIOpsObject.OBJECT_ID, item.getInteger(AIOpsObject.OBJECT_ID)).put(GlobalConstants.METRIC, metric).put(ID, policyId).put(POLICY_TYPE, policy.getString(POLICY_TYPE));

                                //for instance level alerts we will be keeping track using instance.type key
                                if (metric.contains(INSTANCE_SEPARATOR) || CommonUtil.isNotNullOrEmpty(policyContext.getString(PolicyEngineConstants.INSTANCE_TYPE)))
                                {
                                    var instance = metric.contains(INSTANCE_SEPARATOR) ? metric.split(INSTANCE_SEPARATOR)[0] : policyContext.getString(PolicyEngineConstants.INSTANCE_TYPE);

                                    //if result contains that particular instance result then only check condition and save instance
                                    if (result.containsKey(instance) && result.getJsonArray(instance) != null && !result.getJsonArray(instance).isEmpty())
                                    {
                                        context.put(INSTANCE, instance);

                                        inspectInstanceMetric(policyContext, result, context.put(POLICY_SEVERITY, policyContext.getJsonObject(POLICY_SEVERITY)));
                                    }
                                }
                            }
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Inspects metrics at the instance level against policy conditions.
     * This method evaluates instance-level metrics against policy thresholds
     * and prepares the context for policy evaluation.
     *
     * @param policyContext The policy configuration context
     * @param result        The metric result to inspect
     * @param context       The context for policy evaluation
     */
    private void inspectInstanceMetric(JsonObject policyContext, JsonObject result, JsonObject context)
    {
        try
        {
            var entries = result.getJsonArray(context.getString(INSTANCE));

            var filter = policyContext.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null);

            if (entries != null && !entries.isEmpty())
            {
                var policyByTag = policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG);

                var entities = policyByTag ? policyContext.getJsonArray(ENTITIES) : null;

                if (filter != null && !filter.isEmpty())
                {
                    var conditionGroup = filter.getJsonArray(CONDITION_GROUPS).getJsonObject(0);

                    var operator = conditionGroup.getString(OPERATOR);

                    var conditions = conditionGroup.getJsonArray(CONDITIONS);

                    for (var i = 0; i < entries.size(); i++)
                    {
                        var satisfied = false;

                        var entry = entries.getJsonObject(i);

                        for (var j = 0; j < conditions.size(); j++)
                        {
                            var condition = conditions.getJsonObject(j);

                            var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ? condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] : condition.getString(OPERAND);

                            if (entry.containsKey(operand))
                            {
                                satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), entry.getValue(operand));

                                if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                                {
                                    break;
                                }
                            }
                        }

                        if (satisfied && entry.containsKey(context.getString(GlobalConstants.METRIC)))
                        {
                            var tags = TagCacheStore.getStore().getTags(context.getInteger(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + context.getString(INSTANCE) + KEY_SEPARATOR + (context.getString(INSTANCE).equalsIgnoreCase(NMSConstants.INTERFACE) ? entry.getString("interface~index") : entry.getString(context.getString(INSTANCE))));

                            if (!policyByTag || (tags != null && entities != null && entities.stream().map(CommonUtil::getLong).anyMatch(tags::contains)))
                            {
                                instancesByObject.computeIfAbsent(context.getInteger(AIOpsObject.OBJECT_ID), value -> new HashMap<>()).computeIfAbsent(context.getString(GlobalConstants.METRIC), value -> new HashSet<>()).add(CommonUtil.getString(entry.getValue(context.getString(INSTANCE))) + SEPARATOR + context.getInteger(PLUGIN_ID));
                            }
                        }
                    }
                }
                else
                {

                    for (var i = 0; i < entries.size(); i++)
                    {
                        var entry = entries.getJsonObject(i);

                        var tags = TagCacheStore.getStore().getTags(context.getInteger(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + context.getString(INSTANCE) + KEY_SEPARATOR + (context.getString(INSTANCE).equalsIgnoreCase(NMSConstants.INTERFACE) ? entry.getString("interface~index") : entry.getString(context.getString(INSTANCE))));

                        if (entry.containsKey(context.getString(GlobalConstants.METRIC)) && (!policyByTag || (tags != null && entities != null && entities.stream().map(CommonUtil::getLong).anyMatch(tags::contains))))
                        {
                            instancesByObject.computeIfAbsent(context.getInteger(AIOpsObject.OBJECT_ID), value -> new HashMap<>()).computeIfAbsent(context.getString(GlobalConstants.METRIC), value -> new HashSet<>()).add(CommonUtil.getString(entry.getValue(context.getString(INSTANCE))) + SEPARATOR + context.getInteger(PLUGIN_ID));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    //qualifying policies on groups and monitor id and combining into one single map for evaluation..

    /**
     * Finds applicable policies for an object based on its groups, tags, and plugin.
     * This method aggregates policies from different mapping sources to create a complete
     * set of policies that should be evaluated for the object.
     *
     * @param groups   The groups associated with the object
     * @param tags     The tags associated with the object
     * @param objectId The ID of the object
     * @param pluginId The ID of the plugin
     * @return A map of metrics to lists of policy IDs that should be evaluated
     */
    private Map<String, List<Long>> qualify(JsonArray groups, JsonArray tags, long objectId, int pluginId)
    {
        var qualifiedPolicies = new HashMap<String, List<Long>>();

        var sort = false;

        try
        {
            if (policiesByObject.containsKey(objectId))
            {
                for (var entry : policiesByObject.get(objectId).entrySet())
                {
                    if (columns.getJsonObject(entry.getKey().split(SEPARATOR_WITH_ESCAPE)[1].trim())
                            .getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger)
                            .anyMatch(plugin -> plugin == pluginId))
                    {
                        qualifiedPolicies.computeIfAbsent(entry.getKey(), value -> new ArrayList<>());

                        for (var policy : entry.getValue())
                        {
                            if (!qualifiedPolicies.get(entry.getKey()).contains(policy))
                            {
                                qualifiedPolicies.get(entry.getKey()).add(policy);
                            }
                        }
                    }
                }
            }

            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getLong(index);

                if (policiesByGroup.containsKey(group))
                {
                    for (var entry : policiesByGroup.get(group).entrySet())
                    {
                        if (qualifiedPolicies.containsKey(entry.getKey()))
                        {
                            sort = true;
                        }
                        else
                        {

                            if (columns.getJsonObject(entry.getKey().split(SEPARATOR_WITH_ESCAPE)[1].trim())
                                    .getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger)
                                    .anyMatch(plugin -> plugin == pluginId))
                            {
                                qualifiedPolicies.put(entry.getKey(), new ArrayList<>());
                            }
                        }

                        for (var policy : entry.getValue())
                        {
                            if (qualifiedPolicies.containsKey(entry.getKey()) && !qualifiedPolicies.get(entry.getKey()).contains(policy))
                            {
                                qualifiedPolicies.get(entry.getKey()).add(policy);
                            }
                        }
                    }
                }
            }

            for (var index = 0; index < tags.size(); index++)
            {
                var tagId = tags.getLong(index);

                if (policiesByTag.containsKey(tagId))
                {
                    for (var entry : policiesByTag.get(tagId).entrySet())
                    {
                        if (qualifiedPolicies.containsKey(entry.getKey()))
                        {
                            sort = true;
                        }
                        else
                        {

                            if (columns.getJsonObject(entry.getKey().split(SEPARATOR_WITH_ESCAPE)[1].trim())
                                    .getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).stream().map(CommonUtil::getInteger)
                                    .anyMatch(plugin -> plugin == pluginId))
                            {
                                qualifiedPolicies.put(entry.getKey(), new ArrayList<>());
                            }
                        }

                        for (var policy : entry.getValue())
                        {
                            if (qualifiedPolicies.containsKey(entry.getKey()) && !qualifiedPolicies.get(entry.getKey()).contains(policy))
                            {
                                qualifiedPolicies.get(entry.getKey()).add(policy);
                            }
                        }
                    }
                }
            }

            if (sort)
            {
                for (var entry : qualifiedPolicies.entrySet())
                {
                    if (entry.getValue().size() > 1)
                    {
                        entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedPolicies;
    }

    /**
     * Updates policy assignments for a specific metric.
     * This method reassigns policies when a metric's configuration changes.
     *
     * @param metric The metric whose policies need to be updated
     */
    private void assign(String metric)
    {
        var update = false;

        for (var policy : policies.values())
        {
            if (!policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()) && policy.getJsonObject(POLICY_CONTEXT).getString(METRIC).equalsIgnoreCase(metric))
            {
                update = true;
            }
        }

        if (update)
        {
            assign();
        }
    }

    /**
     * Assigns all policies to objects, groups, and tags.
     * This method builds the mapping between policies and the entities they apply to,
     * which is essential for efficient policy evaluation.
     */
    private void assign()
    {
        policiesByObject.clear();

        policiesByGroup.clear();

        policiesByTag.clear();

        policies.forEach((key, value) ->
        {
            var metric = value.getString(POLICY_TYPE) + SEPARATOR + value.getJsonObject(POLICY_CONTEXT).getString(METRIC);

            JsonArray entities;

            var policyByObject = true;

            var policyByTag = true;

            var policyContext = value.getJsonObject(POLICY_CONTEXT);

            if (policyContext.getJsonArray(ENTITIES) != null && !policyContext.getJsonArray(ENTITIES).isEmpty())//if policy contains filter of group or monitor so just assigning that filtered entities or groups
            {
                entities = policyContext.getJsonArray(ENTITIES);

                if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                {
                    policyByObject = false;

                    policyByTag = false;
                }
                else if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                {
                    policyByObject = false;
                }
            }
            else
            {
                if (value.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()))
                {
                    entities = ObjectConfigStore.getStore().getItemsByPlugin(new JsonArray().add(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.AVAILABILITY.getName())), true);
                }
                else
                {
                    if (!columns.isEmpty() && columns.containsKey(policyContext.getString(METRIC)))
                    {
                        entities = ObjectConfigStore.getStore().getItemsByPlugin(columns.getJsonObject(policyContext.getString(METRIC)).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS), true);
                    }
                    else entities = new JsonArray();
                }
            }

            assign(value.getLong(ID), metric, entities, policyByObject ? policiesByObject : policyByTag ? policiesByTag : policiesByGroup);
        });

        sort();
    }

    //according to entity type selected monitor or group will be assigning policies accordingly

    /**
     * Assigns a specific policy to entities.
     * This method maps a policy to the objects, groups, or tags it applies to,
     * creating the necessary data structures for policy evaluation.
     *
     * @param policyId         The ID of the policy to assign
     * @param metricKey        The metric key associated with the policy
     * @param entities         The entities (objects, groups, or tags) to assign the policy to
     * @param policiesByEntity The map to store the policy assignments
     */
    private void assign(long policyId, String metricKey, JsonArray entities, Map<Long, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            for (var i = 0; i < entities.size(); i++)
            {
                var object = entities.getLong(i);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());

                    policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                }
                else if (!policiesByEntity.get(object).containsKey(metricKey))
                {
                    policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(metricKey).contains(policyId))
                {
                    policiesByEntity.get(object).get(metricKey).add(policyId);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    // will be sorting all policies according to metric with its last created or updated time in reverse order or descending so that only last one needs to be evaluated..

    /**
     * Sorts policies by creation time in descending order.
     * This ensures that newer policies take precedence over older ones
     * when multiple policies apply to the same object and metric.
     */
    private void sort()
    {
        for (var values : policiesByObject.values())
        {
            for (var entry : values.entrySet())
            {
                entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
            }
        }

        for (var values : policiesByGroup.values())
        {
            for (var entry : values.entrySet())
            {
                entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
            }
        }

        for (var values : policiesByTag.values())
        {
            for (var entry : values.entrySet())
            {
                entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
            }
        }
    }

    /**
     * Updates column mappings when they change.
     * This method processes column mapper update events and updates the internal
     * column mapping data structures accordingly.
     *
     * @param event The event containing updated column mapping information
     */
    private void updateColumnMapper(JsonObject event)
    {
        try
        {
            if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
            {
                var tokens = event.getString(DatastoreConstants.MAPPER).split(COLUMN_SEPARATOR, -1);

                if (!columns.containsKey(tokens[2]))
                {
                    columns.put(tokens[2], new JsonObject());
                }

                var mapper = columns.getJsonObject(tokens[2]);

                var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                if (plugins == null)
                {
                    plugins = new JsonArray(new ArrayList<>(1));
                }

                var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

                if (categories == null)
                {
                    categories = new JsonArray(new ArrayList<>(1));
                }

                if (!categories.contains(CommonUtil.getInteger(tokens[0])))
                {
                    mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
                }

                mapper.put(DatastoreConstants.MAPPER_INSTANCE, tokens[3]);

                if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
                {
                    mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));

                    assign(tokens[2]);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Executes policy evaluation queries.
     * This method prepares and sends visualization queries for policy evaluation,
     * handling different policy types (anomaly, baseline, forecast) appropriately.
     *
     * @param qualifiedPolicies Map of metrics to lists of policy IDs that should be evaluated
     */
    private void executeQuery(Map<String, List<Long>> qualifiedPolicies)
    {
        try
        {
            qualifiedPolicies.forEach((key, value) ->
            {
                var tokens = key.split(SEPARATOR_WITH_ESCAPE);

                var policyId = CommonUtil.getLong(tokens[0]);

                var policy = policies.get(policyId);

                var context = new JsonObject(VisualizationConstants.VISUALIZATION_CHART_GRID_CONTEXT);

                var metric = tokens[1].trim();

                var instanceRequest = metric.contains(INSTANCE_SEPARATOR);

                String timeline;

                context.put(DatastoreConstants.DATASTORE_PASSOVER_STEP_1_QUERY, YES);//as decided need to this parameter required from DB to pass over step 1 and directly do to step 2

                if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()))
                {
                    timeline = policy.getJsonObject(POLICY_CONTEXT).getString("evaluation.window", VisualizationConstants.VisualizationTimeline.LAST_24_HOURS.getName());

                    context.put(VisualizationConstants.VISUALIZATION_CATEGORY, VisualizationConstants.VisualizationCategory.FORECAST.getName());

                    context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, FORECAST_TIMELINES.get(timeline));

                    context.put(VisualizationConstants.VISUALIZATION_TYPE, VisualizationConstants.VisualizationCategory.HISTOGRAM.getName());

                    context.put(VisualizationConstants.VISUALIZATION_RESULT_TYPE, VisualizationConstants.VisualizationResultType.EXTENDED.getName());//as we getting grid data in forecast so Result type will be compacted
                }

                else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
                {
                    timeline = VisualizationConstants.VisualizationTimeline.LAST_15_DAYS.getName();

                    context.put(VisualizationConstants.VISUALIZATION_CATEGORY, VisualizationConstants.VisualizationCategory.BASELINE.getName());

                    context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, "30 m");

                    var iterator = value.iterator();

                    while (iterator.hasNext())
                    {
                        var object = ObjectConfigStore.getStore().getItem(iterator.next());

                        if ((DateTimeUtil.currentSeconds() - object.getLong(AIOpsObject.OBJECT_CREATION_TIME_SECONDS)) < BASELINE_TRIGGER_INTERVAL_SECONDS)//minimum require time for anomaly policy qualification
                        {
                            iterator.remove();
                        }
                    }

                    context.put(VisualizationConstants.VISUALIZATION_RESULT_TYPE, VisualizationConstants.VisualizationResultType.COMPACTED.getName());//as we getting grid data in forecast so Result type will be compacted
                }

                else
                {
                    var iterator = value.iterator();

                    var creationTimeSeconds = 0L;

                    while (iterator.hasNext())
                    {
                        var object = ObjectConfigStore.getStore().getItem(iterator.next());

                        if (DateTimeUtil.currentSeconds() - object.getLong(AIOpsObject.OBJECT_CREATION_TIME_SECONDS) < ANOMALY_TRIGGER_INTERVAL_SECONDS)//minimum require time for anomaly policy qualification
                        {
                            iterator.remove();

                            if (instancesByObject.containsKey(object.getInteger(AIOpsObject.OBJECT_ID)))
                            {
                                instancesByObject.get(object.getInteger(AIOpsObject.OBJECT_ID)).remove(metric);
                            }
                        }

                        else
                        {

                            if (creationTimeSeconds > 0)
                            {
                                if (object.getLong(AIOpsObject.OBJECT_CREATION_TIME_SECONDS) < creationTimeSeconds)
                                {
                                    creationTimeSeconds = object.getLong(AIOpsObject.OBJECT_CREATION_TIME_SECONDS);
                                }
                            }

                            else
                            {
                                creationTimeSeconds = object.getLong(AIOpsObject.OBJECT_CREATION_TIME_SECONDS);
                            }
                        }
                    }

                    context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, "30 m");

                    //will be taking minimum creation time of qualified monitors then will be calculating difference in days from current time
                    var duration = TimeUnit.DAYS.convert((DateTimeUtil.currentSeconds() - creationTimeSeconds), TimeUnit.SECONDS);

                    if (duration >= 90)
                    {
                        timeline = "-90d";
                    }

                    else
                    {
                        //according to qualified days will be taking granularity
                        if (duration <= 1)
                        {
                            context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, "5 m");
                        }

                        else if (duration <= 6)
                        {
                            context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, "15 m");
                        }

                        timeline = duration <= 1 ? VisualizationConstants.VisualizationTimeline.LAST_24_HOURS.getName() : "-" + duration + "d";
                    }

                    context.put(VisualizationConstants.PUBLISH_SUB_QUERY_PROGRESS, true).put(VisualizationConstants.VISUALIZATION_CATEGORY, VisualizationConstants.VisualizationCategory.ANOMALY.getName()).put(VisualizationConstants.VISUALIZATION_TYPE, "Line");

                    context.getJsonObject(VisualizationConstants.VISUALIZATION_PROPERTIES).put("anomaly", new JsonObject().put(VisualizationConstants.DATA_POINTS, policy.getJsonObject(POLICY_CONTEXT).getInteger(VisualizationConstants.DATA_POINTS, NOT_AVAILABLE)));

                    context.put(VisualizationConstants.VISUALIZATION_RESULT_TYPE, VisualizationConstants.VisualizationResultType.EXTENDED.getName());//as we getting grid data in Anomaly so Result type will be extended to get single monitor series at a time
                }

                var dataSource = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0);

                var dataPoint = new JsonObject().put(VisualizationConstants.AGGREGATOR, DatastoreConstants.AggregationType.AVG.getName()).put(VisualizationConstants.DATA_POINT, key.split(SEPARATOR_WITH_ESCAPE)[1]);

                dataPoint.put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName());

                dataPoint.put(ENTITIES, value);

                context.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).put(VisualizationConstants.RELATIVE_TIMELINE, timeline);

                dataSource.put(VisualizationConstants.DATA_POINTS, new JsonArray().add(dataPoint));

                var instanceKeys = new ArrayList<String>();

                var id = CommonUtil.newEventId();

                if (instanceRequest)
                {
                    value.forEach(entity ->
                    {
                        if (instancesByObject.containsKey(ObjectConfigStore.getStore().getObjectId(entity)) && instancesByObject.get(ObjectConfigStore.getStore().getObjectId(entity)).containsKey(metric))
                        {
                            for (var instance : instancesByObject.get(ObjectConfigStore.getStore().getObjectId(entity)).remove(metric))
                            {
                                instanceKeys.add(ObjectConfigStore.getStore().getObjectId(entity) + CARET_SEPARATOR + instance.split(SEPARATOR_WITH_ESCAPE)[0] + CARET_SEPARATOR + metric + SEPARATOR + instance.split(SEPARATOR_WITH_ESCAPE)[1] + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(CommonUtil.getInteger(instance.split(SEPARATOR_WITH_ESCAPE)[1])));
                            }
                        }
                    });

                    dataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationConstants.VisualizationGrouping.MONITOR.getName()).add(metric.split(INSTANCE_SEPARATOR)[0]));
                }

                else
                {
                    dataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationConstants.VisualizationGrouping.MONITOR.getName()));
                }

                if (!value.isEmpty())
                {
                    if (instanceRequest)
                    {
                        if (!instanceKeys.isEmpty())
                        {
                            if (instanceKeys.size() > DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE)
                            {
                                for (var i = 0; i < instanceKeys.size(); i += DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE)
                                {
                                    var entityKeys = new HashMap<>();

                                    instanceKeys.subList(i, Math.min(i + DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE, instanceKeys.size())).forEach(entityKey -> entityKeys.put(entityKey.split(SEPARATOR_WITH_ESCAPE)[0], entityKey.split(SEPARATOR_WITH_ESCAPE)[1]));

                                    dataPoint.put(VisualizationConstants.ENTITY_KEYS, entityKeys);

                                    send(id, context.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(REQUEST_ID, id).put(METRIC, metric).put(PolicyEngineConstants.POLICY_ID, policyId).put(User.USER_NAME, DEFAULT_USER).put(PLUGIN_ID, tokens[2]).put(EVENT_TYPE, EVENT_AIOPS_METRIC_POLICY_RESPONSE));
                                }
                            }
                            else
                            {
                                var entityKeys = new HashMap<>();

                                instanceKeys.forEach(entityKey -> entityKeys.put(entityKey.split(SEPARATOR_WITH_ESCAPE)[0], entityKey.split(SEPARATOR_WITH_ESCAPE)[1]));

                                dataPoint.put(VisualizationConstants.ENTITY_KEYS, entityKeys);

                                send(id, context.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(REQUEST_ID, id).put(METRIC, metric).put(PolicyEngineConstants.POLICY_ID, policyId).put(PLUGIN_ID, tokens[2]).put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_AIOPS_METRIC_POLICY_RESPONSE));
                            }
                        }

                        else
                        {
                            LOGGER.warn("No instances qualified for AIOps policy inspection");
                        }
                    }

                    else
                    {
                        if (value.size() > DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE)
                        {
                            for (var i = 0; i < value.size(); i += DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE)
                            {

                                dataPoint.put(ENTITIES, value.subList(i, Math.min(i + DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE, value.size())));

                                send(id, context.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(REQUEST_ID, id).put(METRIC, metric).put(PolicyEngineConstants.POLICY_ID, policyId).put(PLUGIN_ID, tokens[2]).put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_AIOPS_METRIC_POLICY_RESPONSE));
                            }
                        }

                        else
                        {
                            send(id, context.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(REQUEST_ID, id).put(METRIC, metric).put(PolicyEngineConstants.POLICY_ID, policyId).put(PLUGIN_ID, tokens[2]).put(User.USER_NAME, DEFAULT_USER).put(EVENT_TYPE, EVENT_AIOPS_METRIC_POLICY_RESPONSE));
                        }
                    }
                }

                else
                {
                    LOGGER.warn("No entities qualified for AIOps policy inspection");
                }
            });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Sends the next policy evaluation query.
     * This method processes the queue of policy evaluation queries,
     * sending the next one if the queue is not empty.
     */
    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queryContexts.remove(id));
    }

    /**
     * Sends a specific policy evaluation query.
     * This method sends the query context to the event bus for processing
     * and updates the list of running queries.
     *
     * @param id      The ID of the query
     * @param context The context containing query parameters
     */
    private void send(long id, JsonObject context)
    {
        //TODO need to change aiops request size

        if (runningQueries.size() == 4)
        {
            queryContexts.put(id, context);

            queuedQueries.add(id);
        }

        else
        {
            runningQueries.add(id);

            vertx.eventBus().send(EVENT_VISUALIZATION, context);
        }
    }

    /**
     * Stops the AIOpsMetricPolicyManager verticle.
     * This method cleans up resources and stops the event engine
     * when the verticle is undeployed.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
