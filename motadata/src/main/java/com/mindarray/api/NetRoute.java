/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.LicenseCacheStore;
import com.mindarray.store.NetRouteCacheStore;
import com.mindarray.store.NetRouteConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.net.InetAddress;
import java.util.Arrays;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class NetRoute extends AbstractAPI
{
    public static final String NETROUTE_ID = "netroute.id";
    public static final String NETROUTE_NAME = "netroute.name";
    public static final String NETROUTE_SOURCE = "netroute.source";
    public static final String NETROUTE_DESTINATION = "netroute.destination";
    public static final String NETROUTE_DESTINATION_IP = "netroute.destination.ip";
    public static final String NETROUTE_DESTINATION_PORT = "netroute.destination.port";
    public static final String NETROUTE_POLLING_TIME = "netroute.polling.time";
    public static final String NETROUTE_STATE = "netroute.state";
    public static final String NETROUTE_TAGS = "netroute.tags";

    private static final Logger LOGGER = new Logger(NetRoute.class, MOTADATA_API, "NetRoute API");

    public NetRoute()
    {
        super("netroutes", NetRouteConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState);

            router.get("/" + endpoint + "/:id/metadata").handler(this::getHopMetadata);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        var promise = Promise.<JsonObject>promise();

        if (LicenseUtil.getRemainingObjects(APIConstants.Entity.NETROUTE.getName()) > 0)
        {
            try
            {
                var items = NetRouteConfigStore.getStore().flatItemsByValue(NETROUTE_SOURCE, requestBody.getString(NETROUTE_SOURCE), NETROUTE_DESTINATION, requestBody.getString(NETROUTE_DESTINATION));

                if (items.isEmpty())
                {
                    var ipAddresses = InetAddress.getAllByName(requestBody.getString(NETROUTE_DESTINATION));

                    if (ipAddresses != null && ipAddresses.length > 0)
                    {
                        var ipAddress = Arrays.stream(ipAddresses).map(InetAddress::getHostAddress).filter(item -> APIConstants.PATTERN_IP_ADDRESS.matcher(item).matches()).findFirst().orElse(null);

                        if (ipAddress != null)
                        {
                            LOGGER.info(String.format("destination IP address %s of %s ", ipAddress, requestBody.getString(NETROUTE_DESTINATION)));

                            items = NetRouteConfigStore.getStore().flatItemsByMultiValueField(NETROUTE_DESTINATION_IP, ipAddress, NETROUTE_SOURCE);

                            if (items.isEmpty())
                            {
                                requestBody.put(NETROUTE_DESTINATION_IP, new JsonArray().add(ipAddress)).put(NETROUTE_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(NETROUTE_TAGS), Tag.TagType.NETROUTE.getName(), DBConstants.ENTITY_TYPE_USER));

                                promise.complete(requestBody);
                            }
                            else
                            {
                                promise.fail(String.format(NETROUTE_ALREADY_EXIST, ipAddress));

                                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(NETROUTE_ALREADY_EXIST, ipAddress)));
                            }
                        }
                        else
                        {
                            promise.fail(NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS);

                            this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST)
                                    .put(MESSAGE, String.format(ENTITY_CREATE_FAILED, APIConstants.Entity.NETROUTE.getName(), NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS)));
                        }
                    }
                    else
                    {
                        promise.fail(String.format(ENTITY_CREATE_FAILED, APIConstants.Entity.NETROUTE.getName(), NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS));

                        this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST)
                                .put(MESSAGE, String.format(ENTITY_CREATE_FAILED, APIConstants.Entity.NETROUTE.getName(), NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS)));

                    }
                }
                else
                {
                    promise.fail(String.format(NETROUTE_ALREADY_EXIST, requestBody.getString(NETROUTE_DESTINATION)));

                    this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                            .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(NETROUTE_ALREADY_EXIST, requestBody.getString(NETROUTE_DESTINATION))));

                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception.getMessage());

                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, SC_BAD_REQUEST)
                        .put(MESSAGE, String.format(ENTITY_CREATE_FAILED, APIConstants.Entity.NETROUTE.getName(), NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS)).put(ERROR, exception.getMessage()));

            }
        }
        else
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, LICENSE_LIMIT_EXCEEDED)
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));

            promise.fail(LICENSE_LIMIT_EXCEEDED);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        routingContext.body().asJsonObject().put(NETROUTE_TAGS, TagConfigStore.getStore().addItems(routingContext.body().asJsonObject().getJsonArray(NETROUTE_TAGS), Tag.TagType.NETROUTE.getName(), DBConstants.ENTITY_TYPE_USER));

        return Future.succeededFuture(routingContext.body().asJsonObject());
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray items, RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                item.put(NMSConstants.LAST_POLL_TIME, NetRouteCacheStore.getStore().getMetricPollTimestamp(item.getLong(ID)));

                item.put(NetRoute.NETROUTE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(NetRoute.NETROUTE_TAGS), Tag.TagType.NETROUTE, DBConstants.ENTITY_TYPE_USER));

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(NETROUTE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(NetRoute.NETROUTE_TAGS), Tag.TagType.NETROUTE, DBConstants.ENTITY_TYPE_USER))));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject response, RoutingContext routingContext)
    {
        NetRouteCacheStore.getStore().addMetric(response.getLong(ID), 60);

        LicenseCacheStore.getStore().update(APIConstants.Entity.NETROUTE.getName(), true);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_NETROUTE.name()).put(EventBusConstants.EVENT_CONTEXT, response));

        return super.afterCreate(response, routingContext);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_NETROUTE).put(ID, entity.getLong(ID)));

        LicenseCacheStore.getStore().update(APIConstants.Entity.NETROUTE.getName(), false);

        NetRouteCacheStore.getStore().deleteMetric(entity.getLong(ID));

        this.send(routingContext, entity);

        return Future.succeededFuture();
    }

    protected void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(NETROUTE_STATE);

        var id = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(DBConstants.TBL_NETROUTE, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                    new JsonObject().put(NETROUTE_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            var item = NetRouteConfigStore.getStore().getItem(id, false);

                            if (NMSConstants.State.DISABLE.name().equalsIgnoreCase(state))
                            {
                                NetRouteCacheStore.getStore().deleteMetric(id);
                            }
                            else if (NMSConstants.State.ENABLE.name().equalsIgnoreCase(state))
                            {
                                NetRouteCacheStore.getStore().addMetric(id, item.getInteger(NetRoute.NETROUTE_POLLING_TIME));
                            }

                            NetRouteConfigStore.getStore().updateItem(id);

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, id));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, id));
                        }
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getHopMetadata(RoutingContext routingContext)
    {
        var id = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, NetRouteCacheStore.getStore().getHopRegistryInfo(id)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
