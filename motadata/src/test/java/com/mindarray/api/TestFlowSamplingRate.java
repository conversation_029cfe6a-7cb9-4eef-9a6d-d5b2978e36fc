/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.FlowSamplingRateConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestFlowSamplingRate
{
    private static final String IP = "**********";

    private static final Logger LOGGER = new Logger(TestFlowSamplingRate.class, MOTADATA_API, "Test Flow Sample Rate");

    private static long ID = 0L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION,
                new JsonObject().put(EventBusConstants.EVENT_SOURCE, IP)
                        .put(EventBusConstants.EVENT, EventBusConstants.EVENT_FLOW)
                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                        .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testSaveFlowSamplingRate(VertxTestContext testContext) throws InterruptedException
    {
        TestUtil.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, IP).put(NMSConstants.INTERFACE_INDEX, 12).put(FlowEngineConstants.INTERFACE_SAMPLING_RATE, 1));

        var items = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(EventBusConstants.EVENT_TYPE, new JsonArray().add("flow"));

        LOGGER.info("Event Sources : " + items.encodePrettily());

        TestUtil.vertx().setTimer(5000, timer ->

                TestAPIUtil.get(TestAPIConstants.FLOW_SAMPLING_RATES, testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(200, response.statusCode());

                    var result = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                    LOGGER.info("testSaveFlowSamplingRate result: " + result);

                    Assertions.assertNotNull(result);

                    for (var index = 0; index < result.size(); index++)
                    {
                        var item = result.getJsonObject(index);

                        if (item.getString(EventBusConstants.EVENT_SOURCE).equals(IP) && item.getString(NMSConstants.INTERFACE_INDEX).equals("12"))
                        {
                            ID = item.getLong(GlobalConstants.ID);

                            Assertions.assertEquals("1", item.getString(FlowEngineConstants.INTERFACE_SAMPLING_RATE));

                            testContext.completeNow();
                        }
                    }

                    testContext.completeNow();
                }))));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testUpdateFlowSamplingRate1(VertxTestContext testContext) throws InterruptedException
    {
        LOGGER.info("testUpdateFlowSamplingRate1 : ID :" + ID);

        TestUtil.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_FLOW_SAMPLING_RATE).put(EventBusConstants.EVENT_SOURCE, IP).put(NMSConstants.INTERFACE_INDEX, 15).put(FlowEngineConstants.INTERFACE_SAMPLING_RATE, 5));

        TestUtil.vertx().setTimer(5000, timer ->

                TestAPIUtil.get(TestAPIConstants.FLOW_SAMPLING_RATES, testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(200, response.statusCode());

                    var result = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                    LOGGER.info("testUpdateFlowSamplingRate1 result: " + result);

                    Assertions.assertNotNull(result);

                    for (var index = 0; index < result.size(); index++)
                    {
                        var item = result.getJsonObject(index);

                        if (item.getString(EventBusConstants.EVENT_SOURCE).equals(IP) && item.getString(NMSConstants.INTERFACE_INDEX).equals("15"))
                        {
                            ID = item.getLong(GlobalConstants.ID);

                            LOGGER.info("updated sampling Rate for ID : " + ID);

                            Assertions.assertEquals("5", item.getString(FlowEngineConstants.INTERFACE_SAMPLING_RATE));

                            testContext.completeNow();
                        }
                    }
                }))));


        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testUpdateFlowSamplingRate2(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject("{\"interface.custom.sampling.rate\":10}").put(GlobalConstants.ID, ID);

        LOGGER.info("Going to update :" + ID);

        TestAPIUtil.put(TestAPIConstants.FLOW_SAMPLING_RATES + "/" + ID, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            Assertions.assertEquals(200, response.statusCode());

            var item = FlowSamplingRateConfigStore.getStore().getItem(ID);

            Assertions.assertTrue(item.containsKey("interface.custom.sampling.rate"));

            Assertions.assertEquals("10", item.getString("interface.custom.sampling.rate"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testGetFlowSamplingRate(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(TestAPIConstants.FLOW_SAMPLING_RATES, testContext.succeeding(response -> testContext.verify(() ->
        {

            LOGGER.info("testGetFlowSamplingRate Result : " + response.bodyAsJsonObject());

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertNotNull(response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}
