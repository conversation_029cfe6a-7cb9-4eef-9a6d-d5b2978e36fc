/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import org.apache.commons.io.FileUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.util.Objects;
import java.util.Set;

import static com.mindarray.GlobalConstants.DASH_SEPARATOR;
import static com.mindarray.GlobalConstants.PATH_SEPARATOR;

/**
 * The FlapCacheBackupJob class is responsible for backing up status flap cache files in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs regular backups of critical cache files
 * related to status flaps and policy triggers. These backups are important for data recovery in case
 * of system failures or data corruption.
 * <p>
 * The job performs the following tasks:
 * <ol>
 *   <li>Creates a temporary directory for the backup</li>
 *   <li>Copies specific cache files to the temporary directory</li>
 *   <li>Compresses the temporary directory into a zip file with a timestamp</li>
 *   <li>Deletes the temporary directory</li>
 * </ol>
 * <p>
 * The job is scheduled to run every 30 minutes to ensure regular backups of these critical files.
 * <p>
 * Implementation note: This job was added as part of MOTADATA-970 to take backup of status-flaps,
 * status-flap-durations, and metric-policy-flap-durations files.
 */
public class FlapCacheBackupJob implements Job
{
    /**
     * CRON expression for scheduling this job to run every 30 minutes
     */
    public static final String FLAP_CACHE_BACKUP_RETENTION_JOB_CRON_EXPRESSION = "0 */30 * * * ?"; // Every 30 minute

    /**
     * Set of cache file names that should be backed up by this job
     */
    public static final Set<String> CACHE_FILES = Set.of("status-flaps", "status-flap-durations", "metric-policy-flap-durations");

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlapCacheBackupJob.class, GlobalConstants.MOTADATA_JOB, "Flap Cache Backup Job");

    /**
     * Executes the flap cache backup job.
     * <p>
     * This method performs the backup operation by copying specified cache files to a temporary
     * directory, compressing them into a zip file with a timestamp, and then deleting the
     * temporary directory. The backup operation is executed in a blocking context to prevent
     * interference with other operations.
     *
     * @param context the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext context)
    {
        // Get the directory containing the configuration files to be backed up
        var configDir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

        // Create a temporary directory for the backup files
        // Note: We create a separate directory inside config-db-backups/flap-caches to avoid
        // accidental deletion of other files during the cleanup phase
        var backupDir = new File(DBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR + "flap-caches" + PATH_SEPARATOR + "temp-flap-cache");

        // Execute the backup operation in a blocking context to prevent interference with other operations
        Bootstrap.vertx().executeBlocking(future ->
        {
            LOGGER.info("Starting Flap Cache Backup Job...");

            try
            {
                // Create the temporary backup directory
                backupDir.mkdirs();

                // Copy each relevant cache file to the backup directory
                for (var file : Objects.requireNonNull(configDir.listFiles()))
                {
                    // Only backup files that are in the CACHE_FILES set
                    if (CACHE_FILES.contains(file.getName()))
                    {
                        // Create a new file in the backup directory with the same name
                        var backupFile = new File(backupDir.getAbsolutePath() + PATH_SEPARATOR + file.getName());

                        // Copy the original file to the backup file
                        FileUtils.copyFile(file, backupFile);

                        // Log debug information if debug mode is enabled
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("%s file backed up...", file.getName()));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            // If the backup directory exists and contains files, compress it into a zip file
            if (backupDir.exists() && backupDir.length() > 0)
            {
                // Create a zip file with a timestamp in its name for uniqueness
                var zipFile = new File(DBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR +
                        "flap-caches" + PATH_SEPARATOR +
                        "flap-cache" + DASH_SEPARATOR +
                        DateTimeUtil.currentMilliSeconds() + ".zip");

                // Compress the backup directory into the zip file
                ZipUtil.pack(backupDir, zipFile, false);

                // Delete the temporary backup directory after compression
                FileUtils.deleteQuietly(backupDir);
            }

            LOGGER.info("Status Flap Cache Backup Job completed...");
        });

    }

}
