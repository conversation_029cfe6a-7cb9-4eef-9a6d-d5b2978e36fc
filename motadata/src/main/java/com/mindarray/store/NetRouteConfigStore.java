/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 */


package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.NetRoute;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;

import java.util.ArrayList;
import java.util.HashMap;

import static com.mindarray.GlobalConstants.ENTITIES;
import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.api.APIConstants.*;

public class NetRouteConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(NetRouteConfigStore.class, GlobalConstants.MOTADATA_STORE, "NetRoute Config Store");

    private static final NetRouteConfigStore STORE = new NetRouteConfigStore();

    private NetRouteConfigStore()
    {
        super(DBConstants.TBL_NETROUTE, LOGGER, false, new ArrayList<>()
        {{
            add(new HashMap<>()
            {{
                put(REFERENCE_ENTITY, Entity.NETROUTE_POLICY);
                put(REFERENCE_ENTITY_PROPERTY, PolicyEngineConstants.POLICY_CONTEXT);
                put(REFERENCE_ENTITY_NESTED_PROPERTY, ENTITIES);
                put(REFERENCE_ENTITY_STORE, ConfigStore.NETROUTE_POLICY);
                put(REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.MULTIMAP_VALUE);
            }});

        }});
    }

    public static NetRouteConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_NETROUTE, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                            }

                            if (item.getString(NetRoute.NETROUTE_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                            {
                                NetRouteCacheStore.getStore().addMetric(item.getLong(ID), item.getInteger(NetRoute.NETROUTE_POLLING_TIME));
                            }
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    public int getProvisionedItems()
    {
        return items.size();
    }
}
