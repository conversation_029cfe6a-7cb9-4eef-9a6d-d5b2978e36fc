/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.FlowIPGroupConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.TestAPIConstants.FLOW_IP_GROUP_API_ENDPOINT;
import static com.mindarray.api.APIConstants.REQUEST_PARAM_IDS;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestFlowIPGroup
{
    private static final Map<String, Long> IDS = new HashMap<>();

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testCreateFlowIPGroup1(VertxTestContext testContext) throws InterruptedException
    {
        assertFlowIPGroupCreate(testContext, "1", new JsonObject("{\"flow.ip.group\":[\"*******\",\"*******\"],\"flow.ip.group.name\":\"motadata.com\"}"), "*******", "motadata.com");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testCreateFlowIPGroup2(VertxTestContext testContext) throws InterruptedException
    {
        assertFlowIPGroupCreate(testContext, "2", new JsonObject("{\"flow.ip.group\":[\"*******\",\"*******-********\"],\"flow.ip.group.name\":\"motadata1.com\"}"), "*******", "motadata1.com");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testCreateFlowIPGroup3(VertxTestContext testContext) throws InterruptedException
    {
        assertFlowIPGroupCreate(testContext, "3", new JsonObject("{\"flow.ip.group\":[\"*******\",\"*******-10\"],\"flow.ip.group.name\":\"motadata2.com\"}"), "*******", "motadata2.com");
    }

    @Test
    @Order(4)
    public void testCreateFlowIPGroup4(VertxTestContext testContext) throws InterruptedException
    {
        assertFlowIPGroupCreate(testContext, "4", new JsonObject("{\"flow.ip.group\":[\"5.2.3.xx\"],\"flow.ip.group.name\":\"motadata3.com\"}"), "*********", "motadata3.com");
    }

    @Test
    @Order(5)
    public void testCreateFlowIPGroup5(VertxTestContext testContext) throws InterruptedException
    {
        assertFlowIPGroupCreate(testContext, "5", new JsonObject("{\"flow.ip.group\":[\"*******-255\"],\"flow.ip.group.name\":\"motadata4.com\"}"), "*********", "motadata4.com");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testGetFlowIPGroup(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(FLOW_IP_GROUP_API_ENDPOINT + "/" + IDS.get("1"), testContext.succeeding(response -> testContext.verify(() ->
        {

            var body = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertEquals(IDS.get("1"), body.getJsonObject(GlobalConstants.RESULT).getLong(GlobalConstants.ID));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void testGetAllFlowIPGroup(VertxTestContext testContext) throws InterruptedException
    {
        var flowIPGroupIds = FlowIPGroupConfigStore.getStore().getIds();

        TestAPIUtil.get(FLOW_IP_GROUP_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

            for (var index = 0; index < items.size(); index++)
            {
                Assertions.assertTrue(flowIPGroupIds.contains(items.getJsonObject(index).getLong(GlobalConstants.ID)));
            }

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    public void testUpdateFlowIPGroup(VertxTestContext testContext) throws InterruptedException
    {
        var payload = new JsonObject("{\"flow.ip.group\":[\"*******\"],\"flow.ip.group.name\":\"mindarray.com\"}");

        TestAPIUtil.put(FLOW_IP_GROUP_API_ENDPOINT + "/" + IDS.get("1"), payload, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var item = FlowIPGroupConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

            Assertions.assertNotNull(item);

            Assertions.assertEquals(payload.getString(FlowIPGroup.FLOW_IP_GROUP_NAME), item.getString(FlowIPGroup.FLOW_IP_GROUP_NAME));

            Assertions.assertEquals(payload.getString(FlowIPGroup.FLOW_IP_GROUP_NAME), FlowIPGroupConfigStore.getStore().getDomain("*******"));

            Assertions.assertNull(FlowIPGroupConfigStore.getStore().getDomain("*******"), "Flow IP Group Cache should be updated but didn't");

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    public void testDeleteFlowIPGroup(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.delete(FLOW_IP_GROUP_API_ENDPOINT + "/" + IDS.get("1"), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertNull(FlowIPGroupConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID)), "Flow IP Group should be deleted but didn't");

            Assertions.assertNull(FlowIPGroupConfigStore.getStore().getDomain("*******"), "Flow IP Group Cache should be deleted but didn't");

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    public void testDeleteAllFlowIPGroup(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.deleteAll(FLOW_IP_GROUP_API_ENDPOINT, new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(IDS.get("2"))), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertNull(FlowIPGroupConfigStore.getStore().getItem(IDS.get("2")), "Flow IP Group should be deleted but didn't");

            Assertions.assertNull(FlowIPGroupConfigStore.getStore().getDomain("*******"), "Flow IP Group Cache should be deleted but didn't");

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    private void assertFlowIPGroupCreate(VertxTestContext testContext, String key, JsonObject payload, String ipToBeValidated, String domainToBeValidated) throws InterruptedException
    {
        TestAPIUtil.post(FLOW_IP_GROUP_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {

            var body = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertNotNull(body.getLong(GlobalConstants.ID));

            Assertions.assertNotNull(FlowIPGroupConfigStore.getStore().getItem(body.getLong(GlobalConstants.ID)));

            IDS.put(key, body.getLong(GlobalConstants.ID));

            Assertions.assertEquals(domainToBeValidated, FlowIPGroupConfigStore.getStore().getDomain(ipToBeValidated));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}
