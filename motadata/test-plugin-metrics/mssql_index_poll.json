{"************": {"result": {"correlation.metrics": ["mssql.index", "mssql.unused.index", "mssql.missing.index"], "mssql.missing.index": [{"mssql.missing.index": 5, "mssql.missing.index.group": 6, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[IPAM_Vendors]", "mssql.missing.index.column.id": 1, "mssql.missing.index.column.name": "Name", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 2919, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.54, "mssql.missing.index.user.percent": 98.68}, {"mssql.missing.index": 5, "mssql.missing.index.group": 6, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[IPAM_Vendors]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Icon", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 2919, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.54, "mssql.missing.index.user.percent": 98.68}, {"mssql.missing.index": 7, "mssql.missing.index.group": 8, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[Subscriptions]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "LastSuccessfulDelivery", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 118, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.03, "mssql.missing.index.user.percent": 88.53}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "MaxLoad", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 5, "mssql.missing.index.column.name": "AvgLoad", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 6, "mssql.missing.index.column.name": "TotalMemory", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 8, "mssql.missing.index.column.name": "MaxMemoryUsed", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 22, "mssql.missing.index.group": 23, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 10, "mssql.missing.index.column.name": "PercentMemoryUsed", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 77.93}, {"mssql.missing.index": 20, "mssql.missing.index.group": 21, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.33}, {"mssql.missing.index": 20, "mssql.missing.index.group": 21, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 5, "mssql.missing.index.column.name": "AvgLoad", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.33}, {"mssql.missing.index": 20, "mssql.missing.index.group": 21, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[CPULoad_CS_Daily_hist]", "mssql.missing.index.column.id": 10, "mssql.missing.index.column.name": "PercentMemoryUsed", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.33}, {"mssql.missing.index": 24, "mssql.missing.index.group": 25, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ResponseTime_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.08}, {"mssql.missing.index": 24, "mssql.missing.index.group": 25, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ResponseTime_CS_Daily_hist]", "mssql.missing.index.column.id": 5, "mssql.missing.index.column.name": "AvgResponseTime", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.08}, {"mssql.missing.index": 24, "mssql.missing.index.group": 25, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ResponseTime_CS_Daily_hist]", "mssql.missing.index.column.id": 6, "mssql.missing.index.column.name": "<PERSON><PERSON><PERSON><PERSON>", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 75.08}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "NodeID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "In_Averagebps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 6, "mssql.missing.index.column.name": "In_Maxbps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 15, "mssql.missing.index.column.name": "Out_Averagebps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 26, "mssql.missing.index.group": 27, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 17, "mssql.missing.index.column.name": "Out_Maxbps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 12129, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.16, "mssql.missing.index.user.percent": 66.63}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "NodeID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "In_Discards", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 5, "mssql.missing.index.column.name": "In_Errors", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 6, "mssql.missing.index.column.name": "Out_Discards", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 3, "mssql.missing.index.group": 4, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceErrors_CS_Daily_hist]", "mssql.missing.index.column.id": 7, "mssql.missing.index.column.name": "Out_Errors", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.98}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "Timestamp", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "In_Averagebps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 7, "mssql.missing.index.column.name": "In_TotalBytes", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 8, "mssql.missing.index.column.name": "In_TotalPkts", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 15, "mssql.missing.index.column.name": "Out_Averagebps", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 18, "mssql.missing.index.column.name": "Out_TotalBytes", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 1, "mssql.missing.index.group": 2, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[InterfaceTraffic_CS_Daily_hist]", "mssql.missing.index.column.id": 19, "mssql.missing.index.column.name": "Out_TotalPkts", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 25925, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 58.46}, {"mssql.missing.index": 17, "mssql.missing.index.group": 18, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_ComponentTemplate]", "mssql.missing.index.column.id": 10, "mssql.missing.index.column.name": "ApplicationItemType", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 134, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.13, "mssql.missing.index.user.percent": 39.9}, {"mssql.missing.index": 37, "mssql.missing.index.group": 38, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ContainerStatus_Detail]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "DateTime", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 59, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.54, "mssql.missing.index.user.percent": 37.98}, {"mssql.missing.index": 37, "mssql.missing.index.group": 38, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ContainerStatus_Detail]", "mssql.missing.index.column.id": 4, "mssql.missing.index.column.name": "Status", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 59, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.54, "mssql.missing.index.user.percent": 37.98}, {"mssql.missing.index": 35, "mssql.missing.index.group": 36, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ContainerStatus_Detail]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "DateTime", "mssql.missing.index.column.usage": "INEQUALITY", "mssql.missing.index.user.seeks.rate": 59, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.55, "mssql.missing.index.user.percent": 36.68}, {"mssql.missing.index": 35, "mssql.missing.index.group": 36, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ContainerStatus_Detail]", "mssql.missing.index.column.id": 5, "mssql.missing.index.column.name": "PercentAvailability", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 59, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.55, "mssql.missing.index.user.percent": 36.68}, {"mssql.missing.index": 35, "mssql.missing.index.group": 36, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[ContainerStatus_Detail]", "mssql.missing.index.column.id": 6, "mssql.missing.index.column.name": "PercentMembersAvailability", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 59, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.55, "mssql.missing.index.user.percent": 36.68}, {"mssql.missing.index": 9, "mssql.missing.index.group": 10, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "ParentID", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 32.41}, {"mssql.missing.index": 9, "mssql.missing.index.group": 10, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "ComponentID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 32.41}, {"mssql.missing.index": 9, "mssql.missing.index.group": 10, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 13, "mssql.missing.index.column.name": "IsThresholdOverridden", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 32.41}, {"mssql.missing.index": 15, "mssql.missing.index.group": 16, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "ComponentID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 16.0}, {"mssql.missing.index": 15, "mssql.missing.index.group": 16, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 13, "mssql.missing.index.column.name": "IsThresholdOverridden", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 16.0}, {"mssql.missing.index": 15, "mssql.missing.index.group": 16, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 21, "mssql.missing.index.column.name": "UseBaseline", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 16.0}, {"mssql.missing.index": 11, "mssql.missing.index.group": 12, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 21, "mssql.missing.index.column.name": "UseBaseline", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 15.94}, {"mssql.missing.index": 13, "mssql.missing.index.group": 14, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 2, "mssql.missing.index.column.name": "ParentID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 14.71}, {"mssql.missing.index": 13, "mssql.missing.index.group": 14, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 3, "mssql.missing.index.column.name": "ComponentID", "mssql.missing.index.column.usage": "INCLUDE", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 14.71}, {"mssql.missing.index": 13, "mssql.missing.index.group": 14, "mssql.missing.index.database.name": "SolarWindsOrion", "mssql.missing.index.table.name": "[SolarWindsOrion].[dbo].[APM_DynamicEvidenceColumnSchema]", "mssql.missing.index.column.id": 13, "mssql.missing.index.column.name": "IsThresholdOverridden", "mssql.missing.index.column.usage": "EQUALITY", "mssql.missing.index.user.seeks.rate": 67, "mssql.missing.index.user.scans.rate": 0, "mssql.missing.index.user.cost": 0.18, "mssql.missing.index.user.percent": 14.71}], "mssql.missing.indices": 50, "mssql.index": [{"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "sqlagent_jobsteps", "mssql.index": "sqlagent_jobsteps_clust", "mssql.index.user.seeks.rate": 80, "mssql.index.user.scans.rate": 322, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 201}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "plan_persist_query_text", "mssql.index": "plan_persist_query_text_cidx", "mssql.index.user.seeks.rate": 0, "mssql.index.user.scans.rate": 67, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 67}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "dbo", "mssql.index.table.name": "sp_MScleanupmergepublisher", "mssql.index.user.seeks.rate": 0, "mssql.index.user.scans.rate": 126, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 0}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "plan_persist_query", "mssql.index": "plan_persist_query_cidx", "mssql.index.user.seeks.rate": 134, "mssql.index.user.scans.rate": 0, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 67}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "queue_messages_1003150619", "mssql.index": "queue_clustered_index", "mssql.index.user.seeks.rate": 4, "mssql.index.user.scans.rate": 7777, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 0}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "plan_persist_context_settings", "mssql.index": "plan_persist_context_settings_cidx", "mssql.index.user.seeks.rate": 4762634, "mssql.index.user.scans.rate": 1614320, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 335}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "queue_messages_1035150733", "mssql.index": "queue_clustered_index", "mssql.index.user.seeks.rate": 4, "mssql.index.user.scans.rate": 0, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 0}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "plan_persist_query_template_parameterization", "mssql.index": "plan_persist_query_template_parameterization_cidx", "mssql.index.user.seeks.rate": 8736909, "mssql.index.user.scans.rate": 268, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 335}, {"mssql.index.database.name": "master", "mssql.index.schema.name": "dbo", "mssql.index.table.name": "TEST_LOCK", "mssql.index": "PK__TEST_LOC__3213E83FC57CA99A", "mssql.index.user.seeks.rate": 389, "mssql.index.user.scans.rate": 424, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 642729}, {"mssql.index.database.name": "SolarWindsOrion", "mssql.index.schema.name": "sys", "mssql.index.table.name": "syscommittab", "mssql.index": "ci_commit_ts", "mssql.index.user.seeks.rate": 3771794, "mssql.index.user.scans.rate": 116731, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 0}], "mssql.indices": 10, "mssql.unused.index": [{"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_db", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_dev", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_usg", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_monitor", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "MSreplication_options", "mssql.unused.index.id": 0}], "mssql.unused.indices": 5}, "errors": []}, "fd00:1:1:1::132": {"result": {"correlation.metrics": ["mssql.index", "mssql.unused.index", "mssql.missing.index"], "mssql.index": [{"mssql.index.database.name": "master", "mssql.index.schema.name": "dbo", "mssql.index.table.name": "spt_monitor", "mssql.index.user.seeks.rate": 0, "mssql.index.user.scans.rate": 3320, "mssql.index.user.lookups.rate": 0, "mssql.index.user.updates.rate": 0}], "mssql.indices": 1, "mssql.unused.index": [{"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_db", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_dev", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "spt_fallback_usg", "mssql.unused.index.id": 0}, {"mssql.unused.index.database.name": "master", "mssql.unused.index.schema.name": "dbo", "mssql.unused.index.table.name": "MSreplication_options", "mssql.unused.index.id": 0}], "mssql.unused.indices": 4}, "errors": []}}