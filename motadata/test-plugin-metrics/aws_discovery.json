{"127.0.0.1": {"_type": "1", "credential.profile.name": "AWS-Cloud-Test1655203684393", "discovery.category": "Cloud", "discovery.credential.profiles": [{"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.service.down.instance.discovery": "", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203684393", "credential.profile.protocol": "Cloud", "id": ***************, "objects": [{"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "E1RVVPM08N16XT(Global)", "object.region": "Global", "object.target": "E1RVVPM08N16XT(Global)", "object.type": "Amazon CloudFront", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "awsplugintests3(ap-south-1)", "object.region": "Global", "object.target": "awsplugintests3(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "docs.motadata.com(ap-south-1)", "object.region": "Global", "object.target": "docs.motadata.com(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "elasticbeanstalk-ap-south-1-************(ap-south-1)", "object.region": "Global", "object.target": "elasticbeanstalk-ap-south-1-************(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "elasticbeanstalk-us-east-1-************(us-east-1)", "object.region": "Global", "object.target": "elasticbeanstalk-us-east-1-************(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "itsm-docs.motadata.com(ap-south-1)", "object.region": "Global", "object.target": "itsm-docs.motadata.com(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "mindarray(ap-south-1)", "object.region": "Global", "object.target": "mindarray(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata-common(ap-south-1)", "object.region": "Global", "object.target": "motadata-common(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata2016(us-east-1)", "object.region": "Global", "object.target": "motadata2016(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadataserviceops(us-east-1)", "object.region": "Global", "object.target": "motadataserviceops(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceopsdiscoverytestserviceopstest(ap-south-1)", "object.region": "Global", "object.target": "serviceopsdiscoverytestserviceopstest(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceopstestbucket(ap-south-1)", "object.region": "Global", "object.target": "serviceopstestbucket(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-0c1ede17541c33a71", "object.ip": "**************", "object.name": "windows-wsus(ap-south-1)", "object.region": "ap-south-1", "object.target": "windows-wsus(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-015017f0d4b79fca2", "object.ip": "************", "object.name": "Sampleebs-env(ap-south-1)", "object.region": "ap-south-1", "object.target": "Sampleebs-env(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-05ed31c26565ddbd1", "object.ip": "*************", "object.name": "serviceops-saas(ap-south-1)", "object.region": "ap-south-1", "object.target": "serviceops-saas(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "**************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-033a217b9b307a8fe(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-033a217b9b307a8fe(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0537e607fc8a15359(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0537e607fc8a15359(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0549f35d3cfc85e27(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0549f35d3cfc85e27(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0ef1cdd875c16187e(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0ef1cdd875c16187e(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0a2978c982278b5ad(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0a2978c982278b5ad(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0da9c0644d6c8fd92(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0da9c0644d6c8fd92(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "devauroraitsm-instance-1(ap-south-1)", "object.region": "ap-south-1", "object.target": "devauroraitsm-instance-1(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata(ap-south-1)", "object.region": "ap-south-1", "object.target": "motadata(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceops-postgres(ap-south-1)", "object.region": "ap-south-1", "object.target": "serviceops-postgres(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.region": "ap-south-1", "object.target": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.type": "AWS Auto Scaling"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testlambda(ap-south-1)", "object.region": "ap-south-1", "object.target": "testlambda(ap-south-1)", "object.type": "AWS Lambda"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "aaalamda(us-east-1)", "object.region": "us-east-1", "object.target": "aaalamda(us-east-1)", "object.type": "AWS Lambda"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.make.model": "NETWORK", "object.name": "networkelb(ap-south-1)", "object.region": "ap-south-1", "object.target": "networkelb(ap-south-1)", "object.type": "AWS ELB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.make.model": "APPLICATION", "object.name": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "object.region": "ap-south-1", "object.target": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "object.type": "AWS ELB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata8-dynamoDB(ap-south-1)", "object.region": "ap-south-1", "object.target": "motadata8-dynamoDB(ap-south-1)", "object.type": "Amazon DynamoDB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testdb(us-west-1)", "object.region": "us-west-1", "object.target": "testdb(us-west-1)", "object.type": "Amazon DynamoDB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "sqs(ap-south-1)", "object.region": "ap-south-1", "object.target": "sqs(ap-south-1)", "object.type": "Amazon SQS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "qsq(us-east-1)", "object.region": "us-east-1", "object.target": "qsq(us-east-1)", "object.type": "Amazon SQS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "MyTopic(ap-south-1)", "object.region": "ap-south-1", "object.target": "MyTopic(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "TestTopic2(ap-south-1)", "object.region": "ap-south-1", "object.target": "TestTopic2(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "dynamodb(ap-south-1)", "object.region": "ap-south-1", "object.target": "dynamodb(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testsns(ap-south-1)", "object.region": "ap-south-1", "object.target": "testsns(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "MyTopic(us-east-1)", "object.region": "us-east-1", "object.target": "MyTopic(us-east-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "dynamodb(us-west-1)", "object.region": "us-west-1", "object.target": "dynamodb(us-west-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "Sampleebs-env(ap-south-1)", "object.region": "ap-south-1", "object.target": "Sampleebs-env(ap-south-1)", "object.type": "AWS Elastic Beanstalk"}], "plugin.id": "4", "status": "succeed"}], "discovery.discovered.objects": 41, "discovery.event.processors": [***************], "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203684410", "discovery.progress": 50, "discovery.status": "Not Run Yet", "discovery.total.objects": 0, "event.id": ***************, "event.topic": "remote.event.processor ", "event.type": "discovery", "id": ***************, "message": "Internet connection succeeded", "metric.plugin": "aws", "object.credential.profile": ***************, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.type": "AWS Cloud", "objects": [{"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "E1RVVPM08N16XT(Global)", "object.region": "Global", "object.target": "E1RVVPM08N16XT(Global)", "object.type": "Amazon CloudFront", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "awsplugintests3(ap-south-1)", "object.region": "Global", "object.target": "awsplugintests3(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "docs.motadata.com(ap-south-1)", "object.region": "Global", "object.target": "docs.motadata.com(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "elasticbeanstalk-ap-south-1-************(ap-south-1)", "object.region": "Global", "object.target": "elasticbeanstalk-ap-south-1-************(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "elasticbeanstalk-us-east-1-************(us-east-1)", "object.region": "Global", "object.target": "elasticbeanstalk-us-east-1-************(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "itsm-docs.motadata.com(ap-south-1)", "object.region": "Global", "object.target": "itsm-docs.motadata.com(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "mindarray(ap-south-1)", "object.region": "Global", "object.target": "mindarray(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata-common(ap-south-1)", "object.region": "Global", "object.target": "motadata-common(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata2016(us-east-1)", "object.region": "Global", "object.target": "motadata2016(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadataserviceops(us-east-1)", "object.region": "Global", "object.target": "motadataserviceops(us-east-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceopsdiscoverytestserviceopstest(ap-south-1)", "object.region": "Global", "object.target": "serviceopsdiscoverytestserviceopstest(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceopstestbucket(ap-south-1)", "object.region": "Global", "object.target": "serviceopstestbucket(ap-south-1)", "object.type": "Amazon S3"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-0c1ede17541c33a71", "object.ip": "**************", "object.name": "windows-wsus(ap-south-1)", "object.region": "ap-south-1", "object.target": "windows-wsus(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-015017f0d4b79fca2", "object.ip": "************", "object.name": "Sampleebs-env(ap-south-1)", "object.region": "ap-south-1", "object.target": "Sampleebs-env(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-05ed31c26565ddbd1", "object.ip": "*************", "object.name": "serviceops-saas(ap-south-1)", "object.region": "ap-south-1", "object.target": "serviceops-saas(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "**************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-033a217b9b307a8fe(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-033a217b9b307a8fe(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0537e607fc8a15359(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0537e607fc8a15359(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0549f35d3cfc85e27(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0549f35d3cfc85e27(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0ef1cdd875c16187e(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0ef1cdd875c16187e(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0a2978c982278b5ad(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0a2978c982278b5ad(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "vol-0da9c0644d6c8fd92(ap-south-1)", "object.region": "ap-south-1", "object.target": "vol-0da9c0644d6c8fd92(ap-south-1)", "object.type": "Amazon EBS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "devauroraitsm-instance-1(ap-south-1)", "object.region": "ap-south-1", "object.target": "devauroraitsm-instance-1(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata(ap-south-1)", "object.region": "ap-south-1", "object.target": "motadata(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "serviceops-postgres(ap-south-1)", "object.region": "ap-south-1", "object.target": "serviceops-postgres(ap-south-1)", "object.type": "Amazon RDS", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.region": "ap-south-1", "object.target": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "object.type": "AWS Auto Scaling"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testlambda(ap-south-1)", "object.region": "ap-south-1", "object.target": "testlambda(ap-south-1)", "object.type": "AWS Lambda"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "aaalamda(us-east-1)", "object.region": "us-east-1", "object.target": "aaalamda(us-east-1)", "object.type": "AWS Lambda"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.make.model": "NETWORK", "object.name": "networkelb(ap-south-1)", "object.region": "ap-south-1", "object.target": "networkelb(ap-south-1)", "object.type": "AWS ELB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.make.model": "APPLICATION", "object.name": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "object.region": "ap-south-1", "object.target": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "object.type": "AWS ELB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "motadata8-dynamoDB(ap-south-1)", "object.region": "ap-south-1", "object.target": "motadata8-dynamoDB(ap-south-1)", "object.type": "Amazon DynamoDB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testdb(us-west-1)", "object.region": "us-west-1", "object.target": "testdb(us-west-1)", "object.type": "Amazon DynamoDB", "status": "Up"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "sqs(ap-south-1)", "object.region": "ap-south-1", "object.target": "sqs(ap-south-1)", "object.type": "Amazon SQS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "qsq(us-east-1)", "object.region": "us-east-1", "object.target": "qsq(us-east-1)", "object.type": "Amazon SQS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "MyTopic(ap-south-1)", "object.region": "ap-south-1", "object.target": "MyTopic(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "TestTopic2(ap-south-1)", "object.region": "ap-south-1", "object.target": "TestTopic2(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "dynamodb(ap-south-1)", "object.region": "ap-south-1", "object.target": "dynamodb(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "testsns(ap-south-1)", "object.region": "ap-south-1", "object.target": "testsns(ap-south-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "MyTopic(us-east-1)", "object.region": "us-east-1", "object.target": "MyTopic(us-east-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "dynamodb(us-west-1)", "object.region": "us-west-1", "object.target": "dynamodb(us-west-1)", "object.type": "Amazon SNS"}, {"object.account.id": "************", "object.context": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "ap-south-1,us-east-1", "AWS RDS": "ap-south-1", "AWS SNS": "ap-south-1,us-east-1,us-west-1", "AWS SQS": "ap-south-1,us-east-1"}}}, "object.name": "Sampleebs-env(ap-south-1)", "object.region": "ap-south-1", "object.target": "Sampleebs-env(ap-south-1)", "object.type": "AWS Elastic Beanstalk"}], "plugin.engine": "go", "plugin.id": 4, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "status": "succeed", "timeout": 120}}