[{"_type": "0", "id": 10000000004123, "visualization.name": "Mongo DB Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.used.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.available.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.used.connections"}, {"type": "metric", "data.point": "mongodb.available.connections"}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "mongodb.used.connections.last"}, {"label": "Available", "value": "mongodb.available.connections.last"}]}, "style": {"icon": {"name": "active-connections"}, "color.data.point": "mongodb.available.connections"}}}}, {"id": 10000000004124, "_type": "0", "visualization.name": "Total Queries", "visualization.description": "Mongo Total Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.operations", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.operations"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "mongodb.operations", "font.size": "medium", "icon": {"name": "file-search", "placement": "prefix"}}, "header": {"title": "Total Queries", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mongodb.operations.last"}]}}}}, {"_type": "0", "id": 10000000004125, "visualization.name": "Request", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.insert.operations", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.update.operations", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.query.operations", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.insert.operations"}, {"type": "metric", "data.point": "mongodb.update.operations"}, {"type": "metric", "data.point": "mongodb.query.operations"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "clock", "placement": "prefix"}}, "header": {"title": "Request", "style": {"font.size": "medium"}, "data.points": [{"label": "Queries", "value": "mongodb.query.operations.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Insert", "value": "mongodb.insert.operations.last"}, {"label": "Update", "value": "mongodb.update.operations.last"}]}}}}, {"id": 10000000004126, "_type": "0", "visualization.name": "Commits", "visualization.description": "MongoDB Commits", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.commits"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "mongodb.commits", "font.size": "medium", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Commits", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mongodb.commits.last"}]}}}}, {"id": 10000000004127, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON> <PERSON> ", "visualization.description": "MongoDB Buffer <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.cache.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.cache.size.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "mongodb.cache.size.bytes", "font.size": "medium", "icon": {"name": "buffer", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mongodb.cache.size.bytes.last"}]}}}}, {"_type": "0", "id": 10000000004128, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.read.response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.write.response.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mongodb.read.response.time.ms"}, {"type": "metric", "data.point": "mongodb.write.response.time.ms"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "mongodb.read.response.time.ms.last"}, {"label": "Write", "value": "mongodb.write.response.time.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "mongodb.read.response.time.ms.last"}}}}, {"id": 10000000004129, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "Mongo DB Buffer Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.cache.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004130, "_type": "0", "visualization.name": "Memory Details", "visualization.description": "Mongo DB Memory Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.virtual.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.resident.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004131, "_type": "0", "visualization.name": "Latency", "visualization.description": "Mongo DB Read and Write Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.read.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004132, "_type": "0", "visualization.name": "Network Traffic", "visualization.description": "Mongo DB Network Traffic", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.network.in.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.network.out.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004133, "_type": "0", "visualization.name": "Global Lock Details", "visualization.description": "Mongo DB Global Lock Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.global.read.lock", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.global.write.lock", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004134, "_type": "0", "visualization.name": "Cursor Timeout", "visualization.description": "Mongo DB Cursor Timeout", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.timed.out.cursors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004135, "_type": "0", "visualization.name": "Page Faults", "visualization.description": "Mongo DB <PERSON>s", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.page.faults", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004136, "_type": "0", "visualization.name": "Query Details", "visualization.description": "Mongo DB Query Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.insert.operations", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.delete.operations", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.update.operations", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.getmore.operations", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004137, "_type": "0", "visualization.name": "Request Details", "visualization.description": "Mongo DB Request Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.insert.operations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.update.operations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.delete.operations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.getmore.operations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004138, "_type": "0", "visualization.name": "Operation Details", "visualization.description": "Mongo DB Operation Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.db.operation.scan", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.db.write.conflicts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004139, "_type": "0", "visualization.name": "Assertion Error", "visualization.description": "Mongo DB Assertion Error", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.assert.regular", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.assert.warning", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.assert.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.assert.user", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004140, "_type": "0", "visualization.name": "Database Lock Details", "visualization.description": "Mongo DB Database Lock Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.db.read.lock", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.db.write.lock", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004141, "_type": "0", "visualization.name": "Lock Active Client Details", "visualization.description": "Mongo DB Lock Active Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.global.lock.active.clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.global.read.lock.active.clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.global.write.lock.active.clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004142, "_type": "0", "visualization.name": "Current Connections", "visualization.description": "Mongo DB Current Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.used.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.available.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004143, "_type": "0", "visualization.name": "Distinct Requests", "visualization.description": "Mongo DB Distinct Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.network.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004152, "_type": "0", "visualization.name": "Lock Current Queue Details", "visualization.description": "Lock Current Queue Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.global.lock.queue", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.global.read.lock.queue", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.global.write.lock.queue", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004144, "_type": "0", "visualization.name": "<PERSON> <PERSON><PERSON>", "visualization.description": "Mongo DB Dirty <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.dirty.cache.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004145, "_type": "0", "visualization.name": "<PERSON><PERSON> and Write Size", "visualization.description": "Mongo DB Cache Read and Write Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.cache.read.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.cache.write.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004146, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "Mongo DB Cache <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.cache.configured.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.cache.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004147, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "Mongo DB <PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.pages.read", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.pages.write", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.pages.evicted", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.pages.requested", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004148, "_type": "0", "visualization.name": "Response Time", "visualization.description": "Mongo DB Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.read.response.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.write.response.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000004149, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000004150, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000004151, "visualization.name": "Database Statistics", "visualization.category": "Grid", "visualization.type": "Database Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.database~collections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.database~objects", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.database~data.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.database~indexes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.database~index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.database~avg.object.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Database Statistics", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "mongodb.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no"}, {"name": "mongodb.database~collections.last", "title": "Collections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "mongodb.database~objects.last", "title": "Objects", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "mongodb.database~data.size.bytes.last", "title": "Data Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "mongodb.database~indexes.last", "title": "Indexes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "mongodb.database~index.size.bytes.last", "title": "Index Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "mongodb.database~avg.object.size.bytes.last", "title": "Avg Object Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"id": 10000000004153, "_type": "0", "visualization.name": "Document Details", "visualization.description": "Document Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.returned.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.matched.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.deleted.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.inserted.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000004154, "_type": "0", "visualization.name": "Query Execution Details", "visualization.description": "Query Execution Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.examined.index", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.examined.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.colscan.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000004155, "visualization.name": "Collection Statistics", "visualization.category": "Grid", "visualization.type": "Collection Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.collection~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~documents", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~data.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~indexes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~avg.document.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~capacity.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~storage.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mongodb.collection~database", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Collection Statistics", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "mongodb.collection", "title": "Collection", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 1}, {"name": "mongodb.collection~name.last", "title": "Collection Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "mongodb.collection~database.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "mongodb.collection~total.size.bytes.last", "title": "Collection Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "mongodb.collection~documents.last", "title": "Documents", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "mongodb.collection~avg.document.size.bytes.last", "title": "Avg Documents Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "mongodb.collection~data.size.bytes.last", "title": "DATA SIZE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "mongodb.collection~storage.size.bytes.last", "title": "STORAGE SIZE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "mongodb.collection~indexes.last", "title": "Indexes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "mongodb.collection~index.size.bytes.last", "title": "Index Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no"}]}}}, {"_type": "0", "id": 10000000004156, "visualization.name": "Top Collections By Size", "visualization.description": "Top Collections By Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["mongodb.collection"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.collection~capacity.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "mongodb.collection~capacity.size.bytes.avg"}}}}, {"_type": "0", "id": 10000000004157, "visualization.name": "Top Collections By Index Size", "visualization.description": "Top Collections By Index Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["mongodb.collection"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.collection~index.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "mongodb.collection~index.size.bytes.avg"}}}}, {"_type": "0", "id": 10000000004158, "visualization.name": "Top Collections By Storage Size", "visualization.description": "Top Collections By Storage Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.result.by": ["mongodb.collection"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mongodb.collection~storage.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "mongodb.collection~storage.size.bytes.avg"}}}}]