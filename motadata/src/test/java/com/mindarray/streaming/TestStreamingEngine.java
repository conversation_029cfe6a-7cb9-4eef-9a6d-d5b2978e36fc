/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.streaming;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventengine.TestRemoteEventForwarder;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.SNMPTrapListenerConfigStore;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.AsyncResult;
import io.vertx.core.datagram.DatagramSocket;
import io.vertx.core.datagram.DatagramSocketOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.snmp4j.PDU;
import org.snmp4j.PDUv1;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.IpAddress;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.SNMP_TRAP_LISTENER_API_ENDPOINT;
import static com.mindarray.api.SNMPTrapProfile.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.SNMP_TRAP_VERSION;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static com.mindarray.notification.Notification.NotificationType.SNMP_TRAP;
import static com.mindarray.streaming.StreamingEngine.STREAMING_TYPE;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public
class TestStreamingEngine
{
    private static final DatagramSocket SOCKET = Bootstrap.vertx().createDatagramSocket(new DatagramSocketOptions());

    private static final Logger LOGGER = new Logger(TestRemoteEventForwarder.class, GlobalConstants.MOTADATA_EVENT_BUS, "Streaming Engine Test");

    private static final JsonObject CONTEXT4 = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testAutoClearTimerProfile")
            .put(SNMP_TRAP_PROFILE_OID, ".*******.4.1.644.********23")
            .put(SNMP_TRAP_PROFILE_DROP_STATUS, NO)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_STATUS, YES)
            .put(SNMP_TRAP_PROFILE_AUTO_CLEAR_TIMER, 5)
            .put(SNMP_TRAP_PROFILE_SEVERITY, "Clear")
            .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.START_LOG_TAIL.name()));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.START_TRAP_TAIL.name()));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_START, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_SOURCE, new JsonArray().add("localhost").add("127.0.0.1"))
                .put("keyword", "motadata").put("match.condition", "any"));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_START, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(FILTER, new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED)));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_START, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_ENGINE_STATS.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(ENTITIES, objects));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_START, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.TRAP_TAIL.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(ENTITIES, objects));

        testContext.completeNow();

    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_SOURCE, new JsonArray().add("localhost").add("127.0.0.1"))
                .put("keyword", "motadata").put("match.condition", "any"));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(ENTITIES, new JsonArray().add(1234567891015L)));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_ENGINE_STATS.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(ENTITIES, new JsonArray().add(1234567891015L)));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, new JsonObject()
                .put(STREAMING_TYPE, StreamingEngine.StreamingType.TRAP_TAIL.getName())
                .put(EventBusConstants.UI_EVENT_UUID, "1234567")
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(ENTITIES, new JsonArray().add(1234567891015L)));

        if (SOCKET != null)
        {
            SOCKET.close();
        }
        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testStreamLogTail(VertxTestContext testContext)
    {
        var records = new JsonArray();

        records.add("Sep 16 06:33:03 motadata systemd[1]: Starting Daily apt upgrade and clean activities..");

        records.add("Sep 16 06:33:11 motadata systemd[1]: Started Daily apt upgrade and clean activities.");

        records.add("Sep 16 07:10:55 motadata snapd[11149]: autorefresh.go:540: auto-refresh: all snaps are up-to-date");

        records.add("Sep 16 09:17:01 motadata CRON[22600]: (root) CMD (   cd / && run-parts --report /etc/cron.hourly)");

        records.add("Sep 16 09:31:18 motadata systemd[22691]: Listening on GnuPG network certificate management daemon.");

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
            {
                try
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (!context.isEmpty() && context.containsKey(EVENT) && !context.getString(EVENT).isEmpty() && context.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.LOG_TAIL.getName()))
                    {
                        Assertions.assertTrue(records.contains(context.getString(EVENT)));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        for (var i = 0; i < 30; i++)
        {

            records.forEach(entry -> SOCKET.send(CommonUtil.getString(entry), MotadataConfigUtil.getUDPPort(), "localhost", AsyncResult::succeeded));
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testStreamTrapTail(VertxTestContext testContext, TestInfo testInfo)
    {

        PDU pdu;
        var pdu1 = new PDUv1();
        pdu1.setType(PDU.V1TRAP);
        pdu1.setAgentAddress(new IpAddress("localhost"));     //SET THIS. This is the sender address
        pdu1.setSpecificTrap(5);
        pdu1.setGenericTrap(0);
        pdu = pdu1;
        pdu.add(new VariableBinding(SnmpConstants.snmpTrapOID, new OID(CONTEXT4.getString(SNMP_TRAP_PROFILE_OID))));
        pdu.add(new VariableBinding(SnmpConstants.snmpTrapAddress, new IpAddress("localhost")));

        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
            {
                try
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (!context.isEmpty() && context.containsKey(SNMPTrapProcessor.SNMP_TRAP_OID) && context.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.TRAP_TAIL.getName()))
                    {
                        Assertions.assertTrue(context.containsKey(SNMPTrapProcessor.SNMP_TRAP_MESSAGE));

                        Assertions.assertTrue(context.containsKey(SNMPTrapProcessor.SNMP_TRAP_OID));

                        Assertions.assertEquals(".*******.4.1.644.********23", context.getString(SNMPTrapProcessor.SNMP_TRAP_OID));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        var payload = new JsonObject("{\"snmp.trap.listener.v1.v2.status\":\"yes\",\"snmp.trap.listener.v3.status\":\"no\",\"snmp.trap.listener.v1.v2.port\":1620,\"snmp.trap.listener.v3.port\":1630,\"snmp.community\":\"public\"}");

        TestAPIUtil.put(SNMP_TRAP_LISTENER_API_ENDPOINT + "/" + SNMPTrapListenerConfigStore.getStore().getItem().getLong(ID), payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapListenerConfigStore.getStore(), payload, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "SNMP Trap Listener"), LOGGER, testInfo.getTestMethod().get().getName());

            for (var index = 0; index < 5; index++)
            {
                TestUtil.vertx().eventBus().send(EVENT_NOTIFICATION, new JsonObject().put(EVENT, CodecUtil.toBytes(pdu)).put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                        .put(TARGET, "localhost/" + 1620)
                        .put(SNMP_TRAP_VERSION, SnmpConstants.version1)
                        .put(NOTIFICATION_TYPE, SNMP_TRAP.getName())
                        .put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()));
            }
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testStreamTaskManager(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
            {
                try
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (!context.isEmpty() && context.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.EVENT_TRACKER.getName()))
                    {
                        Assertions.assertTrue(context.containsKey(EVENT_ID));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        for (var i = 0; i < 10; i++)
        {

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_TRACKER.getName()).put(EVENT_CONTEXT, new JsonObject("{\"event.state\":\"completed\",\"object.system.oid\": \".*******.*******.222\", \"object.ip\": \"*************\", \"credential.profile.protocol\": \"SNMP V1/V2c\", \"user.name\": \"admin\", \"object.make.model\": \"Cisco 7000 Series\", \"object.discovery.method\": \"REMOTE\", \"object.state\": \"ENABLE\", \"plugin.engine.request\": \"Network Metric\", \"timeout\": 60, \"interface.discovery\": \"yes\", \"object.host\": \"R4_Test1.cisco.com\", \"object.snmp.device.catalog\": 397490073135096, \"snmp.check.retries\": 0, \"credential.profile.name\": \"Default SNMP\", \"remote.address\": \"0:0:0:0:0:0:0:1\", \"id\": 397490073180890, \"snmp.version\": \"v2c\", \"ping.check.status\": \"yes\", \"topology.plugin.discovery\": \"no\", \"object.id\": 26, \"object.type\": \"Router\", \"object.category\": \"Network\", \"_type\": \"1\", \"object.vendor\": \"Cisco Systems\", \"object.name\": \"R4_Test1.cisco.com\", \"metric.plugin\": \"snmp\", \"object.credential.profile\": 10000000000001, \"port\": 161, \"object.creation.time\": \"2022/12/26 12:00:42\", \"event.type\": \"plugin.engine\", \"object.user.tags\": [], \"ui.event.uuid\": \"917ce67b-bf61-4def-ab48-f21edfa4ece7\", \"object.target\": \"*************\", \"object.groups\": [10000000000002], \"object.business.hour.profile\": 10000000000001, \"session-id\": \"667f30aa-bfd6-446b-bce9-a7dda0eb95cb\", \"event.id\": 397490073113464, \"plugin.engine\": \"go\"}")));

        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSteamEventEngineStats(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body() != null && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST))
            {
                try
                {
                    var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (!context.isEmpty() && context.getString(STREAMING_TYPE).equalsIgnoreCase(StreamingEngine.StreamingType.EVENT_ENGINE_STATS.getName()))
                    {
                        Assertions.assertTrue(context.containsKey("event.rate"));

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            }
        });

        for (var i = 0; i < 10; i++)
        {

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, (new JsonObject()
                    .put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_ENGINE_STATS.getName()).put(
                            EVENT_CONTEXT, new JsonObject().put("event.rate", 10)
                                    .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                    .put(ENGINE_TYPE, EVENT_LOG))));
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testClearEvents(VertxTestContext testContext)
    {
        Bootstrap.undeployVerticle(StreamingEngine.class.getSimpleName()).onComplete(undeploy ->
        {
            if (undeploy.succeeded())
            {
                try
                {
                    LOGGER.info("Undeployed Streaming Engine Successfully");

                    var devMode = MotadataConfigUtil.class.getDeclaredField("devMode");

                    devMode.setAccessible(true);

                    devMode.set(devMode, false);

                    Assertions.assertFalse(MotadataConfigUtil.devMode());

                    Bootstrap.startEngine(new StreamingEngine(), StreamingEngine.class.getSimpleName(), null);

                    testContext.awaitCompletion(7, TimeUnit.SECONDS);

                    devMode.set(devMode, true);

                    testContext.completeNow();
                }
                catch (Exception ignore)
                {
                    testContext.failNow(undeploy.cause());
                }
            }
        });
    }
}
