{"52.167.234.113": {"result": {"18": {"metadata.fields": {"Environment": "Test", "Tag": "Demo"}}, "azure.location": "southcentralus", "azure.provisioning.state": "Succeeded", "azure.sku.name": "18.04-LTS", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm.computer.name": "ubuntu-linux2", "azure.vm.cpu.consumed.credits": 0, "azure.vm.cpu.percent": 0.81, "azure.vm.cpu.remaining.credits": 0, "azure.vm.disk": [{"azure.vm.disk": "datadisk", "azure.vm.disk.size.bytes": **********}], "azure.vm.disk.io.bytes.per.sec": 0, "azure.vm.disk.io.read.bytes": 16382.47, "azure.vm.disk.io.read.bytes.per.sec": 0, "azure.vm.disk.io.reads.per.sec": 0.01, "azure.vm.disk.io.write.bytes": 995482.43, "azure.vm.disk.io.write.bytes.per.sec": 0, "azure.vm.disk.io.writes.per.sec": 1.08, "azure.vm.id": "06e89917-b696-4b47-9b11-19a3fe8d8354", "azure.vm.inbound.flows": 7, "azure.vm.memory.free.percent": 43, "azure.vm.memory.page.reads.per.sec": 0, "azure.vm.memory.page.writes.per.sec": 16.25, "azure.vm.memory.pages.per.sec": 16.25, "azure.vm.memory.used.bytes": 245628928, "azure.vm.memory.used.percent": 57, "azure.vm.network.in.bytes.rate": 216592, "azure.vm.network.out.bytes.rate": 230954, "azure.vm.network.traffic.bytes.rate": 447546, "azure.vm.os.disk": "ubuntu-linux2_disk1_32cd06c927df45699d73f7557ababf00", "azure.vm.os.disk.depth": 0, "azure.vm.os.disk.io.cache.read.hit.percent": 100, "azure.vm.os.disk.io.cache.read.miss.percent": 0, "azure.vm.os.type": "Linux", "azure.vm.outbound.flows": 7, "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "************", "azure.vm.publisher": "Canonical", "azure.vm.resource.id": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Compute/virtualMachines/ubuntu-linux2", "azure.vm.resource.name": "ubuntu-linux2", "azure.vm.size": "Standard_B1ls", "azure.vm.storage.name": "motadatafreetierdiag678", "azure.vm.storage.uri": "https://motadatafreetierdiag678.blob.core.windows.net/", "azure.vm.swap.memory.free.bytes": 0, "azure.vm.swap.memory.free.percent": 0, "azure.vm.swap.memory.used.bytes": 0, "azure.vm.swap.memory.used.percent": 0, "status": "Up"}, "status": "succeed"}, "************": {"result": {"18": {"metadata.fields": {"Environment": "Test", "Tag": "Demo"}}, "azure.location": "southcentralus", "azure.provisioning.state": "Succeeded", "azure.sku.name": "18.04-LTS", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm.computer.name": "ubuntu-linux2", "azure.vm.cpu.consumed.credits": 0, "azure.vm.cpu.percent": 0.81, "azure.vm.cpu.remaining.credits": 0, "azure.vm.disk": [{"azure.vm.disk": "datadisk", "azure.vm.disk.size.bytes": **********}], "azure.vm.disk.io.bytes.per.sec": 0, "azure.vm.disk.io.read.bytes": 16382.47, "azure.vm.disk.io.read.bytes.per.sec": 0, "azure.vm.disk.io.reads.per.sec": 0.01, "azure.vm.disk.io.write.bytes": 995482.43, "azure.vm.disk.io.write.bytes.per.sec": 0, "azure.vm.disk.io.writes.per.sec": 1.08, "azure.vm.id": "06e89917-b696-4b47-9b11-19a3fe8d8354", "azure.vm.inbound.flows": 7, "azure.vm.memory.free.percent": 43, "azure.vm.memory.page.reads.per.sec": 0, "azure.vm.memory.page.writes.per.sec": 16.25, "azure.vm.memory.pages.per.sec": 16.25, "azure.vm.memory.used.bytes": 245628928, "azure.vm.memory.used.percent": 57, "azure.vm.network.in.bytes.rate": 216592, "azure.vm.network.out.bytes.rate": 230954, "azure.vm.network.traffic.bytes.rate": 447546, "azure.vm.os.disk": "ubuntu-linux2_disk1_32cd06c927df45699d73f7557ababf00", "azure.vm.os.disk.depth": 0, "azure.vm.os.disk.io.cache.read.hit.percent": 100, "azure.vm.os.disk.io.cache.read.miss.percent": 0, "azure.vm.os.type": "Linux", "azure.vm.outbound.flows": 7, "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "************", "azure.vm.publisher": "Canonical", "azure.vm.resource.id": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Compute/virtualMachines/ubuntu-linux2", "azure.vm.resource.name": "ubuntu-linux2", "azure.vm.size": "Standard_B1ls", "azure.vm.storage.name": "motadatafreetierdiag678", "azure.vm.storage.uri": "https://motadatafreetierdiag678.blob.core.windows.net/", "azure.vm.swap.memory.free.bytes": 0, "azure.vm.swap.memory.free.percent": 0, "azure.vm.swap.memory.used.bytes": 0, "azure.vm.swap.memory.used.percent": 0, "status": "Up"}, "status": "succeed"}, "*************": {"result": {"18": {"metadata.fields": {"Environment": "Test", "Tag": "Demo"}}, "azure.location": "southcentralus", "azure.provisioning.state": "Succeeded", "azure.sku.name": "18.04-LTS", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm.computer.name": "ubuntu-linux2", "azure.vm.cpu.consumed.credits": 0, "azure.vm.cpu.percent": 0.81, "azure.vm.cpu.remaining.credits": 0, "azure.vm.disk": [{"azure.vm.disk": "datadisk", "azure.vm.disk.size.bytes": **********}], "azure.vm.disk.io.bytes.per.sec": 0, "azure.vm.disk.io.read.bytes": 16382.47, "azure.vm.disk.io.read.bytes.per.sec": 0, "azure.vm.disk.io.reads.per.sec": 0.01, "azure.vm.disk.io.write.bytes": 995482.43, "azure.vm.disk.io.write.bytes.per.sec": 0, "azure.vm.disk.io.writes.per.sec": 1.08, "azure.vm.id": "06e89917-b696-4b47-9b11-19a3fe8d8354", "azure.vm.inbound.flows": 7, "azure.vm.memory.free.percent": 43, "azure.vm.memory.page.reads.per.sec": 0, "azure.vm.memory.page.writes.per.sec": 16.25, "azure.vm.memory.pages.per.sec": 16.25, "azure.vm.memory.used.bytes": 245628928, "azure.vm.memory.used.percent": 57, "azure.vm.network.in.bytes.rate": 216592, "azure.vm.network.out.bytes.rate": 230954, "azure.vm.network.traffic.bytes.rate": 447546, "azure.vm.os.disk": "ubuntu-linux2_disk1_32cd06c927df45699d73f7557ababf00", "azure.vm.os.disk.depth": 0, "azure.vm.os.disk.io.cache.read.hit.percent": 100, "azure.vm.os.disk.io.cache.read.miss.percent": 0, "azure.vm.os.type": "Linux", "azure.vm.outbound.flows": 7, "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "************", "azure.vm.publisher": "Canonical", "azure.vm.resource.id": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Compute/virtualMachines/ubuntu-linux2", "azure.vm.resource.name": "ubuntu-linux2", "azure.vm.size": "Standard_B1ls", "azure.vm.storage.name": "motadatafreetierdiag678", "azure.vm.storage.uri": "https://motadatafreetierdiag678.blob.core.windows.net/", "azure.vm.swap.memory.free.bytes": 0, "azure.vm.swap.memory.free.percent": 0, "azure.vm.swap.memory.used.bytes": 0, "azure.vm.swap.memory.used.percent": 0, "status": "Up"}, "status": "succeed"}}