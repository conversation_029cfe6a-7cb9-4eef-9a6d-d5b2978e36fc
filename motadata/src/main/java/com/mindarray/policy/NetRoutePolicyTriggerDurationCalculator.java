/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 *  June-5-2025     Pruthviraj Jadeja       Added Support for ticks in Netroute.
 */

package com.mindarray.policy;

/**
 * NetRoutePolicyTriggerDurationCalculator tracks and calculates the duration of network route policy triggers and state changes.
 * <p>
 * This class:
 * 1. Maintains information about how long network route policies have been in specific states
 * 2. Tracks policy flaps (state changes) and their durations
 * 3. Persists policy state information for high availability and recovery
 * 4. Handles policy acknowledgments and comments
 * 5. Provides duration data for visualization and reporting
 * <p>
 * The calculator runs as a Vert.x verticle and communicates with other components
 * through the event bus to update and retrieve policy duration information.
 */

import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.MetricPolicy;
import com.mindarray.api.NetRoute;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.store.NetRouteConfigStore;
import com.mindarray.store.NetRoutePolicyConfigStore;
import com.mindarray.store.NetRoutePolicyFlapDurationCacheStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_TIMESTAMP;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_VALUE;

public class NetRoutePolicyTriggerDurationCalculator extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(NetRoutePolicyTriggerDurationCalculator.class, GlobalConstants.MOTADATA_POLICY, "NetRoute Policy Trigger Duration Calculator");
    private static final int POLICY_FLAP_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getPolicyFlapFlushTimerSeconds();
    private final JsonObject objectPolicyStatuses = new JsonObject();
    private final JsonObject policyTriggerTicks = new JsonObject();
    private final JsonObject ticks = new JsonObject();
    private final AtomicBoolean policyTriggerTickDirty = new AtomicBoolean();
    private final AtomicBoolean policyStatusDirty = new AtomicBoolean();
    private boolean updated = false;

    /**
     * Starts the NetRoutePolicyTriggerDurationCalculator verticle.
     * This method:
     * 1. Loads persisted policy trigger ticks and flap durations from backup files
     * 2. Sets up event bus handlers for duration calculation, queries, and acknowledgments
     * 3. Initializes periodic timers for flushing data to disk
     * 4. Registers with the high availability system for cache synchronization
     *
     * @param promise A promise that will be completed when startup is finished or failed if an error occurs
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            var timerIntervalSeconds = new AtomicInteger(POLICY_FLAP_FLUSH_TIMER_SECONDS);

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes())));

                    policyTriggerTicks.mergeIn(new JsonObject(context.getString(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName())));

                    LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_FLAP_DURATIONS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap().forEach((key, value) -> objectPolicyStatuses.put(key, JsonObject.mapFrom(value)));

                    LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.NETROUTE_POLICY_FLAP_DURATIONS));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_SEVERITY_DURATION_CALCULATE, message ->
            {
                try
                {

                    if (!message.body().isEmpty())
                    {
                        var event = message.body();

                        if (event.getString(PolicyEngineConstants.POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()))
                        {
                            var key = event.getLong(ENTITY_ID) + SEPARATOR + event.getLong(ID) + SEPARATOR + (event.getString(GlobalConstants.METRIC));

                            if (objectPolicyStatuses.containsKey(key))
                            {
                                if (!objectPolicyStatuses.getJsonObject(key).getString(SEVERITY).equalsIgnoreCase(event.getString(SEVERITY)))
                                {
                                    var previousFlapTimeStamp = objectPolicyStatuses.getJsonObject(key).getLong(PREVIOUS_FLAP_TIMESTAMP);

                                    var flapDiffSeconds = event.getLong(EVENT_TIMESTAMP) - previousFlapTimeStamp;

                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("key : %s , previous time : %s , previous severity : %s , current time : %s current severity : %s ", key, previousFlapTimeStamp, objectPolicyStatuses.getJsonObject(key).getString(SEVERITY), event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY)));
                                    }

                                    send(objectPolicyStatuses.getJsonObject(key).put(DURATION, flapDiffSeconds));

                                    objectPolicyStatuses.getJsonObject(key).mergeIn(event).put(PolicyEngineConstants.POLICY_NOTE, EMPTY_VALUE).put(PREVIOUS_FLAP_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(PREVIOUS_FLAP_VALUE, event.getValue(VALUE)).remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);

                                    NetRoutePolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), true);
                                }
                                else
                                {
                                    objectPolicyStatuses.getJsonObject(key).mergeIn(event);
                                }
                            }
                            else
                            {
                                objectPolicyStatuses.put(key, event.put(DURATION, 0).put(PolicyEngineConstants.POLICY_NOTE, EMPTY_VALUE).put(PREVIOUS_FLAP_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(PREVIOUS_FLAP_VALUE, event.getValue(VALUE)));

                                NetRoutePolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), true);
                            }

                            if (!policyTriggerTicks.containsKey(key))
                            {
                                updated = true;

                                policyTriggerTicks.put(key, System.currentTimeMillis());
                            }

                            event.put(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, policyTriggerTicks.getLong(key));
                        }
                        else
                        {
                            var key = CommonUtil.getString(event.getLong(ID)) + SEPARATOR + event.getLong(ENTITY_ID);

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("updating duration for key : %s ",key));
                            }

                            NetRoutePolicyFlapDurationCacheStore.getStore().updateTriggerTicks(key, event.getLong(EVENT_TIMESTAMP));

                            updated = true;
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY, message -> message.reply(new JsonObject().mergeIn(objectPolicyStatuses)));

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_TRIGGER_TICK_QUERY, message ->
            {
                var event = message.body();

                if (event.getString(PolicyEngineConstants.POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()))
                {
                    message.reply(new JsonObject().mergeIn(policyTriggerTicks));
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_ACKNOWLEDGE, message ->
            {
                var event = message.body();

                LOGGER.info(String.format("event for policy acknowledge : %s", event.encode()));

                var policy = NetRoutePolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID), false);

                var key = event.getLong(NetRoute.NETROUTE_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC));

                var context = new JsonObject().mergeIn(event).put(PolicyEngineConstants.POLICY_ACKNOWLEDGE_TIME, DateTimeUtil.currentMilliSeconds());

                context.remove(NetRoute.NETROUTE_ID);

                context.remove(PolicyEngineConstants.POLICY_ID);

                if (context.getString(ACKNOWLEDGED).equalsIgnoreCase(YES))
                {
                    objectPolicyStatuses.getJsonObject(key).put(PolicyEngineConstants.POLICY_ACKNOWLEDGE, context.encode());

                    NetRoutePolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), false);
                }
                else
                {
                    objectPolicyStatuses.getJsonObject(key).remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);

                    NetRoutePolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), false);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_NETROUTE_POLICY_COMMENT_UPDATE, message ->
            {
                var event = message.body();

                LOGGER.info(String.format("event for policy comment : %s", event.encode()));

                var policy = NetRoutePolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID), false);

                var key = event.getLong(NetRoute.NETROUTE_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC));

                objectPolicyStatuses.getJsonObject(key).put(PolicyEngineConstants.POLICY_NOTE, event.getString(PolicyEngineConstants.POLICY_NOTE));
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                if (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.DELETE_NETROUTE)
                {
                    objectPolicyStatuses.getMap().keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));
                }
                else if (ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                {
                    if (policyTriggerTickDirty.get())
                    {
                        policyTriggerTickDirty.set(false);

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS).put(STATUS, STATUS_SUCCEED).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS)));
                    }

                    if (policyStatusDirty.get())
                    {
                        policyStatusDirty.set(false);

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, PolicyEngineConstants.NETROUTE_POLICY_FLAP_DURATIONS).put(STATUS, STATUS_SUCCEED).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_FLAP_DURATIONS)));
                    }
                }
            }).exceptionHandler(LOGGER::error);

            vertx.setPeriodic(10 * 1000L, id -> write());

            vertx.setPeriodic(30 * 1000L, id ->
            {
                if (updated)
                {
                    policyTriggerTickDirty.set(true);

                    updated = false;

                    vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_TRIGGER_TICKS,
                            Buffer.buffer(CodecUtil.compress(new JsonObject()
                                    .put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName(), policyTriggerTicks.encode())
                                    .put(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName(), JsonObject.mapFrom(NetRoutePolicyFlapDurationCacheStore.getStore().getTriggerTicks()).encode()).encode().getBytes())));
                }

                timerIntervalSeconds.set(timerIntervalSeconds.get() - 30);

                if (timerIntervalSeconds.get() <= 0 && !objectPolicyStatuses.isEmpty())
                {
                    policyStatusDirty.set(true);

                    vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.NETROUTE_POLICY_FLAP_DURATIONS,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(objectPolicyStatuses).encode().getBytes())));

                    timerIntervalSeconds.set(POLICY_FLAP_FLUSH_TIMER_SECONDS);
                }
            });

            NetRoutePolicyFlapDurationCacheStore.getStore().initStore();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    /**
     * Updates policy durations by incrementing the duration counters.
     * This method is called periodically to:
     * 1. Increment the duration for all active policy states
     * 2. Clean up entries for network routes that no longer exist
     * <p>
     * The duration is incremented by 10 seconds each time this method is called.
     */
    private void write()
    {
        for (var entry : objectPolicyStatuses.getMap().entrySet())
        {
            try
            {
                var value = (JsonObject) entry.getValue();

                var id = value.getLong(APIConstants.ENTITY_ID);

                var item = NetRouteConfigStore.getStore().getItem(id, false);

                if (item != null)
                {
                    value.put(DURATION, value.getInteger(DURATION, 0) + 10);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }
    }

    /**
     * Stops the NetRoutePolicyTriggerDurationCalculator verticle.
     * This method ensures that all policy state information is persisted to disk
     * before the verticle is undeployed, preserving the data for recovery.
     *
     * @param promise A promise that will be completed when shutdown is finished
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        LOGGER.info("Undeploying verticle NetRouteTriggerDurationCalculator...");

        if (!objectPolicyStatuses.isEmpty())
        {
            vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(objectPolicyStatuses).encode().getBytes())));
        }

        promise.complete();
    }

    /**
     * Sends policy flap information to the datastore.
     * This method:
     * 1. Creates a buffer with policy flap data including timestamps, durations, and state information
     * 2. Formats the data for storage in the event datastore
     * 3. Resets the duration counter after recording the flap
     *
     * @param value The policy flap information to be sent to the datastore
     */
    private void send(JsonObject value)
    {
        try
        {
            var buffer = Buffer.buffer();

            appendBytes(buffer, value.getLong(PREVIOUS_FLAP_TIMESTAMP), DatastoreConstants.PluginId.NETROUTE_POLICY_DURATION_METRIC.getName());

            updateEventFieldBatch(NetRoute.NETROUTE_ID, CommonUtil.getString(value.getLong(NetRoute.NETROUTE_ID)), buffer);

            updateEventFieldBatch(PolicyEngineConstants.POLICY_ID, CommonUtil.getString(value.getLong(ID)), buffer);

            updateEventFieldBatch(DURATION, CommonUtil.getString(value.getInteger(DURATION)), buffer);

            updateEventFieldBatch(SEVERITY, value.getString(SEVERITY), buffer);

            updateEventFieldBatch(MetricPolicy.POLICY_THRESHOLD, CommonUtil.getString(value.getValue(MetricPolicy.POLICY_THRESHOLD, EMPTY_VALUE)), buffer);

            updateEventFieldBatch(VALUE, CommonUtil.getString(value.getValue(PREVIOUS_FLAP_VALUE, value.getValue(VALUE))), buffer);

            updateEventFieldBatch(METRIC, CommonUtil.getString(value.getValue(METRIC)), buffer);

            updateEventFieldBatch(PolicyEngineConstants.POLICY_TYPE, CommonUtil.getString(value.getValue(PolicyEngineConstants.POLICY_TYPE)), buffer);

            vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), buffer.getBytes());

            value.put(DURATION, 0);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Initializes a buffer with common event metadata.
     * This method sets up the buffer with timestamp, plugin information,
     * and datastore format details that are required for all events.
     *
     * @param bytes     The buffer to initialize
     * @param timestamp The event timestamp in epoch seconds
     * @param pluginId  The ID of the plugin generating the event
     */
    private void appendBytes(Buffer bytes, long timestamp, int pluginId)
    {
        bytes.setLongLE(0, timestamp);

        var plugin = pluginId + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName().toLowerCase();

        bytes.appendIntLE(plugin.length());

        bytes.appendString(plugin);

        bytes.appendByte(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName().byteValue());

        bytes.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.METRIC_POLICY_FLAP_HISTORY.ordinal()));

        bytes.appendShortLE(CommonUtil.getShort(EMPTY_VALUE.length()));

        bytes.appendString(EMPTY_VALUE);
    }

    /**
     * Updates a field in the event buffer for batch processing.
     * This method formats and appends a field-value pair to the buffer
     * according to the datastore's expected format, handling different
     * data types appropriately.
     *
     * @param column The name of the field to update
     * @param value  The string value to set for the field
     * @param buffer The buffer to append the field-value pair to
     */
    private void updateEventFieldBatch(String column, String value, Buffer buffer)
    {
        try
        {
            var category = DatastoreConstants.DataCategory.STRING.getName();

            if (column.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID) || column.equalsIgnoreCase(DURATION) || column.equalsIgnoreCase(NetRoute.NETROUTE_ID))
            {
                category = DatastoreConstants.DataCategory.NUMERIC.getName();
            }

            buffer.appendByte(category);

            buffer.appendIntLE(column.length());

            buffer.appendString(column);

            if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
            {
                buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
            }
            else
            {
                var bytes = value.getBytes(StandardCharsets.UTF_8);

                buffer.appendIntLE(bytes.length);

                buffer.appendBytes(bytes);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
