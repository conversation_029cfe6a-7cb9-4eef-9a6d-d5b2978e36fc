/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.store.WidgetConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.TestAPIConstants.*;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestReport
{
    private static Map<String, Long> reportIds;

    private static long widgetId;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        reportIds = new HashMap<>();

        JsonObject widgetPayload = new JsonObject("{\"id\":-1,\"visualization.name\":\"Test Widget\",\"visualization.description\":\"Widget is created for test case\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Chart\",\"visualization.type\":\"Area\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{}},\"data.points\":[{\"data.point\":\"interface~packets\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[8587355538242]}]}],\"visualization.properties\":{\"chart\":{\"rotation.angle\":0,\"chart.legend\":\"no\",\"chart.label\":\"no\",\"highchart\":{},\"sorting\":{\"limit\":10,\"order\":\"desc\"}}}}");

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, widgetPayload, testContext.succeeding(responseWidget -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, responseWidget.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, responseWidget.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            widgetId = responseWidget.bodyAsJsonObject().getLong(GlobalConstants.ID);

            Assertions.assertNotNull(WidgetConfigStore.getStore().getItemsByValue(GlobalConstants.ID, widgetId));

            testContext.completeNow();

        })));
    }

    public static void assertReportTestResult(VertxTestContext testContext, String key, JsonObject payload) throws InterruptedException
    {
        TestAPIUtil.post(REPORT_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {

            var body = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertNotNull(body.getLong(GlobalConstants.ID));

            var item = ReportConfigStore.getStore().getItem(body.getLong(GlobalConstants.ID));

            Assertions.assertNotNull(item);

            Assertions.assertEquals(payload.getString(Report.REPORT_NAME), item.getString(Report.REPORT_NAME));

            reportIds.put(key, body.getLong(GlobalConstants.ID));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void TestCreateReport1(VertxTestContext testContext) throws InterruptedException
    {
        assertReportTestResult(testContext, "1", new JsonObject("{\"report.name\":\"Test Report 1\",\"report.description\":\"This report is created for test cases\",\"report.scheduler\":\"no\",\"report.category\":\"Metric\",\"enable.grid.view\":\"no\",\"report.type\":\"Custom\",\"report.layout\":\"auto\"}").put("report.widgets", new JsonArray().add(widgetId)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void TestCreateReport2(VertxTestContext testContext) throws InterruptedException
    {
        assertReportTestResult(testContext, "2", new JsonObject("{\"report.name\":\"Test Report 2\",\"report.description\":\"This report is created for test cases\",\"report.scheduler\":\"no\",\"report.category\":\"Metric\",\"enable.grid.view\":\"no\",\"report.type\":\"Custom\",\"report.layout\":\"auto\"}").put("report.widgets", new JsonArray().add(widgetId)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void TestCreateReport3(VertxTestContext testContext) throws InterruptedException
    {
        assertReportTestResult(testContext, "3", new JsonObject("{\"report.name\":\"Test Report 3\",\"report.description\":\"This report is created for test cases\",\"report.scheduler\":\"no\",\"report.category\":\"Metric\",\"enable.grid.view\":\"no\",\"report.type\":\"Custom\",\"report.layout\":\"auto\"}").put("report.widgets", new JsonArray().add(widgetId)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void TestCreateReportWithScheduler(VertxTestContext testContext) throws InterruptedException
    {
        assertReportTestResult(testContext, "4", new JsonObject("{\"report.name\":\"Test Report 4\",\"report.description\":\"This report is created for test cases\",\"report.scheduler\":\"yes\",\"report.category\":\"Metric\",\"enable.grid.view\":\"no\",\"report.type\":\"Custom\",\"report.layout\":\"auto\"}").put("report.widgets", new JsonArray().add(widgetId)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void TestGetReport(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.get(REPORT_API_ENDPOINT + "/" + reportIds.get("1"), testContext.succeeding(response -> testContext.verify(() ->
        {

            var body = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertEquals(reportIds.get("1"), body.getJsonObject(GlobalConstants.RESULT).getLong(GlobalConstants.ID));

            Assertions.assertEquals("Test Report 1", body.getJsonObject(GlobalConstants.RESULT).getString(Report.REPORT_NAME));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void TestGetAllReport(VertxTestContext testContext) throws InterruptedException
    {
        var reportIds = ReportConfigStore.getStore().getIds();

        TestAPIUtil.get(REPORT_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

            for (var index = 0; index < items.size(); index++)
            {
                Assertions.assertTrue(reportIds.contains(items.getJsonObject(index).getLong(GlobalConstants.ID)));
            }

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void TestUpdateReport(VertxTestContext testContext) throws InterruptedException
    {
        var payload = new JsonObject("{\"report.name\":\"Test Report 11\",\"report.scheduler\":\"yes\",\"enable.grid.view\":\"yes\"}");

        TestAPIUtil.put(REPORT_API_ENDPOINT + "/" + reportIds.get("1"), payload, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var item = ReportConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

            Assertions.assertEquals(payload.getString(Report.REPORT_NAME), item.getString(Report.REPORT_NAME));

            Assertions.assertEquals(GlobalConstants.YES, item.getString(Report.REPORT_SCHEDULER));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    public void TestDeleteReport(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.delete(REPORT_API_ENDPOINT + "/" + reportIds.get("1"), testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertNull(ReportConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID)), "Report should be deleted but didn't");

            Assertions.assertNotNull(WidgetConfigStore.getStore().getItem(widgetId), "Widget should not be deleted");

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    public void TestDeleteReportScheduler(VertxTestContext testContext) throws InterruptedException
    {
        TestAPIUtil.post(SCHEDULER_API_ENDPOINT,

                new JsonObject("{\"scheduler.job.type\":\"Report\",\"scheduler.start.date\":\"04-11-2022\",\"scheduler.times\":[\"00:00\"],\"scheduler.timeline\":\"Once\",\"scheduler.context\":{\"objects\":[" + reportIds.get("4") + "]},\"scheduler.email.recipients\":[]}"),

                testContext.succeeding(schedulerResponse -> TestAPIUtil.delete(REPORT_API_ENDPOINT + "/" + reportIds.get("4"), testContext.succeeding(response -> testContext.verify(() ->
                {

                    Assertions.assertEquals(SC_OK, response.statusCode());

                    Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

                    var retries = new AtomicInteger(3);

                    TestUtil.vertx().setPeriodic(5000, timer ->
                    {

                        var report = ReportConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                        var scheduler = SchedulerConfigStore.getStore().getItem(schedulerResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.ID).getLong(0));

                        var widget = WidgetConfigStore.getStore().getItem(widgetId);

                        if (report == null && scheduler == null && widget != null)
                        {
                            Assertions.assertNull(ReportConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID)), "Report should be deleted but didn't");

                            Assertions.assertNull(SchedulerConfigStore.getStore().getItem(schedulerResponse.bodyAsJsonObject().getJsonArray(GlobalConstants.ID).getLong(0)), "Report should be deleted but didn't");

                            Assertions.assertNotNull(WidgetConfigStore.getStore().getItem(widgetId), "Widget should not be deleted");

                            testContext.completeNow();
                        }
                        else
                        {
                            if (retries.decrementAndGet() <= 0)
                            {
                                testContext.failNow("max attempt exceeded..");
                            }
                        }
                    });
                })))));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}