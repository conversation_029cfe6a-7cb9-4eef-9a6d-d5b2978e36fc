{"************": {"result": {"tanzu.kubernetes.pod": [{"tanzu.kubernetes.pod": "default-cadvisor-gxmk7", "tanzu.kubernetes.pod.application": "cadvisor", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:11", "tanzu.kubernetes.pod.ip.address": "***********", "tanzu.kubernetes.pod.name": "cadvisor-gxmk7", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.node.name": "worker1", "tanzu.kubernetes.pod.restarts": 1, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "default-cadvisor-mhmsg", "tanzu.kubernetes.pod.application": "cadvisor", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:11", "tanzu.kubernetes.pod.ip.address": "*********", "tanzu.kubernetes.pod.name": "cadvisor-mhmsg", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 1, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "default-my-job-8gdmh", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Mon, Feb 24 2025, 13:16", "tanzu.kubernetes.pod.name": "my-job-8gdmh", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 0, "tanzu.kubernetes.pod.status": "Succeeded", "tanzu.kubernetes.pod.type": "Job"}, {"tanzu.kubernetes.pod": "default-my-pod", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Wed, Feb 26 2025, 05:22", "tanzu.kubernetes.pod.name": "my-pod", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 0, "tanzu.kubernetes.pod.status": "Failed"}, {"tanzu.kubernetes.pod": "default-my-statefulset-0", "tanzu.kubernetes.pod.application": "my-app", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Mon, Feb 24 2025, 13:36", "tanzu.kubernetes.pod.name": "my-statefulset-0", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.persistent.volume": "my-volume-my-statefulset-0", "tanzu.kubernetes.pod.status": "Pending", "tanzu.kubernetes.pod.type": "StatefulSet"}, {"tanzu.kubernetes.pod": "default-prometheus-7f6cdfb75d-h5zhz", "tanzu.kubernetes.pod.application": "prometheus", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Mon, Apr 14 2025, 08:12", "tanzu.kubernetes.pod.ip.address": "*********", "tanzu.kubernetes.pod.name": "prometheus-7f6cdfb75d-h5zhz", "tanzu.kubernetes.pod.namespace": "default", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 1, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "ReplicaSet"}, {"tanzu.kubernetes.pod": "kube-system-coredns-7c65d6cfc9-r8jbl", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "***********", "tanzu.kubernetes.pod.memory.limit.percent": 4.4557248180391325, "tanzu.kubernetes.pod.memory.request.percent": 1.834710219192584, "tanzu.kubernetes.pod.name": "coredns-7c65d6cfc9-r8jbl", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker1", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "ReplicaSet"}, {"tanzu.kubernetes.pod": "kube-system-coredns-7c65d6cfc9-z7cc4", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "***********", "tanzu.kubernetes.pod.memory.limit.percent": 4.4557248180391325, "tanzu.kubernetes.pod.memory.request.percent": 1.834710219192584, "tanzu.kubernetes.pod.name": "coredns-7c65d6cfc9-z7cc4", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker1", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "ReplicaSet"}, {"tanzu.kubernetes.pod": "kube-system-etcd-master", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.memory.request.percent": 2.6210145988465485, "tanzu.kubernetes.pod.name": "etcd-master", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "Node"}, {"tanzu.kubernetes.pod": "kube-system-kube-apiserver-master", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 6.25, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-apiserver-master", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 8, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "Node"}, {"tanzu.kubernetes.pod": "kube-system-kube-controller-manager-master", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-controller-manager-master", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 10, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "Node"}, {"tanzu.kubernetes.pod": "kube-system-kube-proxy-bbrsg", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-proxy-bbrsg", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "kube-system-kube-proxy-lhqdd", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:33", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-proxy-lhqdd", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "kube-system-kube-proxy-ltl4w", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:33", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-proxy-ltl4w", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker1", "tanzu.kubernetes.pod.restarts": 5, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "kube-system-kube-scheduler-master", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "kube-scheduler-master", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 10, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "Node"}, {"tanzu.kubernetes.pod": "kube-system-metrics-server-6794476995-w2sxc", "tanzu.kubernetes.pod.containers": 1, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Mon, Apr 14 2025, 07:10", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.memory.request.percent": 5.242029197693097, "tanzu.kubernetes.pod.name": "metrics-server-6794476995-w2sxc", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 1, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "ReplicaSet"}, {"tanzu.kubernetes.pod": "kube-system-weave-net-6c4pk", "tanzu.kubernetes.pod.containers": 2, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "weave-net-6c4pk", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker1", "tanzu.kubernetes.pod.restarts": 13, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "kube-system-weave-net-sx2pd", "tanzu.kubernetes.pod.containers": 2, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "weave-net-sx2pd", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "master", "tanzu.kubernetes.pod.restarts": 13, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}, {"tanzu.kubernetes.pod": "kube-system-weave-net-wmprn", "tanzu.kubernetes.pod.containers": 2, "tanzu.kubernetes.pod.cpu.request.percent": 2.5, "tanzu.kubernetes.pod.creation.time": "Sun, Feb 09 2025, 05:47", "tanzu.kubernetes.pod.ip.address": "************", "tanzu.kubernetes.pod.name": "weave-net-wmprn", "tanzu.kubernetes.pod.namespace": "kube-system", "tanzu.kubernetes.pod.node.name": "worker2", "tanzu.kubernetes.pod.restarts": 12, "tanzu.kubernetes.pod.status": "Running", "tanzu.kubernetes.pod.type": "DaemonSet"}], "tanzu.kubernetes.pods": 19}}}