{"172.16.8.166": {"result": {"mysql.innodb.buffer.pool.pages.data": 64.0, "mysql.innodb.buffer.pool.dirty.pages": 0.0, "mysql.innodb.buffer.pool.flush.pages.rate": 0.0, "mysql.innodb.buffer.pool.free.pages": 6784.0, "mysql.innodb.buffer.pool.misc.pages": 0.0, "mysql.innodb.buffer.pool.pages": 6848.0, "mysql.innodb.buffer.pool.random.ahead.reads": 1.0, "mysql.innodb.buffer.pool.sequential.reads.rate": 0.0, "mysql.innodb.buffer.pool.read.requests.rate": 4140.0, "mysql.innodb.buffer.pool.reads.rate": 13.0, "mysql.innodb.buffer.pool.free.waits": 0.0, "mysql.innodb.buffer.pool.write.requests.rate": 0.0, "mysql.innodb.data.fsyncs.rate": 3.0, "mysql.innodb.data.pending.fsyncs": 0.0, "mysql.innodb.data.pending.reads": 0.0, "mysql.innodb.data.pending.writes": 0.0, "mysql.innodb.data.reads.rate": 74.0, "mysql.innodb.data.writes.rate": 3.0, "mysql.innodb.double.write.written.pages.rate": 0.0, "mysql.innodb.double.write.writes.rate": 0.0, "mysql.innodb.log.waits.rate": 0.0, "mysql.innodb.log.write.requests.rate": 0.0, "mysql.innodb.log.writes.rate": 1.0, "mysql.innodb.os.log.fsyncs.rate": 3.0, "mysql.innodb.os.log.pending.fsyncs": 0.0, "mysql.innodb.os.log.pending.writes": 0.0, "mysql.innodb.os.log.written.rate": 512.0, "mysql.innodb.page.size.bytes": 0.02, "mysql.innodb.created.pages.rate": 0.0, "mysql.innodb.read.pages.rate": 64.0, "mysql.innodb.written.pages.rate": 0.0, "mysql.innodb.row.lock.current.waits": 0.0, "mysql.innodb.row.lock.time.ms": 0.0, "mysql.innodb.average.row.lock.time.ms": 0.0, "mysql.innodb.row.lock.waits": 0.0, "mysql.innodb.deleted.rows.rate": 0.0, "mysql.innodb.inserted.rows.rate": 0.0, "mysql.innodb.read.rows.rate": 0.0, "mysql.innodb.update.rows.rate": 0.0}, "errors": []}, "172.16.8.165": {"result": {"mysql.innodb.buffer.pool.pages.data": 1218.0, "mysql.innodb.buffer.pool.dirty.pages": 0.0, "mysql.innodb.buffer.pool.flush.pages.rate": 848.0, "mysql.innodb.buffer.pool.free.pages": 6958.0, "mysql.innodb.buffer.pool.misc.pages": 16.0, "mysql.innodb.buffer.pool.pages": 8192.0, "mysql.innodb.buffer.pool.random.ahead.reads": 0.0, "mysql.innodb.buffer.pool.read.requests.rate": 362897656.0, "mysql.innodb.buffer.pool.reads.rate": 1023.0, "mysql.innodb.buffer.pool.free.waits": 0.0, "mysql.innodb.buffer.pool.write.requests.rate": 7189.0, "mysql.innodb.data.fsyncs.rate": 778.0, "mysql.innodb.data.pending.fsyncs": 0.0, "mysql.innodb.data.pending.reads": 0.0, "mysql.innodb.data.pending.writes": 0.0, "mysql.innodb.data.reads.rate": 1571.0, "mysql.innodb.data.writes.rate": 1515.0, "mysql.innodb.double.write.written.pages.rate": 625.0, "mysql.innodb.double.write.writes.rate": 173.0, "mysql.innodb.log.waits.rate": 0.0, "mysql.innodb.log.write.requests.rate": 2863.0, "mysql.innodb.log.writes.rate": 366.0, "mysql.innodb.os.log.fsyncs.rate": 284.0, "mysql.innodb.os.log.pending.fsyncs": 0.0, "mysql.innodb.os.log.pending.writes": 0.0, "mysql.innodb.os.log.written.rate": 312832.0, "mysql.innodb.page.size.bytes": 0.02, "mysql.innodb.created.pages.rate": 288.0, "mysql.innodb.read.pages.rate": 1022.0, "mysql.innodb.written.pages.rate": 862.0, "mysql.innodb.row.lock.current.waits": 0.0, "mysql.innodb.row.lock.time.ms": 0.0, "mysql.innodb.average.row.lock.time.ms": 0.0, "mysql.innodb.row.lock.waits": 0.0, "mysql.innodb.deleted.rows.rate": 2.0, "mysql.innodb.inserted.rows.rate": 16390.0, "mysql.innodb.read.rows.rate": 8207.0, "mysql.innodb.update.rows.rate": 0.0}, "errors": []}}