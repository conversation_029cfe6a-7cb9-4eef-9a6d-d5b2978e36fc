[{"_type": "0", "id": 10000000000854, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.vm~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "citrix.xen.vm~cpu.percent"}}}}, {"_type": "0", "id": 10000000000855, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.vm~memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "citrix.xen.vm~memory.used.percent"}}}}, {"_type": "0", "id": 10000000000856, "visualization.name": "Disk IO", "visualization.description": "Disk Utilization Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disk.io.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disk.io.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~disk.capacity.bytes"}, {"type": "metric", "data.point": "citrix.xen.vm~disk.io.write.bytes.per.sec"}, {"type": "metric", "data.point": "citrix.xen.vm~disk.io.read.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "data.points": [{"label": "Capacity", "value": "citrix.xen.vm~disk.capacity.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "citrix.xen.vm~disk.io.read.bytes.per.sec.last"}, {"label": "Write", "value": "citrix.xen.vm~disk.io.write.bytes.per.sec.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "citrix.xen.vm~disk.io.read.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000857, "visualization.name": "Memory Usages", "visualization.description": "Memory Usages Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~memory.overhead.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~memory.target.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~memory.overhead.bytes"}, {"type": "metric", "data.point": "citrix.xen.vm~memory.target.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Usage", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Overhead", "value": "citrix.xen.vm~memory.overhead.bytes.last"}, {"label": "Target", "value": "citrix.xen.vm~memory.target.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "citrix.xen.vm~memory.target.bytes"}}}}, {"_type": "0", "id": 10000000000858, "visualization.name": "IO Latency", "visualization.description": "IO Latency Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~disk.io.avg.read.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disk.io.avg.write.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~disk.io.avg.read.latency.ms"}, {"type": "metric", "data.point": "citrix.xen.vm~disk.io.avg.write.latency.ms"}]}], "visualization.properties": {"gauge": {"header": {"title": "IO Latency", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "citrix.xen.vm~disk.io.avg.read.latency.ms.last"}, {"label": "Write", "value": "citrix.xen.vm~disk.io.avg.write.latency.ms.last"}]}, "style": {"icon": {"name": "backup"}, "color.data.point": "citrix.xen.vm~disk.io.avg.read.latency.ms"}}}}, {"_type": "0", "id": 10000000000859, "visualization.name": "Network Traffic", "visualization.description": "Network Traffic Citrix XenServer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.vm~network.bytes.per.sec"}, {"type": "metric", "data.point": "citrix.xen.vm~network.in.bytes.per.sec"}, {"type": "metric", "data.point": "citrix.xen.vm~network.out.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "data.points": [{"label": "Traffic", "value": "citrix.xen.vm~network.bytes.per.sec.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "citrix.xen.vm~network.in.bytes.per.sec.last"}, {"label": "Out", "value": "citrix.xen.vm~network.out.bytes.per.sec.last"}]}, "style": {"icon": {"name": "topology"}, "color.data.point": "citrix.xen.vm~network.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000860, "visualization.name": "Overhead/Target Memory", "visualization.description": "Overhead/Target (TimeSeries) Citrix XenServer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~memory.target.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~memory.overhead.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000861, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization (TimeSeries) Citrix XenServer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000862, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization (TimeSeries) Citrix XenServer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000863, "visualization.name": "Network Traffic Details", "visualization.description": "Network Traffic (TimeSeries) Citrix XenServer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~network.in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~network.out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000864, "visualization.name": "Disk Throughput", "visualization.description": "Throughput (TimeSeries) Citrix XenServer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000865, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "citrix.xen.vm~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000866, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.vm~uptime.percent.avg"}}}}]