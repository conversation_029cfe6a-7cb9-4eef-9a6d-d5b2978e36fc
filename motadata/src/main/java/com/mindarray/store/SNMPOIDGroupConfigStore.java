/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;

public class SNMPOIDGroupConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SNMPOIDGroupConfigStore.class, GlobalConstants.MOTADATA_STORE, "SNMP OID Group Config Store");

    private static final SNMPOIDGroupConfigStore STORE = new SNMPOIDGroupConfigStore();

    private SNMPOIDGroupConfigStore()
    {

        super(DBConstants.TBL_SNMP_OID_GROUP, LOGGER, false);
    }

    public static SNMPOIDGroupConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            super.initStore().onComplete(result ->
            {
                if (result.succeeded())
                {
                    items.values().stream().filter(item -> item.getString(NMSConstants.SNMP_OID_GROUP_ID) != null).toList()
                            .forEach(item -> SNMPTemplateOIDGroupCacheStore.getStore().updateItem(item.getString(NMSConstants.SNMP_OID_GROUP_ID), item));

                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }
}
