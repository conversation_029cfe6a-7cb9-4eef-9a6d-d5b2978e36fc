[{"id": 10000000002001, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "Cisco UCS Chassis", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.total.chassis", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.ucs.total.chassis"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "interface", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.ucs.total.chassis"}]}}}}, {"id": 10000000002002, "_type": "0", "visualization.name": "Blade Server", "visualization.description": "Cisco UCS Blade Server", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.chassis.servers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.ucs.chassis.servers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "server-alt", "placement": "prefix"}}, "header": {"title": "", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.ucs.chassis.servers"}]}}}}, {"id": 10000000002003, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "Cisco UCS Rack Server", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.rack.mount.servers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.ucs.rack.mount.servers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "server", "placement": "prefix"}}, "header": {"title": "", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.ucs.rack.mount.servers"}]}}}}, {"id": 10000000002004, "_type": "0", "visualization.name": "Fabric Interconnect", "visualization.description": "Cisco UCS Fabric Interconnect", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "total.cisco.ucs.fabric.interconnect", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "total.cisco.ucs.fabric.interconnect"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "server-alt", "placement": "prefix"}}, "header": {"title": "", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "total.cisco.ucs.fabric.interconnect"}]}}}}, {"id": 10000000002005, "_type": "0", "visualization.name": "HBAs", "visualization.description": "Cisco UCS Adapters", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.adapters", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.ucs.adapters"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "processor", "placement": "prefix"}}, "header": {"title": "", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.ucs.adapters"}]}}}}, {"id": 10000000002006, "_type": "0", "visualization.name": "IO Modules", "visualization.description": "Cisco UCS IO Modules", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.io.modules", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.ucs.io.modules"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.ucs.io.modules"}]}}}}, {"_type": "0", "id": 10000000002007, "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "Cisco UCS Chassis Grid", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.chassis.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.chassis.blade.server", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.chassis.psu", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.chassis.fan", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.chassis.io.module", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "cisco.ucs.chassis.name", "title": "<PERSON><PERSON><PERSON> Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "chassis", "style": {}}, {"name": "cisco.ucs.chassis.blade.server", "title": "Blade Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.chassis.psu", "title": "PSUs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.chassis.fan", "title": "Fan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.chassis.io.module", "title": "IO Modules", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002008, "visualization.name": "<PERSON><PERSON>", "visualization.description": "Cisco UCS Rack Server Grid", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.rack.mount.server", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.rack.mount.server.cpus", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.rack.mount.psu", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.rack.mount.fan", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.rack.mount.io.module", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.rack.mount.adapter", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "cisco.ucs.rack.mount.server", "title": "Server Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "rack", "style": {}}, {"name": "cisco.ucs.rack.mount.server.cpus", "title": "CPUs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.rack.mount.psu", "title": "PSUs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.rack.mount.fan", "title": "Fan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.rack.mount.io.module", "title": "IO Modules", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.rack.mount.adapter", "title": "Adapter", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002009, "visualization.name": "Fabric Interconnect", "visualization.description": "Cisco UCS Fabric Interconnect", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.ucs.fabric.interconnect", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.fabric.interconnect.ethernet.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.fabric.interconnect.ethernet.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.fabric.interconnect.psu", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.fabric.interconnect.fan", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.ucs.fabric.interconnect.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "cisco.ucs.fabric.interconnect", "title": "<PERSON><PERSON>ric <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "fabric", "style": {}}, {"name": "cisco.ucs.fabric.interconnect.ethernet.port", "title": "Ethernet", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.fabric.interconnect.ethernet.port", "title": "FC Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.fabric.interconnect.psu", "title": "PSUs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.fabric.interconnect.fan", "title": "Fan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.ucs.fabric.interconnect.memory.installed.bytes", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]