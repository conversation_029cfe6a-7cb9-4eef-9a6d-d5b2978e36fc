/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.NetClient;
import io.vertx.core.net.NetClientOptions;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;

public final class PortUtil
{
    private static final Logger LOGGER = new Logger(PortUtil.class, GlobalConstants.MOTADATA_UTIL, "Port Util");

    private static NetClient client = null;

    public static void init()
    {
        client = Bootstrap.vertx().createNetClient(new NetClientOptions().setConnectTimeout(5000).setIdleTimeout(5000).setTcpKeepAlive(true).setTcpFastOpen(true));
    }

    public static void close()
    {
        if (client != null)
        {
            client.close();
        }
    }

    public static boolean isConnected(String target, int port)
    {
        return isConnected(target, port, 1);
    }

    public static JsonObject test(String target, int port, int timeout)
    {
        var result = new JsonObject();

        try (var socket = new Socket())
        {
            setupCleanupTimer(timeout, socket);

            var millis = System.currentTimeMillis();

            socket.setSoTimeout(timeout * 1000);

            socket.connect(new InetSocketAddress(target, port), timeout * 1000);

            result.put(GlobalConstants.STATUS, GlobalConstants.STATUS_UP).put(GlobalConstants.PORT, port).put(GlobalConstants.IP_ADDRESS, socket.getLocalAddress().getHostAddress()).put(GlobalConstants.RESULT, System.currentTimeMillis() - millis);
        }

        catch (Exception exception)
        {
            result.put(GlobalConstants.STATUS, GlobalConstants.STATUS_DOWN).put(GlobalConstants.PORT, port).put(GlobalConstants.MESSAGE, exception.getMessage())
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()));

            LOGGER.error(exception);
        }

        return result;
    }

    public static JsonObject test(String target, int port)
    {
        return test(target, port, 1);
    }

    public static boolean isConnected(String target, int port, int timeout)
    {
        return test(target, port, timeout).getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_UP);
    }

    public static Future<JsonArray> isOpen(JsonArray targets, int port)
    {
        var promise = Promise.<JsonArray>promise();

        try
        {
            var futures = new ArrayList<Future<JsonObject>>();

            for (var index = 0; index < targets.size(); index++)
            {
                var target = targets.getString(index);

                var future = Promise.<JsonObject>promise();

                futures.add(future.future());

                client.connect(port, target, result ->
                {
                    if (result.succeeded())
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("connected to %s on %s using AsyncSocket", target, port));
                        }

                        result.result().close();

                        future.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_UP)
                                .put(AIOpsObject.OBJECT_TARGET, target)
                                .put(AIOpsObject.OBJECT_NAME, port));
                    }

                    else
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("failed to connect to %s on %s using AsyncSocket", target, port));
                        }

                        future.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_DOWN)
                                .put(AIOpsObject.OBJECT_TARGET, target)
                                .put(GlobalConstants.MESSAGE, result.cause().getMessage())
                                .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                    }
                });
            }

            Future.join(futures).onComplete(result -> promise.complete(new JsonArray(result.result().list())));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    public static Future<JsonObject> isOpen(String target, int port, int timeout, Method method)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            if (CommonUtil.isNotNullOrEmpty(target))
            {

                if (method == Method.ASYNC_SOCKET)
                {

                    client.connect(port, target, result ->
                    {

                        if (result.succeeded())
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("connected to %s on %s using AsyncSocket", target, port));
                            }

                            result.result().close();

                            promise.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_UP)
                                    .put(AIOpsObject.OBJECT_NAME, port));
                        }

                        else
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("failed to connect to %s on %s using AsyncSocket", target, port));
                            }

                            promise.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_DOWN)
                                    .put(AIOpsObject.OBJECT_NAME, port)
                                    .put(GlobalConstants.MESSAGE, result.cause().getMessage())
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                        }
                    });

                }

                else if (method == Method.SYNC_SOCKET)
                {
                    Bootstrap.vertx().<JsonObject>executeBlocking(future ->
                    {

                        try (var socket = new Socket())
                        {
                            setupCleanupTimer(timeout, socket);

                            socket.setSoTimeout(timeout * 1000);

                            socket.connect(new InetSocketAddress(target, port), timeout * 1000);

                            future.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_UP)
                                    .put(AIOpsObject.OBJECT_NAME, port));

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("connected to %s on %s using SyncSocket", target, port));
                            }

                        }

                        catch (Exception exception)
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("failed to connect to %s on %s using SyncSocket", target, port));
                            }

                            future.complete(new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_DOWN)
                                    .put(GlobalConstants.MESSAGE, exception.getMessage())
                                    .put(AIOpsObject.OBJECT_NAME, port)
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));

                        }
                    }, false, result -> promise.complete(result.result()));

                }

                else
                {

                    promise.fail("NMap/NC method not implemented yet...");

                    //Todo start process and get the response.
                }

            }

            else
            {
                promise.fail("Invalid target");
            }

        }

        catch (Exception exception)
        {

            LOGGER.error(exception);

            promise.fail(exception);
        }


        return promise.future();
    }

    public static Future<JsonObject> isOpen(String target, int port)
    {
        return isOpen(target, port, 5, Method.ASYNC_SOCKET);
    }

    public static Future<JsonObject> isOpen(String target, int port, Method method)
    {
        return isOpen(target, port, 5, method);
    }

    public static Future<JsonObject> isOpen(String target, int port, int timeout)
    {
        return isOpen(target, port, timeout, Method.ASYNC_SOCKET);
    }

    public static Future<JsonArray> isOpen(String target, JsonArray ports, int timeout, Method method)
    {

        if (method == Method.NMAP)
        {
            return scan(target, ports, timeout);
        }

        else
        {
            var promise = Promise.<JsonArray>promise();

            var futures = new ArrayList<Future<JsonObject>>();

            ports.forEach(port -> futures.add(isOpen(target, CommonUtil.getInteger(port), timeout, method)));

            Future.join(futures).onComplete(result -> promise.complete(new JsonArray(result.result().list())));

            return promise.future();

        }

    }

    public static Future<JsonArray> isOpen(String target, JsonArray ports)
    {
        return isOpen(target, ports, 5, Method.ASYNC_SOCKET);
    }

    public static Future<JsonArray> isOpen(String target, JsonArray ports, int timeout)
    {
        return isOpen(target, ports, timeout, Method.ASYNC_SOCKET);
    }

    public static Future<JsonArray> isOpen(String target, JsonArray ports, Method method)
    {
        return isOpen(target, ports, 5, method);
    }

    /**
     * @return available port
     */
    public static int getAvailablePort() throws IOException
    {
        try (var socket = new ServerSocket(0))
        {
            return socket.getLocalPort();
        }
    }

    private static Future<JsonArray> scan(String target, JsonArray ports, int timeout)
    {
        return Promise.<JsonArray>promise().future();
    }

    private static void setupCleanupTimer(int timeout, Socket socket)
    {
        Bootstrap.vertx().setTimer(timeout * 1000L, timer ->
        {
            Bootstrap.vertx().cancelTimer(timer);

            if (!socket.isClosed())
            {
                try
                {
                    //remove socket.getRemoteSocketAddress().toString() because some time return null
                    LOGGER.warn("socket has to be closed forcefully...");

                    if (!socket.isInputShutdown())
                    {
                        socket.shutdownInput();
                    }

                    if (!socket.isOutputShutdown())
                    {
                        socket.shutdownOutput();
                    }

                    socket.close();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }

        });
    }

    public enum Method
    {
        NMAP,
        NC,
        ASYNC_SOCKET,
        SYNC_SOCKET,
    }


}
