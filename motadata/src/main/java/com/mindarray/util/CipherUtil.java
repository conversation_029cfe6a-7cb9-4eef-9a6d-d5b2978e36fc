/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.GlobalConstants;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Utility class for encryption and decryption operations using AES algorithm.
 * <p>
 * This class provides methods to:
 * - Encrypt and decrypt strings
 * - Encrypt and decrypt byte arrays
 * <p>
 * The implementation uses AES (Advanced Encryption Standard) with a fixed key.
 * All encryption/decryption operations are performed using this key.
 * <p>
 * Note: This implementation uses ECB mode (default for "AES" transformation string)
 * which may not be suitable for all security requirements. For production systems,
 * consider using a more secure mode like CBC with proper IV handling.
 */
public class CipherUtil
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(CipherUtil.class, GlobalConstants.MOTADATA_UTIL, "Cipher Util");

    /**
     * Cipher instance configured for encryption operations
     */
    private Cipher encryption;

    /**
     * Cipher instance configured for decryption operations
     */
    private Cipher decryption;

    /**
     * Creates a new CipherUtil instance and initializes the encryption and decryption ciphers.
     * <p>
     * This constructor:
     * 1. Creates AES cipher instances for both encryption and decryption
     * 2. Initializes them with a fixed 128-bit key
     * 3. Logs any exceptions that occur during initialization
     * <p>
     * If initialization fails, the encrypt/decrypt methods will return null or empty values.
     */
    public CipherUtil()
    {
        try
        {
            // Initialize encryption cipher
            encryption = Cipher.getInstance("AES");

            // Create a fixed AES key (16 bytes = 128 bits)
            var key = new SecretKeySpec(new byte[]{'C', 'O', 'V', 'I', 'D', '-', '1', '9', 'M', 'O', 'T', 'A', 'D', 'A', 'T', 'A'}, "AES");

            // Configure cipher for encryption
            encryption.init(Cipher.ENCRYPT_MODE, key);

            // Initialize decryption cipher
            decryption = Cipher.getInstance("AES");

            // Configure cipher for decryption (using the same key)
            decryption.init(Cipher.DECRYPT_MODE, key);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Encrypts a string value using AES encryption.
     * <p>
     * This method:
     * 1. Checks if the input string is not null or empty
     * 2. Encrypts the string using the AES cipher
     * 3. Encodes the encrypted bytes using Base64 for safe storage/transmission
     *
     * @param value The string to encrypt
     * @return Base64-encoded encrypted string, or null if encryption fails or input is null/empty
     */
    public String encrypt(String value)
    {
        String result = null;

        try
        {
            if (CommonUtil.isNotNullOrEmpty(value))
            {
                // Encrypt the string bytes and encode the result as Base64
                result = Base64.getEncoder().encodeToString(encryption.doFinal(value.getBytes()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Decrypts a Base64-encoded encrypted string.
     * <p>
     * This method:
     * 1. Checks if the input string is not null or empty
     * 2. Decodes the Base64 string to get the encrypted bytes
     * 3. Decrypts the bytes using the AES cipher
     * 4. Converts the decrypted bytes back to a string
     *
     * @param value The Base64-encoded encrypted string to decrypt
     * @return The decrypted string, or an empty string if decryption fails or input is null/empty
     */
    public String decrypt(String value)
    {
        var result = GlobalConstants.EMPTY_VALUE;

        try
        {
            if (CommonUtil.isNotNullOrEmpty(value))
            {
                // Decode the Base64 string, decrypt the bytes, and convert to string
                result = new String(decryption.doFinal(Base64.getDecoder().decode(value.trim())));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Encrypts a byte array using AES encryption.
     * <p>
     * This method directly encrypts the input bytes without any additional encoding.
     *
     * @param bytes The byte array to encrypt
     * @return The encrypted byte array, or the original array if encryption fails or input is null
     */
    public byte[] encrypt(byte[] bytes)
    {
        try
        {
            if (bytes != null)
            {
                // Encrypt the byte array
                bytes = encryption.doFinal(bytes);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return bytes;
    }

    /**
     * Decrypts a byte array that was encrypted using the encrypt(byte[]) method.
     *
     * @param bytes The encrypted byte array to decrypt
     * @return The decrypted byte array, or the original array if decryption fails or input is null
     */
    public byte[] decrypt(byte[] bytes)
    {
        try
        {
            if (bytes != null)
            {
                // Decrypt the byte array
                bytes = decryption.doFinal(bytes);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return bytes;
    }

}
