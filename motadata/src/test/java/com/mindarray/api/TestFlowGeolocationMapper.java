/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.FlowGeolocationMapperConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.TestAPIConstants.FLOW_GEOLOCATION_MAPPER_API_ENDPOINT;
import static com.mindarray.api.FlowGeolocationMapper.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestFlowGeolocationMapper
{
    private static final String ENTITY_NAME = "Flow Geolocation Mapper";

    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestFlowGeolocationMapper.class, "Test Flow Geolocation Mapper", "Test Flow Geolocation Mapper");


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateFlowGeolocationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_GEOLOCATION_MAPPER_DESCRIPTION, "This is the test").put(FLOW_GEOLOCATION_MAPPER_COUNTRY, "India").put(FLOW_GEOLOCATION_MAPPER_CITY, "Ahmedabad").put(FLOW_GEOLOCATION_MAPPER_GROUP, new JsonArray().add("**********-255"));

        TestAPIUtil.post(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowGeolocationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestFlowGeolocationMapper.CONTEXT.put("flow.geolocation.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateDuplicateFlowGeolocationMapper(VertxTestContext testContext)
    {
        var context = new JsonObject().put(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_GEOLOCATION_MAPPER_DESCRIPTION, "This is the test Motadata").put(FLOW_GEOLOCATION_MAPPER_COUNTRY, "India").put(FLOW_GEOLOCATION_MAPPER_CITY, "Surat").put(FLOW_GEOLOCATION_MAPPER_GROUP, new JsonArray().add("**********-255"));

        TestAPIUtil.post(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Profile Name"),
                                    FlowGeolocationMapperConfigStore.getStore(), FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, context.getString(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetFlowGeolocationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.geolocation.mapper"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("flow.geolocation.mapper"), FlowGeolocationMapperConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllFlowGeolocationMapper(VertxTestContext testContext)
    {
        TestAPIUtil.get(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, FlowGeolocationMapperConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateFlowGeolocationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, "Motadata").put(FLOW_GEOLOCATION_MAPPER_DESCRIPTION, "This is the test Data").put(FLOW_GEOLOCATION_MAPPER_COUNTRY, "India").put(FLOW_GEOLOCATION_MAPPER_CITY, "Surat").put(FLOW_GEOLOCATION_MAPPER_GROUP, new JsonArray().add("**********-255"));

        TestAPIUtil.put(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT + TestFlowGeolocationMapper.CONTEXT.getLong("flow.geolocation.mapper"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(FlowGeolocationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, ENTITY_NAME), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteFlowGeolocationMapper(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT + CONTEXT.getLong("flow.geolocation.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(FlowGeolocationMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_DELETED, ENTITY_NAME));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteFlowGeolocationMapperNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT + 1234, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, ENTITY_NAME, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.FLOW_GEOLOCATION_MAPPER.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateSecondFlowGeolocationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, "Motadata Test 2").put(FLOW_GEOLOCATION_MAPPER_DESCRIPTION, "This is the test 2").put(FLOW_GEOLOCATION_MAPPER_COUNTRY, "India").put(FLOW_GEOLOCATION_MAPPER_CITY, "Ahmedabad").put(FLOW_GEOLOCATION_MAPPER_GROUP, new JsonArray().add("*******"));

        TestAPIUtil.post(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowGeolocationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateThirdFlowGeolocationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(FLOW_GEOLOCATION_MAPPER_PROFILE_NAME, "Motadata Test 3").put(FLOW_GEOLOCATION_MAPPER_DESCRIPTION, "This is the test 3").put(FLOW_GEOLOCATION_MAPPER_COUNTRY, "India").put(FLOW_GEOLOCATION_MAPPER_CITY, "Ahmedabad").put(FLOW_GEOLOCATION_MAPPER_GROUP, new JsonArray().add("**********-************"));

        TestAPIUtil.post(FLOW_GEOLOCATION_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(FlowGeolocationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, ENTITY_NAME), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestFlowGeolocationMapper.CONTEXT.put("flow.geolocation.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }
}
