/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.datagram.DatagramSocketOptions;
import io.vertx.core.json.JsonObject;

import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * A verticle responsible for collecting log messages over UDP connections.
 * <p>
 * The UDPLogCollector creates a UDP socket that listens on a configured port for
 * incoming log messages. It performs the following tasks:
 * <ul>
 *   <li>Listens for UDP packets on the configured port</li>
 *   <li>Parses incoming log messages, extracting the source IP from the message</li>
 *   <li>Creates log events with metadata (source IP, timestamp, etc.)</li>
 *   <li>Forwards log events to the appropriate event bus address</li>
 *   <li>Supports streaming log messages for real-time monitoring</li>
 *   <li>Tracks log quota usage</li>
 * </ul>
 * <p>
 * The collector behaves differently based on the bootstrap type:
 * <ul>
 *   <li>For COLLECTOR: Sends logs to the remote event processor</li>
 *   <li>For other types: Processes logs locally</li>
 * </ul>
 * <p>
 * UDP is a connectionless protocol, which makes it suitable for high-volume
 * log collection where some packet loss is acceptable.
 */
public class UDPLogCollector extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(UDPLogCollector.class, GlobalConstants.MOTADATA_LOG, "UDP Log Collector");

    /**
     * Flag indicating whether log streaming is enabled
     */
    private boolean streaming = false;

    /**
     * Initializes and starts the UDP log collector.
     * <p>
     * This method sets up a UDP socket that listens for incoming log messages.
     * It configures the socket with the following options:
     * <ul>
     *   <li>IPv6 disabled (using IPv4 only)</li>
     *   <li>Port reuse enabled for quick restart capability</li>
     *   <li>Address reuse enabled for multiple listeners</li>
     * </ul>
     * <p>
     * The collector listens on localhost at the configured collector UDP port.
     * For each received packet, it:
     * <ul>
     *   <li>Extracts the log message and source information</li>
     *   <li>Creates an event object with metadata</li>
     *   <li>Routes the event based on the bootstrap type</li>
     *   <li>Handles streaming if enabled</li>
     * </ul>
     *
     * @param promise A Promise that will be completed when the verticle has been started
     * @throws Exception If an error occurs during startup
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Create a UDP socket with IPv6 disabled, port reuse enabled, and address reuse enabled
        vertx.createDatagramSocket(new DatagramSocketOptions().setIpV6(false).setReusePort(true).setReuseAddress(true)).listen(
                MotadataConfigUtil.getCollectorUDPPort(), "localhost", result ->
                {
                    if (result.succeeded())
                    {
                        // Log successful connection
                        LOGGER.info(String.format("UDP Log listener connected to %s ", MotadataConfigUtil.getCollectorUDPPort() + ":" + MotadataConfigUtil.getVIPIPAddress()));

                        // Set up handler for incoming UDP packets
                        result.result().handler(packet ->
                        {
                            // Extract log message from packet data
                            var log = packet.data().getString(0, packet.data().length());

                            // Check if we're in EVENT_COLLECTOR mode or if we have quota available
                            if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR || LicenseUtil.updateUsedLogQuota(log.getBytes().length))
                            {
                                // Create event object with metadata
                                // Note: The first 15 characters of the log message contain the source information
                                var event = new JsonObject()
                                        .put(EventBusConstants.EVENT_VOLUME_BYTES, log.getBytes().length)
                                        .put(EventBusConstants.EVENT, log.substring(15).trim()) // Actual log message (after source info)
                                        .put(EventBusConstants.EVENT_SOURCE, log.substring(0, 15).trim()) // Source info (first 15 chars)
                                        .put(LogEngineConstants.RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(EVENT_TYPE, EVENT_LOG);

                                // Route event based on bootstrap type
                                if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
                                {
                                    // In COLLECTOR mode, send to remote event processor with registration ID
                                    vertx.eventBus().send(EVENT_REMOTE, event
                                            .put(EVENT_TYPE, EVENT_LOG)
                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                            .put("log.collector", GlobalConstants.YES));
                                }
                                else
                                {
                                    // In other modes, process locally
                                    vertx.eventBus().send(EVENT_LOG, event);
                                }

                                // If streaming is enabled, broadcast the event for real-time monitoring
                                if (streaming)
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST,
                                            new JsonObject()
                                                    .put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName())
                                                    .put(EVENT_CONTEXT, event));
                                }

                                // Log detailed trace information if enabled
                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("log received from UDP source: %s %s",
                                            packet.sender().host(), CommonUtil.getString(log)));
                                }
                            }
                            // If quota is exceeded or in wrong mode, the log is silently dropped
                        });

                        // Complete the promise to signal successful startup
                        promise.complete();
                    }
                    else
                    {
                        // Log server start failure
                        LOGGER.info(String.format("UDP Log listener failed to connected to %s ",
                                MotadataConfigUtil.getCollectorUDPPort() + ":" + MotadataConfigUtil.getVIPIPAddress()));

                        // Fail the promise with the cause
                        promise.fail(result.cause());
                    }

                }
                // Set up handler for when the socket is closed
        ).endHandler(result -> LOGGER.info("UDP log collector listener down..."));

        // Set up a consumer for change notifications to control streaming behavior
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            // Extract the notification type from the message
            switch (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                // Enable streaming when a START_LOG_TAIL notification is received
                case START_LOG_TAIL -> streaming = true;

                // Disable streaming when a STOP_LOG_TAIL notification is received
                case STOP_LOG_TAIL -> streaming = false;

                // Ignore other notification types
                default ->
                {
                    // do nothing for other notification types
                }
            }

            // Log any exceptions that occur during message processing
        }).exceptionHandler(LOGGER::error);
    }
}
