/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.store.ProxyServerConfigStore;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.vertx.core.MultiMap;
import io.vertx.core.http.Cookie;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.net.PemKeyCertOptions;
import io.vertx.core.net.ProxyOptions;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;

import static com.mindarray.api.APIConstants.CLIENT_ID;
import static com.mindarray.api.APIConstants.UI_CLIENT_ID;

public final class WebClientUtil
{
    private static final WebClientOptions WEB_CLIENT_OPTIONS = new WebClientOptions()
            .setTryUseCompression(true)
            .setPemKeyCertOptions(new PemKeyCertOptions()
                    .setCertPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-cert.pem")
                    .setKeyPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem"))
            .setSsl(true)
            .setUseAlpn(true)
            .setVerifyHost(false)
            .setTrustAll(true);
    private static final MultiMap MULTI_MAP = HttpHeaders.headers().add(CommonUtil.getString(HttpHeaderNames.COOKIE), Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode());
    private static ProxyOptions proxyOptions;
    private static WebClient webClient;
    private static WebClient proxyWebClient;

    private WebClientUtil()
    {

    }

    public static MultiMap getMultiMap()
    {
        return MULTI_MAP;
    }

    public static void setMultiMap(String token)
    {
        MULTI_MAP.remove(HttpHeaders.AUTHORIZATION.toString());

        MULTI_MAP.add(HttpHeaders.AUTHORIZATION.toString(), token);
    }

    public static ProxyOptions getProxyOptions()
    {
        return proxyOptions;
    }

    public static void init()
    {
        webClient = WebClient.create(Bootstrap.vertx(), WEB_CLIENT_OPTIONS.setMaxPoolSize(20));
    }

    public static void init(ProxyOptions options)
    {
        proxyOptions = options;

        proxyWebClient = WebClient.create(Bootstrap.vertx(), WEB_CLIENT_OPTIONS.setMaxPoolSize(1).setProxyOptions(proxyOptions));
    }

    public static WebClient getWebClient()
    {
        return webClient;
    }

    public static WebClient getProxyWebClient()
    {
        return proxyWebClient;
    }

    public static void close()
    {
        if (webClient != null)
        {
            webClient.close();
        }
    }

    public static void closeProxy()
    {
        if (proxyWebClient != null)
        {
            proxyWebClient.close();

            proxyOptions = null;
        }
    }

    public static boolean proxyEnabled()
    {
        return proxyWebClient != null && ProxyServerConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID) != null;
    }
}
