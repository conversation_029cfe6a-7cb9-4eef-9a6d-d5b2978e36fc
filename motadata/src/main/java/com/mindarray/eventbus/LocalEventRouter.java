/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * The LocalEventRouter class routes events to appropriate handlers based on event type and routing key.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Deploying multiple event router instances for load balancing</li>
 *   <li>Routing events to the appropriate router based on a routing key</li>
 *   <li>Supporting different routing strategies (dynamic, round-robin)</li>
 *   <li>Managing the lifecycle of router instances (add/remove)</li>
 *   <li>Handling event queries and bulk operations</li>
 *   <li>Persisting router state for recovery after restart</li>
 *   <li>Providing statistics about event processing when enabled</li>
 * </ul>
 * <p>
 * The LocalEventRouter supports multiple routing strategies:
 * <ul>
 *   <li><b>Dynamic Routing</b>: Time-based routing that routes to different handlers based on time.
 *       This is useful for cases like datastore-writer where data is written minute-wise, so events
 *       from the same minute are sent to the same datastore writer.</li>
 *   <li><b>Round Robin Routing</b>: Event-based routing that distributes events evenly across all
 *       available handlers. This is useful when multiple events arrive simultaneously and need to
 *       be processed in parallel.</li>
 * </ul>
 * <p>
 * The router maintains a mapping between routing keys and router instances, ensuring that events
 * with the same routing key are consistently routed to the same handler. It also supports dynamic
 * scaling by allowing router instances to be added or removed at runtime.
 * <p>
 * Example usage:
 * <pre>
 * // Create and deploy a LocalEventRouter with 4 instances
 * LocalEventRouter router = new LocalEventRouter(
 *     "metric-events",           // Event type
 *     "deviceId",                // Routing key
 *     4,                         // Number of router instances
 *     "com.mindarray.MetricHandler", // Handler class
 *     false,                     // Non-blocking
 *     true,                      // Enable stats
 *     EventRouter.DYNAMIC        // Routing strategy
 * );
 *
 * vertx.deployVerticle(router);
 *
 * // Send an event to be routed
 * JsonObject event = new JsonObject()
 *     .put("deviceId", "device-123")
 *     .put("metric", "cpu-usage")
 *     .put("value", 75.5)
 *     .put("timestamp", System.currentTimeMillis());
 *
 * vertx.eventBus().send("metric-events", event);
 * </pre>
 */
public class LocalEventRouter extends AbstractVerticle
{
    private static final String PROBE = "probe";
    private static final String DEPLOYED_ROUTERS = "deployed.routers";
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions();
    private static final Map<String, JsonObject> CONTEXTS = Map.ofEntries(Map.entry(EventBusConstants.EVENT_AVAILABILITY_CORRELATION,
                    new JsonObject().put(APIConstants.ENTITY_TABLE, DBConstants.TBL_CORRELATION).put(PROBE, new JsonObject().put(DBConstants.FIELD_NAME, AIOpsConstants.CORRELATION_TYPE).put(VALUE, AIOpsConstants.CorrelationType.AVAILABILITY.getName()))),
            Map.entry(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(),
                    new JsonObject().put(APIConstants.ENTITY_TABLE, DBConstants.TBL_DEPENDENCY).put(PROBE, new JsonObject().put(DBConstants.FIELD_NAME, AIOpsConstants.DEPENDENCY_TYPE).put(VALUE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()))),
            Map.entry(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.CROSS_DOMAIN.getName(),
                    new JsonObject().put(APIConstants.ENTITY_TABLE, DBConstants.TBL_DEPENDENCY).put(PROBE, new JsonObject().put(DBConstants.FIELD_NAME, AIOpsConstants.DEPENDENCY_TYPE).put(VALUE, AIOpsConstants.DependencyType.CROSS_DOMAIN.getName()))));
    private final Logger logger;
    //router...
    private final Map<Object, String> routes = new HashMap<>();
    private final Map<String, Integer> routers = new HashMap<>();
    private final String eventType;
    private final String routingKey;
    private final boolean blocking;
    private final String clazz;
    private final Map<String, String> deployedRouters = new HashMap<>();
    private final boolean enableStats;
    private int eventRouters;
    private long events = 0L;
    private boolean streaming = false;
    private int routerType = NOT_AVAILABLE;
    private int probes = 0;
    private int assignedRouters = 0;

    public LocalEventRouter(String eventType, String routingKey, int eventRouters, String clazz, boolean blocking)
    {
        this.eventType = eventType;

        this.routingKey = routingKey;

        this.eventRouters = eventRouters >= 0 ? eventRouters : 1;

        this.clazz = clazz;

        this.logger = new Logger(LocalEventRouter.class, MOTADATA_EVENT_BUS, String.format("Local Event Router:(%s)", eventType));

        this.enableStats = false;

        this.blocking = blocking;
    }

    public LocalEventRouter(String eventType, String routingKey, int eventRouters, String clazz, boolean blocking, boolean enableStats)
    {
        this.eventType = eventType;

        this.routingKey = routingKey;

        this.eventRouters = eventRouters >= 0 ? eventRouters : 1;

        this.clazz = clazz;

        this.blocking = blocking;

        this.enableStats = enableStats;

        this.logger = new Logger(LocalEventRouter.class, MOTADATA_EVENT_BUS, String.format("Local Event Router:(%s)", eventType));
    }

    /*
     *  Dynamic Routing: time(second) based routing
     *                   -- route to the other handler at every second
     *                   -- use-case: In datastore-writer we write data minute wise so when same minute data is received it will send to same datastore so
     *                                only one writer creates that file
     *  Round Robin Routing: event based routing
     *                   -- route to the other handler at every event
     *                   -- use-case: pmacct sends all aggregated events to AIOps at the same time.
     *                                if dynamic routing is applied all events will be handled by single vertical. hence using round-robin routing
     * */

    public LocalEventRouter(String eventType, String routingKey, int eventRouters, String clazz, boolean blocking, boolean enableStats, EventRouter routerType)
    {
        this.eventType = eventType;

        this.routingKey = routingKey;

        this.eventRouters = eventRouters >= 0 ? eventRouters : 1;

        this.clazz = clazz;

        this.enableStats = enableStats;

        this.blocking = blocking;

        this.routerType = routerType.getValue();

        this.logger = new Logger(LocalEventRouter.class, MOTADATA_EVENT_BUS, String.format("Local Event Router:(%s)", eventType));
    }


    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        if (CommonUtil.isNullOrEmpty(eventType))
        {
            promise.fail("event type is invalid...");
        }

        if (CommonUtil.isNullOrEmpty(routingKey))
        {
            promise.fail("routing key is invalid...");
        }

        if (CommonUtil.isNullOrEmpty(clazz))
        {
            promise.fail("class name is invalid...");
        }

        var futures = new ArrayList<Future<Void>>();

        for (var eventRouter = 1; eventRouter <= this.eventRouters; eventRouter++)
        {
            var router = eventType + "." + eventRouter;

            routers.put(router, 0);

            var future = Promise.<Void>promise();

            futures.add(future.future());

            if (blocking)
            {
                vertx.deployVerticle(clazz, new DeploymentOptions().setConfig(new JsonObject().put(EventBusConstants.EVENT_ROUTER_CONFIG, EventBusConstants.replace(router)).put(EVENT_TYPE, router)).setThreadingModel(ThreadingModel.WORKER).setWorkerPoolSize(1).setWorkerPoolName("wp." + router), result ->
                {

                    if (result.succeeded())
                    {
                        future.complete();

                        deployedRouters.put(router, result.result());
                    }

                    else
                    {
                        future.fail(result.cause());
                    }
                });

            }

            else
            {

                vertx.deployVerticle(clazz, new DeploymentOptions().setConfig(new JsonObject().put(EventBusConstants.EVENT_ROUTER_CONFIG, EventBusConstants.replace(router)).put(EVENT_TYPE, router)), result ->
                {
                    if (result.succeeded())
                    {
                        future.complete();

                        deployedRouters.put(router, result.result());
                    }

                    else
                    {
                        future.fail(result.cause());
                    }
                });
            }
        }

        //sending event to streaming engine
        if (enableStats)
        {
            vertx.setPeriodic(1000, timer ->
            {

                if (streaming)
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject()
                            .put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.EVENT_ENGINE_STATS.getName()).put(
                                    EVENT_CONTEXT, new JsonObject().put("event.rate", events)
                                            .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                            .put(ENGINE_TYPE, eventType)));
                }

                events = 0;
            });
        }

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                var asyncFuture = Promise.<Void>promise();

                if (CONTEXTS.containsKey(eventType))
                {
                    Bootstrap.configDBService().getOneByQuery(CONTEXTS.get(eventType).getString(APIConstants.ENTITY_TABLE),
                            CONTEXTS.get(eventType).getJsonObject(PROBE),
                            response ->
                            {
                                try
                                {
                                    var oldRouters = new ArrayList<String>();

                                    if (response.succeeded() && response.result().containsKey(DEPLOYED_ROUTERS))
                                    {
                                        response.result().getJsonArray(DEPLOYED_ROUTERS).forEach(router -> oldRouters.add(CommonUtil.getString(router)));

                                        logger.info(String.format("handler result : %s ", response.result()));
                                    }
                                    else
                                    {
                                        logger.info(String.format("failed to get result for %s , reason : %s ", eventType, response.cause()));
                                    }

                                    var newRouters = getRouters(eventType);

                                    logger.info(String.format("new router %s ", newRouters));

                                    logger.info(String.format("old router %s ", oldRouters));

                                    Bootstrap.configDBService().update(CONTEXTS.get(eventType).getString(APIConstants.ENTITY_TABLE),
                                            CONTEXTS.get(eventType).getJsonObject(PROBE),
                                            new JsonObject().put(DEPLOYED_ROUTERS, newRouters),
                                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                            asyncResult -> logger.info(String.format("new deployed routers %s updated successfully in table %s", newRouters.toString(), CONTEXTS.get(eventType).getString(APIConstants.ENTITY_TABLE))));

                                    if (!oldRouters.isEmpty())
                                    {
                                    /*
                                    iterate old routers and if new routers contains old router than fill up that router's cache and remove entry from old routers
                                    now if old routers is not emptier than distribute all remaining routers' data to new deployed router one by one
                                     */
                                        var promises = new ArrayList<Future<JsonObject>>();

                                        var iterator = oldRouters.iterator();

                                        var previousRouters = oldRouters.size();

                                        // if new deployed routers size is greater than previous one then and only save it...for decreased router instances do not need to save
                                        if (newRouters.size() > previousRouters)
                                        {
                                            for (var router : newRouters)
                                            {
                                                if (!oldRouters.contains(router))
                                                {
                                                    save(router);
                                                }
                                            }
                                        }

                                        // read data for same deployed router
                                        while (iterator.hasNext())
                                        {
                                            var router = CommonUtil.getString(iterator.next());

                                            if (newRouters.contains(router))
                                            {
                                                iterator.remove();

                                                var future = Promise.<JsonObject>promise();

                                                promises.add(future.future());

                                                vertx.eventBus().<JsonArray>request(router + ".read", new JsonObject().put(EVENT_TYPE, router), reply -> process(future, reply.result().body(), router));
                                            }
                                        }

                                        //means any router is still remaining that is not deployed on current system startup
                                        if (!oldRouters.isEmpty())
                                        {
                                            var routerIndex = 0;

                                            for (var oldRouter : oldRouters)
                                            {
                                                if (routerIndex >= newRouters.size())
                                                {
                                                    routerIndex = 0;
                                                }

                                                var router = newRouters.get(routerIndex);

                                                routerIndex++;

                                                var future = Promise.<JsonObject>promise();

                                                promises.add(future.future());

                                                vertx.eventBus().<JsonArray>request(router + ".read", new JsonObject().put(EVENT_TYPE, oldRouter), reply ->
                                                {
                                                    process(future, reply.result().body(), router);

                                                    var file = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + EventBusConstants.replace(oldRouter);

                                                    if (vertx.fileSystem().existsBlocking(file))
                                                    {
                                                        vertx.fileSystem().deleteBlocking(file); // delete that old router's data that not deployed on system startup

                                                        logger.info(String.format("file deleted : %s after read from router : %s ", file, router + ".read"));
                                                    }
                                                });
                                            }
                                        }

                                        Future.join(promises).onComplete(asyncResult -> asyncFuture.complete());
                                    }

                                    else
                                    {
                                        getRouters(eventType).forEach(this::save);

                                        asyncFuture.complete();
                                    }
                                }
                                catch (Exception exception)
                                {
                                    logger.error(exception);
                                }

                            });
                }
                else
                {
                    asyncFuture.complete();
                }

                asyncFuture.future().onComplete(response ->
                {
                    vertx.eventBus().<JsonObject>localConsumer(eventType, message ->
                    {
                        try
                        {
                            if (message.body() != null)
                            {
                                var event = message.body().getMap();

                                if (event.get(routingKey) != null)
                                {
                                    var router = EMPTY_VALUE;

                                    if (routerType == EventRouter.ROUND_ROBIN.getValue())
                                    {
                                        if (assignedRouters == eventRouters)
                                        {
                                            assignedRouters = 0;
                                        }

                                        assignedRouters++;

                                        router = eventType + "." + assignedRouters;
                                    }
                                    else
                                    {
                                        var routingValue = routerType == EventRouter.DYNAMIC.getValue() ? CommonUtil.getString(CommonUtil.getInteger(CommonUtil.getLong(event.containsKey(EVENT_TIMESTAMP) ? event.get(EVENT_TIMESTAMP) : event.get(LogEngineConstants.RECEIVED_TIMESTAMP)) % (24 * 60 * 60))) : event.get(routingKey);

                                        router = routes.get(routingValue);

                                        if (router == null)
                                        {
                                            router = eventRouters > 1 ? routers.entrySet().stream().min(Map.Entry.comparingByValue()).get().getKey() : eventType + "." + eventRouters;

                                            routes.put(routingValue, router);

                                            routers.put(router, routers.get(router) + 1);
                                        }
                                    }

                                    if (event.containsKey(EVENT_REPLY) && CommonUtil.getString(event.get(EVENT_REPLY)).equalsIgnoreCase(YES))
                                    {
                                        // when any event takes more than 5 minutes to run, we need to pass request timeout in the event.
                                        // currently in runbook we're passing request timeout which is equal to timeout of the runbook, as some runbook takes >=10 minutes to complete
                                        vertx.eventBus().<JsonObject>request(router, new JsonObject(event),
                                                event.containsKey(REQUEST_TIMEOUT) && CommonUtil.getLong(event.get(REQUEST_TIMEOUT)) > 0 ? DELIVERY_OPTIONS.setSendTimeout(TimeUnit.SECONDS.toMillis(CommonUtil.getLong(event.get(REQUEST_TIMEOUT)))) : DELIVERY_OPTIONS.setSendTimeout(300000L), reply ->
                                                {
                                                    if (reply.succeeded())
                                                    {
                                                        message.reply(reply.result().body());
                                                    }
                                                    else
                                                    {
                                                        message.fail(NOT_AVAILABLE, reply.cause().getMessage());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(router, message.body());
                                    }

                                    if (enableStats)
                                    {
                                        events++;
                                    }
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }

                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(eventType + ".write", message ->
                    {
                        var asyncFutures = new ArrayList<Future<JsonObject>>();

                        probes++;

                        var publish = new AtomicBoolean(false);

                        if (probes == 5) // do not need to send slave server to sync data on every time...send on every 5th probe so let's say motadata persist dependency on every 5 min then 5*5 = 25 min it will send new synced data to slave server
                        {
                            publish.set(true);

                            probes = 0;
                        }

                        for (var router : getRouters(eventType))
                        {
                            var future = Promise.<JsonObject>promise();

                            asyncFutures.add(future.future());

                            vertx.eventBus().<byte[]>request(router + ".write", null, DELIVERY_OPTIONS.setSendTimeout(300000L), reply -> future.complete());
                        }

                        Future.join(asyncFutures).onComplete(asyncResult -> message.reply(EMPTY_VALUE));

                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(eventType + ".add.context", message ->
                    {
                        var event = message.body();

                        if (event.containsKey(TARGET) && CommonUtil.isNotNullOrEmpty(event.getString(EventBusConstants.ENGINE_TYPE)))
                        {
                            routes.put(event.getValue(TARGET), event.getString(EventBusConstants.ENGINE_TYPE));
                        }
                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(eventType + EventBusConstants.EVENT_QUERY, message ->
                    {
                        if (message.body() != null)
                        {
                            var event = message.body();

                            if (!event.isEmpty())
                            {
                                if (event.containsKey(EVENT_QUERY_BULK) && event.getString(EVENT_QUERY_BULK).equalsIgnoreCase(YES))
                                {
                                    var asyncFutures = new ArrayList<Future<Void>>();

                                    var results = new JsonArray();

                                    for (var router : getRouters(eventType))
                                    {
                                        var future = Promise.<Void>promise();

                                        asyncFutures.add(future.future());

                                        vertx.eventBus().<JsonArray>request(router + EventBusConstants.EVENT_QUERY, event, reply ->
                                        {
                                            results.addAll(reply.result().body());

                                            future.complete();
                                        });
                                    }

                                    Future.join(asyncFutures).onComplete(asyncResult -> message.reply(results));
                                }
                                else if (event.getValue(routingKey) != null)
                                {
                                    var routerName = routes.get(event.getValue(routingKey));

                                    if (routerName != null)
                                    {
                                        vertx.eventBus().<JsonObject>request(routerName + EventBusConstants.EVENT_QUERY, event, reply -> message.reply(reply.result().body()));
                                    }
                                    else
                                    {
                                        message.fail(NOT_AVAILABLE, String.format("invalid routing value %s for %s", event.getValue(routingKey), eventType));
                                    }
                                }
                                else
                                {
                                    message.fail(NOT_AVAILABLE, String.format("invalid query for %s", eventType));
                                }
                            }
                            else
                            {
                                message.fail(NOT_AVAILABLE, String.format("invalid query for %s", eventType));
                            }
                        }
                        else
                        {
                            message.fail(NOT_AVAILABLE, String.format("invalid query for %s", eventType));
                        }
                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(eventType + ".add.router", message ->
                    {

                        eventRouters++;

                        var router = eventType + "." + eventRouters;

                        vertx.deployVerticle(clazz, new DeploymentOptions().setConfig(new JsonObject().put(EventBusConstants.EVENT_ROUTER_CONFIG, EventBusConstants.replace(router)).put(EVENT_TYPE, router)), asyncResult ->
                        {

                            if (asyncResult.succeeded())
                            {
                                deployedRouters.put(router, asyncResult.result());

                                routers.put(router, 0);

                                message.reply(router);
                            }

                            else
                            {
                                message.fail(GlobalConstants.NOT_AVAILABLE, asyncResult.cause().getMessage());
                            }
                        });


                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(eventType + ".remove.router", message ->
                    {

                        var router = eventType + "." + eventRouters;

                        eventRouters--;

                        routers.remove(router);

                        routes.entrySet().removeIf(entry -> (router.equalsIgnoreCase(entry.getValue())));

                        vertx.undeploy(deployedRouters.remove(router), asyncResult ->
                        {

                            if (asyncResult.succeeded())
                            {
                                message.reply(router);
                            }

                            else
                            {
                                message.fail(GlobalConstants.NOT_AVAILABLE, asyncResult.cause().getMessage());
                            }

                        });

                    }).exceptionHandler(logger::error);

                    vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        var event = message.body();

                        switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                        {
                            case START_EVENT_STAT_STREAMING -> streaming = true;

                            case STOP_EVENT_STAT_STREAMING -> streaming = false;
                        }

                    }).exceptionHandler(logger::error);

                    promise.complete();
                });
            }

            else
            {
                promise.fail(result.cause());
            }

        });
    }

    private void save(String router)
    {
        try
        {
            var file = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + EventBusConstants.replace(router);

            if (!vertx.fileSystem().existsBlocking(file))
            {
                vertx.fileSystem().createFileBlocking(file);

                vertx.fileSystem().writeFileBlocking(file, Buffer.buffer(EMPTY_VALUE.getBytes()));

                logger.info(String.format("file created and written empty : %s ", file));
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    private void process(Promise<JsonObject> future, JsonArray items, String newRouter)
    {
        future.complete();

        for (var item : items)
        {
            routes.put(item, newRouter);
        }
    }

    private List<String> getRouters(String eventType)
    {
        return routers.keySet().stream().filter(key -> key.startsWith(eventType)).collect(Collectors.toList());
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            var futures = new ArrayList<Future<Void>>();

            for (var deploymentId : deployedRouters.values())
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                vertx.undeploy(deploymentId, result -> future.complete());
            }

            Future.join(futures).onComplete(asyncResult -> promise.complete());
        }
        catch (Exception exception)
        {
            logger.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }
        }
    }
}
