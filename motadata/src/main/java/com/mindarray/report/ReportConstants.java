/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Log:
 *	Date			Author			    Notes
 *  19-Mar-2025		<PERSON><PERSON>		Added Support for Availability Flap Summary Report
 */

package com.mindarray.report;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.eventbus.EventBusConstants.EVENT_REPORT_EXPORT;
import static com.mindarray.visualization.VisualizationConstants.MAX_RECORDS;

public class ReportConstants
{
    public static final String REPORT_ID = "report.id";
    public static final int REPORT_PREVIEW_MAX_RECORDS = MotadataConfigUtil.getReportPreviewMaxRecords();

    protected static final String REQUEST_BATCHES = "request.batches";

    protected static final String ALIAS = "alias";
    protected static final String TEMPLATE = "template";
    protected static final String COLUMN = "column";
    protected static final String REPORT_SCRIPT_TYPE = "report.script.type";
    protected static final String REPORT_SCRIPT = "report.script";
    protected static final String REPORT_TYPE_CUSTOM_SCRIPT = "custom.script";
    protected static final String REPORT_TYPE_CAPACITY_PLANNING = "capacity.planning";

    protected static final String REPORT_TYPE_AVAILABILITY_FLAP_SUMMARY = "availability.flap.summary";
    protected static final String REPORT_TYPE_FORECAST = "forecast";
    protected static final String REPORT_TYPE_UNHEALTHY_MONITORS = "unhealthy.monitors";
    protected static final String THRESHOLD = "threshold";
    protected static final String CONDITION = "condition";
    protected static final String PREDICTION_INDEX = "prediction.index";
    protected static final String REPORT_FORECAST_THRESHOLD_CONDITIONS = "report.forecast.threshold.conditions";
    protected static final String REPORT_FORECAST_COLUMN = "report.forecast.column";
    protected static final String REPORT_CAPACITY_PLANNING_THRESHOLD_CRITERIA = "report.capacity.planning.threshold.criteria";
    protected static final String REPORT_CAPACITY_PLANNING_CRITERIA = "report.capacity.planning.criteria";
    protected static final String REPORT_CAPACITY_PLANNING_COLUMN = "report.capacity.planning.column";
    protected static final String REPORT_CPU_UTILIZATION = "cpu.utilization";
    protected static final String REPORT_MEMORY_UTILIZATION = "memory.utilization";
    protected static final String REPORT_DISK_UTILIZATION = "disk.utilization";
    protected static final String REPORT_CAPACITY_PLANNING_MESSAGE = "%s%s of time %s %s %s%s";
    protected static final String REPORT_CPU_STATS = "cpu.stats";
    protected static final String REPORT_MEMORY_STATS = "memory.stats";
    protected static final String REPORT_DISK_STATS = "disk.stats";
    protected static final String COUNT = "count";
    protected static final String THRESHOLD_COUNT = "threshold.count";
    protected static final String TEST_REQUEST_BATCH_SIZE = "test.request.batch.size";


    /**
     * Method used to limit the row in the Report preview
     * In the Future, we will have row limit in PDF as well.
     *
     * @param event  Event from UI
     * @param widget Widget
     */
    protected static void setMaxRecords(JsonObject event, JsonObject widget)
    {
        if (!event.containsKey(EVENT_REPORT_EXPORT))
        {
            widget.put(MAX_RECORDS, ReportConstants.REPORT_PREVIEW_MAX_RECORDS);
        }
    }

    public enum ReportVisualizationType
    {
        MULTIPLE_WIDGETS("Multi-Widgets"),
        MULTIPLE_TOP_N("Multi-TopN"),
        MULTIPLE_AVAILABILITY("Multi-Availability"),
        MULTIPLE_CAPACITY_PLANNING_REPORT("Multi-Capacity-Planning-Report"),
        FORECAST("Forecast"),
        UNHEALTHY_MONITORS("Unhealthy Monitors"),
        AVAILABILITY_ALERTS("Availability Alerts"),
        DEFAULT("");

        private static final Map<String, ReportVisualizationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(ReportVisualizationType::getName, e -> e)));
        public final String name;

        ReportVisualizationType(String name)
        {
            this.name = name;
        }

        public static ReportVisualizationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
