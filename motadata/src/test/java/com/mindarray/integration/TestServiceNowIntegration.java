/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.integration;

import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestServiceNowIntegration
{
    private static final Logger LOGGER = new Logger(TestServiceNowIntegration.class, INTEGRATION_DIR, "Test ServiceNow Integration");

    private static JsonObject PAYLOAD;

    private static MessageConsumer<JsonObject> messageConsumer;

    private static String ackId;

    private static Long id;

    private static Map<String, Long> profiles;

    private static long credentialProfileId;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var object = ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.LINUX);

            PAYLOAD = new JsonObject().put(SEVERITY, GlobalConstants.Severity.CRITICAL.name())
                    .put(MetricPolicy.POLICY_THRESHOLD, "80")
                    .put(Metric.METRIC_TYPE, "Linux")
                    .put(AIOpsObject.OBJECT_CATEGORY, object.getString(object.getString(AIOpsObject.OBJECT_CATEGORY)))
                    .put(PLUGIN_ID, 88).put(AIOpsConstants.ENTITY_ID, object.getLong(ID)).put(METRIC, "system.cpu.percent")
                    .put(PolicyEngineConstants.POLICY_TYPE, PolicyEngineConstants.PolicyType.STATIC.getName()).put(VALUE, "80.0")
                    .put(PolicyEngineConstants.POLICY_KEY, object.getLong(ID) + SEPARATOR + PolicyEngineConstants.PolicyType.STATIC.getName() + SEPARATOR + "system.cpu.percent")
                    .put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID)).put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                    .put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(PolicyEngineConstants.POLICY_ID, DEFAULT_ID).put(User.USER_NAME, "admin").put(MESSAGE, "system.cpu.percent over xyz was >= 80 during last 5 mins with 1 abnormality occurrences.");

            id = IntegrationProfileConfigStore.getStore().getItemByValue(IntegrationProfile.INTEGRATION_PROFILE_NAME, "DemoServiceNowIntegrationProfile").getLong(ID);

            var field = IntegrationCacheStore.class.getDeclaredField("profiles");

            field.setAccessible(true);

            profiles = (Map<String, Long>) field.get(IntegrationCacheStore.getStore());

            TestAPIUtil.createCredentialProfile(new JsonObject("{\"credential.profile.name\": \"ServiceNow Credential Profile Test 1" + System.currentTimeMillis() + "\",\"credential.profile.protocol\": \"HTTP/HTTPS\",\"credential.profile.context\": {\"username\": \"admin\",\"authentication.type\": \"basic\",\"password\": \"admin\"}\n}"), testContext).onComplete(result ->
            {
                if (result.succeeded())
                {
                    credentialProfileId = result.result();

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(result.cause());

                    LOGGER.error(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();

        PAYLOAD.put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(EventBusConstants.EVENT_ID, CommonUtil.newEventId());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testServiceNowCreateIncident(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                {
                    assertIncidentCreateTestResult(message.body());

                    Assertions.assertTrue(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(ID, id));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testServiceNowUpdateIncident(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("number"));

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("sys_id"));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertTrue(event.containsKey("comments"));

                    Assertions.assertTrue(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(SEVERITY, Severity.MAJOR.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testServiceNowClearIncident(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("number"));

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("sys_id"));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertTrue(event.containsKey("comments"));

                    Assertions.assertTrue(event.containsKey("state"));

                    Assertions.assertEquals("6", event.getString("state"));

                    Assertions.assertFalse(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(SEVERITY, Severity.CLEAR.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testServiceNowReopenIncident(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                {
                    var event = message.body();

                    Assertions.assertTrue(event.containsKey(RESULT));

                    Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("number"));

                    Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("sys_id"));

                    Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

                    Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));

                    Assertions.assertTrue(event.containsKey("comments"));

                    Assertions.assertTrue(event.containsKey("state"));

                    Assertions.assertTrue(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals(id, profiles.get(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                    Assertions.assertEquals("1", event.getString("state"));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(ID, id));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testServiceNowClearIncidentAutoCloseOff(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"integration.profile.context\": { \"category\": \"network\", \"subcategory\": \"ip address\", \"assignment_group\": \"8a5055c9c61122780043563ef53438e3\", \"assigned_to\": \"46d44a23a9fe19810012d100cca80666\", \"impact\": \"1be6c35bd12302104f34ba60d8b7c141\", \"urgency\": \"53e6c35bd12302104f34ba60d8b7c140\", \"business_service\": \"26da329f0a0a0bb400f69d8159bc753d\", \"auto.close.ticket.status\": \"no\" }}");

        TestAPIUtil.put(INTEGRATION_PROFILE_API_ENDPOINT + "/" + id, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(IntegrationProfileConfigStore.getStore(), payload, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
            {
                try
                {
                    if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                    {
                        Assertions.assertFalse(message.body().isEmpty());

                        Assertions.assertFalse(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                        messageConsumer.unregister();

                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }));

            TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.put(SEVERITY, Severity.CLEAR.name()));
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testServiceNowCreateEvent(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"event\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
                    {
                        try
                        {
                            if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                            {
                                assertEventCreateTestResult(message.body());

                                IntegrationCacheStore.getStore().removeProfiles(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID));

                                messageConsumer.unregister();

                                testContext.completeNow();
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }));

                    TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(ID, id));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(300 * 1000)
    void testServiceNowProcessPendingEvents(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/300/\", \"timeout\": 60,  \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    LOGGER.info("response : " + response.bodyAsJsonObject());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
                    {
                        try
                        {
                            if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                            {
                                var result = message.body();

                                LOGGER.info("result : " + result);

                                Assertions.assertTrue(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                Assertions.assertEquals(id, profiles.get(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                Assertions.assertTrue(result.containsKey(MESSAGE));

                                Assertions.assertTrue(result.getString(MESSAGE).contains("Resource not found"));

                                messageConsumer.unregister();

                                var integration = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }}");

                                integration.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialProfileId);

                                TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, integration,
                                        testContext.succeeding(asyncResult -> testContext.verify(() ->
                                                messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", event -> testContext.verify(() ->
                                                {
                                                    if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                                                    {
                                                        var context = event.body();

                                                        assertIncidentCreateTestResult(context);

                                                        ackId = context.getJsonObject(RESULT).getString("number") + HASH_SEPARATOR + context.getJsonObject(RESULT).getString("sys_id");

                                                        messageConsumer.unregister();

                                                        testContext.completeNow();
                                                    }
                                                })))));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }));

                    TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(ID, id).put(SEVERITY, Severity.CRITICAL.name()));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetIntegrationResponse(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_RESPONSE_GET.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(result.containsKey("number"));

                    Assertions.assertTrue(result.containsKey("sys_id"));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_RESPONSE_GET, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(IntegrationEngine.ACK_ID, ackId));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testManualDeclareIncident(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
            {
                try
                {
                    Assertions.assertFalse(message.body().isEmpty());

                    messageConsumer.unregister();

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", event -> testContext.verify(() ->
                    {
                        if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                        {
                            try
                            {
                                assertIncidentCreateTestResult(event.body());

                                Assertions.assertTrue(profiles.containsKey(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                Assertions.assertEquals(id, profiles.get(PAYLOAD.getString(PolicyEngineConstants.POLICY_ID) + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(SEVERITY, Severity.CLEAR.name()));

                                messageConsumer.unregister();

                                testContext.completeNow();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }
                    }));

                    var payload = PAYLOAD.copy();

                    payload.remove(EVENT_TIMESTAMP);

                    payload.remove(PolicyEngineConstants.POLICY_KEY);

                    payload.remove(AIOpsObject.OBJECT_IP);

                    payload.remove(MESSAGE);

                    TestUtil.vertx().eventBus().send(UI_ACTION_DECLARE_INCIDENT, payload.put(ID, id).put(SEVERITY, Severity.CRITICAL.name()).put(SESSION_ID, TestUtil.getSessionId()));

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(SEVERITY, Severity.CLEAR.name()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testServiceNowIntegrationAfterPolicyUpdate(VertxTestContext testContext, TestInfo testInfo)
    {
        var policy = new JsonObject("{ \"policy.name\": \"ServiceNowTestPolicy\", \"policy.type\": \"Metric Threshold\", \"policy.rolling.window\": null, \"policy.scheduled\": \"no\", \"policy.context\": { \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 3 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">\", \"policy.threshold\": \"10\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$-$$$object.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$seveirty$$$ state with value $$$value$$$\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"CLEAR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" }, { \"recipient\": \"user1\", \"type\": \"user\" } ] }, \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } }, \"Renotification\": { \"renotify.acknowledged\": \"yes\", \"CRITICAL\": { \"timer.seconds\": 30, \"recipients\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } }, \"Integration\": { \"id\": " + id + " } } }");

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, policy, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    var policyId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
                    {
                        try
                        {
                            if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                            {
                                Assertions.assertTrue(profiles.containsKey(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                Assertions.assertEquals(id, profiles.get(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + policyId, new JsonObject("{\"policy.actions\": {}}"), testContext.succeeding(result ->
                                        testContext.verify(() ->
                                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), timer ->
                                                {
                                                    Assertions.assertFalse(profiles.containsKey(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                                    TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(SEVERITY, Severity.CLEAR.name()));

                                                    messageConsumer.unregister();

                                                    testContext.completeNow();
                                                }))));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }));

                    TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(PolicyEngineConstants.POLICY_ID, policyId).put(SEVERITY, Severity.CRITICAL.name()).put(ID, id));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testServiceNowIntegrationAfterPolicyDelete(VertxTestContext testContext, TestInfo testInfo)
    {
        var policy = new JsonObject("{ \"policy.name\": \"ServiceNowTestPolicy1\", \"policy.type\": \"Metric Threshold\", \"policy.rolling.window\": null, \"policy.scheduled\": \"no\", \"policy.context\": { \"entities\": [], \"metric\": \"system.memory.used.percent\", \"policy.metric.plugins\": [ 3 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">\", \"policy.threshold\": \"10\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$-$$$object.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$seveirty$$$ state with value $$$value$$$\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] }, \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"MAJOR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"WARNING\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ], \"CLEAR\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" }, { \"recipient\": \"user1\", \"type\": \"user\" } ] }, \"Sound\": { \"CRITICAL\": {}, \"MAJOR\": {}, \"CLEAR\": {}, \"WARNING\": {} } }, \"Renotification\": { \"renotify.acknowledged\": \"yes\", \"CRITICAL\": { \"timer.seconds\": 30, \"recipients\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } }, \"Integration\": { \"id\": " + id + " } } }");

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, policy, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    var policyId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                    messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
                    {
                        try
                        {
                            if (message.body().getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName()))
                            {
                                Assertions.assertTrue(profiles.containsKey(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                Assertions.assertEquals(id, profiles.get(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + policyId, testContext.succeeding(result ->
                                        testContext.verify(() ->
                                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), timer ->
                                                {
                                                    Assertions.assertFalse(profiles.containsKey(policyId + SEPARATOR + PAYLOAD.getString(PolicyEngineConstants.POLICY_KEY)));

                                                    messageConsumer.unregister();

                                                    testContext.completeNow();
                                                }))));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }));

                    TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(PolicyEngineConstants.POLICY_ID, policyId).put(SEVERITY, Severity.CRITICAL.name()).put(ID, id));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Timeout(80 * 1000)
    void testServiceNowSyncJobFailure(VertxTestContext testContext)
    {
        var item = IntegrationConfigStore.getStore().getItem(10000000000002L);

        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/400/\", \"timeout\": 60, \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    var count = new AtomicInteger();

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(10), timer ->
                    {
                        var updatedItem = IntegrationConfigStore.getStore().getItem(10000000000002L);

                        LOGGER.info(updatedItem.encode());

                        if (item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES).equals(updatedItem.getJsonObject(Integration.INTEGRATION_ATTRIBUTES)))
                        {
                            Assertions.assertEquals(item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES),
                                    updatedItem.getJsonObject(Integration.INTEGRATION_ATTRIBUTES));

                            TestUtil.vertx().cancelTimer(timer);

                            testContext.completeNow();
                        }
                        else if (count.incrementAndGet() > 3)
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("service now sync failed!");
                        }
                    });
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testServiceNowCreateIncidentInvalidId(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.integration", message -> testContext.verify(() ->
        {
            try
            {
                Assertions.assertTrue(message.body().containsKey(MESSAGE));

                Assertions.assertTrue(message.body().getString(MESSAGE).contains("invalid integration"));

                messageConsumer.unregister();

                testContext.completeNow();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(EVENT_INTEGRATION, PAYLOAD.copy().put(PolicyEngineConstants.POLICY_ID, 1234L).put(ID, 1234L));
    }

    private void assertIncidentCreateTestResult(JsonObject event)
    {
        Assertions.assertTrue(event.containsKey(RESULT));

        Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

        Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("number"));

        Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("sys_id"));

        Assertions.assertTrue(event.containsKey("caller_id"));

        Assertions.assertTrue(event.containsKey("short_description"));

        Assertions.assertTrue(event.containsKey("description"));

        Assertions.assertTrue(event.containsKey("category"));

        Assertions.assertTrue(event.containsKey("subcategory"));

        Assertions.assertTrue(event.containsKey("assignment_group"));

        Assertions.assertTrue(event.containsKey("assigned_to"));

        Assertions.assertTrue(event.containsKey("impact"));

        Assertions.assertTrue(event.containsKey("urgency"));

        Assertions.assertTrue(event.containsKey("business_service"));

        Assertions.assertTrue(event.containsKey(STATUS));

        Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

        Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));
    }

    private void assertEventCreateTestResult(JsonObject event)
    {
        Assertions.assertTrue(event.containsKey(RESULT));

        Assertions.assertFalse(event.getJsonObject(RESULT).isEmpty());

        Assertions.assertTrue(event.getJsonObject(RESULT).containsKey("source"));

        Assertions.assertTrue(event.containsKey("caller_id"));

        Assertions.assertTrue(event.containsKey("message_key"));

        Assertions.assertTrue(event.containsKey("description"));

        Assertions.assertTrue(event.containsKey("node"));

        Assertions.assertTrue(event.containsKey("resource"));

        Assertions.assertTrue(event.containsKey("source"));

        Assertions.assertTrue(event.containsKey("metric_name"));

        Assertions.assertTrue(event.containsKey("type"));

        Assertions.assertTrue(event.containsKey("resolution_state"));

        Assertions.assertTrue(event.getString("resolution_state").equalsIgnoreCase("Closing"));

        Assertions.assertEquals(0, event.getInteger(SEVERITY));

        Assertions.assertTrue(event.containsKey(STATUS));

        Assertions.assertEquals(STATUS_SUCCEED, event.getString(STATUS));

        Assertions.assertTrue(event.containsKey(Integration.INTEGRATION_TYPE));
    }
}
