/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.HashSet;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class MetricPolicy extends AbstractAPI
{
    public static final String POLICY_CONDITION = "policy.condition";
    public static final String THRESHOLD_TYPE = "threshold.type";
    public static final String POLICY_THRESHOLD = "policy.threshold";
    public static final String POLICY_TRIGGER_OCCURRENCES = "policy.trigger.occurrences";
    public static final String POLICY_TRIGGER_TIME = "policy.trigger.time";
    public static final String POLICY_RENOTIFY = "policy.renotify";
    public static final String POLICY_AUTO_CLEAR_TIMER_SECONDS = "policy.auto.clear.timer.seconds";
    public static final String POLICY_UNPROVISION_OBJECT_TIMER_SECONDS = "policy.unprovision.object.timer.seconds";
    private static final Logger LOGGER = new Logger(MetricPolicy.class, MOTADATA_API, "Metric Policy API");

    public MetricPolicy()
    {
        super("metric-policies", MetricPolicyConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update  policy state API
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
            {
                var columns = reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS);

                var item = MetricPolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).getJsonObject(POLICY_CONTEXT);

                var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

                var items = new JsonArray();

                if (item.getJsonArray(ENTITIES) == null || item.getJsonArray(ENTITIES).isEmpty())
                {
                    if (columns.containsKey(item.getString(METRIC)))
                    {
                        items = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, ObjectConfigStore.getStore().getItemsByPlugin(columns.getJsonObject(item.getString(METRIC)).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS), true)));
                    }

                    else if (item.getString(METRIC).equalsIgnoreCase(STATUS))
                    {
                        items = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, new JsonArray(new ArrayList<>(ObjectStatusCacheStore.getStore().getItems().keySet()))));
                    }
                }

                else
                {

                    if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.OBJECT.getName()))
                    {
                        items = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(ENTITIES)));
                    }
                    else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.GROUP.getName()))
                    {
                        items = ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(ENTITIES), userGroups)));
                    }
                    else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                    {
                        var entities = TagConfigStore.getStore().getIdsByItems(item.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                        var counts = new HashSet<Integer>();

                        var objects = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS, new JsonArray(entities));

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var id = objects.getJsonObject(index).getInteger(AIOpsObject.OBJECT_ID);

                            if (counts.add(id))
                            {
                                items.add(ObjectConfigStore.getStore().getItemByObjectId(id));
                            }
                        }

                        var tags = TagCacheStore.getStore().getInstanceTagIds();

                        for (var entity : entities)
                        {
                            for (var entry : tags.entrySet())
                            {
                                if (entry.getValue().contains(TagConfigStore.getStore().getTag(CommonUtil.getLong(entity))) && counts.add(entry.getKey()))
                                {
                                    items.add(ObjectConfigStore.getStore().getItemByObjectId(entry.getKey()));
                                }
                            }
                        }
                    }
                }

                promise.complete(response.put(Entity.OBJECT.getName(), items));
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return promise.future();
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
            {
                try
                {
                    var userGroups = new JsonArray();

                    if (response.containsKey(USER_ID))
                    {
                        userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));
                    }

                    var columns = reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS);

                    var ids = MetricPolicyConfigStore.getStore().getIds();

                    for (var index = 0; index < ids.size(); index++)
                    {
                        try
                        {
                            var item = MetricPolicyConfigStore.getStore().getItem(ids.getLong(index)).getJsonObject(POLICY_CONTEXT);

                            if (item.containsKey(METRIC))
                            {
                                if (item.getJsonArray(ENTITIES) == null || item.getJsonArray(ENTITIES).isEmpty())
                                {
                                    if (columns.containsKey(item.getString(METRIC)))
                                    {
                                        try
                                        {

                                            response.put(CommonUtil.getString(ids.getLong(index)), ObjectConfigStore.getStore().getIdsByGroups(userGroups, ObjectConfigStore.getStore().getItemsByPlugin(columns.getJsonObject(item.getString(METRIC)).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS), true)).size());
                                        }

                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);
                                        }
                                    }

                                    else if (item.getString(METRIC).equalsIgnoreCase(STATUS))
                                    {
                                        response.put(CommonUtil.getString(ids.getLong(index)), ObjectConfigStore.getStore().getIdsByGroups(userGroups, new JsonArray(new ArrayList<>(ObjectStatusCacheStore.getStore().getItems().keySet()))).size());
                                    }
                                }

                                else
                                {
                                    if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.OBJECT.getName()))
                                    {
                                        response.put(CommonUtil.getString(ids.getLong(index)), ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(ENTITIES)).size());
                                    }
                                    else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.GROUP.getName()))
                                    {
                                        response.put(CommonUtil.getString(ids.getLong(index)), ObjectConfigStore.getStore().getItemsByGroups(GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(ENTITIES), userGroups)).size());
                                    }
                                    else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                                    {
                                        var entities = TagConfigStore.getStore().getIdsByItems(item.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                                        var counts = new HashSet<>();

                                        var objects = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS, new JsonArray(entities));

                                        for (var i = 0; i < objects.size(); i++)
                                        {
                                            counts.add(objects.getJsonObject(i).getInteger(AIOpsObject.OBJECT_ID));
                                        }

                                        var tags = TagCacheStore.getStore().getInstanceTagIds();

                                        for (var entity : entities)
                                        {
                                            for (var entry : tags.entrySet())
                                            {
                                                if (entry.getValue().contains(TagConfigStore.getStore().getTag(CommonUtil.getLong(entity))))
                                                {
                                                    counts.add(entry.getKey());
                                                }
                                            }
                                        }

                                        response.put(CommonUtil.getString(ids.getLong(index)), counts.size());
                                    }
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    promise.complete(response);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var items = MetricPolicyConfigStore.getStore().getItems();

        var requestBody = routingContext.body().asJsonObject();

        var valid = true;

        // Need to check manually as we are not deleting policy just archiving so if that policy is archived so user can create policy with same archived one..
        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if ((!item.containsKey(POLICY_ARCHIVED) || item.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)) && item.getString(POLICY_NAME).equalsIgnoreCase(requestBody.getString(POLICY_NAME)))
            {
                valid = false;

                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME)));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME));

                break;
            }
        }

        if (valid)
        {
            promise.complete(requestBody.put(POLICY_ARCHIVED, NO).put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()).put(POLICY_STATE, YES));
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return Future.succeededFuture(routingContext.body().asJsonObject().put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()));
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var table = schema.getString(APIConstants.ENTITY_TABLE);

                this.beforeDelete(routingContext).compose(parameters ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                var validRequest = true;

                                if (item.getString(DBConstants.FIELD_TYPE) != null && item.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
                                {
                                    validRequest = false;

                                    var message = String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, schema.getString(APIConstants.ENTITY_NAME));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.MESSAGE, message).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                    promise.fail(message);

                                    Bootstrap.vertx().eventBus().send(EVENT_AUDIT,
                                            new JsonObject().put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(ENTITY_TABLE, table)
                                                    .put(REQUEST, REQUEST_DELETE).put(MESSAGE, message).put(STATUS, Boolean.FALSE));

                                }

                                if (validRequest)
                                {
                                    item.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray());

                                    Bootstrap.configDBService().update(table,
                                            new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                            item.put(POLICY_ARCHIVED, YES),
                                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                            result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (!result.result().isEmpty())
                                                    {
                                                        this.configStore.updateItem(id);

                                                        promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));

                                                    }

                                                    else
                                                    {
                                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_FAIL));

                                                        promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN));
                                                    }
                                                }

                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                    promise.fail(result.cause());

                                                }
                                            });
                                }
                            }

                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.METRIC_POLICY.getName()))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.METRIC_POLICY.getName()));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_POLICY.name()));

        return Future.succeededFuture();
    }

    private void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(POLICY_STATE);

        var policyId = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(DBConstants.TBL_METRIC_POLICY, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, policyId),
                    new JsonObject().put(POLICY_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            MetricPolicyConfigStore.getStore().updateItem(policyId).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, policyId).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));
                                }
                            });

                          /*  //disable policy notification order to clear its cache will ne confirm the same whether its required or not
                            if (state.equalsIgnoreCase(NO))
                            {
                                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,new JsonObject().put(ID,policyId).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,EventBusConstants.ChangeNotificationType.DISABLE_POLICY.name()));
                            }*/

                            this.send(routingContext, new JsonObject().put(MESSAGE, MetricPolicyConfigStore.getStore().getItem(policyId).getString(POLICY_NAME) + (state.equalsIgnoreCase(YES) ? " Enabled" : " Disabled") + " successfully...").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, policyId));
                        }

                        else
                        {

                            this.send(routingContext, new JsonObject().put(MESSAGE, "Failed to " + (state.equalsIgnoreCase(YES) ? "Enable" : "Disable") + " policy " + MetricPolicyConfigStore.getStore().getItem(policyId).getString(POLICY_NAME)).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, policyId));
                        }
                    });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
