/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *
 *    Date          Author              Notes
 *    2025-02-28   <PERSON><PERSON>      Added Functionality for AGG FILTER as well as Custom Join Query
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.compliance.ComplianceConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationComplianceManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationComplianceManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Compliance Manager");

    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();//queryId along with subqueryIds and status

    private final Map<Long, JsonObject> subQueries = new HashMap<>(); //subquery context

    private final Map<Long, JsonObject> queryContexts = new HashMap<>(); //query original request context

    private final Map<Long, String> queries = new HashMap<>(); //queryid

    private final Map<Long, Short> queryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<Long, Short>> subQueryTrackers = new HashMap<>();//query progress tracker

    private final List<Long> runningQueries = new ArrayList<>();

    private final List<Long> queuedQueries = new ArrayList<>();

    private final Set<Integer> objectIds = new HashSet<>();

    private final Map<Long, JsonObject> queuedQueryContexts = new HashMap<>();

    private final Map<Long, Integer> queryTickers = new HashMap<>();//query timer after time complete query not completed removing its context

    private final StringBuilder builder = new StringBuilder();

    private static void validateFilters(JsonObject filters)
    {
        if (filters != null && !filters.isEmpty())
        {
            var filter = filters.getJsonObject(DATA_FILTER);

            if (filter != null && !filter.isEmpty() && filter.containsKey(CONDITION_GROUPS))
            {
                for (var i = 0; i < filter.getJsonArray(CONDITION_GROUPS).size(); i++)
                {
                    var conditions = filter.getJsonArray(CONDITION_GROUPS).getJsonObject(i).getJsonArray(CONDITIONS);

                    for (var j = 0; j < conditions.size(); j++)
                    {
                        var condition = conditions.getJsonObject(j);

                        if (condition.getString(OPERAND).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName().toLowerCase()))
                        {
                            condition.put(OPERAND, AIOpsObject.OBJECT_ID);

                            condition.put(VALUE, ObjectConfigStore.getStore().getObjectIdById(condition.getLong(VALUE)));
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds the object IDs from the records map to the compositeResult JsonObject.
     *
     * @param result  The JsonObject to which the object IDs will be added.
     * @param records A map where the key is a Long representing the compliance rule ID,
     *                and the value is a JsonArray containing the object IDs that comply with the rule.
     */
    private static void setObjectIds(JsonObject result, Map<Long, Set<Integer>> records)
    {
        result.put(AIOpsObject.OBJECT_ID, new JsonArray(records.values()
                .stream()
                .flatMap(Collection::stream) // Flatten and cast elements
                .collect(Collectors.toList())));
    }

    /**
     * Populates the compositeResult JsonObject with compliance rule IDs and their corresponding object count.
     *
     * @param result  The JsonObject to which the compliance rule IDs and counts will be added.
     * @param records A map where the key is a Long representing the compliance rule ID,
     *                and the value is a JsonArray containing the object IDs that comply with the rule.
     */
    private static void setRuleCounts(JsonObject result, Map<Long, Set<Integer>> records)
    {
        var ids = result.getJsonArray(ComplianceConstants.COMPLIANCE_RULE_ID);

        result.put("count", new JsonArray());

        var counts = result.getJsonArray("count");

        for (var entry : records.entrySet())
        {
            ids.add(entry.getKey());

            counts.add(entry.getValue().size());
        }
    }

    /**
     * Populates the complianceByObjects map with object IDs that match the specified status.
     * Removes entries from complianceByObjects if their status does not match the specified status.
     *
     * @param records A map where the key is a Long representing the compliance rule ID,
     *                and the value is a JsonArray containing the object IDs that comply with the rule.
     * @param result  The result of the asynchronous operation containing the compliance data.
     * @param status  The status to filter the compliance objects by.
     */
    private static void setRuleStatuses(Map<Long, Set<Integer>> records, JsonObject result, String status)
    {
        var statusRecords = new HashMap<Long, String>();

        var ids = result.getJsonArray(AIOpsObject.OBJECT_ID);

        var ruleIds = result.getJsonArray(ComplianceConstants.COMPLIANCE_RULE_ID);

        var statuses = result.getJsonArray(ComplianceConstants.CURRENT_SCAN_STATUS);

        for (var i = 0; i < ids.size(); i++)
        {
            records.computeIfAbsent(ruleIds.getLong(i), value -> new HashSet<>()).add(ids.getInteger(i));

            if (status.equalsIgnoreCase(STATUS_SUCCEEDED))
            {
                if (!statuses.getString(i).equalsIgnoreCase(status))
                {
                    statusRecords.put(ruleIds.getLong(i), statuses.getString(i));
                }

                if (statusRecords.containsKey(ruleIds.getLong(i)))
                {
                    records.remove(ruleIds.getLong(i));
                }
            }
        }
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == BootstrapType.COLLECTOR)
            {
                promise.fail("failed to start visualization compliance manager, reason: invalid boot sequence...");
            }
            else
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
                {
                    if (reply.succeeded())
                    {
                        var ids = ObjectConfigStore.getStore().getObjectIds();

                        for (var i = 0; i < ids.size(); i++)
                        {
                            objectIds.add(ids.getInteger(i));
                        }
                        //invalid queries kindly remove it as response already received or query aborted
                        vertx.setPeriodic(30 * 1000L, periodicTimer ->
                        {
                            var iterator = queryTickers.entrySet().iterator();

                            while (iterator.hasNext())
                            {
                                var entry = iterator.next();

                                entry.setValue(entry.getValue() - 30);

                                if (entry.getValue() <= 0)
                                {
                                    vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                            .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, entry.getKey()).encode().getBytes()).getBytes()));

                                    if (queryStatuses.containsKey(entry.getKey()))
                                    {
                                        for (var subQueryId : queryStatuses.get(entry.getKey()).keySet())
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));

                                            Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));
                                        }
                                    }

                                    iterator.remove();
                                }
                            }

                            if (runningQueries.removeIf(query -> !queries.containsKey(query)) && !queuedQueries.isEmpty())
                            {
                                send();
                            }
                        });

                        vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                        {
                            try
                            {

                                var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                                var widgetId = 0L;

                                var queryId = buffer.getLongLE(0);

                                var subQueryId = buffer.getLongLE(8);

                                var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                                var subQueryProgress = buffer.getUnsignedByte(16);

                                var status = buffer.getUnsignedByte(33);

                                if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                                {
                                    var succeeded = true;

                                    short queryProgress = 0;

                                    subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).put(subQueryId, subQueryCalculatedProgress);

                                    if (queryStatuses.get(queryId).size() > 1)
                                    {
                                        queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                        buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                        queryTrackers.put(queryId, queryProgress);
                                    }

                                    else
                                    {
                                        queryTrackers.put(queryId, subQueryCalculatedProgress);
                                    }

                                    if (queries.containsKey(queryId))
                                    {
                                        var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                        if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                        {
                                            widgetId = CommonUtil.getLong(tokens[0]);
                                        }
                                    }

                                    if (status == 0)//fail
                                    {
                                        var errorLength = 38 + buffer.getIntLE(34);

                                        var errorMessage = buffer.getString(38, errorLength);

                                        if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                        {
                                            succeeded = false;
                                        }

                                        queryStatuses.get(queryId).put(subQueryId, errorMessage);

                                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                                    }
                                    else
                                    {
                                        queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                                    }

                                    EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                                    if (subQueries.containsKey(subQueryId) || queryContexts.containsKey(queryId))
                                    {
                                        var context = subQueries.containsKey(subQueryId) ? subQueries.get(subQueryId) : queryContexts.get(queryId);

                                        if (context.containsKey(EVENT_TYPE))
                                        {
                                            var publish = queryTrackers.get(queryId) >= 100;

                                            if (context.containsKey(PUBLISH_SUB_QUERY_PROGRESS) && context.getBoolean(PUBLISH_SUB_QUERY_PROGRESS))
                                            {
                                                publish = true;
                                            }

                                            if (publish)//if require event to be published to other events will be publishing it
                                            {
                                                if (context.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(VISUALIZATION_DECODE_RESPONSE).equalsIgnoreCase(YES))
                                                {
                                                    vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId), queryContexts.get(queryId).getBoolean(DISCARD_DUMMY_ROWS, true))));
                                                }
                                                else
                                                {
                                                    vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, buffer.getBytes()));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //in some widgets like hardware sensor UI needs data only after its subquery progress is 100 otherwise its not able to merge multiple subqueries into single
                                            if (((queryContexts.get(queryId) != null && !queryContexts.get(queryId).containsKey(PUBLISH_SUB_QUERY_PROGRESS)) || subQueryProgress == 100))
                                            {
                                                vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                            }

                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, false, queryContexts.get(queryId), true, true));
                                            }
                                        }
                                    }

                                    if (subQueryProgress == 100)
                                    {
                                        subQueries.remove(subQueryId);
                                    }

                                    if (queryTrackers.get(queryId) >= 100)
                                    {
                                        runningQueries.remove(queryId);

                                        if (!queuedQueries.isEmpty())
                                        {
                                            send();
                                        }

                                        queryStatuses.get(queryId).forEach((key, value) ->
                                        {
                                            if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                builder.append(value).append(NEW_LINE);
                                            }
                                        });

                                        if (succeeded)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                        }

                                        cleanUp(queryId);

                                        if (CommonUtil.traceEnabled())
                                        {
                                            //as of now dumping trace log will be having query explorer to track each and every query
                                            LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                                    .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("query.contexts", queryContexts.size())
                                                    .put("queries", queries.size()));
                                        }

                                        builder.setLength(0);
                                    }
                                }
                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                        });

                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                        {
                            var event = message.body();

                            switch (ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                            {
                                case ADD_OBJECT ->
                                        objectIds.add(ObjectConfigStore.getStore().getObjectIdById(event.getLong(ID)));

                                case DELETE_OBJECT ->
                                {
                                    if (!event.containsKey(OBJECTS) || event.getValue(OBJECTS) == null)
                                    {
                                        objectIds.remove(event.getInteger(AIOpsObject.OBJECT_ID));
                                    }
                                }

                                default ->
                                {
                                    // do nothing
                                }
                            }
                        }).exceptionHandler(LOGGER::error);

                        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
                        {
                            var queryId = new AtomicLong();

                            try
                            {
                                var currentTime = System.currentTimeMillis();

                                var event = message.body();

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Visualization Compliance Request received:" + event);
                                }

                                queryId.set(event.getLong(VisualizationConstants.QUERY_ID));

                                var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                                var adminRole = false;

                                if (item.getLong(ID).equals(DEFAULT_ID))//default
                                {
                                    item.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));

                                    adminRole = true;
                                }

                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                        .put(EventBusConstants.EVENT_ID, queryId.get())
                                        .put(USER_NAME, event.getString(USER_NAME))
                                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_VISUALIZATION));

                                EventBusConstants.updateEvent(queryId.get(),
                                        String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()),
                                        EventBusConstants.EVENT_STATE_RUNNING);

                                var filters = event.containsKey(FILTERS) && !event.getJsonObject(FILTERS).isEmpty() ? JsonObject.mapFrom(event.remove(FILTERS)) : new JsonObject();

                                var dataSources = new JsonArray().addAll((JsonArray) event.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                                if (!dataSources.isEmpty())
                                {
                                    var buffer = Buffer.buffer();

                                    var visualizationDataSource = dataSources.getJsonObject(0);

                                    var entities = new HashMap<String, Object>();

                                    var columns = new HashMap<String, JsonArray>();

                                    var error = EMPTY_VALUE;

                                    var subQueryId = event.getLong(SUB_QUERY_ID);

                                    queryContexts.put(queryId.get(), new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));

                                    queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(subQueryId, EMPTY_VALUE);

                                    if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                                    {
                                        event.remove(VisualizationConstants.VISUALIZATION_TIMELINE);

                                        DateTimeUtil.buildTimeline(visualizationDataSource, event, item);

                                        event.put(VisualizationConstants.VISUALIZATION_TIMELINE, visualizationDataSource.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE));

                                        visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_TIMELINE);
                                    }

                                    if (!filters.isEmpty())
                                    {
                                        visualizationDataSource.put(FILTERS, filters);
                                    }

                                    validateFilters(visualizationDataSource.getJsonObject(FILTERS));//for safe side if filter size gets > 3 so will be removing extra groups as DB not able to handle

                                    switch (VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                    {
                                        case COMPLIANCE_TRAIL, COMPLIANCE_STATS_POLICY, COMPLIANCE_STATS_ENTITY ->
                                        {
                                            var visualizationDataPoints = new JsonArray();

                                            var dataPoints = visualizationDataSource.getJsonArray(DATA_POINTS);

                                            for (var i = 0; i < dataPoints.size(); i++)
                                            {
                                                if (visualizationDataPoints.size() < 14)
                                                {
                                                    var visualizationDataPoint = dataPoints.getJsonObject(i);

                                                    if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                                    {
                                                        visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                                        visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                                    }

                                                    if (!visualizationDataPoint.getString(DATA_POINT).isEmpty())
                                                    {
                                                        columns.computeIfAbsent(DBConstants.COLUMNS, value -> new JsonArray());

                                                        if (!visualizationDataPoint.getString(AGGREGATOR).isEmpty())
                                                        {
                                                            if (visualizationDataPoint.containsKey(AGGREGATOR + DOT_SEPARATOR + FILTER) && !visualizationDataPoint.getString(AGGREGATOR + DOT_SEPARATOR + FILTER).isEmpty())
                                                            {
                                                                columns.get(DBConstants.COLUMNS).add(visualizationDataPoint.getString(AGGREGATOR) + "(" + visualizationDataPoint.getString(AGGREGATOR + DOT_SEPARATOR + FILTER) + " " + visualizationDataPoint.getString(DATA_POINT) + ")");
                                                            }
                                                            else
                                                            {
                                                                columns.get(DBConstants.COLUMNS).add(visualizationDataPoint.getString(AGGREGATOR) + "(" + visualizationDataPoint.getString(DATA_POINT) + ")");
                                                            }
                                                        }
                                                        else
                                                        {
                                                            columns.get(DBConstants.COLUMNS).add(visualizationDataPoint.getString(DATA_POINT));
                                                        }
                                                    }

                                                    if (visualizationDataPoint.containsKey(DATA_POINT_ALIAS))
                                                    {
                                                        columns.computeIfAbsent(DBConstants.COLUMN_ALIASES, value -> new JsonArray());

                                                        if (!visualizationDataPoint.getString(DATA_POINT_ALIAS).isEmpty())
                                                        {
                                                            columns.get(DBConstants.COLUMN_ALIASES).add(visualizationDataPoint.getString(DATA_POINT_ALIAS));
                                                        }
                                                        else
                                                        {
                                                            columns.get(DBConstants.COLUMN_ALIASES).add(EMPTY_VALUE);
                                                        }
                                                    }

                                                    visualizationDataPoints.add(visualizationDataPoint);
                                                }
                                            }

                                            if (!visualizationDataPoints.isEmpty())
                                            {
                                                visualizationDataSource.put(DBConstants.QUERY, buildQuery(visualizationDataSource, columns));

                                                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataPoints);

                                                visualizationDataSource.put(ENTITIES, entities);

                                                visualizationDataSource.put(GROUP_BY, EMPTY_VALUE);
                                            }

                                            else
                                            {
                                                error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                            }
                                        }

                                        default ->
                                        {
                                            // do nothing
                                        }
                                    }

                                    if (error.isEmpty())
                                    {
                                        var subQueryContext = new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonObject().mergeIn(visualizationDataSource)).put(VisualizationConstants.ADMIN_ROLE, adminRole ? YES : NO).put(VisualizationConstants.QUERY_ID, queryId.get()).put(VisualizationConstants.SUB_QUERY_ID, subQueryId);

                                        if (event.containsKey(EVENT_TYPE))
                                        {
                                            queryTickers.put(queryId.get(), INTERVAL_SECONDS);
                                        }

                                        buffer.appendByte(DatastoreConstants.OperationType.DATA_READ.getName()).appendBytes(subQueryContext.encode().getBytes());

                                        EventBusConstants.updateEvent(queryId.get(), String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED, subQueryId, DateTimeUtil.timestamp()));

                                        var queryContext = new JsonObject().mergeIn(subQueryContext).put("cache", subQueryContext.getString(CONTAINER_TYPE, "dashboard").equalsIgnoreCase("Template") || subQueryContext.getString("drill.down.type", "no").equalsIgnoreCase("yes")).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource));

                                        subQueries.put(subQueryId, queryContext);//ui requires same datasource as sent to identify in some custom widgets

                                        if (!event.containsKey(EVENT_TYPE))//as in case its internal request so need not send to VM
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_SUB_QUERY_CONTEXT, queryContext);
                                        }

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace("Visualization Metric Request generated time taken:" + (System.currentTimeMillis() - currentTime) + " ms for query id:" + queryId.get() + " sub query id : " + subQueryId);
                                        }

                                        visualizationDataSource.put(ENTITIES, ObjectConfigStore.getStore().getItemsByObjectIds(entities.keySet().stream().map(CommonUtil::getInteger).collect(Collectors.toList())));

                                        if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName())
                                                || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GRID.getName())
                                                || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.TOP_N.getName()))
                                        {
                                            switch (VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                            {
                                                case COMPLIANCE_TRAIL, COMPLIANCE_STATS_POLICY,
                                                     COMPLIANCE_STATS_ENTITY ->
                                                        Bootstrap.complianceDBService().get(visualizationDataSource.getString(VisualizationConstants.TYPE), visualizationDataSource, result ->
                                                        {
                                                            if (result.succeeded() && result.result() != null)
                                                            {
                                                                if (!result.result().isEmpty())
                                                                {
                                                                    if (event.containsKey(JOIN_TYPE) && event.getString(JOIN_TYPE).equalsIgnoreCase(VisualizationJoinType.JOIN_TYPE_CUSTOM.getName()))
                                                                    {
                                                                        VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), join(event, result.result()), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                                    }
                                                                    else
                                                                    {
                                                                        VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                                }

                                                            }
                                                            else
                                                            {
                                                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), result.cause().getMessage()), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                            }

                                                        });

                                                default ->
                                                {
                                                    // do nothing
                                                }
                                            }
                                        }
                                    }

                                    else
                                    {
                                        VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", error), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);
                                    }
                                }
                                else
                                {
                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), queryId.get(), event.getLong(SUB_QUERY_ID), LOGGER, EVENT_VISUALIZATION_RESPONSE);
                                }

                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });
                    }
                });

                vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                {
                    try
                    {

                        var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                        var widgetId = 0L;

                        var queryId = buffer.getLongLE(0);

                        var subQueryId = buffer.getLongLE(8);

                        var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                        var subQueryProgress = buffer.getUnsignedByte(16);

                        var status = buffer.getUnsignedByte(33);

                        var validResult = true;

                        //there are certain times it happens due to ZMQ 50 percent progress is received after 100 so ignoring 50 percent result
                        if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                        {
                            subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).computeIfAbsent(subQueryId, value -> subQueryProgress);

                            if (subQueryTrackers.get(queryId).get(subQueryId) <= subQueryProgress)
                            {
                                subQueryTrackers.get(queryId).put(subQueryId, subQueryProgress);
                            }
                            else
                            {
                                validResult = false;
                            }
                        }

                        if (validResult && queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                        {
                            var succeeded = true;

                            short queryProgress = 0;

                            subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).put(subQueryId, subQueryCalculatedProgress);

                            if (queryStatuses.get(queryId).size() > 1)
                            {
                                queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                queryTrackers.put(queryId, queryProgress);
                            }

                            else
                            {
                                queryTrackers.put(queryId, subQueryCalculatedProgress);
                            }

                            if (queries.containsKey(queryId))
                            {
                                var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                {
                                    widgetId = CommonUtil.getLong(tokens[0]);
                                }
                            }

                            if (status == 0)//fail
                            {
                                var errorLength = 38 + buffer.getIntLE(34);

                                var errorMessage = buffer.getString(38, errorLength);

                                if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                {
                                    succeeded = false;
                                }

                                queryStatuses.get(queryId).put(subQueryId, errorMessage);

                                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                            }
                            else
                            {
                                queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                            }

                            EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                            if (subQueries.containsKey(subQueryId) || queryContexts.containsKey(queryId))
                            {
                                var context = subQueries.containsKey(subQueryId) ? subQueries.get(subQueryId) : queryContexts.get(queryId);

                                if (context.containsKey(EVENT_TYPE))
                                {
                                    var publish = queryTrackers.get(queryId) >= 100;

                                    if (context.containsKey(PUBLISH_SUB_QUERY_PROGRESS) && context.getBoolean(PUBLISH_SUB_QUERY_PROGRESS))
                                    {
                                        publish = true;
                                    }

                                    if (publish)//if require event to be published to other events will be publishing it
                                    {
                                        if (context.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(VISUALIZATION_DECODE_RESPONSE).equalsIgnoreCase(YES))
                                        {
                                            vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId), queryContexts.get(queryId).getBoolean(DISCARD_DUMMY_ROWS, true))));
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, buffer.getBytes()));
                                        }
                                    }
                                }
                                else
                                {
                                    //in some widgets like hardware sensor UI needs data only after its subquery progress is 100 otherwise its not able to merge multiple subqueries into single
                                    if (((queryContexts.get(queryId) != null && !queryContexts.get(queryId).containsKey(PUBLISH_SUB_QUERY_PROGRESS)) || subQueryProgress == 100))
                                    {
                                        vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                    }

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, false, queryContexts.get(queryId), true, true));
                                    }
                                }
                            }

                            if (subQueryProgress == 100)
                            {
                                subQueries.remove(subQueryId);
                            }

                            if (queryTrackers.get(queryId) >= 100)
                            {
                                runningQueries.remove(queryId);

                                if (!queuedQueries.isEmpty())
                                {
                                    send();
                                }

                                queryStatuses.get(queryId).forEach((key, value) ->
                                {
                                    if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        builder.append(value).append(NEW_LINE);
                                    }
                                });

                                if (succeeded)
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                                }
                                else
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                }

                                if (queryStatuses.get(queryId).size() == 1)
                                {
                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                }

                                cleanUp(queryId);

                                if (CommonUtil.traceEnabled())
                                {
                                    //as of now dumping trace log will be having query explorer to track each and every query
                                    LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                            .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("query.contexts", queryContexts.size())
                                            .put("queries", queries.size()));
                                }

                                builder.setLength(0);
                            }
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                });
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private JsonObject join(JsonObject event, JsonObject result)
    {
        var result1 = new JsonObject();

        var records = new HashMap<Long, Set<Integer>>();

        if (event.containsKey(JOIN_RESULT) && event.getString(JOIN_RESULT).equalsIgnoreCase("rule.passed"))
        {
            setRuleStatuses(records, result, STATUS_SUCCEEDED);

            result1.put(ComplianceConstants.COMPLIANCE_RULE_ID, new JsonArray());

            setRuleCounts(result1, records);
        }
        else if (event.containsKey(JOIN_RESULT) && event.getString(JOIN_RESULT).equalsIgnoreCase("rule.passed.drilldown"))
        {
            setRuleStatuses(records, result, STATUS_SUCCEEDED);

            setObjectIds(result1, records);
        }
        else if (event.containsKey(JOIN_RESULT) && event.getString(JOIN_RESULT).equalsIgnoreCase("rule.failed.drilldown"))
        {
            setRuleStatuses(records, result, STATUS_FAILED);

            setObjectIds(result1, records);
        }

        return result1;
    }

    /*
         Query Builder Function is to build a query depending upon the postgres filters, first it will start with select query and then group by and others.
     */
    private StringBuilder buildQuery(JsonObject visualizationDataSource, Map<String, JsonArray> columns)
    {
        var query = new StringBuilder();

        DBConstants.select(columns, query);

        DBConstants.from(VISUALIZATION_COMPLIANCE_TABLE_MAPPINGS.get(visualizationDataSource.getString(TYPE)), query);

        var filters = applyFilter(visualizationDataSource.getJsonObject(FILTERS));

        if (!filters.isEmpty())
        {
            DBConstants.where(filters, query);
        }

        if (visualizationDataSource.containsKey(VISUALIZATION_RESULT_BY) && !visualizationDataSource.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty())
        {
            DBConstants.groupBy(visualizationDataSource.getJsonArray(VISUALIZATION_RESULT_BY), query);
        }

        return query;
    }

    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queuedQueryContexts.remove(id));
    }

    private void send(long id, JsonObject event)
    {
        if (event != null)
        {
            if (runningQueries.size() == MotadataConfigUtil.getMetricQueryQueueSize())
            {
                queuedQueryContexts.put(id, event);

                queuedQueries.add(id);
            }

            else
            {
                runningQueries.add(id);

                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, event.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
            }
        }
    }

    private StringBuilder applyFilter(JsonObject filters)
    {
        var query = new StringBuilder();

        var filter = filters.getJsonObject(DATA_FILTER);

        if (!filter.isEmpty())
        {
            var operator = filter.getString(OPERATOR);

            for (var i = 0; i < filter.getJsonArray(CONDITION_GROUPS).size(); i++)
            {
                var conditions = filter.getJsonArray(CONDITION_GROUPS).getJsonObject(i).getJsonArray(CONDITIONS);

                for (var j = 0; j < conditions.size(); j++)
                {
                    var condition = conditions.getJsonObject(j);

                    query.append(condition.getString(OPERAND).replace(".", "_")).append(" ").append(condition.getString(OPERATOR)).append(" ");

                    if (condition.getValue(VALUE) instanceof String)
                    {
                        query.append("'").append(condition.getValue(VALUE)).append("'");
                    }
                    else
                    {
                        query.append(condition.getValue(VALUE));
                    }

                    // Check if additional conditions exist. If they do, append the operation (e.g., AND/OR) followed by a space, and then add the new condition as before.
                    if (conditions.size() > j + 1)
                    {
                        query.append(" ").append(operator).append(" ");
                    }
                }

            }
        }

        return query;
    }

    private void cleanUp(long queryId)
    {
        try
        {
            queryTrackers.remove(queryId);

            if (queryStatuses.containsKey(queryId))
            {
                queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
            }

            queries.remove(queryId);

            subQueryTrackers.remove(queryId);

            queryContexts.remove(queryId);

            queryTickers.remove(queryId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
