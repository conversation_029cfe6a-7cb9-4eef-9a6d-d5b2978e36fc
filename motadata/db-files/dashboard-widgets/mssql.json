[{"_type": "0", "id": 10000000001734, "visualization.name": "SQL Databases", "visualization.description": "SQL Databases Azure MsSql", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.databases", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.databases", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "SQL Databases", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.databases.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001735, "visualization.name": "Storage", "visualization.description": "Storage Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.data.storage.used.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.sql.database.storage.size.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.storage.size.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "azure.sql.database.data.storage.used.bytes.avg"}, {"label": "Total", "value": "azure.sql.database.storage.size.bytes.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001736, "visualization.name": "Connections", "visualization.description": "Connections Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.successful.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.sql.database.failed.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.successful.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Failed", "value": "azure.sql.database.failed.connections.avg"}, {"label": "Successful", "value": "azure.sql.database.successful.connections.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001737, "visualization.name": "Session Utilization", "visualization.description": "Session Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.session.utilization.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.session.utilization.percent", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Session Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.session.utilization.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001738, "visualization.name": "Worker Utilization", "visualization.description": "Worker Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.worker.utilization.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.worker.utilization.percent", "icon": {"name": "settings", "placement": "prefix"}}, "header": {"title": "Worker Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.worker.utilization.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001739, "visualization.name": "Process Utilization", "visualization.description": "Process Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "Process Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}}}, "container.type": "dashboard"}, {"id": 10000000001740, "visualization.name": "TOP Instances By CPU Utilization", "visualization.description": "TOP Instances By CPU Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.cpu.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.sql.database.cpu.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001741, "visualization.name": "TOP Instances By Storage Utilization", "visualization.description": "TOP Instances By Storage Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.in.memory.oltp.storage.used.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.sql.database.in.memory.oltp.storage.used.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001742, "visualization.name": "TOP Instances By Session Utilization", "visualization.description": "TOP Instances By Session Utilization Azure MsSql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.session.utilization.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "5", "order": "desc", "column": "azure.sql.database.session.utilization.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001743, "visualization.name": "TOP Instances By Failed Connections", "visualization.description": "TOP Instances By Failed Connections Azure MsSql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.failed.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001744, "visualization.name": "TOP Instances By Firewall Blocked Connections", "visualization.description": "TOP Instances By Firewall Blocked Connections Azure MsSql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.firewall.blocked.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001745, "visualization.name": "TOP Instances By Worker Utilization", "visualization.description": "TOP Instances By Worker Utilization Azure MsSql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.worker.utilization.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]