/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.SystemProcessConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.SYSTEM_PROCESS_API_ENDPOINT;
import static com.mindarray.api.SystemProcess.*;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestSystemProcess
{

    private static final Logger LOGGER = new Logger(TestSystemProcess.class, MOTADATA_API, "Test System Process");
    private static long id;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSystemProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SYSTEM_PROCESS, "Process").put(SYSTEM_PROCESS_APP_TYPE, "Apache HTTP").put(SYSTEM_PROCESS_OS, "Linux");

        TestAPIUtil.post(SYSTEM_PROCESS_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System Process"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllSystemProcess(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SystemProcessConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetSystemProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, id, SystemProcessConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateSystemProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_PROCESS, "Process1").put(SYSTEM_PROCESS_APP_TYPE, "Active Directory");

        TestAPIUtil.put(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "System Process"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteProcess(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SYSTEM_PROCESS_API_ENDPOINT + "/" + id,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SystemProcessConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "System Process"));

                            testContext.completeNow();
                        })));
    }

    //3578 improvement
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateApacheHTTPSystemProcess(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SYSTEM_PROCESS, "Process2").put(SYSTEM_PROCESS_APP_TYPE, "Apache HTTP").put(SYSTEM_PROCESS_OS, "Linux");

        TestAPIUtil.post(SYSTEM_PROCESS_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System Process"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();

                        })));

    }

    //3578 improvement
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateSystemProcessEmptyApplicationType(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_PROCESS, "Process2");

        TestAPIUtil.put(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "System Process"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    //3578 improvement
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetSystemProcessAfterUpdate(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT + "/" + id
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, id, SystemProcessConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            Assertions.assertFalse(response.bodyAsJsonObject().getJsonObject(RESULT).containsKey(SYSTEM_PROCESS_APP_TYPE));

                            testContext.completeNow();
                        })));
    }
}


