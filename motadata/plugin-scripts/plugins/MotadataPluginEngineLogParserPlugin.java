/*
 * Copyright (c) Motadata 2023. All rights reserved.
 */

package plugins;

import io.vertx.core.json.JsonObject;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.List;
import java.util.regex.Pattern;

public class MotadataPluginEngineLogParserPlugin implements LogParserPlugin
{
    private static final List<String> FIELDS = List.of("timestamp", "severity","plugin", "message");

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("dd-MMMM-yyyy hh:mm:ss.SSSSSS a");

    private static final Pattern PATTERN = Pattern.compile("^(\\d+-\\w+-\\d+\\s\\d+:\\d+:\\d+.\\d+\\s\\w+)\\s(\\w+)\\s\\[(.*)\\]:(.*)");

    public String parse(JsonObject event)
    {
        var parsed = false;

        var error = String.format(ERROR_PARSE, ERROR_UNKNOWN);

        var timestamp = event.getLong(EVENT_RECEIVED_TIMESTAMP);

        try
        {
            var matcher = PATTERN.matcher(event.getString(EVENT));

            if (matcher.find())
            {
                parsed = true;

                for (var index = 1; index <= FIELDS.size(); index++)
                {
                    if (matcher.group(index) != null && !matcher.group(index).isEmpty())
                    {
                        event.put(FIELDS.get(index - 1), matcher.group(index).trim());
                    }
                }

                if (event.containsKey(TIMESTAMP) && !event.getString(TIMESTAMP).isEmpty())
                {
                    if (event.containsKey("event.timezone"))
                    {
                        timestamp = (DATE_TIME_FORMATTER.withZone(DateTimeZone.forID(event.getString("event.timezone"))).parseDateTime(event.getString(TIMESTAMP).trim()).getMillis()) / 1000;
                    }
                    else
                    {
                        timestamp = DATE_TIME_FORMATTER.parseDateTime(event.getString(TIMESTAMP).trim()).getMillis() / 1000;
                    }

                    event.put(MESSAGE, event.getString(EVENT).replace(event.getString(TIMESTAMP), "").trim());
                }
            }
            else
            {
                error = String.format(ERROR_PARSE, ERROR_NO_MATCH_FOUND);
            }

        }
        catch (Exception exception)
        {
            error = String.format(ERROR_PARSE, exception.getMessage());
        }

        event.put(TIMESTAMP, timestamp);

        return parsed ? null : error;
    }
}