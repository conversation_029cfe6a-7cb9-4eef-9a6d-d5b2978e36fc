    /*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


    /*
     *   Change Logs:
     *   Date          Author              Notes
     *   2025-02-06    <PERSON><PERSON> <PERSON>      Added Constant.

     */
    package com.mindarray.api;

    import com.mindarray.Bootstrap;
    import com.mindarray.GlobalConstants;
    import com.mindarray.db.DBConstants;
    import com.mindarray.eventbus.EventBusConstants;
    import com.mindarray.store.ComplianceRuleConfigStore;
    import com.mindarray.store.TagConfigStore;
    import com.mindarray.util.Logger;
    import io.vertx.core.Future;
    import io.vertx.core.Promise;
    import io.vertx.core.json.JsonArray;
    import io.vertx.core.json.JsonObject;
    import io.vertx.ext.web.Router;
    import io.vertx.ext.web.RoutingContext;
    import org.apache.http.HttpStatus;

    import static com.mindarray.GlobalConstants.*;
    import static com.mindarray.api.APIConstants.RESPONSE_CODE;

    public class ComplianceRule extends AbstractAPI
    {
        public static final String COMPLIANCE_RULE_NAME = "compliance.rule.name";
        public static final String COMPLIANCE_RULE_CHECK_TYPE = "compliance.rule.check.type";
        public static final String COMPLIANCE_RULE_CONDITIONS = "compliance.rule.conditions";
        public static final String COMPLIANCE_RULE_BLOCK_CRITERIA = "compliance.rule.block.criteria";
        public static final String COMPLIANCE_RULE_BLOCK_START = "compliance.rule.block.start";
        public static final String COMPLIANCE_RULE_BLOCK_END = "compliance.rule.block.end";
        public static final String COMPLIANCE_RULE_CHECK_CATEGORY = "compliance.rule.check.category";
        public static final String COMPLIANCE_RULE_SEVERITY = "compliance.rule.severity";
        public static final String COMPLIANCE_RULE_TAGS = "compliance.rule.tags";
        public static final String COMPLIANCE_RULE_CONTEXT = "compliance.rule.context";
        public static final String COMPLIANCE_RULE_RUNBOOKS = "compliance.rule.runbooks";
        public static final String COMPLIANCE_RULE_COMMAND = "compliance.rule.command";
        private static final Logger LOGGER = new Logger(ComplianceRule.class, MOTADATA_API, "Compliance Rule API");

        protected ComplianceRule()
        {
            super("compliance-rules", ComplianceRuleConfigStore.getStore(), LOGGER);
        }

        @Override
        public void init(Router router)
        {
            try
            {
                super.init(router);

                router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        @Override
        protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
        {
            return qualify(routingContext, APIConstants.REQUEST_CREATE);
        }

        private Future<JsonObject> qualify(RoutingContext routingContext, String requestType)
        {
            var promise = Promise.<JsonObject>promise();

            var requestBody = routingContext.body().asJsonObject();

            if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE))
            {
                requestBody.put(ComplianceRule.COMPLIANCE_RULE_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(ComplianceRule.COMPLIANCE_RULE_TAGS), Tag.TagType.COMPLIANCE.getName(), DBConstants.ENTITY_TYPE_USER));

                promise.complete(requestBody);
            }

            return promise.future();
        }

        @Override
        protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
        {
            return qualify(routingContext, APIConstants.REQUEST_UPDATE);
        }

        @Override
        protected Future<Void> afterGetAll(JsonArray items, RoutingContext routingContext)
        {
            var entities = new JsonArray();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                item.put(COMPLIANCE_RULE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_RULE_TAGS)));

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));

            return Future.succeededFuture();
        }

        @Override
        protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
        {
            try
            {
                item = new JsonObject().mergeIn(item);

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(COMPLIANCE_RULE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_RULE_TAGS)))));
            }

            catch (Exception exception)
            {
                APIUtil.sendResponse(exception, routingContext);
            }

            return Future.succeededFuture();
        }

        @Override
        protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
        {
            this.send(routingContext, entity);

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_COMPLIANCE_RULE.name()));

            return Future.succeededFuture();
        }

        @Override
        protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
        {
            this.send(routingContext, entity);

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_COMPLIANCE_RULE.name()));

            return super.afterDelete(entity, routingContext);
        }

        @Override
        protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
        {
            this.send(routingContext, entity);

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_COMPLIANCE_RULE.name()));

            return Future.succeededFuture();
        }

    }
