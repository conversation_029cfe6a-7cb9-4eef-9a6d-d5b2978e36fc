/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestWirelessObjectPoller
{
    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollCiscoWirelessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_WIRELESS.getName()), testContext), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollCiscoWirelessRougeAPMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_WIRELESS_ROUGE_AP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollCiscoWirelessClientMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_WIRELESS_CLIENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollCiscoWirelessAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollRuckusWirelessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RUCKUS_WIRELESS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollRuckusWirelessClientMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RUCKUS_WIRELESS_CLIENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollRuckusWirelessRogueAPMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RUCKUS_WIRELESS_ROUGE_AP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollRuckusWirelessWLANMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RUCKUS_WIRELESS_WLAN.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPollRuckusWirelessAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollArubaWirelessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ARUBA_WIRELESS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollArubaWirelessRougeAPMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ARUBA_WIRELESS_ROUGE_AP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testPollArubaWirelessClientMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ARUBA_WIRELESS_CLIENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testPollArubaWirelessAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }
}
