/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.datastore;

import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_ID;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_WRITER_MAX_BUFFER_BYTES;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")

public class TestDatastore
{
    private static final Logger LOGGER = new Logger(TestDatastore.class, "db", "Datastore Test");

    public static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        MotadataConfigUtil.loadConfigs(MotadataConfigUtil.getConfigs().put("datastore.file.close.time.seconds", 90));

        var registration = new JsonObject()
                .put(EVENT_TYPE, EVENT_DATASTORE_REGISTER)
                .put(REMOTE_EVENT_PROCESSOR_HOST, "test-case-db-datastore")
                .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.DATASTORE.name())
                .put(REMOTE_EVENT_PROCESSOR_IP, "*************")
                .put(REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore")
                .put(REMOTE_EVENT_PROCESSOR_VERSION, "8.0.1");

        TestUtil.vertx().eventBus().send(EVENT_DATASTORE_REGISTER, registration);

        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Test
    @Order(1)
    void testDatastoreMetricWorker(VertxTestContext testContext) throws Exception
    {
        var timerId = new AtomicLong();

        var context = new JsonObject("{\"_type\":\"1\",\"cli.enabled\":\"no\",\"credential.profile.name\":\"motadata - motadata\",\"credential.profile.protocol\":\"SSH\",\"errors\":[],\"event.id\":25662621045973,\"event.timestamp\":1703843559,\"event.topic\":\"remote.event.processor \",\"id\":25662621112518,\"metric.category\":\"Server\",\"metric.credential.profile\":25662621112478,\"metric.credential.profile.protocol\":\"SSH\",\"metric.discovery.method\":\"REMOTE\",\"metric.name\":\"Linux Network Interface\",\"metric.object\":25662621112517,\"metric.plugin\":\"linuxnetworkinterface\",\"metric.polling.min.time\":600,\"metric.polling.time\":600,\"metric.state\":\"ENABLE\",\"metric.type\":\"Linux\",\"object.business.hour.profile\":10000000000001,\"object.category\":\"Server\",\"object.creation.time\":\"2023/12/29 15:22:33\",\"object.creation.time.seconds\":1703843553,\"object.discovery.method\":\"REMOTE\",\"object.event.processors\":[],\"object.groups\":[10000000000019,10000000000017],\"object.host\":\"motadata12145\",\"object.id\":1,\"object.ip\":\"*************\",\"object.name\":\"motadata12145\",\"object.state\":\"ENABLE\",\"object.target\":\"*************\",\"object.type\":\"Linux\",\"object.user.tags\":[],\"password\":\"motadata\",\"ping.check.status\":\"yes\",\"plugin.engine\":\"go\",\"plugin.id\":94,\"port\":22,\"remote.address\":\"0:0:0:0:0:0:0:1\",\"remote.event.processor.uuid\":\"0A88CDE0C0B5459199D357FBE318A5F0\",\"result\":{\"system.network.interface\":[{\"system.network.interface\":\"ens160\",\"system.network.interface.bytes.rate\":***********,\"system.network.interface.in.bytes.rate\":***********,\"system.network.interface.out.bytes.rate\":5135320169}]},\"ss.bin.path\":\"/bin/ss\",\"status\":\"succeed\",\"timeout\":60,\"ui.event.uuid\":\"e5dae3b0-68ca-4455-a831-0e92163aaee5\",\"user.name\":\"admin\",\"username\":\"motadata\",\"latency.ms\":52428.0,\"error.code\":\"MD000\",\"event.type\":\"metric.poll\",\"copy.required\":true}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.datastore.metric", message ->
        {
            LOGGER.info("Message received.... " + message.body().encodePrettily());

            var result = TestUtil.decodeMetricEnricherBuffer(message.body());

            LOGGER.info(DateTimeUtil.timestamp() + ": RESULT: " + result.encodePrettily());

            if (result.getString(PLUGIN_ID).equalsIgnoreCase("94-linux.network.interface"))
            {
                assertEquals(result.getLong(EVENT_TIMESTAMP), context.getLong(EVENT_TIMESTAMP));

                assertEquals("94-linux.network.interface", result.getString(PLUGIN_ID));

                assertEquals("0", result.getString(DatastoreConstants.DATASTORE_FORMAT));

                assertEquals("1", result.getString(DatastoreConstants.DATASTORE_TYPE));

                assertEquals(1, result.getInteger(OBJECT_ID));

                var networkInterface = context.getJsonObject(RESULT).getJsonArray(NMSConstants.SYSTEM_NETWORK_INTERFACE).getJsonObject(0);

                assertEquals(result.getString(NMSConstants.SYSTEM_NETWORK_INTERFACE), networkInterface.getString(NMSConstants.SYSTEM_NETWORK_INTERFACE));

                assertEquals(result.getString(GlobalConstants.INSTANCE), networkInterface.getString(NMSConstants.SYSTEM_NETWORK_INTERFACE));

                assertEquals(0, result.getInteger(NMSConstants.SYSTEM_NETWORK_INTERFACE + "~" + "out.bytes.per.sec"));

                assertEquals(result.getLong("system.network.interface~out.bytes"), networkInterface.getLong("system.network.interface.out.bytes.rate"));

                assertEquals(0, result.getLong("system.network.interface~bytes.per.sec"));

                assertEquals(result.getLong("system.network.interface~bytes"), networkInterface.getLong("system.network.interface.bytes.rate"));

                assertEquals(0, result.getLong("system.network.interface~in.bytes.per.sec"));

                assertEquals(result.getLong("system.network.interface~in.bytes"), networkInterface.getLong("system.network.interface.in.bytes.rate"));

                assertEquals(result.getString("system.network.interface~instance.name"), networkInterface.getString(NMSConstants.SYSTEM_NETWORK_INTERFACE));

                if (timerId.get() > 0)
                {
                    TestUtil.vertx().cancelTimer(timerId.get());
                }

                testContext.completeNow();
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_DOWN));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENRICHER, context);

        TestUtil.vertx().setPeriodic(5 * 1000, timer ->
                TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + GlobalConstants.DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.METRIC.getName() + ".primary_db_uuid_for_datastore.0.active", new JsonObject().put(DatastoreConstants.DATASTORE_CATEGORY, 0).put(DATASTORE_WRITER_MAX_BUFFER_BYTES, 20971520).put("operation.type", 2).put(HAConstants.HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())));

        TestUtil.vertx().setPeriodic(5 * 1000, timer ->
                TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + GlobalConstants.DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.METRIC.getName() + ".primary_db_uuid_for_datastore.1.active", new JsonObject().put(DatastoreConstants.DATASTORE_CATEGORY, 0).put(DATASTORE_WRITER_MAX_BUFFER_BYTES, 20971520).put("operation.type", 2).put(HAConstants.HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())));

        timerId.set(TestUtil.vertx().setPeriodic(2 * 1000, timer -> TestUtil.vertx().eventBus().send(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_UP))));
    }

    @Test
    @Order(2)
    void testDatastoreEventWorker(VertxTestContext testContext) throws Exception
    {
        var timerId = new AtomicLong();

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.datastore.event", message ->
        {
            LOGGER.info("Message received.... " + message.body().encodePrettily());

            var result = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(CodecUtil.toBytes(message.body().getBinary(EVENT_CONTEXT))));

            if (result.getLong(EVENT_TIMESTAMP) == 1704609289)
            {
                assertEquals("500003-policy", result.getString(PLUGIN_ID));

                assertEquals("1", result.getString(DatastoreConstants.DATASTORE_FORMAT));

                assertEquals("14", result.getString(DatastoreConstants.DATASTORE_TYPE));

                assertEquals("*************", result.getString("source"));

                assertEquals(GlobalConstants.Severity.CLEAR.name(), result.getString(SEVERITY));

                assertEquals("Metric Threshold", result.getString(PolicyEngineConstants.POLICY_TYPE));

                assertEquals("Server", result.getString(AIOpsObject.OBJECT_CATEGORY));

                assertEquals("system.disk.volume~used.percent", result.getString(METRIC));

                assertEquals(0, result.getLong(VALUE));

                assertEquals("policy", result.getString(LogEngineConstants.EVENT_CATEGORY));
            }

            if (timerId.get() > 0)
            {
                TestUtil.vertx().cancelTimer(timerId.get());
            }

            testContext.completeNow();
        });

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), Buffer.buffer(FileUtils.readFileToByteArray(new File(CURRENT_DIR + PATH_SEPARATOR + "test-plugin-metrics/datastore_event_write"))).getBytes());

        timerId.set(TestUtil.vertx().setPeriodic(2 * 1000, timer -> TestUtil.vertx().eventBus().send(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_UP))));
    }

    @Test
    @Order(3)
    void testDatastoreAvailabilityWorker(VertxTestContext testContext) throws Exception
    {
        var timerId = new AtomicLong();

        var bytes = new byte[]{-105, 9, -90, 103, 0, 0, 0, 0, 14, 0, 0, 0, 48, 45, 97, 118, 97, 105, 108, 97, 98, 105, 108, 105, 116, 121, 0, 2, -21, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 109, 101, 116, 114, 105, 99, 14, 0, 0, 0, 109, 111, 110, 105, 116, 111, 114, 46, 117, 112, 116, 105, 109, 101, 0, 6, 0, 0, 0, 115, 116, 97, 116, 117, 115, 2, 0, 0, 0, 85, 80, 0, 6, 0, 0, 0, 114, 101, 97, 115, 111, 110, 2, 0, 0, 0, 85, 112, 1, 8, 0, 0, 0, 100, 117, 114, 97, 116, 105, 111, 110, 60, 0, 0, 0, 0, 0, 0, 0};

        var timestamp = ByteUtil.readLongValue(bytes);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.datastore.availability", message ->
        {
            LOGGER.info("Message received.... " + message.body().encodePrettily());

            var result = message.body();

            if (timestamp == result.getLong(EVENT_TIMESTAMP))
            {
                Assertions.assertArrayEquals(bytes, CodecUtil.toBytes(result.getBinary(EVENT_CONTEXT)));
            }

            testContext.completeNow();
        });

        TestUtil.vertx().eventBus().send(EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.AVAILABILITY.getName(), bytes);

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        timerId.set(TestUtil.vertx().setPeriodic(2 * 1000, timer -> TestUtil.vertx().eventBus().send(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_UP))));
    }

    @Test
    @Order(4)
    void testCloseDatastoreFiles(VertxTestContext testContext)
    {
        LOGGER.info("running : testCloseDatastoreFiles ");

        for (var index = 0; index < 1000; index++)
        {
            TestUtil.vertx().eventBus().publish(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_DOWN));
        }

        var count = new AtomicLong();

        TestUtil.vertx().setPeriodic(10 * 1000L, timer ->
        {
            try
            {
                var content = FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "datastore" + PATH_SEPARATOR + "$$$-Metric Datastore.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date()))), StandardCharsets.UTF_8);

                if (content.contains("closing files"))
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            count.getAndIncrement();

            if (count.get() == 10)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow("failed to close file");
            }
        });

    }

    @Test
    @Order(5)
    void testLoadDatastoreFiles(VertxTestContext testContext)
    {
        LOGGER.info("running : testLoadDatastoreFiles ");

        for (var index = 0; index < 100; index++)
        {
            TestUtil.vertx().eventBus().publish(EVENT_DATASTORE_PING, new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid_for_datastore").put(STATUS, STATUS_UP));
        }

        var count = new AtomicLong();

        TestUtil.vertx().setPeriodic(10 * 1000L, timer ->
        {
            try
            {
                var content = FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + LOGS_DIR + PATH_SEPARATOR + "datastore" + PATH_SEPARATOR + "$$$-Metric Datastore.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date()))), StandardCharsets.UTF_8);

                if (content.contains("loading files"))
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            count.getAndIncrement();

            if (count.get() == 10)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow("failed to load file");
            }
        });
    }
}