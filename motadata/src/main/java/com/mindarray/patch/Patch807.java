/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  3-Mar-2025      Chandresh       LDAP_SERVER_HOST constant was removed hence added "ldap.server.host" string
 */


package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.SystemFile;
import com.mindarray.db.DBConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Scheduler.SCHEDULER_CONTEXT;
import static com.mindarray.api.Scheduler.SCHEDULER_JOB_TYPE;
import static com.mindarray.api.User.*;
import static com.mindarray.db.DBConstants.*;

public class Patch807 implements Patch
{

    private static final Logger LOGGER = new Logger(Patch807.class, MOTADATA_PATCH, "Patch 8.0.7");

    private static final String VERSION = "8.0.7";

    private static final Pattern DRIVE_NAME_PATTERN = Pattern.compile("^[A-Za-z]:\\\\$");

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info(String.format("executing patch %s", VERSION));

            var futures = new ArrayList<Future<Void>>();

            futures.add(updateLDAPUsers()); // Multiple LDAP Server patch to add LDAP Server id in each ldap users

            // C drive patch (MOTADATA-2730)

            futures.add(updateWindowsDirectoryMetric());

            futures.add(deleteSystemDefaultWindowsDirectory());

            var ids = SchedulerConfigStore.getStore().flatItems(SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName(), ID);

            for (var index = 0; index < ids.size(); index++)
            {
                if (Objects.equals(ids.getLong(index), 10000000000012L))
                {
                    continue;
                }

                var future = Promise.<Void>promise();

                try
                {
                    var item = SchedulerConfigStore.getStore().getItem(ids.getLong(index));

                    var layers = item.containsKey(SCHEDULER_CONTEXT) ? item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(NMSConstants.TOPOLOGY_LINK_LAYER) : null;

                    if (CommonUtil.isNotNullOrEmpty(layers))
                    {
                        futures.add(future.future());

                        var protocols = new JsonArray();

                        for (var i = 0; i < layers.size(); i++)
                        {
                            if (layers.getString(i).equalsIgnoreCase(NMSConstants.TopologyLinkLayer.L2.getName()))
                            {
                                protocols.add(NMSConstants.TopologyProtocol.CDP.getName()).add(NMSConstants.TopologyProtocol.LLDP.getName()).add(NMSConstants.TopologyProtocol.SPM.getName());
                            }
                            else
                            {
                                protocols.add(NMSConstants.TopologyProtocol.OSPF.getName()).add(NMSConstants.TopologyProtocol.IS_IS.getName()).add(NMSConstants.TopologyProtocol.BGP.getName());
                            }
                        }

                        item.getJsonObject(SCHEDULER_CONTEXT).put(NMSConstants.TOPOLOGY_PROTOCOLS, protocols);

                        updateSchedulerConfigStore(future, item);
                    }
                    else
                    {
                        LOGGER.warn(String.format("Failed to update scheduler for %s with reason %s", item, "eiter scheduler context not found or topology link layer not found"));

                        future.fail(String.format("Failed to update scheduler for %s with reason %s", item, "eiter scheduler context not found or topology link layer not found"));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    future.fail(exception);
                }
            }

            var items = MetricConfigStore.getStore().flatItems(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ROUTING.getName(), ID);

            LOGGER.info("Total routing protocol metrics are  " + items.size());

            for (var index = 0; index < items.size(); index++)
            {
                var future = Promise.<Void>promise();

                try
                {
                    futures.add(future.future());

                    var item = MetricConfigStore.getStore().getItem(items.getLong(index));

                    item.remove(ID);

                    addNetworkLayerProtocolsMetricGroups(item.copy(), NMSConstants.MetricPlugin.BGP)
                            .compose(handler1 -> addNetworkLayerProtocolsMetricGroups(item.copy(), NMSConstants.MetricPlugin.OSPF))
                            .compose(handler2 -> addNetworkLayerProtocolsMetricGroups(item.copy(), NMSConstants.MetricPlugin.ISIS)).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    future.complete();
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    future.fail(result.cause());
                                }
                            });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    future.fail(exception);
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Patch version 8.0.7 executed successfully");

                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });

            return promise.future();
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private void updateSchedulerConfigStore(Promise<Void> future, JsonObject item)
    {
        Bootstrap.configDBService().update(TBL_SCHEDULER,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                new JsonObject().put(SCHEDULER_CONTEXT, item.getJsonObject(SCHEDULER_CONTEXT)),
                DEFAULT_USER,
                SYSTEM_REMOTE_ADDRESS,
                result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            SchedulerConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.trace(String.format("item updated successfully for scheduler %s", item));

                                    future.complete();
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Failed to update scheduler for %s with reason %s", item, asyncResult.cause()));

                                    future.fail(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update Config DB entry of scheduler for %s with reason %s", item, result.cause()));

                            future.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        future.complete();

                        LOGGER.error(exception);
                    }
                });
    }

    private Future<Void> addNetworkLayerProtocolsMetricGroups(JsonObject metric, NMSConstants.MetricPlugin plugin)
    {
        var future = Promise.<Void>promise();

        try
        {
            var item = ObjectManagerCacheStore.getStore().getItemByMetricPlugin(plugin.getName());

            var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

            if (object != null && MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), plugin.getName()) == NOT_AVAILABLE)
            {
                metric.put(Metric.METRIC_PLUGIN, item.getString(Metric.METRIC_PLUGIN)).put(Metric.METRIC_NAME, item.getString(Metric.METRIC_NAME));

                if (metric.containsKey(Metric.METRIC_CONTEXT))
                {
                    metric.getJsonObject(Metric.METRIC_CONTEXT).put(PLUGIN_ID, ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(plugin.getName()));
                }

                Bootstrap.configDBService().save(DBConstants.TBL_METRIC, metric, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            MetricConfigStore.getStore().addItem(result.result());

                            MetricCacheStore.getStore().addMetric(result.result(), metric.getInteger(Metric.METRIC_POLLING_TIME));

                            LOGGER.info(String.format("instance %s of metric %s of object %s added", object.getString(AIOpsObject.OBJECT_NAME), metric.getString(Metric.METRIC_NAME), metric.getString(Metric.METRIC_NAME)));

                            future.complete();
                        }
                        else
                        {
                            LOGGER.info(String.format("failed to add instance %s of metric %s of object %s", object.getString(AIOpsObject.OBJECT_NAME), metric.getString(Metric.METRIC_NAME), metric.getString(Metric.METRIC_NAME)));

                            future.fail(result.cause().getMessage());
                        }
                    }
                    catch (Exception exception)
                    {
                        future.complete();

                        LOGGER.error(exception);
                    }
                });
            }
            else
            {
                future.complete();
            }
        }
        catch (Exception exception)
        {
            future.complete();

            LOGGER.error(exception);
        }

        return future.future();
    }

    private Future<Void> updateLDAPUsers()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var item = LDAPServerConfigStore.getStore().getItem();

            if (item != null && !item.isEmpty())
            {
                var id = item.getLong(ID);

                if (item.containsKey("ldap.server.host") && CommonUtil.isNotNullOrEmpty("ldap.server.host"))
                {
                    var promises = new ArrayList<Future<Void>>();

                    // MOTADATA-2879
                    var ldapServerPromise = Promise.<Void>promise();

                    promises.add(ldapServerPromise.future());

                    Bootstrap.configDBService().update(DBConstants.TBL_LDAP_SERVER,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, id),
                            item.put(FIELD_TYPE, ENTITY_TYPE_USER),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    LDAPServerConfigStore.getStore().updateItem(id).onComplete(asyncResult -> ldapServerPromise.complete());
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to update ldap server: %s. Possible reason: %s", id, result.cause()));

                                    ldapServerPromise.fail(result.cause());
                                }
                            });

                    var users = UserConfigStore.getStore().getItemsByValue(USER_TYPE, USER_TYPE_LDAP);

                    if (users != null && !users.isEmpty())
                    {
                        for (var index = 0; index < users.size(); index++)
                        {
                            var userPromise = Promise.<Void>promise();

                            promises.add(userPromise.future());

                            var user = users.getJsonObject(index);

                            Bootstrap.configDBService().update(DBConstants.TBL_USER,
                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, user.getLong(ID)),
                                    user.put(USER_LDAP_SERVER, id), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            UserConfigStore.getStore().updateItem(user.getLong(ID)).onComplete(asyncResult -> userPromise.complete());
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("failed to update user: %s. Possible reason: %s", user.getLong(ID), result.cause()));

                                            userPromise.fail(result.cause());
                                        }
                                    });
                        }
                    }

                    Future.all(promises).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            LOGGER.info("Added ID in all LDAP users!");

                            promise.complete();
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update LDAP users. Possible reason: %s", result.cause()));

                            promise.fail(result.cause());
                        }
                    });
                }
                else
                {
                    Bootstrap.configDBService().delete(DBConstants.TBL_LDAP_SERVER,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, id),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                            {
                                if (result.succeeded())
                                {
                                    LOGGER.info("No LDAP sever is configure, deleting default entry");

                                    LDAPServerConfigStore.getStore().deleteItem(id);

                                    promise.complete();
                                }
                                else
                                {
                                    LOGGER.warn("Failed to delete LDAP server, Possible reason : " + result.cause().getMessage());

                                    promise.fail(result.cause());
                                }
                            });
                }
            }
            else
            {
                LOGGER.info("No LDAP server exists!");

                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception.getMessage());
            }
        }

        return promise.future();
    }

    private Future<Void> updateWindowsDirectoryMetric()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DIR.getName());

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                var item = items.getJsonObject(index);

                var id = item.getLong(ID);

                var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                if (context != null && !context.isEmpty() && context.containsKey(NMSConstants.OBJECTS) && CommonUtil.isNotNullOrEmpty(context.getJsonArray(NMSConstants.OBJECTS)))
                {
                    var iterator = context.getJsonArray(NMSConstants.OBJECTS).iterator();

                    while (iterator.hasNext())
                    {
                        if (DRIVE_NAME_PATTERN.matcher(JsonObject.mapFrom(iterator.next()).getString(AIOpsObject.OBJECT_NAME)).find())
                        {
                            iterator.remove();
                        }
                    }
                }

                if (item.containsKey(NMSConstants.METRIC_INSTANCES) && CommonUtil.isNotNullOrEmpty(item.getJsonArray(NMSConstants.METRIC_INSTANCES)))
                {
                    var iterator = item.getJsonArray(NMSConstants.METRIC_INSTANCES).iterator();

                    while (iterator.hasNext())
                    {
                        if (DRIVE_NAME_PATTERN.matcher(iterator.next().toString()).find())
                        {
                            iterator.remove();
                        }
                    }
                }

                Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                        item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        result ->
                        {
                            if (result.succeeded())
                            {
                                MetricConfigStore.getStore().updateItem(id).onComplete(handler -> future.complete());
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to update metric : %s, Possible reason : %s", id, result.cause().getMessage()));

                                future.fail(result.cause().getMessage());
                            }
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Windows directory metric plugin updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.warn("Failed to update windows directory metric plugin");

                    promise.fail("Failed to update windows directory metric plugin");
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception.getMessage());
            }
        }

        return promise.future();
    }

    private Future<Void> deleteSystemDefaultWindowsDirectory()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = SystemFileConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var id = item.getLong(ID);

                if (!item.isEmpty() && item.containsKey(SystemFile.SYSTEM_FILE_OS)
                        && item.getString(SystemFile.SYSTEM_FILE_OS).equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName())
                        && item.containsKey(SystemFile.SYSTEM_FILE) && DRIVE_NAME_PATTERN.matcher(item.getString(SystemFile.SYSTEM_FILE)).find())
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().delete(TBL_SYSTEM_FILE,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    LOGGER.info(String.format("%s drive removed successfully!", item.getString(SystemFile.SYSTEM_FILE)));

                                    SystemFileConfigStore.getStore().deleteItem(id);

                                    future.complete();
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Failed to delete system file : %s, Possible reason : %s", item.getString(SystemFile.SYSTEM_FILE), result.cause().getMessage()));

                                    future.fail(result.cause().getMessage());
                                }
                            });
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("System file config store updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.warn("Failed to update system file config store");

                    promise.fail("Failed to update system file config store");
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception.getMessage());
            }
        }

        return promise.future();
    }
}
