[{"_type": "0", "id": 10000000000770, "visualization.name": "Virtual Machines ESXI", "visualization.description": "Virtual Machines ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.virtual.machines"}, {"type": "metric", "data.point": "esxi.running.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Virtual Machine", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "esxi.virtual.machines.last"}, {"label": "Running", "value": "esxi.running.virtual.machines.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "esxi.running.virtual.machines.last"}}}}, {"_type": "0", "id": 10000000000771, "visualization.name": "CPU ESXI", "visualization.description": "CPU ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.cpu.capacity.hz", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.cpu.used.hz", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.cpu.capacity.hz"}, {"type": "metric", "data.point": "esxi.cpu.cores"}, {"type": "metric", "data.point": "esxi.cpu.percent"}, {"type": "metric", "data.point": "esxi.cpu.speed.hz"}, {"type": "metric", "data.point": "esxi.cpu.used.hz"}, {"type": "metric", "data.point": "esxi.cpu.wait.seconds"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "data.points": [{"label": "CPU Core", "value": "esxi.cpu.cores.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Capacity", "value": "esxi.cpu.capacity.hz.last"}, {"label": "Used", "value": "esxi.cpu.used.hz.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "esxi.cpu.used.hz"}}}}, {"_type": "0", "id": 10000000000772, "visualization.name": "Memory ESXI", "visualization.description": "Memory ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.active.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.consumed.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.shared.memory.bytes"}, {"type": "metric", "data.point": "esxi.vmkernel.memory.bytes"}, {"type": "metric", "data.point": "esxi.unreserved.memory.bytes"}, {"type": "metric", "data.point": "esxi.balloon.memory.bytes"}, {"type": "metric", "data.point": "esxi.granted.memory.bytes"}, {"type": "metric", "data.point": "esxi.active.memory.bytes"}, {"type": "metric", "data.point": "esxi.memory.installed.bytes"}, {"type": "metric", "data.point": "esxi.consumed.memory.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "data.points": [{"label": "Active Memory", "value": "esxi.active.memory.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "esxi.memory.installed.bytes.last"}, {"label": "Consumed", "value": "esxi.consumed.memory.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "esxi.consumed.memory.bytes"}}}}, {"_type": "0", "id": 10000000000773, "visualization.name": "Swap Memory ESXI", "visualization.description": "Swap Memory ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.swap.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.swap.in.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.swap.out.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.memory.swap.in.bytes.per.sec"}, {"type": "metric", "data.point": "esxi.memory.swap.out.bytes.per.sec"}, {"type": "metric", "data.point": "esxi.swap.memory.bytes"}, {"type": "metric", "data.point": "esxi.swap.in.memory.bytes"}, {"type": "metric", "data.point": "esxi.swap.out.memory.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Swap Memory", "data.points": [{"label": "ESXI Swap Memory", "value": "esxi.swap.memory.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "esxi.swap.in.memory.bytes.last"}, {"label": "Out", "value": "esxi.swap.out.memory.bytes.last"}]}, "style": {"icon": {"name": "swap"}, "color.data.point": "esxi.swap.memory.bytes"}}}}, {"_type": "0", "id": 10000000000774, "visualization.name": "Disk Storage Details ESXI", "visualization.description": "Disk Storage Details ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.disk.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.disk.capacity.bytes"}, {"type": "metric", "data.point": "esxi.disk.free.bytes"}, {"type": "metric", "data.point": "esxi.disk.used.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Storage Details", "data.points": [{"label": "Capacity", "value": "esxi.disk.capacity.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "esxi.disk.free.bytes.last"}, {"label": "Used", "value": "esxi.disk.used.bytes.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "esxi.disk.used.bytes"}}}}, {"_type": "0", "id": 10000000000775, "visualization.name": "Network Stats ESXI", "visualization.description": "Network Stats ESXI", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "esxi.network.bytes.per.sec"}, {"type": "metric", "data.point": "esxi.network.in.bytes.per.sec"}, {"type": "metric", "data.point": "esxi.network.out.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network Stats", "data.points": [{"label": "Total", "value": "esxi.network.bytes.per.sec.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "esxi.network.in.bytes.per.sec.last"}, {"label": "Out", "value": "esxi.network.out.bytes.per.sec.last"}]}, "style": {"icon": {"name": "network"}, "color.data.point": "esxi.network.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000000776, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000777, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000778, "visualization.name": "Datastore Details", "visualization.description": "ESXI Datastore Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.datastore~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~read.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~write.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore", "title": "Datastore", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~read.latency.ms.last", "title": "Read Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~write.latency.ms.last", "title": "Write Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~read.bytes.per.sec.last", "title": "Read Volume Per Sec", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~write.bytes.per.sec.last", "title": "Write Volume Per Sec", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000779, "visualization.name": "CPU Utilization", "visualization.description": "ESXI CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000780, "visualization.name": "Memory Utilization", "visualization.description": "ESXI Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.memory.installed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.active.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.shared.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000781, "visualization.name": "Response Time vs. Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000782, "visualization.name": "Top VMs By CPU Utilization", "visualization.description": "Top ESXI VMs By CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "esxi.vm~cpu.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "esxi.vm~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000783, "visualization.name": "Top VMs By Memory Utilization", "visualization.description": "Top ESXI VMs By Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~memory.used.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "esxi.vm~memory.used.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~memory.used.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "esxi.vm~memory.used.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000784, "visualization.name": "Top VMs By Disk IO", "visualization.description": "Top ESXI VMs By Disk IO", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.vm~disk.io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.vm~disk.io.bytes.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "vm", "style": {}}, {"name": "esxi.vm~disk.io.bytes.per.sec.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.vm~disk.io.bytes.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "esxi.vm~disk.io.bytes.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000785, "visualization.name": "Network Interface Details", "visualization.description": "ESXI Network Interface Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.network.interface~in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.network.interface~out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.network.interface~bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.network.interface~in.bytes.per.sec.avg", "title": "In Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "long-arrow-right", "classes": ["text-neutral-light"]}}}, {"name": "esxi.network.interface~out.bytes.per.sec.avg", "title": "Out Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "long-arrow-left", "classes": ["text-neutral-light"]}}}, {"name": "esxi.network.interface~bytes.per.sec.avg", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000000786, "visualization.name": "Datastore Utilization", "visualization.description": "ESXI Datastore Usages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "esxi.datastore~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "esxi.datastore~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~capacity.bytes.last", "alias": "system.disk.volume.capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~used.bytes.last", "alias": "system.disk.volume.used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "esxi.datastore~free.bytes.last", "alias": "system.disk.volume.free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]