/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/consts"
	"motadatasdk/motadatatypes"
	"os"
	"regexp"
)

//Linux:= "command": "ps -eo fname,pid,user,pcpu,pmem,vsz,rss,etime,nlwp,args --sort=-pcpu | head -$$$count$$$ | sed '1 d'",

// Solaris:= "command": "ps -eo fname,pid,user,pcpu,pmem,vsz,rss,etime,nlwp,args | sort -k 4 -r | head -n $$$count$$$ | sed '1 d'"

func main() {

	decodedContext, _ := base64.StdEncoding.DecodeString(os.Args[2:][0])

	context := make(motadatatypes.MotadataMap)

	_ = json.Unmarshal(decodedContext, &context)

	bytes, _ := json.Marshal(Run(context))

	fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
}

func Run(context motadatatypes.MotadataMap) motadatatypes.MotadataMap {

	result := motadatatypes.MotadataMap{
		consts.Status: consts.StatusFail,
		consts.Result: make(motadatatypes.MotadataMap),
	}

	output := context.GetMotadataStringValue(consts.Result)

	var processes []motadatatypes.MotadataMap

	if len(output) > 0 {

		for _, token := range output.Split("\n") {

			space := regexp.MustCompile(`\s+`)

			metric := motadatatypes.MotadataString(space.ReplaceAllString(token.ToString(), consts.SpaceSeparator)).SplitN(consts.SpaceSeparator, 10)

			if len(metric) > 9 {

				process := make(motadatatypes.MotadataMap)

				process[consts.Status] = consts.StatusUp

				process[consts.Process] = metric[0].TrimSpace()

				process[consts.ProcessID] = metric[1].TrimSpace().ToInt()

				process[consts.ProcessUser] = metric[2].TrimSpace()

				process[consts.ProcessCPUPercent] = metric[3].TrimSpace()

				process[consts.ProcessMemoryPercent] = metric[4].TrimSpace()

				process[consts.ProcessVirtualMemoryBytes] = motadatatypes.MotadataKB(metric[5].TrimSpace().ToFloat64()).ToBytes()

				process[consts.ProcessMemoryBytes] = motadatatypes.MotadataKB(metric[6].TrimSpace().ToFloat64()).ToBytes()

				process[consts.ProcessThreads] = metric[8].TrimSpace().ToInt()

				processes = append(processes, process)
			}
		}
	}

	if len(processes) > 0 {

		result.GetMapValue(consts.Result)[consts.Process] = processes

		result[consts.Status] = consts.StatusSucceed
	}

	return result
}
