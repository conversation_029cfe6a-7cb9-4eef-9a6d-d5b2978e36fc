/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*Change Logs
 *   Date          Author              Notes
 *   2025-02-28    Umang Sharma      CompliancePolicyCacheStore Items for the Scheduler statuses
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.compliance.ComplianceConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;

public class CompliancePolicyCacheStore extends AbstractCacheStore
{
    private static final CompliancePolicyCacheStore STORE = new CompliancePolicyCacheStore();

    private static final Logger LOGGER = new Logger(CompliancePolicyCacheStore.class, GlobalConstants.MOTADATA_STORE, "Compliance Policy Cache Store");

    private final Map<String, Long> itemsByTick = new ConcurrentHashMap<>(); // compliance.policy.id||object.id||rule.id -> [Long {Timestamp}]

    private final Map<String, Integer> itemsByStatus = new ConcurrentHashMap<>();// compliance.policy.id||object.id||rule.id -> [String {Status}]

    private final Map<Long, Integer> activeItems = new ConcurrentHashMap<>(); // State Running or Completed

    private CompliancePolicyCacheStore()
    {

    }

    public static CompliancePolicyCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        loadItems();

        promise.complete();

        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        return promise.future();
    }

    private void loadItems()
    {
        if (LicenseUtil.COMPLIANCE_ENABLED.get())
        {
            Bootstrap.complianceDBService().get(DBConstants.TABLE_COMPLIANCE_TRAIL, new JsonObject().put(DBConstants.TRANSFORM, false).put(DBConstants.QUERY, DBConstants.QUERY_COMPLIANCE_TRAIL_TABLE), result ->
            {
                if (result.succeeded() && result.result() != null)
                {
                    var rows = result.result().getJsonArray(RESULT);

                    for (var i = 0; i < rows.size(); i++)
                    {
                        var row = rows.getJsonObject(i);

                        this.itemsByStatus.put(row.getLong(ComplianceConstants.COMPLIANCE_POLICY_ID) + KEY_SEPARATOR + row.getLong(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + row.getLong(ComplianceConstants.COMPLIANCE_RULE_ID), row.getInteger(ComplianceConstants.CURRENT_SCAN_STATUS));

                        this.itemsByTick.put(row.getLong(ComplianceConstants.COMPLIANCE_POLICY_ID) + KEY_SEPARATOR + row.getLong(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + row.getLong(ComplianceConstants.COMPLIANCE_RULE_ID), row.getLong(ComplianceConstants.CURRENT_SCAN_TIMESTAMP));
                    }
                }
                else
                {
                    LOGGER.error(result.cause().getCause());
                }

            });
        }
    }

    public Integer getLastScanStatus(long compliancePolicyId, int objectId, long ruleId)
    {
        return this.itemsByStatus.getOrDefault(compliancePolicyId + KEY_SEPARATOR + objectId + KEY_SEPARATOR + ruleId, null);
    }


    public void updateLastScanStatus(long compliancePolicyId, int objectId, long ruleId, int value)
    {
        this.itemsByStatus.put(compliancePolicyId + KEY_SEPARATOR + objectId + KEY_SEPARATOR + ruleId, value);
    }

    public long getLastScanTimestamp(long compliancePolicyId, int objectId, long ruleId)
    {
        return this.itemsByTick.getOrDefault(compliancePolicyId + KEY_SEPARATOR + objectId + KEY_SEPARATOR + ruleId, -1L);
    }

    public void updateLastScanTimestamp(long compliancePolicyId, int objectId, long ruleId, long value)
    {
        this.itemsByTick.put(compliancePolicyId + KEY_SEPARATOR + objectId + KEY_SEPARATOR + ruleId, value);
    }

    public void deleteItem(long id)
    {
        this.itemsByTick.keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0]) == id);

        this.itemsByStatus.keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0]) == id);
    }

    public Integer getItem(long id)
    {
        return this.activeItems.getOrDefault(id, GlobalConstants.NOT_AVAILABLE);
    }

    public void complete(long id)
    {
        this.activeItems.remove(id);
    }

    public void updateItem(long id, Integer count)
    {
        this.activeItems.put(id, count);
    }
}
