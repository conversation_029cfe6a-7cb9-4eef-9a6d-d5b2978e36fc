/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The flow package provides functionality for collecting, processing, and analyzing network flow data.
 * <p>
 * Network flow data represents information about network traffic between source and destination endpoints,
 * including IP addresses, ports, protocols, and traffic volume. This package implements components for
 * capturing, processing, and analyzing this data to provide insights into network behavior and performance.
 * <p>
 * The package includes the following key components:
 * <ul>
 *   <li>{@link com.mindarray.flow.FlowListener} - Listens for incoming network flow data (sFlow, NetFlow)
 *      and forwards it for processing. It manages the connection to flow data sources and handles
 *      different flow protocols.</li>
 *   <li>{@link com.mindarray.flow.FlowProcessor} - Processes raw flow data by enriching it with additional
 *      information such as geolocation data, AS (Autonomous System) information, and interface details.
 *      It transforms the raw flow data into a structured format for analysis and storage.</li>
 *   <li>{@link com.mindarray.flow.FlowCacheProcessor} - Manages the caching of flow data to improve
 *      performance and handle high volumes of flow information. It implements mechanisms for storing
 *      and retrieving flow data efficiently.</li>
 *   <li>{@link com.mindarray.flow.FlowStatCalculator} - Calculates statistical information from flow data,
 *      such as traffic volume, flow rates, and other metrics. These statistics provide insights into
 *      network behavior and performance.</li>
 *   <li>{@link com.mindarray.flow.FlowEngineConstants} - Defines constants used throughout the flow
 *      processing system, including field names, protocols, and configuration parameters.</li>
 * </ul>
 * <p>
 * The flow package interacts with other components of the system:
 * <ul>
 *   <li>It uses the datastore package for persisting flow data and statistics</li>
 *   <li>It integrates with the event bus for communication between components</li>
 *   <li>It leverages configuration stores for retrieving settings related to flow processing</li>
 *   <li>It utilizes utility classes for common operations like IP address handling and time calculations</li>
 * </ul>
 * <p>
 * Flow data processing follows this general sequence:
 * <ol>
 *   <li>FlowListener receives raw flow data from network devices</li>
 *   <li>The data is passed to FlowProcessor for enrichment and transformation</li>
 *   <li>FlowCacheProcessor manages temporary storage of flow data</li>
 *   <li>FlowStatCalculator computes statistics based on the processed flow data</li>
 *   <li>The processed data and statistics are stored for analysis and visualization</li>
 * </ol>
 * <p>
 * This package is designed to handle high volumes of flow data efficiently and provide
 * valuable insights into network traffic patterns and behavior.
 */
package com.mindarray.flow;