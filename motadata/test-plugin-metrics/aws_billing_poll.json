{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500037, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Cloud Billing", "metric.object": ***************, "metric.plugin": "awsbilling", "metric.polling.min.time": 43200, "metric.polling.time": 5000, "metric.state": "ENABLE", "metric.type": "AWS Cloud", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:27.757 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************], "object.id": 2, "object.name": "AWS Cloud (************)", "object.state": "ENABLE", "object.target": "AWS Cloud (************)", "object.type": "AWS Cloud", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 5, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.billing.expenditure": 448.**************, "aws.billing.service": [{"aws.billing.service": "NoRegion", "aws.billing.service.usage.amount": 68.43, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-northeast-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-northeast-2", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-northeast-3", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-south-1", "aws.billing.service.usage.amount": 364.33, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-southeast-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ap-southeast-2", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "ca-central-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "eu-central-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "eu-north-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "eu-west-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "eu-west-2", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "eu-west-3", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "global", "aws.billing.service.usage.amount": 0.55, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "sa-east-1", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "us-east-1", "aws.billing.service.usage.amount": 12.23, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "us-east-2", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "us-west-1", "aws.billing.service.usage.amount": 3.1, "aws.billing.service.used.units": "USD"}, {"aws.billing.service": "us-west-2", "aws.billing.service.usage.amount": 0, "aws.billing.service.used.units": "USD"}], "aws.billing.service.cost": 448.**************, "aws.billing.usage.forecast": 735.28}, "status": "succeed", "timeout": 120}}