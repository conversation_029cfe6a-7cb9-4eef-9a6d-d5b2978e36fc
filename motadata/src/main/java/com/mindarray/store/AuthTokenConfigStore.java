/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AuthTokenConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(AuthTokenConfigStore.class, GlobalConstants.MOTADATA_STORE, "Auth Token Config Store");

    private static final AuthTokenConfigStore STORE = new AuthTokenConfigStore();

    private final Map<String, Long> authTokens = new ConcurrentHashMap<>();

    private final Map<String, String> refreshTokens = new ConcurrentHashMap<>(); // key -> refresh token, value -> auth token

    private AuthTokenConfigStore()
    {

        super(DBConstants.TBL_TOKEN, LOGGER, false);
    }

    public static AuthTokenConfigStore getStore()
    {
        return STORE;
    }

    public JsonObject getItem(String refreshToken)
    {
        JsonObject token = null;

        if (authTokens.containsKey(refreshToken) && refreshTokens.containsKey(refreshToken))
        {
            token = new JsonObject();

            var item = items.get(authTokens.get(refreshToken));

            token.put(APIConstants.AUTH_ACCESS_TOKEN, refreshTokens.get(refreshToken));

            token.put(GlobalConstants.REMOTE_ADDRESS, item.getString(GlobalConstants.REMOTE_ADDRESS));
        }

        return token;
    }

    public long getUserId(String refreshToken)
    {
        var userId = CommonUtil.getLong(GlobalConstants.NOT_AVAILABLE);

        if (authTokens.containsKey(refreshToken))
        {
            userId = items.get(authTokens.get(refreshToken)).getLong(APIConstants.TOKEN_USER);
        }

        return userId;
    }

    public void deleteItem(String refreshToken)
    {
        if (authTokens.containsKey(refreshToken))
        {
            deleteItem(authTokens.remove(refreshToken));
        }

        refreshTokens.remove(refreshToken);
    }

    public void updateToken(String refreshToken, String authToken)
    {
        refreshTokens.put(refreshToken, authToken);
    }

    @Override
    public Future<Void> addItem(long id)
    {
        var promise = Promise.<Void>promise();

        super.addItem(id).onComplete(result ->
        {

            if (result.succeeded())
            {
                authTokens.put(items.get(id).getString(APIConstants.TOKEN_ID), id);

                refreshTokens.put(items.get(id).getString(APIConstants.TOKEN_ID), items.get(id).getString(APIConstants.AUTH_ACCESS_TOKEN));

                promise.complete();
            }

            else
            {
                promise.fail(result.cause());

            }
        });

        return promise.future();

    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        try
        {
            super.initStore().onComplete(result ->
            {
                if (result.succeeded())
                {
                    items.forEach((id, item) ->
                    {

                        authTokens.put(item.getString(APIConstants.TOKEN_ID), id);

                        refreshTokens.put(items.get(id).getString(APIConstants.TOKEN_ID), items.get(id).getString(APIConstants.AUTH_ACCESS_TOKEN));

                    });

                    promise.complete();
                }

                else
                {
                    promise.fail(result.cause());
                }

            });
        }

        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }
}
