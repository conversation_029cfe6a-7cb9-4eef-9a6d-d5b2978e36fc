/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date          Author              Notes
 *   2025-02-04    Smit Prajapati      MOTADATA-4977  Log Parser | Used count is not displayed for cisco ACI log parser.
 *   2025-05-15    Chopra Deven        MOTADATA-6240  Changed to use context class loader as parent to ensure plugin classes can access application-level classes within encrypted .exe; fixes classloading failure caused by jar2exe isolating system class loader
 */

package com.mindarray.log;

import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.EventOrdinalCacheStore;
import com.mindarray.util.CommonUtil;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * Constants and utility methods for the log processing engine.
 * <p>
 * This class defines constants, enums, and utility methods used throughout the log package.
 * It includes:
 * <ul>
 *   <li>Field names for log events</li>
 *   <li>Configuration keys</li>
 *   <li>Enums for log parser types, collector types, source types, and categories</li>
 *   <li>Utility methods for processing log events</li>
 * </ul>
 * <p>
 * The constants defined here ensure consistency in field naming across the log processing system.
 * The enums provide type-safe representations of various log-related concepts.
 * The utility methods help with common log processing tasks.
 */
public final class LogEngineConstants
{
    /**
     * Class loader for loading log parser plugins
     */
    public static final URLClassLoader LOG_PLUGIN_CLASS_LOADER = init();

    /**
     * Constant for source groups field name
     */
    public static final String SOURCE_GROUPS = "source.groups";

    /**
     * Constant for processor category field name
     */
    public static final String PROCESSOR_CATEGORY = "processor.category";

    /**
     * Constant for received timestamp field name
     */
    public static final String RECEIVED_TIMESTAMP = "event.received.time";

    /**
     * Constant for maximum events field name
     */
    public static final String MAXIMUM_EVENTS = "maximum.events";

    /**
     * Constant for sample file name field
     */
    public static final String SAMPLE_FILE_NAME = "sample.file";

    /**
     * Constant for assigned log parsers field
     */
    public static final String ASSIGNED_LOG_PARSERS = "assigned.log.parsers";

    /**
     * Constant for unassigned log parsers field
     */
    public static final String UNASSIGNED_LOG_PARSERS = "unassigned.log.parsers";

    /**
     * Constant for event pattern ID field
     */
    public static final String EVENT_PATTERN_ID = "event.pattern.id";

    /**
     * Constant for event pattern field
     */
    public static final String EVENT_PATTERN = "event.pattern";

    /**
     * Constant for event category field
     */
    public static final String EVENT_CATEGORY = "event.category";

    /**
     * Constant for event source type field
     */
    public static final String EVENT_SOURCE_TYPE = "event.source.type";

    /**
     * Constant for log patterns configuration file name
     */
    public static final String LOG_PATTERNS = "log-patterns";

    /**
     * Constant for event ordinals configuration file name
     */
    public static final String EVENT_ORDINALS = "event-ordinals";

    /**
     * Constant for event statistics key field
     */
    public static final String EVENT_STAT_KEY = "event.stat.key";

    /**
     * Constant for log volume bytes field
     */
    public static final String LOG_VOLUME_BYTES = "log.volume.bytes";

    /**
     * Constant for logs per second field
     */
    public static final String LOGS_PER_SEC = "logs.per.sec";

    /**
     * Constant for log volume bytes per second field
     */
    public static final String LOG_VOLUME_BYTES_PER_SEC = "log.volume.bytes.per.sec";

    /**
     * Constant for event severity field
     */
    public static final String EVENT_SEVERITY = "event.severity";

    private static final Set<String> INDEXING_METADATA_FIELDS = Set.of(PLUGIN_ID, EventBusConstants.EVENT_TIMESTAMP, MESSAGE, RECEIVED_TIMESTAMP, EVENT_TYPE, DatastoreConstants.DATASTORE_TYPE, EVENT_VOLUME_BYTES, EVENT);

    private static final JsonObject SEVERITIES = new JsonObject().put("info", "Informational").put("warn", "Warning");

    private LogEngineConstants()
    {
    }

    /**
     * Initializes the class loader for log parser plugins.
     * <p>
     * This method creates a URLClassLoader that points to the plugin scripts directory,
     * allowing the system to dynamically load log parser plugins at runtime.
     * <p>
     * If an exception occurs during initialization, null is returned.
     *
     * @return A URLClassLoader for loading log parser plugins, or null if initialization fails
     */
    private static URLClassLoader init()
    {
        try
        {
            // MOTADATA-6240 : // Changed to use context class loader as parent to ensure plugin classes can access application-level classes within encrypted .exe; fixes classloading failure caused by jar2exe isolating system class loader
            return new URLClassLoader(null, new URL[]{new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR).toURI().toURL()}, Thread.currentThread().getContextClassLoader());
        }
        catch (Exception exception)
        {
            return null;
        }
    }

    /**
     * Maps a source type to its corresponding category.
     * <p>
     * This method determines the appropriate category for a given source type.
     * For example, Windows and Linux are categorized as SERVER, while MySQL and Oracle
     * are categorized as DATABASE.
     * <p>
     * The categorization helps in organizing and filtering log sources in the system.
     *
     * @param type The source type to categorize
     * @return The category corresponding to the given type
     */
    public static Category getCategory(Type type)
    {
        return switch (type)
        {
            case WINDOWS, LINUX, IBM_AIX, HP_UX, SOLARIS -> Category.SERVER;
            case NGINX, APACHE_HTTP, LIGHTTPD, MICROSOFT_IIS, HA_PROXY -> Category.WEB_SERVER;
            case BIND9, IBM_WEBSPHERE, ORACLE_WEBLOGIC, APACHE_MQ, IBM_MQ, MSMQ, RABBITMQ, APACHE_TOMCAT,
                 GLASS_FISH_SERVER, WILDFLY -> Category.MIDDLEWARE;
            case AWS_CLOUD, AZURE_CLOUD, OFFICE_365, MICROSOFT_TEAMS -> Category.CLOUD;
            case VMWARE_ESXI, CITRIX_XEN, HYPER_V, VCENTER -> Category.VIRTUALIZATION;
            case MARIADB, MYSQL, IBM_DB2, SQL_SERVER, POSTGRESQL, SYBASE, SAP_HANA, SAP_MAXDB, ORACLE_DATABASE ->
                    Category.DATABASE;
            case CISCO_WIRELESS, RUCKUS_WIRELESS, FIREWALL, SWITCH, ROUTER, LOAD_BALANCER, PRINTER, UPS,
                 WIRELESS_CONTROLLER, SNMP_DEVICE -> Category.NETWORK;
            default -> Category.OTHER;
        };
    }

    /**
     * Processes and writes a log event to the datastore.
     * <p>
     * This method prepares a log event for storage by:
     * <ol>
     *   <li>Removing unnecessary fields</li>
     *   <li>Converting event fields to a binary format for efficient storage</li>
     *   <li>Handling severity information</li>
     *   <li>Writing the event to the datastore</li>
     * </ol>
     * <p>
     * The method ensures that only relevant fields are stored and that field values
     * don't exceed size limits. It also ensures that every log event has a severity
     * value, defaulting to "Informational" if none is provided.
     *
     * @param event            The log event to process and write
     * @param mappers          A set of column mappers for the datastore
     * @param builder          A StringBuilder for building SQL queries
     * @param indexableColumns A JsonObject containing information about which columns should be indexed
     */
    public static void write(JsonObject event, Set<String> mappers, StringBuilder builder, JsonObject indexableColumns)
    {
        // Remove fields that are no longer needed
        event.remove(EVENT_COPY_REQUIRED);
        event.remove(EVENT_STAT_KEY);

        var iterator = event.getMap().keySet().iterator();
        var buffer = Buffer.buffer();
        var severityKey = EMPTY_VALUE;

        // Create a plugin identifier by combining plugin ID and event category
        var plugin = event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + (event.getString(EVENT_CATEGORY).trim().replace(" ", ".").toLowerCase());

        // Process each field in the event
        while (iterator.hasNext())
        {
            var key = iterator.next();

            // Only process fields that should be indexed
            if (!INDEXING_METADATA_FIELDS.contains(key) && (event.getInteger(PLUGIN_ID) < DatastoreConstants.PluginId.LOG_EVENT.getName() || !indexableColumns.containsKey(plugin) || indexableColumns.getJsonArray(plugin).contains(key)))
            {
                // Add the field ordinal to the buffer
                buffer.appendIntLE(EventOrdinalCacheStore.getStore().getOrdinal(event.getInteger(PLUGIN_ID) + SEPARATOR + key));

                // Convert the field value to bytes
                var bytes = CommonUtil.getString(event.getValue(key)).getBytes(StandardCharsets.UTF_8);

                // Truncate field values that exceed the maximum size
                if (bytes.length > Short.MAX_VALUE)
                {
                    bytes = CommonUtil.getString(event.getValue(key)).substring(0, Short.MAX_VALUE).getBytes(StandardCharsets.UTF_8);
                    event.put(key, bytes);
                }

                // Check if this field contains severity information
                if (key.contains(SEVERITY))
                {
                    severityKey = key;
                }

                // Add the field value length and bytes to the buffer
                buffer.appendShortLE(CommonUtil.getShort(bytes.length));
                buffer.appendBytes(bytes);
            }
        }

        // Handle severity information
        var severity = true;

        if (severityKey.isEmpty())
        {
            // If no severity field was found, check if EVENT_SEVERITY exists
            if (!event.containsKey(EVENT_SEVERITY))
            {
                severity = false;
                // Default to "Informational" if no severity is provided
                event.put(EVENT_SEVERITY, "Informational");
            }
        }
        else
        {
            severity = false;
            // Capitalize the first letter of the severity value
            event.put(EVENT_SEVERITY, StringUtils.capitalize(SEVERITIES.getString(event.getString(severityKey).toLowerCase(), event.getString(severityKey))));
        }

        // Add severity information to the buffer if needed
        if (!severity && buffer.length() > 0)
        {
            buffer.appendIntLE(EventOrdinalCacheStore.getStore().getOrdinal(event.getInteger(PLUGIN_ID) + SEPARATOR + EVENT_SEVERITY));
            buffer.appendShortLE(CommonUtil.getShort(event.getString(EVENT_SEVERITY).getBytes(StandardCharsets.UTF_8).length));
            buffer.appendBytes(event.getString(EVENT_SEVERITY).getBytes(StandardCharsets.UTF_8));
        }

        // Add the buffer to the event if it contains data
        if (buffer.length() > 0)
        {
            event.put(EVENT, buffer.getBytes());
        }

        // Write the event to the datastore
        DatastoreConstants.write(event.put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.LOG.ordinal()), event.getString(EVENT_CATEGORY), mappers, builder);
    }


    /**
     * Enum representing different types of date-time formatters used in log parsing.
     * <p>
     * This enum defines the types of formatters that can be used to parse date-time
     * information in log entries:
     * <ul>
     *   <li>FORMATTER - Uses a pattern-based formatter (e.g., DateTimeFormatter)</li>
     *   <li>NUMERIC - Uses numeric parsing for timestamps (e.g., Unix timestamps)</li>
     * </ul>
     */
    public enum LogDateTimeFormatterType
    {
        /**
         * Pattern-based formatter type
         */
        FORMATTER("formatter"),

        /**
         * Numeric timestamp formatter type
         */
        NUMERIC("numeric");

        /**
         * The name of the formatter type
         */
        private final String name;

        /**
         * Constructs a LogDateTimeFormatterType with the specified name.
         *
         * @param name The name of the formatter type
         */
        LogDateTimeFormatterType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the name of the formatter type.
         *
         * @return The name of the formatter type
         */
        public String getName()
        {
            return this.name;
        }
    }

    /**
     * Enum representing different time units used in log date-time parsing.
     * <p>
     * This enum defines the time units that can be used when parsing date-time
     * information in log entries:
     * <ul>
     *   <li>SECONDS - Time in seconds</li>
     *   <li>MILLIS - Time in milliseconds</li>
     * </ul>
     * <p>
     * These units are used to specify the precision of timestamp values in log entries,
     * particularly when working with numeric timestamps.
     */
    public enum LogDateTimeUnit
    {
        /**
         * Time in seconds
         */
        SECONDS("seconds"),

        /**
         * Time in milliseconds
         */
        MILLIS("millis");

        /**
         * The name of the time unit
         */
        private final String name;

        /**
         * Constructs a LogDateTimeUnit with the specified name.
         *
         * @param name The name of the time unit
         */
        LogDateTimeUnit(String name)
        {
            this.name = name;
        }

        /**
         * Gets the name of the time unit.
         *
         * @return The name of the time unit
         */
        public String getName()
        {
            return this.name;
        }
    }

    /**
     * Enum representing different types of log parsing strategies.
     * <p>
     * This enum defines the strategies that can be used to parse log entries:
     * <ul>
     *   <li>REGEX - Uses regular expressions to extract fields from log entries</li>
     *   <li>JSON - Parses log entries in JSON format</li>
     *   <li>DELIMITER - Uses delimiters (e.g., comma, tab) to split log entries into fields</li>
     *   <li>CUSTOM - Uses custom plugins or scripts for specialized log parsing</li>
     * </ul>
     * <p>
     * The parser type determines how log entries are processed and what configuration
     * parameters are required for parsing.
     */
    public enum LogParserType
    {
        /**
         * Regular expression-based parser
         */
        REGEX("regex"),

        /**
         * JSON format parser
         */
        JSON("json"),

        /**
         * Delimiter-based parser
         */
        DELIMITER("delimiter"),

        /**
         * Custom plugin-based parser
         */
        CUSTOM("custom");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, LogParserType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(LogParserType::getName, e -> e)));

        /**
         * The name of the parser type
         */
        private final String name;

        /**
         * Constructs a LogParserType with the specified name.
         *
         * @param name The name of the parser type
         */
        LogParserType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the specified name.
         * <p>
         * This method provides a way to look up enum values by their string name,
         * which is useful when parsing configuration files or API requests.
         *
         * @param name The name of the parser type to look up
         * @return The corresponding LogParserType, or null if not found
         */
        public static LogParserType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the name of the parser type.
         *
         * @return The name of the parser type
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing different types of log collection methods.
     * <p>
     * This enum defines the methods that can be used to collect logs from various sources:
     * <ul>
     *   <li>PYTHON - Uses Python scripts to collect logs</li>
     *   <li>SSH - Uses SSH connections to collect logs from remote systems</li>
     *   <li>POWERSHELL - Uses PowerShell scripts to collect logs from Windows systems</li>
     *   <li>HTTP_HTTPS - Uses HTTP/HTTPS requests to collect logs from web services or APIs</li>
     * </ul>
     * <p>
     * The collector type determines how logs are retrieved from the source and what
     * authentication and connection parameters are required.
     */
    public enum LogCollectorType
    {
        /**
         * Python script-based collector
         */
        PYTHON("Python"),

        /**
         * SSH-based collector for remote systems
         */
        SSH("SSH"),

        /**
         * PowerShell-based collector for Windows systems
         */
        POWERSHELL("Powershell"),

        /**
         * HTTP/HTTPS-based collector for web services
         */
        HTTP_HTTPS("HTTP/HTTPS");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, LogCollectorType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(LogCollectorType::getName, e -> e)));

        /**
         * The name of the collector type
         */
        private final String name;

        /**
         * Constructs a LogCollectorType with the specified name.
         *
         * @param name The name of the collector type
         */
        LogCollectorType(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the specified name.
         * <p>
         * This method provides a way to look up enum values by their string name,
         * which is useful when parsing configuration files or API requests.
         *
         * @param name The name of the collector type to look up
         * @return The corresponding LogCollectorType, or null if not found
         */
        public static LogCollectorType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the name of the collector type.
         *
         * @return The name of the collector type
         */
        public String getName()
        {
            return name;
        }
    }


    /**
     * Enum representing different types of log sources.
     * <p>
     * This enum defines the various types of systems and applications that can generate logs.
     * It includes operating systems, web servers, databases, cloud services, network devices,
     * and many other types of systems.
     * <p>
     * The source type is used to categorize log sources and determine appropriate parsing
     * strategies and patterns. It also helps in organizing and filtering logs in the user interface.
     * <p>
     * Source types are grouped into categories (see {@link Category}) for higher-level organization.
     */
    public enum Type
    {
        /**
         * Windows operating system
         */
        WINDOWS("Windows"),

        /**
         * Linux operating system
         */
        LINUX("Linux"),

        /**
         * IBM AIX operating system
         */
        IBM_AIX("IBM AIX"),

        /**
         * HP-UX operating system
         */
        HP_UX("HP-UX"),

        /**
         * Apache HTTP web server
         */
        APACHE_HTTP("Apache HTTP"),

        /**
         * Amazon Web Services cloud platform
         */
        AWS_CLOUD("AWS Cloud"),

        /**
         * Microsoft Azure cloud platform
         */
        AZURE_CLOUD("Azure Cloud"),

        /**
         * Bind9 DNS server
         */
        BIND9("Bind9"),

        /**
         * Microsoft Internet Information Services web server
         */
        MICROSOFT_IIS("Microsoft IIS"),

        /**
         * Nginx web server
         */
        NGINX("Nginx"),

        /**
         * IBM Db2 database
         */
        IBM_DB2("IBM Db2"),

        /**
         * Oracle Database
         */
        ORACLE_DATABASE("Oracle Database"),

        /**
         * Microsoft SQL Server database
         */
        SQL_SERVER("SQL Server"),

        /**
         * MySQL database
         */
        MYSQL("MySQL"),

        /**
         * MariaDB database
         */
        MARIADB("MariaDB"),

        /**
         * PostgreSQL database
         */
        POSTGRESQL("PostgreSQL"),

        /**
         * HAProxy load balancer
         */
        HA_PROXY("HAProxy"),

        /**
         * IBM MQ message broker
         */
        IBM_MQ("IBM MQ"),

        /**
         * Microsoft Message Queuing
         */
        MSMQ("MSMQ"),

        /**
         * RabbitMQ message broker
         */
        RABBITMQ("RabbitMQ"),

        /**
         * Apache ActiveMQ message broker
         */
        APACHE_MQ("Apache MQ"),

        /**
         * Oracle WebLogic application server
         */
        ORACLE_WEBLOGIC("Oracle WebLogic"),

        /**
         * GlassFish application server
         */
        GLASS_FISH_SERVER("GlassFish Server"),

        /**
         * Lighttpd web server
         */
        LIGHTTPD("Light Httpd"),

        /**
         * Apache Tomcat application server
         */
        APACHE_TOMCAT("Apache Tomcat"),

        /**
         * IBM WebSphere application server
         */
        IBM_WEBSPHERE("IBM WebSphere"),

        /**
         * WildFly application server (formerly JBoss)
         */
        WILDFLY("WildFly"),

        /**
         * Sybase database
         */
        SYBASE("Sybase"),

        /**
         * SAP HANA database
         */
        SAP_HANA("SAP HANA"),

        /**
         * SAP MaxDB database
         */
        SAP_MAXDB("SAP MaxDB"),

        /**
         * Cisco UCS server
         */
        CISCO_UCS("Cisco UCS"),

        /**
         * Cisco wireless network devices
         */
        CISCO_WIRELESS("Cisco Wireless"),

        /**
         * Citrix Xen hypervisor
         */
        CITRIX_XEN("Citrix Xen"),

        /**
         * VMware ESXi hypervisor
         */
        VMWARE_ESXI("VMware ESXi"),

        /**
         * Microsoft Hyper-V hypervisor
         */
        HYPER_V("Hyper-V"),

        /**
         * Microsoft Office 365 cloud service
         */
        OFFICE_365("Office 365"),

        /**
         * Ruckus wireless network devices
         */
        RUCKUS_WIRELESS("Ruckus Wireless"),

        /**
         * Aruba wireless network devices
         */
        ARUBA_WIRELESS("Aruba Wireless"),

        /**
         * Solaris operating system
         */
        SOLARIS("Solaris"),

        /**
         * Microsoft Teams collaboration platform
         */
        MICROSOFT_TEAMS("Microsoft Teams"),

        /**
         * VMware vCenter server
         */
        VCENTER("vCenter"),

        /**
         * Network switch
         */
        SWITCH("Switch"),

        /**
         * Network router
         */
        ROUTER("Router"),

        /**
         * Network firewall
         */
        FIREWALL("Firewall"),

        /**
         * Network printer
         */
        PRINTER("Printer"),

        /**
         * Load balancer
         */
        LOAD_BALANCER("Load Balancer"),

        /**
         * Uninterruptible Power Supply
         */
        UPS("UPS"),

        /**
         * Symantec Messaging Gateway
         */
        SYMANTEC_MESSAGING_GATEWAY("Symantec Messaging Gateway"),

        /**
         * Generic SNMP device
         */
        SNMP_DEVICE("SNMP Device"),

        /**
         * Nutanix hyperconverged infrastructure
         */
        NUTANIX("Nutanix"),

        /**
         * Wireless network controller
         */
        WIRELESS_CONTROLLER("Wireless Controller"),

        /**
         * Custom source type for user-defined sources
         */
        CUSTOM("Custom"),

        /**
         * Other source types not specifically defined
         */
        OTHER("Other");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, Type> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Type::getName, e -> e)));

        /**
         * The display name of the source type
         */
        private final String name;

        /**
         * Constructs a Type with the specified display name.
         *
         * @param name The display name of the source type
         */
        Type(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the specified name.
         * <p>
         * This method provides a way to look up enum values by their string name,
         * which is useful when parsing configuration files or API requests.
         *
         * @param name The name of the source type to look up
         * @return The corresponding Type, or null if not found
         */
        public static Type valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of the source type.
         *
         * @return The display name of the source type
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing high-level categories for log sources.
     * <p>
     * This enum defines broad categories that group related source types (see {@link Type})
     * for easier organization and filtering. Each source type is mapped to one of these categories.
     * <p>
     * Categories provide a way to organize and filter log sources at a higher level than
     * individual source types. For example, all database-related source types (MySQL, Oracle, etc.)
     * are categorized as DATABASE.
     * <p>
     * The mapping from source types to categories is done in the {@link #getCategory(Type)} method.
     */
    public enum Category
    {
        /**
         * Server operating systems (Windows, Linux, etc.)
         */
        SERVER("Server"),

        /**
         * Cloud services and platforms (AWS, Azure, etc.)
         */
        CLOUD("Cloud"),

        /**
         * Network devices and infrastructure (switches, routers, firewalls, etc.)
         */
        NETWORK("Network"),

        /**
         * Virtualization platforms (VMware, Hyper-V, etc.)
         */
        VIRTUALIZATION("Virtualization"),

        /**
         * Middleware and application servers (WebSphere, WebLogic, etc.)
         */
        MIDDLEWARE("Middleware"),

        /**
         * Database systems (Oracle, SQL Server, MySQL, etc.)
         */
        DATABASE("Database"),

        /**
         * Web servers (Apache, Nginx, IIS, etc.)
         */
        WEB_SERVER("Web Server"),

        /**
         * Software-Defined Networking
         */
        SDN("SDN"),

        /**
         * Other sources that don't fit into the defined categories
         */
        OTHER("Other");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, Category> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Category::getName, e -> e)));

        /**
         * The display name of the category
         */
        private final String name;

        /**
         * Constructs a Category with the specified display name.
         *
         * @param name The display name of the category
         */
        Category(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the specified name.
         * <p>
         * This method provides a way to look up enum values by their string name,
         * which is useful when parsing configuration files or API requests.
         *
         * @param name The name of the category to look up
         * @return The corresponding Category, or null if not found
         */
        public static Category valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of the category.
         *
         * @return The display name of the category
         */
        public String getName()
        {
            return name;
        }
    }

}
