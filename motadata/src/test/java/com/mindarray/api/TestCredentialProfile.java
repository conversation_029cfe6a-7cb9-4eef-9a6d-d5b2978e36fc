/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.MOTADATA_API;

@ExtendWith(VertxExtension.class)
@Timeout(30 * 1000)
@Execution(ExecutionMode.CONCURRENT)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^AGENT|^PRIMARY$")
public class TestCredentialProfile
{
    public static final Map<String, JsonArray> CREDENTIAL_PROFILES = new ConcurrentHashMap<>();
    private static final Logger LOGGER = new Logger(CredentialProfile.class, MOTADATA_API, "Credential Profile API");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "discovery-parameters.json");

            if (file.exists())
            {
                new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)).forEach(entry ->
                {
                    try
                    {
                        var credentialContext = JsonObject.mapFrom(entry.getValue());

                        if (credentialContext.containsKey(Discovery.DISCOVERY_CATEGORY) && credentialContext.containsKey("discovery.credential.profile.context"))
                        {
                            if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SERVER.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));

                                if (credentialContext.containsKey("application.discovery.credential.profile.context") && !credentialContext.getJsonObject("application.discovery.credential.profile.context").isEmpty())
                                {
                                    var context = JsonObject.mapFrom(entry.getValue());

                                    var applications = CREDENTIAL_PROFILES.computeIfAbsent("Application", value -> new JsonArray());

                                    context.getJsonObject("application.discovery.credential.profile.context").getMap().forEach((k1, v1) ->
                                            applications.add(JsonObject.mapFrom(v1)));
                                }
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.NETWORK.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));

                                if (credentialContext.containsKey("topology.discovery.credential.profile.context") && !credentialContext.getJsonObject("topology.discovery.credential.profile.context").isEmpty())
                                {
                                    var context = JsonObject.mapFrom(entry.getValue());

                                    var applications = CREDENTIAL_PROFILES.computeIfAbsent("Topology", value -> new JsonArray());

                                    context.getJsonObject("topology.discovery.credential.profile.context").getMap().forEach((k1, v1) ->
                                            applications.add(JsonObject.mapFrom(v1)));
                                }
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.OTHER.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.VIRTUALIZATION.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SERVICE_CHECK.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.CLOUD.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.HCI.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SDN.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.SDN.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));

                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.STORAGE.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.STORAGE.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                            else if (credentialContext.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CONTAINER_ORCHESTRATION.getName()))
                            {
                                CREDENTIAL_PROFILES.computeIfAbsent(NMSConstants.Category.CONTAINER_ORCHESTRATION.getName(), value -> new JsonArray()).addAll(JsonObject.mapFrom(entry.getValue()).getJsonArray("discovery.credential.profile.context", new JsonArray()));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getCause());

            LOGGER.error(exception.getCause());
        }

    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateServerCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SERVER.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateNetworkCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.NETWORK.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateOtherCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.OTHER.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateVirtualizationCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.VIRTUALIZATION.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^AGENT|^PRIMARY$")
    void testCreateServiceCheckCategoryCredential(VertxTestContext testContext)
    {
        // service check devices may not contain the credential profile
        if (CREDENTIAL_PROFILES.get(NMSConstants.Category.SERVICE_CHECK.getName()) != null)
        {
            createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SERVICE_CHECK.getName()), new AtomicInteger(0), testContext);
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateCloudCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.CLOUD.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateApplicationCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get("Application"), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateTopologyCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get("Topology"), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateHCICategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.HCI.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateSDNCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.SDN.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateStorageCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.STORAGE.getName()), new AtomicInteger(0), testContext);
    }

    @Test
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
    void testCreateContainerOrchestrationCategoryCredential(VertxTestContext testContext)
    {
        createCredentialProfiles(CREDENTIAL_PROFILES.get(NMSConstants.Category.CONTAINER_ORCHESTRATION.getName()), new AtomicInteger(0), testContext);
    }

    void createCredentialProfiles(JsonArray profiles, AtomicInteger index, VertxTestContext testContext)
    {
        try
        {
            if (index.get() >= profiles.size())
            {
                testContext.completeNow();
            }
            else
            {
                TestUtil.vertx().setTimer(100, id ->
                        TestAPIUtil.createCredentialProfile(profiles.getJsonObject(index.get()), testContext).onComplete(result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    index.set(index.incrementAndGet());

                                    createCredentialProfiles(profiles, index, testContext);
                                }
                                else
                                {
                                    testContext.failNow(result.cause());

                                    LOGGER.error(result.cause());
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        }));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }
}
