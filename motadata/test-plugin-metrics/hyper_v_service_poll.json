{"************": {"result": {"hyperv.service": [{"hyperv.service": "vmms", "hyperv.service.display.name": "Hyper-V Virtual Machine Management", "hyperv.service.description": "Management service for Hyper-V, provides service to run multiple virtual machines.", "hyperv.service.status": "Running", "hyperv.service.startup.type": "Auto", "status": "Up"}, {"hyperv.service": "vmickvpexchange", "hyperv.service.display.name": "Hyper-V Data Exchange Service", "hyperv.service.description": "Provides a mechanism to exchange data between the virtual machine and the operating system running on the physical computer.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "vmicshutdown", "hyperv.service.display.name": "Hyper-V Guest Shutdown Service", "hyperv.service.description": "Provides a mechanism to shut down the operating system of this virtual machine from the management interfaces on the physical computer.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "vmicheartbeat", "hyperv.service.display.name": "Hyper-V Heartbeat Service", "hyperv.service.description": "Monitors the state of this virtual machine by reporting a heartbeat at regular intervals. This service helps you identify running virtual machines that have stopped responding.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "vmicrdv", "hyperv.service.display.name": "Hyper-V Remote Desktop Virtualization Service", "hyperv.service.description": "Provides a platform for communication between the virtual machine and the operating system running on the physical computer.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "vmictimesync", "hyperv.service.display.name": "Hyper-V Time Synchronization Service", "hyperv.service.description": "Synchronizes the system time of this virtual machine with the system time of the physical computer.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "vmicvss", "hyperv.service.display.name": "Hyper-V Volume Shadow Copy Requestor", "hyperv.service.description": "Coordinates the communications that are required to use Volume Shadow Copy Service to back up applications and data on this virtual machine from the operating system on the physical computer.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Manual", "status": "Down"}, {"hyperv.service": "vmicguestinterface", "hyperv.service.display.name": "Hyper-V Guest Service Interface", "hyperv.service.description": "Provides an interface for the Hyper-V host to interact with specific services running inside the virtual machine.", "hyperv.service.status": "Stopped", "hyperv.service.startup.type": "Auto", "status": "Down"}, {"hyperv.service": "nvspwmi", "status": "Down"}, {"hyperv.service": "vhdsvc", "status": "Down"}, {"hyperv.service": "vmicvmsession", "status": "Down"}]}, "errors": []}, "***********": {"result": {"hyperv.service": [{"hyperv.service": "nvspwmi", "hyperv.service.display.name": "Hyper-V Networking Management Service", "hyperv.service.description": "Provides Hyper-V Networking WMI management", "hyperv.service.status": "Running", "hyperv.service.startup.type": "Auto", "status": "Up"}, {"hyperv.service": "vhdsvc", "hyperv.service.display.name": "Hyper-V Image Management Service", "hyperv.service.description": "Provides Image Management servicing for Hyper-V", "hyperv.service.status": "Running", "hyperv.service.startup.type": "Auto", "status": "Up"}, {"hyperv.service": "vmms", "hyperv.service.display.name": "Hyper-V Virtual Machine Management", "hyperv.service.description": "Management service for Hyper-V, provides service to run multiple virtual machines.", "hyperv.service.status": "Running", "hyperv.service.startup.type": "Auto", "status": "Up"}, {"hyperv.service": "vmickvpexchange", "status": "Down"}, {"hyperv.service": "vmicvmsession", "status": "Down"}, {"hyperv.service": "vmicshutdown", "status": "Down"}, {"hyperv.service": "vmicheartbeat", "status": "Down"}, {"hyperv.service": "vmicrdv", "status": "Down"}, {"hyperv.service": "vmictimesync", "status": "Down"}, {"hyperv.service": "vmicvss", "status": "Down"}, {"hyperv.service": "vmicguestinterface", "status": "Down"}]}, "errors": []}}