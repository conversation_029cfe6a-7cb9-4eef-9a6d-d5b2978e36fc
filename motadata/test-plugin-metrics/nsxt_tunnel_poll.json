{"***********": {"result": {"nsxt.down.tunnels": 0, "nsxt.tunnel": [{"nsxt.tunnel": "geneve2887218178-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218178", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi04.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218178-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218178", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi04.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218179-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218179", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi04.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218179-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218179", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi04.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218182-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218182", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi02.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218182-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218182", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi02.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218183-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218183", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi02.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218183-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218183", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-esxi02.boc.lk", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218436-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218436", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-en02", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218436-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218436", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-en02", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218437-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth1", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218437", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-en02", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}, {"nsxt.tunnel": "geneve2887218437-f1b8038a-b59e-46e9-b732-6759c4672cf5", "nsxt.tunnel.bfd.state": "UP", "nsxt.tunnel.egress.interface": "fp-eth0", "nsxt.tunnel.encap": "GENEVE", "nsxt.tunnel.local.ip.address": "************", "nsxt.tunnel.name": "geneve2887218437", "nsxt.tunnel.remote.ip.address": "************", "nsxt.tunnel.remote.node.ip.address": "**************", "nsxt.tunnel.remote.node.name": "bocdr02-en02", "nsxt.tunnel.status": "UP", "nsxt.tunnel.transport.node.ip.address": "**************"}], "nsxt.up.tunnels": 6}, "errors": []}}