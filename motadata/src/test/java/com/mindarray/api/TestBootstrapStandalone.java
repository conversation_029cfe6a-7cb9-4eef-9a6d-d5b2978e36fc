/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LogUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(900 * 1000)
public class TestBootstrapStandalone
{
    private static final Logger LOGGER = new Logger(TestBootstrapStandalone.class, GlobalConstants.MOTADATA_SYSTEM, "Test Bootstrap Standalone");

    private static final Set<AbstractCacheStore.CacheStore> PASSOVER_CACHE_STORES = Set.of(AbstractCacheStore.CacheStore.OBJECT, AbstractCacheStore.CacheStore.LOGGER, AbstractCacheStore.CacheStore.NETROUTE_POLICY);

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        TestUtil.startService(testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                testContext.completeNow();
            }
            else
            {
                System.exit(0);
            }
        });
    }

    // testcase to set build pipeline address & start RPE. Disable it in local to avoid unnecessary process spawning
    // format for cli arguments : ipv4_address username password
    @Test
    @Order(1)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
    void testSetBuildPipelineIPAddressAndStartRPE(VertxTestContext testContext)
    {
        Process process = null;

        var valid = false;

        try (DatagramSocket socket = new DatagramSocket())
        {
            socket.connect(InetAddress.getByName("*******"), 1366);

            var ipAddress = socket.getLocalAddress().getHostAddress();

            if (ipAddress != null && !ipAddress.isEmpty())
            {
                var collectorAddress = "************";

                var remoteEventProcessorUUID = "8179c220-0d38-45bb-bbb8-4fc50d746f7a";

                var remoteEventProcessorHost = "motadata8235";

                switch (ipAddress)
                {
                    case "************" ->
                    {
                    }

                    case "************" ->
                    {
                        collectorAddress = "************";

                        remoteEventProcessorUUID = "38A92C4E6A5A412A9649FC970E142986";

                        remoteEventProcessorHost = "aiops9208";
                    }

                    case "************" ->
                    {
                        collectorAddress = "************";

                        remoteEventProcessorUUID = "8179c220-0d38-45bb-bbb8-4fc50d746f7b";

                        remoteEventProcessorHost = "aiops9209";
                    }

                    case "************" ->
                    {
                        collectorAddress = "************";

                        remoteEventProcessorUUID = "38A92C4E6A5A412A9649FC970E142986";

                        remoteEventProcessorHost = "aiops9210";
                    }
                }

                LOGGER.debug(String.format("current running build pipeline address is %s using collector as %s whose uuid is %s and host is %s", ipAddress, collectorAddress, remoteEventProcessorUUID, remoteEventProcessorHost));

                var processBuilder = new ProcessBuilder(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "collectorscript", collectorAddress, "motadata", "motadata", MotadataConfigUtil.getVersion());

                process = processBuilder.start();

                if (process.waitFor(100, TimeUnit.SECONDS))
                {
                    var inputReader = new BufferedReader(new InputStreamReader(process.getInputStream()));

                    var line = inputReader.readLine();

                    if (line != null && line.equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        valid = true;

                        System.setProperty("build.pipeline.ip.address", ipAddress);

                        System.setProperty("build.pipeline.collector.address", collectorAddress);

                        System.setProperty("build.pipeline.collector.uuid", remoteEventProcessorUUID);

                        System.setProperty("build.pipeline.collector.uuhostid", remoteEventProcessorHost);

                        System.out.println("current running build pipeline address is " + ipAddress + " and using collector as " + collectorAddress);

                        testContext.completeNow();
                    }
                    else
                    {
                        LOGGER.info("failed to start motadata RPE");
                    }
                }
                else
                {
                    LOGGER.info("failed to start motadata RPE because process timed out");

                    process.destroyForcibly();
                }
            }
            else
            {
                LOGGER.debug("Unknown ipv4 address found for build pipeline :" + ipAddress);

                testContext.failNow("Unknown ipv4 address found for build pipeline " + ipAddress);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
        finally
        {

            if (process != null && process.isAlive())
            {
                process.destroyForcibly();
            }

            if (!valid)
            {
                testContext.failNow("failed to start motadata RPE calling System.exit(0)");

                System.exit(0);
            }
        }
    }

    @Test
    @Order(2)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testRemoteEventProcessorRegistration(VertxTestContext testContext) throws Exception
    {
        var registerId = Bootstrap.getRegistrationId();

        var registrationFile = new File(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + GlobalConstants.REGISTRATION_FILE);

        Assertions.assertTrue(registrationFile.exists());

        var context = new JsonObject(FileUtils.readFileToString(registrationFile, StandardCharsets.UTF_8));

        Assertions.assertTrue(registerId != null && !registerId.isEmpty());

        Assertions.assertFalse(context.isEmpty());

        Assertions.assertTrue(context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(registerId));

        testContext.completeNow();
    }

    @Test
    @Order(3)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testConfigStoreInitialize(VertxTestContext testContext)
    {
        var storeFiles = new JsonArray().add("my account");

        var directoryFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "logs" + GlobalConstants.PATH_SEPARATOR + "store");

        for (File file : Objects.requireNonNull(directoryFile.listFiles()))
        {

            if (file.getName().toLowerCase().contains("config store.log"))
            {
                storeFiles.add(file.getName().split("-")[3].toLowerCase().replaceAll("config store.log", "").trim());
            }
        }

        for (var configStore : AbstractConfigStore.ConfigStore.values())
        {
            try
            {
                Assertions.assertTrue(storeFiles.contains(configStore.name().replace("_", " ").toLowerCase().trim()));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        testContext.completeNow();
    }

    @Test
    @Order(4)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testCacheStoreInitialize(VertxTestContext testContext)
    {
        var storeFiles = new JsonArray();

        var directoryFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "logs" + GlobalConstants.PATH_SEPARATOR + "store");

        for (File file : Objects.requireNonNull(directoryFile.listFiles()))
        {

            if (file.getName().toLowerCase().contains("cache store.log"))
            {
                storeFiles.add(file.getName().split("-")[3].toLowerCase().replaceAll("cache store.log", "").trim());
            }
        }

        for (var cacheStore : AbstractCacheStore.CacheStore.values())
        {
            try
            {
                if (!PASSOVER_CACHE_STORES.contains(cacheStore))
                {
                    Assertions.assertTrue(storeFiles.contains(cacheStore.name().replace("_", " ").toLowerCase().trim()));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        testContext.completeNow();
    }

    @Test
    @Order(5)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testConfigStoreInit(VertxTestContext testContext)
    {
        var user = UserConfigStore.getStore().getItem();

        Assertions.assertTrue(user != null && !user.isEmpty());

        Assertions.assertEquals(GlobalConstants.DEFAULT_ID, user.getLong(GlobalConstants.ID));

        Assertions.assertTrue(user.getString(User.USER_PASSWORD).equalsIgnoreCase("admin"));

        var groups = GroupConfigStore.getStore().getItems();

        Assertions.assertTrue(groups != null && !groups.isEmpty());

        var role = UserRoleConfigStore.getStore().getItemByValue(UserRole.USER_ROLE_NAME, "admin");

        Assertions.assertTrue(role != null && !role.isEmpty());

        Assertions.assertEquals(role.getLong(ID), user.getLong(User.USER_ROLE));

        var passwordPolicy = PasswordPolicyConfigStore.getStore().getItem();

        Assertions.assertTrue(passwordPolicy != null && !passwordPolicy.isEmpty());

        var credential = CredentialProfileConfigStore.getStore().getItem(DEFAULT_ID);

        Assertions.assertTrue(credential != null && !credential.isEmpty());

        Assertions.assertTrue(credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_NAME));

        Assertions.assertFalse(SNMPDeviceCatalogConfigStore.getStore().getItems().isEmpty());

        var vendors = SNMPDeviceCatalogConfigStore.getStore().flatItems(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_VENDOR);

        Assertions.assertFalse(vendors.isEmpty());

        Assertions.assertTrue(vendors.contains("Cisco Systems") && vendors.contains("Juniper Networks"));

        testContext.completeNow();
    }

    @Test
    @Order(6)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testCacheStoreInit(VertxTestContext testContext)
    {
        var snmpTemplates = SNMPTemplateOIDGroupCacheStore.getStore().getItemsByType("Cisco Systems", NMSConstants.Type.SWITCH);

        Assertions.assertTrue(snmpTemplates != null && !snmpTemplates.isEmpty());

        snmpTemplates = SNMPTemplateOIDGroupCacheStore.getStore().getItemsByType("Juniper Networks", NMSConstants.Type.SWITCH);

        Assertions.assertTrue(snmpTemplates != null && !snmpTemplates.isEmpty());

        var items = ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.LINUX);

        Assertions.assertEquals(7, items.size());

        Assertions.assertFalse(ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.PING).isEmpty());

        items = ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.AWS_CLOUD);

        Assertions.assertNotNull(items);

        Assertions.assertFalse(ObjectManagerCacheStore.getStore().getItemsByObjectType(NMSConstants.Type.AWS_CLOUD).isEmpty());

        testContext.completeNow();
    }

    @Test
    @Order(7)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testGetWirelessControllerSNMPCatalog(VertxTestContext testContext)
    {
        Assertions.assertFalse(SNMPDeviceCatalogConfigStore.getStore().getItemsByValue(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_TYPE, NMSConstants.Type.WIRELESS_CONTROLLER.getName()).isEmpty());

        testContext.completeNow();
    }

    @Test
    @Order(8)
    // bug - 20149
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testCheckNetworkServiceSchedulerState(VertxTestContext testContext)
    {
        var schedulers = SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.NETWORK_SERVICE.getName());

        Assertions.assertFalse(schedulers.isEmpty());

        var scheduler = schedulers.getJsonObject(0);

        if (scheduler.containsKey(Scheduler.SCHEDULER_CONTEXT))
        {
            scheduler.mergeIn(scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT));

            scheduler.remove(Scheduler.SCHEDULER_CONTEXT);
        }

        Assertions.assertTrue(scheduler.containsKey(NMSConstants.AUTO_PROVISION_STATUS));

        Assertions.assertTrue(scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES));

        Assertions.assertTrue(scheduler.containsKey(Scheduler.SCHEDULER_STATE));

        Assertions.assertTrue(scheduler.getString(Scheduler.SCHEDULER_STATE).equalsIgnoreCase(YES));

        testContext.completeNow();
    }

    @Test
    @Order(9)
    @EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|^AGENT|SLAVE$")
    void testCheckResetSystemLogLevel(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var attempts = new AtomicInteger();

        TestUtil.vertx().setPeriodic(0, TimeUnit.SECONDS.toMillis(3), timer ->
        {
            if (attempts.incrementAndGet() <= 5)
            {
                if (CommonUtil.getLogLevel() == LOG_LEVEL_INFO)
                {
                    Assertions.assertEquals(LOG_LEVEL_INFO, CommonUtil.getLogLevel());

                    LogUtil.setLogLevel(LOG_LEVEL_TRACE);

                    LOGGER.info(String.format(testInfo.getTestMethod().get().getName() + " Changed log-level: %s", GlobalConstants.LOG_LEVELS.get(CommonUtil.getLogLevel())));

                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
            }
            else
            {
                testContext.failNow("failed to reset log level to info");
            }
        });

        testContext.completeNow();
    }
}
