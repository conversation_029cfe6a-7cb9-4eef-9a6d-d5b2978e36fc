/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  June-09-2025    Um<PERSON> Sharma       Change in Function of Apply Filter
 */

package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationEventHistoryManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationEventHistoryManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Event History Manager");

    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();//queryId along with subqueryIds and status

    private final Map<Long, JsonObject> subQueries = new HashMap<>(); //subquery context

    private final Map<Long, JsonObject> queryContexts = new HashMap<>(); //query original request context

    private final Map<Long, String> queries = new HashMap<>(); //queryid

    private final Map<Long, String> paginationQueries = new HashMap<>(); //queryid

    private final Map<Long, Map<String, JsonObject>> paginatedQuerySessions = new HashMap<>();//session context when query fired..

    private final Map<Long, Short> queryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<Long, Short>> subQueryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<String, JsonObject>> customQueries = new HashMap<>();//custom or join queries which result needs to be decoded for manipulation and adding special columns

    private final JsonObject eventColumns = new JsonObject();

    private final Map<String, Set<String>> eventCategories = new HashMap<>();//event category by sourceTypes (LOG)

    private final List<Long> runningQueries = new ArrayList<>();

    private final List<Long> queuedQueries = new ArrayList<>();

    private final Map<Long, JsonObject> queuedQueryContexts = new HashMap<>();

    private final StringBuilder builder = new StringBuilder();

    private final Set<Integer> objectIds = new HashSet<>();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            VisualizationConstants.loadCategories(eventCategories, LOGGER);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, "", reply ->
            {
                if (reply.succeeded())
                {
                    eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

                    var ids = ObjectConfigStore.getStore().getObjectIds();

                    for (var i = 0; i < ids.size(); i++)
                    {
                        objectIds.add(ids.getInteger(i));
                    }

                    //invalid queries kindly remove it as response already received or query aborted
                    vertx.setPeriodic(30 * 1000L, periodicTimer ->
                    {
                        if (runningQueries.removeIf(query -> !queries.containsKey(query)) && !queuedQueries.isEmpty())
                        {
                            send();
                        }
                    });

                    vertx.eventBus().<String>localConsumer(EVENT_EVENT_CATEGORY_UPDATE, message ->
                    {
                        try
                        {
                            if (!message.body().isEmpty())
                            {
                                var tokens = message.body().split(SEPARATOR_WITH_ESCAPE);

                                eventCategories.computeIfAbsent(tokens[0], value -> new HashSet<>()).add(tokens[1]);
                            }
                        }

                        catch (Exception exception)
                        {
                            message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                            LOGGER.error(exception);
                        }
                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                    {
                        try
                        {

                            var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                            var widgetId = 0L;

                            var queryId = buffer.getLongLE(0);

                            var subQueryId = buffer.getLongLE(8);

                            var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                            var subQueryProgress = buffer.getUnsignedByte(16); // to-do: need to watch

                            var status = buffer.getUnsignedByte(33);

                            if (paginationQueries.containsKey(queryId) && subQueries.containsKey(subQueryId) && subQueries.get(subQueryId).containsKey(PAGINATION_QUERY))
                            {
                                //will be needing paginationquerysession as in some cases pagination response comes after 100 progress of main query response of log explorer so will not be able to publish response so this is required..
                                paginatedQuerySessions.get(queryId).forEach((key, value) -> EventBusConstants.publish(key.split(COLUMN_SEPARATOR)[0], EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(UI_EVENT_UUID, key.split(COLUMN_SEPARATOR)[1]).mergeIn(value.getLong(ID) > 0 ? WidgetConfigStore.getStore().getItem(value.getLong(ID)).mergeIn(subQueries.get(subQueryId)) : value.mergeIn(subQueries.get(subQueryId))).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, buffer.getBytes())));

                                cleanUp(queryId, subQueryId);

                                paginatedQuerySessions.remove(queryId);

                                paginationQueries.remove(queryId);

                                if (queryStatuses.get(queryId).isEmpty())
                                {
                                    customQueries.remove(queryId);

                                    cleanUp(queryId);
                                }
                            }
                            else
                            {
                                if (!customQueries.containsKey(queryId) && queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                                {
                                    var succeeded = true;

                                    short queryProgress = 0;

                                    subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).put(subQueryId, subQueryCalculatedProgress);

                                    if (queryStatuses.get(queryId).size() > 1)
                                    {
                                        queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                        buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                        queryTrackers.put(queryId, queryProgress);
                                    }

                                    else
                                    {
                                        queryTrackers.put(queryId, subQueryCalculatedProgress);
                                    }

                                    if (queries.containsKey(queryId))
                                    {
                                        var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                        if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                        {
                                            widgetId = CommonUtil.getLong(tokens[0]);
                                        }
                                    }

                                    if (status == 0)//fail
                                    {
                                        var errorLength = 38 + buffer.getIntLE(34);

                                        var errorMessage = buffer.getString(38, errorLength);

                                        if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                        {
                                            succeeded = false;
                                        }

                                        queryStatuses.get(queryId).put(subQueryId, errorMessage);

                                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                                    }

                                    else
                                    {
                                        queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                                    }

                                    EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, false, queryContexts.get(queryId), true, true));
                                    }

                                    //reason for doing this is because will be having same query id everytime so it will pile up in query status so calculation will go on forever so removing its subquery to get 100 result
                                    if (queryContexts.get(queryId).containsKey("sub.query.remove"))
                                    {
                                        cleanUp(queryId, subQueryId);
                                    }

                                    if (subQueryProgress == 100)
                                    {
                                        subQueries.remove(subQueryId);
                                    }

                                    if (queryTrackers.get(queryId) >= 100)
                                    {
                                        runningQueries.remove(queryId);

                                        if (!queuedQueries.isEmpty())
                                        {
                                            send();
                                        }

                                        queryStatuses.get(queryId).forEach((key, value) ->
                                        {
                                            if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                builder.append(value).append(NEW_LINE);
                                            }
                                        });

                                        if (succeeded)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));

                                        }

                                        else
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                        }

                                        builder.setLength(0);

                                        cleanUp(queryId);

                                        if (CommonUtil.traceEnabled())
                                        {
                                            //as of now dumping trace log will be having query explorer to track each and every query
                                            LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                                    .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("query.contexts", queryContexts.size())
                                                    .put("custom.queries", customQueries.size()).put("queries", queries.size()).put("pagination.query.sessions", paginatedQuerySessions.size()));
                                        }
                                    }
                                }

                                else
                                {
                                    if (customQueries.containsKey(queryId))
                                    {
                                        var response = VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId).containsKey(JOIN_ALIAS), queryContexts.get(queryId), false, true);

                                        if (response.containsKey(RESULT) && !response.getJsonArray(RESULT).isEmpty())
                                        {
                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace("Response received from DB:" + response);
                                            }

                                            customQueries.get(queryId).put(CommonUtil.getString(subQueryId), response);

                                            if (subQueries.get(subQueryId).containsKey(PAGINATION_QUERY))
                                            {
                                                paginationQueries.put(queryId, EMPTY_VALUE);

                                                vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION_EVENT_HISTORY_RESPONSE_PROCESSOR, new JsonObject().put(QUERY_PROGRESS, subQueryCalculatedProgress).put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_CATEGORY)).put(VisualizationConstants.SUB_QUERY_ID, subQueryId).put(QUERY_CONTEXT, subQueries.get(subQueryId)).put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));
                                            }
                                            else
                                            {
                                                vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION_EVENT_HISTORY_RESPONSE_PROCESSOR, new JsonObject().put(QUERY_PROGRESS, subQueryCalculatedProgress).put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_CATEGORY)).put(VisualizationConstants.SUB_QUERY_ID, subQueryId).put(QUERY_CONTEXT, subQueries.get(subQueryId)).put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                                if (subQueryCalculatedProgress >= 100)
                                                {
                                                    var qualifiedSubQueries = new ArrayList<Long>();

                                                    queryStatuses.get(queryId).keySet().stream().filter(subQuery -> !Objects.equals(subQuery, subQueryId) && subQueries.containsKey(subQueryId) && !subQueries.get(subQuery).containsKey(PAGINATION_QUERY)).forEach(qualifiedSubQueries::add);

                                                    qualifiedSubQueries.forEach(subQuery -> cleanUp(queryId, subQuery));
                                                }

                                                customQueries.remove(queryId);
                                            }
                                        }

                                        else
                                        {
                                            if (subQueryCalculatedProgress == 100)
                                            {
                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId).containsKey(JOIN_ALIAS), queryContexts.get(queryId), false, true));
                                                }

                                                var byteBuffer = Buffer.buffer();

                                                if (subQueries.get(subQueryId).containsKey(PAGINATION_QUERY))
                                                {
                                                    paginationQueries.put(queryId, EMPTY_VALUE);

                                                    packHeaders(byteBuffer, queryId, subQueryId, subQueryCalculatedProgress, CommonUtil.getShort(1));

                                                    byteBuffer.setUnsignedByte(18, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal()));

                                                    vertx.eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(byteBuffer.getBytes()));
                                                }
                                                else
                                                {
                                                    packHeaders(byteBuffer, queryId, subQueryId, CommonUtil.getShort(50), CommonUtil.getShort(1));

                                                    byteBuffer.setUnsignedByte(18, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal()));

                                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(byteBuffer.getBytes()));

                                                    byteBuffer = Buffer.buffer();

                                                    packHeaders(byteBuffer, queryId, subQueryId, subQueryCalculatedProgress, CommonUtil.getShort(1));

                                                    byteBuffer.setUnsignedByte(18, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_EVENT_HISTORY.ordinal()));

                                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(byteBuffer.getBytes()));

                                                    customQueries.remove(queryId);

                                                    cleanUp(queryId);

                                                }
                                            }

                                            else
                                            {

                                                if (subQueryCalculatedProgress < 100)
                                                {
                                                    //due to UI handling of event history result for next query will be sending empty result..
                                                    var byteBuffer = Buffer.buffer();

                                                    packHeaders(byteBuffer, queryId, subQueryId, subQueryCalculatedProgress, CommonUtil.getShort(1));

                                                    byteBuffer.setUnsignedByte(18, CommonUtil.getShort(VisualizationCategoryOrdinal.VISUALIZATION_EVENT_HISTORY.ordinal()));

                                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(byteBuffer.getBytes()));
                                                }

                                                else
                                                {
                                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                                }

                                                cleanUp(queryId, subQueryId);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                    });

                    vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
                    {
                        try
                        {
                            var event = message.body();

                            var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                            if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                            {
                                VisualizationConstants.update(eventColumns, tokens, false);
                            }
                        }

                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
                    {
                        var queryId = new AtomicLong();

                        try
                        {
                            var event = message.body();

                            var currentTime = System.currentTimeMillis();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("Visualization Event History Request received:" + event);
                            }

                            queryId.set(event.getLong(VisualizationConstants.QUERY_ID));

                            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                            EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                            JsonArray qualifiedSources;

                            var adminRole = false;

                            if (user.getLong(ID).equals(DEFAULT_ID))//default
                            {
                                qualifiedSources = EventSourceConfigStore.getStore().flatItems(EVENT_SOURCE);

                                user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));

                                adminRole = true;
                            }
                            else
                            {
                                qualifiedSources = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, user.getJsonArray(User.USER_GROUPS), EVENT_SOURCE);
                            }

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                    .put(EventBusConstants.EVENT_ID, queryId.get())
                                    .put(USER_NAME, event.getString(USER_NAME))
                                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_VISUALIZATION));

                            queries.put(queryId.get(), event.getLong(ID) + SEPARATOR + user.getLong(ID));

                            EventBusConstants.updateEvent(queryId.get(),
                                    String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()),
                                    EventBusConstants.EVENT_STATE_RUNNING);

                            var filters = event.containsKey(FILTERS) && !event.getJsonObject(FILTERS).isEmpty() ? JsonObject.mapFrom(event.remove(FILTERS)) : new JsonObject();

                            var dataSources = new JsonArray().addAll((JsonArray) event.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                            var buffer = Buffer.buffer();

                            var visualizationDataSource = dataSources.getJsonObject(0);

                            var entities = new HashMap<String, Object>();

                            var filteredGroupEntities = new HashSet<>();

                            var error = EMPTY_VALUE;

                            var subQueryId = event.getLong(SUB_QUERY_ID);

                            queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(subQueryId, EMPTY_VALUE);

                            if (visualizationDataSource.containsKey(CATEGORY) && (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.USER_NOTIFICATION.getName()) || visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.AUDIT.getName()) || visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.FLOW.getName()) || visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.PERFORMANCE_METRIC.getName())))
                            {
                                event.put("sub.query.remove", true);
                            }

                            else
                            {
                                customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(CommonUtil.getString(subQueryId), new JsonObject());
                            }

                            queryContexts.put(queryId.get(), new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));

                            if (!filters.isEmpty())
                            {
                                visualizationDataSource.put(FILTERS, filters);
                            }

                            VisualizationConstants.validateFilters(visualizationDataSource.getJsonObject(FILTERS));//for safe side if filter size gets > 3 so will be removing extra groups as DB not able to handle

                            if (Objects.requireNonNull(VisualizationDataSource.valueOfName(visualizationDataSource.getString(TYPE))) == VisualizationDataSource.EVENT_HISTORY)
                            {
                                event.put(ADMIN_ROLE, visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()) || adminRole);//as in policy trigger action data security will not be applied

                                if (visualizationDataSource.containsKey(CATEGORY) && visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(METRIC))
                                {
                                    Set<Integer> qualifiedObjects;

                                    if (user.getLong(ID).equals(DEFAULT_ID))//default
                                    {
                                        qualifiedObjects = objectIds;
                                    }

                                    else
                                    {
                                        qualifiedObjects = ObjectConfigStore.getStore().getUniqueObjectIdsByGroups(user.getJsonArray(User.USER_GROUPS));

                                        if (qualifiedObjects == null)
                                        {
                                            qualifiedObjects = new HashSet<>();
                                        }
                                    }

                                    var filterObject = false;

                                    var severities = new HashSet<>();

                                    var policies = new HashSet<>();

                                    var visualizationDataPoints = new JsonArray();

                                    var dataPoints = visualizationDataSource.getJsonArray(DATA_POINTS);

                                    for (var i = 0; i < dataPoints.size(); i++)
                                    {
                                        var visualizationDataPoint = dataPoints.getJsonObject(i);

                                        if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                        {
                                            visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                            visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                        }

                                        if (visualizationDataPoint.containsKey(ENTITY_TYPE) && (visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationGrouping.MONITOR.getName()) || visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationGrouping.GROUP.getName()) || visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationGrouping.TAG.getName())) && !visualizationDataPoint.getJsonArray(ENTITIES).isEmpty())
                                        {
                                            filterObject = true;
                                        }

                                        var qualifiedEntities = qualifyEntities(false, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, null, null, LOGGER, false);

                                        if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                        {
                                            visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                            entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

                                            visualizationDataPoint.put(PLUGINS, qualifiedEntities.remove(PLUGINS));

                                            visualizationDataPoints.add(visualizationDataPoint);
                                        }
                                    }

                                    if (!visualizationDataPoints.isEmpty())
                                    {
                                        event.put(ADMIN_ROLE, adminRole);

                                        applyFilter(VisualizationDataSource.PERFORMANCE_METRIC.getName(), event.put(OBJECT_FILTER, filterObject), false, visualizationDataSource, severities, policies, entities.keySet());

                                        visualizationDataSource.put(DATA_POINTS, visualizationDataPoints);

                                        visualizationDataSource.put(ENTITIES, entities);

                                        visualizationDataSource.put(PLUGINS, new ArrayList<>(new JsonArray().add(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName()).getList()));
                                    }

                                    else
                                    {
                                        error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                    }

                                }

                                else
                                {

                                    if (event.containsKey(PAGINATION_QUERY))
                                    {
                                        paginatedQuerySessions.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(event.getString(APIConstants.SESSION_ID) + COLUMN_SEPARATOR + event.getString(UI_EVENT_UUID), new JsonObject().mergeIn(event));

                                        visualizationDataSource.put(CATEGORY, VisualizationDataSource.LOG.getName());
                                    }

                                    error = prepareEventDataSource(eventColumns, event, visualizationDataSource, entities, user, qualifiedSources, filteredGroupEntities, LOGGER, eventCategories);
                                }

                                if (!visualizationDataSource.containsKey(LogEngineConstants.EVENT_CATEGORY) || visualizationDataSource.getString(LogEngineConstants.EVENT_CATEGORY).isEmpty())
                                {
                                    visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, visualizationDataSource.getString(TYPE).toLowerCase());
                                }
                            }

                            if (error.isEmpty())
                            {
                                var subQueryContext = new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonObject().mergeIn(visualizationDataSource)).put(VisualizationConstants.ADMIN_ROLE, adminRole ? YES : NO).put(VisualizationConstants.QUERY_ID, queryId.get()).put(VisualizationConstants.SUB_QUERY_ID, subQueryId);

                                setQueryParameters(event, subQueryContext, null, null);

                                buffer.appendByte(DatastoreConstants.OperationType.DATA_READ.getName()).appendBytes(subQueryContext.encode().getBytes());

                                EventBusConstants.updateEvent(queryId.get(), String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED, subQueryId, DateTimeUtil.timestamp()));

                                subQueries.put(subQueryId, new JsonObject().mergeIn(subQueryContext).put("cache", false).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));//ui requires same datasource as sent to identify in some custom widgets

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Visualization Event History Request generated time taken:" + (System.currentTimeMillis() - currentTime) + " ms for query id:" + queryId.get());
                                }

                                if (subQueryContext.getInteger(QUERY_PRIORITY) == QueryPriority.P0.getName())
                                {
                                    vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                            .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                }

                                else
                                {
                                    send(queryId.get(), new JsonObject()
                                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                            .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                }
                            }

                            else
                            {
                                customQueries.remove(queryId.get());

                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", error), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);
                            }
                        }

                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queuedQueryContexts.remove(id));
    }

    private void send(long id, JsonObject context)
    {
        if (runningQueries.size() == MotadataConfigUtil.getEventHistoryQueryQueueSize())
        {
            queuedQueryContexts.put(id, context);

            queuedQueries.add(id);
        }

        else
        {
            runningQueries.add(id);

            vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, context.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
        }
    }

    private void cleanUp(long queryId, long subQueryId)
    {
        //will be removing all subquerycontext

        if (queryStatuses.containsKey(queryId))
        {
            queryStatuses.get(queryId).remove(subQueryId);
        }

        subQueries.remove(subQueryId);
    }

    private void cleanUp(long queryId)
    {
        queryTrackers.remove(queryId);

        if (queryStatuses.containsKey(queryId))
        {
            queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
        }

        queries.remove(queryId);

        subQueryTrackers.remove(queryId);

        queryContexts.remove(queryId);

        customQueries.remove(queryId);
    }
}
