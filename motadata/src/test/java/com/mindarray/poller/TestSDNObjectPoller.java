/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSDNObjectPoller
{
    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollCiscovManageMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VMANAGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollCiscovSmartMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VSMART.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollCiscovBondMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VBOND.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollCiscovEdgeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollCiscovEdgeBGPRouteMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_BGP_ROUTE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollCiscovEdgeBGPNeighborMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_BGP_NEIGHBOR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollCiscovEdgeInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollCiscovEdgeTunnelMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_TUNNEL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPollCiscovEdgeTLOCMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_TLOC.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollCiscovManageSiteMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VMANAGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollCiscovEdgeHardwareSensorMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_VEDGE_HARDWARE_SENSOR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testPollCiscoMerakiMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testPollCiscoMerakiNetworkMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_NETWORK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testPollCiscoMerakiClientMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_CLIENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testPollCiscoMerakiVPNMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_CLIENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testPollCiscoMerakiSecurityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_SECURITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testPollCiscoMerakiSubnetMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_SUBNET.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testPollCiscoMerakiVLANMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_VLAN.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testPollCiscoMerakiInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testPollCiscoMerakiSwitchMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_SWITCH.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testPollCiscoMerakiSTPMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_STP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testPollCiscoMerakiRadioMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_RADIO.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testPollCiscoMerakiEthernetMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_ETHERNET.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollCiscoMerakiSSIDMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_SSID.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollCiscoMerakiChannelMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_MERAKI_CHANNEL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollCiscoACIMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_ACI.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testPollCiscoACIFabricMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_ACI_FABRIC.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testPollCiscoACITenantMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_ACI_TENANT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollCiscoACIEndpointMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.CISCO_ACI_ENDPOINT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testPollNSXTMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testPollNSXTFirewallMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_FIREWALL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testPollNSXTInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_NODE_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testPollNSXTNetworkMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_NETWORK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testPollNSXTProcessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_NODE_PROCESS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testPollNSXTTransportNodeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_TRANSPORT_NODE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testPollNSXTTunnelMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NSXT_TUNNEL.getName()), testContext), testContext);
    }

}
