/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SNMPTrapVendorCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(SNMPTrapVendorCacheStore.class, GlobalConstants.MOTADATA_STORE, "SNMP Trap Vendor Cache Store");
    private static final SNMPTrapVendorCacheStore STORE = new SNMPTrapVendorCacheStore();
    private final Map<String, String> items = new ConcurrentHashMap<>();

    private SNMPTrapVendorCacheStore()
    {
    }

    public static SNMPTrapVendorCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }

        else
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {

                try
                {
                    for (var entry : new JsonObject(new CipherUtil().decrypt(Bootstrap.vertx().fileSystem()
                            .readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + "vendors.db").toString()))
                            .getMap().entrySet())
                    {
                        if (entry.getValue() instanceof String)
                        {
                            items.put(entry.getKey(), CommonUtil.getString(entry.getValue()));
                        }
                    }

                    future.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                catch (Exception exception)
                {
                    future.fail(exception.getMessage());

                    LOGGER.error(exception);
                }

            }, result ->
            {

                if (result.succeeded())
                {
                    promise.complete();
                }

                else
                {
                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }

    public String getItem(String trapIndex)
    {
        return items.getOrDefault(trapIndex, null);
    }

    public JsonArray getItems()
    {
        return new JsonArray(items.values().stream().distinct().collect(Collectors.toList()));
    }
}
