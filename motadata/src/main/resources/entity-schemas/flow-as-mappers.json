{"entity": "Flow AS Mapper", "table": "tbl_config_flow_as_mapper", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "flow.as.mapper.name", "title": "AS Name", "type": "string", "rules": ["required"]}, {"name": "flow.as.mapper.number", "title": "AS Number", "type": "numeric", "rules": ["required"]}, {"name": "flow.as.mapper.organization", "title": "Organization", "type": "string", "rules": ["required"]}, {"name": "flow.as.mapper.group", "title": "IP Group/IP", "type": "list", "rules": ["required"]}]}