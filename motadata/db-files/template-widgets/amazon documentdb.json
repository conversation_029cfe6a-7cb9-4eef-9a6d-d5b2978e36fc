[{"_type": "0", "id": 10000000000065, "visualization.name": "CPU", "visualization.description": "DocumentDB CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.documentdb.cpu.percent.last"}]}}}}, {"_type": "0", "id": 10000000000066, "visualization.name": "Buffer <PERSON><PERSON>", "visualization.description": "DocumentDB Buffer <PERSON>ache Hit Ratio", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.buffer.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.buffer.cache.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.buffer.cache.hit.ratio.percent", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "Buffer <PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.documentdb.buffer.cache.hit.ratio.percent.last"}]}}}}, {"_type": "0", "id": 10000000000067, "visualization.name": "Free Local Storage", "visualization.description": "DocumentDB Free Local Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.local.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.local.storage.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.local.storage.free.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Free Local Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.documentdb.local.storage.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000068, "visualization.name": "Connection", "visualization.description": "DocumentDB Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.database.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.database.max.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.database.connections"}, {"type": "metric", "data.point": "aws.documentdb.database.max.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.database.max.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connection", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "aws.documentdb.database.connections.last"}, {"label": "Max", "value": "aws.documentdb.database.max.connections.last"}]}}}}, {"_type": "0", "id": 10000000000069, "visualization.name": "Latency", "visualization.description": "DocumentDB Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.read.latency.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.write.latency.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.read.latency.seconds"}, {"type": "metric", "data.point": "aws.documentdb.write.latency.seconds"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.read.latency.seconds", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.documentdb.read.latency.seconds.last"}, {"label": "Write", "value": "aws.documentdb.write.latency.seconds.last"}]}}}}, {"_type": "0", "id": 10000000000070, "visualization.name": "Throughput", "visualization.description": "DocumentDB Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.disk.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.disk.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.documentdb.disk.read.bytes.per.sec"}, {"type": "metric", "data.point": "aws.documentdb.disk.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.documentdb.disk.write.bytes.per.sec", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.documentdb.disk.read.bytes.per.sec.last"}, {"label": "Write", "value": "aws.documentdb.disk.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000071, "visualization.name": "CPU Utilization", "visualization.description": "DocumentDB CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000072, "visualization.name": "Buffer <PERSON><PERSON>", "visualization.description": "DocumentDB Buffer <PERSON>ache Hit Ratio", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.buffer.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000073, "visualization.name": "Volume Used", "visualization.description": "DocumentDB Volume Used", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.volume.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000074, "visualization.name": "Database Connection", "visualization.description": "DocumentDB Database Connection", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.database.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000075, "visualization.name": "Free Memory", "visualization.description": "DocumentDB Free Memory", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000076, "visualization.name": "Free Local Storage", "visualization.description": "DocumentDB Free Local Storage Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.local.storage.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000077, "visualization.name": "DBCluster Minimum/Maximum Replica Lag", "visualization.description": "DocumentDB DBCluster Minimum/Maximum Replica Lag", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.cluster.replica.maximum.lag.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.cluster.replica.minimum.lag.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000078, "visualization.name": "Volume Read/Write IOPS", "visualization.description": "DocumentDB Volume Read/Write IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.volume.read.ops", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.volume.write.ops", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000079, "visualization.name": "Disk Read/Write Throughput", "visualization.description": "DocumentDB Disk Read/Write Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.disk.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.disk.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000080, "visualization.name": "Read/Write Latency", "visualization.description": "DocumentDB Read/Write Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.read.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.write.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000081, "visualization.name": "Network Received/Sent Throughput", "visualization.description": "DocumentDB Network Receive/Sent Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.network.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.network.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000082, "visualization.name": "S<PERSON>p <PERSON>", "visualization.description": "DocumentDB Swap Usage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.swap.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000083, "visualization.name": "DB Instance Replica Lag", "visualization.description": "DocumentDB DB Instance Replica Lag", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.instance.replica.lag.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000084, "visualization.name": "Disk Queue Depth", "visualization.description": "DocumentDB Disk Queue Depth", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.disk.queue.depth", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000085, "visualization.name": "Documents Inserted/Deleted/Updated/Returned", "visualization.description": "DocumentDB Documents Inserted/Deleted/Updated/Returned", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.deleted.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.inserted.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.returned.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.updated.documents", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000086, "visualization.name": "Opcounters Insert/Query/Delete/Update/Get more", "visualization.description": "DocumentDB Opcounters Insert/Query/Delete/Update/Get more", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.delete.opcounters", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.getmore.opcounters", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.insert.opcounters", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.update.opcounters", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.query.opcounters", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000087, "visualization.name": "Transactions Started/Committed/Aborted/Opened", "visualization.description": "Transactions Started/Committed/Aborted/Opened", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.aborted.transactions", "aggregator": "avg", "entity.type": "monitor"}, {"data.point": "aws.documentdb.opened.transactions", "aggregator": "avg", "entity.type": "monitor"}, {"data.point": "aws.documentdb.started.transactions", "aggregator": "avg", "entity.type": "monitor"}, {"data.point": "aws.documentdb.committed.transactions", "aggregator": "avg", "entity.type": "monitor"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000000088, "visualization.name": "Snapshot/Backup/Total Backup Storage", "visualization.description": "DocumentDB Snapshot/Backup/Total Backup Storage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.documentdb.backup.retention.period.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.backup.storage.billed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.documentdb.snapshot.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]