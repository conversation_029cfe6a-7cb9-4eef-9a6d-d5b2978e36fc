/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.NetServerOptions;
import io.vertx.core.parsetools.RecordParser;

import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * A verticle responsible for listening for log messages over TCP connections.
 * <p>
 * The TCPLogListener creates a TCP server that listens on a configured port for
 * incoming log messages. It performs the following tasks:
 * <ul>
 *   <li>Listens for TCP connections on the configured port</li>
 *   <li>Parses incoming log messages using newline delimiters</li>
 *   <li>Creates log events with metadata (source IP, timestamp, etc.)</li>
 *   <li>Forwards log events to the appropriate event bus address</li>
 *   <li>Supports streaming log messages for real-time monitoring</li>
 *   <li>Tracks log quota usage</li>
 * </ul>
 * <p>
 * The listener handles connection lifecycle events (connect, data, end, error)
 * and ensures proper cleanup of resources when connections are closed.
 * <p>
 * It can be configured to send logs directly to the log processing pipeline or
 * to a log processor component based on the PROCESSOR_ENABLED setting.
 */
public class TCPLogListener extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(TCPLogListener.class, GlobalConstants.MOTADATA_LOG, "TCP Log Listener");

    /**
     * Flag indicating whether log processor is enabled
     */
    private static final boolean PROCESSOR_ENABLED = MotadataConfigUtil.logProcessorEnabled();

    /**
     * Flag indicating whether log streaming is enabled
     */
    private boolean streaming = false;

    /**
     * Initializes and starts the TCP log listener.
     * <p>
     * This method sets up a TCP server that listens for incoming log messages.
     * It configures the server with the following options:
     * <ul>
     *   <li>Address reuse enabled for quick restart capability</li>
     *   <li>Port reuse enabled for multiple listeners on the same port</li>
     *   <li>TCP keep-alive enabled for connection stability</li>
     * </ul>
     * <p>
     * For each connection, it sets up handlers for:
     * <ul>
     *   <li>Connection establishment</li>
     *   <li>Data reception</li>
     *   <li>Connection termination</li>
     *   <li>Error handling</li>
     * </ul>
     * <p>
     * Incoming log messages are parsed using newline delimiters and converted to
     * event objects that are sent to the appropriate event bus address.
     *
     * @param promise A Promise that will be completed when the verticle has been started
     * @throws Exception If an error occurs during startup
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Create a TCP server with socket options for reliability and performance
        vertx.createNetServer(new NetServerOptions().setReuseAddress(true).setReusePort(true).setTcpKeepAlive(true)).connectHandler(socket ->
        {
            // Log connection information in debug mode
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug("[TCP] connection from :" + socket.remoteAddress().host() + "->" + socket.remoteAddress().port());
            }

            // Create a record parser that splits incoming data on newline characters
            var parser = RecordParser.newDelimited("\r\n", socket);

            // Also handle plain newlines (without carriage return)
            parser.delimitedMode("\n");

            // Handle connection end event
            parser.endHandler(result ->
            {
                // Close the socket when the connection ends
                socket.close();

                // Log connection closure in debug mode
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug("[TCP] connection closed : " + socket.remoteAddress().host() + "->" + socket.remoteAddress().port());
                }

                // Handle exceptions during parsing or processing
            }).exceptionHandler(exception ->
            {
                // Log the exception
                LOGGER.error(exception);

                // Close the socket on error
                socket.close();

                // Handle incoming data packets
            }).handler(packet ->
            {
                // Check if we're in EVENT_COLLECTOR mode or if we have quota available
                if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR || LicenseUtil.updateUsedLogQuota(packet.getBytes().length))
                {
                    // Create a log event object with metadata
                    var event = new JsonObject()
                            .put(EventBusConstants.EVENT_VOLUME_BYTES, packet.getBytes().length)
                            .put(EventBusConstants.EVENT, packet.toString().trim())
                            .put(EventBusConstants.EVENT_SOURCE, socket.remoteAddress().host())
                            .put(LogEngineConstants.RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(EVENT_TYPE, EVENT_LOG);

                    // Route the event based on processor configuration
                    if (PROCESSOR_ENABLED)
                    {
                        // Send to log processor if enabled
                        vertx.eventBus().send(EVENT_LOG_PROCESS, event);
                    }
                    else
                    {
                        // Send directly to log event handler
                        vertx.eventBus().send(EVENT_LOG, event);
                    }

                    // If streaming is enabled, broadcast the event for real-time monitoring
                    if (streaming)
                    {
                        vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST,
                                new JsonObject()
                                        .put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName())
                                        .put(EVENT_CONTEXT, event));
                    }

                    // Log detailed trace information if enabled
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("log received from TCP source: %s %s",
                                socket.remoteAddress().host(), CommonUtil.getString(packet)));
                    }
                }
                // If quota is exceeded or in wrong mode, the log is silently dropped
            });

            // Start listening on the configured TCP port
        }).listen(MotadataConfigUtil.getTCPPort(), "0.0.0.0", result ->
        {
            if (result.succeeded())
            {
                // Log successful server start
                LOGGER.info(String.format("LOG listener connected port %s", MotadataConfigUtil.getTCPPort()));

                // Complete the promise to signal successful startup
                promise.complete();
            }
            else
            {
                // Log server start failure
                LOGGER.info(String.format("LOG listener failed to connected : %s ", result.cause()));

                // Fail the promise with the cause
                promise.fail(result.cause());
            }
        });

        // Set up a consumer for change notifications to control streaming behavior
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            // Extract the notification type from the message
            switch (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                // Enable streaming when a START_LOG_TAIL notification is received
                case START_LOG_TAIL -> streaming = true;

                // Disable streaming when a STOP_LOG_TAIL notification is received
                case STOP_LOG_TAIL -> streaming = false;

                // Ignore other notification types
                default ->
                {
                    // do nothing for other notification types
                }
            }

            // Log any exceptions that occur during message processing
        }).exceptionHandler(LOGGER::error);
    }
}
