/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.Logger;
import io.github.artsok.ParameterizedRepeatedIfExceptionsTest;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_CATEGORY;
import static com.mindarray.api.AIOpsObject.OBJECT_TYPE;
import static com.mindarray.api.ApplicationMapper.*;
import static com.mindarray.api.Group.*;
import static com.mindarray.api.Metric.METRIC_NAME;
import static com.mindarray.api.SNMPTrapForwarder.*;
import static com.mindarray.api.SNMPTrapProfile.*;
import static com.mindarray.api.SystemProcess.*;
import static com.mindarray.api.User.*;
import static com.mindarray.api.UserRole.USER_ROLE_CONTEXT;
import static com.mindarray.api.UserRole.USER_ROLE_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@Timeout(80 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public
class TestAPIPermission
{

    private static final JsonObject PERMISSION_CONTEXT = new JsonObject();

    private static final JsonObject IDS = new JsonObject();
    private static final JsonObject VIEW_CONTEXT = new JsonObject()
            .put(USER_NAME, "viewUser")
            .put(USER_FIRST_NAME, "motadata1")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject CREATE_CONTEXT = new JsonObject()
            .put(USER_NAME, "createUser")
            .put(USER_FIRST_NAME, "motadata2")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject UPDATE_CONTEXT = new JsonObject()
            .put(USER_NAME, "updateUser")
            .put(USER_FIRST_NAME, "motadata3")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final JsonObject DELETE_CONTEXT = new JsonObject()
            .put(USER_NAME, "deleteUser")
            .put(USER_FIRST_NAME, "motadata4")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final Logger LOGGER = new Logger(TestAPIPermission.class, MOTADATA_API, "Test API Permission");
    private static long id;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    static Stream<Arguments> createRoles()
    {
        return Stream.of(
                Arguments.of("settings.role.view", new JsonArray().add("user-settings:read").add("system-settings:read").add("discovery-settings:read").add("group-settings:read").add("monitor-settings:read").add("snmp-trap-settings:read").add("flow-settings:read").add("my-account-settings:read")),

                Arguments.of("settings.role.create", new JsonArray().add("user-settings:read-write").add("system-settings:read-write").add("discovery-settings:read-write").add("group-settings:read-write").add("monitor-settings:read-write").add("flow-settings:read-write").add("snmp-trap-settings:read-write")),

                Arguments.of("settings.role.update", new JsonArray().add("user-settings:read-write").add("system-settings:read-write").add("discovery-settings:read-write").add("group-settings:read-write").add("monitor-settings:read-write").add("snmp-trap-settings:read-write").add("flow-settings:read-write").add("my-account-settings:read-write")),

                Arguments.of("settings.role.delete", new JsonArray().add("user-settings:delete").add("system-settings:delete").add("discovery-settings:delete").add("group-settings:delete").add("snmp-trap-settings:delete").add("monitor-settings:delete").add("flow-settings:delete")));
    }

    static Stream<Arguments> createUsers()
    {
        return Stream.of(
                Arguments.of(VIEW_CONTEXT, "settings.role.view", "view.user.id"),

                Arguments.of(CREATE_CONTEXT, "settings.role.create", "create.user.id"),

                Arguments.of(UPDATE_CONTEXT, "settings.role.update", "update.user.id"),

                Arguments.of(DELETE_CONTEXT, "settings.role.delete", "delete.user.id"));
    }

    static Stream<Arguments> generateToken()
    {
        return Stream.of(
                Arguments.of(VIEW_CONTEXT, VIEW_ACCESS_TOKEN),

                Arguments.of(CREATE_CONTEXT, CREATE_ACCESS_TOKEN),

                Arguments.of(UPDATE_CONTEXT, UPDATE_ACCESS_TOKEN),

                Arguments.of(DELETE_CONTEXT, DELETE_ACCESS_TOKEN));
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
    @MethodSource("createRoles")
    @Order(1)
    void testCreateRole(String roleName, JsonArray roleContext, VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_ROLE_NAME, roleName).put(USER_ROLE_CONTEXT, roleContext);

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserRoleConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.USER_ROLE.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            PERMISSION_CONTEXT.put(roleName, response.bodyAsJsonObject().getLong(ID));
                            testContext.completeNow();
                        })));

    }

    @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
    @MethodSource("createUsers")
    @Order(2)
    void testCreateUser(JsonObject userContext, String userRoleName, String userId, VertxTestContext testContext, TestInfo testInfo)
    {
        var context = userContext.copy().put(USER_GROUPS, new JsonArray().add(0L))
                .put(USER_ROLE, PERMISSION_CONTEXT.getLong(userRoleName));

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            IDS.put(userId, response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
    @MethodSource("generateToken")
    @Order(3)
    void testGetToken(JsonObject userContext, String permissionName, VertxTestContext testContext)
    {

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, userContext.getString(USER_NAME))
                        .put(USER_PASSWORD, userContext.getString(USER_PASSWORD)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                            Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                            PERMISSION_CONTEXT.put(permissionName, "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN));

                            testContext.completeNow();
                        })));
    }

    //View Permission Check

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetAllApplicationMapperHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, ApplicationMapperConfigStore.getStore(), null);

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllApplicationMapperHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetAllApplicationMapperHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetAllApplicationMapperHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(APPLICATION_MAPPER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read");

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetAllBusinessHourHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, BusinessHourConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testGetAllBusinessHourHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testGetAllBusinessHourHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testGetAllBusinessHourHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(BUSINESS_HOUR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetAllCustomMonitoringFieldHavingViewPermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(CUSTOM_MONITORING_FIELD_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, CustomMonitoringFieldConfigStore.getStore(), null);
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testGetAllCustomMonitoringFieldHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(CUSTOM_MONITORING_FIELD_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetAllCustomMonitoringFieldHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(CUSTOM_MONITORING_FIELD_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testGetAllRemoteEventProcessorHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, RemoteEventProcessorConfigStore.getStore(), new JsonArray().add(STATUS).add(DURATION).add(DISABLED).add(EventBusConstants.EVENT_TIMESTAMP).add(MotadataAppManager.HEARTBEAT_STATE));
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testGetAllRemoteEventProcessorHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testGetAllRemoteEventProcessorHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testGetAllRemoteEventProcessorHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(REMOTE_EVENT_PROCESSOR_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGetAllSNMPDeviceCatalogHavingViewPermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SNMPDeviceCatalogConfigStore.getStore(), null);
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testGetAllSNMPDeviceCatalogHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testGetAllSNMPDeviceCatalogHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testGetAllSNMPDeviceCatalogHavingDeletePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");
                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testGetAllSystemFileHavingViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testGetAllSystemFileHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testGetAllSystemFileHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testGetAllSystemFileHavingDeletePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_FILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testGetAllSystemProcessHavingViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testGetAllSystemProcessHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testGetAllSystemProcessHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testGetAllSystemProcessHavingDeletePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    //Create Permission Check
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCreateSystemProcessHavingCreatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SYSTEM_PROCESS, "TestProcess").put(SYSTEM_PROCESS_APP_TYPE, "Apache HTTP").put(SYSTEM_PROCESS_OS, "Linux");

        TestAPIUtil.post(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "System Process"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            id = response.bodyAsJsonObject().getLong(ID);

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testCreateSystemProcessHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(SYSTEM_PROCESS, "TestProcess").put(SYSTEM_PROCESS_APP_TYPE, "Apache HTTP").put(SYSTEM_PROCESS_OS, "Linux");

        TestAPIUtil.post(SYSTEM_PROCESS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read-write");

                            testContext.completeNow();

                        })));

    }

    //update Permission Check

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testUpdateSystemProcessHavingUpdatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SYSTEM_PROCESS, "TestProcess1").put(SYSTEM_PROCESS_APP_TYPE, "Active Directory");

        TestAPIUtil.put(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SystemProcessConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "System Process"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testUpdateSystemProcessHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(SYSTEM_PROCESS, "TestProcess1").put(SYSTEM_PROCESS_APP_TYPE, "Active Directory");

        TestAPIUtil.put(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read-write");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testUpdateSystemProcessHavingDeletePermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(SYSTEM_PROCESS, "TestProcess1").put(SYSTEM_PROCESS_APP_TYPE, "Active Directory");

        TestAPIUtil.put(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read-write");

                            testContext.completeNow();
                        })));
    }

    //delete Permission check

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testDeleteProcessHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Delete");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testDeleteProcessHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Delete");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testDeleteProcessHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Delete");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testDeleteProcessHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SYSTEM_PROCESS_API_ENDPOINT + "/" + id, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SystemProcessConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "System Process"));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testGetAllObjectHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + OBJECT_CATEGORY + "=" + new JsonArray().add(NMSConstants.Category.NETWORK.getName()), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testGetMetricHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(METRIC_API_ENDPOINT + "?filter=" + new JsonObject().put(OBJECT_TYPE, NMSConstants.Type.SWITCH.getName())
                        .put(METRIC_NAME, NMSConstants.Type.AVAILABILITY.getName()), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN))
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Monitor settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testCreateApplicationMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(APPLICATION_MAPPER_NAME, "SSH_New").put(APPLICATION_MAPPER_PROTOCOL, "TCP").put(APPLICATION_MAPPER_PORT, 12);

        TestAPIUtil.post(APPLICATION_MAPPER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(ApplicationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "Application Mapper"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            PERMISSION_CONTEXT.put("application.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testCreateApplicationMapperHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(APPLICATION_MAPPER_NAME, "SSH").put(APPLICATION_MAPPER_PROTOCOL, "TCP").put(APPLICATION_MAPPER_PORT, 12);

        TestAPIUtil.post(APPLICATION_MAPPER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testCreateApplicationMapperHavingDeletePermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(APPLICATION_MAPPER_NAME, "SSH").put(APPLICATION_MAPPER_PROTOCOL, "TCP").put(APPLICATION_MAPPER_PORT, 12);

        TestAPIUtil.post(APPLICATION_MAPPER_API_ENDPOINT,
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testUpdateApplicationMapperHavingUpdatePermission(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject()
                .put(APPLICATION_MAPPER_NAME, "SSH1read-write").put(APPLICATION_MAPPER_PROTOCOL, "UDP")
                .put(APPLICATION_MAPPER_PORT, 809);

        TestAPIUtil.put(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(ApplicationMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Application Mapper"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testUpdateApplicationMapperHavingViewPermission(VertxTestContext testContext)
    {

        var context = new JsonObject()
                .put(APPLICATION_MAPPER_NAME, "SSH1read-write").put(APPLICATION_MAPPER_PROTOCOL, "UDP")
                .put(APPLICATION_MAPPER_PORT, 22);

        TestAPIUtil.put(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read-write");

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testUpdateApplicationMapperHavingDeletePermission(VertxTestContext testContext)
    {

        var context = new JsonObject()
                .put(APPLICATION_MAPPER_NAME, "SSH1read-write").put(APPLICATION_MAPPER_PROTOCOL, "UDP")
                .put(APPLICATION_MAPPER_PORT, 22);

        TestAPIUtil.put(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Read-write");

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testDeleteApplicationMapperHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertDeleteEntityTestResult(ApplicationMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_DELETED, "Application Mapper"));

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testDeleteApplicationMapperHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Delete");

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testDeleteApplicationMapperHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Delete");

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testDeleteApplicationMapperHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(APPLICATION_MAPPER_API_ENDPOINT + PERMISSION_CONTEXT.getLong("application.mapper"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(),
                        PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), testContext.succeeding(response -> testContext.verify(() ->
                {
                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Flow settings Delete");

                    testContext.completeNow();

                })));
    }


    //Create Permission Check
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testCreateGroupHavingCreatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermission")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            PERMISSION_CONTEXT.put("group", response.bodyAsJsonObject().getLong(ID));

                            var count = new AtomicInteger();

                            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
                            {
                                if (count.getAndIncrement() < 3)
                                {
                                    var user = UserConfigStore.getStore().getItemByValue(USER_NAME, CREATE_CONTEXT.getString(USER_NAME));

                                    LOGGER.trace(String.format("user : %s", user));

                                    if (user != null && !user.isEmpty() && user.containsKey(USER_GROUPS) && user.getJsonArray(USER_GROUPS).contains(response.bodyAsJsonObject().getLong(ID)))
                                    {
                                        assertTrue(user.getJsonArray(USER_GROUPS).contains(response.bodyAsJsonObject().getLong(ID)));

                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                }
                                else
                                {
                                    testContext.failNow("failed to create group with different user");

                                    TestUtil.vertx().cancelTimer(timer);
                                }
                            });
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testViewGroupHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testDeleteGroupHavingCreatePermission(VertxTestContext testContext)
    {


        TestAPIUtil.delete(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testCreateGroupHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermission")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testViewGroupHavingViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, PERMISSION_CONTEXT.getLong("group"), GroupConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testDeleteGroupHavingViewPermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testUpdateGroupHavingViewPermission(VertxTestContext testContext)
    {

        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermissionread-write")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.put(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testCreateGroupHavingUpdatePermission(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermission")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testViewGroupHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testDeleteGroupHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testUpdateGroupHavingUpdatePermission(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermissionread-write")
                .put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.put(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.GROUP.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testCreateGroupHavingDeletePermission(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermission")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.post(GROUP_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testViewGroupHavingDeletePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testUpdateGroupHavingDeletePermission(VertxTestContext testContext)
    {

        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupPermissionread-write")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        TestAPIUtil.put(GROUP_API_ENDPOINT + PERMISSION_CONTEXT.getLong("group"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Group settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testDeleteGroupHavingDeletePermission(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "adminGroup")
                .put(FIELD_PARENT_GROUP, 0L)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

        // create group with admin user
        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestAPIUtil.delete(GROUP_API_ENDPOINT + response.bodyAsJsonObject().getLong(ID), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)),
                                    testContext.succeeding(result ->
                                            testContext.verify(() ->
                                            {
                                                TimeUnit.SECONDS.sleep(2);

                                                TestAPIUtil.assertDeleteEntityTestResult(GroupConfigStore.getStore(), result.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.GROUP.getName()));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testCreateSNMPTrapProfileHavingCreatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDrop" + System.currentTimeMillis())
                .put(SNMP_TRAP_PROFILE_OID, ".1.3.6.1.4.1.321.10.111447")
                .put(SNMP_TRAP_PROFILE_DROP_STATUS, YES)
                .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
                .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Trap Profile"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            PERMISSION_CONTEXT.put("snmp.trap.profile", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(70)
    void testViewSNMPTrapProfileHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(71)
    void testDeleteSNMPTrapProfileHavingCreatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(73)
    void testCreateSNMPTrapProfileHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDrop" + System.currentTimeMillis())
                .put(SNMP_TRAP_PROFILE_OID, ".1.3.6.1.4.1.321.10.111447")
                .put(SNMP_TRAP_PROFILE_DROP_STATUS, YES)
                .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
                .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(74)
    void testViewSNMPTrapProfileHavingViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, PERMISSION_CONTEXT.getLong("snmp.trap.profile"), SNMPTrapProfileConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(75)
    void testDeleteSNMPTrapProfileHavingViewPermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(76)
    void testUpdateSNMPTrapProfileHavingViewPermission(VertxTestContext testContext)
    {

        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testDrop");

        TestAPIUtil.put(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(78)
    void testUpdateSNMPTrapProfileHavingUpdatePermission(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDropread-write");

        TestAPIUtil.put(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SNMPTrapProfileConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "SNMP Trap Profile"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(79)
    void testDeleteSNMPTrapProfileHavingUpdatePermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Delete");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(80)
    void testViewSNMPTrapProfileHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(81)
    void testCreateSNMPTrapForwarderHavingCreatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertTrue(PERMISSION_CONTEXT.getLong("snmp.trap.profile") != null && PERMISSION_CONTEXT.getLong("snmp.trap.profile") != null);

        var context = new JsonObject().put(SNMP_TRAP_FORWARDER_NAME, "trap-forwarder-auto-clear")
                .put(SNMP_TRAP_FORWARDER_PROFILES, new JsonArray()
                        .add(PERMISSION_CONTEXT.getLong("snmp.trap.profile")))
                .put(SNMP_TRAP_FORWARDER_DESTINATION_HOST, "localhost")
                .put(SNMP_TRAP_FORWARDER_DESTINATION_PORT, 7894);

        TestAPIUtil.post(SNMP_TRAP_FORWARDER_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), context, testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertCreateEntityTestResult(SNMPTrapForwarderConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    PERMISSION_CONTEXT.put("snmp.trap.forwarder", response.bodyAsJsonObject().getLong(ID));

                    testContext.completeNow();

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(82)
    void testViewSNMPTrapForwarderHavingCreatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.forwarder"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)), testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read");

                    testContext.completeNow();

                })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(83)
    void testDeleteSNMPTrapForwarderHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SNMP_TRAP_FORWARDER_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.forwarder"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), testContext.succeeding(response ->
                testContext.verify(() ->
                {

                    TestAPIUtil.assertDeleteEntityTestResult(GroupConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.SNMP_TRAP_FORWARDER.getName()));

                    testContext.completeNow();

                })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(84)
    void testCreateSNMPTrapForwarderHavingDeletePermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDrop" + System.currentTimeMillis())
                .put(SNMP_TRAP_PROFILE_OID, ".1.3.6.1.4.1.321.10.111447")
                .put(SNMP_TRAP_PROFILE_DROP_STATUS, YES)
                .put(SNMP_TRAP_PROFILE_SEVERITY, "Warning")
                .put(SNMP_TRAP_PROFILE_TRANSLATOR, "Received trap from motadata $1 is $2");

        TestAPIUtil.post(SNMP_TRAP_PROFILE_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read-write");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(85)
    void testUpdateSNMPTrapForwarderHavingDeletePermission(VertxTestContext testContext)
    {

        var context = new JsonObject().put(SNMP_TRAP_PROFILE_NAME, "testProfileDropread-write");

        TestAPIUtil.put(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read-write");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(86)
    void testViewSNMPTrapForwarderHavingDeletePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "Snmp trap settings Read");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(87)
    void testDeleteSNMPTrapProfileHavingDeletePermission(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SNMP_TRAP_PROFILE_API_ENDPOINT + "/" + PERMISSION_CONTEXT.getLong("snmp.trap.profile"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(DELETE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SNMPTrapProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "SNMP Trap Profile"));

                            testContext.completeNow();

                        })));
    }

    //Uncomment it once 3177 merge into master
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(88)
    void testGetMyProfileHavingViewPermission(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(MY_ACCOUNT_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(89)
    void testGetMyProfileHavingUpdatePermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(MY_ACCOUNT_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "My account settings Read");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(90)
    void testUpdateMyProfileHavingViewPermission(VertxTestContext testContext)
    {
        var context = new JsonObject().put(USER_PREFERENCES, new JsonObject().put("ui.theme", "black"));

        TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + IDS.getLong("view.user.id"), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(VIEW_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "My account settings Read-write");

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(91)
    void testUpdateMyProfileHavingUpdatePermission(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_PREFERENCES, new JsonObject().put("ui.theme", "black"));

        TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + IDS.getLong("update.user.id"),
                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(UPDATE_ACCESS_TOKEN)), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.MY_ACCOUNT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(92)
    void testGetMyProfileWithoutHavingViewPermission(VertxTestContext testContext)
    {
        TestAPIUtil.get(MY_ACCOUNT_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), PERMISSION_CONTEXT.getString(CREATE_ACCESS_TOKEN)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertForbiddenRequestTestResult(response, "My account settings Read");

                            testContext.completeNow();
                        })));
    }


}
