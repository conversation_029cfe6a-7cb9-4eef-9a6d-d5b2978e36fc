/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author          Notes
 *   5-Mar-2025     Bharat          MOTADATA-4740: Two factor authentication 2FA
 *   20-Feb-2025    Pruthviraj      MOTADATA-4904: Error message added for netroute API
 *   Date           Author      Notes
 *   5-Mar-2025     Bharat      MOTADATA-4740: Two factor authentication 2FA
 *   24-Mar-2025    Chandresh   MOTADATA-5426: Docker discovery and polling support added
 */
package com.mindarray;

import com.mindarray.api.Discovery;
import com.mindarray.nms.NMSConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.*;

public final class ErrorMessageConstants
{
    public static final String API_INVALID_INPUT_PARAMETERS = "Connection failure: Invalid input parameters";
    public static final String API_REQUEST_BODY_MISSING = "Request body missing";
    public static final String API_ACCESS_FAILED_NOT_AUTHORIZED = "Unauthorized access: Client is not allowed to access API - %s";
    public static final String INTERNAL_SERVER_EXCEPTION = "Internal server exception, Possible reason: %s";
    public static final String INTEGRATION_DOWN = "%s Integration is Down, Possible reason: %s";
    public static final String UNABLE_TO_CONNECT = "Unable to connect with server";
    public static final String API_INVALID_ID = "Invalid id value";
    public static final String API_FIELD_REQUIRED = "Missing information: %s is a required field";
    public static final String API_FIELD_NOT_REQUIRED = "%s is not required";
    public static final String API_INVALID_TYPE_VALUE = "The value entered for %s should be of type %s";
    public static final String API_MAXIMUM_VALUE_RULE = "The maximum allowed value for %s is %s";
    public static final String API_MINIMUM_VALUE_RULE = "The minimum allowed value for %s is %s";
    public static final String API_RANGE_VALUE_RULE = "%s should have a value in the range %s";
    public static final String API_RANGE_DIVIDE_VALUE_RULE = "%s should be divisible of %s";
    public static final String API_PORT_RULE = "%s should have value in the range 0-65535";
    public static final String API_INVALID_IP_ADDRESS_RULE = "%s is not a valid ip address";
    public static final String API_INVALID_IP_SUBNET_RULE = "%s is not a valid subnet cidr";
    public static final String API_INVALID_IP_ADDRESS_RANGE_RULE = "%s is not a valid ip address range";
    public static final String API_INVALID_CSV_FILE = "Invalid CSV File";
    public static final String API_INVALID_VALUE_RULE = "Value of %s is invalid";
    public static final String API_HOST_ADDRESS_NOT_FOUND = "Failed to find host address";
    public static final String API_FIELD_UNIQUE_RULE = "%s is not unique";
    public static final String API_FIELD_VALUE_NOT_FOUND = "%s does not exist";
    public static final String ENTITY_CREATE_FAILED = "Failed to create %s, Possible reason: %s";
    public static final String ENTITY_UPDATE_FAILED = "Failed to update %s, Possible reason: %s";
    public static final String ENTITY_DELETE_FAILED = "Failed to delete %s, Possible reason: %s";
    public static final String ENTITY_DELETE_FAILED_IN_USED = "Failed to delete %s, Possible reason: Entity is in use";
    public static final String ENTITY_DELETE_NOT_ALLOWED = "Unable to remove default %s";
    public static final String ENTITY_UPDATE_NOT_ALLOWED = "Default %s is not allowed to update";
    public static final String API_REQUEST_BAD_PARAMETER = "Invalid Parameter: %s";
    public static final String AGENT_NOT_FOUND = "No Agent found with id: %s";
    public static final String REMOTE_EVENT_PROCESSOR_NOT_FOUND = "No Remote Event Processor found with id: %s";
    public static final String REMOTE_EVENT_PROCESSOR_BUSY = "Remote Event Processor %s is busy. Please wait a few minutes and try again.";
    public static final String REMOTE_EVENT_PROCESSOR_NOT_REACHABLE = "Remote Event Processor %s is not reachable";

    public static final String REMOTE_EVENT_PROCESSOR_REGISTRATION_FAILED = "Remote event processor %s registration failed. Possible reason : %s";
    public static final String AGENT_BUSY = "Agent %s is busy. Please wait a few minutes and try again.";
    public static final String AGENT_NOT_REACHABLE = "Agent %s is not reachable";
    public static final String AGENT_REGISTRATION_FAILED = "Agent %s registration failed. Possible reason : %s";
    public static final String AGENT_REGISTRATION_DUPLICATE_ERROR = "Agent %s registration failed because it is already registered";
    public static final String PASSWORD_POLICY_NOT_SATISFIED = "Password Policy Violation";
    public static final String PASSWORD_POLICY_NOT_FOUND = "Password Policy Not Found";
    public static final String INCORRECT_OLD_PASSWORD = "Old Password You Entered Was Incorrect";
    public static final String USER_NOT_FOUND = "User not found";
    public static final String USER_DISABLED = "Account Disabled. Please contact your Motadata Administrator.";
    public static final String USER_EMAIL_NOT_SET = "Email address is not updated in the user profile";
    public static final String TWO_FACTOR_NOT_ENABLED = "Two-factor authentication is not enabled";
    public static final String TEMPORARY_PASSWORD_EXPIRED = "Temporary password expired";
    public static final String OTP_EXPIRED = "Your One-Time Password (OTP) has expired. Please request a new code to continue";
    public static final String TOTP_INVALID = "Your Time Based One-Time Password is invalid";
    public static final String RESET_PASSWORD_FAILED = "Password Reset Failed. Possible reason: %s";
    public static final String RESET_MFA_FAILED = "Two Factor Authentication Reset Failed. Possible reason: %s";
    public static final String RESET_PASSWORD_USER_NOT_AUTHORIZED = "User %s is not authorized to reset password";
    public static final String RESET_PASSWORD_SYSTEM_USER_ERROR = "You can not reset a password of system admin";
    public static final String RESET_PASSWORD_LDAP_USER_ERROR = "LDAP user Password Reset Denied. Please contact your LDAP Administrator";
    public static final String FILE_NOT_FOUND = "File %s not found";
    public static final String SCHEMA_FILE_NOT_FOUND = "Schema file not found for %s";
    public static final String LICENSE_DETAILS_NOT_FOUND = "License Details Retrieval Failed";
    public static final String CREDENTIAL_PROFILE_NOT_FOUND = "Credential profile not found";
    public static final String LOGIN_FAILED_USER_NOT_FOUND = "Login Failed: Invalid Credentials";
    public static final String LOGIN_FAILED_TOKEN_FETCH_FAILED = "Login failed, reason: Token not found in the store";
    public static final String LOGIN_FAILED_TOKEN_SAVE_FAILED = "Login failed, reason: Failed to save the token in the store";
    public static final String LOGIN_FAILED_TOKEN_CREATE_FAILED = "Login failed, reason: Failed to create the token in the store";
    public static final String LOGIN_FAILED_INVALID_REFRESH_TOKEN = "Login failed, reason: Invalid refresh token";

    public static final String LOGIN_FAILED_INVALID_SESSION_ID = "Login failed, reason: Invalid session id for sso user";
    public static final String LOGIN_FAILED_USER_NOT_CONFIGURED = "Login failed. Reason: User is authenticated but not configured properly to access the system";
    public static final String LOGIN_FAILED_SSO_NOT_CONFIGURED = "Login failed. Reason: Unable to send saml authentication request please validate sso configuration";

    public static final String LOGOUT_FAILED_FAILED_TO_DELETE_REFRESH_TOKEN = "Logout failed, reason: Failed to delete refresh token";

    public static final String LOGIN_FAILED_INVALID_SAML_RESPONSE = "Login failed. Reason: Invalid SAML response received from the IDP";

    public static final String LOGIN_FAILED_SSO_USER_NOT_CONFIGURED = "Login failed. Reason: SSO User is authenticated but not configured properly to access the system";
    public static final String LOGIN_FAILED_USER_NOT_ALLOWED = "Login failed. Reason: User is authenticated but does not belong to 'motadata users' group";
    public static final String LOGIN_FAILED_INVALID_LDAP_USER = "Login failed. Reason: failed to validate LDAP user (%s)";
    public static final String LOGIN_FAILED_USER_DISABLED = "Login Failed : Account Disabled. Please contact your Motadata Administrator";
    public static final String LOGIN_FAILED = "Login Failed. Possible reason: %s";
    public static final String LOGIN_FAILED_FQDN_IS_EMPTY_OR_NULL = "Login failed, reason: FQDN is empty or null";
    public static final String LOGIN_FAILED_MISSING_OR_INVALID_LDAP_CONNECTION = "Login Failed: Missing or Invalid LDAP Connection";
    public static final String LOGIN_FAILED_MOTADATA_USERS_GROUP_NOT_FOUND = "Login Failed: Ensure that  the %s group is properly configured on the LDAP server";
    public static final String LOGIN_FAILED_LDAP_SERVER_ERROR = "Login failed due to LDAP server error: %s";
    public static final String LOGIN_FAILED_INVALID_CREDENTIALS = "Login Failed: Invalid Credentials";
    public static final String LOGIN_FAILED_USER_NAME_MISSING = "Login failed, reason: Username is missing";
    public static final String LOGIN_FAILED_PASSWORD_MISSING = "Login failed, reason: Password is missing";
    public static final String LDAP_SERVER_TEST_FAILED_INVALID_EVENT_PARAMETERS = "LDAP Test Failed: Required Fields Missing";
    public static final String LDAP_SERVER_TEST_FAILED = "LDAP test failed. Possible reason: %s";
    public static final String MY_ACCOUNT_UPDATE_ACTION_NOT_ALLOWED = "Action not allowed, reason: User can only modify their own account";

    public static final String OAUTH_CONTEXT_NOT_FOUND_ERROR = "Context not found for Session %s";

    public static final String OAUTH_ACCESS_TOKEN_FETCH_ERROR = "Failed to get an access token. Possible Reason: %s";

    public static final String LDAP_SERVER_ALREADY_EXISTS = "LDAP server: %s already exists";

    public static final String INVALID_AUTHENTICATION_PROTOCOL = "Invalid Authentication Protocol";

    public static final String MISSING_AUTHENTICATION_PROTOCOL_AUTHENTICATION_CONTEXT = "Authentication protocol/context missing";
    public static final String MAIL_SERVER_TEST_FAILED = "Email server test failed. Possible reason: %s";
    public static final String MAIL_SERVICE_FAILED = "Email Service Unavailable: Email Notifications will not be sent";
    public static final String SMS_SERVICE_FAILED = "SMS Service Unavailable: SMS Notifications will not be sent";

    public static final String TEAMS_SERVICE_FAILED = "Teams Service Unavailable";
    public static final String OTP_INVALID = "Provided OTP is invalid";
    public static final String PROXY_SERVICE_FAILED = "Proxy Service Unavailable";
    public static final String EVENT_INVALID_CONTEXT = "Invalid event body";
    public static final String SMS_GATEWAY_TEST_FAILED = "SMS Gateway Test Failed. Possible reason: %s";
    public static final String PROXY_SERVER_TEST_FAILED = "Proxy Server Test Failed. Possible reason: %s";
    public static final String MAIL_SEVER_NOT_CONFIGURED = "The mail server settings are not configured. Please ensure they are set up correctly.";
    public static final String CREDENTIAL_PROFILE_TEST_FAILED_INVALID = "Credential profile test failed, reason: Invalid credentials,Please check and try again";
    public static final String CREDENTIAL_PROFILE_TEST_FAILED = "Credential profile test failed, reason: %s";
    public static final String OAUTH_CREDENTIAL_PROFILE_TEST_FAILED = "Credential profile test failed, reason: Context not found for session %s ";

    public static final String CREDENTIAL_PROFILE_TEST_FAILED_NOT_FOUND = String.format(CREDENTIAL_PROFILE_TEST_FAILED, CREDENTIAL_PROFILE_NOT_FOUND);

    public static final String INTEGRATION_PROFILE_TEST_FAILED = "Integration profile test failed, reason: %s";
    public static final String INTEGRATION_IS_NOT_CONFIGURED = "Invalid configurations";
    public static final String INTEGRATION_PROFILE_TEST_FAILED_NOT_FOUND = String.format(INTEGRATION_PROFILE_TEST_FAILED, "Integration profile not found");
    public static final String INTEGRATION_PROFILE_TEST_FAILED_INTEGRATION_NOT_CONFIGURED = String.format(INTEGRATION_PROFILE_TEST_FAILED, INTEGRATION_IS_NOT_CONFIGURED);

    public static final String INTEGRATION_ATTRIBUTE_SYNC_FAILED = "Failed to sync %s integration's attributes. reason: %s";

    public static final String INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_STATUS_CODE = "Invalid status code %s.";
    public static final String INTEGRATION_ATTRIBUTE_SYNC_FAILED_INVALID_TOKEN_GENERATION = "Error while generating new token.";
    public static final String INTEGRATION_ATTRIBUTE_SYNC_JOB_ALREADY_RUNNING = "Job is already running.";
    public static final String MICROSOFT_TEAMS_NOT_FOUNT = "Invalid account";

    public static final String PING_FAILED = "Host/IP Unreachable: %s";
    public static final String PORT_CONNECTION_FAILED = "Connection to port %s failed";
    public static final String LOG_FORWARDER_CONNECTION_FAILED = "Log Forwarder Connection Failed. Possible reason: %s";
    public static final String DNS_SERVER_TEST_FAILED = "DNS server test failed, reason: %s";
    public static final String METRIC_POLL_FAILED = "Metric polling failed. Possible reason: %s";
    public static final String LIVE_STREAMING_FAILED = "Live Streaming failed. Possible reason: %s";
    public static final String SNMP_OID_GROUP_TEST_FAILED = "SNMP OID Group test failed, Possible reason: %s";
    public static final String METRIC_PLUGIN_TEST_FAILED = "Metric Plugin test failed, Possible reason: %s";
    public static final String METRIC_PLUGIN_UPDATE_FAILED = "Metric Plugin update failed, Possible reason: %s";
    public static final String RUNBOOK_PLUGIN_FAILED = "Runbook Plugin failed, Possible reason: %s";
    public static final String METRIC_PLUGIN_TEST_FAILED_AGENT_NOT_AVAILABLE = "Metric Plugin test failed, reason: No agent is available";
    public static final String PARSING_SCRIPT_FAILED = "Failed to parse script";
    public static final String INVALID_JSON = "Invalid JSON format";
    public static final String INVALID_STATUS_CODE = "Invalid status code %s";
    public static final String TOPOLOGY_PLUGIN_TEST_FAILED = "Topology Plugin test failed, Possible reason: %s";
    public static final String DISCOVERY_PING_FAILED = "Ping failed";
    public static final String DISCOVERY_INTERNET_CONNECTION_FAILED = "Internet connection failed";
    public static final String DISCOVERY_PORT_NOT_REACHABLE = "Port is not reachable";
    public static final String CONNECTION_FAILED = "Connection failed";
    public static final String CREDENTIAL_ERROR = "Invalid credentials";
    public static final String URL_ERROR = "Invalid URL";
    public static final String DATABASE_ACCESS_ERROR = "Insufficient database permissions";
    public static final String INTERNAL_ERROR = "Internal error";
    public static final String TIMED_OUT = "Timed out";
    public static final String PROCESS_TIMED_OUT = "Timed out: Process was killed";
    public static final String STREAM_CLOSED = "Stream closed";
    public static final String INVALID_CREDENTIALS = "Invalid credentials";
    public static final String INVALID_URL = "Invalid URL";
    public static final String INSUFFICIENT_DATABASE_PERMISSIONS = "Insufficient database privileges";
    public static final String DISCOVERY_CSV_MAX_PROBE_LIMIT_EXCEEDED = String.format("Discovery can have a maximum of %s probes", Discovery.DISCOVERY_CSV_MAX_PROBE_LIMIT);
    public static final String DISCOVERY_PROGRESS_DB_RESULT_SAVE_FAILED = "Failed to save the discovery result in the database, Possible reason: %s";
    public static final String OBJECT_PROVISIONING_FAILED = "Object provisioning failed, Possible reason: %s";
    public static final String DISCOVERY_START_FAILED = "Failed to start the discovery, Possible reason: %s";
    public static final String DISCOVERY_RESULT_UPDATE_FAILED = "Failed to update discovery %s result";
    public static final String DISCOVERY_OBJECT_NOT_FOUND = "Monitor is already provisioned";
    public static final String OBJECT_NOT_FOUND = "Monitor not found";
    public static final String METRIC_UPDATE_FAILED = "Failed to update metric settings. Possible reason: %s";
    public static final String METRIC_SUSPEND_STATE_FAILED = "Failed to suspend metric %s of object %s";
    public static final String METRIC_ENABLE_STATE_FAILED = "Failed to enable metric %s of object %s";
    public static final String METRIC_DISABLE_STATE_FAILED = "Failed to disable metric %s of object %s";
    public static final String OBJECT_ENABLE_STATE_FAILED = "Failed to enable monitor %s";
    public static final String OBJECT_DISABLE_STATE_FAILED = "Failed to disable monitor %s";
    public static final String OBJECT_MAINTENANCE_STATE_FAILED = "Failed to on maintenance mode of monitor %s";
    public static final String OBJECT_PROVISION_FAILED = "Failed to provision monitor %s, Possible reason: %s";
    public static final String OBJECT_METRIC_UPDATE_FAILED = "Failed to update metric %s of monitor %s";
    public static final String METRIC_PROVISION_FAILED = "Failed to provision metric %s of monitor %s, Possible reason: %s";
    public static final String METRIC_PROVISION_VM_FAILED = "Failed to provision vm %s of monitor %s, Possible reason: %s";
    public static final String METRIC_INSTANCES_UNPROVISION_FAILED = "Failed to unprovision instances %s of monitor %s, Possible reason: %s";
    public static final String METRIC_PROVISION_ACCESS_POINT_FAILED = "Failed to provision access point %s of controller %s, Possible reason: %s";
    public static final String DISCOVERY_ABORT_FAILED_NOT_RUNNING = "Failed to abort discovery %s because it is not running";
    public static final String DISCOVERY_RUN_FAILED_ALREADY_RUNNING = "Discovery for %s is already running";
    public static final String REDISCOVERY_RUN_FAILED_ALREADY_RUNNING = "Rediscovery is already running";
    public static final String RUNBOOK_EXECUTION_FAILED = "Failed to execute runbook. Possible reason: %s";
    public static final String NO_MONITOR_AVAILABLE = "%s is not available";
    public static final String GROUP_DUPLICATE_CHILD_GROUP = "Group name must be unique within the same parent group";
    public static final String GROUP_MAXIMUM_HIERARCHICAL_DEPTH = "A group can have a maximum of three levels of hierarchy";
    public static final String UPGRADE_FAILED_OLD_ARTIFACTS = "The upgrade for %s %s failed as the current version exceeds the specified version for the upgrade";
    public static final String UPGRADE_FAILED_ARTIFACTS_NOT_FOUND = "%s upgrade failed, Reason: artifacts %s not found";
    public static final String UPGRADE_FAILED_INVALID_ARTIFACT_NAME = "%s upgrade failed, Reason: artifact name is invalid";
    public static final String UPGRADE_FAILED_VERSION_NOT_FOUND = "%s %s upgrade failed, Reason: version not found";
    public static final String AGENT_INVALID_CONFIG = "Invalid Agent Config File";
    public static final String AGENT_CONFIG_UPDATE_FAILED = "Failed to update the configuration for agent %s. Possible Reason: %s";
    public static final String LDAP_SYNC_FAILED = "LDAP server sync failed. Possible Reason: %s";
    public static final String LDAP_SYNC_FAILED_ALREADY_RUNNING = "LDAP server sync failed as it is already running";
    public static final String LDAP_SYNC_FAILED_NOT_CONFIGURED = "LDAP server sync failed as the server is not configured";
    public static final String LDAP_SYNC_MOTADATA_USERS_GROUP_NOT_FOUND = "LDAP Sync Failed: Ensure that  the %s group is properly configured on the LDAP server";
    public static final String LDAP_SYNC_MISSING_OR_INVALID_LDAP_CONNECTION = "LDAP Sync Failed: Missing or Invalid LDAP Connection";
    public static final String LDAP_SYNC_FQDN_IS_EMPTY_OR_NULL = "LDAP Sync Failed: FQDN Empty or Null";
    public static final String LDAP_SYNC_MOTADATA_USERS_GROUP_NO_USER_FOUND = "LDAP Sync Failed: No users found in the %s group";
    public static final String LDAP_AUTH_DISABLED = "LDAP authentication is disabled";
    public static final String LDAP_SECURE_CONNECTION_FAILED = "Failed to create a secure connection";

    public static final String ITEM_NOT_FOUND_IN_STORE = "Item %s not found";
    public static final String ITEM_NOT_FOUND = "Item not found";
    public static final String SNMP_DEVICE_CATALOG_ASSIGN_FAILED = "Failed to assign SNMP Device Catalog, Reason: %s";
    public static final String SNMP_OID_GROUP_UNIQUE_ERROR = "SNMP OID Group %s is not unique for type %s";
    public static final String NETWORK_OBJECT_NOT_FOUND = "No 'Network' monitor found";
    public static final String VIRTUAL_MACHINE_OBJECT_NOT_FOUND = String.format("No %s/%s/%s type monitor found", NMSConstants.Type.HYPER_V.getName()
            , NMSConstants.Type.VMWARE_ESXI.getName(), NMSConstants.Type.CITRIX_XEN.getName());
    public static final String SERVER_OBJECT_NOT_FOUND = "No 'Server' monitor found";
    public static final String WINDOWS_SERVER_OBJECT_NOT_FOUND = "No 'Windows Server' monitor found";
    public static final String CLOUD_OBJECT_NOT_FOUND = "No 'AWS/Azure/Office365' monitor found";
    public static final String CONTAINER_OBJECT_NOT_FOUND = "No 'Docker' monitor found";
    public static final String PROVISION_OBJECT_FAILED_PARENT_OBJECT_NOT_FOUND = "Failed to provision %s, reason: Parent monitor not found";
    public static final String PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT = "Provision Failure: %s is already provisioned";
    public static final String PROVISION_OBJECT_FAILED_CREDENTIAL_PROFILE_NOT_FOUND = "Failed to provision %s: Credential profile not found";
    public static final String PROVISION_OBJECT_FAILED_INTERNAL_ERROR = "Failed to provision %s. Possible Reason: %s";
    public static final String PROVISION_OBJECT_FAILED_NO_SNMP_DEVICE_CATALOG_FOUND = "Failed to provision %s, reason: No SNMP Device Catalog found" + GlobalConstants.NEW_LINE
            + "Please create SNMP Device catalog for the System OID %s";
    public static final String OBJECT_ARCHIVED_FAILED = "Failed to archive object";
    public static final String SNMP_OID_GROUP_INVALID = "Invalid SNMP OID group.";
    public static final String REDISCOVER_JOB_OBJECT_ERROR = "Failed to rediscover %s, reason: Either Monitor is disabled or down";
    public static final String REDISCOVER_FAILED = "Failed to rediscover, Possible reason: %s";
    public static final String PLUGIN_ENGINE_FAILED = "Failed to discover %s plugin engine, reason: %s";
    public static final String DISCOVERY_FAILED = "Failed to discover. Possible reason: %s";
    public static final String DISCOVERY_APP_FAILED = "Failed to discover %s on %s, Possible reason: %s";
    public static final String EVENT_TRACKER_PROCESS_KILLED = "The process %s was terminated at %s due to a timeout";
    public static final String EVENT_TRACKER_EVENT_TIMED_OUT = "Event timed out after %s seconds. Retrying with new collector";
    public static final String EVENT_TRACKER_EVENT_ACKNOWLEDGE_TIMED_OUT = "Failed to acknowledge event after %s milli seconds. Retrying with new collector";
    public static final String EVENT_ROUTER_ATTEMPT_EXCEEDED = "Attempt limit exceeded";
    public static final String EVENT_ROUTER_COLLECTOR_NOT_AVAILABLE = "no valid collector available";
    public static final String METRIC_POLLER_NO_OBJECT_FOUND = "Metric polling aborted, No valid %s (s) found";
    public static final String NO_OBJECTS_FOUND = "No objects found";
    public static final String NO_APPLICATIONS_FOUND = "No Applications found";
    public static final String OBJECT_ERROR = "Either Monitor is disabled or down";
    public static final String OBJECT_STATE_IS_SAME_ERROR = "Monitor is in %s state";
    public static final String ENTITY_ASSIGN_FAILED = "Failed to assign source to %s. Possible reason: %s";
    public static final String ENTITY_UNASSIGN_FAILED = "Failed to unassign source from %s. Possible reason: %s";
    public static final String LOG_FILE_SIZE_LIMIT_EXCEEDED = "Failed to read the log file: File size limit has been exceeded";
    public static final String LOG_FAILED_TO_PARSE = "Failed to parse logs: no valid parser/pattern found";
    public static final String MOTADATA_ENGINE_RESTART_FAILED = "Failed to restart motadata engine %s";
    public static final String TOPOLOGY_RUN_ERROR = "Failed to run topology, Possible reason: %s";

    //dependency/topology const
    public static final String TOPOLOGY_CREATE_ERROR = "Failed to create topology %s for monitor %s, Possible reason: %s";
    public static final String TOPOLOGY_ABORT_FAILED_NOT_RUNNING = "Failed to abort topology, reason: topology is not running";
    public static final String TOPOLOGY_ENTRY_POINT_MISSING = "Seed IP is missing";
    public static final String CORRELATION_FAILED = "Failed to run correlation for monitor %s, Possible reason: %s";
    public static final String DEPENDENCY_MAPPER_FAILED = "Failed to build dependency, Possible reason: %s";
    public static final String DEPENDENCY_MAPPER_FAILED_DUPLICATE_ERROR = "Failed to build dependency: Duplicate connection error";
    public static final String DEPENDENCY_MAPPER_FAILED_CYCLIC_ERROR = "Failed to build dependency: Cyclic connection error";
    public static final String LICENSE_LIMIT_EXCEEDED = "License limit exceeded";

    // license const
    public static final String VISUALIZATION_QUERY_FAILED = "Failed to execute query for widget %s, Possible reason: %s";

    //visualization constant
    public static final String NO_ENTITY_QUALIFIED = "No entity qualified";
    public static final String DATA_POINT_LIMIT_EXCEEDED = "Data points selection limit exceeded";
    public static final String INVALID_DATA_SOURCE = "Invalid data source";
    public static final String INVALID_NO_DATA_FOUND = "No data found";
    public static final String INVALID_TIMELINE = "Invalid time line";
    public static final String INVALID_DATA_POINTS = "Invalid data points";
    public static final String VISUALIZATION_QUERY_ABORTED = "Query aborted. Possible reason: %s";
    public static final String REPORT_GENERATION_FAILED = "Failed to generate report %s. Possible reason: %s";
    public static final String FAILED_TO_DELETE_REPORT = "Failed to delete report. Possible reason: %s";
    public static final String INSUFFICIENT_DISK_SPACE_ERROR = "Insufficient disk space";

    public static final String INVALID_DATA_RECEIVED = "Invalid data received";
    public static final String LOG_PARSER_INVALID_PLUGIN_METHOD = "Failed to find parse method in the script";
    public static final String LOG_PARSER_INVALID_PLUGIN_CLASS = "Failed to compile java class";

    //log constant
    public static final String LOG_PARSER_TYPE_NOT_AVAILABLE = "Log parser type not selected";
    public static final String LOG_PARSING_FAILED = "Failed to parse event";
    public static final String LOG_TAIL_SESSION_LIMIT_EXCEEDED = "Log tail sessions limit exceeded";
    public static final String TRAP_TAIL_SESSION_LIMIT_EXCEEDED = "Trap tail sessions limit exceeded";
    public static final String LOG_MESSAGE_TOO_BIG_ERROR = "%s trimmed to %d bytes, reason: log size is too big";
    public static final String TRAP_MESSAGE_TOO_BIG_ERROR = "%s trimmed to %d bytes, reason: trap size is too big";
    public static final String REPORT_EXPORT_FAILED_ALREADY_RUNNING = "Failed to export report, reason: export job for report %s is already running";
    public static final String POLLING_FAILED = "Failed to poll %s, reason: %s";
    public static final String FAILED_TO_VALIDATE_LICENSE = "failed to validate license, reason: %s";

    //Configuration specific constants

    public static final String CONFIG_REQUEST_FAILED = "Failed to execute config operation, reason: %s";
    public static final String CONFIG_CREATE_FAILED = "Failed to create configuration %s. Possible Reason: %s";
    public static final String CONFIG_SENDING_REQUEST_FAILED = "Failed to send config operation : %s , for the object : %s, reason : %s ";
    public static final String CONFIG_DISCOVERY_FAILED = "Failed to discover %s : %s , Possible reason : %s";
    public static final String CONFIG_OPERATION_FAILED = "Failed to %s %s : %s , Possible reason : %s";
    public static final String CONFIG_UPDATE_FAILED = "Failed to update configuration by the request : %s , for the object : %s, reason : %s";
    public static final String STORAGE_PROFILE_TEST_FAILED = "Storage profile test failed, reason : %s";
    public static final String CONFIG_BACKUP_FILE_NOT_FOUND = "Config backup file not found : %s, for the object : %s";
    public static final String CONFIG_BASELINE_ASSIGN_FAILED = "Failed to assign baseline version, reason : %s";
    public static final String CONFIG_OBJECT_NOT_FOUND = "Config not found";
    public static final String CONFIG_BASELINE_REMOVE_FAILED = "Failed to unassign baseline version, reason : %s";
    public static final String CONFIG_CONFIG_BACKUP_RESULT_DELETE_FAILED = "Failed to delete configuration config backup result, object : %s, file type : %s, version : %s, reason : %s";
    public static final String CONFIG_BACKUP_RESULT_ERROR = "Enable to get file content, reason : %s";
    public static final String CONFIG_CREDENTIAL_NOT_ASSIGNED = "Credential profile not assigned";
    public static final String CONFIG_INIT_SESSION_FAILED = "Failed to start session";
    public static final String CONFIG_ENABLE_COMMAND_FAILED = "Enable command not valid";
    public static final String CONFIG_ENABLE_USER_FAILED = "Enable user not valid";
    public static final String CONFIG_ENABLE_PASSWORD_FAILED = "Enable password not valid";
    public static final String CONFIG_ENABLE_MODE_FAILED = "Failed to start Enable mode";
    public static final String CONFIG_ENABLE_PASSWORD_NOT_PROVIDED = "Enable password not provided";
    public static final String CONFIG_ENABLE_TEMPLATE_NOT_PROVIDED = "Config Template not provided";
    public static final String CONFIG_ENABLE_OPERATION_NOT_PROVIDED = "Operation not added in Config Template";
    public static final String CONFIG_MANAGE_STATUS_OFF = "Manage config flag is OFF";
    public static final String CONFIG_DEVICE_NOT_AVAILABLE = "No config device available";
    public static final String CONFIG_DEVICE_OBJECT_ID_NOT_AVAILABLE = "Object id not available";
    public static final String CONFIG_RESULT_ADD_FAILED = "Failed to add configuration result for file type : %s , for the object : %s, reason : %s ";
    public static final String STORAGE_SRC_FILE_NOT_FOUND = "Source File/Dir not found";
    public static final String STORAGE_FILE_UPLOAD_PARAM_MISSING = "Missing required parameters to upload file";
    public static final String CREDENTIAL_PROFILE_NOT_PROVIDED = "Credential profile not provided";
    public static final String CONFIG_CONTENT_BLANK = "Content is blank";
    public static final String CONFIG_UPGRADE_FAILED = "Failed to upgrade configurations, reason: %s";

    public static final String RESTORE_FAILED = "Failed to restore the database, reason : %s";

    public static final String DATABASE_BACKUP_FAILED_NOTIFICATION_SUBJECT = "Motadata Notification \n Backup Job : %s Failed";
    public static final String UPGRADE_FAILED = "%s %s upgrade failed";
    public static final String INTEGRATION_FAILED_NOTIFICATION_SUBJECT = "Motadata %s Integration – Failed";
    public static final String DISK_UTILIZATION_EXCEED = "Motadata %s server %s is experiencing high disk utilization in %s with value %s above the %s threshold";

    //SSH Session Constants
    public static final String SSH_SESSION_LIMIT_EXCEED = "Session limit exceeded";
    public static final String SSH_SESSION_INVALID_INPUT = "Invalid hostname or credentials";
    public static final String SSH_SESSION_EXECUTION_FAILED = "Failed to execute command, reason: SSH session expired";
    public static final String SSH_SESSION_EXPIRED = "SSH session expired";

    public static final String FILE_ALREADY_EXIST = "File already exist";
    public static final String TRANSITION_STATUS = "no transition is matched with provided status";

    // netroute
    public static final String NETROUTE_FAILED_TO_RESOLVE_IP_ADDRESS = "Unable to resolve destination IP address";
    public static final String NETROUTE_ALREADY_EXIST = "NetRoute already exists with the for destination : %s";

    //policy error

    private ErrorMessageConstants()
    {
    }


    // License Error

    public static String getLDAPErrorMessage(String errorCode)
    {
        return switch (errorCode)
        {
            case "525" -> "User not found";
            case "52e" -> "Invalid credentials, verify Username, Password and FQDN";
            case "530" -> "Access denied at this time";
            case "531" -> "Access denied at the workstation";
            case "532" -> "Password expired";
            case "533" -> "Account disabled";
            case "534" -> "Access denied at this machine";
            case "701" -> "Account expired";
            case "773" -> "Password reset required";
            case "775" -> "User account locked";
            default -> "Couldn't validate user";
        };
    }

    public static String extract(JsonArray errors, String field)
    {
        var result = new StringBuilder();

        if (errors != null && !errors.isEmpty())
        {
            for (var index = 0; index < errors.size(); index++)
            {
                var error = errors.getJsonObject(index);

                if (error != null && error.getString(field) != null)
                {
                    result.append(error.getString(field)).append(GlobalConstants.NEW_LINE);
                }
            }
        }

        return result.toString();
    }

    public static String extractError(JsonArray errors)
    {
        return extract(errors, GlobalConstants.ERROR);
    }

    public static void extractError(JsonObject context)
    {
        if (context.getJsonArray(ERRORS) != null && !context.getJsonArray(ERRORS).isEmpty())
        {
            context.put(ERROR, context.getJsonArray(ERRORS).encodePrettily());
        }
    }

    public static void extractErrorCode(JsonObject output, String prefix, String errorCode, String errorMessage, String eventType)
    {
        var errors = output.getJsonArray(ERRORS);

        if (errors != null && !errors.isEmpty() && errors.getJsonObject(0).getString(ERROR_CODE) != null)
        {
            var message = errorMessage != null ? errorMessage : extractMessage(errors);

            output.put(ERROR_CODE, errorCode != null ? errorCode : errors.getJsonObject(0).getString(ERROR_CODE)).put(MESSAGE, prefix != null ? eventType != null ? String.format(prefix, eventType, message) : String.format(prefix, message) : message);
        }
        else
        {
            output.put(ERROR_CODE, errorCode != null ? errorCode : ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, prefix != null ? eventType != null ? String.format(prefix, eventType, UNKNOWN) : String.format(prefix, UNKNOWN) : UNKNOWN);
        }
    }

    /**
     * This method is used to extract message based on error codes
     */
    public static String extractMessage(JsonArray errors)
    {
        var message = EMPTY_VALUE;

        if (errors != null && !errors.isEmpty() && errors.getJsonObject(0).getString(ERROR_CODE) != null)
        {
            var errorCode = errors.getJsonObject(0).getString(ERROR_CODE);

            message = switch (errorCode)
            {
                case ErrorCodes.ERROR_CODE_CONNECTION_FAILED, ErrorCodes.ERROR_CODE_NO_RESPONSE -> CONNECTION_FAILED;

                case ErrorCodes.ERROR_CODE_INVALID_CREDENTIALS, ErrorCodes.ERROR_CODE_API_INVALID_ACCESS_KEY,
                     ErrorCodes.ERROR_CODE_API_INVALID_CLIENT_ID, ErrorCodes.ERROR_CODE_API_INVALID_SECRET_KEY,
                     ErrorCodes.ERROR_CODE_API_INVALID_SUBSCRIPTION_ID, ErrorCodes.ERROR_CODE_API_INVALID_TENANT_ID,
                     ErrorCodes.ERROR_CODE_UNAUTHORIZED_URL_ACCESS -> CREDENTIAL_ERROR;

                case ErrorCodes.ERROR_CODE_UNAUTHORIZED_DATABASE_ACCESS, ErrorCodes.ERROR_CODE_DATABASE_LOGIN ->
                        DATABASE_ACCESS_ERROR;

                case ErrorCodes.ERROR_CODE_BAD_RESPONSE -> URL_ERROR;

                case ErrorCodes.ERROR_CODE_CONFIG_INIT_SESSION_FAILED -> CONFIG_INIT_SESSION_FAILED;

                case ErrorCodes.ERROR_CODE_CONFIG_ENABLE_COMMAND_FAILED -> CONFIG_ENABLE_COMMAND_FAILED;

                case ErrorCodes.ERROR_CODE_CONFIG_ENABLE_USER_FAILED -> CONFIG_ENABLE_USER_FAILED;

                case ErrorCodes.ERROR_CODE_CONFIG_ENABLE_PASSWORD_FAILED -> CONFIG_ENABLE_PASSWORD_FAILED;

                case ErrorCodes.ERROR_CODE_CONFIG_ENABLE_MODE_FAILED -> CONFIG_ENABLE_MODE_FAILED;

                case ErrorCodes.ERROR_CODE_CONFIG_ENABLE_PASSWORD_NOT_PROVIDED -> CONFIG_ENABLE_PASSWORD_NOT_PROVIDED;

                case ErrorCodes.ERROR_CODE_CONFIG_TEMPLATE_NOT_PROVIDED -> CONFIG_ENABLE_TEMPLATE_NOT_PROVIDED;

                case ErrorCodes.ERROR_CODE_CONFIG_OPERATION_NOT_PROVIDED -> CONFIG_ENABLE_OPERATION_NOT_PROVIDED;

                default -> errors.getJsonObject(0).getString(MESSAGE);
            };
        }

        return message;
    }
}
