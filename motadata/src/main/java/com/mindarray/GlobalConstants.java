/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author      Notes
 *   5-Mar-2025     Bharat      MOTADATA-4740: Two factor authentication 2FA
 *   18-Mar-2025    Smit        MOTADATA-5431: Module Level Logging
 *   25-Mar-2025    Smit        MOTADATA-5435: Flow back-pressure mechanism.
 *   20-Feb-2025    Pruthviraj  MOTADATA-4904: NetRoute related constant added
 *   18-Apr-2025    Sankalp     MOTADATA-5859 : changed SYSTEM_USER constant to DEFAULT_USER
 *   8-Apr-2025     Vismit      MOTADATA-5613: Added constant total
 *   20-Jun-2025    Darshan     MOTADATA-6587 : certificate based credential profile sensitive field remove
 *   24-Jun-2025    Pruthvi     MOTADATA-6580: convert string arr to set sensitive field
 *   20-May-2025    Aagam       MOTADATA-5847 : Added windows build support
 *   9-May-2025     Harsh       MOTADATA-6007: Windows build support.
 * */
package com.mindarray;

import com.mindarray.api.*;
import com.mindarray.api.MetricPlugin;
import com.mindarray.config.ConfigConstants;

import java.nio.file.FileSystems;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.api.User.USER_TOTP_KEY;
import static com.mindarray.nms.NMSConstants.*;

public final class GlobalConstants
{

    // environment const

    public static final String CURRENT_DIR = System.getProperty("user.dir");

    public static final String PATH_SEPARATOR = FileSystems.getDefault().getSeparator();

    public static final String NEW_LINE = System.lineSeparator();

    public static final String OS_NAME = System.getProperty("os.name").toLowerCase();

    public static final String OS_VERSION = System.getProperty("os.version");

    public static final boolean OS_WINDOWS = OS_NAME.contains("win");

    public static final boolean OS_LINUX = OS_NAME.contains("nix") || OS_NAME.contains("nux") || OS_NAME.contains("aix");

    public static final boolean OS_AIX = OS_NAME.contains("aix");

    public static final boolean OS_SOLARIS = OS_NAME.contains("sunos");

    // config const

    public static final String CONFIG_DIR = "config";
    public static final String SHADOW_COUNTERS_FILE = "shadow-counters.json";
    public static final String LOGS_DIR = "logs";
    public static final String FLOW_CACHE_DIR = "flow-cache";
    public static final String REGISTRATION_FILE = "registration.json";
    public static final String MOTADATA_CONFIG_FILE = "motadata.json";
    public static final String DATASTORE_FILE = "motadata-datastore.json";
    public static final String DB_DIR = "db-files";
    public static final String GEO_LOCATION = "geo-location";
    public static final String SNMP_TEMPLATE_DIR = "snmp-templates";
    public static final String CUSTOM_PLUGIN_SCRIPT_DIR = "customscripts";
    public static final String RUNBOOK_DIR = "runbook";
    public static final String INTEGRATION_DIR = "integration";
    public static final String PYTHON_PLUGIN_ENGINE_DIR = "pluginengine";
    public static final String CACHE_DIR = "cache-files";
    public static final String PLUGIN_CONTEXT_DIR = "plugin-contexts";
    public static final String PLUGIN_SCRIPT_DIR = "plugin-scripts";

    public static final String PLUGIN_PARSER_DIR = "plugins";

    public static final String PARSE_METHOD = "parse";

    public static final String TOPOLOGY_DIR = "topology";

    public static final String PYTHON_PLUGIN_ENGINE_BIN = "bootstrap.pyc";

    public static final String PYTHON_BIN = "python3";

    public static final String NODE_BIN = "node";

    public static final String REPORT_ENGINE_BIN = "./report-engine";

    public static final String REPORTS = "reports";

    public static final String GO_BIN = "go";

    public static final String GO_RUN_BIN = "run";

    public static final String GO_PLUGIN_ENGINE_DIR = "goengine";

    public static final String DOTNET_ENGINE_DIR = "dotnetengine";

    public static final String GO_PLUGIN_ENGINE_BIN = "./gopluginengine";

    public static final String DOTNET_PLUGIN_ENGINE_BIN = "dotnetpluginengine.exe";

    public static final String DOTNET_PING_UTILITY_BIN = "motadata-ping-util.exe";

    //file uploads directory
    public static final String UPLOADS = "uploads";

    public static final String FILE_NAME = "file.name";

    public static final String KEYS = "keys";

    // file downloads directory
    public static final String DOWNLOADS = "downloads";

    // system settings const

    public static final String INSTALLATION_MODE = "installation.mode";
    public static final String INSTALLATION_TYPE = "installation.type";
    public static final String INSTALLATION_DATE = "installation.date";
    public static final String SYSTEM_START_TIME = "system.start.time";

    public static final String SYSTEM_LOG_RETENTION_DAYS = "system.log.retention.days";

    public static final String SYSTEM_BOOTSTRAP_TYPE = "system.bootstrap.type";
    public static final String SYSTEM_SCHEMA_VERSIONS = "system.schema.versions";
    public static final String MANAGER_UUID = "manager.id";

    //logger related constants

    public static final String LOGGER_DATE_FORMAT = "dd-MMMM-yyyy HH";

    public static final String LOG_RETENTION_DATE_FORMAT = "dd-MMMM-yyyy";

    public static final String LOGGER_TIME_FORMAT = "hh:mm:ss.SSS a";

    public static final String SYSTEM_LOG_LEVEL = "system.log.level";

    public static final String SYSTEM_LOG_MODULES = "system.log.modules";

    public static final int LOG_LEVEL_TRACE = 0;

    public static final int LOG_LEVEL_DEBUG = 1;

    public static final int LOG_LEVEL_INFO = 2;

    public static final String LOG_LEVEL_RESET_TIMER_SECONDS = "log.level.reset.timer.seconds";

    public static final String MOTADATA_SYSTEM = "system";

    public static final String MOTADATA_OBSERVER = "observer";

    public static final String MOTADATA_PLUGIN_ENGINE = "plugin-engine";

    public static final String MOTADATA_LOG = "log";

    public static final String MOTADATA_FLOW = "flow";

    public static final String MOTADATA_NMS = "nms";

    public static final String MOTADATA_STREAMING = "streaming";

    public static final String MOTADATA_RUNBOOK = "runbook";

    public static final String MOTADATA_DB = "db";

    public static final String MOTADATA_DATASTORE = "datastore";

    public static final String MOTADATA_CACHE = "cache";

    public static final String MOTADATA_STORE = "store";

    public static final String MOTADATA_POLICY = "policy";

    public static final String MOTADATA_UTIL = "util";

    public static final String MOTADATA_AIOPS = "aiops";

    public static final String MOTADATA_PATCH = "patch";

    public static final String MOTADATA_NETROUTE = "netroute";

    public static final String MOTADATA_TRACE = "apm";

    public static final String MOTADATA_NOTIFICATION = "notification";

    public static final String MOTADATA_JOB = "job";

    public static final String MOTADATA_CONFIGURATION = "configuration"; //config log dir

    public static final String MOTADATA_EVENT_BUS = "event-bus";

    public static final String MOTADATA_API = "api";

    public static final String MOTADATA_AGENT = "agent";

    public static final String MOTADATA_APP_MANAGER = "motadata-app-manager";

    public static final String MOTADATA_AUDIT = "audit";

    public static final String MOTADATA_HA = "ha";

    public static final String MOTADATA_VISUALIZATION = "visualization";

    public static final String MOTADATA_REPORTING = "reporting";

    public static final String MOTADATA_COMPLIANCE = "compliance";

    public static final String PLUGIN_ID = "plugin.id";
    public static final String EVENT_TIMEZONE = "event.timezone";

    public static final String LATITUDE = "latitude";

    public static final String LONGITUDE = "longitude";
    public static final String ERROR_CODE = "error.code";
    public static final String ERROR = "error";


    // response (api/event/task/availability) status constants
    public static final String ERRORS = "errors";
    public static final String STATUS = "status";
    public static final String STATUS_SUCCEED = "succeed";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_WARNING = "warning";
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_FAIL = "fail";
    public static final String STATUS_TIME_OUT = "timeout";
    public static final String STATUS_ABORT = "abort";
    public static final String STATUS_UP = "Up";
    public static final String STATUS_DOWN = "Down";
    public static final String STATUS_UNREACHABLE = "Unreachable";
    public static final String STATUS_SUSPEND = "Suspend";
    public static final String STATUS_UNKNOWN = "Unknown";
    public static final String STATUS_MAINTENANCE = "Maintenance";
    public static final String STATUS_NONE = "None";
    public static final String STATUS_DISABLE = "Disable";
    public static final String MESSAGE = "message";
    public static final String PROGRESS = "progress";
    public static final String RESULT = "result";
    public static final String REASON = "reason";
    public static final String LICENSE = "license";
    public static final String DURATION = "duration";
    public static final int NOT_AVAILABLE = -1;
    public static final String TARGET = "target";

    // module common const
    public static final String PORT = "port";
    public static final String HOST = "host";
    public static final String DATABASE = "database";
    public static final String ID = "id";
    public static final Long DEFAULT_ID = 10000000000001L;
    public static final String TIMEOUT = "timeout";
    public static final String REQUEST_TIMEOUT = "request.timeout";
    public static final String USERNAME = "username";
    public static final String PASSWORD = "password";
    public static final String OTP = "otp";
    public static final String YES = "yes";
    public static final String NO = "no";
    public static final String EMPTY_VALUE = "";
    public static final Long DUMMY_ID = 0L;
    public static final int DUMMY_NUMERIC_VALUE = 0;
    public static final float DUMMY_FLOAT_VALUE = 0.0f;
    public static final String COLUMN_SEPARATOR = "§";
    public static final String INSTANCE_SEPARATOR = "~";
    public static final String HASH_SEPARATOR = "#";
    public static final String COMMA_SEPARATOR = ",";
    public static final String DOT_SEPARATOR = ".";
    public static final String DOT_SEPARATOR_ESCAPE = "\\.";
    public static final String DASH_SEPARATOR = "-";
    public static final String ARROW_SEPARATOR = "->";
    public static final String CARET_SEPARATOR = "^";
    public static final String COLON_SEPARATOR = ":";
    public static final String SEPARATOR = "``||``";
    public static final String SEPARATOR_WITH_ESCAPE = "``\\|\\|``";
    public static final String CARET_SEPARATOR_WITH_ESCAPE = "\\^";
    public static final String VALUE_SEPARATOR = "_|@#|_";
    public static final String VALUE_SEPARATOR_WITH_ESCAPE = "_\\|@#\\|_";
    public static final String KEY_SEPARATOR = "¡°";
    public static final String GROUP_SEPARATOR = "###";
    public static final String SEVERITY = "severity";
    public static final String SEVERITIES = "severities";
    public static final String TAGS = "tags";
    public static final String INSTANCE_TAGS = "instance.tags";
    public static final String POLICIES = "policies";
    public static final String UNKNOWN = "Unknown";
    public static final String DEFAULT_USER = "admin";
    public static final String SYSTEM_USER = "system";
    public static final String SYSTEM_REMOTE_ADDRESS = "127.0.0.1";
    public static final String REMOTE_ADDRESS = "remote.address";
    public static final String TIME_STAMP = "timestamp";
    public static final String TIME = "time";
    public static final String LIMIT = "limit";
    public static final String INFO = "info";
    public static final String ENV_TEST = "test";
    public static final String ENV_DEV = "dev";
    public static final String ENTITY_TYPE = "entity.type";
    public static final String ENTITY_TYPE_OBJECT = "object";
    public static final String ENTITIES = "entities";
    public static final String METRIC = "metric";
    public static final String INSTANCE = "instance";
    public static final String INSTANCE_IP = "instance.ip";
    public static final String REMOTE_EVENT_PROCESSOR_CACHE_PATH = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "remote-event-processor-cache";
    public static final byte COMPRESSION_TYPE_ZERO = 0;
    public static final byte COMPRESSION_TYPE_ONE = 1;
    public static final String VALUE = "value";
    public static final String FILTERS = "filters";
    public static final String DATA_FILTER = "data.filter";
    public static final String FILTER = "filter";
    public static final String OPERATOR = "operator";
    public static final String OPERAND = "operand";
    public static final String LENGTH = "length";
    public static final String CONDITIONS = "conditions";
    public static final String CONDITION_GROUPS = "groups";
    public static final String KEY = "key";
    public static final String URI = "uri";

    public static final String REQUEST_ID = "request.id";

    public static final int SSH_PORT = 22;
    public static final int TELNET_PORT = 23;

    public static final String VERSION_FILE = "VERSION";

    public static final String VERSION = "version";
    public static final String STORAGE_PATH = "path";
    public static final String SRC_FILE_PATH = "src.file.path";
    public static final String DEST_FILE_NAME = "dest.file.name";
    public static final String DELETE_SRC_FILE = "delete.src.file";
    public static final String SAMPLE_FILE = "storage-test.txt";

    public static final Map<Integer, String> LOG_LEVELS = Map.of(0, "trace", 1, "debug", 2, "info");
    public static final int PORT_TIMEOUT_SECONDS = 3;
    public static final String EVENT_PUBLISHER_HOST = "event.publisher.host";
    public static final String EVENT_SUBSCRIBER_HOST = "event.subscriber.host";
    public static final String EVENT_PUBLISHER_OBSERVER_HOST = "event.publisher.observer.host";
    public static final String EVENT_SUBSCRIBER_OBSERVER_HOST = "event.subscriber.observer.host";
    public static final String EVENT_SUBSCRIBER_SECONDARY_HOST = "event.subscriber.secondary.host";
    public static final String EVENT_PUBLISHER_PORT = "event.publisher.port";
    public static final String EVENT_SUBSCRIBER_PORT = "event.subscriber.port";
    public static final String HTTP_SERVER_PORT = "http.server.port";

    public static final String IP_ADDRESS = "ip.address";
    public static final String HTTP_SERVER_HOST = "http.server.host";
    public static final String DISABLED = "disabled"; // only stored in cache
    public static final String ACKNOWLEDGED = "acknowledge";
    public static final String RW_PERMISSION = "rw-rw-rw-";
    public static final String ALL_PERMISSION = "rwxrwxrwx";
    public static final String STATUS_SUCCEEDED = "succeeded";
    public static final String FLOW_CACHE_DIRECTORY_PATH = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + FLOW_CACHE_DIR;
    private static final Set<String> SENSITIVE_FIELDS = Set.of("$$$password$$$", "$$$snmp.community$$$",
            "$$$snmp.authentication.password$$$", "$$$snmp.private.password$$$", "$$$user.password$$$", "$$$secret.key$$$"
            , "$$$access.id$$$", "$$$client.id$$$", "$$$ssh.key$$$", "$$$passphrase$$$", "$$$key$$$",
            MetricPlugin.METRIC_PLUGIN_VARIABLES, "access_token", "refresh_token", "client.secret", "client.id",
            PASSWORD, "Password", "snmp.community", "snmp.authentication.password", "snmp.write.community",
            "snmp.private.password", MailServerConfiguration.MAIL_SERVER_PASSWORD, ProxyServer.PROXY_SERVER_PASSWORD, USER_TOTP_KEY,
            LDAPServer.LDAP_SERVER_PASSWORD, AIOpsObject.OBJECT_CONTEXT, Discovery.DISCOVERY_CONTEXT, CredentialProfile.CREDENTIAL_PROFILE_CONTEXT, User.USER_PASSWORD,
            CLOUD_SECRET_KEY, CLOUD_ACCESS_ID, CLOUD_CLIENT_ID, CLOUD_TENANT_ID,
            "ssh.key", "passphrase", "SNMP Community", "Authentication Password", "Private Password", "user.temporary.password",
            "enable.password", StorageProfile.STORAGE_PROFILE_CONTEXT, ConfigConstants.ConfigBackupType.RUNNING.getName() + ConfigConstants.BACKUP_FILE_CONTENT, ConfigConstants.ConfigBackupType.STARTUP.getName() + ConfigConstants.BACKUP_FILE_CONTENT, "server.password", PersonalAccessToken.PERSONAL_ACCESS_TOKEN, "key", Integration.INTEGRATION_CONTEXT, Integration.INTEGRATION_ACCESS_TOKEN
            , CLIENT_CERTIFICATE, CLIENT_KEY, CERTIFICATE_AUTHORITY);

    public static Set<String> getSensitiveFields()
    {
        return SENSITIVE_FIELDS;
    }

    //system bootstrapping type // use to scale motadata horizontally

    /**
     * This BootstrapType must be sorted wrt priority of stopping infra, inorder to stop the infra properly
     **/
    public enum BootstrapType
    {
        DATASTORE, //standalone,primary,secondary
        AGENT,
        CLOUD_DEFAULT,
        CLOUD_AGENT,
        APP,  // standalone,primary,secondary
        MANAGER,
        COLLECTOR, //log+flow+metric poller
        OBSERVER,
        CLOUD_COLLECTOR,
        EVENT_COLLECTOR, //log
        EVENT_PROCESSOR,
        FLOW_COLLECTOR,
        REMOTE_EVENT_FORWARDER
    }

    public enum InstallationMode
    {
        STANDALONE,
        PRIMARY,
        SECONDARY,
        REPLICA,
        FAILOVER
    }

    public enum HAMode
    {
        ACTIVE,
        PASSIVE
    }

    public enum StatusType
    {
        UP((byte) 1),

        DOWN((byte) 2),

        SUSPEND((byte) 3),

        UNKNOWN((byte) 4),

        MAINTENANCE((byte) 5),

        DISABLE((byte) 6),

        UNREACHABLE((byte) 7);

        private static final Map<Byte, StatusType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(StatusType::getType, e -> e)));
        private final byte type;

        StatusType(byte type)
        {
            this.type = type;
        }

        public static StatusType valueOfName(byte type)
        {
            return VALUES.get(type);
        }

        public byte getType()
        {
            return type;
        }
    }


    public enum Severity
    {
        UNKNOWN,

        MAINTENANCE,

        CLEAR,

        WARNING,

        MAJOR,

        CRITICAL,

        UNREACHABLE,

        DOWN
    }

    public enum Status
    {
        UNKNOWN,

        MAINTENANCE,

        UP,

        DISABLE,

        SUSPEND,

        UNREACHABLE,

        DOWN
    }

}
