@echo off

echo %date%:%time%:executing manager upgrade script > logs/MANAGER-UPGRADER-SCRIPT.log

copy /Y upgrade.me upgrade.me.org
cmd /c "type upgrade.me.org&&echo :fail"  > upgrade.me.txt
copy /Y upgrade.me.txt upgrade.me

echo %date%:%time%:stopping motadata service >> logs/MANAGER-UPGRADER-SCRIPT.log
net stop "Motadata Agent"
taskkill /f /im motadata-manager.exe
ping localhost -n 5 > nul

echo %date%:%time%:taking backup >> logs/MANAGER-UPGRADER-SCRIPT.log

if not exist backup mkdir backup
if exist backup\manager-artifacts rmdir /Q /S backup\manager-artifacts
if not exist backup\manager-artifacts mkdir backup\manager-artifacts

copy /Y motadata-manager.exe backup\manager-artifacts\
xcopy /E /I /Y lib backup\manager-artifacts\lib

echo %date%:%time%:updating manager artifacts >> logs/MANAGER-UPGRADER-SCRIPT.log

del /F /Q motadata-manager.exe
copy /Y downloads\manager-artifacts\windows\motadata-manager.exe .

if exist "downloads\manager-artifacts\lib" (
    echo %date%:%time%:updating lib >> logs/MANAGER-UPGRADER-SCRIPT.log
    rmdir /Q /S lib
    xcopy /E /I /Y downloads\manager-artifacts\lib lib
) else (
    echo %date%:%time%:lib not exists >> logs/MANAGER-UPGRADER-SCRIPT.log
)

echo %date%:%time%:starting motadata service >> logs/MANAGER-UPGRADER-SCRIPT.log

net start "Motadata Agent"
ping localhost -n 5 > nul

tasklist | findstr "motadata-manager" | find /c /v "" > SERVICE_STATUS
set /p SERVICESTATUS= < SERVICE_STATUS
del /F /Q SERVICE_STATUS
 
if %SERVICESTATUS% geq 1 (
echo %date%:%time%:motadata service started successfully >> logs/MANAGER-UPGRADER-SCRIPT.log

cmd /c "type upgrade.me.org&&echo :succeed"  > upgrade.me.txt
copy /Y upgrade.me.txt upgrade.me
del /F /Q upgrade.me.txt
del /F /Q upgrade.me.org

) else (

echo %date%:%time%:motadata manager upgrade failed >> logs/MANAGER-UPGRADER-SCRIPT.log

net stop "Motadata Agent"
taskkill /f /im motadata-manager.exe
del /F /Q motadata-manager.exe
rmdir /Q /S lib

echo %date%:%time%:restoring manager artifacts >> logs/MANAGER-UPGRADER-SCRIPT.log

copy /Y backup\manager-artifacts\motadata-manager.exe .
xcopy /E /I /Y backup\manager-artifacts\lib lib

echo %date%:%time%:starting motadata service >> logs/MANAGER-UPGRADER-SCRIPT.log

net start "Motadata Agent"
del /F /Q upgrade.me.txt
del /F /Q upgrade.me.org
)