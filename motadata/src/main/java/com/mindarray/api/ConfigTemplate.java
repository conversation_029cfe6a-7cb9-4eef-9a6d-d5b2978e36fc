/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.config.ConfigConstants;
import com.mindarray.store.ConfigTemplateConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class ConfigTemplate extends AbstractAPI
{
    public static final String CONFIG_TEMPLATE_NAME = "config.template.name";
    public static final String CONFIG_TEMPLATE_CATALOG_IDS = "config.template.catalog.ids";
    public static final String CONFIG_TEMPLATE_OS_TYPE = "config.template.os.type";
    public static final String CONFIG_TEMPLATE_VENDOR = "config.template.vendor";
    public static final String CONFIG_TEMPLATE_DESCRIPTION = "config.template.description";
    private static final Logger LOGGER = new Logger(ConfigTemplate.class, MOTADATA_API, "Config Template API");

    public ConfigTemplate()
    {
        super("config-templates", ConfigTemplateConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            if (response != null && !response.isEmpty() && response.containsKey(APIConstants.Entity.CONFIGURATION.getName()))
            {
                var entities = response.getJsonArray(APIConstants.Entity.CONFIGURATION.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    var item = entities.getJsonObject(index);

                    var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT));

                    ConfigConstants.removeGarbageFields(object);

                    item.mergeIn(object);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}
