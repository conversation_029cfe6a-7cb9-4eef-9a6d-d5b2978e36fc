/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: Added tag-rules.json for Rule Based Tagging
 *  28-Feb-2025		Darshan Parmar		     MOTADATA-5215: SonarQube Suggestions Resolution
 *  4-Mar-2025      Bharat                   MOTADATA-4740: Two factor authentication 2FA
 *  2-Apr-2025      Bharat                   MOTADATA-5637: Domain Mapping in Flow
 *  20-Feb-2025     Pruth<PERSON>aj               MOTADATA-5285: NetRoute and NetRoute Policy json added
 *  22-Apr-2025     Bharat                   MOTADATA-5822: Metric Explorer Enhancements
 *  24-Jun-2025     Yash Tiwari              MOTADATA-6528: added explorers.json for Common Explorers implementation
 * */

package com.mindarray.db;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.util.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;

public class ConfigDBInitializer
{
    private static final Logger LOGGER = new Logger(ConfigDBInitializer.class, GlobalConstants.MOTADATA_DB, "Config DB Initializer");

    private ConfigDBInitializer()
    {
    }

    public static Future<Boolean> init()
    {
        var promise = Promise.<Boolean>promise();

        var schemas = new JsonArray().
                add("dependencies.json").
                add("flow-ip-groups.json").
                add("protocol-mappers.json").
                add("policy-first-trigger-ticks.json").
                add("event-sources.json").
                add("data-retention-policy.json").
                add("system-services.json").
                add("dependency-mappers.json").
                add("backup-profiles.json").
                add("tags.json").
                add("netroutes.json").
                add("snmp-trap-forwarders.json").
                add("agents.json").
                add("snmp-trap-profiles.json").
                add("artifact.json").
                add("flow-ip-mappers.json").
                add("widgets.json").
                add("password-policy.json").
                add("integrations.json").
                add("application-mappers.json").
                add("explorers.json").
                add("my-account.json").
                add("column-mapper.json").
                add("remote-event-processors.json").
                add("compliance-policies.json").
                add("users.json").
                add("config-templates.json").
                add("flow-as-mappers.json").
                add("custom-monitoring-fields.json").
                add("metric-policies.json").
                add("mail-server-configuration.json").
                add("tag-rules.json").
                add("metric-plugins.json").
                add("compliance-rules.json").
                add("snmp-trap-listener-configuration.json").
                add("dashboards.json").
                add("system-processes.json").
                add("flow-settings.json").
                add("runbook-plugins.json").
                add("sms-gateway-configuration.json").
                add("topology-plugins.json").
                add("personal-access-tokens.json").
                add("rebranding.json").
                add("business-hours.json").
                add("user-roles.json").
                add("auth-token.json").
                add("plugin-id.json").
                add("single-sign-on.json").
                add("license.json").
                add("netroute-policies.json").
                add("ldap-servers.json").
                add("correlations.json").
                add("motadata-agent.json").
                add("proxy-server.json").
                add("compliance-weighted-calculations.json").
                add("log-parser-plugins.json").
                add("integration-profiles.json").
                add("mac-scanners.json").
                add("objects.json").
                add("log-forwarders.json").
                add("storage-profiles.json").
                add("flow-domain-mappers.json").
                add("flow-geolocation-mappers.json").
                add("log-collectors.json").
                add("system-files.json").
                add("groups.json").
                add("credential-profiles.json").
                add("snmp-device-catalogs.json").
                add("discoveries.json").
                add("configurations.json").
                add("dns-record.json").
                add("schedulers.json").
                add("two-factor-authentication.json").
                add("compliance-benchmarks.json").
                add("reports.json").
                add("flow-sampling-rates.json").
                add("templates.json").
                add("metrics.json").
                add("snmp-oid-groups.json");

        if (LicenseUtil.LICENSE_EDITION.get() != LicenseUtil.LicenseEdition.HYBRID_LITE && LicenseUtil.LICENSE_EDITION.get() != LicenseUtil.LicenseEdition.ESSENTIAL)
        {
            schemas.add("log-parsers.json").add("event-policies.json");
        }

        LOGGER.info("config db initializer started....");

        LOGGER.info("checking system table...");

        Bootstrap.configDBService().getOne(DBConstants.TBL_SYSTEM,
                result ->
                {
                    if (result.succeeded())
                    {
                        if (result.result().containsKey(INSTALLATION_DATE))
                        {
                            LOGGER.info(String.format("found system installation date time %s", result.result().getString(INSTALLATION_DATE)));

                            var millis = result.result().containsKey(SYSTEM_START_TIME) ? result.result().getLong(SYSTEM_START_TIME) : DateTimeUtil.currentMilliSeconds();

                            //TODO will need to remove as of now keeping this because whomsoever takes this build will need to do blank configdb

                            if (!result.result().containsKey(SYSTEM_START_TIME))//backward compatibility handling
                            {
                                try
                                {
                                    millis = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse(result.result().getString(INSTALLATION_DATE)).getTime();
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            else
                            {
                                Bootstrap.configDBService().update(DBConstants.TBL_SYSTEM,
                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, DEFAULT_ID),
                                        result.result().put(SYSTEM_START_TIME, millis), DEFAULT_USER, MOTADATA_SYSTEM, asyncResult ->
                                        {
                                        });
                            }

                            CommonUtil.setSystemInstallationTimeSeconds(millis);
                        }

                        else
                        {
                            CommonUtil.setSystemInstallationTimeSeconds(DateTimeUtil.currentMilliSeconds());
                        }

                        populateConfigDB(promise, schemas, result.result(), !result.result().containsKey(INSTALLATION_DATE));
                    }
                    else
                    {
                        promise.fail("something went wrong with config db service..");

                        LOGGER.fatal("something went wrong with config db service..");

                        LOGGER.info("stopping config db service...");
                    }
                });

        return promise.future();
    }

    private static void populateConfigDB(Promise<Boolean> promise, JsonArray schemas, JsonObject result, boolean freshInstallation)
    {
        var futures = new ArrayList<Future<Void>>();

        var update = new AtomicBoolean();

        var cipherUtil = new CipherUtil();

        var schemaVersions = result.containsKey(SYSTEM_SCHEMA_VERSIONS) ? result.getJsonObject(SYSTEM_SCHEMA_VERSIONS) : new JsonObject();

        try
        {
            Bootstrap.vertx().<Void>executeBlocking(response ->
            {
                for (var schemaName : schemas)
                {
                    try (var inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("entity-schemas" + PATH_SEPARATOR + schemaName.toString()))
                    {
                        var schema = new JsonObject(IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8));

                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        if (!schema.isEmpty() && schema.getJsonArray(DBConstants.ENTRIES) != null && !schema.getJsonArray(DBConstants.ENTRIES).isEmpty()
                                && schema.getString(APIConstants.ENTITY_TABLE) != null)
                        {
                            var schemaEntries = schema.getJsonArray(DBConstants.ENTRIES);

                            initSchemaEntries(schemaEntries, schemaVersions, schema, CommonUtil.getString(schemaName), future, cipherUtil, new AtomicInteger(0), update);
                        }
                        else if (schema.getString(APIConstants.ENTITY_TABLE) != null)
                        {
                            Bootstrap.configDBService().upsertAll(schema.getString(APIConstants.ENTITY_TABLE), null, SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("created table " + schemaName);
                                }
                                else
                                {
                                    LOGGER.info("failed to create table " + schemaName);
                                }

                                future.complete();
                            });
                        }
                        else
                        {
                            future.complete();
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                if (futures.isEmpty())
                {
                    promise.complete(freshInstallation);

                    response.complete();
                }
                else
                {
                    Future.join(futures).onComplete(asyncResponse ->
                    {
                        if (asyncResponse.succeeded())
                        {
                            if (update.get())
                            {
                                Bootstrap.configDBService().upsert(DBConstants.TBL_SYSTEM,
                                        result.isEmpty() ? new JsonObject().put(INSTALLATION_DATE, LocalDateTime.now().toString()).put(SYSTEM_START_TIME, CommonUtil.getSystemInstallationTimeSeconds())
                                                .put(ID, DEFAULT_ID).put(SYSTEM_SCHEMA_VERSIONS, schemaVersions) : new JsonObject().put(SYSTEM_SCHEMA_VERSIONS, schemaVersions).put(ID, DEFAULT_ID),
                                        SYSTEM_USER, MOTADATA_SYSTEM, asyncResult -> promise.complete(freshInstallation));
                            }
                            else
                            {
                                promise.complete(freshInstallation);
                            }
                        }
                        else
                        {
                            promise.fail(asyncResponse.cause());
                        }

                        response.complete();
                    });
                }
            }, false, asyncResult ->
            {
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private static void initSchemaEntries(JsonArray schemaEntries, JsonObject schemaVersions, JsonObject schema, String schemaName, Promise<Void> promise, CipherUtil cipherUtil, AtomicInteger currentIndex, AtomicBoolean update)
    {
        if (currentIndex.get() >= schemaEntries.size())
        {
            promise.complete();
        }
        else
        {
            var entries = new JsonArray();

            var entry = schemaEntries.getJsonObject(currentIndex.get());

            var entryVersion = CommonUtil.getFloat(entry.getString("version"));

            var schemaVersion = CommonUtil.getFloat(schemaVersions.containsKey(CommonUtil.getString(schemaName)) ? schemaVersions.getString(CommonUtil.getString(schemaName)) : "0.0");

            if (entryVersion > schemaVersion)
            {
                update.set(true);

                var table = schema.getString(APIConstants.ENTITY_TABLE);

                LOGGER.info(String.format("found default entries for table %s", table));

                var entities = entry.getJsonArray(DBConstants.RECORDS);

                if (!entry.isEmpty() && entry.getString(DBConstants.TYPE) != null && entities != null)
                {
                    try
                    {
                        switch (entry.getString(DBConstants.TYPE))
                        {
                            case DBConstants.INLINE ->
                            {
                                var userEntry = table.equalsIgnoreCase(DBConstants.TBL_USER);

                                for (var index = 0; index < entities.size(); index++)
                                {
                                    var item = entities.getJsonObject(index);

                                    if (userEntry)
                                    {
                                        item.put(User.USER_PASSWORD_LAST_UPDATED_TIME, System.currentTimeMillis());
                                    }

                                    entries.add(item.put(DBConstants.FIELD_TYPE, item.getString(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)));
                                }

                            }

                            case DBConstants.FILE ->
                            {
                                // read data from file

                                for (var entity : entities)
                                {
                                    try
                                    {
                                        var items = new JsonArray(cipherUtil.decrypt(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + entity).toString()));

                                        for (var index = 0; index < items.size(); index++)
                                        {
                                            var item = items.getJsonObject(index);

                                            entries.add(item.put(DBConstants.FIELD_TYPE, item.getString(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)));
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                }
                            }

                            case DBConstants.DIRECTORY ->
                            {
                                for (var entity : entities)
                                {
                                    try
                                    {
                                        var files = new File(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + entity).listFiles();

                                        if (files != null)
                                        {
                                            if (entry.containsKey(DBConstants.ENCODED))
                                            {
                                                for (var file : files)
                                                {
                                                    var items = new JsonArray(Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath()).toString());

                                                    for (var index = 0; index < items.size(); index++)
                                                    {
                                                        var item = items.getJsonObject(index);

                                                        entries.add(item.put(DBConstants.FIELD_TYPE, item.getString(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)));
                                                    }
                                                }
                                            }

                                            else
                                            {
                                                for (var file : files)
                                                {
                                                    var items = new JsonArray(cipherUtil.decrypt(Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath()).toString()));

                                                    for (var index = 0; index < items.size(); index++)
                                                    {
                                                        var item = items.getJsonObject(index);

                                                        entries.add(item.put(DBConstants.FIELD_TYPE, item.getString(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)));
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                }
                            }

                            case DBConstants.SCRIPT ->
                            {
                                for (var index = 0; index < entities.size(); index++)
                                {
                                    update(entities.getJsonObject(index), table, entry.getString(DBConstants.DIRECTORY), entries);
                                }
                            }

                            default ->
                            {
                                // do nothing
                            }
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        currentIndex.set(currentIndex.incrementAndGet());

                        initSchemaEntries(schemaEntries, schemaVersions, schema, CommonUtil.getString(schemaName), promise, cipherUtil, currentIndex, update);
                    }
                }

                schemaVersions.put(CommonUtil.getString(schemaName), entryVersion);

                if (!entries.isEmpty())
                {
                    Bootstrap.configDBService().upsertAll(table, entries, SYSTEM_USER, MOTADATA_SYSTEM, asyncResult ->
                    {
                        if (asyncResult.failed())
                        {
                            LOGGER.fatal(String.format("failed to save default entry for table %s", table));
                        }

                        else
                        {
                            LOGGER.info(String.format("default entry for table %s inserted successfully...", table));
                        }

                        currentIndex.set(currentIndex.incrementAndGet());

                        initSchemaEntries(schemaEntries, schemaVersions, schema, CommonUtil.getString(schemaName), promise, cipherUtil, currentIndex, update);
                    });
                }
                else
                {
                    LOGGER.debug("no default entries for table " + table);

                    Bootstrap.configDBService().upsertAll(schema.getString(APIConstants.ENTITY_TABLE), null, SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            LOGGER.info("created table " + schemaName);
                        }
                        else
                        {
                            LOGGER.info("failed to create table " + schemaName);
                        }

                        currentIndex.set(currentIndex.incrementAndGet());

                        initSchemaEntries(schemaEntries, schemaVersions, schema, CommonUtil.getString(schemaName), promise, cipherUtil, currentIndex, update);
                    });
                }
            }
            else
            {
                LOGGER.debug(String.format("for table %s entryversion %s is not greater then schemaVersion %s", schemaName, entryVersion, schemaVersion));

                currentIndex.set(currentIndex.incrementAndGet());

                initSchemaEntries(schemaEntries, schemaVersions, schema, CommonUtil.getString(schemaName), promise, cipherUtil, currentIndex, update);
            }
        }
    }

    private static void update(JsonObject item, String table, String directory, JsonArray items)
    {
        JsonObject context = null;

        String key = null;

        try
        {
            switch (table)
            {
                case DBConstants.TBL_METRIC_PLUGIN ->
                {
                    context = item.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT);

                    key = item.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL).equalsIgnoreCase(NMSConstants.MetricPlugin.CUSTOM.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;
                }

                case DBConstants.TBL_RUNBOOK_PLUGIN ->
                {
                    context = item.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

                    key = item.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.CUSTOM_SCRIPT.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;
                }

                case DBConstants.TBL_TOPOLOGY_PLUGIN ->
                {
                    context = item.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);

                    key = item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.CUSTOM.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;
                }

                case DBConstants.TBL_LOG_PARSER_PLUGIN ->
                {
                    context = item.getJsonObject(LogParserPlugin.LOG_PARSER_PLUGIN_CONTEXT);

                    key = LogParserPlugin.LOG_PARSER_PLUGIN_SCRIPT;

                }

                default ->
                {
                    // do nothing
                }
            }

            if (context != null && CommonUtil.isNotNullOrEmpty(context.getString(key)))
            {
                var file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + directory + GlobalConstants.PATH_SEPARATOR +
                        context.getString(key);

                if (Bootstrap.vertx().fileSystem().existsBlocking(file))
                {
                    context.put(key, CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(file)).trim());
                }
            }

            items.add(item.put(DBConstants.FIELD_TYPE, item.getString(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}