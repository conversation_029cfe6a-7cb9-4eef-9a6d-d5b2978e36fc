/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  17-APR-2025     Chopra Deven        MOTADATA-5827: passing "container.type" in "Active Alert" request
 *  25-Mar-2025     <PERSON><PERSON>        Added Support for Status Flap Metric
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 *  09-June-2025    <PERSON><PERSON>        Updated Gauge Availability logic to not consider non-monitor as  instances.
 */
package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationAvailabilityManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationAvailabilityManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Availability Manager");

    private final JsonObject metricColumns = new JsonObject();

    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();//queryId along with subqueryIds and status

    private final Map<Long, JsonObject> subQueries = new HashMap<>(); //subquery context

    private final Map<Long, JsonObject> queryContexts = new HashMap<>(); //query original request context

    private final Map<Long, String> queries = new HashMap<>(); //queryid

    private final Map<Long, Map<Long, String>> streamingQueries = new HashMap<>(); //queryid

    private final Map<Long, Short> queryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<Long, Short>> subQueryTrackers = new HashMap<>();//query progress tracker

    private final Map<Long, Map<String, JsonObject>> customQueries = new HashMap<>();//custom or join queries which result needs to be decoded for manipulation and adding special columns

    private final Map<Long, List<Long>> customSubQueries = new HashMap<>();//main custom sub query of custom queries

    private final Map<Long, List<String>> childQueries = new HashMap<>();//dependent categories queries

    //private final Map<Long, List<JsonObject>> appStatusQueries = new HashMap<>();//dependent datasource queries

    private final List<Long> runningQueries = new ArrayList<>();

    private final List<Long> queuedQueries = new ArrayList<>();

    private final Set<Integer> objectIds = new HashSet<>();

    private final Map<Long, JsonObject> queuedQueryContexts = new HashMap<>();

    private final Map<Long, Integer> queryTickers = new HashMap<>();//query timer after time complete query not completed removing its context

    private final StringBuilder builder = new StringBuilder();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == BootstrapType.COLLECTOR)
            {
                promise.fail("failed to start visualization availability manager, reason: invalid boot sequence...");
            }
            else
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
                {
                    if (reply.succeeded())
                    {
                        var ids = ObjectConfigStore.getStore().getObjectIds();

                        for (var i = 0; i < ids.size(); i++)
                        {
                            objectIds.add(ids.getInteger(i));
                        }
                        //invalid queries kindly remove it as response already received or query aborted
                        vertx.setPeriodic(30 * 1000L, periodicTimer ->
                        {
                            var iterator = queryTickers.entrySet().iterator();

                            while (iterator.hasNext())
                            {
                                var entry = iterator.next();

                                entry.setValue(entry.getValue() - 30);

                                if (entry.getValue() <= 0)
                                {
                                    vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                            .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, entry.getKey()).encode().getBytes()).getBytes()));

                                    if (queryStatuses.containsKey(entry.getKey()))
                                    {
                                        for (var subQueryId : queryStatuses.get(entry.getKey()).keySet())
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));

                                            Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));
                                        }
                                    }

                                    iterator.remove();
                                }
                            }

                            if (runningQueries.removeIf(query -> !queries.containsKey(query)) && !queuedQueries.isEmpty())
                            {
                                send();
                            }
                        });

                        metricColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));

                        vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                        {
                            try
                            {

                                var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                                var widgetId = 0L;

                                var queryId = buffer.getLongLE(0);

                                var subQueryId = buffer.getLongLE(8);

                                var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                                var subQueryProgress = buffer.getUnsignedByte(16);

                                var status = buffer.getUnsignedByte(33);

                                var validResult = true;

                                //there are certain times it happens due to ZMQ 50 percent progress is received after 100 so ignoring 50 percent result
                                if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId) && !childQueries.containsKey(queryId))
                                {
                                    subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).computeIfAbsent(subQueryId, val -> subQueryProgress);

                                    if (subQueryTrackers.get(queryId).get(subQueryId) <= subQueryProgress)
                                    {
                                        subQueryTrackers.get(queryId).put(subQueryId, subQueryProgress);
                                    }
                                    else
                                    {
                                        validResult = false;
                                    }
                                }

                                if (validResult)
                                {
                                    if (!customQueries.containsKey(queryId) && queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                                    {
                                        var succeeded = true;

                                        short queryProgress = 0;

                                        if (childQueries.containsKey(queryId))
                                        {
                                            if (subQueryCalculatedProgress >= 100)
                                            {
                                                subQueryCalculatedProgress = CommonUtil.getShort(subQueryCalculatedProgress / childQueries.get(queryId).size());

                                                buffer.setUnsignedByte(16, subQueryCalculatedProgress);

                                                queryStatuses.get(queryId).remove(subQueryId);//as one subquery data received now not required for progress calculation so removing it

                                                if (subQueries.containsKey(subQueryId))
                                                {
                                                    subQueries.get(subQueryId).put(VISUALIZATION_TYPE, subQueries.get(subQueryId).getString(VISUALIZATION_TYPE_CHILD_VISUALIZATION)).put(VISUALIZATION_CATEGORY, childQueries.get(queryId).removeFirst());
                                                }

                                                if (!childQueries.get(queryId).isEmpty())
                                                {
                                                    vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR, new JsonObject().put(VISUALIZATION_CATEGORY, childQueries.get(queryId).getFirst()).put("child.query", true).put(QUERY_CONTEXT, queryContexts.get(queryId)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, false, null, false, true)).put(VisualizationConstants.QUERY_ID, queryId));
                                                }

                                                else
                                                {
                                                    childQueries.remove(queryId);
                                                }
                                            }

                                            else
                                            {
                                                subQueryCalculatedProgress = CommonUtil.getShort(subQueryCalculatedProgress / childQueries.get(queryId).size());

                                                buffer.setUnsignedByte(16, subQueryCalculatedProgress);

                                                subQueries.get(subQueryId).put(VISUALIZATION_TYPE, subQueries.get(subQueryId).getString(VISUALIZATION_TYPE_CHILD_VISUALIZATION)).put(VISUALIZATION_CATEGORY, childQueries.get(queryId).getFirst());
                                            }
                                        }

                                    /*if (appStatusQueries.containsKey(queryId) && subQueryCalculatedProgress == 100)
                                    {
                                        appStatusQueries.get(queryId).add(VisualizationConstants.unpack(buffer, LOGGER, false, null, false,true));
                                    }*/

                                        subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).put(subQueryId, subQueryCalculatedProgress);

                                        if (queryStatuses.get(queryId).size() > 1)
                                        {
                                            queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                            buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                            queryTrackers.put(queryId, queryProgress);
                                        }

                                        else
                                        {
                                            queryTrackers.put(queryId, subQueryCalculatedProgress);
                                        }

                                        if (queries.containsKey(queryId))
                                        {
                                            var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                            if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                            {
                                                widgetId = CommonUtil.getLong(tokens[0]);
                                            }
                                        }

                                        if (status == 0)//fail
                                        {
                                            var errorLength = 38 + buffer.getIntLE(34);

                                            var errorMessage = buffer.getString(38, errorLength);

                                            if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                            {
                                                succeeded = false;
                                            }

                                            queryStatuses.get(queryId).put(subQueryId, errorMessage);

                                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                                        }
                                        else
                                        {
                                            queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                                        }

                                        EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                                        if (subQueries.containsKey(subQueryId) || queryContexts.containsKey(queryId))
                                        {
                                            var context = subQueries.containsKey(subQueryId) ? subQueries.get(subQueryId) : queryContexts.get(queryId);

                                            if (context.containsKey(EVENT_TYPE))
                                            {
                                                var publish = queryTrackers.get(queryId) >= 100;

                                                if (context.containsKey(PUBLISH_SUB_QUERY_PROGRESS) && context.getBoolean(PUBLISH_SUB_QUERY_PROGRESS))
                                                {
                                                    publish = true;
                                                }

                                                if (publish)//if require event to be published to other events will be publishing it
                                                {
                                                    if (context.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(VISUALIZATION_DECODE_RESPONSE).equalsIgnoreCase(YES))
                                                    {
                                                        vertx.eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId), queryContexts.get(queryId).getBoolean(DISCARD_DUMMY_ROWS, true))));
                                                    }
                                                    else
                                                    {
                                                        vertx.eventBus().send(context.getString(EVENT_TYPE), new JsonObject().mergeIn(context).put(QUERY_CONTEXT, queryContexts.get(queryId)).put(QUERY_PROGRESS, queryTrackers.get(queryId)).put(RESULT, buffer.getBytes()));
                                                    }
                                                }
                                            }
                                            else if (!streamingQueries.containsKey(queryId))
                                            {
                                                //in some widgets like hardware sensor UI needs data only after its subquery progress is 100 otherwise its not able to merge multiple subqueries into single
                                                if ((queryContexts.get(queryId) != null && !queryContexts.get(queryId).containsKey(PUBLISH_SUB_QUERY_PROGRESS)) || subQueryProgress == 100)
                                                {
                                                    vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                                }

                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, false, queryContexts.get(queryId), true, true));
                                                }
                                            }
                                        }

                                        if (streamingQueries.containsKey(queryId) && streamingQueries.get(queryId).size() > 1 && subQueryProgress == 100)
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                        }

                                        if (subQueryProgress == 100)
                                        {
                                            subQueries.remove(subQueryId);
                                        }

                                        if (queryTrackers.get(queryId) >= 100)
                                        {
                                            runningQueries.remove(queryId);

                                            if (!queuedQueries.isEmpty())
                                            {
                                                send();
                                            }

                                            queryStatuses.get(queryId).forEach((key, value) ->
                                            {
                                                if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                                {
                                                    builder.append(value).append(NEW_LINE);
                                                }
                                            });

                                            if (succeeded)
                                            {
                                                vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                                            }
                                            else
                                            {
                                                vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                            }

                                            if (streamingQueries.containsKey(queryId) && queryStatuses.get(queryId).size() == 1)
                                            {
                                                vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                            }

                                        /*if (appStatusQueries.containsKey(queryId))
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR, new JsonObject().put("application.status", true).put(SUB_QUERY_ID, subQueryId).put(QUERY_CONTEXT, queryContexts.get(queryId)).put(RESULT, appStatusQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                            appStatusQueries.remove(queryId);
                                        }*/

//                                        else
//                                        {
                                            cleanUp(queryId);
//                                        }

                                            if (CommonUtil.traceEnabled())
                                            {
                                                //as of now dumping trace log will be having query explorer to track each and every query
                                                LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                                        .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("custom.sub.queries", customSubQueries.size()).put("query.contexts", queryContexts.size())
                                                        .put("custom.queries", customQueries.size()).put("child.queries", childQueries.size()).put("queries", queries.size()).put("streaming.queries", streamingQueries.size()));
                                            }

                                            builder.setLength(0);
                                        }
                                    }

                                    else
                                    {
                                        if (customQueries.containsKey(queryId))
                                        {
                                            var completes = 0;

                                            var voidResponses = 0;

                                            var previousProgress = 0;

                                            // in some of the cases, first 100% and later 50% result is received for the same query id in that case no data is displayed
                                            if (customQueries.get(queryId).get(CommonUtil.getString(subQueryId)) != null
                                                    && customQueries.get(queryId).get(CommonUtil.getString(subQueryId)).getInteger(QUERY_PROGRESS) != null)
                                            {
                                                previousProgress = customQueries.get(queryId).get(CommonUtil.getString(subQueryId)).getInteger(QUERY_PROGRESS);
                                            }

                                            if (previousProgress < subQueryProgress)
                                            {
                                                customQueries.get(queryId).put(CommonUtil.getString(subQueryId),
                                                        unpack(buffer, LOGGER, queryContexts.get(queryId).containsKey(JOIN_ALIAS), queryContexts.get(queryId), false, true));
                                            }
                                            else
                                            {
                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace(String.format("Unordered result fetched for : %s and query : %s and visualization : %s", subQueryId, queryId,
                                                            queryContexts.get(queryId).getString(VISUALIZATION_NAME)));
                                                }
                                            }

                                            for (var value : customQueries.get(queryId).values())
                                            {
                                                if (value.containsKey(VisualizationConstants.QUERY_PROGRESS) && (value.getInteger(VisualizationConstants.QUERY_PROGRESS) == 100 ||
                                                        (subQueries.containsKey(subQueryId) && subQueries.get(subQueryId).containsKey(JOIN_RESULT) &&
                                                                subQueries.get(subQueryId).getString(JOIN_RESULT).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))))
                                                {
                                                    completes++;

                                                    if (!value.containsKey(RESULT))
                                                    {
                                                        voidResponses++;
                                                    }

                                                    if (customSubQueries.containsKey(queryId) && !subQueries.containsKey(subQueryId))
                                                    {
                                                        // Send to visualization manager for progress update in case of join query
                                                        vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                                    }
                                                }
                                            }

                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace("QUERY ID :  " + queryId + ", SUB QUE ID :" + subQueryId + ", COMPLETE :" + completes + ", PROGRESS :" + subQueryProgress + ", VoidResponse :" + voidResponses + ", CUSTOM QUERY SIZE : " + customQueries.get(queryId).size() + ", COMP QUERY SIZE : " + (queryContexts.get(queryId).containsKey(COMPOSITE_QUERY_SIZE) ? queryContexts.get(queryId).getInteger(COMPOSITE_QUERY_SIZE) : 0));
                                            }

                                            // Processing composite query only when total response received matches with total query size
                                            if (completes == customQueries.get(queryId).size() && (queryContexts.get(queryId) == null || !queryContexts.get(queryId).containsKey(COMPOSITE_QUERY_SIZE) || !queryContexts.get(queryId).getBoolean("composite.query", false) || queryContexts.get(queryId).getInteger(COMPOSITE_QUERY_SIZE) == completes))
                                            {
                                                if (customSubQueries.containsKey(queryId) && subQueries.get(customSubQueries.get(queryId).getFirst()) != null)
                                                {
                                                    vertx.eventBus().send(EventBusConstants.EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR, new JsonObject().put(QUERY_PROGRESS, subQueryCalculatedProgress).put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(customSubQueries.get(queryId).getFirst()).getString(VisualizationConstants.VISUALIZATION_CATEGORY)).put(VisualizationConstants.SUB_QUERY_ID, customSubQueries.get(queryId).getFirst()).put(QUERY_CONTEXT, subQueries.get(customSubQueries.get(queryId).getFirst())).put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                                    customQueries.get(queryId).keySet().stream().filter(subQuery -> !Objects.equals(CommonUtil.getLong(subQuery), customSubQueries.get(queryId).getFirst())).forEach(subQuery -> cleanUp(queryId, CommonUtil.getLong(subQuery)));
                                                }
                                                else if (subQueries.containsKey(subQueryId) && (voidResponses > 0 && completes >= voidResponses))//means result not available need to provide error to UI
                                                {
                                                    if (CommonUtil.debugEnabled())
                                                    {
                                                        LOGGER.debug("Query Id : " + queryId + ", Subquery Id : " + subQueryId + " Sending error to UI as received void response");
                                                    }

                                                    vertx.eventBus().send(EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR, new JsonObject().put(QUERY_PROGRESS, 100).put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_CATEGORY)).put(VisualizationConstants.SUB_QUERY_ID, subQueryId).put(QUERY_CONTEXT, subQueries.get(subQueryId)).put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                                    customQueries.get(queryId).keySet().stream().filter(subQuery -> !Objects.equals(CommonUtil.getLong(subQuery), subQueryId)).forEach(subQuery -> cleanUp(queryId, CommonUtil.getLong(subQuery)));
                                                }
                                                else
                                                {
                                                    if (CommonUtil.debugEnabled())
                                                    {
                                                        LOGGER.debug("Query Id : " + queryId + ", Subquery Id : " + subQueryId + " Clearing custom query context");
                                                    }

                                                    subQueries.remove(subQueryId);

                                                    queryContexts.remove(queryId);

                                                    queryStatuses.remove(queryId);
                                                }

                                                customSubQueries.remove(queryId);

                                                customQueries.remove(queryId);
                                            }
                                        }
                                    }
                                }
                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }

                        });

                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
                        {
                            try
                            {
                                var event = message.body();

                                var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                                {
                                    update(metricColumns, tokens, true);
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }).exceptionHandler(LOGGER::error);

                        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                        {
                            var event = message.body();

                            switch (ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                            {
                                case ADD_OBJECT ->
                                        objectIds.add(ObjectConfigStore.getStore().getObjectIdById(event.getLong(ID)));

                                case DELETE_OBJECT ->
                                {
                                    if ((!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null) && (!event.containsKey(OBJECTS) || event.getValue(OBJECTS) == null))
                                    {
                                        objectIds.remove(event.getInteger(AIOpsObject.OBJECT_ID));
                                    }
                                }

                                default ->
                                {
                                    // do nothing
                                }
                            }
                        }).exceptionHandler(LOGGER::error);

                        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
                        {
                            var queryId = new AtomicLong();

                            try
                            {
                                var currentTime = System.currentTimeMillis();

                                var event = message.body();

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Visualization Metric Request received:" + event);
                                }

                                queryId.set(event.getLong(VisualizationConstants.QUERY_ID));

                                var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                                if (event.containsKey(VisualizationConstants.VISUALIZATION_STREAMING))
                                {
                                    streamingQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());
                                }

                                Set<Integer> qualifiedObjects;

                                var adminRole = false;

                                if (user.getLong(ID).equals(DEFAULT_ID))//default
                                {
                                    qualifiedObjects = objectIds;

                                    user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));

                                    adminRole = true;
                                }

                                else
                                {
                                    qualifiedObjects = ObjectConfigStore.getStore().getUniqueObjectIdsByGroups(user.getJsonArray(User.USER_GROUPS));

                                    if (qualifiedObjects == null)
                                    {
                                        qualifiedObjects = new HashSet<>();
                                    }
                                }

                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                        .put(EventBusConstants.EVENT_ID, queryId.get())
                                        .put(USER_NAME, event.getString(USER_NAME))
                                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_VISUALIZATION));

                                EventBusConstants.updateEvent(queryId.get(),
                                        String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()),
                                        EventBusConstants.EVENT_STATE_RUNNING);

                                var filters = event.containsKey(FILTERS) && !event.getJsonObject(FILTERS).isEmpty() ? JsonObject.mapFrom(event.remove(FILTERS)) : new JsonObject();

                                var dataSources = new JsonArray().addAll((JsonArray) event.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                               /* if (event.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS) && (event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS) || event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY) || event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES)))
                                {
                                    appStatusQueries.computeIfAbsent(queryId.get(), value -> new ArrayList<>());
                                }*/

                                if (!dataSources.isEmpty())
                                {
                                    var replaceableColumn = false;

                                    if (event.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && !event.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                                    {
                                        for (var column : event.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY))
                                        {
                                            if (VisualizationConstants.REPLACEABLE_COLUMNS.containsKey(CommonUtil.getString(column)))
                                            {
                                                replaceableColumn = true;
                                            }
                                        }
                                    }

                                    if (event.containsKey(VisualizationConstants.JOIN_TYPE))
                                    {
                                        customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());

                                        replaceableColumn = false;
                                    }

                                    if (replaceableColumn || event.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.MAP.getName()) || (event.containsKey(PAGINATION_QUERY) || (event.containsKey(VISUALIZATION_TYPE) && event.getString(VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))))
                                    {
                                        customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());
                                    }

                                    if (event.containsKey(VISUALIZATION_CHILD_CATEGORIES) && !childQueries.containsKey(queryId.get()))
                                    {
                                        childQueries.computeIfAbsent(queryId.get(), value -> new ArrayList<>()).addAll(event.getJsonArray(VISUALIZATION_CHILD_CATEGORIES).getList());
                                    }

                                    var buffer = Buffer.buffer();

                                    var visualizationDataSource = dataSources.getJsonObject(0);

                                    var entities = new HashMap<String, Object>();

                                    var filteredGroupEntities = new HashSet<>();

                                    var filteredTagEntities = new HashSet<>();

                                    var instanceMetric = false;

                                    var cache = false;

                                    var filterTagEntities = false; //Used for Tag entities only for HeatMap for SourceType.

                                    var plugins = new HashSet<>();

                                    var instanceTypes = new JsonArray();

                                    var aggregations = new HashSet<String>();

                                    var aggregationFuncs = 0;

                                    var columns = new HashSet<String>();

                                    var error = EMPTY_VALUE;

                                    var groupBy = EMPTY_VALUE;

                                    var columnPlugins = new JsonArray();

                                    var subQueryId = event.getLong(SUB_QUERY_ID);

                                    if (streamingQueries.containsKey(queryId.get()))
                                    {
                                        streamingQueries.get(queryId.get()).put(subQueryId, YES);
                                    }

                                    queryContexts.put(queryId.get(), new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));

                                    if (customQueries.containsKey(queryId.get()))
                                    {
                                        customQueries.get(queryId.get()).put(CommonUtil.getString(subQueryId), new JsonObject());

                                        if (replaceableColumn || visualizationDataSource.containsKey(VisualizationConstants.JOIN_TYPE))
                                        {
                                            customSubQueries.computeIfAbsent(queryId.get(), value -> new ArrayList<>()).add(subQueryId);
                                        }
                                    }

                                    queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(subQueryId, EMPTY_VALUE);

                                    if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                                    {
                                        event.remove(VisualizationConstants.VISUALIZATION_TIMELINE);

                                        DateTimeUtil.buildTimeline(visualizationDataSource, event, user);

                                        event.put(VisualizationConstants.VISUALIZATION_TIMELINE, visualizationDataSource.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE));

                                        visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_TIMELINE);
                                    }

                                    if (!filters.isEmpty())
                                    {
                                        visualizationDataSource.put(FILTERS, filters);
                                    }

                                    var filterQuery = !visualizationDataSource.getJsonObject(FILTERS).getJsonObject(DATA_FILTER).isEmpty();

                                    VisualizationConstants.validateFilters(visualizationDataSource.getJsonObject(FILTERS));//for safe side if filter size gets > 3 so will be removing extra groups as DB not able to handle

                                    if (VisualizationDataSource.OBJECT_AVAILABILITY.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)) || VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)) || VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                    {
                                        var visualizationDataPoints = new JsonArray();

                                        var statuses = new HashSet<>();

                                        var pluginEntities = new HashMap<String, Set<Integer>>();

                                        var dataPoints = visualizationDataSource.getJsonArray(DATA_POINTS);

                                        if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName())))
                                        {
                                            if (dataPoints.getJsonObject(0).containsKey(ENTITIES) && !dataPoints.getJsonObject(0).getJsonArray(ENTITIES).isEmpty() && (dataPoints.getJsonObject(0).containsKey(ENTITY_TYPE) && dataPoints.getJsonObject(0).getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationGrouping.TAG.getName())))
                                            {
                                                filterTagEntities = true;
                                            }

                                            if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(GROUP_VM))
                                            {
                                                groupBy = GROUP_VM;

                                                var dataPointObj = (JsonObject) dataPoints.remove(0);

                                                for (var dataPoint : VM_DATA_POINTS)
                                                {
                                                    dataPoints.add(new JsonObject().mergeIn(dataPointObj).put(DATA_POINT, dataPoint).put(INSTANCE, true));
                                                }
                                            }
                                            else if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(GROUP_ACCESS_POINT))
                                            {
                                                groupBy = GROUP_ACCESS_POINT;

                                                var dataPointObj = (JsonObject) dataPoints.remove(0);

                                                for (var dataPoint : ACCESS_POINT_DATA_POINTS)
                                                {
                                                    dataPoints.add(new JsonObject().mergeIn(dataPointObj).put(DATA_POINT, dataPoint).put(INSTANCE, true));
                                                }
                                            }

                                            else if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(GROUP_APP))
                                            {
                                                groupBy = GROUP_APP;

                                                var dataPointObj = (JsonObject) dataPoints.remove(0);

                                                for (var dataPoint : APPLICATION_DATA_POINTS)
                                                {
                                                    dataPoints.add(new JsonObject().mergeIn(dataPointObj).put(DATA_POINT, dataPoint).put(INSTANCE, true));
                                                }
                                            }
                                            else if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(NMSConstants.INTERFACE) || dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS))
                                            {
                                                if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(NMSConstants.INTERFACE))
                                                {
                                                    groupBy = INTERFACE;
                                                }
                                                else
                                                {
                                                    groupBy = GROUP_PROCESS;
                                                }

                                                dataPoints.getJsonObject(0).put(INSTANCE, true);
                                            }
                                            else if (!dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()) && !event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()))
                                            {
                                                groupBy = dataPoints.getJsonObject(0).getString(DATA_POINT);

                                                dataPoints.getJsonObject(0).put(INSTANCE, true);
                                            }
                                        }
                                        else if (event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS))
                                        {
                                            if (dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(GROUP_APP))
                                            {
                                                groupBy = GROUP_APP;

                                                var dataPointObj = (JsonObject) dataPoints.remove(0);

                                                for (var dataPoint : APPLICATION_DATA_POINTS)
                                                {
                                                    dataPoints.add(new JsonObject().mergeIn(dataPointObj).put(DATA_POINT, dataPoint).put(INSTANCE, true));
                                                }
                                            }
                                        }

                                        if (event.containsKey(CONTAINER_TYPE) && event.getString(CONTAINER_TYPE).equalsIgnoreCase("report") && !event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()))//as in case of report need counters ofr availability and status flap so adding
                                        {
                                            var dataPoint = dataPoints.getJsonObject(0);

                                            if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()) || VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                            {
                                                dataSources.getJsonObject(0).remove(VISUALIZATION_RESULT_BY);

                                                event.put(JOIN_RESULT, VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName());

                                                queryContexts.get(queryId.get()).put(JOIN_ALIAS, true);

                                                if (!customQueries.containsKey(queryId.get()))
                                                {
                                                    customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(CommonUtil.getString(subQueryId), new JsonObject());

                                                    customSubQueries.computeIfAbsent(queryId.get(), value -> new ArrayList<>()).add(subQueryId);
                                                }

                                                if (dataPoint.getString(DATA_POINT).equalsIgnoreCase("monitor"))
                                                {
                                                    columnPlugins.add(NMSConstants.AVAILABILITY_PLUGIN_ID);

                                                    if (dataPoint.containsKey(ENTITIES) && !dataPoint.getJsonArray(ENTITIES).isEmpty())
                                                    {
                                                        JsonArray statusFlapDataPoints;

                                                        if (VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                                        {
                                                            statusFlapDataPoints = new JsonObject(HOURLY_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                        }
                                                        else
                                                        {
                                                            statusFlapDataPoints = new JsonObject(CUMULATIVE_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                        }

                                                        for (var i = 0; i < statusFlapDataPoints.size(); i++)
                                                        {
                                                            statusFlapDataPoints.getJsonObject(i).put(ENTITY_TYPE, dataPoint.getString(ENTITY_TYPE)).put(ENTITIES, dataPoint.getJsonArray(ENTITIES));
                                                        }

                                                        dataPoints = statusFlapDataPoints;
                                                    }
                                                    else
                                                    {
                                                        if (VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                                        {
                                                            dataPoints = new JsonObject(HOURLY_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                        }
                                                        else
                                                        {
                                                            dataPoints = new JsonObject(CUMULATIVE_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    if (metricColumns.containsKey(dataPoint.getString(DATA_POINT) + INSTANCE_SEPARATOR + DURATION))
                                                    {
                                                        columnPlugins.addAll(metricColumns.getJsonObject(dataPoint.getString(DATA_POINT) + INSTANCE_SEPARATOR + DURATION).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS));

                                                        if (dataPoint.containsKey(ENTITIES) && !dataPoint.getJsonArray(ENTITIES).isEmpty())
                                                        {
                                                            JsonArray statusFlapDataPoints;

                                                            if (VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                                            {
                                                                statusFlapDataPoints = new JsonObject(HOURLY_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                            }
                                                            else
                                                            {
                                                                statusFlapDataPoints = new JsonObject(VisualizationConstants.INSTANCE_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                            }

                                                            for (var i = 0; i < statusFlapDataPoints.size(); i++)
                                                            {
                                                                var statusFlapDataPoint = statusFlapDataPoints.getJsonObject(i);

                                                                statusFlapDataPoint.put(DATA_POINT, dataPoint.getString(DATA_POINT) + INSTANCE_SEPARATOR + statusFlapDataPoint.getString(DATA_POINT)).put(ENTITY_TYPE, dataPoint.getString(ENTITY_TYPE)).put(ENTITIES, dataPoint.getJsonArray(ENTITIES));
                                                            }

                                                            dataPoints = statusFlapDataPoints;
                                                        }
                                                        else
                                                        {
                                                            JsonArray statusFlapDataPoints;

                                                            if (VisualizationDataSource.HOURLY_OBJECT_STATUS_FLAP.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                                            {
                                                                statusFlapDataPoints = new JsonObject(HOURLY_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                            }
                                                            else
                                                            {
                                                                statusFlapDataPoints = new JsonObject(VisualizationConstants.INSTANCE_STATUS_FLAP_DATA_POINTS).getJsonArray(DATA_POINTS);
                                                            }

                                                            for (var i = 0; i < statusFlapDataPoints.size(); i++)
                                                            {
                                                                var statusFlapDataPoint = statusFlapDataPoints.getJsonObject(i);

                                                                statusFlapDataPoint.put(DATA_POINT, dataPoint.getString(DATA_POINT) + INSTANCE_SEPARATOR + statusFlapDataPoint.getString(DATA_POINT)).put(ENTITY_TYPE, dataPoint.getString(ENTITY_TYPE)).put(ENTITIES, dataPoint.getJsonArray(ENTITIES));
                                                            }

                                                            dataPoints = statusFlapDataPoints;
                                                        }
                                                    }
                                                }

                                                if (visualizationDataSource.containsKey(STATUS) && !visualizationDataSource.getJsonArray(STATUS).isEmpty())
                                                {
                                                    if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))
                                                    {
                                                        var dataFilter = new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

                                                        dataFilter.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0).getJsonArray(CONDITIONS).add(new JsonObject().put(OPERAND, "status.flap.history").put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()).put(VALUE, visualizationDataSource.getJsonArray(STATUS).stream().map(item -> CommonUtil.getString(item).toUpperCase()).collect(Collectors.toList())));

                                                        visualizationDataSource.getJsonObject(FILTERS).put(DATA_FILTER, dataFilter.getJsonObject(DATA_FILTER));
                                                    }
                                                    else
                                                    {
                                                        var resultFilter = new JsonObject(VISUALIZATION_RESULT_FILTER_JSON_TEMPLATE);

                                                        resultFilter.getJsonObject(RESULT_FILTER).getJsonArray(CONDITIONS).getJsonObject(0).put(OPERAND, "status.flap.history").put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()).put(VALUE, visualizationDataSource.getJsonArray(STATUS).stream().map(item -> CommonUtil.getString(item).toUpperCase()).collect(Collectors.toList()));

                                                        visualizationDataSource.getJsonObject(FILTERS).put(RESULT_FILTER, resultFilter.getJsonObject(RESULT_FILTER));
                                                    }
                                                }
                                            }
                                        }

                                        for (var i = 0; i < dataPoints.size(); i++)
                                        {
                                            if (visualizationDataPoints.size() < 16)
                                            {
                                                var buildEntityKeys = true;

                                                var buildFilterTagEntityKeys = false;

                                                var instanceFilterKeys = new JsonArray();

                                                var visualizationDataPoint = dataPoints.getJsonObject(i);

                                                if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                                {
                                                    visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                                    visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                                }

                                                if (event.containsKey(VisualizationConstants.FILTER_KEYS))//for interface drilldown will be providing that particular keys only..
                                                {
                                                    instanceFilterKeys.addAll(event.getJsonArray(VisualizationConstants.FILTER_KEYS));
                                                }

                                                visualizationDataPoint.put(VisualizationConstants.FILTER_KEYS, instanceFilterKeys);

                                                var pluginIds = new JsonArray();

                                                var column = visualizationDataPoint.getString(VisualizationConstants.DATA_POINT);

                                                columns.add(column);

                                                instanceMetric = column.contains(INSTANCE_SEPARATOR);

                                                if (!columnPlugins.isEmpty())
                                                {
                                                    pluginIds.addAll(columnPlugins);
                                                }
                                                else if (metricColumns.containsKey(column))
                                                {
                                                    pluginIds = metricColumns.getJsonObject(column).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);
                                                }
                                                else
                                                {
                                                    if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && ((event.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && event.getString(VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationCategory.GRID.getName()) && (event.containsKey(VISUALIZATION_DRILL_DOWN) && event.getString(VISUALIZATION_DRILL_DOWN).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()))) || (column.contains("monitor") || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()))))
                                                    {
                                                        buildEntityKeys = false;

                                                        pluginIds.add(NMSConstants.AVAILABILITY_PLUGIN_ID);//availability plugin
                                                    }
                                                    else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CONFIG_METRIC.getName()))
                                                    {
                                                        buildEntityKeys = false;

                                                        pluginIds.add(ConfigConstants.CONFIG_PLUGIN_ID);
                                                    }
                                                }

                                                if (!pluginIds.isEmpty())
                                                {
                                                    if (filterQuery || (instanceMetric && !(PASSOVER_FILTER_INSTANCES.contains(column.split(INSTANCE_SEPARATOR)[0]))))
                                                    {
                                                        buildEntityKeys = false;
                                                    }

                                                    if ((visualizationDataPoint.containsKey(ENTITY_TYPE) && visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationGrouping.TAG.getName())) && (visualizationDataPoint.containsKey(ENTITIES)
                                                            && !visualizationDataPoint.getJsonArray(ENTITIES).isEmpty()) && (column.contains(INSTANCE_SEPARATOR) && (visualizationDataSource.containsKey(VISUALIZATION_RESULT_BY) && !visualizationDataSource.getJsonArray(VISUALIZATION_RESULT_BY).isEmpty()) && column.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(visualizationDataSource.getJsonArray(VISUALIZATION_RESULT_BY).getString(0))))
                                                    {
                                                        buildFilterTagEntityKeys = true;
                                                    }

                                                    var qualifiedEntities = qualifyEntities(visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, pluginIds, filteredGroupEntities, filteredTagEntities, buildEntityKeys, buildFilterTagEntityKeys, event.containsKey("archived") && YES.equalsIgnoreCase(event.getString("archived")));

                                                    if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                    {
                                                        if (visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                                                        {
                                                            aggregations.add(DatastoreConstants.AggregationType.SUM.getName());

                                                            aggregations.add(DatastoreConstants.AggregationType.COUNT.getName());

                                                            aggregationFuncs += 2;
                                                        }

                                                        else
                                                        {
                                                            aggregations.add(visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR));

                                                            aggregationFuncs++;
                                                        }

                                                        visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                                        entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

                                                        pluginEntities.putAll((Map<? extends String, ? extends Set<Integer>>) qualifiedEntities.get(PLUGIN_ENTITIES));

                                                        plugins.addAll(entities.values());

                                                        if (qualifiedEntities.containsKey(ENTITY_KEYS) && qualifiedEntities.get(VisualizationConstants.ENTITY_KEYS) != null)
                                                        {
                                                            visualizationDataPoint.put(ENTITY_KEYS, qualifiedEntities.get(VisualizationConstants.ENTITY_KEYS));
                                                        }

                                                        visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                                                        visualizationDataPoints.add(visualizationDataPoint);
                                                    }

                                                    //When Type - grid and Drill Down -> Gauge it will fetch data from cache...
                                                    if ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && (event.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && event.getString(VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationCategory.GRID.getName())) && (event.containsKey(VISUALIZATION_DRILL_DOWN) && event.getString(VISUALIZATION_DRILL_DOWN).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()))) ||
                                                            ((visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CONFIG_METRIC.getName())) && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName())))//data will be fetched from in memory ObjectStatusCacheStore
                                                    {
                                                        cache = true;

                                                        var status = EMPTY_VALUE;

                                                        if (instanceMetric)
                                                        {
                                                            status = column.split(INSTANCE_SEPARATOR)[1].replace(".count", EMPTY_VALUE);
                                                        }

                                                        else
                                                        {
                                                            status = column.split("\\.")[1];
                                                        }

                                                        statuses.add(status);
                                                    }
                                                    else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.CONFIG_METRIC.getName()) || (event.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS))) //data will be fetched from in memory config cache store and for Application Status
                                                    {
                                                        cache = true;
                                                    }
                                                    else if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName())))//data will be fetched from in memory ObjectStatusCacheStore
                                                    {
                                                        cache = true;

                                                        if (!visualizationDataPoint.getString(DATA_POINT).equalsIgnoreCase(VisualizationGrouping.MONITOR.getName()))
                                                        {
                                                            instanceTypes.add(instanceMetric ? visualizationDataPoint.getString(DATA_POINT).split(INSTANCE_SEPARATOR)[0] : visualizationDataPoint.getString(DATA_POINT));
                                                        }

                                                        if (visualizationDataSource.containsKey(STATUS) && !visualizationDataSource.getJsonArray(STATUS).isEmpty())
                                                        {
                                                            for (var status : visualizationDataSource.getJsonArray(GlobalConstants.STATUS))
                                                            {
                                                                statuses.add(CommonUtil.getString(status).toLowerCase());
                                                            }
                                                        }
                                                        else
                                                        {
                                                            statuses.add(STATUS_DOWN.toLowerCase());
                                                            statuses.add(STATUS_UP.toLowerCase());
                                                            statuses.add(STATUS_MAINTENANCE.toLowerCase());
                                                            statuses.add(STATUS_DISABLE.toLowerCase());
                                                            statuses.add(STATUS_UNREACHABLE.toLowerCase());
                                                        }
                                                    }

                                                    if (instanceMetric && !(visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName())))
                                                    {
                                                        instanceTypes.add(column.split(INSTANCE_SEPARATOR)[0]);
                                                    }
                                                }
                                            }
                                        }

                                        if (event.containsKey("instance.type.filter") && event.containsKey(INSTANCE_TYPE) && event.containsKey(event.getString(INSTANCE_TYPE)))
                                        {
                                            prepareInstanceFilter(visualizationDataSource, event.getValue(event.getString(INSTANCE_TYPE)), event.getJsonObject("instance.type.filter").getString(event.getString(INSTANCE_TYPE)));
                                        }

                                        if (!visualizationDataPoints.isEmpty())
                                        {
                                            if (aggregationFuncs > VisualizationConstants.MAX_AGGREGATION_FUNCTIONS)// combination of unique counters with selected aggregation should be less than 16
                                            {
                                                error = ErrorMessageConstants.DATA_POINT_LIMIT_EXCEEDED;
                                            }

                                            else
                                            {
                                                for (var plugin : plugins)
                                                {
                                                    var pluginName = CommonUtil.getString(plugin);

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace("plugin size::" + pluginEntities.get(pluginName).size() + " items plugin size::" + ObjectConfigStore.getStore().getItemsByPlugin(CommonUtil.getInteger(pluginName.split(DASH_SEPARATOR)[0])));

                                                    }


                                                    if (pluginEntities.get(pluginName).size() == ObjectConfigStore.getStore().getItemsByPlugin(CommonUtil.getInteger(pluginName.split(DASH_SEPARATOR)[0])))
                                                    {
                                                        adminRole = true;
                                                    }

                                                    else
                                                    {
                                                        adminRole = false;

                                                        break;
                                                    }
                                                }

                                                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataPoints);

                                                visualizationDataSource.put(ENTITIES, entities);

                                                visualizationDataSource.put(GROUP_BY, groupBy);

                                                visualizationDataSource.put(STATUS, new ArrayList<>(statuses));

                                                if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName()) && (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()) || event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS)))
                                                {
                                                    visualizationDataSource.put(VisualizationConstants.INSTANCE_TYPE, instanceTypes.isEmpty() ? instanceTypes.add(EMPTY_VALUE) : instanceTypes);
                                                }
                                                else
                                                {
                                                    visualizationDataSource.put(VisualizationConstants.INSTANCE_TYPE, instanceTypes.isEmpty() ? EMPTY_VALUE : instanceTypes.getValue(0));
                                                }

                                                if (visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName()))
                                                {
                                                    applyFilter(VisualizationDataSource.PERFORMANCE_METRIC.getName(), event.put(OBJECT_FILTER, true), false, visualizationDataSource, null, null, entities.keySet());
                                                }
                                            }
                                        }

                                        else
                                        {
                                            error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                        }

                                    }

                                    if (error.isEmpty())
                                    {
                                        if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY))
                                        {
                                            if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                                            {
                                                visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_RESULT_BY);
                                            }

                                            else
                                            {
                                                if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationGrouping.GROUP.getName()))
                                                {
                                                    if (!visualizationDataSource.containsKey(VisualizationGrouping.GROUP.getName()))
                                                    {
                                                        visualizationDataSource.put(VisualizationGrouping.GROUP.getName(), filterGroups(visualizationDataSource.getString(VisualizationConstants.TYPE), !filteredGroupEntities.isEmpty() ? new ArrayList<>(filteredGroupEntities) : user.getJsonArray(User.USER_GROUPS).getList()));
                                                    }
                                                }

                                                else if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationGrouping.MONITOR.getName()))
                                                {
                                                    if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())) && filterTagEntities)
                                                    {
                                                        visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterTags(cache, filteredTagEntities, entities, instanceTypes));
                                                    }
                                                }

                                                else if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationGrouping.TAG.getName()))
                                                {
                                                    if (!visualizationDataSource.containsKey(VisualizationGrouping.TAG.getName()))
                                                    {
                                                        if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())))
                                                        {
                                                            visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterTags(cache, filteredTagEntities, entities, instanceTypes));
                                                        }
                                                        else
                                                        {
                                                            visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterTags(cache, filteredTagEntities, entities, instanceTypes.isEmpty() ? EMPTY_VALUE : instanceTypes.getString(0)));
                                                        }
                                                    }
                                                }

                                                // Result By other than Main Three..
                                                else if (!(visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationGrouping.MONITOR.getName()) || visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationGrouping.GROUP.getName()) || visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationGrouping.TAG.getName())))
                                                {
                                                    if (instanceMetric)
                                                    {
                                                        if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(instanceTypes.getString(0)))
                                                        {
                                                            visualizationDataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationGrouping.MONITOR.getName()).add(visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                                        }
                                                        else
                                                        {
                                                            if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())))
                                                            {
                                                                visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterGroupTags(cache, filteredTagEntities, entities, instanceTypes, visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                                            }
                                                            else
                                                            {
                                                                visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterGroupTags(cache, filteredTagEntities, entities, instanceTypes.isEmpty() ? EMPTY_VALUE : instanceTypes.getString(0), visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                                            }

                                                            visualizationDataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationGrouping.TAG.getName()));
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())))
                                                        {
                                                            visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterGroupTags(cache, filteredTagEntities, entities, instanceTypes, visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                                        }
                                                        else
                                                        {
                                                            visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterGroupTags(cache, filteredTagEntities, entities, instanceTypes.isEmpty() ? EMPTY_VALUE : instanceTypes.getString(0), visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                                        }

                                                        visualizationDataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationGrouping.TAG.getName()));
                                                    }
                                                }
                                            }
                                        }

                                        else if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())) || (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.PERFORMANCE_METRIC.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())) && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GRID.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.TOP_N.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.CHART.getName()))
                                        {
                                            if (instanceMetric && !instanceTypes.isEmpty())
                                            {
                                                visualizationDataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationGrouping.MONITOR.getName()).add(instanceTypes.getValue(0)));
                                            }

                                            else
                                            {
                                                visualizationDataSource.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationGrouping.MONITOR.getName()));
                                            }
                                        }

                                        else if ((event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName()) && visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.OBJECT_AVAILABILITY.getName())) && filterTagEntities)
                                        {
                                            if (!instanceTypes.isEmpty())
                                            {
                                                visualizationDataSource.put(VisualizationGrouping.TAG.getName(), filterTags(cache, filteredTagEntities, entities, instanceTypes.getString(0)));

                                                visualizationDataSource.put("plugin.ids", metricColumns.getJsonObject(instanceTypes.getString(0)).getJsonArray("mapper.plugin.ids"));
                                            }
                                        }

                                        visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));

                                        var subQueryContext = new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonObject().mergeIn(visualizationDataSource)).put(VisualizationConstants.ADMIN_ROLE, adminRole ? YES : NO).put(VisualizationConstants.QUERY_ID, queryId.get()).put(VisualizationConstants.SUB_QUERY_ID, subQueryId);

                                        if (!cache)
                                        {
                                            setQueryParameters(event, subQueryContext, aggregations, null);
                                        }

                                        if (event.containsKey(EVENT_TYPE))
                                        {
                                            queryTickers.put(queryId.get(), INTERVAL_SECONDS);
                                        }

                                        buffer.appendByte(DatastoreConstants.OperationType.DATA_READ.getName()).appendBytes(subQueryContext.encode().getBytes());

                                        EventBusConstants.updateEvent(queryId.get(), String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED, subQueryId, DateTimeUtil.timestamp()));

                                        var queryContext = new JsonObject().mergeIn(subQueryContext).put("cache", cache || subQueryContext.getString(CONTAINER_TYPE, "dashboard").equalsIgnoreCase("Template") || subQueryContext.getString("drill.down.type", "no").equalsIgnoreCase("yes")).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource));

                                        subQueries.put(subQueryId, queryContext);//ui requires same datasource as sent to identify in some custom widgets

                                        if (!event.containsKey(EVENT_TYPE))//as in case its internal request so need not send to VM
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_SUB_QUERY_CONTEXT, queryContext);
                                        }

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace("Visualization Metric Request generated time taken:" + (System.currentTimeMillis() - currentTime) + " ms for query id:" + queryId.get());
                                        }

                                        if (!cache)
                                        {
                                            if (subQueryContext.getInteger(QUERY_PRIORITY) == QueryPriority.P0.getName())
                                            {
                                                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                        .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                            }

                                            else
                                            {
                                                send(queryId.get(), new JsonObject()
                                                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                        .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                            }
                                        }
                                        else
                                        {
                                            visualizationDataSource.put(ENTITIES, ObjectConfigStore.getStore().getItemsByObjectIds(entities.keySet().stream().map(CommonUtil::getInteger).collect(Collectors.toList())));

                                            if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GAUGE.getName())
                                                    || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GRID.getName())
                                                    || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.TOP_N.getName())
                                                    || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()) || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()))
                                            {
                                                if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HEAT_MAP.getName()))
                                                {
                                                    Bootstrap.availabilityCacheService().getAvailabilityHeatmap(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }

                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });
                                                }
                                                else if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.GRID.getName()) && event.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_STATUS))
                                                {
                                                    Bootstrap.availabilityCacheService().getApplicationStatus(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }

                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });

                                                }
                                                else if ((event.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && event.getString(VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationCategory.GRID.getName())) && (event.containsKey(VISUALIZATION_DRILL_DOWN) && event.getString(VISUALIZATION_DRILL_DOWN).equalsIgnoreCase(VisualizationCategory.GAUGE.getName())))
                                                {
                                                    Bootstrap.availabilityCacheService().getDrillDownResult(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }

                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });
                                                }
                                                else if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()))
                                                {
                                                    Bootstrap.availabilityCacheService().getActiveStatus(visualizationDataSource.put(CONTAINER_TYPE, event.getString(CONTAINER_TYPE, EMPTY_VALUE)), result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }

                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    if (VisualizationDataSource.OBJECT_AVAILABILITY.getName().equalsIgnoreCase(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                                    {
                                                        Bootstrap.availabilityCacheService().getAvailabilityByStatus(visualizationDataSource, result ->
                                                        {
                                                            if (result.succeeded() && result.result() != null)
                                                            {
                                                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                            }

                                                            else
                                                            {
                                                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);
                                                            }
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    else
                                    {
                                        customQueries.remove(queryId.get());

                                        customSubQueries.remove(queryId.get());

                                        VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", error), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);

                                    }
                                }
                                else
                                {
                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), queryId.get(), event.getLong(SUB_QUERY_ID), LOGGER, EVENT_VISUALIZATION_RESPONSE);
                                }

                            }

                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });
                    }
                });

                /*
                Following Local Consumer Purpose is to get acknowledgment from db and then to set the timer as per the ack in queryTicker.
                */
                Bootstrap.vertx().eventBus().<Long>localConsumer(EVENT_DATASTORE_ACKNOWLEDGEMENT, message ->
                {
                    try
                    {
                        if (message.body() != null)
                        {
                            var queryId = message.body();

                            if (queryTickers.containsKey(queryId))
                            {
                                queryTickers.put(queryId, INTERVAL_SECONDS);
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                }).exceptionHandler(LOGGER::error);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private Map<String, Object> qualifyEntities(JsonObject context, JsonArray qualifiedGroups, Set<Integer> qualifiedObjects, JsonArray pluginIds, Set filteredGroupEntities, Set filteredTagEntities, boolean buildEntityKey, boolean instanceEntityKey, boolean archived)
    {
        Map<String, Object> qualifiedEntities = null;

        try
        {
            var column = context.getString(DATA_POINT);

            Map<String, String> entities = null;

            Map<String, String> entityKeys = null;

            JsonObject filteredEntities;

            var pluginEntities = new HashMap<String, Set<Integer>>();

            var plugins = new HashSet<>();

            var instanceMetric = column.contains(INSTANCE_SEPARATOR);

            var filterInstanceKeys = context.getJsonArray(FILTER_KEYS, new JsonArray());

            if (!context.containsKey(ENTITY_TYPE))
            {
                context.put(ENTITY_TYPE, "all");
            }

            for (var pluginId : pluginIds)
            {
                if (MetricConfigStore.getStore().getMetricName(CommonUtil.getInteger(pluginId)) != null)
                {
                    pluginEntities.computeIfAbsent(CommonUtil.getInteger(pluginId) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(CommonUtil.getInteger(pluginId)), value -> new HashSet<>());
                }
            }

            var items = filterEntities(context, qualifiedGroups, qualifiedObjects, filteredGroupEntities, filteredTagEntities, LOGGER, archived);

            if (items != null && !items.isEmpty())
            {
                entities = new HashMap<>();

                entityKeys = new HashMap<>();

                if ((!filterInstanceKeys.isEmpty() || buildEntityKey || (instanceEntityKey && !filteredTagEntities.isEmpty())) && instanceMetric)
                {
                    if (pluginIds.contains(NMSConstants.AVAILABILITY_PLUGIN_ID))
                    {
                        pluginIds.remove(NMSConstants.AVAILABILITY_PLUGIN_ID);//as we will not be considering availability plugin so removing it..
                    }

                    /*
                    Purpose of Using Instance Entity Key is when the data of result By and InstanceType is same, thus filtering out and put it in entity.keys.
                     */
                    if (instanceEntityKey && !filteredTagEntities.isEmpty())
                    {
                        filteredEntities = TagCacheStore.getStore().getInstanceTagEntities(items, context.getJsonArray(ENTITIES), context.getString(DATA_POINT).split(INSTANCE_SEPARATOR)[0]);

                        setTagInstanceEntities(pluginIds, column, entities, entityKeys, filteredEntities, pluginEntities, plugins);
                    }
                    else if (items.size() == 1 && !archived)//if only single monitor is qualified then will be sending entity keys also
                    {
                        setEntities(pluginIds, column, entities, entityKeys, pluginEntities, plugins, filterInstanceKeys, items);
                    }
                    else
                    {
                        setEntities(pluginIds, archived, items, entities, plugins, pluginEntities);
                    }
                }

                else
                {
                    if (pluginIds.contains(NMSConstants.AVAILABILITY_PLUGIN_ID))
                    {
                        for (var item : items)
                        {
                            entities.put(CommonUtil.getString(item), NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(NMSConstants.AVAILABILITY_PLUGIN_ID));

//                            entityKeys.put(item + CARET_SEPARATOR + column, NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(NMSConstants.AVAILABILITY_PLUGIN_ID));

                            pluginEntities.get(NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(NMSConstants.AVAILABILITY_PLUGIN_ID)).add(CommonUtil.getInteger(item));
                        }

                        plugins.add(NMSConstants.AVAILABILITY_PLUGIN_ID + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(NMSConstants.AVAILABILITY_PLUGIN_ID));
                    }

                    else
                    {
                        setEntities(pluginIds, archived, items, entities, plugins, pluginEntities);
                    }
                }
            }

            if (entities != null && !entities.isEmpty())
            {
                qualifiedEntities = new HashMap<>();

                qualifiedEntities.put(ENTITIES, entities);

                qualifiedEntities.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));

                qualifiedEntities.put(VisualizationConstants.PLUGIN_ENTITIES, pluginEntities);

                if (!entityKeys.isEmpty())
                {
                    qualifiedEntities.put(VisualizationConstants.ENTITY_KEYS, entityKeys);
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedEntities;
    }

    //as in case when we need to get data of instance with another instance filter than this method will be used for example in access point template we need to get data of wireless clients with filter of access point
    private void prepareInstanceFilter(JsonObject visualizationDataSource, Object value, String key)
    {
        try
        {
            var filters = new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

            filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0).getJsonArray(CONDITIONS).add(new JsonObject().put(OPERAND, key).put(OPERATOR, DatastoreConstants.ConditionGroup.EQUAL.getName()).put(VALUE, value));

            visualizationDataSource.put(FILTERS, filters);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Map<String, List<String>> filterTags(boolean cache, Set<Object> filteredEntities, Map<String, Object> qualifiedEntities, String instanceTypes)
    {
        var tags = new HashMap<String, List<String>>();

        try
        {
            if (!cache && filteredEntities != null && !filteredEntities.isEmpty())//filter tag provided
            {
                if (!instanceTypes.isEmpty())
                {
                    //filter level
                    filteredEntities.forEach(tag -> tags.put(CommonUtil.getString(tag), TagCacheStore.getStore().getInstanceIdsByTags(new JsonArray().add(CommonUtil.getString(tag))).getList()));

                }
                else
                {
                    filteredEntities.forEach(tag -> tags.put(CommonUtil.getString(tag), ObjectConfigStore.getStore().getObjectIdsByTags(new JsonArray().add(CommonUtil.getString(tag))).getList()));
                }
            }

            else
            {
                var entities = new JsonArray();

                qualifiedEntities.keySet().forEach(entity -> entities.add(CommonUtil.getInteger(entity)));

                Map<String, JsonArray> items;

                if (!instanceTypes.isEmpty())
                {
                    //For Instance Level Tagging....
                    items = TagCacheStore.getStore().getInstanceTags(entities, instanceTypes);
                }
                else
                {
                    //For Monitor Level Tagging...
                    items = ObjectConfigStore.getStore().getTagsByObjects(entities);
                }

                filterItems(filteredEntities, items, tags);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return tags;
    }

    private Map<String, List<String>> filterTags(boolean cache, Set<Object> filteredEntities, Map<String, Object> qualifiedEntities, JsonArray instanceTypes)
    {
        var tags = new HashMap<String, List<String>>();

        try
        {
            if (!cache && filteredEntities != null && !filteredEntities.isEmpty())//filter tag provided
            {
                if (!instanceTypes.isEmpty())
                {
                    //filter level
                    filteredEntities.forEach(tag -> tags.put(CommonUtil.getString(tag), TagCacheStore.getStore().getInstanceIdsByTags(new JsonArray().add(CommonUtil.getString(tag))).getList()));

                }
                else
                {
                    filteredEntities.forEach(tag -> tags.put(CommonUtil.getString(tag), ObjectConfigStore.getStore().getObjectIdsByTags(new JsonArray().add(CommonUtil.getString(tag))).getList()));
                }
            }

            else
            {
                var entities = new JsonArray();

                qualifiedEntities.keySet().forEach(entity -> entities.add(CommonUtil.getInteger(entity)));

                Map<String, JsonArray> items;

                if (!instanceTypes.isEmpty() && !instanceTypes.getString(0).equalsIgnoreCase(EMPTY_VALUE))
                {
                    //For Instance Level Tagging....
                    items = TagCacheStore.getStore().getInstanceTags(entities, instanceTypes);
                }
                else
                {
                    //For Monitor Level Tagging...
                    items = ObjectConfigStore.getStore().getTagsByObjects(entities);
                }

                filterItems(filteredEntities, items, tags);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return tags;
    }


    private Map<String, List<String>> filterGroupTags(boolean cache, Set<Object> filteredEntities, Map<String, Object> qualifiedEntities, String instanceType, String group)
    {
        var tags = new HashMap<String, List<String>>();

        try
        {
            if (!cache && filteredEntities != null && !filteredEntities.isEmpty())//filter tag provided
            {
                if (!instanceType.isEmpty())
                {
                    for (var entities : filteredEntities)
                    {
                        if (CommonUtil.getString(entities).contains(COLON_SEPARATOR) && CommonUtil.getString(entities).split(COLON_SEPARATOR)[0].equalsIgnoreCase(group))
                        {
                            tags.put(CommonUtil.getString(entities), TagCacheStore.getStore().getInstanceIdsByTags(new JsonArray().add(CommonUtil.getString(entities))).getList());
                        }
                    }
                }
                else
                {
                    for (var entities : filteredEntities)
                    {
                        if (CommonUtil.getString(entities).contains(COLON_SEPARATOR) && CommonUtil.getString(entities).split(COLON_SEPARATOR)[0].equalsIgnoreCase(group))
                        {
                            tags.put(CommonUtil.getString(entities), ObjectConfigStore.getStore().getObjectIdsByTags(new JsonArray().add(CommonUtil.getString(entities))).getList());
                        }
                    }
                }
            }

            else
            {
                var entities = new JsonArray();

                qualifiedEntities.keySet().forEach(entity -> entities.add(CommonUtil.getInteger(entity)));

                Map<String, JsonArray> items;

                if (!instanceType.isEmpty())
                {
                    //For Instance Level Tagging....
                    items = TagCacheStore.getStore().getTagsByGroupInstanceType(entities, instanceType, group);
                }
                else
                {
                    //For Monitor Level Tagging...
                    items = ObjectConfigStore.getStore().getTagsByGroupObjects(entities, group);
                }

                filterItems(filteredEntities, items, tags);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return tags;
    }

    private Map<String, List<String>> filterGroupTags(boolean cache, Set<Object> filteredEntities, Map<String, Object> qualifiedEntities, JsonArray instanceTypes, String group)
    {
        var tags = new HashMap<String, List<String>>();

        try
        {
            if (!cache && filteredEntities != null && !filteredEntities.isEmpty())//filter tag provided
            {
                if (!instanceTypes.isEmpty())
                {
                    for (var entities : filteredEntities)
                    {
                        if (CommonUtil.getString(entities).contains(COLON_SEPARATOR) && CommonUtil.getString(entities).split(COLON_SEPARATOR)[0].equalsIgnoreCase(group))
                        {
                            tags.put(CommonUtil.getString(entities), TagCacheStore.getStore().getInstanceIdsByTags(new JsonArray().add(CommonUtil.getString(entities))).getList());
                        }
                    }
                }
                else
                {
                    for (var entities : filteredEntities)
                    {
                        if (CommonUtil.getString(entities).contains(COLON_SEPARATOR) && CommonUtil.getString(entities).split(COLON_SEPARATOR)[0].equalsIgnoreCase(group))
                        {
                            tags.put(CommonUtil.getString(entities), ObjectConfigStore.getStore().getObjectIdsByTags(new JsonArray().add(CommonUtil.getString(entities))).getList());
                        }
                    }
                }
            }

            else
            {
                var entities = new JsonArray();

                qualifiedEntities.keySet().forEach(entity -> entities.add(CommonUtil.getInteger(entity)));

                Map<String, JsonArray> items;

                if (!instanceTypes.isEmpty() && !instanceTypes.getString(0).equalsIgnoreCase(EMPTY_VALUE))
                {
                    //For Instance Level Tagging....
                    items = TagCacheStore.getStore().getTagsByGroupInstanceTypes(entities, instanceTypes, group);
                }
                else
                {
                    //For Monitor Level Tagging...
                    items = ObjectConfigStore.getStore().getTagsByGroupObjects(entities, group);
                }

                filterItems(filteredEntities, items, tags);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return tags;
    }

    private void filterItems(Set<Object> filteredEntities, Map<String, JsonArray> items, Map<String, List<String>> tags)
    {
        if (!items.isEmpty())
        {
            if (filteredEntities != null && !filteredEntities.isEmpty())
            {
                var filteredTags = filteredEntities.stream().map(CommonUtil::getString).collect(Collectors.toSet());

                items.forEach((tag, objects) ->
                {
                    if (filteredTags.contains(tag))
                    {
                        tags.put(CommonUtil.getString(tag), objects.getList());
                    }
                });
            }
            else
            {
                items.forEach((tag, objects) -> tags.put(CommonUtil.getString(tag), objects.getList()));
            }
        }
    }

    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queuedQueryContexts.remove(id));
    }

    private void send(long id, JsonObject event)
    {
        if (event != null)
        {
            if (runningQueries.size() == MotadataConfigUtil.getMetricQueryQueueSize())
            {
                queuedQueryContexts.put(id, event);

                queuedQueries.add(id);
            }

            else
            {
                runningQueries.add(id);

                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, event.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
            }
        }
    }

    private void cleanUp(long queryId, long subQueryId)
    {
        try
        {
            //will be removing all subquerycontext
            if (queryStatuses.containsKey(queryId))
            {
                queryStatuses.get(queryId).remove(subQueryId);
            }

            if (customSubQueries.containsKey(queryId))
            {
                customSubQueries.get(queryId).remove(subQueryId);
            }

            subQueries.remove(subQueryId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void cleanUp(long queryId)
    {
        try
        {
            queryTrackers.remove(queryId);

            if (queryStatuses.containsKey(queryId))
            {
                queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
            }

            queries.remove(queryId);

            subQueryTrackers.remove(queryId);

            streamingQueries.remove(queryId);

            queryContexts.remove(queryId);

            //appStatusQueries.remove(queryId);

            customQueries.remove(queryId);

            customSubQueries.remove(queryId);

            queryTickers.remove(queryId);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
