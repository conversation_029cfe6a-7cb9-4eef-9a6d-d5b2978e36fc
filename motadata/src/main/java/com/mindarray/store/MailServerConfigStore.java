/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.MailServerConfiguration;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

public class MailServerConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(MailServerConfigStore.class, GlobalConstants.MOTADATA_STORE, "Mail Server Config Store");

    private static final MailServerConfigStore STORE = new MailServerConfigStore();

    private MailServerConfigStore()
    {

        super(DBConstants.TBL_MAIL_SERVER, LOGGER, false);
    }

    public static MailServerConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> updateStore()
    {
        var promise = Promise.<Void>promise();

        super.updateStore().onComplete(result ->
        {

            if (result.succeeded())
            {

                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                        new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                EventBusConstants.ChangeNotificationType.MAIL_SERVER.name()));

                promise.complete();
            }

            else
            {
                promise.fail(result.cause());

            }
        });

        return promise.future();
    }

    /**
     * @return true if 'mail.server.host' key is available
     */
    public boolean isConfigured()
    {
        var item = getItem();

        return item != null && CommonUtil.isNotNullOrEmpty(item.getString(MailServerConfiguration.MAIL_SERVER_HOST));
    }
}
