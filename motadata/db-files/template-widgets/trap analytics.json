[{"_type": "0", "id": 10000000100001, "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "trap", "join.type": "any", "sorting.column": "timestamp^last", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["event.source", "event.source", "trap.oid"], "data.points": [{"data.point": "trap.message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}, {"type": "trap.flap", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["event.source", "trap.oid"], "data.points": [{"data.point": "*", "aggregator": "last", "entities": [], "entity.type": "event.source"}]}], "visualization.name": "Trap Events", "visualization.properties": {"grid": {"visualization.grid.properties.required": "yes", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "trap.name", "title": "Trap Name", "type": "trap_drilldown", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "trap.oid", "title": "Trap OID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "event.source", "title": "Sources", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "trap.message.count", "title": "Count", "type": "selected_item_pills", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "trap.message.last", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "timestamp.last", "title": "Timestamp", "type": "ms_datetime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "Action", "title": "Action", "type": "trap_action", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "trap.severity", "title": "Severity", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "trap.type", "title": "Type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "trap.enterprise.id.last", "title": "Enterprise ID", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "trap.version.last", "title": "Trap Version", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "trap.vendor.last", "title": "<PERSON><PERSON><PERSON>", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "trap.raw.message", "title": "Raw Message", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}]}}, "visualization.result.by": ["event.source", "trap.oid"], "join.type": "any", "sorting.column": "timestamp^last", "join.columns": ["event.source", "trap.oid"], "dummy.fields": true}, {"_type": "0", "id": 10000000100002, "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.name": "Trap Overview", "visualization.type": "StackedVerticalBar", "visualization.data.sources": [{"type": "trap", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "trap.message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "vertical.legend": "no", "chart.label": "no", "highchart.settings": {"yAxis": {"allowDecimal": false}}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": []}]