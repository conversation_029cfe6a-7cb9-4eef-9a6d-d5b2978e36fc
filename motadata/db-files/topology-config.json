{"Hewlett Packard Enterprise": {"SPM": {"topology.plugin.protocol": "SPM", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 90, "parsing.script": "hpswitchportmapper.go", "script.language": "go", "topology.plugin.oids": {"spm.neighbor": {".*******.********.*******.2": "spm.neighbor"}}}}}, "D-Link": {"SPM": {"topology.plugin.protocol": "SPM", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 90, "parsing.script": "dlinkswitchportmapper.go", "script.language": "go", "topology.plugin.oids": {"spm.neighbor": {".*******.********.*******.2": "spm.neighbor"}}}}}, "Extreme Networks": {"SPM": {"topology.plugin.protocol": "SPM", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 90, "parsing.script": "extremeswitchportmapper.go", "script.language": "go", "topology.plugin.oids": {"spm.neighbor": {".*******.********.3.1.1": "spm.neighbor", ".*******.********.3.1.2": "spm.neighbor.port"}, "spm.port": {".*******.********.4.1.2": "spm.port"}, "spm.vlan": {".*******.4.1.1916.********.1": "spm.vlan"}}}}}, "Cisco Systems": {"IS-IS": {"topology.plugin.protocol": "IS-IS", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "isis.go", "script.language": "go", "topology.plugin.oids": {"isis.neighbor": {".*******.*********.*******": "isis.neighbor"}, "isis.cisco.neighbor": {".*******.********.118.1.*******": "isis.cisco.neighbor"}, "isis.cisco.neighbor.v6.interface": {".*******.********.1.4": "isis.cisco.neighbor.v6.interface"}}}}, "SPM": {"topology.plugin.protocol": "SPM", "topology.plugin.type": "Custom", "topology.plugin.context": {"timeout": 180, "script": "ciscoswitchportmapper.go", "script.language": "go"}}}, "Generic": {"CDP": {"topology.plugin.protocol": "CDP", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "cdp.go", "script.language": "go", "topology.plugin.oids": {"cdp.neighbor": {".*******.*******.********.1.4": "cdp.neighbor", ".*******.*******.********.1.7": "cdp.neighbor.interface"}}}}, "LLDP": {"topology.plugin.protocol": "LLDP", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "lldp.go", "script.language": "go", "topology.plugin.oids": {"lldp.neighbor": {".1.0.8802.*******.*******": "lldp.neighbor", ".1.0.8802.*******.*******": "lldp.neighbor.port.subtype", ".1.0.8802.*******.*******": "lldp.neighbor.port"}, "lldp.port.interface": {".1.0.8802.*******.*******": "lldp.port.interface", ".1.0.8802.*******.*******": "lldp.port.subtype"}}}}, "IS-IS": {"topology.plugin.protocol": "IS-IS", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "isis.go", "script.language": "go", "topology.plugin.oids": {"isis.neighbor": {".*******.*********.*******": "isis.neighbor"}, "isis.neighbor.v6.interface": {".*******.********.1.4": "isis.neighbor.v6.interface"}}}}, "OSPF": {"topology.plugin.protocol": "OSPF", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "ospf.go", "script.language": "go", "topology.plugin.oids": {"ospf.neighbor": {".*******.*********.1.1": "ospf.neighbor"}, "ospf.v6.neighbor": {".*******.*********.9.1.5": "ospf.v6.neighbor"}, "ospf.neighbor.interface": {".*******.********.1.1": "ospf.neighbor.interface"}, "ospf.neighbor.v6.interface": {".*******.********.1.4": "ospf.neighbor.v6.interface"}}}}, "BGP": {"topology.plugin.protocol": "BGP", "topology.plugin.type": "SNMP", "topology.plugin.context": {"timeout": 60, "parsing.script": "bgp.go", "script.language": "go", "topology.plugin.oids": {"bgp.neighbor": {".*******.********.1.7": "bgp.neighbor"}, "bgp.v6.neighbor": {"*******.*******.*********.1.2": "bgp.v6.neighbor"}, "bgp.neighbor.interface": {".*******.********.1.1": "bgp.neighbor.interface"}, "bgp.neighbor.v6.interface": {".*******.********.1.4": "bgp.neighbor.v6.interface"}}}}}}