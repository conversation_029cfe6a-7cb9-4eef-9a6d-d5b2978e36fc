/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.APIConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.FlowSamplingRateConfigStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

import static com.mindarray.TestAPIConstants.FLOW_IP_GROUP_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.MISC_COLUMN_MAPPER_API_ENDPOINT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_FLOW;
import static com.mindarray.eventbus.EventBusConstants.EVENT_VOLUME_BYTES;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestFlowProcessor
{
    private static final ZMQ.Socket FORWARDER = Bootstrap.zcontext().socket(SocketType.PUSH);
    private static final Logger LOGGER = new Logger(TestFlowProcessor.class, "Test Flow Processor", "Test Flow Processor");
    private static final String FLOW_CACHE_DIR_PATH = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.FLOW_CACHE_DIR;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormat.forPattern("yyyyMMddHHmm");
    private static MessageConsumer<byte[]> eventDBWriteTestConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var field = FlowSamplingRateConfigStore.getStore().getClass().getDeclaredField("samplingRates");

            field.setAccessible(true);

            var samplingRates = new ConcurrentHashMap<>();

            samplingRates.put("*******``||``1", 2);

            field.set(FlowSamplingRateConfigStore.getStore(), samplingRates);

            TestAPIUtil.post(FLOW_IP_GROUP_API_ENDPOINT, new JsonObject("{\"flow.ip.group\":[\"*******\"],\"flow.ip.group.name\":\"motadata.com\"}"), handler -> testContext.completeNow());

            FORWARDER.setSndHWM(MotadataConfigUtil.getEventBacklogSize());

            FORWARDER.setHWM(MotadataConfigUtil.getEventBacklogSize());

            FORWARDER.setSendTimeOut(0);

            FORWARDER.connect("tcp://" + "localhost" + ":" + 9449);

            LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

            if (TestUtil.vertx().fileSystem().existsBlocking(FLOW_CACHE_DIR_PATH))
            {
                LOGGER.info("flow-cache directory is available, deleting the directory...");

                TestUtil.vertx().fileSystem().deleteRecursive(FLOW_CACHE_DIR_PATH, true, asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        LOGGER.info("Directory deleted successfully.");

                        testContext.completeNow();
                    }
                    else
                    {
                        LOGGER.warn("Failed to delete directory: " + asyncResult.cause().getMessage());

                        testContext.failNow(asyncResult.cause());
                    }
                });
            }
            else
            {
                LOGGER.info("flow-cache directory is not available, before ALL is completed");

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (eventDBWriteTestConsumer != null)
        {
            eventDBWriteTestConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        if (eventDBWriteTestConsumer != null)
        {
            eventDBWriteTestConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testIngressFlow(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"0 (Routine)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":3930,\"ingress.packets\":9020,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\",\"user\":\"Vidhi\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 1, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 0, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    @Timeout(5000)
    void testEgressFlow(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"7 (Network Control)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":3930,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":9020,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 2, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 7, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @Timeout(5000)
    void testFlowTOSFlag1(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"2 (Immediate)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":786,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-2\",\"destination.if.index\":\"interface-index-1\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":1804,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 1, \"iface_out\": 2, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 2, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    @Timeout(5000)
    void testFlowTOSFlag2(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"3 (Flash)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":786,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-1\",\"destination.if.index\":\"interface-index-2\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":1804,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 2, \"iface_out\": 1, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 3, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(5000)
    void testFlowTOSFlag3(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"4 (Flash Override)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 4, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    @Timeout(5000)
    void testFlowTOSFlag4(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"5 (CRITIC/ECP)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 5, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(5000)
    void testFlowTOSFlag5(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"6 (Inter Network Control)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 6, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    @Timeout(5000)
    void testFlowTOSFlag6(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"1 (Priority)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 1, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(5000)
    void testFlowTOSFlag7(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"9 (Unknown)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 0, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 9, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)   // #24861
    @Order(10)
    @Timeout(5000)
    void testFlow1(VertxTestContext testContext) throws InterruptedException
    {
        var values = new JsonObject("{\"source\":\"*******\",\"tos\":\"7 (Network Control)\",\"flows\":1,\"source.ip\":\"*******\",\"destination.ip\":\"*******\",\"egress.volume.bytes\":3930,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":9020,\"volume.bytes\":3930,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\": 0,\"destination.port\": 80,\"tcp.flags\":\"unknown\",\"volume.bytes.per.packet\":0,\"protocol\":\"6 (TCP)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"Cloudflare\",\"source.asn\":80,\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"United States\",\"destination.city\":\"unknown\",\"destination.isp\":\"Lumen\",\"destination.domain\":\"unknown\",\"destination.asn\":15169,\"destination.aso\":\"GOOGLE\",\"destination.threat\":\"no\",\"packets\":9020,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\": 2, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 7, \"sampling_rate\": 10, \"timestamp_start\": \"0000-00-00 00:00:00.000000\", \"timestamp_end\": \"0000-00-00 00:00:00.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)   // #24861
    @Order(11)
    @Timeout(5000)
    void testFlow2(VertxTestContext testContext) throws InterruptedException
    {
        //source is **********, so source.if.index will be resolved
        var values = new JsonObject("{\"source.ip\":\"*******\",\"source.if.index\":\"Gi1/0/5\",\"destination.if.index\":\"interface-index-0\"}");

        assertFlowProcessorTestResult(testContext, values);

        var flow = "{\"tag\":2,\"class\":\"unknown\",\"peer_ip_src\":\"**********\",\"peer_ip_dst\":\"\",\"iface_in\":0,\"iface_out\":12,\"ip_src\":\"*******\",\"ip_dst\":\"*******\",\"port_src\":40,\"port_dst\":80,\"tcp_flags\":\"0\",\"ip_proto\":\"6\",\"tos\":7,\"sampling_rate\":10,\"timestamp_start\":\"0000-00-00 00:00:00.000000\",\"timestamp_end\":\"0000-00-00 00:00:00.000000\",\"export_proto_seqno\":1,\"export_proto_version\":5,\"flows\":1,\"packets\":902,\"bytes\":393,\"flow_type\":\"NetFlow\"}";

        TestUtil.vertx().eventBus().send(EVENT_FLOW, new JsonObject(flow).put(EVENT_VOLUME_BYTES, flow.length()));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
        {
            if (!testContext.completed())
            {
                testContext.failNow("could not complete the task, reason: timed out.");
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Timeout(5000)
    void testFlowCollectorResponse(VertxTestContext testContext)
    {

        var cipherUtil = new CipherUtil();

        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                var entries = new JsonObject("{\"plugin.id\":\"500000-flow\",\"datastore.format\":\"1\",\"datastore.type\":\"12\",\"source\":\"127.0.0.1\",\"tos\":\"0 (Routine)\",\"flows\":1,\"source.ip\":\"************\",\"destination.ip\":\"*************\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":11,\"packets.per.sec\":0,\"volume.bytes.per.sec\":0,\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\":\"0 (Unknown)\",\"destination.port\":\"0 (Unknown)\",\"tcp.flags\":\"unknown\",\"destination.ip.as\":2159,\"destination.as\":0,\"source.as\":0,\"volume.bytes.per.packet\":0,\"protocol\":\"6 (Unknown)\",\"destination.threat\":\"no\",\"packets\":71,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.category\":\"flow\"}");

                if ((DatastoreConstants.PluginId.FLOW_EVENT.getName() + "-" + "flow").equalsIgnoreCase(event.getString(GlobalConstants.PLUGIN_ID)))
                {
                    testContext.verify(() ->
                    {

                        for (var entry : entries)
                        {
                            Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                        }
                    });

                    eventDBWriteTestConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        var flow = new JsonObject("{\"tos\":\"0 (Routine)\",\"flows\":1,\"event.volume.bytes\":526,\"source.ip\":\"************\",\"destination.ip\":\"*************\",\"egress.volume.bytes\":0,\"ingress.volume.bytes\":0,\"ingress.packets\":0,\"egress.packets\":0,\"volume.bytes\":11,\"packets.per.sec\":0.0,\"volume.bytes.per.sec\":0.0,\"application\":\"Apache HTTP\",\"source.if.index\":\"interface-index-0\",\"destination.if.index\":\"interface-index-0\",\"source.port\":\"0 (Unknown)\",\"destination.port\":\"0 (Unknown)\",\"tcp.flags\":\"unknown\",\"destination.ip.as\":2159,\"destination.as\":0,\"source.as\":0,\"volume.bytes.per.packet\":0.0,\"protocol\":\"6 (Unknown)\",\"source.country\":\"India\",\"source.city\":\"Ahmedabad\",\"source.isp\":\"unknown\",\"source.asn\":\"80\",\"source.aso\":\"Oxford University\",\"source.threat\":\"no\",\"destination.country\":\"unknown\",\"destination.city\":\"unknown\",\"destination.isp\":\"unknown\",\"destination.domain\":\"unknown\",\"destination.asn\":\"unknown\",\"destination.aso\":\"unknown\",\"destination.threat\":\"no\",\"packets\":71,\"duration\":0,\"original.source.port\":40,\"original.destination.port\":80,\"event.timestamp\":1705980901,\"event.source\":\"127.0.0.1\",\"plugin.id\":500000}").
                put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_FLOW).
                put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, "collectorUUID");

        FORWARDER.send(Buffer.buffer().appendShortLE(CommonUtil.getShort((EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + " ").length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + " ").appendBytes(cipherUtil.encrypt(CodecUtil.compress(flow.encode()))).getBytes());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testColumnMapperEntries(VertxTestContext testContext)
    {
        TestAPIUtil.get(MISC_COLUMN_MAPPER_API_ENDPOINT + "?filter=" + new JsonObject().put(VisualizationConstants.VISUALIZATION_GROUP_TYPE, VisualizationConstants.VisualizationDataSource.FLOW.getName()),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertNotNull(body.getJsonObject(GlobalConstants.RESULT));

                            for (var entry : body.getJsonObject("result"))
                            {
                                Assertions.assertFalse(entry.getKey().contains("_"));
                            }

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 15000L)
    @Order(14)
    void testFlowCacheProcessor(VertxTestContext testContext)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if ((DatastoreConstants.PluginId.FLOW_EVENT.getName() + "-" + "flow").equalsIgnoreCase(event.getString(GlobalConstants.PLUGIN_ID)))
                {
                    LOGGER.info("testFlowCacheProcessor: " + event);

                    Assertions.assertEquals("*************", event.getString("source"));

                    Assertions.assertEquals("flow", event.getString("event.category"));

                    eventDBWriteTestConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().fileSystem().exists(FLOW_CACHE_DIR_PATH, result ->
        {

            if (result.succeeded() && !result.result())
            {
                TestUtil.vertx().fileSystem().mkdirs(FLOW_CACHE_DIR_PATH, asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        LOGGER.info("flow-cache directory created successfully... ");

                        LOGGER.info("file: " + DateTime.now().minusMinutes(10).toString(TIME_FORMATTER));
                        TestUtil.vertx().fileSystem().writeFileBlocking(FLOW_CACHE_DIR_PATH + GlobalConstants.PATH_SEPARATOR + DateTime.now().minusMinutes(10).toString(TIME_FORMATTER), Buffer.buffer("""
                                {"event_type": "purge", "tag": 1, "class": "indeed-com", "peer_ip_src": "*************", "peer_ip_dst": "*******", "iface_in": 7, "iface_out": 1, "ip_src": "*************", "ip_dst": "***********", "port_src": 46787, "port_dst": 214, "tcp_flags": "0", "ip_proto": "6", "tos": 0, "sampling_rate": 0, "export_proto_version": 9, "stamp_inserted": "2025-03-22 12:24:00", "stamp_updated": "2025-03-22 12:24:09", "flows": 1, "packets": 10, "bytes": 3000}"""));

                        LOGGER.info("file: " + DateTime.now().minusMinutes(15).toString(TIME_FORMATTER));
                        TestUtil.vertx().fileSystem().writeFileBlocking(FLOW_CACHE_DIR_PATH + GlobalConstants.PATH_SEPARATOR + DateTime.now().minusMinutes(15).toString(TIME_FORMATTER), Buffer.buffer("""
                                {"event_type": "purge", "tag": 1, "class": "ms-wbt", "peer_ip_src": "*************", "peer_ip_dst": "*******", "iface_in": 4, "iface_out": 1, "ip_src": "**************", "ip_dst": "************", "port_src": 46787, "port_dst": 300, "tcp_flags": "0", "ip_proto": "17", "tos": 0, "sampling_rate": 0, "export_proto_version": 9, "stamp_inserted": "2025-03-22 12:24:00", "stamp_updated": "2025-03-22 12:24:09", "flows": 1, "packets": 10, "bytes": 3000}"""));

                        LOGGER.info("files are created successfully...");
                    }
                    else
                    {
                        LOGGER.warn("Failed to create directory: " + asyncResult.cause().getMessage());
                    }
                });
            }
            else
            {
                LOGGER.info("flow-cache directory is already available, creating the file...");

                TestUtil.vertx().fileSystem().writeFileBlocking(FLOW_CACHE_DIR_PATH + GlobalConstants.PATH_SEPARATOR + DateTime.now().minusMinutes(5).toString(TIME_FORMATTER), Buffer.buffer("""
                        {"event_type": "purge", "tag": 1, "class": "indeed-com", "peer_ip_src": "*************", "peer_ip_dst": "*******", "iface_in": 7, "iface_out": 1, "ip_src": "*************", "ip_dst": "***********", "port_src": 46787, "port_dst": 214, "tcp_flags": "0", "ip_proto": "6", "tos": 0, "sampling_rate": 0, "export_proto_version": 9, "stamp_inserted": "2025-03-22 12:24:00", "stamp_updated": "2025-03-22 12:24:09", "flows": 1, "packets": 10, "bytes": 3000}"""));

                TestUtil.vertx().fileSystem().writeFileBlocking(FLOW_CACHE_DIR_PATH + GlobalConstants.PATH_SEPARATOR + DateTime.now().minusMinutes(8).toString(TIME_FORMATTER), Buffer.buffer("""
                        {"event_type": "purge", "tag": 1, "class": "ms-wbt", "peer_ip_src": "*************", "peer_ip_dst": "*******", "iface_in": 4, "iface_out": 1, "ip_src": "**************", "ip_dst": "************", "port_src": 46787, "port_dst": 300, "tcp_flags": "0", "ip_proto": "17", "tos": 0, "sampling_rate": 0, "export_proto_version": 9, "stamp_inserted": "2025-03-22 12:24:00", "stamp_updated": "2025-03-22 12:24:09", "flows": 1, "packets": 10, "bytes": 3000}"""));

            }
        });
    }

    private void assertFlowProcessorTestResult(VertxTestContext testContext, JsonObject entries)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            try
            {
                var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if ((DatastoreConstants.PluginId.FLOW_EVENT.getName() + "-" + "flow").equalsIgnoreCase(event.getString(GlobalConstants.PLUGIN_ID)))
                {
                    testContext.verify(() ->
                    {
                        LOGGER.info("event: " + event.encodePrettily());

                        LOGGER.info("entry: " + entries.encodePrettily());

                        for (var entry : entries)
                        {
                            Assertions.assertTrue(event.containsKey(entry.getKey()) && CommonUtil.getString(entry.getValue()).equalsIgnoreCase(event.getString(entry.getKey())));
                        }

                        Assertions.assertFalse(event.containsKey(EventBusConstants.EVENT_COPY_REQUIRED));

                    });

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }
}
