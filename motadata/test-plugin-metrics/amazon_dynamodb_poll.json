{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500042, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon DynamoDB", "metric.object": ***************, "metric.plugin": "amazondynamodb", "metric.polling.min.time": 300, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Amazon DynamoDB", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.561 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 42, "object.name": "testdb(us-west-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "motadata8-dynamoDB(ap-south-1)", "object.type": "Amazon DynamoDB", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 6, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"6": {"metadata.fields": {"env": "test"}}, "aws.dynamodb.account.max.reads": 80000, "aws.dynamodb.account.max.table.reads": 40000, "aws.dynamodb.account.max.table.writes": 40000, "aws.dynamodb.account.max.writes": 80000, "aws.dynamodb.amazon.resource.name": "arn:aws:dynamodb:ap-south-1:************:table/motadata8-dynamoDB", "aws.dynamodb.consumed.reads.per.sec": 0, "aws.dynamodb.db.name": "motadata8-dynamoDB", "aws.dynamodb.decreased.throughput": 0, "aws.dynamodb.index": [{"aws.dynamodb.index": "user-index", "aws.dynamodb.index.arn": "arn:aws:dynamodb:ap-south-1:************:table/motadata8-dynamoDB/index/user-index", "aws.dynamodb.index.decreased.throughput": 0, "aws.dynamodb.index.items": 0, "aws.dynamodb.index.reads.per.sec": 1, "aws.dynamodb.index.status": "ACTIVE", "aws.dynamodb.index.writes.per.sec": 1}, {"aws.dynamodb.index": "test-2nd-1-index", "aws.dynamodb.index.arn": "arn:aws:dynamodb:ap-south-1:************:table/motadata8-dynamoDB/index/test-2nd-1-index", "aws.dynamodb.index.decreased.throughput": 0, "aws.dynamodb.index.items": 0, "aws.dynamodb.index.reads.per.sec": 1, "aws.dynamodb.index.status": "ACTIVE", "aws.dynamodb.index.writes.per.sec": 1}], "aws.dynamodb.index.consumed.writes.per.sec": 0, "aws.dynamodb.items": 2, "aws.dynamodb.last.decreased.time": " 819 days 4 hours 14 minutes 28 seconds", "aws.dynamodb.provisioned.read.used.percent": 0, "aws.dynamodb.provisioned.reads.per.sec": 1, "aws.dynamodb.provisioned.table.read.used.percent": 0, "aws.dynamodb.provisioned.table.write.used.percent": 0, "aws.dynamodb.provisioned.write.used.percent": 0, "aws.dynamodb.provisioned.writes.per.sec": 1, "aws.dynamodb.read.throttled.requests": 0, "aws.dynamodb.reads.per.sec": 0, "aws.dynamodb.region": "ap-south-1", "aws.dynamodb.stream.status": "ENABLED", "aws.dynamodb.table.size.bytes": 24, "aws.dynamodb.throttled.requests": 0, "aws.dynamodb.write.throttled.requests": 0, "aws.dynamodb.writes.per.sec": 0, "aws.instance.creation.time": " 819 days 4 hours 15 minutes 34 seconds", "aws.instance.creation.time.seconds": 70776934, "aws.status": "ACTIVE", "status": "Up"}, "status": "succeed", "timeout": 60}}