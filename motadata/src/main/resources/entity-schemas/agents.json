{"entity": "Agent", "table": "tbl_config_agent", "version": "1.0", "author": "<PERSON>", "props": [{"name": "agent.id", "title": "ID", "type": "string", "rules": ["unique"]}, {"name": "agent.os.name", "title": "OS", "type": "string", "rules": ["required"]}, {"name": "agent.version", "title": "Version", "type": "string", "rules": ["required"]}, {"name": "agent.configs", "title": "Configs", "type": "string", "rules": ["required"]}, {"name": "agent.status.flap.counter", "title": "Status Flap Counter", "type": "numeric"}, {"name": "agent.business.hour.profile", "title": "Monitoring Hours", "type": "numeric"}, {"name": "agent.status.type", "title": "Status Type", "type": "string", "rules": ["required"], "values": ["Heartbeat", "<PERSON>"]}, {"name": "agent.status", "title": "Status", "type": "string"}]}