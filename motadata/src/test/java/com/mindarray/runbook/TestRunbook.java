/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.runbook;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.RunbookPlugin.*;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PASSWORD;
import static com.mindarray.db.DBConstants.FIELD_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.policy.PolicyEngineConstants.RUNBOOK_WORKLOG_RESULT;
import static com.mindarray.util.CronExpressionUtil.CRON_ONCE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(150 * 1000)

@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestRunbook
{

    private static final Logger LOGGER = new Logger(TestRunbook.class, MOTADATA_RUNBOOK, "Runbook Test");

    private static final JsonObject CONTEXTS = new JsonObject();
    private static final String PATTERN = "dd-MM-yyyy";
    private static final long entityId = 0L;
    private static MessageConsumer<JsonObject> messageConsumer;

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        object.getJsonObject(AIOpsObject.OBJECT_CONTEXT).put(NMSConstants.PING_CHECK_STATUS, YES);

        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                object, GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handler -> ObjectConfigStore.getStore().updateItem(object.getLong(ID)).onComplete(result -> promise.complete()));

        if (entityId > 0L)
        {
            ObjectStatusCacheStore.getStore().updateItem(entityId, STATUS_UP, DateTimeUtil.currentSeconds());
        }

        for (var index = 0; index < 200; index++)
        {
            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, CONTEXTS.getLong("ping.runbook")).put(SESSION_ID, TestUtil.getSessionId()));

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, CONTEXTS.getLong("snmp.runbook")).put(SESSION_ID, TestUtil.getSessionId()));

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, CONTEXTS.getLong("ssh.runbook")).put(SESSION_ID, TestUtil.getSessionId()));

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, CONTEXTS.getLong("powershell.cpu.runbook")).put(SESSION_ID, TestUtil.getSessionId()));
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateSSHRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

        assertCreateTestResult(context, testContext, "ssh.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetSSHRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(RUNBOOK_PLUGIN_API_ENDPOINT + CONTEXTS.getLong("ssh.runbook"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXTS.getLong("ssh.runbook"), RunbookPluginConfigStore.getStore(), new JsonArray().add(NMSConstants.STATE).add(RunbookPlugin.RUNBOOK_SCHEDULER), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateSSHRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES, new JsonObject().put("$$$count$$$", 10));

        TestAPIUtil.put(RUNBOOK_PLUGIN_API_ENDPOINT + CONTEXTS.getLong("ssh.runbook"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(RunbookPluginConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "Runbook Plugin"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreatePowershellAvailabilityRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

        assertCreateTestResult(context, testContext, "powershell.availability.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateCustomPowershellTopNCPURunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

        assertCreateTestResult(context, testContext, "powershell.cpu.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testCreateCustomPowershellTopNMemoryRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

        assertCreateTestResult(context, testContext, "powershell.memory.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateSNMPRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertCreateTestResult(context, testContext, "snmp.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateJDBCRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertCreateTestResult(context, testContext, "jdbc.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateTraceRouteRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertCreateTestResult(context, testContext, "traceroute.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreatePingRunbookPluginHavingGroupObjects(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams("testCreatePingRunbookPlugin").copy();

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        context.put(RUNBOOK_PLUGIN_ENTITY_TYPE, APIConstants.Entity.GROUP.getName()).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, GroupConfigStore.getStore().flatItems(Group.FIELD_GROUP_NAME, "Network", ID));

        assertCreateTestResult(context, testContext, "ping.group.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testAssignRunbookPlugin(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("traceroute.runbook"));

        Assertions.assertNotNull(context);

        Assertions.assertTrue(context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

        var entities = context.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES);

        assertAssignObjectTestResult(testContext, entities, ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.LINUX.getName(), ID), "traceroute.runbook");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testAssignRunbookPluginHavingGroup(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("ping.group.runbook"));

        Assertions.assertNotNull(context);

        Assertions.assertTrue(context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

        var entities = context.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES);

        var assignedGroups = GroupConfigStore.getStore().flatItems(Group.FIELD_GROUP_NAME, "Network", ID);

        Assertions.assertFalse(assignedGroups.isEmpty());

        var groups = GroupConfigStore.getStore().getIds();

        assignedGroups.forEach(id -> groups.remove(CommonUtil.getLong(id)));

        assertAssignObjectTestResult(testContext, entities, groups, "ping.group.runbook");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreatePingRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertCreateTestResult(context, testContext, "ping.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetAllRunbookPlugins(VertxTestContext testContext)
    {
        var filters = new JsonArray().add(NMSConstants.STATE).add(RunbookPlugin.RUNBOOK_SCHEDULER);

        TestAPIUtil.get(RUNBOOK_PLUGIN_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var entities = body.getJsonArray(RESULT);

            Assertions.assertNotNull(entities);

            assertFalse(entities.isEmpty());

            var items = RunbookPluginConfigStore.getStore().getItems();

            Assertions.assertNotNull(items);

            assertFalse(items.isEmpty());

            var filteredItems = new JsonArray();

            for (var index = 0; index < entities.size(); index++)
            {
                var entity = entities.getJsonObject(index);

                entity.remove(ENTITY_PROPERTY_COUNT);

                entity.remove(RUNBOOK_RUNNABLE);

                entity.remove(DBConstants.FIELD_TYPE);

                if (filters != null)
                {
                    entity.getMap().keySet().removeIf(filters::contains);
                }
                filteredItems.add(CommonUtil.removeSensitiveFields(entity, true));
            }

            var records = new JsonArray();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.remove(ENTITY_PROPERTY_COUNT);

                item.remove(DBConstants.FIELD_TYPE);

                records.add(CommonUtil.removeSensitiveFields(item, true));
            }

            Assertions.assertEquals(filteredItems.stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getLong(ID), Collectors.toList()))
                    , records.stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getLong(ID), Collectors.toList())));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Timeout(3 * 1000 * 60)
    @Order(16)
    void testRunbookInvalidTarget(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testCreatePowershellAvailabilityRunbookPlugin");

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_FAIL, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, ErrorMessageConstants.CONNECTION_FAILED), ErrorCodes.ERROR_CODE_RUNBOOK_PLUGIN_TEST, testContext, context, STATUS_UP);
    }


    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testRunbookEventSourceEntityType(VertxTestContext testContext)
    {
        var context = TestConstants.prepareParams("testCreateSSHRunbookPlugin");

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var key_ = context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.CUSTOM_SCRIPT.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;

        var item_ = context.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

        LOGGER.info("File exist for testCreateSSHRunbookPlugin: " + TestUtil.vertx().fileSystem().existsBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + RUNBOOK_DIR + GlobalConstants.PATH_SEPARATOR + item_.getString(key_)));

        var item = context.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

        item.put(RUNBOOK_PLUGIN_CREDENTIAL_PROFILE, CredentialProfileConfigStore.getStore().getItemByValue("credential.profile.name", "ssh-************-range").getLong(ID));

        LOGGER.info("Credential exist for testCreateSSHRunbookPlugin: " + item.getLong(RUNBOOK_PLUGIN_CREDENTIAL_PROFILE));

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add("************")).put(EVENT_ID, CommonUtil.newEventId()).put(RUNBOOK_PLUGIN_ENTITY_TYPE, EVENT_SOURCE).put(EVENT_SOURCE, "************");

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testRunPingRunbookEmptyObjects(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("ping.runbook"));

        Assertions.assertNotNull(context);

        Assertions.assertFalse(context.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(12345L)).put(SESSION_ID, TestUtil.getSessionId());

        assertRunbookRunEventTestResult(testContext, context, STATUS_FAIL, String.format("failed to run runbook %s, reason: object(s) not qualified", context.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)), ErrorCodes.ERROR_CODE_INTERNAL_ERROR);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testRunSNMPRunbookEmptyObjects(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("snmp.runbook"));

        Assertions.assertNotNull(context);

        Assertions.assertFalse(context.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(12345L)).put(SESSION_ID, TestUtil.getSessionId());

        assertRunbookRunEventTestResult(testContext, context, STATUS_FAIL, String.format("failed to run runbook %s, reason: object(s) not qualified", context.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)), ErrorCodes.ERROR_CODE_INTERNAL_ERROR);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testRunSNMPRunbookScript(VertxTestContext testContext)
    {
        if (entityId > 0L)
        {
            ObjectStatusCacheStore.getStore().updateItem(entityId, STATUS_UP, DateTimeUtil.currentSeconds());
        }

        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("snmp.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testRunDefaultSNMPRunbookScript(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(10000000000003L);

        Assertions.assertNotNull(context);

        Assertions.assertFalse(context.isEmpty());

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "***********", ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertRunbookRunEventTestResult(testContext, context, STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testRunSSHRunbookScript(VertxTestContext testContext)
    {
        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("ssh.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testRunSSHRunbookScriptValidateResult(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        Bootstrap.vertx().eventBus().<byte[]>localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message ->
        {
            var event = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

            if (event.getString(GlobalConstants.PLUGIN_ID).equalsIgnoreCase(DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName() + "-runbook.worklog"))
            {
                assertEquals(CommonUtil.getString(DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal()), event.getString(DatastoreConstants.DATASTORE_TYPE));

                Assertions.assertTrue(event.containsKey(RUNBOOK_WORKLOG_RESULT));

                testContext.completeNow();
            }
        });

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, CONTEXTS.getLong("ssh.runbook")).put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    @Timeout(value = 122, timeUnit = TimeUnit.SECONDS)
    @Disabled
    void testRunPowershellCPURunbookScript(VertxTestContext testContext)
    {
        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("powershell.cpu.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    @Timeout(value = 122, timeUnit = TimeUnit.SECONDS)
    @Disabled
    void testRunPowershellMemoryRunbookScript(VertxTestContext testContext)
    {
        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("powershell.memory.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testRunPingRunbookScript(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        object.getJsonObject(AIOpsObject.OBJECT_CONTEXT).put(NMSConstants.PING_CHECK_STATUS, YES);

        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                object, GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handler -> ObjectConfigStore.getStore().updateItem(object.getLong(ID)).onComplete(result -> promise.complete()));

        promise.future().onComplete(result ->
                assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("ping.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testRunRunbookScriptInvalidScript(VertxTestContext testContext)
    {
        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, 1234L).put(SESSION_ID, TestUtil.getSessionId()), STATUS_ABORT, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.RUNBOOK_PLUGIN.getName())), ErrorCodes.ERROR_CODE_BAD_REQUEST);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    @Timeout(value = 70, timeUnit = TimeUnit.SECONDS)
    void testRunPingRunbookScriptHavingPingDisabled(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132");

        object.getJsonObject(AIOpsObject.OBJECT_CONTEXT).put(NMSConstants.PING_CHECK_STATUS, NO);

        LOGGER.warn("testRunPingRunbookScriptHavingPingDisabled: Object: " + object.encodePrettily());

        Assertions.assertNotNull(object);

        ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_UP, DateTimeUtil.currentSeconds());

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                object,
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, handler -> ObjectConfigStore.getStore().updateItem(object.getLong(ID)).onComplete(asyncResult -> assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("ping.runbook")).put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testRunPingRunbookScriptHavingGroup(VertxTestContext testContext) throws Exception
    {
        var context = new JsonObject().put(ID, CONTEXTS.getLong("ping.group.runbook")).put(SESSION_ID, TestUtil.getSessionId());

        Assertions.assertNotNull(context);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, context);

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testUnassignEntities(VertxTestContext testContext)
    {
        var item = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("traceroute.runbook"));

        Assertions.assertNotNull(item);

        Assertions.assertTrue(item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

        var entities = item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES);

        Assertions.assertFalse(entities.isEmpty());

        var objects = new JsonArray().add(entities.remove(0));

        assertUnassignObjectTestResult(testContext, objects, entities, "traceroute.runbook");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testUnassignGroupEntities(VertxTestContext testContext)
    {
        var item = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("ping.group.runbook"));

        Assertions.assertNotNull(item);

        Assertions.assertTrue(item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

        var entities = item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES);

        Assertions.assertFalse(entities.isEmpty());

        var objects = new JsonArray().add(entities.remove(0));

        assertUnassignObjectTestResult(testContext, objects, entities, "ping.group.runbook");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testCreateRunbookScheduler(VertxTestContext testContext)
    {
        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.RUNBOOK.getName())
                .put(Scheduler.SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(OBJECTS, new JsonArray().add(CONTEXTS.getLong("ping.runbook")))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    CONTEXTS.put("scheduler.runbook", response.bodyAsJsonObject().getJsonArray(ID).getLong(0));

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testGetRunbookScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SCHEDULER_API_ENDPOINT + "?filter=" + new JsonObject().put(ID, CONTEXTS.getLong("ping.runbook"))
                        .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.RUNBOOK.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testUpdateRunbookScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date()))
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("10:00"))
                .put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.RUNBOOK.getName())
                .put(Scheduler.SCHEDULER_CONTEXT, new JsonObject().put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>"))
                        .put(OBJECTS, new JsonArray().add(CONTEXTS.getLong("ping.runbook")))
                        .put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")));

        TestAPIUtil.put(SCHEDULER_API_ENDPOINT + "/" + CONTEXTS.getLong("scheduler.runbook"), scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertUpdateEntityTestResult(SchedulerConfigStore.getStore(), scheduler, response.bodyAsJsonObject(),
                                            String.format(InfoMessageConstants.ENTITY_UPDATED, "Scheduler"), LOGGER, testInfo.getTestMethod().get().getName());

                                    testContext.completeNow();
                                }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testTriggerRunbookPluginScheduler(VertxTestContext testContext) throws Exception
    {
        var item = SchedulerConfigStore.getStore().getItem(CONTEXTS.getLong("scheduler.runbook"));

        Assertions.assertNotNull(item);

        TestUtil.vertx().eventBus().send(UI_ACTION_SCHEDULER_RUN, item.put(EVENT_SCHEDULER, item.getLong(ID)).put(SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, DEFAULT_USER));

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testDeleteRunbookScheduler(VertxTestContext testContext)
    {
        TestAPIUtil.delete(SCHEDULER_API_ENDPOINT + "/" + CONTEXTS.getLong("scheduler.runbook") + "?" + SCHEDULER_JOB_TYPE + "=" + JobScheduler.JobType.RUNBOOK.getName(),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "Scheduler"));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testDeleteRunbookPluginEntityInUsed(VertxTestContext testContext)
    {
        TestAPIUtil.delete(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong("snmp.runbook"), testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(HttpStatus.SC_BAD_REQUEST, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_FAIL, response.bodyAsJsonObject().getString(STATUS));

            assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, "Runbook Plugin"), response.bodyAsJsonObject().getString(MESSAGE));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testUnassignAllEntities(VertxTestContext testContext)
    {
        var item = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("snmp.runbook"));

        Assertions.assertNotNull(item);

        Assertions.assertTrue(item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

        var entities = item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES);

        Assertions.assertFalse(entities.isEmpty());

        var iterator = entities.iterator();

        var objects = new JsonArray();

        while (iterator.hasNext())
        {
            objects.add(CommonUtil.getLong(iterator.next()));

            iterator.remove();
        }

        assertUnassignObjectTestResult(testContext, objects, entities, "snmp.runbook");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testDeleteRunbookPlugin(VertxTestContext testContext)
    {
        TestAPIUtil.delete(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong("snmp.runbook"), testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(HttpStatus.SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

            assertEquals(String.format(InfoMessageConstants.ENTITY_DELETED, "Runbook Plugin"), response.bodyAsJsonObject().getString(MESSAGE));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testAssertObjectTypesByRunbookPluginType(VertxTestContext testContext)
    {
        var context = Runbook.getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType.SNMP);

        Assertions.assertFalse(context.isEmpty());

        Assertions.assertTrue(context.contains(NMSConstants.Type.SNMP_DEVICE.getName()));

        Assertions.assertTrue(context.contains(NMSConstants.Type.CISCO_WIRELESS.getName()));

        context = Runbook.getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType.SSH_SCRIPT);

        Assertions.assertFalse(context.isEmpty());

        Assertions.assertTrue(context.contains(NMSConstants.Type.LINUX.getName()));

        Assertions.assertTrue(context.contains(NMSConstants.Type.SOLARIS.getName()));

        context = Runbook.getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType.POWERSHELL_SCRIPT);

        Assertions.assertFalse(context.isEmpty());

        Assertions.assertTrue(context.contains(NMSConstants.Type.WINDOWS.getName()));

        Assertions.assertTrue(context.contains(NMSConstants.Type.HYPER_V.getName()));

        context = Runbook.getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType.DATABASE_SCRIPT);

        Assertions.assertFalse(context.isEmpty());

        Assertions.assertTrue(context.contains(NMSConstants.Type.WINDOWS.getName()));

        Assertions.assertTrue(context.contains(NMSConstants.Type.LINUX.getName()));

        context = Runbook.getObjectTypesByRunbookPluginType(Runbook.RunbookPluginType.PING);

        Assertions.assertNull(context);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testRunbookStateChangeEvent(VertxTestContext testContext)
    {
        var context = RunbookPluginConfigStore.getStore().getItem(CONTEXTS.getLong("ssh.runbook"));

        Assertions.assertNotNull(context);

        Assertions.assertFalse(context.isEmpty());

        context.put(SESSION_ID, TestUtil.getSessionId()).put(EVENT_SCHEDULER, 12345L);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_RUNBOOK_STATE_CHANGE))
                {
                    testContext.verify(() ->
                    {
                        Assertions.assertTrue(eventContext.containsKey(ID));

                        Assertions.assertEquals(eventContext.getLong(ID), context.getLong(ID));

                        Assertions.assertTrue(eventContext.containsKey(NMSConstants.STATE));

                        Assertions.assertEquals(NMSConstants.STATE_RUNNING, eventContext.getString(NMSConstants.STATE));

                        messageConsumer.unregister(result -> testContext.completeNow());
                    });
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().eventBus().send(EVENT_RUNBOOK, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testCreateHTTPRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, context.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) + "-" + System.currentTimeMillis());

        var object = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.VCENTER.getName(), ID);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, object);

        assertCreateTestResult(context, testContext, "http.runbook", testInfo);
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testHPUXTopProcessByCPURunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.HP_UX.getName(), ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testHPUXTopProcessByMemoryRunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.HP_UX.getName(), ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testIBMAIXTopProcessByCPURunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.IBM_AIX.getName(), ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testIBMAIXTopProcessByMemoryRunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.IBM_AIX.getName(), ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testSolarisTopProcessByCPURunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testSolarisTopProcessByMemoryRunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testLinuxTopProcessByMemoryRunbookScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(ID, objects.getLong(0)).put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(objects.getLong(0))).put(EVENT_ID, CommonUtil.newEventId());

        assertTestResult(objects.getLong(0), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testContext, context, STATUS_UP);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testGetRunbookPluginReferencesHavingGroups(VertxTestContext testContext)
    {
        // group runbook plugin references
        TestAPIUtil.get(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong("ping.group.runbook") + "/references",
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    Assertions.assertNotNull(body.getJsonObject(RESULT));

                    body = body.getJsonObject(RESULT);

                    Assertions.assertTrue(body.containsKey(APIConstants.Entity.GROUP.getName()));

                    Assertions.assertFalse(body.getJsonArray(APIConstants.Entity.GROUP.getName()).isEmpty());

                    testContext.completeNow();
                })));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testDeleteRemoteEventCollector(VertxTestContext testContext)
    {
        var remoteEventProcessor = RemoteEventProcessorConfigStore.getStore().getItems().stream().
                filter(item -> JsonObject.mapFrom(item).getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_USER)
                        && RemoteEventProcessorCacheStore.getStore().getDuration(JsonObject.mapFrom(item).getLong(ID)) > DUMMY_ID)
                .map(item -> JsonObject.mapFrom(item).getLong(ID))
                .findFirst().orElse(0L);

        Assertions.assertTrue(remoteEventProcessor > 0L);

        Bootstrap.configDBService().delete(DBConstants.TBL_REMOTE_EVENT_PROCESSOR,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, remoteEventProcessor),
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                handler ->
                {
                    RemoteEventProcessorConfigStore.getStore().deleteItem(remoteEventProcessor);

                    var ids = ObjectConfigStore.getStore().flatItemsByValues(AIOpsObject.OBJECT_IP, new JsonArray().add("************").add("***********"), ID);

                    Assertions.assertFalse(ids.isEmpty());

                    var eventProcessor = RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).getLong(ID);

                    Bootstrap.configDBService().updateAll(DBConstants.TBL_OBJECT,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                            new JsonObject().put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(eventProcessor)),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            result -> ObjectConfigStore.getStore().updateItems(ids).onComplete(asyncResult -> testContext.completeNow()));
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testCreateRunbookPluginEntityTypeEventSource(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case " + testInfo.getTestMethod().get().getName());

        var payload = new JsonObject("{ \"runbook.plugin.name\": \"Linux Top 10 Processes-IP\", \"runbook.plugin.type\": \"SSH Script\", \"runbook.plugin.entity.type\": \"event.source\", \"runbook.plugin.entities\": [ \"************\" ], \"runbook.plugin.category\": \"Performance Monitoring\", \"runbook.plugin.context\": { \"runbook.plugin.credential.profile\": 61822526355, \"timeout\": 60, \"port\": 22, \"parsing.script\": \"/*\\n * Copyright (c) Motadata 2024.  All rights reserved.\\n */\\n\\npackage main\\n\\nimport (\\n\\t\\\"encoding/base64\\\"\\n\\t\\\"encoding/json\\\"\\n\\t\\\"fmt\\\"\\n\\t\\\"motadatasdk/consts\\\"\\n\\t. \\\"motadatasdk/globals\\\"\\n\\t\\\"motadatasdk/utils\\\"\\n\\t\\\"os\\\"\\n\\t\\\"regexp\\\"\\n)\\n\\n//\\\"command\\\": \\\"ps -eo fname,pid,user,pcpu,pmem,vsz,rss,etime,nlwp,args | sed '1 d'\\\", ALL Processes\\n\\nfunc main() {\\n\\n\\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\\n\\n\\tif err != nil {\\n\\n\\t\\tbytes, _ := json.Marshal(MotadataMap{\\n\\n\\t\\t\\tconsts.Status: consts.StatusFail,\\n\\n\\t\\t\\tconsts.Errors: []MotadataStringMap{\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeInternalError,\\n\\t\\t\\t\\t\\tconsts.Error:     fmt.Sprintf(\\\"%v\\\", err),\\n\\t\\t\\t\\t\\tconsts.Message:   \\\"Failed to load context\\\",\\n\\t\\t\\t\\t}},\\n\\t\\t})\\n\\n\\t\\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\\n\\n\\t} else {\\n\\n\\t\\tresult := make(MotadataMap)\\n\\n\\t\\trun(result, context)\\n\\n\\t\\tbytes, err := json.Marshal(result)\\n\\n\\t\\tif err != nil {\\n\\n\\t\\t\\tbytes, _ = json.Marshal(MotadataMap{\\n\\n\\t\\t\\t\\tconsts.Status: consts.StatusFail,\\n\\n\\t\\t\\t\\tconsts.Errors: []MotadataStringMap{\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeInternalError,\\n\\t\\t\\t\\t\\t\\tconsts.Error:     fmt.Sprintf(\\\"%v\\\", err),\\n\\t\\t\\t\\t\\t\\tconsts.Message:   \\\"Invalid Result\\\",\\n\\t\\t\\t\\t\\t}},\\n\\t\\t\\t})\\n\\t\\t}\\n\\n\\t\\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\\n\\t}\\n}\\n\\nfunc run(result, context MotadataMap) {\\n\\n\\toutput := context.GetMotadataStringValue(consts.Result)\\n\\n\\tvar processes []MotadataMap\\n\\n\\tresult[consts.Status] = consts.StatusFail\\n\\n\\tdefer func() {\\n\\n\\t\\tif r := recover(); r != nil {\\n\\n\\t\\t\\tresult[consts.Errors] = []MotadataStringMap{\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeInternalError,\\n\\t\\t\\t\\t\\tconsts.Message:   \\\"Invalid Result\\\",\\n\\t\\t\\t\\t\\tconsts.Error:     fmt.Sprintf(\\\"%v\\\", r),\\n\\t\\t\\t\\t},\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}()\\n\\n\\tif output.IsNotEmpty() {\\n\\n\\t\\tfor _, row := range output.Split(consts.NewLineSeparator) {\\n\\n\\t\\t\\ttokens := MotadataString(regexp.MustCompile(`\\\\s+`).ReplaceAllString(row.ToString(), consts.SpaceSeparator)).SplitN(consts.SpaceSeparator, 10)\\n\\n\\t\\t\\tif len(tokens) == 10 {\\n\\n\\t\\t\\t\\tprocess := make(MotadataMap)\\n\\n\\t\\t\\t\\tprocess[consts.Status] = consts.StatusUp\\n\\n\\t\\t\\t\\tprocess[consts.Process] = tokens[0].TrimSpace() + \\\"|\\\" + tokens[9].TrimSpace()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessId] = tokens[1].TrimSpace().ToInt()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessUser] = tokens[2].TrimSpace()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessCPUPercent] = tokens[3].TrimSpace().ToFloat64()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessMemoryPercent] = tokens[4].TrimSpace().ToFloat64()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessVirtualMemoryBytes] = MotadataKB(tokens[5].TrimSpace().ToFloat64()).ToBytes()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessMemoryBytes] = MotadataKB(tokens[6].TrimSpace().ToFloat64()).ToBytes()\\n\\n\\t\\t\\t\\tsetProcessUptimeMetrics(process, tokens[7].TrimSpace())\\n\\n\\t\\t\\t\\tprocess[consts.ProcessThreads] = tokens[8].TrimSpace().ToInt()\\n\\n\\t\\t\\t\\tprocess[consts.ProcessCommand] = tokens[9].TrimSpace()\\n\\n\\t\\t\\t\\tprocesses = append(processes, process)\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\tif IsNotEmptyMapSlice(processes) {\\n\\n\\t\\tresult[consts.Result] = make(MotadataMap)\\n\\n\\t\\tresult.GetMapValue(consts.Result)[consts.Process] = processes\\n\\n\\t\\tresult[consts.Status] = consts.StatusSucceed\\n\\n\\t} else {\\n\\n\\t\\tresult[consts.Errors] = []MotadataStringMap{\\n\\t\\t\\t{\\n\\t\\t\\t\\tconsts.ErrorCode: consts.ErrorCodeNoItemFound,\\n\\t\\t\\t\\tconsts.Message:   \\\"No Result found\\\",\\n\\t\\t\\t},\\n\\t\\t}\\n\\t}\\n}\\n\\nfunc setProcessUptimeMetrics(process MotadataMap, token MotadataString) {\\n\\n\\tdays := MotadataINT(0)\\n\\n\\thours := MotadataINT(0)\\n\\n\\tminutes := MotadataINT(0)\\n\\n\\tseconds := MotadataINT(0)\\n\\n\\tif token.Contains(\\\"-\\\") {\\n\\n\\t\\ttokens := token.Split(\\\"-\\\")\\n\\n\\t\\tdays = tokens[0].ToINT()\\n\\n\\t\\ttoken = tokens[1]\\n\\t}\\n\\n\\ttokens := token.Split(\\\":\\\")\\n\\n\\tif len(tokens) == 3 {\\n\\n\\t\\thours = tokens[0].ToINT()\\n\\n\\t\\tminutes = tokens[1].ToINT()\\n\\n\\t\\tseconds = tokens[2].ToINT()\\n\\n\\t} else if len(tokens) == 2 {\\n\\n\\t\\tminutes = tokens[0].ToINT()\\n\\n\\t\\tseconds = tokens[1].ToINT()\\n\\n\\t} else if len(tokens) == 1 {\\n\\n\\t\\tseconds = tokens[0].ToINT()\\n\\t}\\n\\n\\tprocess[consts.ProcessUptimeSeconds] = ((days*24+hours)*60+minutes)*60 + seconds\\n\\n\\tprocess[consts.ProcessUptime] = MotadataTime(process.GetINTValue(consts.ProcessUptimeSeconds)).ToString()\\n}\\n\", \"script.language\": \"go\", \"command\": \"ps -eo fname,pid,user,pcpu,pmem,vsz,rss,etime,nlwp,args --sort=-pcpu | head -10 | sed '1 d'\" } }");

        TestAPIUtil.post(RUNBOOK_PLUGIN_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(RunbookPluginConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.RUNBOOK_PLUGIN.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            CONTEXTS.put("ip-host", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testDeleteRunbookPluginEntityTypeEventSource(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case " + testInfo.getTestMethod().get().getName());

        TestAPIUtil.delete(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong("ip-host"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(RunbookPluginConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, Entity.RUNBOOK_PLUGIN.getName()));

            CONTEXTS.put("ip-host", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testCreatePostgresSQLDatabaseRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info("running test case " + testInfo.getTestMethod().get().getName());

            var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

            Assertions.assertNotNull(context);

            prepareTestContext(context);

            var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132", ID);

            Assertions.assertNotNull(objects);

            Assertions.assertFalse(objects.isEmpty());

            context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

            assertCreateTestResult(context, testContext, "database.runbook", testInfo);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testCreateSSHInstanceRunbookPlugin(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        Assertions.assertNotNull(context);

        prepareTestContext(context);

        var objects = ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "************", ID);

        Assertions.assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        context.put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, objects);

        assertCreateTestResult(context, testContext, "ssh.instance.runbook", testInfo);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testRunSSHInstanceRunbookScript(VertxTestContext testContext)
    {
        assertRunbookRunEventTestResult(testContext, new JsonObject().put(ID, CONTEXTS.getLong("ssh.instance.runbook")).put(INSTANCE, "java").put(SESSION_ID, TestUtil.getSessionId()), STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testGetRunbookPluginReferencesDataSecurity(VertxTestContext testContext)
    {
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "MinimalAccess");

        Assertions.assertNotNull(item);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                .put(USER_PASSWORD, item.getString(USER_PASSWORD)), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var response = asyncResult.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                // group runbook plugin references
                TestAPIUtil.get(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong("ping.group.runbook") + "/references",
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        testContext.succeeding(asyncResponse -> testContext.verify(() ->
                        {
                            LOGGER.info("testGetRunbookPluginReferencesDataSecurity : response: " + asyncResponse.bodyAsJsonObject().encode());

                            assertEquals(SC_OK, asyncResponse.statusCode());

                            var responseBody = asyncResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(responseBody);

                            assertEquals(SC_OK, responseBody.getInteger(RESPONSE_CODE));

                            Assertions.assertNotNull(responseBody.getJsonObject(RESULT));

                            responseBody = responseBody.getJsonObject(RESULT);

                            Assertions.assertTrue(responseBody.containsKey(APIConstants.Entity.GROUP.getName()));

                            Assertions.assertFalse(responseBody.getJsonArray(APIConstants.Entity.GROUP.getName()).isEmpty());

                            testContext.completeNow();
                        })));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                testContext.failNow(asyncResult.cause());
            }

        });
    }

    private void assertTestResult(long id, String status, String value, String errorCode, VertxTestContext testContext, JsonObject context, String objectStatus)
    {
        try
        {
            ObjectStatusCacheStore.getStore().updateItem(id, objectStatus, DateTimeUtil.currentSeconds());

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.getString(EVENT_TYPE) != null && (eventContext.getString(EVENT_TYPE).contains("-Runbook-") || eventContext.getString(EVENT_TYPE).contains("Runbook Plugin Test")) && eventContext.containsKey(STATUS))
                    {
                        testContext.verify(() ->
                        {
                            Assertions.assertEquals(eventContext.getString(STATUS), status != null ? status : STATUS_SUCCEED);

                            Assertions.assertEquals(eventContext.getString(ERROR_CODE), errorCode);

                            if (eventContext.getString(MESSAGE) != null)
                            {
                                Assertions.assertEquals(eventContext.getString(MESSAGE), value != null ? value : InfoMessageConstants.RUNBOOK_PLUGIN_TEST_SUCCEEDED);
                            }

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }
                }
                catch (Exception exception)
                {
                    testContext.failNow(exception);
                }
            });

            TestUtil.vertx().eventBus().send(UI_ACTION_RUNBOOK_PLUGIN_TEST, context.put(SESSION_ID, TestUtil.getSessionId()));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    private void assertCreateTestResult(JsonObject context, VertxTestContext testContext, String key, TestInfo testInfo)
    {
        context.put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, context.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME) + "-" + System.currentTimeMillis());

        TestAPIUtil.post(RUNBOOK_PLUGIN_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(RunbookPluginConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "Runbook Plugin"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXTS.put(key, response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    private void assertAssignObjectTestResult(VertxTestContext testContext, JsonArray entities, JsonArray objects, String key)
    {
        TestAPIUtil.put(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong(key) + "/assign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(HttpStatus.SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    var total = entities.size() + objects.size();

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, CONTEXTS.getLong(key), total);

                })));
    }

    private void assertUnassignObjectTestResult(VertxTestContext testContext, JsonArray objects, JsonArray entities, String key)
    {
        TestAPIUtil.put(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + CONTEXTS.getLong(key) + "/unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                    assertEquals(HttpStatus.SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                    testContext.awaitCompletion(1, TimeUnit.SECONDS);

                    assertAssignObjectTestResult(testContext, CONTEXTS.getLong(key), entities.size());

                })));
    }

    private void assertAssignObjectTestResult(VertxTestContext testContext, long id, int size)
    {
        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(1000, timer ->
        {
            retries.getAndIncrement();

            var item = RunbookPluginConfigStore.getStore().getItem(id);

            if (item == null)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("item not found"));
            }
            else
            {
                if (item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                    item.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
                }

                Assertions.assertNotNull(item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES));

                if (item.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).size() == size)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }

                if (retries.get() > 5)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow(new Exception("Runbook plugin entities not match..."));
                }
            }
        });
    }

    private void assertRunbookRunEventTestResult(VertxTestContext testContext, JsonObject context, String status, String message, String errorCode)
    {
        assertRunbookRunEventTestResult(testContext, context, status, message, errorCode, false);
    }

    private void assertRunbookRunEventTestResult(VertxTestContext testContext, JsonObject context, String status, String message, String errorCode, boolean instance)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK, context.put(EventBusConstants.EVENT_REPLY, YES),
                new DeliveryOptions().setSendTimeout(120000L), reply ->
                {
                    try
                    {
                        if (reply.failed())
                        {
                            LOGGER.warn("Handler failure: " + reply.cause().getMessage());

                            testContext.failNow(reply.cause());
                        }
                        var response = reply.result().body();

                        LOGGER.warn("Response: " + response.encodePrettily());

                        if (response.containsKey(STATUS) && !response.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)) // event failed assertions not contains event.reply.contexts
                        {
                            Assertions.assertEquals(response.getString(MESSAGE), message);

                            Assertions.assertEquals(response.getString(STATUS), status);

                            Assertions.assertEquals(response.getString(ERROR_CODE), errorCode);

                            testContext.completeNow();
                        }
                        else
                        {
                            Assertions.assertNotNull(response);

                            Assertions.assertTrue(response.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS));

                            Assertions.assertFalse(response.getJsonArray(EVENT_REPLY_CONTEXTS).isEmpty());

                            response = response.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0);

                            if (response.containsKey(MESSAGE))
                            {
                                Assertions.assertEquals(response.getString(MESSAGE), message);
                            }

                            Assertions.assertTrue(response.containsKey(STATUS));

                            Assertions.assertEquals(response.getString(STATUS), status);

                            Assertions.assertEquals(response.getString(ERROR_CODE), errorCode);

                            if (instance)
                            {
                                Assertions.assertTrue(response.containsKey(INSTANCE));
                            }

                            testContext.completeNow();
                        }
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception);
                    }
                });

    }

    private void prepareTestContext(JsonObject context)
    {
        var key = context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.CUSTOM_SCRIPT.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;

        var item = context.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

        if (item != null && item.getString(key) != null && !item.getString(key).isEmpty())
        {
            var file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + RUNBOOK_DIR + GlobalConstants.PATH_SEPARATOR +
                    item.getString(key);

            if (TestUtil.vertx().fileSystem().existsBlocking(file))
            {
                item.put(key, CommonUtil.getString(TestUtil.vertx().fileSystem().readFileBlocking(file)).trim());
            }
        }

    }
}
