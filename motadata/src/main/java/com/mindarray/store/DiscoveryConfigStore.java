/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Discovery;
import com.mindarray.db.DBConstants;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;

public class DiscoveryConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(DiscoveryConfigStore.class, GlobalConstants.MOTADATA_STORE, "Discovery Config Store");

    private static final DiscoveryConfigStore STORE = new DiscoveryConfigStore();

    private DiscoveryConfigStore()
    {
        super(DBConstants.TBL_DISCOVERY, LOGGER, false);
    }

    public static DiscoveryConfigStore getStore()
    {
        return STORE;
    }

    public String getDiscoveryName(long id)
    {
        return items.containsKey(id) ? items.get(id).getString(Discovery.DISCOVERY_NAME) : null;
    }

    public void markDiscoveryStatusAsRunning(long id)
    {
        items.get(id).put(Discovery.DISCOVERY_STATUS, String.format("Discovery is running, started at %s", DateTimeUtil.timestamp()));
    }
}
