{"entity": "Explorer", "table": "tbl_config_explorer", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "explorer.name", "title": "Explorer Name", "type": "string", "rules": ["required", "unique"]}, {"name": "explorer.description", "title": "Explorer Description", "type": "string"}, {"name": "explorer.type", "title": "Explorer Type", "type": "string", "rules": ["required"], "value": ["metric", "topology"]}, {"name": "explorer.access.type", "title": "Security", "type": "string", "rules": ["required"], "value": ["private", "public"]}, {"name": "explorer.object.type", "title": "Explorer Object Type", "type": "string"}, {"name": "explorer.object.id", "title": "Explorer Object id", "type": "numeric"}, {"name": "explorer.global.view.enabled", "title": "Explorer Global View Enable", "type": "string", "values": ["yes", "no"]}, {"name": "explorer.users", "title": "Users", "type": "list"}, {"name": "explorer.context", "title": "Explorer Context", "type": "map", "rules": ["required"]}]}