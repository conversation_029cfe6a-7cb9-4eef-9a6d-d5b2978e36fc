/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.*;

public class IntegrationProfile extends AbstractAPI
{
    public static final String INTEGRATION = "integration";
    public static final String INTEGRATION_PROFILE_CONTEXT = "integration.profile.context";
    public static final String INTEGRATION_PROFILE_NAME = "integration.profile.name";
    public static final String INTEGRATION_CATEGORY = "integration.category";
    public static final String INTEGRATION_CREDENTIAL_PROFILE = "integration.credential.profile";
    // integration profile context related constants
    public static final String AUTO_CLOSE_TICKET_STATUS = "auto.close.ticket.status";
    public static final String AUTO_CLOSE_TICKET_STATE = "auto.close.ticket.state";
    private static final Logger LOGGER = new Logger(IntegrationProfile.class, MOTADATA_API, "Integration Profile API");

    public IntegrationProfile()
    {
        super("integration-profiles", IntegrationProfileConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router);

        router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_INTEGRATION_PROFILE.name()).mergeIn(entity));

        return super.afterCreate(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_INTEGRATION_PROFILE.name()).mergeIn(entity));

        return super.afterUpdate(entity, routingContext);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_INTEGRATION_PROFILE.name()).mergeIn(entity));

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var item = IntegrationProfileConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var items = MetricPolicyConfigStore.getStore().getItems();

            var metricPolicies = new JsonArray();

            for (var index = 0; index < items.size(); index++)
            {
                var policy = items.getJsonObject(index);

                if (item != null && isProfileInUse(policy, item.getLong(ID)))
                {
                    metricPolicies.add(policy);
                }
            }

            if (!metricPolicies.isEmpty())
            {
                response.put(APIConstants.Entity.METRIC_POLICY.getName(), metricPolicies);
            }

            var eventPolicies = new JsonArray();

            items = EventPolicyConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var policy = items.getJsonObject(index);

                if (item != null && isProfileInUse(policy, item.getLong(ID)))
                {
                    eventPolicies.add(policy);
                }
            }

            if (!eventPolicies.isEmpty())
            {
                response.put(APIConstants.Entity.EVENT_POLICY.getName(), eventPolicies);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var item = IntegrationConfigStore.getStore().getItem(requestBody.getLong(INTEGRATION));

            //as of now there are two type of integration notification in which will be notifying users like slack,teams and incident ticket generations like serviceops,servicenow
            if (IntegrationConstants.NOTIFICATION_INTEGRATIONS.contains(item.getString(Integration.INTEGRATION_TYPE)))
            {
                requestBody.put(INTEGRATION_CATEGORY, IntegrationConstants.IntegrationCategory.NOTIFICATION.getName());
            }
            else
            {
                requestBody.put(INTEGRATION_CATEGORY, IntegrationConstants.IntegrationCategory.INCIDENT.getName());
            }

            promise.complete(requestBody);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail("Error configuring integrations");
        }

        return promise.future();

    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var items = IntegrationProfileConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var id = CommonUtil.getString(item.getLong(ID));

                var references = 0;

                var metricPolicies = MetricPolicyConfigStore.getStore().getItems();

                for (var i = 0; i < metricPolicies.size(); i++)
                {
                    var policy = metricPolicies.getJsonObject(i);

                    if (isProfileInUse(policy, item.getLong(ID)))
                    {
                        references++;
                    }
                }

                var eventPolicies = EventPolicyConfigStore.getStore().getItems();

                for (var i = 0; i < eventPolicies.size(); i++)
                {
                    var policy = eventPolicies.getJsonObject(i);

                    if (isProfileInUse(policy, item.getLong(ID)))
                    {
                        references++;
                    }
                }

                response.put(id, response.containsKey(id) ? response.getInteger(id) + references : references);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    private boolean isProfileInUse(JsonObject policy, long id)
    {
        var status = false;

        try
        {
            if (NO.equalsIgnoreCase(policy.getString(PolicyEngineConstants.POLICY_ARCHIVED, NO))
                    && policy.containsKey(PolicyEngineConstants.POLICY_ACTIONS)
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).isEmpty()
                    && policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.INTEGRATION.getName())
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.INTEGRATION.getName()).isEmpty())
            {
                var integration = policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.INTEGRATION.getName());

                for (var severity : Severity.values())
                {
                    if (integration.containsKey(severity.name()) && !integration.getJsonArray(severity.name()).isEmpty())
                    {
                        var items = integration.getJsonArray(severity.name());

                        for (var index = 0; index < items.size(); index++)
                        {
                            if (items.getJsonObject(index).containsKey(ID) && items.getJsonObject(index).getLong(ID).equals(id))
                            {
                                status = true;

                                break;
                            }
                        }
                    }

                    if (status)
                    {
                        break;
                    }
                }
            }

            if (NO.equalsIgnoreCase(policy.getString(PolicyEngineConstants.POLICY_ARCHIVED, NO))
                    && policy.containsKey(PolicyEngineConstants.POLICY_ACTIONS)
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).isEmpty()
                    && policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName())
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty()
                    && policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()).containsKey(PolicyEngineConstants.CHANNELS)
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(PolicyEngineConstants.CHANNELS).isEmpty())
            {

                var notifications = policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(PolicyEngineConstants.CHANNELS);

                for (var severity : Severity.values())
                {
                    if (notifications.containsKey(severity.name()) && !notifications.getJsonArray(severity.name()).isEmpty())
                    {
                        var items = notifications.getJsonArray(severity.name());

                        for (var index = 0; index < items.size(); index++)
                        {
                            if (items.getJsonObject(index).containsKey(ID) && items.getJsonObject(index).getLong(ID).equals(id))
                            {
                                status = true;

                                break;
                            }
                        }
                    }

                    if (status)
                    {
                        break;
                    }
                }
            }

            if (NO.equalsIgnoreCase(policy.getString(PolicyEngineConstants.POLICY_ARCHIVED, NO))
                    && policy.containsKey(PolicyEngineConstants.POLICY_ACTIONS)
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).isEmpty()
                    && policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.RENOTIFICATION.getName())
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty())
            {

                var renotification = policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RENOTIFICATION.getName());

                for (var severity : Severity.values())
                {
                    if (renotification.containsKey(severity.name()) && !renotification.getJsonObject(severity.name()).isEmpty())
                    {
                        var items = renotification.getJsonObject(severity.name()).getJsonArray(PolicyEngineConstants.RECIPIENTS);

                        for (var index = 0; index < items.size(); index++)
                        {
                            if (items.getJsonObject(index).getString(PolicyEngineConstants.RECIPIENT_TYPE).equalsIgnoreCase(PolicyEngineConstants.RecipientType.CHANNEL.getName()) && (items.getJsonObject(index).containsKey(ID) && items.getJsonObject(index).getLong(ID).equals(id)))
                            {
                                status = true;

                                break;
                            }
                        }
                    }

                    if (status)
                    {
                        break;
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return status;
    }
}
