{"127.0.0.1": {"220": {"metadata.fields": {"name": "cdntest", "type": "CDN"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 1**************, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure CDN", "metric.object": ***************, "metric.plugin": "azurecdn", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure CDN", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.738 pm 14/06/2022", "object.custom.fields": {"***************": "CDN", "***************": "cdntest"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 48, "object.name": "cdntest(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.state": "ENABLE", "object.target": "cdntest(cloud-shell-storage-centralindia)", "object.type": "Azure CDN", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 220, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"220": {"metadata.fields": {"name": "cdntest", "type": "CDN"}}, "azure.cdn.byte.hit.ratio.percent": 0, "azure.cdn.endpoints": 1, "azure.cdn.latency.ms": 0, "azure.cdn.requests": 0, "azure.cdn.response.bytes.rate": 0, "azure.location": "Global", "azure.name": "cdntest", "azure.status": "Succeeded", "azure.type": "Microsoft.Cdn/profiles"}, "status": "succeed", "timeout": 60}}