{"172.16.8.166": {"result": {"mysql.admin.commands": 5620.0, "mysql.analyze.commands": 0.0, "mysql.backup.table.commands": 0.0, "mysql.change.database.commands": 0.0, "mysql.change.master.commands": 0.0, "mysql.check.commands": 0.0, "mysql.create.database.commands": 0.0, "mysql.drop.database.commands": 0.0, "mysql.flush.commands.rate": 0.0, "mysql.grant.commands": 0.0, "mysql.kill.commands": 0.0, "mysql.optimize.commands": 0.0, "mysql.repair.commands": 0.0, "mysql.reset.commands": 0.0, "mysql.revoke.commands": 0.0, "mysql.alter.table.commands": 0.0, "mysql.create.function.commands": 0.0, "mysql.create.index.commands": 0.0, "mysql.create.table.commands": 0.0, "mysql.drop.function.commands": 0.0, "mysql.drop.index.commands": 0.0, "mysql.drop.table.commands": 0.0, "mysql.rename.table.commands": 0.0, "mysql.handler.close.commands": 0.0, "mysql.handler.open.commands": 0.0, "mysql.handler.read.commands": 0.0, "mysql.master.data.commands": 0.0, "mysql.set.option.commands": 5620.0, "mysql.slave.start.commands": 0.0, "mysql.slave.stop.commands": 0.0, "mysql.insert.select.commands": 0.0, "mysql.load.commands": 0.0, "mysql.load.master.table.commands": 0.0, "mysql.purge.commands": 0.0, "mysql.replace.commands": 0.0, "mysql.replace.select.commands": 0.0, "mysql.restore.table.commands": 0.0, "mysql.truncate.commands": 0.0, "mysql.show.binary.log.commands": 0.0, "mysql.show.binary.log.event.commands": 0.0, "mysql.show.database.commands": 0.0, "mysql.show.field.commands": 0.0, "mysql.show.grant.commands": 0.0, "mysql.show.key.commands": 0.0, "mysql.show.master.status.commands": 0.0, "mysql.show.new.master.commands": 0.0, "mysql.show.open.table.commands": 0.0, "mysql.show.processlist.commands": 538.0, "mysql.show.slave.host.commands": 0.0, "mysql.show.slave.status.commands": 0.0, "mysql.show.table.commands": 0.0, "mysql.show.variable.commands": 1673.0, "mysql.show.status.commands": 1826.0, "mysql.begin.transaction.commands.rate": 0.0, "mysql.commit.transaction.commands.rate": 0.0, "mysql.lock.table.commands": 0.0, "mysql.rollback.transaction.commands.rate": 0.0, "mysql.unlock.table.commands": 0.0}, "errors": []}, "************": {"result": {"mysql.admin.commands": 1551787.0, "mysql.analyze.commands": 0.0, "mysql.change.database.commands": 12.0, "mysql.change.master.commands": 0.0, "mysql.check.commands": 0.0, "mysql.create.database.commands": 1.0, "mysql.drop.database.commands": 0.0, "mysql.flush.commands.rate": 1.0, "mysql.grant.commands": 0.0, "mysql.kill.commands": 4.0, "mysql.optimize.commands": 0.0, "mysql.repair.commands": 0.0, "mysql.reset.commands": 0.0, "mysql.revoke.commands": 0.0, "mysql.alter.table.commands": 0.0, "mysql.create.function.commands": 0.0, "mysql.create.index.commands": 0.0, "mysql.create.table.commands": 35.0, "mysql.drop.function.commands": 0.0, "mysql.drop.index.commands": 0.0, "mysql.drop.table.commands": 0.0, "mysql.rename.table.commands": 0.0, "mysql.handler.close.commands": 0.0, "mysql.handler.open.commands": 0.0, "mysql.handler.read.commands": 0.0, "mysql.set.option.commands": 92124.0, "mysql.slave.start.commands": 0.0, "mysql.slave.stop.commands": 0.0, "mysql.insert.select.commands": 0.0, "mysql.load.commands": 0.0, "mysql.purge.commands": 0.0, "mysql.replace.commands": 0.0, "mysql.replace.select.commands": 0.0, "mysql.truncate.commands": 0.0, "mysql.show.binary.log.commands": 0.0, "mysql.show.binary.log.event.commands": 0.0, "mysql.show.database.commands": 16.0, "mysql.show.field.commands": 381.0, "mysql.show.grant.commands": 0.0, "mysql.show.key.commands": 0.0, "mysql.show.engine.log.commands": 0.0, "mysql.show.master.status.commands": 0.0, "mysql.show.open.table.commands": 0.0, "mysql.show.processlist.commands": 10720.0, "mysql.show.slave.host.commands": 0.0, "mysql.show.slave.status.commands": 0.0, "mysql.show.table.commands": 13.0, "mysql.show.variable.commands": 30855.0, "mysql.show.status.commands": 31957.0, "mysql.begin.transaction.commands.rate": 0.0, "mysql.commit.transaction.commands.rate": 0.0, "mysql.lock.table.commands": 0.0, "mysql.rollback.transaction.commands.rate": 0.0, "mysql.unlock.table.commands": 0.0}, "errors": []}}