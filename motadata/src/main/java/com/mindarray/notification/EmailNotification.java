/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.Bootstrap;
import com.mindarray.eventbus.EventBusConstants;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

/**
 * Implementation of the Notification abstract class for sending email notifications.
 * <p>
 * This class handles email notifications by delegating the actual email sending
 * to a dedicated email service via the Vert.x event bus. The email service is
 * responsible for:
 * <ul>
 *   <li>Connecting to the configured SMTP server</li>
 *   <li>Formatting the email content (HTML or plain text)</li>
 *   <li>Adding attachments if specified</li>
 *   <li>Sending the email to recipients</li>
 *   <li>Handling delivery status and errors</li>
 * </ul>
 * <p>
 * The notification event should contain the following fields:
 * <ul>
 *   <li>{@link Notification#EMAIL_NOTIFICATION_RECIPIENTS}: JsonArray of recipient email addresses</li>
 *   <li>{@link Notification#EMAIL_NOTIFICATION_SUBJECT}: Subject line of the email</li>
 *   <li>{@link Notification#EMAIL_NOTIFICATION_MESSAGE}: Content of the email</li>
 *   <li>{@link Notification#EMAIL_NOTIFICATION_SENDER} (optional): Sender email address</li>
 *   <li>{@link Notification#EMAIL_NOTIFICATION_ATTACHMENTS} (optional): Attachments to include</li>
 * </ul>
 */
class EmailNotification extends Notification
{

    /**
     * Processes and sends an email notification based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Forwards the notification event to the email service via the event bus</li>
     *   <li>Waits for the email service to process the notification</li>
     *   <li>Returns the result of the email delivery attempt</li>
     * </ul>
     *
     * @param event A JsonObject containing the email notification parameters
     *              (recipients, subject, message, etc.)
     * @return A Promise that resolves to a JsonObject containing the result of the
     * email notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        // Create a promise to be completed when the email notification is processed
        var promise = Promise.<JsonObject>promise();

        // Send the notification event to the email service via the event bus
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EMAIL_NOTIFICATION, event, reply ->
        {
            // If the email service successfully processed the notification
            if (reply.succeeded())
            {
                // Complete the promise with the result from the email service
                promise.complete(reply.result().body());
            }
            // If the email service encountered an error
            else
            {
                // Fail the promise with the error from the email service
                promise.fail(reply.cause());
            }
        });

        return promise;
    }

}
