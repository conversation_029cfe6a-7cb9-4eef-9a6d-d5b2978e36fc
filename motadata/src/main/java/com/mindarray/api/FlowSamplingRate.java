/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.FlowSamplingRateConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.Set;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.api.APIConstants.*;
import static org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR;

public class FlowSamplingRate extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(FlowSamplingRate.class, GlobalConstants.MOTADATA_API, "Flow Sampling Rate API");

    public FlowSamplingRate()
    {
        super("flow-sampling-rates", FlowSamplingRateConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_CREATE, REQUEST_DELETE));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
     * Need to merge two collections, FlowSamplingRate and Metric (for interface details)
     * if device is provisioned than we will merge interface details
     * if device is not provisioned than we will show only interface.index, event-source and sampling.rate
     * */

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var eventSources = EventSourceConfigStore.getStore().getItemsByMultiValueFieldAny(EventBusConstants.EVENT_TYPE, new JsonArray().add("flow"));

            var interfaces = new HashMap<String, JsonObject>(); // for caching provisioned event source device interfaces to merge collections

            var entities = new JsonArray();

            if (eventSources != null && !eventSources.isEmpty())
            {
                for (var i = 0; i < eventSources.size(); i++)
                {
                    var sourceIP = eventSources.getJsonObject(i).getString(EventBusConstants.EVENT_SOURCE);

                    if (sourceIP != null)
                    {
                        var objects = MetricConfigStore.getStore().getNetworkInterfacesBySource(sourceIP);

                        if (objects != null)
                        {
                            for (var index = 0; index < objects.size(); index++)
                            {
                                var object = objects.getJsonObject(index);

                                interfaces.put(sourceIP + GlobalConstants.SEPARATOR + object.getString(NMSConstants.INTERFACE_INDEX), object);
                            }
                        }

                    }
                }

                var items = FlowSamplingRateConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    var samplingKey = item.getString(EventBusConstants.EVENT_SOURCE) + GlobalConstants.SEPARATOR + item.getString(NMSConstants.INTERFACE_INDEX);

                    if (interfaces.containsKey(samplingKey) && interfaces.get(samplingKey) != null)
                    {
                        item.mergeIn(interfaces.get(samplingKey));
                    }

                    entities.add(item);
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, entities));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(GlobalConstants.ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(GlobalConstants.MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
