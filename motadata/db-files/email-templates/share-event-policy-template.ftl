<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;">
    <meta name="x-apple-disable-message-reformatting">
    <style type="text/css">
        @media screen and (max-width:599px) {
            .mobile-block {
                display: block
            }
            .mobile-100p {
                width: 100%;
                height: auto
            }
            .mobile-hide {
                display: none
            }
            .mobile-space {
                padding-top: 8px !important;
                padding-bottom: 8px !important
            }
            .space16 {
                height: 16px
            }
            .side-spacing {
                padding-left: 16px !important;
                padding-right: 16px !important
            }
            .message-width {
                width: 65%
            }
        }
        .major {
            color: #f58518
        }
        .critical {
            color: #f04e3e
        }
        .clear {
            color: #89c540
        }
        .down {
            color: #ed7c70
        }
        .warning {
            color: #f5bc18
        }
        .major.border {
            border-color: #f58518 !important
        }
        .critical.border {
            border-color: #f04e3e !important
        }
        .clear.border {
            border-color: #89c540 !important
        }
        .down.border {
            border-color: #ed7c70 !important
        }
        .warning.border {
            border-color: #f5bc18 !important
        }
    </style>
    <title>Critical</title>
</head>
<body bgcolor="#F3F6F8" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table width="100%" bgcolor="#F3F6F8" border="0" cellpadding="0" cellspacing="0" align="center"
       style="border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8">
    <tr><td height="40" class="space16"></td></tr>
    <#include "rebranding-header.ftl">
    <tr><td height="24" class="space16"></td></tr>
    <tr>
        <td valign="top" align="center">
            <#assign severityClass = "">
            <#if .data_model["severity"]??>
                <#assign severityClass = .data_model["severity"]>
            </#if>
            <table class="mobile-100p side-spacing ${severityClass} border" width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffffff"
                   style="padding:0 24px;border-top:4px solid;background:#fff">
                <tbody>
                <tr><td height="24" class="space16"></td></tr>
                <tr>
                    <td style="text-align:center">
                        <#if .data_model["severity"]??>
                            <img width="36" height="36" style="width:36px;height:36px" src="cid:${.data_model["severity"]}.png" alt="severity">
                        </#if>
                    </td>
                </tr>
                <tr><td height="10"></td></tr>
                <tr>
                    <td style="text-align:center">
                        <h1 style="font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px">
                            <#if .data_model["policy.type"]??>${.data_model["policy.type"]}</#if> Alert
                        </h1>
                        <p style="color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700">
                            <#if .data_model["Timestamp"]??>${.data_model["Timestamp"]}</#if>
                        </p>
                    </td>
                </tr>
                <tr><td height="32" class="space16"></td></tr>
                <tr><td><hr style="border:1px solid #eef2f6;margin:0;padding:0"></td></tr>
                <tr><td height="16"></td></tr>
                <tr>
                    <td>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;margin:0;padding:0">
                            <tbody>
                            <tr valign="top">
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Policy Name</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["policy.name"]??>${.data_model["policy.name"]}</#if>
                                    </p>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Policy Type</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["policy.type"]??>${.data_model["policy.type"]}</#if>
                                    </p>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.3333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Evaluation Window</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["evaluation.window"]??>${.data_model["evaluation.window"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr class="mobile-hide"><td colspan="3" height="16"></td></tr>
                            <tr valign="top">
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Alert Type</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["trigger.mode"]??>${.data_model["trigger.mode"]}</#if>
                                    </p>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Severity</p>
                                    <#if .data_model["severity"]??>
                                        <p class="${.data_model["severity"]}" style="font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["severity"]}</p>
                                    </#if>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Last Seen</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["active.since"]??>${.data_model["active.since"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr class="mobile-hide"><td colspan="3" height="16"></td></tr>
                            <tr valign="top">
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Dimension</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["event.field"]??>${.data_model["event.field"]}</#if>
                                    </p>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Trigger Value</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["value"]??>${.data_model["value"]}</#if>
                                    </p>
                                </td>
                                <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="padding:0 5px">
                                </td>
                            </tr>
                            <tr class="mobile-hide"><td colspan="3" height="16"></td></tr>
                            <tr><td colspan="3"><hr style="border:1px solid #eef2f6;margin:0;padding:0"></td></tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr>
                                <td colspan="3" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Trigger Condition</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["triggerCondition"]??>${.data_model["triggerCondition"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr class="mobile-hide"><td colspan="3" height="16"></td></tr>
                            <tr><td colspan="3"><hr style="border:1px solid #eef2f6;margin:0;padding:0"></td></tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr>
                                <td colspan="3" style="padding:0 5px">
                                    <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Message</p>
                                    <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">
                                        <#if .data_model["message"]??>${.data_model["message"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr class="mobile-hide"><td colspan="3" height="16"></td></tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;margin:0;padding:0">
                            <tbody>
                            <tr><td colspan="3" height="24"></td></tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr>
                                <td colspan="3">
                                    <p style="color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px">
                                        <strong>Disclaimer:</strong>This is an auto-generated message. Do not respond to this email.<br/>
                                        If you have any questions, please contact your System Administrator.
                                    </p>
                                </td>
                            </tr>
                            <tr><td colspan="3" height="24"></td></tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    <tr><td height="24"></td></tr>

    <tr align="center">
        <td>
            <p style="font-size:10px;margin:0;padding:0;color:#7b8fa5">
                For more details, visit
                <#if .data_model["policy.url"]??>
                    <a style="color:#009ddc;text-decoration:none" href="${.data_model["policy.url"]}" target="_blank">Click here</a>.
                <#else>
                    <a style="color:#009ddc;text-decoration:none" href="https://www.motadata.com/" target="_blank">Click here</a>.
                </#if>
            </p>
        </td>
    </tr>

    <tr><td height="16"></td></tr>

    <#include "rebranding-footer.ftl">

    <tr><td height="32"></td></tr>
</table>
</body>
</html>