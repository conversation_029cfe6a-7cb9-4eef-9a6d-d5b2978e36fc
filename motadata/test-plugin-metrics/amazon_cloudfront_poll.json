{"127.0.0.1": {"209": {"metadata.fields": {"type": "CDN"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176993700500097, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Cloud Front", "metric.object": ***************, "metric.plugin": "amazoncloudfront", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Amazon CloudFront", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:27.737 pm 14/06/2022", "object.custom.fields": {"***************": "CDN"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 1, "object.name": "E1RVVPM08N16XT(Global)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "E1RVVPM08N16XT(ap-south-1)", "object.type": "Amazon CloudFront", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 209, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"209": {"metadata.fields": {"type": "CDN"}}, "aws.cloudfront.401.error.ratio.percent": 0, "aws.cloudfront.403.error.ratio.percent": 0, "aws.cloudfront.404.error.ratio.percent": 0, "aws.cloudfront.4xx.error.ratio.percent": 0, "aws.cloudfront.502.error.ratio.percent": 0, "aws.cloudfront.503.error.ratio.percent": 0, "aws.cloudfront.504.error.ratio.percent": 0, "aws.cloudfront.5xx.error.ratio.percent": 0, "aws.cloudfront.cache.hit.ratio.percent": 100, "aws.cloudfront.domain.name": "ddmxnfvn1ssd3.cloudfront.net", "aws.cloudfront.downloaded.bytes.rate": 974230, "aws.cloudfront.error.ratio.percent": 0, "aws.cloudfront.http.version": "HTTP2", "aws.cloudfront.ipv6.enabled": "true", "aws.cloudfront.last.modified.time": " 363 days 1 hours 29 minutes 31 seconds", "aws.cloudfront.requests.rate": 1, "aws.cloudfront.uploaded.bytes.rate": 0, "aws.status": "Enabled", "status": "Up"}, "status": "succeed", "timeout": 60}}