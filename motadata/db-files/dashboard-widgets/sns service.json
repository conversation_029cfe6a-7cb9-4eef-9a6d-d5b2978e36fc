[{"_type": "0", "id": 10000000001641, "visualization.name": "Topics", "visualization.description": "Topics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.topic", "aggregator": "last", "entity.type": "Group"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.topic", "icon": {"name": "topics", "placement": "prefix"}}, "header": {"title": "Topics", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.topic.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001642, "visualization.name": "Subscriptions", "visualization.description": "Subscriptions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.subscriptions", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.subscriptions", "icon": {"name": "message-star", "placement": "prefix"}}, "header": {"title": "Subscriptions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.subscriptions.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001643, "visualization.name": "Delivered Notification", "visualization.description": "Delivered Notification", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.delivered.notifications", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.delivered.notifications", "icon": {"name": "delivered-notifications", "placement": "prefix"}}, "header": {"title": "Delivered Notification", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.delivered.notifications.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001644, "visualization.name": "Failed Notification", "visualization.description": "Failed Notification", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.failed.notifications", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.failed.notifications", "icon": {"name": "failed-notifications", "placement": "prefix"}}, "header": {"title": "Failed Notification", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.failed.notifications.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001645, "visualization.name": "Published Messages", "visualization.description": "Published Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.published.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.published.messages", "icon": {"name": "success-message", "placement": "prefix"}}, "header": {"title": "Published Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.published.messages.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001646, "visualization.name": "Success rate", "visualization.description": "Success rate", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.sms.successful.deliveries.rate", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.sns.sms.successful.deliveries.rate", "icon": {"name": "success", "placement": "prefix"}}, "header": {"title": "Success rate", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.sns.sms.successful.deliveries.rate.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001647, "visualization.name": "Topic Details", "visualization.description": "Topic Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.topics", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.failed.notifications", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.delivered.notifications", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.published.messages", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.sms.successful.deliveries.rate", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.sms.usage.cost", "aggregator": "last", "entity.type": "Group", "entities": []}, {"data.point": "aws.sns.published.bytes", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "aws.sns.topics", "title": "Topic Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aws.sns.failed.notifications", "title": "Notifications", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "Failed", "name": "file-search"}}}, {"name": "aws.sns.delivered.notifications", "title": "Notifications", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "Delivered"}}}, {"name": "aws.sns.published.messages", "title": "Published Message Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "name": "database"}}}, {"name": "aws.sns.sms.successful.deliveries.rate", "title": "Success Rate", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "name": "location-pin"}}}, {"name": "aws.sns.sms.usage.cost", "title": "Usage Cost", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "clock"}}}, {"name": "aws.sns.published.bytes", "title": "Published Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"placement": "prefix", "text": "flag"}}}]}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001648, "visualization.name": "Top Topics By Success Rate", "visualization.description": "Top Topics By Success Rate", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.sms.successful.deliveries.rate", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.sns.sms.successful.deliveries.rate.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001649, "visualization.name": "Top Topics By Publish Bytes", "visualization.description": "Top Topics By Publish Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.published.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.sns.published.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001650, "visualization.name": "Published Messages Rate By Topics", "visualization.description": "Published Messages Rate By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.published.messages", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001651, "visualization.name": "Delivered Notification By Topics", "visualization.description": "Delivered Notification By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.delivered.notifications", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001652, "visualization.name": "Failed Notification By Topics", "visualization.description": "Failed Notification By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.failed.notifications", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001653, "visualization.name": "Published Bytes By Topics", "visualization.description": "Published Bytes By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.published.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001654, "visualization.name": "SMS Success Rate By Topics", "visualization.description": "SMS Success Rate By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.sms.successful.deliveries.rate", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001655, "visualization.name": "SMS Usage Cost By Topics", "visualization.description": "SMS Usage Cost By Topics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.sns.sms.usage.cost", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]