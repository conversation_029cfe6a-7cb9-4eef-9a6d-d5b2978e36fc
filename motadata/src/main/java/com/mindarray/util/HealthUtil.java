/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.util;

import com.mindarray.*;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Agent;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.STATE_NOT_RUNNING;
import static com.mindarray.nms.NMSConstants.STATE_RUNNING;

public class HealthUtil extends AbstractVerticle
{
    public static final String HEALTH_STATS = "health.stats";
    public static final String JVM_STATS = "jvm.stats";
    public static final String JVM_GC_STATS = "jvm.gc.stats";
    public static final String DROPPED_EVENTS = "dropped.events";
    public static final String PENDING_BATCH_EVENTS = "pending.batch.events";

    public static final String IDLE_WORKERS = "idle.workers";

    /// metric poller related stats...
    public static final String PENDING_EVENTS = "pending.events";

    private static final Logger LOGGER = new Logger(HealthUtil.class, GlobalConstants.MOTADATA_UTIL, "Health Util");
    private final Map<String, Set<Integer>> sources = new HashMap<>();
    private final Map<String, Map<Long, JsonObject>> statistics = new HashMap<>();           // key(engine.category) -> value (id -> stats)
    private final Set<String> events = Set.of(EVENT_METRIC_POLL, EventBusConstants.EVENT_DISCOVERY, EventBusConstants.EVENT_REDISCOVER, EventBusConstants.EVENT_PLUGIN_ENGINE, EventBusConstants.EVENT_TOPOLOGY);
    private final StringBuilder builder = new StringBuilder(0);
    private Set<String> mappers;
    // for agent based const
    private AtomicInteger rollingWindows;
    private Map<String, Integer> memoryWarningThresholdEvents;
    private Map<String, Integer> memoryCriticalThresholdEvents;
    private Map<String, Integer> cpuWarningThresholdEvents;
    private Map<String, Integer> cpuCriticalThresholdEvents;
    private String previousSeverity = GlobalConstants.Severity.CLEAR.name();

    @Override
    public void start(Promise<Void> promise)
    {
        mappers = new HashSet<>();

        loadEventSources();

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                var stats = event.getJsonObject(HEALTH_STATS);

                if (event.containsKey(ENGINE_TYPE) && !event.getString(ENGINE_TYPE).isEmpty())
                {
                    var engineCategory = stats.getString(ENGINE_CATEGORY, "core");

                    var engineType = event.getString(ENGINE_TYPE);

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("statistics received for engine type : %s and category : %s from uuid : %s , type : %s , mode : %s ", engineType, engineCategory, event.getString(REMOTE_EVENT_PROCESSOR_UUID), event.getString(REMOTE_EVENT_PROCESSOR_TYPE), event.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));
                    }


                    var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(event.getString(REMOTE_EVENT_PROCESSOR_UUID), event.getString(REMOTE_EVENT_PROCESSOR_TYPE), EventBusConstants.getInstallationMode(event));

                    if (id != null)
                    {
                        statistics.computeIfAbsent(engineCategory, value -> new HashMap<>()).computeIfAbsent(id, value -> new JsonObject());

                        statistics.get(engineCategory).get(id).put(engineType, stats);

                        if (CommonUtil.isNotNullOrEmpty(engineType) && events.contains(engineType))
                        {
                            vertx.eventBus().publish(EventBusConstants.EVENT_HEALTH_MANAGER_NOTIFICATION, event);
                        }

                        if (CommonUtil.traceEnabled())
                        {

                            LOGGER.trace(String.format("statistics : %s ", stats.encode()));

                        }
                    }

                    vertx.eventBus().send(EVENT_DIAGNOSTIC_STATS, event);
                }
                else
                {
                    LOGGER.warn(String.format("Health statistics event %s received with empty engine type value", event));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DATASTORE_STATS_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                var stats = event.getJsonObject(HEALTH_STATS);

                checkEventSource(event, stats);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Datastore health data received for plugin Id %d and statistics %s", event.getInteger(PLUGIN_ID), stats.encode()));
                }

                builder.setLength(0);

                DatastoreConstants.write(stats.put(PLUGIN_ID, event.getInteger(PLUGIN_ID))
                        .put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                        .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                        .put(DATASTORE_TYPE, DatastoreConstants.DatastoreType.HEALTH_METRIC.ordinal()), VisualizationConstants.VisualizationDataSource.HEALTH_METRIC.getName(), mappers, builder);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // JVM health data from app or any remote-entity will be received here.
        vertx.eventBus().<JsonObject>localConsumer(EVENT_JVM_STATS_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                var stats = event.getJsonObject(HEALTH_STATS);

                if (stats.containsKey(JVM_STATS) && !stats.getJsonObject(JVM_STATS).isEmpty())
                {
                    checkEventSource(event.put(PLUGIN_ID, DatastoreConstants.PluginId.JVM_HEALTH_METRIC.getName()), stats.getJsonObject(JVM_STATS));

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("JVM health data received for plugin Id %d and statistics %s", event.getInteger(PLUGIN_ID), stats.getJsonObject(JVM_STATS).encode()));
                    }

                    builder.setLength(0);

                    DatastoreConstants.write(stats.getJsonObject(JVM_STATS)
                            .put(PLUGIN_ID, event.getInteger(PLUGIN_ID))
                            .put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                            .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                            .put(DATASTORE_TYPE, DatastoreConstants.DatastoreType.HEALTH_METRIC.ordinal()), VisualizationConstants.VisualizationDataSource.HEALTH_METRIC.getName(), mappers, builder);
                }
                if (stats.containsKey(JVM_GC_STATS) && !stats.getJsonArray(JVM_GC_STATS).isEmpty())
                {
                    for (var index = 0; index < stats.getJsonArray(JVM_GC_STATS).size(); index++)
                    {
                        var item = stats.getJsonArray(JVM_GC_STATS).getJsonObject(index);

                        checkEventSource(event.put(PLUGIN_ID, DatastoreConstants.PluginId.JVM_GC_HEALTH_METRIC.getName()), item);

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("JVM Garbage Collector health data received for plugin Id %d and statistics %s", event.getInteger(PLUGIN_ID), item.encode()));
                        }

                        builder.setLength(0);

                        DatastoreConstants.write(item
                                .put(PLUGIN_ID, event.getInteger(PLUGIN_ID))
                                .put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                                .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                .put(DATASTORE_TYPE, DatastoreConstants.DatastoreType.HEALTH_METRIC.ordinal()), VisualizationConstants.VisualizationDataSource.HEALTH_METRIC.getName(), mappers, builder);
                    }
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // when UI request for engine statistics
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_HEALTH_STATISTICS, message ->
        {
            try
            {
                var items = statistics.get(message.body().getString(ENGINE_CATEGORY));

                var stats = new JsonArray();

                if (items != null)
                {
                    for (var item : items.entrySet())
                    {
                        for (var entry : item.getValue().getMap().entrySet())
                        {
                            if (!message.body().containsKey(ENGINE_TYPE) || entry.getKey().startsWith(message.body().getString(ENGINE_TYPE)))
                            {
                                var value = (JsonObject) entry.getValue();

                                stats.add(value.put(ENGINE_TYPE, entry.getKey()).mergeIn(RemoteEventProcessorConfigStore.getStore().getItem(item.getKey())));
                            }
                        }
                    }
                }

                EventBusConstants.publish(message.body().getString(SESSION_ID), EventBusConstants.EVENT_HEALTH_STATISTICS, message.body().put(STATUS, STATUS_SUCCEED).put(RESULT, stats));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                EventBusConstants.publish(message.body().getString(SESSION_ID), EventBusConstants.EVENT_HEALTH_STATISTICS, message.body().put(STATUS, STATUS_FAIL));
            }
        });

        if (Bootstrap.bootstrapType() == com.mindarray.GlobalConstants.BootstrapType.AGENT)
        {
            rollingWindows = new AtomicInteger(AgentConfigUtil.getAgentHealthWindowCount());

            memoryWarningThresholdEvents = new HashMap<>();

            memoryCriticalThresholdEvents = new HashMap<>();

            cpuWarningThresholdEvents = new HashMap<>();

            cpuCriticalThresholdEvents = new HashMap<>();

            for (var agent : AgentConstants.Agent.values())
            {
                memoryCriticalThresholdEvents.put(agent.getName(), 0);

                memoryWarningThresholdEvents.put(agent.getName(), 0);

                cpuCriticalThresholdEvents.put(agent.getName(), 0);

                cpuWarningThresholdEvents.put(agent.getName(), 0);
            }

            // start agent inspect health monitoring
            vertx.setPeriodic(AgentConfigUtil.getAgentHealthInspectionTimerSeconds() * 1000L, timer -> diagnosisAgentHealth());
        }
        else
        {
            vertx.eventBus().<JsonObject>localConsumer(EVENT_AGENT_HEALTH_STATS, message ->
            {
                try
                {
                    var context = message.body().getJsonObject(RESULT);

                    var uuid = message.body().getString(Agent.AGENT_UUID);


                    if (context.containsKey("system.disk.used.percent") && context.containsKey("system.current.directory.used.percent") && context.containsKey("system.root.directory.used.percent") &&
                            (RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.COLLECTOR.name(), InstallationMode.STANDALONE.name()) != null
                                    || RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.EVENT_COLLECTOR.name(), InstallationMode.STANDALONE.name()) != null || RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.EVENT_PROCESSOR.name(), InstallationMode.STANDALONE.name()) != null
                                    || RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.FLOW_COLLECTOR.name(), InstallationMode.STANDALONE.name()) != null
                                    || RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.APP.name(), Bootstrap.getInstallationMode()) != null
                                    || RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(uuid, BootstrapType.DATASTORE.name(), Bootstrap.getInstallationMode()) != null))
                    {
                        var currentDirectoryUsedPercent = context.getDouble("system.current.directory.used.percent");

                        var rootDirectoryUsedPercent = context.getDouble("system.root.directory.used.percent");

                        var severity = GlobalConstants.Severity.CLEAR;

                        if (currentDirectoryUsedPercent >= 90 || rootDirectoryUsedPercent >= 90)
                        {
                            // critical
                            severity = GlobalConstants.Severity.CRITICAL;

                            // in case of standalone installation need to stop motadata and datastore both
                            var remoteEventProcessors = RemoteEventProcessorConfigStore.getStore().getItemsByValue(REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(Agent.AGENT_UUID));

                            for (var index = 0; index < remoteEventProcessors.size(); index++)
                            {
                                var remoteEventProcessor = remoteEventProcessors.getJsonObject(index);

                                var type = remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE);

                                if (rootDirectoryUsedPercent >= 90)
                                {
                                    LOGGER.info(String.format("Disk Utilization for %s is critical with value %s for type %s", "Root Partition", rootDirectoryUsedPercent, type));
                                }

                                if (currentDirectoryUsedPercent >= 90)
                                {
                                    LOGGER.info(String.format("Disk Utilization for %s is critical with value %s for type %s", "Current Partition", currentDirectoryUsedPercent, type));
                                }

                                switch (GlobalConstants.BootstrapType.valueOf(type))
                                {
                                    case APP ->
                                    {
                                        LOGGER.warn("stopping Motadata App...");

                                        Bootstrap.undeployLicensedVerticle().onComplete(result ->
                                        {
                                            LOGGER.warn("motadata engine stopped due to disk utilization...");

                                            BootstrapStandalone.APP_SERVER_ONLINE.set(false);

                                        });
                                    }

                                    case COLLECTOR, EVENT_COLLECTOR, FLOW_COLLECTOR, EVENT_PROCESSOR ->
                                    {
                                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP)
                                                .put(REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(Agent.AGENT_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, type));

                                        LOGGER.warn(String.format("shutdown command sent to remote event processor %s", message.body().getString(Agent.AGENT_UUID)));
                                    }

                                    case DATASTORE ->
                                    {
                                        LOGGER.warn("stopping Motadata App and datastore...");

                                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP)
                                                .put(REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(Agent.AGENT_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, type));

                                        LOGGER.warn(String.format("shutdown command sent to remote event processor %s", message.body().getString(Agent.AGENT_UUID)));

                                        Bootstrap.undeployLicensedVerticle().onComplete(result ->
                                        {
                                            LOGGER.warn("datastore and motadata engine both stopped due to disk utilization...");

                                            BootstrapStandalone.APP_SERVER_ONLINE.set(false);

                                        });
                                    }
                                }
                            }
                        }
                        else if (currentDirectoryUsedPercent >= 85 || rootDirectoryUsedPercent >= 85)
                        {
                            // major
                            severity = GlobalConstants.Severity.MAJOR;

                            var remoteEventProcessors = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(Agent.AGENT_UUID));

                            if (rootDirectoryUsedPercent >= 85)
                            {
                                LOGGER.warn(String.format("Disk Utilization for %s is major with value %f for type %s", "Root Partition", rootDirectoryUsedPercent, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                                EventBusConstants.publish(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION, new JsonObject().put(SEVERITY, severity).put(MESSAGE, String.format(ErrorMessageConstants.DISK_UTILIZATION_EXCEED, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE), remoteEventProcessors.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP), "Root Partition", rootDirectoryUsedPercent, 85)));
                            }

                            if (currentDirectoryUsedPercent >= 85)
                            {
                                LOGGER.warn(String.format("Disk Utilization for %s is major with value %f for type %s", "Current Partition", currentDirectoryUsedPercent, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                                EventBusConstants.publish(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION, new JsonObject().put(SEVERITY, severity).put(MESSAGE, String.format(ErrorMessageConstants.DISK_UTILIZATION_EXCEED, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE), remoteEventProcessors.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP), "Current Partition", currentDirectoryUsedPercent, 85)));
                            }
                        }
                        else if (currentDirectoryUsedPercent >= 80 || rootDirectoryUsedPercent >= 80)
                        {
                            // warning
                            severity = GlobalConstants.Severity.WARNING;

                            var remoteEventProcessors = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, message.body().getString(Agent.AGENT_UUID));

                            if (rootDirectoryUsedPercent >= 80)
                            {
                                LOGGER.warn(String.format("Disk Utilization for %s is in warning state with value %f for type %s", "Root Partition", rootDirectoryUsedPercent, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                                EventBusConstants.publish(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION, new JsonObject().put(SEVERITY, severity).put(MESSAGE, String.format(ErrorMessageConstants.DISK_UTILIZATION_EXCEED, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE), remoteEventProcessors.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP), "Root Partition", rootDirectoryUsedPercent, 80)));
                            }

                            if (currentDirectoryUsedPercent >= 80)
                            {
                                LOGGER.warn(String.format("Disk Utilization for %s is in warning state with value %f for type %s", "Current Partition", currentDirectoryUsedPercent, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                                EventBusConstants.publish(UI_NOTIFICATION_DISK_UTILIZATION_EXCEED_NOTIFICATION, new JsonObject().put(SEVERITY, severity).put(MESSAGE, String.format(ErrorMessageConstants.DISK_UTILIZATION_EXCEED, remoteEventProcessors.getString(REMOTE_EVENT_PROCESSOR_TYPE), remoteEventProcessors.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP), "Current Partition", currentDirectoryUsedPercent, 80)));
                            }
                        }

                        if (severity != GlobalConstants.Severity.CLEAR && !severity.name().equalsIgnoreCase(previousSeverity))
                        {
                            previousSeverity = severity.name();

                            var user = UserConfigStore.getStore().getItem(DEFAULT_ID, false);

                            if (user.containsKey(User.USER_EMAIL) && user.getString(User.USER_EMAIL) != null)
                            {
                                if (rootDirectoryUsedPercent >= 80)
                                {
                                    Notification.sendEmail(new JsonObject()
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.name().toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format("System Disk Utilization reached %s level. for root partition", severity.name()))
                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL)))
                                            .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_DISK_UTILIZATION_TEMPLATE)
                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(SEVERITY, severity).put(AIOpsObject.OBJECT_NAME, message.body().getString(AIOpsObject.OBJECT_NAME))
                                                    .put(METRIC, "system.root.directory.used.percent").put(TIME_STAMP, DateTimeUtil.timestamp(message.body().getLong(EVENT_TIMESTAMP) * 1000L))
                                                    .put(AIOpsObject.OBJECT_TARGET, message.body().getString(AIOpsObject.OBJECT_TARGET)).put(AIOpsObject.OBJECT_TYPE, message.body().getString(AIOpsObject.OBJECT_TYPE))
                                                    .put(VALUE, context.getDouble("system.root.directory.used.percent")).put(MESSAGE, "Disk Utilization is beyond threshold for root partition")));
                                }
                                if (currentDirectoryUsedPercent >= 80)
                                {
                                    Notification.sendEmail(new JsonObject()
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.name().toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format("System Disk Utilization reached %s level. for current partition", severity.name()))
                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL)))
                                            .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_DISK_UTILIZATION_TEMPLATE)
                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(SEVERITY, severity).put(AIOpsObject.OBJECT_NAME, message.body().getString(AIOpsObject.OBJECT_NAME)).put(METRIC, "system.current.directory.used.percent").put(TIME_STAMP, DateTimeUtil.timestamp(message.body().getLong(EVENT_TIMESTAMP) * 1000L)).put(AIOpsObject.OBJECT_TARGET, message.body().getString(AIOpsObject.OBJECT_TARGET)).put(AIOpsObject.OBJECT_TYPE, message.body().getString(AIOpsObject.OBJECT_TYPE)).put(VALUE, context.getDouble("system.current.directory.used.percent")).put(MESSAGE, "Disk Utilization is beyond threshold for current partition")));

                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            var context = new JsonObject().put(ENGINE_TYPE, EventBusConstants.EVENT_ENGINE_STATS);

            var remoteContext = new JsonObject().put(EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS).put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC);


            if (Bootstrap.bootstrapType() == BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name())) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
            {
                vertx.setPeriodic(MotadataConfigUtil.getEngineStatFlushTimerSeconds() * 1000L, timer ->
                {
                    try
                    {
                        vertx.eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS, context);            // for primary/standalone APP

                        vertx.eventBus().send(EVENT_PUBLICATION, remoteContext);                            // for remote entities
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }

            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                if (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                {
                    var durations = new JsonObject();                   // uuid -> value (duration,timestamp) etc.

                    // update cache for status/duration when observer publish its cache at every 30 sec
                    vertx.eventBus().<JsonObject>localConsumer(EVENT_OBSERVER_HEARTBEAT, message ->
                    {
                        try
                        {
                            durations.mergeIn(message.body().getJsonObject(DURATION)).put(AIOpsObject.OBJECT_IP, message.body().getString(AIOpsObject.OBJECT_IP)).put(AIOpsObject.OBJECT_HOST, message.body().getString(AIOpsObject.OBJECT_HOST));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });

                    // this is when observer is not connected with primary app then update duration for down state.
                    vertx.setPeriodic(10 * 1000L, timer ->
                    {
                        try
                        {
                            var duration = durations.getJsonObject(Bootstrap.getRegistrationId());

                            if (duration != null)
                            {
                                if (DateTimeUtil.currentSeconds() - duration.getLong(EVENT_TIMESTAMP, DUMMY_ID) > 60L)
                                {
                                    if ((!duration.containsKey(STATUS) || !duration.getString(STATUS).equalsIgnoreCase(STATE_NOT_RUNNING)))
                                    {
                                        // if connectivity lost with primary then update other apps durations.

                                        for (var entry : durations.getMap().entrySet())
                                        {
                                            if (!entry.getKey().equalsIgnoreCase(AIOpsObject.OBJECT_IP) && !entry.getKey().equalsIgnoreCase(AIOpsObject.OBJECT_HOST))
                                            {
                                                durations.getJsonObject(entry.getKey()).put(DURATION, DateTimeUtil.currentSeconds()).put(STATUS, STATE_NOT_RUNNING);

                                                LOGGER.info(String.format("down duration : %s is update to %s", entry.getKey(), DateTimeUtil.currentSeconds()));
                                            }
                                        }

                                        LOGGER.warn(String.format("connectivity has been lost from APP %s to observer %s ", Bootstrap.getRegistrationId(), durations.getString(AIOpsObject.OBJECT_IP)));
                                    }
                                }
                                else
                                {
                                    duration.put(STATUS, STATE_RUNNING);
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });


                    // publish status/duration cache to UI
                    vertx.eventBus().<JsonObject>localConsumer(EVENT_OBSERVER_HEARTBEAT + ".status.query", message ->
                    {
                        var event = message.body();

                        try
                        {
                            var items = RemoteEventProcessorConfigStore.getStore().getItemsByValue(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name());

                            for (var index = 0; index < items.size(); index++)
                            {
                                var item = items.getJsonObject(index);

                                var duration = durations.getJsonObject(item.getString(REMOTE_EVENT_PROCESSOR_UUID), null);

                                if (duration != null)
                                {
                                    if (DateTimeUtil.currentSeconds() - duration.getLong(EVENT_TIMESTAMP, DUMMY_ID) > 60L)
                                    {
                                        item.put(STATUS, STATE_NOT_RUNNING);
                                    }
                                    else
                                    {
                                        item.put(STATUS, STATE_RUNNING);
                                    }

                                    item.put(DURATION, DateTimeUtil.convertTime(duration.getLong(DURATION)));
                                }
                                else
                                {
                                    item.put(STATUS, STATE_NOT_RUNNING);
                                }

                                // observer detail
                                item.put(AIOpsObject.OBJECT_IP, durations.getString(AIOpsObject.OBJECT_IP)).put(AIOpsObject.OBJECT_HOST, durations.getString(AIOpsObject.OBJECT_HOST));
                            }

                            EventBusConstants.publish(message.body().getString(SESSION_ID), EVENT_OBSERVER_HEARTBEAT, event.put(STATUS, STATUS_SUCCEED).put(RESULT, items));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            EventBusConstants.publish(message.body().getString(SESSION_ID), EVENT_OBSERVER_HEARTBEAT, event.put(STATUS, STATUS_FAIL).put(MESSAGE, exception.getMessage()));
                        }
                    });
                }
            }
        }

        promise.complete();
    }

    private void loadEventSources()
    {
        var items = EventSourceConfigStore.getStore().flatItemsByValues(EVENT_TYPE, new JsonArray().add(EVENT_HEALTH_METRIC), EVENT_SOURCE);

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if (!sources.containsKey(item.getString(EVENT_SOURCE)))
            {
                sources.put(item.getString(EVENT_SOURCE), new HashSet<>(item.getJsonArray(PLUGIN_ID, new JsonArray()).getList()));
            }
            else
            {
                // source exist, need to add event category
                var pluginIds = item.getJsonArray(PLUGIN_ID, new JsonArray());

                for (var count = 0; count < pluginIds.size(); count++)
                {
                    sources.get(item.getString(EVENT_SOURCE)).add(pluginIds.getInteger(count));
                }
            }
        }
    }

    private void checkEventSource(JsonObject event, JsonObject healthStates)
    {
        if (!sources.containsKey(event.getString(EVENT_SOURCE)))
        {
            var pluginId = new HashSet<Integer>();

            pluginId.add(event.getInteger(PLUGIN_ID));

            sources.put(event.getString(EVENT_SOURCE), pluginId);

            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                    new JsonObject().put(EventBusConstants.EVENT_SOURCE, event.getString(EVENT_SOURCE))
                            .put(EventBusConstants.EVENT, EVENT_HEALTH_METRIC)
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                            .put(GlobalConstants.PLUGIN_ID, event.getInteger(PLUGIN_ID))
                            .put(LogEngineConstants.EVENT_CATEGORY, EVENT_HEALTH_METRIC));
        }
        else if (sources.containsKey(event.getString(EVENT_SOURCE)) && !sources.get(event.getString(EVENT_SOURCE)).contains(event.getInteger(PLUGIN_ID)))
        {
            sources.get(event.getString(EVENT_SOURCE)).add(event.getInteger(PLUGIN_ID));

            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                    new JsonObject().put(EventBusConstants.EVENT_SOURCE, event.getString(EVENT_SOURCE))
                            .put(EventBusConstants.EVENT, EVENT_HEALTH_METRIC)
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE)
                            .put(GlobalConstants.PLUGIN_ID, event.getInteger(PLUGIN_ID))
                            .put(LogEngineConstants.EVENT_CATEGORY, EVENT_HEALTH_METRIC));
        }

        for (var key : healthStates.getMap().keySet())
        {
            builder.setLength(0);

            byte category;

            if (key.equalsIgnoreCase(ENGINE_TYPE))
            {
                category = DatastoreConstants.DataCategory.STRING.getName();
            }
            else
            {
                category = DatastoreConstants.DataCategory.NUMERIC.getName();
            }

            builder.append(category).append(COLUMN_SEPARATOR).append(event.getInteger(PLUGIN_ID)).append(COLUMN_SEPARATOR).append(key).append(COLUMN_SEPARATOR).append(NO).append(COLUMN_SEPARATOR).append(EVENT_HEALTH_METRIC);

            if (!mappers.contains(builder.toString()))
            {
                mappers.add(builder.toString());

                vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                        new JsonObject()
                                .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                                .put(DatastoreConstants.MAPPER, builder.toString()));
            }
        }
    }

    /*----------------------------------------------------------------------------------------- Agent Based Implementation ------------------------------------------------------------------------------------*/

    private void diagnosisAgentHealth()
    {
        try
        {
            var processors = ProcessUtil.getProcessors();

            ProcessUtil.getProcesses().forEach((agent, process) ->
            {
                if (GlobalConstants.OS_SOLARIS || GlobalConstants.OS_LINUX)
                {
                    Bootstrap.vertx().<Void>executeBlocking(future ->
                            diagnosisLinuxAgentHealth(process.pid(), processors, agent, future), false, result ->
                    {
                    });
                }
                else if (GlobalConstants.OS_WINDOWS)
                {
                    Bootstrap.vertx().<Void>executeBlocking(future ->
                            diagnosisWindowsAgentHealth(process.pid(), processors, agent, future), false, result ->
                    {
                    });
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void diagnosisWindowsAgentHealth(long processId, int processors, String agent, Promise<Void> future)
    {
        var arguments = new String[]{"cmd.exe", "/C", "powershell.exe", "Get-WmiObject -class Win32_PerfFormattedData_PerfProc_Process  -Filter  \"CreatingProcessID=" + processId + "\"  | Select name,PercentProcessorTime,workingSetPrivate"};

        var cpuPercent = 0.0;

        var memoryMB = 0.0;

        try (var reader = new BufferedReader(new InputStreamReader(Runtime.getRuntime().exec(arguments).getInputStream())))
        {
            String line;

            var output = new StringBuilder(0);

            while ((line = reader.readLine()) != null)
            {
                output.append(line);
            }

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("Windows command %s output for process id %s is %s", Arrays.toString(arguments), processId, output));
            }

            if (!output.isEmpty())
            {
                for (var result : output.toString().split(GlobalConstants.NEW_LINE))
                {
                    line = result.trim();

                    if (!line.startsWith("name") && !line.contains("---") && line.contains("motadata-"))
                    {
                        var tokens = line.split("\\s+");

                        cpuPercent = Double.parseDouble(tokens[1]) / processors;

                        //convert in mb
                        memoryMB = Double.parseDouble(tokens[2]) / 1024;

                        break;
                    }
                }

                if (cpuPercent > 0 || memoryMB > 0)
                {
                    diagnosis(Double.parseDouble(String.format("%.2f", cpuPercent)), Double.parseDouble(String.format("%.2f", memoryMB)), agent);
                }
            }

            future.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception);
        }
    }

    private void diagnosisLinuxAgentHealth(long processId, int processors, String agent, Promise<Void> future)
    {
        var arguments = new String[]{"bash", "-c", "ps -eo pid,rss,pcpu | grep " + processId};

        var cpuPercent = 0.0;

        var memoryMB = 0.0;

        try (var bufferedReader = new BufferedReader(new InputStreamReader(Runtime.getRuntime().exec(arguments).getInputStream())))
        {
            var line = bufferedReader.readLine();

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("Linux command %s output for process id %s is %s", Arrays.toString(arguments), processId, line));
            }

            if (line != null && !line.trim().isEmpty())
            {
                var tokens = line.trim().split("\\s+");

                cpuPercent = Double.parseDouble(tokens[2]) / processors;

                //convert in mb
                memoryMB = Double.parseDouble(tokens[1]) / 1024;
            }

            if (cpuPercent > 0 || memoryMB > 0)
            {
                diagnosis(Double.parseDouble(String.format("%.2f", cpuPercent)), Double.parseDouble(String.format("%.2f", memoryMB)), agent);
            }

            future.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception);
        }
    }

    private void diagnosis(Double cpuPercent, Double memoryMB, String agent)
    {
        try
        {
            if (AgentConfigUtil.getAgentHealthThresholds().get(agent) != null)
            {
                var tokens = AgentConfigUtil.getAgentHealthThresholds().get(agent).trim().split(",");

                if (memoryMB > Double.parseDouble(tokens[AgentConstants.MEMORY_WARNING_THRESHOLD_MB]))
                {
                    if (memoryWarningThresholdEvents.get(agent) != null)
                    {
                        memoryWarningThresholdEvents.compute(agent, (key, value) -> value + 1);
                    }
                    if (memoryMB > Double.parseDouble(tokens[AgentConstants.MEMORY_CRITICAL_THRESHOLD_MB]) && memoryCriticalThresholdEvents.get(agent) != null)
                    {
                        memoryCriticalThresholdEvents.compute(agent, (key, value) -> value + 1);
                    }
                }
                else
                {
                    memoryWarningThresholdEvents.put(agent, 0);

                    memoryCriticalThresholdEvents.put(agent, 0);
                }

                if (cpuPercent > Double.parseDouble(tokens[AgentConstants.CPU_WARNING_THRESHOLD_PERCENT]))
                {
                    if (cpuWarningThresholdEvents.get(agent) != null)
                    {

                        cpuWarningThresholdEvents.compute(agent, (key, value) -> value += 1);
                    }

                    if (cpuPercent > Double.parseDouble(tokens[AgentConstants.CPU_CRITICAL_THRESHOLD_PERCENT]) && cpuCriticalThresholdEvents.get(agent) != null)
                    {
                        cpuCriticalThresholdEvents.compute(agent, (key, value) -> value += 1);
                    }
                }
                else
                {
                    cpuWarningThresholdEvents.put(agent, 0);

                    cpuCriticalThresholdEvents.put(agent, 0);
                }

                // means health is clear so send clear event
                if (memoryWarningThresholdEvents.get(agent) == 0 && cpuWarningThresholdEvents.get(agent) == 0)
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(AgentConstants.AGENT_TYPE, agent).put("memory", memoryMB).put("cpu", cpuPercent)
                            .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_HEALTH_CLEAR));

                    LOGGER.debug(String.format("%s agent clear event sent successfully with cpu [%s] and memory [%s]", agent, cpuPercent, memoryMB));
                }
                else
                {
                    notifyServer(agent, memoryMB, cpuPercent);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void notifyServer(String agent, Double memory, Double cpu)
    {
        try
        {
            var context = new JsonObject().put(AgentConstants.AGENT_TYPE, agent).put(AgentConstants.MEMORY_MB, memory).put(AgentConstants.CPU_PERCENT, cpu)
                    .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID());

            if (memoryWarningThresholdEvents.get(agent) >= rollingWindows.get())
            {
                if (memoryCriticalThresholdEvents.get(agent) >= rollingWindows.get())
                {
                    context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_MEMORY_CRITICAL);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

                    LOGGER.debug(String.format("%s agent memory goes into critical state with value [%s]", agent, memory));

                    memoryCriticalThresholdEvents.put(agent, 0);
                }
                else
                {
                    context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_MEMORY_WARNING);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

                    LOGGER.debug(String.format("%s agent memory goes into warning state with value [%s]", agent, memory));
                }

                memoryWarningThresholdEvents.put(agent, 0);
            }

            if (cpuWarningThresholdEvents.get(agent) >= rollingWindows.get())
            {
                if (cpuCriticalThresholdEvents.get(agent) >= rollingWindows.get())
                {
                    context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CPU_CRITICAL);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

                    LOGGER.debug(String.format("%s agent cpu goes into warning state with value [%s]", agent, cpu));

                    cpuCriticalThresholdEvents.put(agent, 0);
                }
                else
                {
                    context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CPU_WARNING);

                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

                    LOGGER.debug(String.format("%s agent cpu goes into critical state with value [%s]", agent, cpu));
                }

                cpuWarningThresholdEvents.put(agent, 0);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        LOGGER.info("undeploying HealthUtil... ");

        promise.complete();
    }
}
