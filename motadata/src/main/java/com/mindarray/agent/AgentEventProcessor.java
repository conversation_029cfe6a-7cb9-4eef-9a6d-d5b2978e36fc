/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.agent;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.agent.AgentConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.db.DBConstants.FIELD_NAME;
import static com.mindarray.db.DBConstants.TBL_AGENT;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.CACHE_NAME;
import static com.mindarray.nms.NMSConstants.DISCOVERABLE_INSTANCES;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.util.CommonUtil.isNotNullOrEmpty;

/**
 * The AgentEventProcessor class is responsible for processing events related to agents in the Motadata system.
 * <p>
 * This verticle handles various agent-related operations including:
 * <ul>
 *   <li>Agent registration and configuration</li>
 *   <li>Agent health monitoring and status updates</li>
 *   <li>Processing agent responses and events</li>
 *   <li>Managing agent logs and diagnostics</li>
 *   <li>Coordinating communication between agents and other system components</li>
 * </ul>
 * <p>
 * The processor listens for events on the event bus and takes appropriate actions based on the event type.
 * It maintains agent state information and ensures proper synchronization between agents and the Motadata server.
 * <p>
 * This class is a central component in the agent management system, handling the lifecycle of agents from
 * registration through operation to deactivation.
 */
public class AgentEventProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(AgentEventProcessor.class, GlobalConstants.MOTADATA_AGENT, "Agent Event Processor");

    /**
     * Set of agent UUIDs that are currently registered with the system
     */
    private final Set<String> uuids = new HashSet<>();

    /**
     * Flag indicating whether a write operation to agent cache is pending
     */
    private boolean writePending = false;

    /**
     * Flag indicating whether log streaming is currently active
     */
    private boolean streaming = false;

    /**
     * Flag indicating whether the agent cache has been modified and needs to be synchronized
     */
    private boolean dirty = false;

    /**
     * Initializes the AgentEventProcessor verticle and sets up event bus consumers for agent-related events.
     * <p>
     * This method configures multiple event bus consumers to handle different types of agent events:
     * <ul>
     *   <li>Change notifications for agent status and configuration</li>
     *   <li>Agent registration and heartbeat events</li>
     *   <li>Agent health monitoring events</li>
     *   <li>Agent log and diagnostic events</li>
     *   <li>Agent configuration update events</li>
     * </ul>
     * <p>
     * It also sets up periodic timers for:
     * <ul>
     *   <li>Writing agent cache data to persistent storage</li>
     *   <li>Checking agent health status</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        // Consumer for handling agent streaming and deletion notifications
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            // Process different types of change notifications
            switch (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                // Enable log streaming when requested
                case START_LOG_TAIL -> streaming = true;

                // Disable log streaming when requested
                case STOP_LOG_TAIL -> streaming = false;

                // Handle agent deletion by removing its UUID from the tracked set
                case DELETE_AGENT ->
                {
                    if (message.body().containsKey(AGENT_UUID))
                    {
                        uuids.remove(message.body().getString(AGENT_UUID));
                    }
                }
            }


        }).exceptionHandler(LOGGER::error);

        // Consumer for handling application mapper and cache update notifications
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(CHANGE_NOTIFICATION_TYPE))
                {
                    var changeNotificationType = ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE));

                    // Handle application mapper changes by notifying all packet agents
                    if (changeNotificationType == ChangeNotificationType.ADD_APPLICATION_MAPPER ||
                            changeNotificationType == ChangeNotificationType.UPDATE_APPLICATION_MAPPER ||
                            changeNotificationType == ChangeNotificationType.DELETE_APPLICATION_MAPPER)
                    {
                        // Notify all packet agents about the application mapper changes
                        AgentConfigStore.getStore().flatMap().values().forEach(agent ->
                                notifyPacketAgent(agent,
                                        new JsonObject(agent.getString(AGENT_CONFIGS)),
                                        ApplicationMapperConfigStore.getStore().getPorts(),
                                        agent.getLong(ID)));
                    }
                    // Handle cache update notifications for high availability synchronization
                    else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                    {
                        if (dirty)
                        {
                            // Reset dirty flag and notify observers about the updated cache
                            dirty = false;
                            HAConstants.notifyObserver(new JsonObject()
                                    .put(CACHE_NAME, "agent-cache")
                                    .put(RESULT, vertx.fileSystem().readFileBlocking(AGENT_CACHE_PATH)));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Set up a periodic timer that runs every minute (60000 ms) to maintain agent connections and update cache
        vertx.setPeriodic(60000, timer ->
                vertx.<Void>executeBlocking(future ->
                {
                    try
                    {
                        // Write current agent cache data to persistent storage
                        // This ensures agent information is preserved across system restarts
                        writeAgentCache();

                        // Iterate through all registered agents in the configuration store
                        for (var agent : AgentConfigStore.getStore().flatMap().values())
                        {
                            // Send heartbeat signal to the agent to maintain connection
                            // This helps detect if the agent is still responsive
                            publishEvent(new JsonObject()
                                    .put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC)
                                    .put(AGENT_UUID, agent.getString(AGENT_UUID))
                                    .put(ID, agent.getLong(ID))
                                    .put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT));

                            // Send heartbeat signal to the Motadata manager
                            // This informs the manager that this agent is being monitored
                            publishEvent(new JsonObject()
                                    .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                    .put(AGENT_UUID, agent.getString(AGENT_UUID))
                                    .put(ID, agent.getLong(ID))
                                    .put(EVENT_TYPE, EVENT_MOTADATA_MANAGER_HEARTBEAT));

                            // Log trace information if trace logging is enabled
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("sending acknowledgement to agent %s", agent.getString(AGENT_UUID)));
                            }
                        }

                        future.complete();
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        future.fail(exception);
                    }
                }, false, result ->
                {
                }));

        /**
         * Set up a periodic timer that runs every 120 seconds to check agent availability status
         *
         * This timer performs several important functions:
         * 1. Checks if agents have been unresponsive for too long
         * 2. Handles agent flap states (rapid changes between available/unavailable)
         * 3. Updates agent status based on heartbeat information
         * 4. Updates associated object status based on agent status
         */
        vertx.setPeriodic(1000 * 120, timer ->
                vertx.<Void>executeBlocking(future ->
                {
                    // Iterate through all registered agents to check their status
                    for (var agent : AgentConfigStore.getStore().flatMap().values())
                    {
                        try
                        {
                            // Get the cached information for this agent
                            var cache = AgentCacheStore.getStore().getItem(agent.getLong(ID));

                            if (cache != null)
                            {
                                var id = agent.getLong(ID);

                                // Get the duration for which the agent has been in its current state
                                var duration = AgentCacheStore.getStore().getDuration(agent.getLong(ID));

                                // Handle agent flap protection
                                // If agent has been in disabled state for more than 90 seconds,
                                // automatically re-enable it to prevent permanent disablement
                                if (duration > 0 && (DateTimeUtil.currentSeconds() - duration) >= 90)
                                {
                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("%s agent action state enabled, reason : %s",
                                                ObjectConfigStore.getStore().getAgentObjectName(id),
                                                "flap timer limit exceeded..."));
                                    }

                                    // Update agent cache to enable the agent
                                    AgentCacheStore.getStore().updateItem(id, cache.put(AGENT_DISABLE, NO));
                                    AgentCacheStore.getStore().updateDuration(id, NO);

                                    // Notify UI about the agent state change
                                    EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject()
                                            .put(EVENT_TYPE, EVENT_AGENT_ACTION)
                                            .put(ID, agent.getLong(ID))
                                            .put(AGENT_UUID, agent.getString(AGENT_UUID))
                                            .put(AGENT_DISABLE, NO));
                                }

                                // Merge cached agent data with current agent data
                                agent.mergeIn(cache);

                                // Initialize event timestamp if not present
                                if (!cache.containsKey(EVENT_TIMESTAMP))
                                {
                                    cache.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                                }

                                // Initialize duration if not present
                                // This is a temporary workaround until the motadata stop service is implemented
                                if (!cache.containsKey(DURATION))
                                {
                                    cache.put(DURATION, DateTimeUtil.currentSeconds());
                                    AgentCacheStore.getStore().updateItem(id, cache);
                                }

                                // Check if agent has not sent a heartbeat for longer than the ping interval plus buffer
                                // This indicates the agent may be down or unreachable
                                if (DateTimeUtil.currentSeconds() - cache.getLong(EVENT_TIMESTAMP) >= AGENT_PING_INTERVAL_SECONDS + 10)
                                {
                                    // Initialize heartbeat state if not present
                                    if (!cache.containsKey(MotadataAppManager.HEARTBEAT_STATE))
                                    {
                                        cache.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_REACHABLE);
                                    }

                                    // If agent was previously running but now unreachable,
                                    // update its state and calculate how long it's been down
                                    if (cache.getString(MotadataAppManager.HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                                    {
                                        cache.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_REACHABLE)
                                                .put(DURATION, DateTimeUtil.currentSeconds() - (AGENT_PING_INTERVAL_SECONDS + 10));
                                    }

                                    // Update agent status based on its status type (heartbeat or ping)
                                    cache.put(STATUS, agent.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentStatusType.HEARTBEAT.getName())
                                            ? agent.getString(AGENT_STATUS)
                                            : cache.getString(STATUS, STATUS_DOWN));

                                    // If agent is enabled, update the status of its associated object
                                    if (agent.containsKey(AGENT_STATE) && agent.getString(AGENT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                                    {
                                        // Update the object status based on agent status
                                        updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)),
                                                cache.getString(STATUS),
                                                DateTimeUtil.currentSeconds(),
                                                cache.getString(MotadataAppManager.HEARTBEAT_STATE),
                                                cache.getLong(DURATION));

                                        // Update the status of all instances associated with this object
                                        updateInstanceStatus(ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)),
                                                cache.getString(STATUS));
                                    }
                                }
                            }
                        }

                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    future.complete();

                }, false, result ->
                {
                }));


        // Set up consumer for handling agent-specific events
        // This consumer processes various types of events from agents including discovery, health, and data events
        vertx.eventBus().<JsonObject>localConsumer(EVENT_AGENT, message ->
        {
            try
            {
                // Verify the message has a valid event type
                if (!message.body().isEmpty() && isNotNullOrEmpty(message.body().getString(EventBusConstants.EVENT_TYPE)))
                {
                    // Fetch additional context information for the event
                    // This enriches the event with necessary data for processing
                    fetchContext(message.body()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var event = asyncResult.result();

                            // Log event details if trace logging is enabled
                            if (CommonUtil.traceEnabled())
                            {
                                // For response stream events, log minimal information to avoid excessive logging
                                if (event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_RESPONSE_STREAM))
                                {
                                    LOGGER.trace(String.format("received %s agent response from %s",
                                            event.getString(AGENT_TYPE), event.getString(AGENT_UUID)));
                                }
                                else
                                {
                                    // For other events, log the full event details (with sensitive fields removed)
                                    LOGGER.trace(String.format("received event %s",
                                            CommonUtil.removeSensitiveFields(event, true)));
                                }
                            }

                            // Process different types of agent events
                            switch (event.getString(EventBusConstants.EVENT_TYPE))
                            {
                                // Handle discovery events from agents
                                // These events contain information about discovered resources
                                case EVENT_DISCOVERY ->
                                    // Forward the discovery response with 100% progress indication
                                        vertx.eventBus().send(EVENT_DISCOVERY_RESPONSE,
                                                event.put(Discovery.DISCOVERY_PROGRESS, 100.00));

                                // Handle rediscovery events from agents
                                // These events update existing resource information
                                case EVENT_REDISCOVER ->
                                {
                                    // Extract any error information from the event
                                    ErrorMessageConstants.extractError(event);

                                    // Update agent configuration if this is a process or Windows service rediscovery
                                    // This allows dynamic updates to agent configuration based on rediscovered resources
                                    if ((event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.PROCESS.getName()) ||
                                            event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.WINDOWS_SERVICE.getName()))
                                            && event.getString(AGENT_CONFIGS) != null)
                                    {
                                        // Update the agent configuration in the database
                                        Bootstrap.configDBService().update(TBL_AGENT,
                                                new JsonObject()
                                                        .put(FIELD_NAME, ID)
                                                        .put(VALUE, event.getLong(AIOpsObject.OBJECT_AGENT)),
                                                new JsonObject()
                                                        .put(AGENT_CONFIGS, event.getString(AGENT_CONFIGS)),
                                                // Use provided username or default
                                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                                // Use provided remote address or system address
                                                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        // Refresh the agent configuration in the cache
                                                        AgentConfigStore.getStore().updateItem(event.getLong(AIOpsObject.OBJECT_AGENT));
                                                    }
                                                });
                                    }

                                    // Forward the rediscovery response to interested components
                                    vertx.eventBus().send(EVENT_REDISCOVER_RESPONSE, event);
                                }

                                case EVENT_PLUGIN_ENGINE ->
                                {
                                    ErrorMessageConstants.extractError(event);

                                    if (event.containsKey(WorkerUtil.WORKER_CONTEXT)) // worker id tracker
                                    {
                                        vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_WORKER_TRACKER, event);
                                    }
                                    else
                                    {
                                        vertx.eventBus().send(EVENT_PLUGIN_ENGINE_RESPONSE, event);
                                    }
                                }

                                case EVENT_METRIC_POLL -> vertx.eventBus().publish(EVENT_METRIC_POLL_RESPONSE, event);

                                case EVENT_AGENT_RESPONSE_STREAM ->
                                {
                                    var agent = AgentConfigStore.getStore().getItem(AgentConfigStore.getStore().getAgentId(event.getString(AGENT_UUID)));

                                    // bug - 4279
                                    if (agent != null && agent.getString(AGENT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && event.getString(EVENT_CONTEXT) != null)
                                    {
                                        var value = new String(CodecUtil.toBytes(event.getBinary(EVENT_CONTEXT)));

                                        switch (AgentConstants.Agent.valueOfName(event.getString(AGENT_TYPE)))
                                        {
                                            case METRIC ->
                                                    vertx.eventBus().send(EventBusConstants.EVENT_METRIC_AGENT, event.put(EVENT_CONTEXT, value));

                                            case PACKET ->
                                                    vertx.eventBus().send(EventBusConstants.EVENT_PACKET_AGENT, event.put(EVENT_CONTEXT, value));

                                            case LOG, WINDOWS_EVENT_LOG ->
                                            {
                                                if (LicenseUtil.updateUsedLogQuota(value.getBytes().length))
                                                {
                                                    if (!event.containsKey(EventBusConstants.EVENT_TIMESTAMP))
                                                    {
                                                        event.put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                                                    }

                                                    event.put(EventBusConstants.EVENT, value)
                                                            .put(EventBusConstants.EVENT_VOLUME_BYTES, value.getBytes().length)
                                                            .put(LogEngineConstants.RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                                                            .put(EventBusConstants.EVENT_SOURCE, ObjectConfigStore.getStore().getItemByAgentId(AgentConfigStore.getStore().getAgentId(event.getString(AGENT_UUID))).getString(AIOpsObject.OBJECT_IP));

                                                    event.remove(EVENT_CONTEXT);

                                                    event.remove(EVENT_TYPE);

                                                    event.remove(AGENT_TYPE);

                                                    event.remove(AGENT_UUID);

                                                    vertx.eventBus().send(EventBusConstants.EVENT_LOG, event);

                                                    if (streaming)
                                                    {
                                                        vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName()).put(EVENT_CONTEXT, event));
                                                    }

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("log received from agent: %s", CommonUtil.removeSensitiveFields(event, false).encodePrettily()));
                                                    }
                                                }
                                            }

                                            default ->
                                            {
                                            }
                                        }

                                    }

                                    else
                                    {
                                        LOGGER.warn(String.format("agent %s polling skipped, reason : agent is null or agent state is disabled", event.getString(AGENT_UUID)));
                                    }
                                }

                                case EVENT_AGENT_REGISTRATION -> registerAgent(event);

                                //config related changes event
                                case EVENT_AGENT_CONFIGURATION_CHANGE ->
                                {
                                    complete(event);

                                    if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
                                    {
                                        var agent = AgentConfigStore.getStore().getItem(event.getLong(ID));

                                        if (event.containsKey(AGENT_TYPE))
                                        {
                                            if (event.getJsonArray(AGENT_TYPE).contains(AGENT))
                                            {
                                                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESTART)
                                                        .put(ID, agent.getLong(ID))
                                                        .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                                                        .put(USER_NAME, event.getString(USER_NAME))
                                                        .put(SESSION_ID, event.getString(SESSION_ID)));
                                            }

                                            else
                                            {
                                                for (var agentType : event.getJsonArray(AGENT_TYPE))
                                                {
                                                    publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESTART)
                                                            .put(ID, agent.getLong(ID))
                                                            .put(AGENT_TYPE, agentType)
                                                            .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(AGENT_DISABLE, NO)
                                                            .put(USER_NAME, event.getString(USER_NAME))
                                                            .put(SESSION_ID, event.getString(SESSION_ID)));
                                                }
                                            }
                                        }

                                        // for manual provision of rediscovered objects we need to update config and in that case need to clear remote event router track event
                                        if (event.containsKey(EVENT_REPLY) && event.getString(EVENT_REPLY).equalsIgnoreCase(YES))
                                        {
                                            clear(event);
                                        }

                                        AgentConstants.updateMetric(event);

                                        //update new configuration
                                        Bootstrap.configDBService().update(TBL_AGENT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                                                new JsonObject().put(AGENT_CONFIGS, event.getString(AGENT_CONFIGS)),
                                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    AgentConfigStore.getStore().updateItem(event.getLong(ID));

                                                    event.put(MESSAGE, String.format(InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(event.getLong(ID))));

                                                    publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE)
                                                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                                            .put(ID, event.getLong(ID)).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(event.getLong(ID)))
                                                            .put(MESSAGE, event.getString(MESSAGE)));
                                                });
                                    }

                                    else
                                    {
                                        publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE).put(STATUS, GlobalConstants.STATUS_FAIL)
                                                .put(ID, event.getLong(ID)).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(event.getLong(ID)))
                                                .put(MESSAGE, event.getString(MESSAGE)));
                                    }

                                    publishUserNotificationEvent(event);
                                }

                                case EVENT_AGENT_DOWNLOAD_LOG -> downloadAgentLog(event);

                                case EVENT_AGENT_HEARTBEAT ->
                                {
                                    var agentUUID = event.getString(AGENT_UUID);

                                    if (AgentConfigStore.getStore().getAgentId(agentUUID) == NOT_AVAILABLE)  //an un-registered agent is trying to contact so publish event to agent as an unknown...so agent will re-register it
                                    {
                                        vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EVENT_AGENT_UNKNOWN)
                                                .put(AGENT_UUID, agentUUID).put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP)));
                                    }

                                    else
                                    {
                                        var agent = AgentConfigStore.getStore().getItem(AgentConfigStore.getStore().getAgentId(agentUUID));

                                        if (agent != null)
                                        {
                                            if (agent.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentStatusType.HEARTBEAT.getName()))
                                            {
                                                if (agent.containsKey(AGENT_STATE) && agent.getString(AGENT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                                                {
                                                    updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)), STATUS_UP, DateTimeUtil.currentSeconds(), NMSConstants.STATE_RUNNING, event.getLong(DURATION));
                                                }

                                                else
                                                {
                                                    LOGGER.warn(String.format("agent %s heartbeat skipped, reason: agent is disabled", agent.getLong(ID)));
                                                }
                                            }
                                        }

                                        else
                                        {
                                            LOGGER.warn(String.format("agent %s heartbeat skipped, reason: agent not found in store", agentUUID));
                                        }
                                    }
                                }

                                case EVENT_AGENT_CPU_WARNING, EVENT_AGENT_MEMORY_WARNING ->
                                        processHealthEvent(event, Severity.WARNING);

                                case EVENT_AGENT_CPU_CRITICAL, EVENT_AGENT_MEMORY_CRITICAL ->
                                        processHealthEvent(event, Severity.CRITICAL);

                                case EVENT_AGENT_HEALTH_CLEAR -> processHealthEvent(event, Severity.CLEAR);

                                case EVENT_ACKNOWLEDGEMENT -> vertx.eventBus().publish(EVENT_ACKNOWLEDGEMENT, event);

                                case EVENT_PROGRESS_UPDATE ->
                                        vertx.eventBus().send(event.getString(EventBusConstants.EVENT_TYPE), event);

                                case EVENT_LICENSE_UPDATE ->
                                {

                                    for (var agent : AgentConfigStore.getStore().flatMap().values())
                                    {
                                        publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.AGENT_TOPIC).put(AGENT_UUID, agent.getString(AGENT_UUID))
                                                .put(ID, agent.getLong(ID)).put(EVENT_TYPE, EVENT_LICENSE_UPDATE).put(EVENT, event.getJsonObject(EVENT)));
                                    }

                                }

                                default ->
                                        LOGGER.warn(String.format("unknown event type: %s for agent %s event", event.getString(EVENT_TYPE), event.getString(AGENT_UUID)));
                            }
                        }
                    });
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        });

        promise.complete();
    }

    /**
     * Enriches an event with agent ID and session ID information from the event tracker.
     * <p>
     * This method retrieves additional context information for events that have an event ID.
     * It queries the event tracker service to get the agent ID and session ID associated with
     * the event, and then adds this information to the event object.
     * <p>
     * For events that don't have an event ID (such as heartbeat, health, and registration events),
     * the method simply returns the original event without modification.
     * <p>
     * This context enrichment is essential for properly routing and processing agent events
     * in the system, as it ensures all events have the necessary identification information.
     *
     * @param event The event object to enrich with context information
     * @return A Future containing the enriched event object
     */
    private Future<JsonObject> fetchContext(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        // Check if the event has an event ID
        // Heartbeat, health, and registration events don't have this field
        if (event.getLong(EVENT_ID) != null)
        {
            // Query the event tracker service to get context information for this event
            vertx.eventBus().<JsonObject>request(EVENT_TRACKER, event, reply ->
            {
                if (reply.succeeded())
                {
                    // Extract context information from the reply
                    var context = reply.result().body();

                    // Add agent ID and session ID to the original event
                    event.put(ID, context.getJsonObject(EVENT_CONTEXT).getLong(ID))
                            .put(APIConstants.SESSION_ID, context.getJsonObject(EVENT_CONTEXT).getString(APIConstants.SESSION_ID));
                }

                // Complete the promise with the enriched event
                promise.complete(event);
            });
        }
        else
        {
            // For events without an event ID, return the original event
            promise.complete(event);
        }

        return promise.future();
    }

    /**
     * Processes health events received from agents and updates the agent health diagnosis information.
     * <p>
     * This method handles health-related events (such as CPU/memory warnings or critical alerts)
     * from agents and their child agents. It updates the health diagnosis information in the
     * agent cache store with the current CPU and memory metrics, along with the severity level.
     * <p>
     * The health diagnosis information is structured hierarchically:
     * <ul>
     *   <li>Each agent has a health diagnosis object</li>
     *   <li>Within that object, each child agent (by type) has its own health context</li>
     *   <li>Each health context contains severity level and resource metrics (CPU, memory)</li>
     * </ul>
     * <p>
     * This information is used for monitoring agent health and triggering alerts when resource
     * usage exceeds configured thresholds.
     *
     * @param context  The event context containing agent information and health metrics
     * @param severity The severity level of the health event (WARNING, CRITICAL, or CLEAR)
     */
    private void processHealthEvent(JsonObject context, GlobalConstants.Severity severity)
    {
        // Get the agent ID from the agent UUID in the context
        var agentId = AgentConfigStore.getStore().getAgentId(context.getString(AGENT_UUID));

        // Retrieve the agent's cached information
        var item = AgentCacheStore.getStore().getItem(agentId);

        if (item != null)
        {
            // Get the existing health diagnosis object or create a new one if it doesn't exist
            var healthContext = item.containsKey(AGENT_HEALTH_DIAGNOSIS)
                    ? item.getJsonObject(AGENT_HEALTH_DIAGNOSIS)
                    : new JsonObject();

            // Get the health context for this specific agent type (child agent)
            // or create a new one if it doesn't exist
            var childAgentHealthContext = healthContext.containsKey(context.getString(AGENT_TYPE))
                    ? healthContext.getJsonObject(context.getString(AGENT_TYPE))
                    : new JsonObject();

            // Update the health context with severity level and resource metrics
            childAgentHealthContext.put(SEVERITY, severity.name())
                    .put(MEMORY_MB, new JsonObject()
                            .put(STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.RESULT, context.getValue(MEMORY_MB)))
                    .put(CPU_PERCENT, new JsonObject()
                            .put(STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.RESULT, context.getValue(CPU_PERCENT)));

            // Update the health diagnosis with the updated child agent health context
            healthContext.put(context.getString(AGENT_TYPE), childAgentHealthContext);

            // Update the agent cache with the updated health diagnosis information
            AgentCacheStore.getStore().updateItem(agentId, item.put(AGENT_HEALTH_DIAGNOSIS, healthContext));
        }
    }

    /**
     * Processes agent log download events and notifies the UI of the result.
     * <p>
     * This method handles the response from an agent after a log download request.
     * It processes the event and sends appropriate notifications to the UI based on
     * whether the download was successful or failed.
     * <p>
     * For successful downloads, it includes the file name and a UUID that can be used
     * to access the downloaded log file. For failed downloads, it includes an error message.
     * <p>
     * The method expects the following fields in the event:
     * <ul>
     *   <li>ID - The agent ID</li>
     *   <li>STATUS - "succeed" or "fail" indicating the download status</li>
     *   <li>RESULT - UUID of the file if successful, or error details if failed</li>
     *   <li>FILE_NAME - Name of the downloaded log file (for successful downloads)</li>
     *   <li>SESSION_ID - Session ID for routing UI notifications</li>
     *   <li>UI_EVENT_UUID - Optional UUID for tracking the UI event</li>
     * </ul>
     *
     * @param event The event containing agent log download information
     */
    private void downloadAgentLog(JsonObject event)
    {
        try
        {
            // Extract the agent ID from the event
            var agentId = event.getLong(ID);

            // Mark the event as complete in the event tracker
            complete(event);

            // Handle successful log download
            if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
            {
                // Create notification context with download success information
                var context = new JsonObject()
                        .put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_DOWNLOAD_LOG)
                        .put(ID, agentId)
                        .put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                        .put(FILE_NAME, event.getString(FILE_NAME))
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, event.getString(RESULT));

                // Include UI event UUID if present for tracking in the UI
                if (event.getString(UI_EVENT_UUID) != null)
                {
                    context.put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID));
                }

                // Publish success notification to the UI
                publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_AGENT, context);
            }
            else
            {
                // Publish failure notification to the UI with error message
                publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_AGENT,
                        new JsonObject()
                                .put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_DOWNLOAD_LOG)
                                .put(ID, agentId)
                                .put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                .put(MESSAGE, event.getString(MESSAGE)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent registration events and registers agents with the Motadata system.
     * <p>
     * This method handles the initial registration of agents when they first connect to the system.
     * It performs several important tasks:
     * <ul>
     *   <li>Extracts agent information (UUID, hostname, IP, OS, etc.)</li>
     *   <li>Checks if the agent is already registered</li>
     *   <li>Creates or updates agent configuration in the database</li>
     *   <li>Associates the agent with an object in the system</li>
     *   <li>Notifies other components about the new agent</li>
     *   <li>Sends registration confirmation back to the agent</li>
     * </ul>
     * <p>
     * The registration process is critical for establishing the agent's identity in the system
     * and enabling proper monitoring and management of the agent.
     *
     * @param event The event containing agent registration information
     */
    private void registerAgent(JsonObject event)
    {
        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("agent registration event : %s", event));
        }

        try
        {
            var agentUUID = event.getString(AGENT_UUID);

            var configs = new JsonObject(event.getString(AGENT_CONFIGS));

            var agentName = event.getString(AIOpsObject.OBJECT_HOST) != null ? event.getString(AIOpsObject.OBJECT_HOST) : event.getString(AIOpsObject.OBJECT_IP);

            var type = NMSConstants.getType(event.getString(AGENT_OS_NAME));

            var agent = AgentConfigStore.getStore().getItem(AgentConfigStore.getStore().getAgentId(agentUUID));

            var metricAgentStatus = configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS, "");

            var version = event.getString(AGENT_VERSION);

            var objectName = new AtomicReference<>(agentName);

            if (agent == null)
            {
                if (!uuids.contains(agentUUID)) // for duplicate agent registration event
                {
                    // for health agent no need to check for license

                    if (RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, configs.getJsonObject(AgentConstants.AGENT).getString(AGENT_UUID)) != null || ((NO.equalsIgnoreCase(metricAgentStatus) || LicenseUtil.licensedObjectConsumed()) && LicenseUtil.LICENSE_EDITION.get() != LicenseUtil.LicenseEdition.ESSENTIAL))
                    {
                        var objectId = ObjectConfigStore.getStore().getObjectIdByIP(event.getString(AIOpsObject.OBJECT_IP), type);

                        if (objectId == NOT_AVAILABLE)
                        {
                            objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(objectName.get());

                            if (objectId != NOT_AVAILABLE)
                            {
                                objectName.set(agentName + "(" + event.getString(AIOpsObject.OBJECT_IP) + ")");

                                objectId = NOT_AVAILABLE;
                            }
                        }

                        if (objectId == NOT_AVAILABLE)
                        {
                            var item = ObjectConfigStore.getStore().flatItemByMultipleValues(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP),
                                    AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.OTHER.getName(), AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PING.getName());

                            if (item != null)
                            {
                                objectId = item.getInteger(AIOpsObject.OBJECT_ID);
                            }
                        }

                        if (objectId == NOT_AVAILABLE)
                        {
                            uuids.add(agentUUID);

                            var context = new JsonObject().put(AGENT_UUID, agentUUID)
                                    .put(AGENT_CONFIGS, configs.encode())
                                    .put(AGENT_DEFAULT_CONFIG, configs.encode())
                                    .put(AGENT_VERSION, version)
                                    .put(AGENT_BUSINESS_HOUR_PROFILE, DEFAULT_ID)
                                    .put(AGENT_STATE, NMSConstants.State.ENABLE.name())
                                    .put(ID, CommonUtil.newId())
                                    .put(AGENT_STATUS_TYPE, AgentStatusType.HEARTBEAT.getName())
                                    .put(AGENT_STATUS, STATUS_DOWN) // MOTADATA-837
                                    .put(OBJECTS, event.getJsonArray(OBJECTS));

                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.AGENT_TOPIC).put(AGENT_UUID, agentUUID)
                                    .put(ID, context.getLong(ID)).put(EVENT_TYPE, EVENT_LICENSE_UPDATE).put(EVENT, LicenseUtil.getLicenseDetails()));

                            Bootstrap.configDBService().save(TBL_AGENT,
                                    context,
                                    event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS
                                    , result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            AgentConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                                            {
                                                //send object provision event for newly register agent
                                                vertx.eventBus().send(EVENT_OBJECT_PROVISION, new JsonObject().put(AIOpsObject.OBJECT_TYPE, type.getName())
                                                        .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.getCategory(type).getName())
                                                        .put(AIOpsObject.OBJECT_GROUPS, NMSConstants.getGroups(type))
                                                        .put(AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE, DEFAULT_ID)
                                                        .put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP))
                                                        .put(AIOpsObject.OBJECT_HOST, agentName).put(AIOpsObject.OBJECT_NAME, objectName.get())
                                                        .put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT.name())
                                                        .put(AIOpsObject.OBJECT_TARGET, objectName.get())
                                                        .put(AIOpsObject.OBJECT_AGENT, result.result()));

                                                var artifact = new JsonObject().put(ID, context.getLong(ID))
                                                        .put(MotadataApp.ARTIFACT_ID, agentUUID)
                                                        .put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name().toLowerCase())
                                                        .put(MotadataApp.ARTIFACT_VERSION, version)
                                                        .put(MotadataApp.ARTIFACT_MODE, InstallationMode.STANDALONE.name().toLowerCase());   // agent will be standalone only

                                                if (MotadataConfigUtil.compatible(BootstrapType.AGENT.name(), version))
                                                {
                                                    LOGGER.info(String.format("agent %s registered successfully...", result.result()));

                                                    AgentCacheStore.getStore().updateItem(result.result(), new JsonObject().put(STATUS, STATUS_UP)
                                                            .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING)
                                                            .put(EVENT_TIMESTAMP, event.getLong(DURATION))
                                                            .put(DURATION, event.getLong(DURATION)));

                                                    if (MotadataConfigUtil.upgradable(MotadataConfigUtil.getVersion(), version))
                                                    {
                                                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED);
                                                    }
                                                    else
                                                    {
                                                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                                                    }

                                                    notifyPacketAgent(AgentConfigStore.getStore().getItem(result.result()), configs, ApplicationMapperConfigStore.getStore().getPorts(), result.result());

                                                    publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_SUCCEED).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, "Agent registered successfully"));
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("failed to register new agent. version is not compatible %s ", version));

                                                    artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NOT_COMPATIBLE);

                                                    AgentCacheStore.getStore().updateItem(result.result(), new JsonObject().put(STATUS, STATUS_DOWN)
                                                            .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING)
                                                            .put(EVENT_TIMESTAMP, event.getLong(DURATION))
                                                            .put(DURATION, event.getLong(DURATION)));

                                                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                            .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID)
                                                            .put(ID, context.getLong(ID)));

                                                    publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format("failed to register agent. version is not compatible %s ", event.getString(AGENT_VERSION))));
                                                }

                                                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(EVENT_COPY_REQUIRED, false));
                                            });
                                        }

                                        else
                                        {
                                            // agent failed to save in config db... need to remove item from store that we manually add to not make blocking code of register agent to check license
                                            AgentConfigStore.getStore().deleteItem(context.getLong(ID));

                                            LOGGER.warn(String.format("failed to register agent, reason: %s", result.cause().getMessage()));

                                            vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                    .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID));

                                            publishUserNotificationEvent(new JsonObject().put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_REGISTRATION_FAILED, agentName, result.cause().getMessage())));
                                        }
                                    });
                        }

                        else
                        {
                            LOGGER.warn(String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT, agentName));

                            vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                    .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID));

                            publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format(ErrorMessageConstants.PROVISION_OBJECT_FAILED_DUPLICATE_OBJECT, agentName)));
                        }
                    }
                    else
                    {
                        if (LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.ESSENTIAL)
                        {
                            LOGGER.warn("failed to register agent, reason: licence edition essential ...");

                            vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                    .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID));

                            publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_REGISTRATION_FAILED, agentName, "licence edition essential")));

                        }
                        else
                        {
                            LOGGER.warn("failed to register agent, reason: licence limit exceeded ...");

                            vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                    .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID));

                            publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_REGISTRATION_FAILED, agentName, "licence limit exceeded")));
                        }
                    }
                }
                else
                {
                    LOGGER.warn(String.format("duplicate registration request received : %s", event.encode()));
                }
            }

            else
            {
                var artifact = ArtifactConfigStore.getStore().getItem(AgentConfigStore.getStore().getAgentId(agentUUID));

                artifact.put(MotadataApp.ARTIFACT_TYPE, artifact.getString(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()).toLowerCase())    // required as all registered agents having "AGENT" as ARTIFACT_TYPE, but it must be "agent" for health screen.
                        .put(MotadataApp.ARTIFACT_MODE, InstallationMode.STANDALONE.name().toLowerCase());                                          // for agent mode is STANDALONE only.

                artifact.put(MotadataApp.ARTIFACT_VERSION, version);

                if (MotadataConfigUtil.compatible(BootstrapType.AGENT.name(), version))
                {
                    var item = AgentCacheStore.getStore().getItem(AgentConfigStore.getStore().getAgentId(agentUUID));

                    if (MotadataConfigUtil.upgradable(MotadataConfigUtil.getVersion(), version))
                    {
                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_UPGRADE_REQUIRED);
                    }
                    else
                    {
                        artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS);
                    }

                    var object = ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID));

                    if (agent.getString(AGENT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                    {
                        LOGGER.info(String.format("agent %s registration skipped, reason: agent is present in store with state %s ", agent.getString(AGENT_UUID), agent.getString(AGENT_STATE)));

                        if (event.getJsonArray(OBJECTS).contains(object.getString(AIOpsObject.OBJECT_IP)))
                        {
                            event.put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP));
                        }

/*                        publish(UI_NOTIFICATION_AGENT, new JsonObject().mergeIn(item).put(ID, agent.getLong(ID)).put(AGENT_UUID, agent.getString(AGENT_UUID))
                                .put(DURATION, convertTime(event.getLong(DURATION))).put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT));*/

                        updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)), STATUS_UP, event.getLong(DURATION), NMSConstants.STATE_RUNNING, event.getLong(DURATION));

                        Bootstrap.configDBService().update(TBL_AGENT,
                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, AgentConfigStore.getStore().getAgentId(agentUUID)),
                                new JsonObject().put(AGENT_CONFIGS, configs.encode()).put(AGENT_VERSION, event.getValue(AGENT_VERSION)).put(OBJECTS, event.getJsonArray(OBJECTS)),
                                event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                                event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        // if manually change metric agent state at that time when agent re-register then motadata will check previous and new status and enable/disable metrics
                                        AgentConstants.updateMetric(event.put(ID, agent.getLong(ID)));

                                        AgentConfigStore.getStore().updateItem(AgentConfigStore.getStore().getAgentId(agentUUID));

                                        LOGGER.info(String.format("update requested for agent object : %s with host : %s and ip : %s", agent.getString(AGENT_UUID), agentName, event.getString(AIOpsObject.OBJECT_IP)));

                                        vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_AGENT.name()).put(EVENT_CONTEXT, event.put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))).put(EVENT_COPY_REQUIRED, false));
                                    }
                                });
                    }

                    else
                    {
                        AgentCacheStore.getStore().updateItem(agent.getLong(ID), item);

                        publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_REGISTRATION_DUPLICATE_ERROR, agentName)));
                    }
                }
                else
                {
                    LOGGER.warn(String.format("failed to register existing agent. version is not compatible %s ", event.getString(AGENT_VERSION)));

                    updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID)), STATUS_DOWN, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()), NMSConstants.STATE_NOT_RUNNING, DateTimeUtil.currentSeconds());

                    artifact.put(ERROR_CODE, ErrorCodes.ERROR_CODE_NOT_COMPATIBLE);

                    Bootstrap.configDBService().update(TBL_AGENT,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, AgentConfigStore.getStore().getAgentId(agentUUID)),
                            new JsonObject().put(AGENT_CONFIGS, configs.encode()).put(AGENT_VERSION, event.getValue(AGENT_VERSION)),
                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    // if manually change metric agent state at that time when agent re-register then motadata will check previous and new status and enable/disable metrics
                                    AgentConstants.updateMetric(event.put(ID, agent.getLong(ID)));

                                    AgentConfigStore.getStore().updateItem(AgentConfigStore.getStore().getAgentId(agentUUID));

                                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                            .put(EVENT_TYPE, EVENT_AGENT_STOP).put(AGENT_UUID, agentUUID)
                                            .put(ID, AgentConfigStore.getStore().getAgentId(agentUUID)));
                                }
                                else
                                {
                                    LOGGER.warn("failed to save agent details in config db ");
                                }
                            });

                    publishUserNotificationEvent(new JsonObject().put(STATUS, STATUS_FAIL).put(EVENT_TYPE, EVENT_AGENT_REGISTRATION).put(MESSAGE, String.format("failed to register agent. version is not compatible %s ", event.getString(AGENT_VERSION))));
                }

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(EVENT_COPY_REQUIRED, false));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void notifyPacketAgent(JsonObject agent, JsonObject configs, JsonArray ports, long id)
    {
        if (configs.getJsonObject(AGENT) != null && configs.getJsonObject(AGENT).getString(PACKET_AGENT_STATUS) != null &&
                configs.getJsonObject(AGENT).getString(PACKET_AGENT_STATUS).equalsIgnoreCase(YES))
        {
            configs.getJsonObject(AGENT_PACKET).put(PACKET_AGENT_APPLICATIONS, ports);

            publishEvent(new JsonObject().put(EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE)
                    .put(ID, id).put(AGENT_UUID, agent.getString(AGENT_UUID))
                    .put(USER_NAME, DEFAULT_USER)
                    .put(AGENT_DISABLE, NO)
                    .put(AGENT_CONFIGS, configs.encode()));
        }
    }

    private void updateStatus(JsonObject object, String status, long timestamp, String heartbeatState, long duration)
    {
        if (object != null)
        {
            var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName()));

            if (item != null)
            {
                item.mergeIn(object).mergeIn(item.getJsonObject(Metric.METRIC_CONTEXT));

                item.put(RESULT, new JsonObject().put(STATUS, status).put(AIOpsObject.OBJECT_TARGET, object.getString(AIOpsObject.OBJECT_IP))).put(EVENT_TYPE, EVENT_METRIC_POLL).put(STATUS, STATUS_SUCCEED).put(EVENT_TIMESTAMP, timestamp).put(MotadataAppManager.HEARTBEAT_STATE, heartbeatState).put(DURATION, duration);

                vertx.eventBus().publish(EVENT_METRIC_POLL_RESPONSE, item);
            }
        }
    }

    //when Agent is Down, we are setting Unreachable Status for its child instances (Services and Processes).
    private void updateInstanceStatus(JsonObject object, String status)
    {

        if (STATUS_DOWN.equalsIgnoreCase(status))
        {
            for (var metric : MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)))
            {
                var context = metric.getJsonObject(Metric.METRIC_CONTEXT, new JsonObject());

                metric.remove(Metric.METRIC_CONTEXT);

                var objects = context.getJsonArray(OBJECTS);

                if (objects != null && !objects.isEmpty() && DISCOVERABLE_INSTANCES.containsKey(metric.getString(Metric.METRIC_PLUGIN)))
                {
                    var items = new JsonArray();

                    var instance = DISCOVERABLE_INSTANCES.get(metric.getString(Metric.METRIC_PLUGIN));

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var item = new JsonObject();

                        var entry = objects.getJsonObject(index);

                        if (entry.containsKey(AIOpsObject.OBJECT_NAME))
                        {
                            item.put(instance, entry.getString(AIOpsObject.OBJECT_NAME));
                        }
                        else
                        {
                            item.put(instance, entry.getString(instance));
                        }

                        if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                        {
                            item.put(PORT, CommonUtil.getInteger(entry.getString(AIOpsObject.OBJECT_NAME).split("\\(")[0].trim()));
                        }

                        item.put(STATUS, STATUS_UNREACHABLE);

                        items.add(item);
                    }

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Agent %s is Down,sending event to put Unreachable state in metric group %s", object.getString(AIOpsObject.OBJECT_IP), metric.getString(Metric.METRIC_NAME)));
                    }

                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, metric.mergeIn(object).mergeIn(context).put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(RESULT, new JsonObject().put(DISCOVERABLE_INSTANCES.get(metric.getString(Metric.METRIC_PLUGIN)), items)));
                }
            }
        }
    }

    private void complete(JsonObject event)
    {
        try
        {
            var eventId = event.getLong(EventBusConstants.EVENT_ID);

            if (event.containsKey(ID)) //improvements - 3636 -> now allow user to perform another actions
            {
                var item = AgentCacheStore.getStore().getItem(event.getLong(ID));

                AgentCacheStore.getStore().updateDuration(event.getLong(ID), NO);

                if (item.containsKey(AGENT_DISABLE) && item.getString(AGENT_DISABLE).equalsIgnoreCase(YES))
                {
                    AgentCacheStore.getStore().updateItem(event.getLong(ID), item.put(AGENT_DISABLE, NO));

                    EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().put(EVENT_TYPE, EVENT_AGENT_ACTION).put(ID, event.getLong(ID)).put(AGENT_DISABLE, NO));
                }
            }

            if (eventId != null)
            {
                vertx.eventBus().publish(EventBusConstants.EVENT_CLEAR_CONTEXT, new JsonObject()
                        .put(EventBusConstants.EVENT_ID, eventId)); //clear the context of load balancer

                if (event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, event);
                }

                else if (event.getString(STATUS).equalsIgnoreCase(STATUS_ABORT))
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_ABORT, event);
                }

                else
                {
                    vertx.eventBus().send(EventBusConstants.EVENT_FAIL, event);
                }

            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Future<Void> writeAgentCache()
    {
        var promise = Promise.<Void>promise();

        if (!writePending)
        {
            writePending = true;

            vertx.<Void>executeBlocking(future ->
            {
                try
                {
                    if (AgentConfigStore.getStore().existItems())
                    {
                        LOGGER.debug("agent cache updating started successfully....");

                        var items = new JsonArray();

                        for (var agent : AgentConfigStore.getStore().flatMap().values())
                        {
                            var cache = AgentCacheStore.getStore().getItem(agent.getLong(ID));

                            //remove health on system startup and only consider duration and event timestamp
                            cache.remove(AGENT_HEALTH_DIAGNOSIS);

                            cache.remove(AGENT_DISABLE); // do not need to save disable status on cache

                            items.add(cache.put(ID, agent.getLong(ID)));
                        }

                        if (!vertx.fileSystem().existsBlocking(AGENT_CACHE_PATH))
                        {
                            vertx.fileSystem().createFileBlocking(AGENT_CACHE_PATH);
                        }

                        var bytes = CodecUtil.compress(items.encode());

                        if (bytes != null && bytes.length > 0)
                        {
                            dirty = true;

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("updating %s bytes of agent cache", bytes.length));
                            }

                            vertx.fileSystem().writeFileBlocking(AGENT_CACHE_PATH, Buffer.buffer(bytes));

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug("agent cache updated successfully....");
                            }
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                future.complete();
            }, false, result ->
            {
                writePending = false;

                promise.complete();
            });
        }

        else
        {
            promise.complete();
        }

        return promise.future();
    }

    private void publishEvent(JsonObject event)
    {
        if (!event.isEmpty() && isNotNullOrEmpty(event.getString(EVENT_TYPE)) && isNotNullOrEmpty(event.getString(AGENT_UUID)))
        {
            event.put(EVENT_ID, CommonUtil.newEventId());

            if (CommonUtil.isNotNullOrEmpty(event.getString(AGENT_DISABLE))) //improvements - 3636 -> prevent user to perform any action
            {
                AgentCacheStore.getStore().updateItem(event.getLong(ID), AgentCacheStore.getStore().getItem(event.getLong(ID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));

                AgentCacheStore.getStore().updateDuration(event.getLong(ID), event.getString(AGENT_DISABLE));

                EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().put(EVENT_TYPE, EVENT_AGENT_ACTION).put(ID, event.getLong(ID))
                        .put(AGENT_UUID, event.getString(AGENT_UUID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));
            }

            if (!PASSOVER_EVENTS.contains(event.getString(EVENT_TYPE)))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                        .put(USER_NAME, event.remove(USER_NAME))
                        .put(EventBusConstants.EVENT_TYPE, event.getString(EVENT_TYPE)));
            }

            if (AGENT_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, event);
            }
            else if (MOTADATA_MANAGER_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, event);
            }

        }

        else
        {
            LOGGER.warn(String.format("failed to send event %s", event));
        }

    }

    @Override
    public void stop(Promise<Void> promise)
    {
        LOGGER.info(String.format("stopping engine %s", this.getClass().getSimpleName()));

        writeAgentCache().onComplete(result -> promise.complete());
    }
}
