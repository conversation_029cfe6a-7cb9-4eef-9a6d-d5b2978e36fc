/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DashboardConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.USER_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.VISUALIZATION_DASHBOARD_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.Dashboard.*;
import static com.mindarray.api.User.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestDashboard
{

    private static final Logger LOGGER = new Logger(TestDashboard.class, MOTADATA_API, "Test Dashboard");
    private static long dashboardId;
    private final JsonObject USER_CONTEXT = new JsonObject();

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateDashboardAccessTypePublic(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"dashboard.name\":\"Dashbord Test\",\"dashboard.category\":\"Default\",\"dashboard.access.type\":\"public\",\"dashboard.users\":[],\"dashboard.favourite\":\"no\",\"dashboard.tabs\":[],\"dashboard.default\":\"no\",\"style\":{\"font.size\":\"medium\",\"h.gap\":8,\"v.gap\":8,\"row.height\":50},\"dashboard.context\":{\"dashboard.widgets\":[],\"dashboard.users\":[]}}");

        context.put(DASHBOARD_NAME, "Dashboard Test " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_DASHBOARD_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            dashboardId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(DashboardConfigStore.getStore().getItem(dashboardId).isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateDashboardAccessTypePrivate(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"dashboard.name\":\"Dashbord Test\",\"dashboard.category\":\"Default\",\"dashboard.access.type\":\"public\",\"dashboard.users\":[],\"dashboard.favourite\":\"no\",\"dashboard.tabs\":[],\"dashboard.default\":\"no\",\"style\":{\"font.size\":\"medium\",\"h.gap\":8,\"v.gap\":8,\"row.height\":50},\"dashboard.context\":{\"dashboard.widgets\":[],\"dashboard.users\":[]}}");

        context.put(DASHBOARD_NAME, "Dashboard Test " + System.currentTimeMillis()).put(DASHBOARD_ACCESS_TYPE, DASHBOARD_ACCESS_TYPE_PRIVATE);

        TestAPIUtil.post(VISUALIZATION_DASHBOARD_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            dashboardId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(DashboardConfigStore.getStore().getItem(dashboardId).isEmpty());

                            var userContext = new JsonObject().put(USER_PREFERENCES, new JsonObject().put(USER_HOME_SCREEN, dashboardId));

                            TestAPIUtil.put(USER_API_ENDPOINT + DEFAULT_ID, userContext,
                                    testContext.succeeding(result ->
                                            testContext.verify(() ->
                                            {
                                                UserConfigStore.getStore().updateItem(DEFAULT_ID);

                                                testContext.completeNow();
                                            })));
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateDashboardCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = DashboardConfigStore.getStore().getItem(dashboardId);

        context.put(DASHBOARD_NAME, "Dashboard Test " + System.currentTimeMillis()).put(DASHBOARD_CATEGORY, NMSConstants.Category.SERVER.getName());

        TestAPIUtil.put(VISUALIZATION_DASHBOARD_API_ENDPOINT + "/" + dashboardId, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(DashboardConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "Dashboard"), LOGGER, testInfo.getTestMethod().get().getName());

                            Assertions.assertEquals(NMSConstants.Category.SERVER.getName(), DashboardConfigStore.getStore().getItem(response.bodyAsJsonObject().getLong(GlobalConstants.ID)).getString(DASHBOARD_CATEGORY));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetDashboard(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(VISUALIZATION_DASHBOARD_API_ENDPOINT + "/" + dashboardId, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, dashboardId, DashboardConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testDeleteDashboard(VertxTestContext testContext)
    {
        TestAPIUtil.delete(VISUALIZATION_DASHBOARD_API_ENDPOINT + "/" + dashboardId,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(DashboardConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "Dashboard"));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDashboardGetAll(VertxTestContext testContext)
    {
        TestAPIUtil.get(VISUALIZATION_DASHBOARD_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var items = body.getJsonObject(RESULT);

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testCreateDashboard(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"dashboard.name\":\"Dashbord Test\",\"dashboard.category\":\"Default\",\"dashboard.access.type\":\"public\",\"dashboard.users\":[],\"dashboard.favourite\":\"no\",\"dashboard.tabs\":[],\"dashboard.default\":\"yes\",\"style\":{\"font.size\":\"medium\",\"h.gap\":8,\"v.gap\":8,\"row.height\":50},\"dashboard.context\":{\"dashboard.widgets\":[],\"dashboard.users\":[]}}");

        context.put(DASHBOARD_NAME, "Default Dashboard Test " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_DASHBOARD_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            dashboardId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(DashboardConfigStore.getStore().getItem(dashboardId).isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCheckPrivateDashboardSecurity(VertxTestContext testContext)
    {

        JsonObject userContext = new JsonObject()
                .put(USER_FIRST_NAME, "motadata")
                .put(USER_LAST_NAME, "motadata")
                .put(USER_EMAIL, "<EMAIL>")
                .put(USER_MOBILE, 1234567899)
                .put(USER_STATUS, "yes")
                .put(USER_ROLE, DEFAULT_ID)
                .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
                .put(USER_TYPE, USER_TYPE_SYSTEM);

        createUser(testContext, userContext.put(USER_NAME, "motadata123").put(USER_PASSWORD, "Motadata@123"), "motadata123").onComplete(result ->
        {
            if (result.succeeded())
            {
                createUser(testContext, userContext.put(USER_NAME, "mindarray123").put(USER_PASSWORD, "Mindarray@123"), "mindarray123").onComplete(asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var dashboardContext = new JsonObject()
                                .put(DASHBOARD_NAME, "Dashboard Test " + System.currentTimeMillis())
                                .put(DASHBOARD_CATEGORY, NMSConstants.Category.SERVER.getName())
                                .put(DASHBOARD_ACCESS_TYPE, DASHBOARD_ACCESS_TYPE_PRIVATE)
                                .put(DASHBOARD_CONTEXT, new JsonObject().put(DASHBOARD_WIDGETS, new JsonArray()).put(DASHBOARD_USERS, new JsonArray().add(USER_CONTEXT.getJsonObject("mindarray123").getLong(GlobalConstants.ID))))
                                .put(DASHBOARD_USERS, new JsonArray().add(USER_CONTEXT.getJsonObject("mindarray123").getLong(GlobalConstants.ID)));

                        TestAPIUtil.post(VISUALIZATION_DASHBOARD_API_ENDPOINT, dashboardContext.put("dashboard.timeline", new JsonObject()),
                                testContext.succeeding(response ->
                                        testContext.verify(() ->
                                        {
                                            dashboardId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                                            var items = DashboardConfigStore.getStore().getItem(dashboardId).getJsonArray(DASHBOARD_USERS);

                                            assertEquals(1, items.size());

                                            Assertions.assertTrue(items.contains(USER_CONTEXT.getJsonObject("mindarray123").getLong(GlobalConstants.ID)));

                                            Assertions.assertFalse(items.contains(USER_CONTEXT.getJsonObject("motadata123").getLong(GlobalConstants.ID)));

                                            testContext.completeNow();

                                        })));
                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    Future<JsonObject> createUser(VertxTestContext testContext, JsonObject context, String key)
    {
        var promise = Promise.<JsonObject>promise();

        TestAPIUtil.post(USER_API_ENDPOINT, context,

                testContext.succeeding(response ->
                {
                    USER_CONTEXT.put(key, new JsonObject().put(GlobalConstants.ID, response.bodyAsJsonObject().getLong(GlobalConstants.ID)));

                    promise.complete();

                }));

        return promise.future();
    }

}

