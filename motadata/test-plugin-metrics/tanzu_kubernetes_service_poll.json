{"************": {"result": {"tanzu.kubernetes.service": [{"tanzu.kubernetes.service": "default-kubernetes", "tanzu.kubernetes.service.cluster.ip.address": "*********", "tanzu.kubernetes.service.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.service.name": "kubernetes", "tanzu.kubernetes.service.namespace": "default", "tanzu.kubernetes.service.ports": "443/TCP", "tanzu.kubernetes.service.type": "ClusterIP"}, {"tanzu.kubernetes.service": "default-prometheus", "tanzu.kubernetes.service.cluster.ip.address": "************", "tanzu.kubernetes.service.creation.time": "Mon, Apr 14 2025, 08:12", "tanzu.kubernetes.service.name": "prometheus", "tanzu.kubernetes.service.namespace": "default", "tanzu.kubernetes.service.ports": "9090/TCP", "tanzu.kubernetes.service.type": "NodePort"}, {"tanzu.kubernetes.service": "kube-system-kube-dns", "tanzu.kubernetes.service.application": "kube-dns", "tanzu.kubernetes.service.cluster.ip.address": "*********0", "tanzu.kubernetes.service.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.service.name": "kube-dns", "tanzu.kubernetes.service.namespace": "kube-system", "tanzu.kubernetes.service.ports": "53/UDP,53/TCP,9153/TCP", "tanzu.kubernetes.service.type": "ClusterIP"}, {"tanzu.kubernetes.service": "kube-system-metrics-server", "tanzu.kubernetes.service.application": "metrics-server", "tanzu.kubernetes.service.cluster.ip.address": "*************", "tanzu.kubernetes.service.creation.time": "Fri, Apr 11 2025, 05:26", "tanzu.kubernetes.service.name": "metrics-server", "tanzu.kubernetes.service.namespace": "kube-system", "tanzu.kubernetes.service.ports": "443/TCP", "tanzu.kubernetes.service.type": "ClusterIP"}], "tanzu.kubernetes.services": 4}}}