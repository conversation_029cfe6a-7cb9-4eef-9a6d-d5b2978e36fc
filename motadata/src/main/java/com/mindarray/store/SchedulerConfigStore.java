/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  23-Jun-2025		Vismit      		MOTADATA-6312: Updated getSchedulerObjects() and getItems() flow to support one scheduler on bulk devices
 *  24-Jun-2025     Yash Tiwari     MOTADATA-6528 : initalizing Topology CAche Store Store after initialization of Scheduler Config Store
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.Scheduler;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import java.util.ArrayList;
import java.util.HashSet;

public class SchedulerConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SchedulerConfigStore.class, GlobalConstants.MOTADATA_STORE, "Scheduler Config Store");

    private static final SchedulerConfigStore STORE = new SchedulerConfigStore();

    private SchedulerConfigStore()
    {
        super(DBConstants.TBL_SCHEDULER, LOGGER, false);
    }

    public static SchedulerConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    TopologyCacheStore.getStore().initStore().onComplete(response -> promise.complete());
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init scheduler config store...");

                    LOGGER.error(exception);
                }
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Checks if a scheduler object with the specified job type contains the given ID in the specified field.
     *
     * @param key      the key to retrieve the context JSON object from the item
     * @param field    the field within the context JSON object to check for the ID
     * @param id       the ID to search for within the field's JsonArray
     * @param jobType  the job type to match against the scheduler job type
     * @return true if any item contains the specified ID in the given field and matches the job type, false otherwise
     */
    public boolean containsObject(String key, String field, Long id, String jobType)
    {
        for (var item : items.values())
        {
            var context = item.getJsonObject(key);

            if (context != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) &&
                    CommonUtil.isNotNullOrEmpty(context.getJsonArray(field)) && context.getJsonArray(field).contains(id))
            {
                return true;
            }
        }

        return false;
    }


    /**
     * Retrieves a JsonArray containing all unique object IDs (e.g., discovery/scheduler IDs)
     * from the specified field of the context JSON object for items matching the given job type.
     *
     * @param key      the key to retrieve the context JSON object from each item
     * @param field    the field within the context JSON object to extract IDs from
     * @param values   a JsonArray of values (currently not used in filtering)
     * @param jobType  the job type to match against the scheduler job type
     * @return         a JsonArray containing all unique object IDs found in the specified field
     */
    public JsonArray getSchedulerObjects(String key, String field, JsonArray values, String jobType)
    {
        var objects = new HashSet<Long>();

        for (var item : items.values())
        {
            var context = item.getJsonObject(key);

            if (context != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) &&
                    CommonUtil.isNotNullOrEmpty(context.getJsonArray(field)))
            {
                objects.addAll(context.getJsonArray(field).getList());
            }
        }

        return new JsonArray(new ArrayList(objects));
    }

    /**
     * Retrieves a JsonArray of scheduler item IDs that match the specified criteria.
     *
     * @param key      the key to retrieve the context JSON object from each item
     * @param field    the field within the context JSON object to check for the ID
     * @param id       the ID to search for within the field's JsonArray
     * @param jobType  the job type to match against the scheduler job type
     * @return         a JsonArray containing the IDs of items that match the criteria
     */
    public JsonArray getItems(String key, String field, Long id, String jobType)
    {
        var objects = new HashSet<Long>();

        for (var item : items.values())
        {
            var context = item.getJsonObject(key);

            if (context != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) &&
                    CommonUtil.isNotNullOrEmpty(context.getJsonArray(field)) &&
                    context.getJsonArray(field).contains(id))
            {
                objects.add(item.getLong(GlobalConstants.ID));
            }
        }

        return new JsonArray(new ArrayList(objects));
    }

    /**
     * Retrieves a JsonArray of scheduler item IDs whose context JSON object's specified field
     * contains any of the provided values and matches the given job type.
     *
     * @param key      the key to retrieve the context JSON object from each item
     * @param field    the field within the context JSON object to check for the values
     * @param values   a JsonArray of values to match against the field's JsonArray
     * @param jobType  the job type to match against the scheduler job type
     * @return         a JsonArray containing the IDs of items that match the criteria
     */
    public JsonArray getItems(String key, String field, JsonArray values, String jobType)
    {
        var objects = new HashSet<Long>();

        for (var item : items.values())
        {
            var context = item.getJsonObject(key);

            if (context != null && item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(jobType) &&
                    CommonUtil.isNotNullOrEmpty(context.getJsonArray(field)) &&
                    context.getJsonArray(field).stream().anyMatch(values::contains))
            {
                objects.add(item.getLong(GlobalConstants.ID));
            }
        }

        return new JsonArray(new ArrayList(objects));
    }
}
