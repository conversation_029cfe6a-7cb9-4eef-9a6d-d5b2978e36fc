/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.UserRoleConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.auth.JWTOptions;
import io.vertx.ext.auth.PubSecKeyOptions;
import io.vertx.ext.auth.jwt.JWTAuth;
import io.vertx.ext.auth.jwt.JWTAuthOptions;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import static com.mindarray.ErrorMessageConstants.API_FIELD_REQUIRED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(62 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestQuery
{
    private static final Logger LOGGER = new Logger(TestQuery.class, MOTADATA_API, "Test Query");
    private static final JsonObject HISTOGRAM_QUERY_CONTEXT = new JsonObject("{\"queries\":[{\"aggregator\":\"avg\",\"data.point\":\"network.service~latency.ms\"}],\"data.filter\":{\"groups\":[{\"conditions\":[{\"operand\":\"network.service~instance.name\",\"operator\":\"contain\",\"value\":\"8\"}],\"filter\":\"include\",\"operator\":\"or\"},{\"conditions\":[{\"operand\":\"network.service~instance.name\",\"operator\":\"=\",\"value\":\"8081 (Apache HTTP)\"}],\"filter\":\"include\",\"operator\":\"or\"}],\"filter\":\"exclude\",\"operator\":\"and\"},\"result.filter\":{\"conditions\":[{\"operand\":\"network.service~latency.ms.avg\",\"operator\":\"<\",\"value\":10},{\"operand\":\"network.service~latency.ms.avg\",\"operator\":\">\",\"value\":8}],\"filter\":\"include\",\"operator\":\"and\"},\"timeline\":{\"from.date\":\"2024/11/28\",\"from.time\":\"00:00:00\",\"to.date\":\"2024/11/29\",\"to.time\":\"00:00:00\",\"relative.timeline\":\"custom\"},\"limit\":10,\"granularity\":\"5 m\",\"type\":\"metric\",\"result.by\":[\"network.service\"]}\n");
    private static final JsonObject GRID_QUERY_CONTEXT = new JsonObject("{\"queries\":[{\"aggregator\":\"avg\",\"data.point\":\"network.service~latency.ms\"}],\"data.filter\":{\"groups\":[{\"conditions\":[{\"operand\":\"network.service~instance.name\",\"operator\":\"contain\",\"value\":\"8\"}],\"filter\":\"include\",\"operator\":\"or\"},{\"conditions\":[{\"operand\":\"network.service~instance.name\",\"operator\":\"=\",\"value\":\"8081 (Apache HTTP)\"}],\"filter\":\"include\",\"operator\":\"or\"}],\"filter\":\"exclude\",\"operator\":\"and\"},\"result.filter\":{\"conditions\":[{\"operand\":\"network.service~latency.ms.avg\",\"operator\":\"<\",\"value\":10},{\"operand\":\"network.service~latency.ms.avg\",\"operator\":\">\",\"value\":8}],\"filter\":\"include\",\"operator\":\"and\"},\"timeline\":{\"from.date\":\"2024/11/28\",\"from.time\":\"00:00:00\",\"to.date\":\"2024/11/29\",\"to.time\":\"00:00:00\",\"relative.timeline\":\"custom\"},\"type\":\"metric\",\"result.by\":[\"network.service\"]}\n");
    private static String accessToken;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws Exception
    {
        var publicKeyBuffer = TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "public-key.pem");

        var secretKeyBuffer = TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem");

        var jwtAuth = JWTAuth.create(TestUtil.vertx(), new JWTAuthOptions()
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(publicKeyBuffer))
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(secretKeyBuffer)));

        var item = UserConfigStore.getStore().getItem(DEFAULT_ID);

        accessToken = jwtAuth.generateToken(
                new JsonObject().put(USER_NAME, "admin").put(GlobalConstants.ID, item.getLong(ID)).put(USER_PERMISSIONS,
                        UserRoleConfigStore.getStore().getItem(10000000000002L)   //default read-only role
                                .getJsonArray(UserRole.USER_ROLE_CONTEXT).add("query:" + RequestType.GET.getName()).add("query:" + RequestType.POST.getName()).add("user:" + RequestType.POST.getName()).add("token:" + RequestType.POST.getName())),
                new JWTOptions()
                        .setAlgorithm(ALGO_RS512)
                        .setSubject(API_VERSION)
                        .setIssuer(API_AUTHOR).setExpiresInMinutes(365 * 24 * 60));

        LOGGER.info(String.format("Read-Only User role : %s ", UserRoleConfigStore.getStore().getItem(10000000000002L).encode()));

        testContext.completeNow();
    }

    @Test
    @Order(1)
    void testQueryVisualizationGridCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var widget = new JsonObject("{\"visualization.name\":\"test\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[49125924693]}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[]}},\"visualization.result.by\":[\"monitor\"],\"container.type\":\"dashboard\",\"_type\":\"1\"}");

        widget.put(VisualizationConstants.VISUALIZATION_NAME, widget.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        var categories = widget.getJsonArray("visualization.data.sources");

        categories.getJsonObject(0).getJsonArray("data.points").getJsonObject(0).put("entities", new JsonArray().add(ObjectConfigStore.getStore().getItemByIP("**********")));

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, widget, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), widget, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestAPIUtil.get(VISUALIZATION_QUERY_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var result = asyncResult.result().bodyAsJsonObject();

                            LOGGER.info(String.format("API response : %s ", result.encode()));

                            Assertions.assertNotNull(result);

                            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                            Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                            Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });

                })));
    }

    @Test
    @Order(2)
    void testQueryVisualizationInvalidCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var widget = new JsonObject("{\"visualization.name\":\"test\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[49125924693]}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[]}},\"visualization.result.by\":[\"monitor\"],\"container.type\":\"dashboard\",\"_type\":\"1\"}");

        widget.put(VisualizationConstants.VISUALIZATION_NAME, widget.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis())
                .put(VisualizationConstants.VISUALIZATION_CATEGORY, VisualizationConstants.VisualizationCategory.CHART.getName());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, widget, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), widget, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestAPIUtil.get(VISUALIZATION_QUERY_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID), new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var result = asyncResult.result().bodyAsJsonObject();

                            LOGGER.info(String.format("API response : %s ", result.encode()));

                            Assertions.assertNotNull(result);

                            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                            Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                            Assertions.assertFalse(result.getJsonArray(ERRORS).isEmpty());

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });

                })));
    }

    @Test
    @Order(3)
    void testQueryVisualizationInvalidId(VertxTestContext testContext, TestInfo testInfo)
    {
        var widget = new JsonObject("{\"visualization.name\":\"test\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[49125924693]}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[]}},\"visualization.result.by\":[\"monitor\"],\"container.type\":\"dashboard\",\"_type\":\"1\"}");

        widget.put(VisualizationConstants.VISUALIZATION_NAME, widget.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, widget, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(WidgetConfigStore.getStore(), widget, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.WIDGET.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestAPIUtil.get(VISUALIZATION_QUERY_API_ENDPOINT + "/123", new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var result = asyncResult.result().bodyAsJsonObject();

                            LOGGER.info(String.format("API response : %s ", result.encode()));

                            Assertions.assertNotNull(result);

                            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                            Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                            Assertions.assertFalse(result.getJsonArray(ERRORS).isEmpty());

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });

                })));
    }

    @Test
    @Order(4)
    void testQueryVisualizationHistogramInvalidParams(VertxTestContext testContext)
    {
        var param = "queries";

        var context = HISTOGRAM_QUERY_CONTEXT.copy();

        context.remove(param);

        TestAPIUtil.post(QUERY_METRIC_HISTOGRAM_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), context, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                Assertions.assertNotNull(result);

                Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(String.format(API_FIELD_REQUIRED, param), result.getString(MESSAGE));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }

        });
    }

    @Test
    @Order(5)
    void testQueryVisualizationHistogram(VertxTestContext testContext)
    {
        var context = HISTOGRAM_QUERY_CONTEXT.copy();

        TestAPIUtil.post(QUERY_METRIC_HISTOGRAM_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), context, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                Assertions.assertNotNull(result);

                LOGGER.info(String.format("API response : %s ", result.encode()));

                Assertions.assertNotNull(result);

                Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }

        });
    }

    @Test
    @Order(4)
    void testQueryVisualizationAggregationInvalidParams(VertxTestContext testContext)
    {
        var param = "timeline";

        var context = GRID_QUERY_CONTEXT.copy();

        context.remove(param);

        TestAPIUtil.post(QUERY_METRIC_AGGREGATIONS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), context, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                Assertions.assertNotNull(result);

                Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(String.format(API_FIELD_REQUIRED, param), result.getString(MESSAGE));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }

        });
    }

    @Test
    @Order(5)
    void testQueryVisualizationAggregations(VertxTestContext testContext)
    {
        var context = GRID_QUERY_CONTEXT.copy();

        TestAPIUtil.post(QUERY_METRIC_AGGREGATIONS_API_ENDPOINT, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), context, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                Assertions.assertNotNull(result);

                LOGGER.info(String.format("API response : %s ", result.encode()));

                Assertions.assertNotNull(result);

                Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }

        });
    }

    @Test
    @Order(6)
    void testGetStatusByMonitorId(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertNotNull(id);

        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/" + id + "/status", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                response = response.getJsonObject(RESULT);

                Assertions.assertNotNull(response);

                Assertions.assertTrue(response.containsKey(STATUS));

                Assertions.assertFalse(response.getString(STATUS).isEmpty());

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(7)
    void testGetPollInfoByMonitorId(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItem().getLong(ID);

        Assertions.assertNotNull(id);

        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/" + id + "/poll-info", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                response = response.getJsonObject(RESULT);

                Assertions.assertNotNull(response);

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(8)
    void testGetInstancesByMonitorId(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItem().getLong(ID);

        Assertions.assertNotNull(id);

        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/" + id + "/instances", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                response = response.getJsonObject(RESULT);

                Assertions.assertNotNull(response);

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(9)
    void testGetMonitorsByGroupId(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/" + ObjectConfigStore.getStore().getItem().getJsonArray(AIOpsObject.OBJECT_GROUPS).getLong(0) + "/group", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                var results = response.getJsonArray(RESULT);

                Assertions.assertNotNull(results);

                testContext.completeNow();

            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(10)
    void testGetMonitorsByStatus(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/status?status=up", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                var results = response.getJsonArray(RESULT);

                Assertions.assertNotNull(results);

                for (var index = 0; index < results.size(); index++)
                {
                    Assertions.assertNotNull(results.getJsonObject(index));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(STATUS));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(AIOpsObject.OBJECT_IP));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(AIOpsObject.OBJECT_ID));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(ID));
                }

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(11)
    void testGetMonitorsByInvalidStatus(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/status", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_FAIL, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(ErrorCodes.ERROR_CODE_BAD_REQUEST, response.getString(ERROR_CODE));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(12)
    void testGetMonitorsBySeverity(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/severity?severity=clear", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                var results = response.getJsonArray(RESULT);

                Assertions.assertNotNull(results);

                for (var index = 0; index < results.size(); index++)
                {
                    Assertions.assertNotNull(results.getJsonObject(index));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(SEVERITY));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(AIOpsObject.OBJECT_IP));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(AIOpsObject.OBJECT_ID));

                    Assertions.assertTrue((results.getJsonObject(index)).containsKey(ID));
                }

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(13)
    void testGetMonitorsByInvalidSeverity(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "/severity", new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_FAIL, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(ErrorCodes.ERROR_CODE_BAD_REQUEST, response.getString(ERROR_CODE));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(14)
    void testGetObjects(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT, new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                var items = response.getJsonArray(RESULT);

                Assertions.assertNotNull(items);

                Assertions.assertTrue(response.containsKey(STATUS));

                Assertions.assertFalse(response.getString(STATUS).isEmpty());

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(15)
    void testGetObjectsByFilter(VertxTestContext testContext)
    {

        var filter = new JsonObject().put("key", "object.type").put("value", new JsonArray().add("Switch"));

        TestAPIUtil.get(OBJECT_QUERY_API_ENDPOINT + "?" + FILTER + "=" + filter.encode(), new JsonObject().put(org.apache.http.HttpHeaders.AUTHORIZATION, "Bearer " + accessToken), result ->
        {
            if (result.succeeded())
            {
                var response = result.result().bodyAsJsonObject();

                Assertions.assertNotNull(response);

                LOGGER.info(String.format("API response : %s ", response.encode()));

                Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, response.getInteger(RESPONSE_CODE));

                var items = response.getJsonArray(RESULT);

                Assertions.assertNotNull(items);

                Assertions.assertTrue(response.containsKey(STATUS));

                Assertions.assertFalse(response.getString(STATUS).isEmpty());

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

//    @Test
//    @Order(16)
//    void testQueryVisualizationInternalErrorScenario(VertxTestContext testContext)
//    {
//        var mockResponse = new JsonObject()
//                .put(STATUS, STATUS_FAIL)
//                .put(MESSAGE, ErrorMessageConstants.INTERNAL_ERROR)
//                .put(ERRORS, new JsonArray().add(new JsonObject()
//                        .put(ERROR, "Internal server error occurred")
//                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
//                        .put(MESSAGE, "Database connection failed")));
//
//        var consumer = Bootstrap.vertx().eventBus().consumer("com.mindarray.eventbus.visualization.query", message -> {
//            message.reply(mockResponse);
//        });
//
//        var widget = new JsonObject("{\"visualization.name\":\"test-internal-error\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Grid\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"metric\",\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"system.cpu.percent\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[49125924693]}]}],\"visualization.properties\":{\"grid\":{\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[]}},\"visualization.result.by\":[\"monitor\"],\"container.type\":\"dashboard\",\"_type\":\"1\"}");
//
//        widget.put(VisualizationConstants.VISUALIZATION_NAME, widget.getString(VisualizationConstants.VISUALIZATION_NAME) + " " + System.currentTimeMillis());
//
//        TestAPIUtil.post(VISUALIZATION_WIDGET_API_ENDPOINT, widget, testContext.succeeding(response -> {
//            var widgetId = response.bodyAsJsonObject().getLong(ID);
//
//            TestAPIUtil.get(VISUALIZATION_QUERY_API_ENDPOINT + "/" + widgetId, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + accessToken), asyncResult -> {
//                try {
//                    if (asyncResult.succeeded()) {
//                        var result = asyncResult.result().bodyAsJsonObject();
//
//                        LOGGER.info(String.format("API response : %s ", result.encode()));
//
//                        Assertions.assertNotNull(result);
//
//                        Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));
//
//                        Assertions.assertEquals(HttpStatus.SC_INTERNAL_SERVER_ERROR, result.getInteger(RESPONSE_CODE));
//
//                        Assertions.assertFalse(result.getJsonArray(ERRORS).isEmpty());
//
//                        consumer.unregister();
//
//                        testContext.completeNow();
//                    } else {
//                        LOGGER.error(asyncResult.cause());
//                        consumer.unregister();
//                        testContext.failNow(asyncResult.cause());
//                    }
//                } catch (Exception e) {
//                    LOGGER.error(e);
//                    consumer.unregister();
//                    testContext.failNow(e);
//                }
//            });
//        }));
//    }
}
