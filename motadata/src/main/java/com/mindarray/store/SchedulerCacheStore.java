/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SchedulerCacheStore extends AbstractCacheStore
{

    private static final SchedulerCacheStore STORE = new SchedulerCacheStore();

    private final Map<Long, JsonArray> schedulerEvents = new ConcurrentHashMap<>();

    private final Map<Long, Integer> discoveredObjects = new ConcurrentHashMap<>();

    private final Map<Long, String> schedulerUsers = new ConcurrentHashMap<>();

    private final Map<Long, Integer> pendingProbesByScheduler = new ConcurrentHashMap<>();

    private SchedulerCacheStore()
    {
    }

    public static SchedulerCacheStore getStore()
    {
        return STORE;
    }


    public void initSchedulerContext(long schedulerId, String userName)
    {
        if (!schedulerEvents.containsKey(schedulerId))
        {
            schedulerEvents.put(schedulerId, new JsonArray());

            schedulerUsers.put(schedulerId, userName);
        }
    }

    public void clearSchedulerContext(long schedulerId)
    {
        schedulerEvents.remove(schedulerId);

        pendingProbesByScheduler.remove(schedulerId);

        schedulerUsers.remove(schedulerId);

        discoveredObjects.remove(schedulerId);
    }

    public JsonArray getSchedulerEvents(long schedulerId)
    {
        return schedulerEvents.get(schedulerId) != null ? schedulerEvents.get(schedulerId).copy() : null;
    }

    public String getSchedulerUser(long schedulerId)
    {
        return schedulerUsers.get(schedulerId) != null ? schedulerUsers.get(schedulerId) : GlobalConstants.DEFAULT_USER;
    }

    public void updateSchedulerEvent(long schedulerId, JsonArray schedulers)
    {
        schedulerEvents.put(schedulerId, schedulers);
    }

    public int getSchedulerPendingProbes(long schedulerId)
    {
        return pendingProbesByScheduler.get(schedulerId) != null ? pendingProbesByScheduler.get(schedulerId) : 0;
    }

    public int getDiscoveredObjects(long schedulerId)
    {
        return discoveredObjects.get(schedulerId) != null ? discoveredObjects.get(schedulerId) : 0;
    }

    public void addSchedulerEvent(long schedulerId, JsonObject schedulerEvent)
    {
        schedulerEvents.get(schedulerId).add(schedulerEvent);
    }

    public void addDiscoveredObjects(long schedulerId, int count)
    {
        discoveredObjects.put(schedulerId, count);
    }

    public void updatePendingProbes(long schedulerId, int count)
    {
        pendingProbesByScheduler.put(schedulerId, count);
    }

    public void updatePendingProbes(long schedulerId)
    {
        pendingProbesByScheduler.put(schedulerId, getSchedulerPendingProbes(schedulerId) > 0 ? pendingProbesByScheduler.get(schedulerId) - 1 : 0);
    }
}
