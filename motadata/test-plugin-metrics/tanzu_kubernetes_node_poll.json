{"************": {"result": {"tanzu.kubernetes.cpu.percent": 3.565553325, "tanzu.kubernetes.masters": 1, "tanzu.kubernetes.memory.percent": 38.33018845709266, "tanzu.kubernetes.node": [{"tanzu.kubernetes.node": "master", "tanzu.kubernetes.node.allocatable.cpu.cores": 4000000000, "tanzu.kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "tanzu.kubernetes.node.allocatable.memory.bytes": 4000649216, "tanzu.kubernetes.node.allocatable.pods": 110, "tanzu.kubernetes.node.available.memory.bytes": 2467192832, "tanzu.kubernetes.node.container.runtime.version": "containerd", "tanzu.kubernetes.node.containers": 8, "tanzu.kubernetes.node.cpu.limit.percent": 0, "tanzu.kubernetes.node.cpu.request.percent": 21.246551599689166, "tanzu.kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.node.ip": "************", "tanzu.kubernetes.node.memory.limit.percent": 0, "tanzu.kubernetes.node.memory.request.percent": 7.863043796539647, "tanzu.kubernetes.node.pods": 7, "tanzu.kubernetes.node.status": "Ready", "tanzu.kubernetes.node.type": "master", "tanzu.kubernetes.node.used.cpu.cores": 142622133, "tanzu.kubernetes.node.used.memory.bytes": 1533456384}, {"tanzu.kubernetes.node": "worker1", "tanzu.kubernetes.node.allocatable.cpu.cores": 4000000000, "tanzu.kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "tanzu.kubernetes.node.allocatable.memory.bytes": 4000649216, "tanzu.kubernetes.node.allocatable.pods": 110, "tanzu.kubernetes.node.available.memory.bytes": 2837106688, "tanzu.kubernetes.node.container.runtime.version": "containerd", "tanzu.kubernetes.node.containers": 6, "tanzu.kubernetes.node.cpu.limit.percent": 0, "tanzu.kubernetes.node.cpu.request.percent": 7.498782917537353, "tanzu.kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:33", "tanzu.kubernetes.node.ip": "************", "tanzu.kubernetes.node.memory.limit.percent": 8.911449636078265, "tanzu.kubernetes.node.memory.request.percent": 3.669420438385168, "tanzu.kubernetes.node.pods": 5, "tanzu.kubernetes.node.status": "Ready", "tanzu.kubernetes.node.type": "worker", "tanzu.kubernetes.node.used.cpu.cores": 138340315, "tanzu.kubernetes.node.used.memory.bytes": 1163542528}, {"tanzu.kubernetes.node": "worker2", "tanzu.kubernetes.node.allocatable.cpu.cores": 4000000000, "tanzu.kubernetes.node.allocatable.ephemeral.storage.bytes": 14169880143, "tanzu.kubernetes.node.allocatable.memory.bytes": 4000661504, "tanzu.kubernetes.node.allocatable.pods": 110, "tanzu.kubernetes.node.available.memory.bytes": 2781097984, "tanzu.kubernetes.node.container.runtime.version": "containerd", "tanzu.kubernetes.node.containers": 7, "tanzu.kubernetes.node.cpu.limit.percent": 0, "tanzu.kubernetes.node.cpu.request.percent": 2.4995866283617483, "tanzu.kubernetes.node.creation.time": "Sun, Feb 09 2025, 05:33", "tanzu.kubernetes.node.ip": "************", "tanzu.kubernetes.node.memory.limit.percent": 0, "tanzu.kubernetes.node.memory.request.percent": 0, "tanzu.kubernetes.node.pods": 6, "tanzu.kubernetes.node.status": "Ready", "tanzu.kubernetes.node.type": "worker", "tanzu.kubernetes.node.used.cpu.cores": 115772146, "tanzu.kubernetes.node.used.memory.bytes": 1219563520}], "tanzu.kubernetes.nodes": 3, "tanzu.kubernetes.workers": 2}}}