{"172.16.8.86": {"errors": [], "result": {"exchange2010.mailbox.role.client.rpc.fails.per.sec": 0.0, "exchange2010.mailbox.role.client.rpc.server.busy.fails.per.sec": 0.0, "exchange2010.mailbox.role.hub.retry.servers": 0.0, "exchange.mailbox.role.mail.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.temporary.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.succeed.submissions.per.sec": 0.0, "exchange2010.mailbox.role.active.connections": 1.0, "exchange2010.mailbox.role.active.users": 0.0, "exchange.mailbox.role.information.store.rpc.requests": 0.0, "exchange.mailbox.role.information.store.rpc.latency.ms": 15.0, "exchange2010.mailbox.role.information.store.rpc.backoff.clients.per.sec": 0.0, "exchange2010.mailbox.role.active.client.logons": 1.0, "exchange2010.mailbox.role.search.slow.finds": 0.0, "exchange2010.mailbox.role.search.tasks.per.sec": 0.0, "exchange2010.mailbox.role.slow.query.processor.threads": 0.0, "exchange2010.mailbox.role.slow.search.threads": 0.0, "exchange2010.mailbox.role.information.store.mailbox.queued.messages": 0.0, "exchange2010.mailbox.role.information.store.public.queued.messages": 0.0, "exchange.mailbox.role.information.store.rpc.request.sends.per.sec": 2.96, "exchange.mailbox.role.information.store.rpc.slow.request.latency.ms": 0.0, "exchange.mailbox.role.information.store.rpc.slow.request.ratio.percent": 0.0, "exchange.mailbox.role.information.store.rop.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.rop.failed.request.ratio.percent": 0.0, "exchange.mailbox.role.information.store.rpc.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.interface.rpc.request.latency.ms": 2.66, "exchange2010.mailbox.role.replication.receive.queue.length": 0.0, "exchange.mailbox.role.copy.queue.length": 0.0, "exchange.mailbox.role.replay.queue.length": 0.0, "exchange2010.mailbox.role.log.copy.latency.ms": 0.0, "exchange.mailbox.role.log.copy.bytes.per.sec": 0.0, "exchange.mailbox.role.log.replay.generations.per.sec": 0.0, "exchange.mailbox.role.log.replay.pending.syncs.per.sec": 0.0, "exchange.mailbox.role.calendar.attendant.processing.time": 0.0, "exchange.mailbox.role.calendar.attendant.failed.requests": 0.0, "exchange.mailbox.role.resource.booking.attendant.processing.time": 0.0, "exchange.mailbox.role.resource.booking.failed.requests": 0.0, "exchange2010.mailbox.role.document.indexing.time.ms": 0.0, "exchange2010.mailbox.role.content.obtain.rpc.latency.ms": 0.0, "startedtime.sec": 9838607.0, "startedtime": "113.0 days, 20.0 hours, 56.0 minutes, 47.0 seconds", "exchange.mailbox.role.mailbox.assistant.cpu.percent": 0.0, "exchange.mailbox.role.search.service.cpu.percent": 0.0, "exchange.mailbox.role.mailbox.assistant.database": [{"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-mailbox database 0568381169", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailsubmission-mailbox database 0568381169", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailsubmission-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}], "exchange.mailbox.role.mailbox.assistant": [{"exchange.mailbox.role.mailbox.assistant": "sharingfolderassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "calendarnotificationassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "provisioningassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "umpartnermessageassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "mwiassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "elceventbasedassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "freebusyassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "conversationsassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "approvalassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "junkemailoptionsassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "oofassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "resourcebookingassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "calendarassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "mailsubmissionassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "msexchangemailsubmission-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}], "exchange.mailbox.role.mailbox.assistant.memory.bytes": 55562240.0, "exchange.mailbox.role.search.service.memory.bytes": 56754176.0, "exchange2010.mailbox.role.search.index.service.bytes": 7712768.0}}, "WIN-RC9AF0C6565.web8.local": {"errors": [], "result": {"exchange.mailbox.role.mail.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.temporary.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.succeed.submissions.per.sec": 0.0, "exchange.mailbox.role.information.store.rpc.requests": 0.0, "exchange.mailbox.role.information.store.rpc.latency.ms": 0.0, "exchange.mailbox.role.information.store.rpc.request.sends.per.sec": 11.07, "exchange.mailbox.role.information.store.rpc.slow.request.latency.ms": 0.0, "exchange.mailbox.role.information.store.rpc.slow.request.ratio.percent": 0.01, "exchange.mailbox.role.information.store.rop.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.rop.failed.request.ratio.percent": 0.01, "exchange.mailbox.role.information.store.rpc.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.interface.rpc.request.latency.ms": 0.72, "exchange.mailbox.role.copy.queue.length": 0.0, "exchange.mailbox.role.replay.queue.length": 0.0, "exchange.mailbox.role.log.copy.bytes.per.sec": 0.0, "exchange.mailbox.role.log.replay.generations.per.sec": 0.0, "exchange.mailbox.role.log.replay.pending.syncs.per.sec": 0.0, "exchange.mailbox.role.calendar.attendant.processing.time": 0.0, "exchange.mailbox.role.calendar.attendant.failed.requests": 0.0, "exchange.mailbox.role.resource.booking.attendant.processing.time": 0.0, "exchange.mailbox.role.resource.booking.failed.requests": 0.0, "startedtime.sec": 9844543.0, "startedtime": "113.0 days, 22.0 hours, 35.0 minutes, 43.0 seconds", "exchange.mailbox.role.database.cache.size.bytes": 524288000.0, "exchange.mailbox.role.database.page.fault.stalls.per.sec": 0.0, "exchange.mailbox.role.database.version.bucket.allocations": 1.0, "exchange.mailbox.role.log.record.stalls.per.sec": 0.0, "exchange.mailbox.role.log.waiting.threads": 0.0, "exchange.mailbox.role.log.write.bytes.per.sec": 0.0, "exchange.mailbox.role.database.cache.hit.ratio.percent": 0.0, "exchange.mailbox.role.database.read.latency.ms": 0.0, "exchange.mailbox.role.database.write.latency.ms": 0.0, "exchange.mailbox.role.io.log.read.latency.ms": 0.0, "exchange.mailbox.role.io.log.write.latency.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.cpu.percent": 0.0, "exchange.mailbox.role.search.service.cpu.percent": 0.0, "exchange.mailbox.role.mailbox.assistant.database": [{"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-mailbox database 1178890725", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "microsoft exchange mailbox transport submission-mailbox database 1178890725", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 2147483650.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "microsoft exchange mailbox transport submission-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}], "exchange.mailbox.role.mailbox.assistant": [{"exchange.mailbox.role.mailbox.assistant": "birthdayassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "calendarinteropassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "recipientdlexpansioneventbasedassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "remindersassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "pushnotificationassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "taskstoreeventbasedassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "discoverysearcheventbasedassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "sharingfolderassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "calendarnotificationassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "provisioningassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "umpartnermessageassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "mwiassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "elceventbasedassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "conversationsassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "approvalassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "junkemailoptionsassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "resourcebookingassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "calendarassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "mailboxtransportsubmissionassistant", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "microsoft exchange mailbox transport submission-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}], "exchange.mailbox.role.mailbox.assistant.memory.bytes": 132911104.0, "exchange.mailbox.role.search.service.memory.bytes": 0, "exchange2010.mailbox.role.search.index.service.bytes": 0}}, "WIN-ETLRI8IIP8H.exchange8.local": {"errors": [], "result": {"exchange.mailbox.role.mail.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.temporary.failed.submissions.per.sec": 0.0, "exchange.mailbox.role.mail.succeed.submissions.per.sec": 0.0, "exchange.mailbox.role.information.store.rpc.requests": 0.0, "exchange.mailbox.role.information.store.rpc.latency.ms": 0.0, "exchange.mailbox.role.information.store.rpc.request.sends.per.sec": 6.96, "exchange.mailbox.role.information.store.rpc.slow.request.latency.ms": 0.0, "exchange.mailbox.role.information.store.rpc.slow.request.ratio.percent": 0.0, "exchange.mailbox.role.information.store.rop.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.rop.failed.request.ratio.percent": 0.0, "exchange.mailbox.role.information.store.rpc.outstanding.requests": 0.0, "exchange.mailbox.role.information.store.interface.rpc.request.latency.ms": 0.68, "exchange.mailbox.role.copy.queue.length": 0.0, "exchange.mailbox.role.replay.queue.length": 0.0, "exchange.mailbox.role.log.copy.bytes.per.sec": 0.0, "exchange.mailbox.role.log.replay.generations.per.sec": 0.0, "exchange.mailbox.role.log.replay.pending.syncs.per.sec": 0.0, "exchange.mailbox.role.calendar.attendant.processing.time": 0.0, "exchange.mailbox.role.calendar.attendant.failed.requests": 0.0, "exchange.mailbox.role.resource.booking.attendant.processing.time": 0.0, "exchange.mailbox.role.resource.booking.failed.requests": 0.0, "startedtime.sec": 9837491.0, "startedtime": "113.0 days, 20.0 hours, 38.0 minutes, 11.0 seconds", "exchange.mailbox.role.database.cache.size.bytes": 281018368.0, "exchange.mailbox.role.database.page.fault.stalls.per.sec": 0.0, "exchange.mailbox.role.database.version.bucket.allocations": 1.0, "exchange.mailbox.role.log.record.stalls.per.sec": 0.0, "exchange.mailbox.role.log.waiting.threads": 0.0, "exchange.mailbox.role.log.write.bytes.per.sec": 0.0, "exchange.mailbox.role.database.cache.hit.ratio.percent": 0.0, "exchange.mailbox.role.database.read.latency.ms": 0.0, "exchange.mailbox.role.database.write.latency.ms": 0.0, "exchange.mailbox.role.io.log.read.latency.ms": 0.0, "exchange.mailbox.role.io.log.write.latency.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.cpu.percent": 0.0, "exchange.mailbox.role.search.service.cpu.percent": 0.0, "exchange.mailbox.role.mailbox.assistant.database": [{"exchange.mailbox.role.mailbox.assistant.database": "msexchangedelivery-1fe7c829-fb4e-4a61-a0a1-a05a10afd2db", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangedelivery-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangeservicesapppool-1fe7c829-fb4e-4a61-a0a1-a05a10afd2db", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangeservicesapppool-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangesyncapppool-1fe7c829-fb4e-4a61-a0a1-a05a10afd2db", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangesyncapppool-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-1fe7c829-fb4e-4a61-a0a1-a05a10afd2db", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-mailbox database 1964088133", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangesubmission-mailbox database 1964088133", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}, {"exchange.mailbox.role.mailbox.assistant.database": "msexchangesubmission-total", "exchange.mailbox.role.mailbox.assistant.database.event.queue.length": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processing.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.database.processes.per.sec": 0.0, "exchange.mailbox.role.mailbox.assistant.database.event.polls.per.sec": 0.0}], "exchange.mailbox.role.mailbox.assistant": [{"exchange.mailbox.role.mailbox.assistant": "msexchangemailboxassistants-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}, {"exchange.mailbox.role.mailbox.assistant": "msexchangesubmission-total", "exchange.mailbox.role.mailbox.assistant.event.process.time.ms": 0.0, "exchange.mailbox.role.mailbox.assistant.event.queue.length": 0.0}], "exchange.mailbox.role.mailbox.assistant.memory.bytes": 389873664.0, "exchange.mailbox.role.search.service.memory.bytes": 0, "exchange2010.mailbox.role.search.index.service.bytes": 0, "exchange.mailbox.role.mailbox.database": [{"exchange.mailbox.role.mailbox.database": "Mailbox Database 1964088133", "exchange.mailbox.role.mailbox.database.server": "WIN-ETLRI8IIP8H", "exchange.mailbox.role.mailbox.database.status": "Mounted", "exchange.mailbox.role.mailbox.database.copy.queue.length": 0.0, "exchange.mailbox.role.mailbox.database.replay.queue.length": 0.0, "exchange.mailbox.role.mailbox.database.last.inspected.log.time": "", "exchange.mailbox.role.mailbox.database.content.index.state": "FailedAndSuspended", "exchange.mailbox.role.mailbox.database.size.bytes": 6291456.0, "exchange.mailbox.role.mailbox.database.white.space.bytes": 467664896.0, "exchange.mailbox.role.mailbox.database.activation.preference": "{[WIN-ETLRI8IIP8H, 1]}", "exchange.mailbox.role.mailbox.database.mailboxes": 2, "exchange.mailbox.role.mailbox.database.mailbox.avg.size.bytes": 3145728.0}], "exchange.mailbox.role.replication.check": [{"exchange.mailbox.role.replication.check": "ReplayService", "exchange.mailbox.role.replication.check.server": "WIN-ETLRI8IIP8H", "exchange.mailbox.role.replication.check.result": "Passed", "exchange.mailbox.role.replication.check.error": ""}, {"exchange.mailbox.role.replication.check": "ActiveManager", "exchange.mailbox.role.replication.check.server": "WIN-ETLRI8IIP8H", "exchange.mailbox.role.replication.check.result": "Passed", "exchange.mailbox.role.replication.check.error": ""}, {"exchange.mailbox.role.replication.check": "TasksRpcListener", "exchange.mailbox.role.replication.check.server": "WIN-ETLRI8IIP8H", "exchange.mailbox.role.replication.check.result": "Passed", "exchange.mailbox.role.replication.check.error": ""}, {"exchange.mailbox.role.replication.check": "DatabaseRedundancy", "exchange.mailbox.role.replication.check.server": "WIN-ETLRI8IIP8H", "exchange.mailbox.role.replication.check.result": "*FAILED*", "exchange.mailbox.role.replication.check.error": "There were database redundancy check failures for database 'Mailbox Database 1964088133' that may be lowering its redundancy and putting the database at risk of data loss. Redundancy Count: 1. Expected Redundancy Count: 2. Detailed error(s):"}], "exchange.version": "Version 15.1 (Build 1591.10)", "exchange.mailbox.role.mailboxes": 2, "exchange.mailbox.role.dag.witness.directory": "C:\\ExchangeSetupLogs\\DMG", "exchange.mailbox.role.dag": "ex16", "exchange.mailbox.role.witness.server": "win-etlri8iip8h.exchange8.local", "exchange.mailbox.role.operational.servers": "", "exchange.mailbox.role.alternate.witness.server": "", "exchange.mailbox.role.alternate.witness.directory": "", "exchange.mailbox.role.primary.active.manager": "", "exchange.mailbox.role.witness.share.in.use": "", "exchange.mailbox.role.replication.port": "64327", "exchange.mailbox.role.network.names": "{}", "exchange2016.rpc.client.connections": 0.0, "exchange2016.ping.pending.commands": 0.0, "exchange2016.sync.pending.commands": 0.0, "exchange2016.sync.active.requests": 0.0, "exchange2016.availability.requests": 0.0, "exchange2016.sync.requests.per.sec": 0.0, "exchange2016.nspi.rpc.browse.request.latency.ms": 0.0, "exchange2016.nspi.rpc.request.latency.ms": 0.0, "exchange2016.referral.rpc.request.latency.ms": 0.0, "exchange2016.control.panel.outbound.proxy.request.latency.ms": 0.0, "exchange2016.control.panel.request.latency.ms": 0.0, "exchange2016.owa.unique.users": 2147484086.0, "exchange2016.owa.requests.per.sec": 0.99, "exchange2016.web.connection.attempts.per.sec": 0.0, "exchange2016.auto.discovery.requests.per.sec": 0.0, "exchange2016.webservice.requests.per.sec": 0.0, "exchange2016.web.connections": 27.0, "exchange2016.request.wait.time.ms": 0.0, "exchange2016.availability.free.or.busy.request.latency.ms": 0.0, "exchange2016.application.restarts": 0.0, "exchange2016.worker.process.restarts": 0.0, "exchange2016.owa.search.time.ms": 0.0, "exchange2016.rpc.operations.per.sec": 0.0, "exchange2016.rpc.users": 0.0, "exchange2016.rpc.active.users": 2.0, "exchange2016.rpc.requests": 0.0, "exchange2016.rpc.latency.ms": 9.0}}}