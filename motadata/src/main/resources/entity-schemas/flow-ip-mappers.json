{"entity": "Flow IP Mapper", "table": "tbl_config_flow_ip_mapper", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "flow.ip.mapper.profile.name", "title": "Profile Name", "type": "string", "rules": ["unique", "required"]}, {"name": "flow.ip.mapper.description", "title": "Description", "type": "string", "rules": []}, {"name": "flow.ip.mapper.source", "title": "Source", "type": "string", "rules": ["required"], "values": ["Manual Mapping"]}]}