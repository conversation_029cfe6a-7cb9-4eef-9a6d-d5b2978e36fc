/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.netroute;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.NetRoute;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.NetRouteCacheStore;
import com.mindarray.store.NetRouteConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;

import java.util.ArrayList;
import java.util.List;

import static com.mindarray.GlobalConstants.TIMEOUT;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * Schedules network route polling operations based on configured intervals.
 * <p>
 * This class is responsible for periodically checking which network routes need to be polled
 * and initiating the polling process. It retrieves network route configurations from the store,
 * prepares polling requests with appropriate parameters, and sends these requests to the event bus
 * for processing.
 * <p>
 * The scheduler runs on a fixed interval (defined by MINIMUM_POLLING_INTERVAL) and checks for
 * network routes that are due for polling based on their configured polling intervals.
 */
public class NetRouteScheduler extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(NetRouteScheduler.class, GlobalConstants.MOTADATA_NETROUTE, "NetRoute Scheduler");

    /**
     * Minimum interval (in seconds) between polling operations
     */
    private static final int MINIMUM_POLLING_INTERVAL = 10;

    /**
     * Maximum number of probe attempts for network route polling
     */
    private static final int MAX_PROBE_ATTEMPTS = MotadataConfigUtil.getNetRouteMaxProbeAttempts();

    /**
     * Maximum number of probe packets to send for each hop
     */
    private static final int MAX_PROBE_PACKETS = MotadataConfigUtil.getNetRouteMaxProbePackets();

    /**
     * Maximum number of hops to trace in the network route
     */
    private static final int MAX_PROBE_HOPS = MotadataConfigUtil.getNetRouteMaxProbeHops();

    /**
     * Number of times to retry sending packets if no response is received
     */
    private static final int PACKET_RETRIES = MotadataConfigUtil.getNetRoutePacketRetries();

    /**
     * Network protocol to use for route tracing (e.g., ICMP, UDP)
     */
    private static final String PROTOCOL = MotadataConfigUtil.getNetRouteProtocol();

    /**
     * List to store IDs of metrics that need to be polled in the current cycle
     */
    private final List<Long> metrics = new ArrayList<>();

    /**
     * Initializes the NetRouteScheduler verticle.
     * <p>
     * This method sets up a periodic timer that runs every MINIMUM_POLLING_INTERVAL seconds
     * to check for network routes that need to be polled. For each route that needs polling,
     * it prepares a polling request with appropriate parameters and sends it to the event bus.
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        // Set up a periodic timer to check for metrics that need polling
        vertx.setPeriodic(MINIMUM_POLLING_INTERVAL * 1000L, timer ->
        {
            try
            {
                // Clear the metrics list from the previous cycle
                metrics.clear();

                // Get the list of metrics that need to be polled in this cycle
                NetRouteCacheStore.getStore().updateMetricInterval(MINIMUM_POLLING_INTERVAL, metrics);

                if (!metrics.isEmpty())
                {
                    // Get the current timestamp for all polling requests
                    var timestamp = DateTimeUtil.currentSeconds();

                    // Process each metric that needs polling
                    for (var id : metrics)
                    {
                        // Get the network route configuration
                        var item = NetRouteConfigStore.getStore().getItem(id);

                        // Prepare the polling request with all necessary parameters
                        item.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT).put(AIOpsObject.OBJECT_AGENT, item.getLong(NetRoute.NETROUTE_SOURCE))
                                .put(NetRouteConstants.NETROUTE_MAX_HOPS, MAX_PROBE_HOPS).put(NetRouteConstants.NETROUTE_PACKET_RETRIES, PACKET_RETRIES).put(NetRouteConstants.NETROUTE_PROTOCOL, PROTOCOL)
                                .put(PluginEngineConstants.PLUGIN_ENGINE, PluginEngineConstants.PluginEngine.GO.getName()).put(NetRouteConstants.NETROUTE_MAX_PROBE_PACKETS, MAX_PROBE_PACKETS).put(TIMEOUT, MAX_PROBE_ATTEMPTS * 20)
                                .put(NetRouteConstants.NETROUTE_MAX_PROBE_ATTEMPTS, MAX_PROBE_ATTEMPTS).put(EVENT_TYPE, EVENT_PLUGIN_ENGINE).put(PluginEngineConstants.PLUGIN_ENGINE_REQUEST, PluginEngineConstants.PluginEngineRequest.NETROUTE.getName());

                        // Send the polling request to the event bus
                        vertx.eventBus().send(EVENT_ROUTER, item.put(EVENT_ID, CommonUtil.newEventId()).put(AIOpsObject.OBJECT_TARGET, item.getString(NetRoute.NETROUTE_DESTINATION)).put(EVENT_TIMESTAMP, timestamp));

                        // Log debugging information if enabled
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.info(String.format("netroute metric : %s is scheduled to poll", item.getString(NetRoute.NETROUTE_NAME)));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Complete the promise to signal successful initialization
        promise.complete();
    }

    /**
     * Stops the NetRouteScheduler verticle.
     * <p>
     * This method is called when the verticle is undeployed. It completes the provided
     * promise to signal successful shutdown.
     *
     * @param promise Promise to be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        // Complete the promise to signal successful shutdown
        promise.complete();
    }
}
