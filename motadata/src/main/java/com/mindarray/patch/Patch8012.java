/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.BackupProfile;
import com.mindarray.api.Scheduler;
import com.mindarray.db.DBConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.BackupProfileConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.DBConstants.FIELD_NAME;

public class Patch8012 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8012.class, MOTADATA_PATCH, "Patch 8.0.12");

    private static final String VERSION = "8.0.12";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("executing 8.0.12 patch");

            var futures = new ArrayList<Future<Void>>();

            futures.add(executeCustomTabPatch());

            futures.add(executeBackupProfilePatch());

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("successfully executed 8.0.12 patch");

                    promise.complete();
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    // custom tab patch
    private Future<Void> executeCustomTabPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("executing patch for retrieving all custom tabs...");

            var futures = new ArrayList<Future<Void>>();

            for (var entry : getCustomTabs().entrySet())
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                var item = TemplateConfigStore.getStore().getItem(entry.getKey());

                Bootstrap.configDBService.update(DBConstants.TBL_TEMPLATE,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        updateTemplate(item, entry.getValue()),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                TemplateConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        LOGGER.info(String.format("updated template : %s : item: %s", item.getLong(ID), TemplateConfigStore.getStore().getItem(item.getLong(ID))));

                                        future.complete();
                                    }
                                    else
                                    {
                                        LOGGER.warn(asyncResult.cause().getMessage());

                                        future.fail(asyncResult.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to update template : %s because of : %s ", item.getLong(ID), result.cause()));

                                future.fail(result.cause());
                            }
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Map<Long, JsonArray> getCustomTabs()
    {

        var items = TemplateConfigStore.getStore().getItemsByValue("_type", "1");   // getting all custom templates

        var customTabs = new HashMap<Long, JsonArray>();    // mapping of list of custom tabs with their parent template ID

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if (item.containsKey("template.tabs") && !item.getJsonArray("template.tabs").isEmpty())
            {
                var templateTabs = item.getJsonArray("template.tabs");

                for (var i = 0; i < templateTabs.size(); i++)
                {
                    var tab = templateTabs.getJsonObject(i);

                    if (tab.getString("text").equalsIgnoreCase("Overview")) // need to find parent template
                    {

                        customTabs.computeIfAbsent(tab.getLong("tab.id"), value -> new JsonArray())
                                .add(new JsonObject().put("tab.id", item.getLong(ID)).put("text", item.getString("template.name")).put("user.created", "yes"));
                    }
                }
            }
        }

        return customTabs;
    }

    private JsonObject updateTemplate(JsonObject item, JsonArray customTabs)
    {
        LOGGER.info("ID : " + item.getLong(ID) + " item : " + item.encode());

        item.put("custom.tabs", customTabs);  // add list of custom tabs as a value of "custom.tabs"

        // will remove custom tabs from the list of default tabs as we have already added
        if (item.containsKey("template.tabs") && !item.getJsonArray("template.tabs").isEmpty())
        {
            var templateTabs = item.getJsonArray("template.tabs");

            for (var i = 0; i < templateTabs.size(); i++)
            {
                var tab = templateTabs.getJsonObject(i);

                if (tab.containsKey("user.created") && tab.getString("user.created").equalsIgnoreCase(YES))
                {
                    templateTabs.remove(i--); // need to remove user created tab from the list of default tabs
                }
            }
        }

        LOGGER.info("After adding and removing custom tabs for the " + item.getLong(ID) + " : " + item.encode());

        return item;
    }

    // backup profile patch
    private Future<Void> executeBackupProfilePatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var item = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName());

            if (item != null && !item.isEmpty())
            {
                LOGGER.info("profile : " + item.encode());

                var futures = new ArrayList<Future<Void>>();

                futures.add(updateBackupProfile(item));

                futures.add(updateDatabaseBackupScheduler(item));

                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                promise.fail("report db backup profile not found!");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception.getCause());
            }
        }

        return promise.future();
    }

    private Future<Void> updateBackupProfile(JsonObject profile)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var item = new JsonObject("{ \"backup.profile.name\": \"Report DB Backup Profile\", \"backup.profile.type\": \"Report DB\", \"backup.profile.context\": { \"datastore.types\" : [\"metric\", \"log\", \"trap\", \"alert\", \"config.history\", \"system.event\"] }, \"backup.storage.profile\": 10000000000002 }");

            Bootstrap.configDBService().update(DBConstants.TBL_BACKUP_PROFILE,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, profile.getLong(ID)),
                    item,
                    DEFAULT_USER,
                    SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            BackupProfileConfigStore.getStore().updateItem(profile.getLong(ID)).onComplete(result ->
                            {
                                LOGGER.info(String.format("backup profile %s updated successfully", profile.getString(BackupProfile.BACKUP_PROFILE_NAME)));

                                promise.complete();
                            });
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    private Future<Void> updateDatabaseBackupScheduler(JsonObject profile)
    {
        var promise = Promise.<Void>promise();

        JsonObject item = null;

        var items = SchedulerConfigStore.getStore().getItemsByValue(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName());

        for (var index = 0; index < items.size(); index++)
        {
            if (items.getJsonObject(index).getJsonObject(Scheduler.SCHEDULER_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getLong(0).equals(profile.getLong(ID)))
            {
                item = items.getJsonObject(index);
            }
        }

        if (item != null && !item.isEmpty())
        {
            LOGGER.info("scheduler : " + item);

            var id = item.getLong(ID);

            Bootstrap.configDBService().update(DBConstants.TBL_SCHEDULER,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, id),
                    new JsonObject("{\"scheduler.timeline\": \"Daily\"}"),
                    DEFAULT_USER,
                    SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            SchedulerConfigStore.getStore().updateItem(id).onComplete(result ->
                            {
                                LOGGER.info(String.format("updated scheduler %s successfully", id));

                                promise.complete();
                            });
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            promise.complete();
                        }
                    });
        }
        else
        {
            LOGGER.warn("scheduler : " + item);

            promise.complete(); // if scheduler not gets updated then it's not a big issue
        }

        return promise.future();
    }
}
