{"************": {"result": {"bind9.nsstat.ipv4.requests": 0, "bind9.nsstat.ipv6.requests": 0, "bind9.nsstat.received.edns0.requests": 0, "bind9.nsstat.bad.edns.requests": 0, "bind9.nsstat.tsig.requests": 0, "bind9.nsstat.received.tsig0.requests": 0, "bind9.nsstat.bad.tsig.requests": 0, "bind9.nsstat.tcp.requests.rate": 0, "bind9.nsstat.rejected.auth.queries": 0, "bind9.nsstat.rejected.recursive.queries": 0, "bind9.nsstat.rejected.zone.transfers": 0, "bind9.nsstat.rejected.updates": 0, "bind9.nsstat.sent.responses": 0, "bind9.nsstat.sent.truncated.responses": 0, "bind9.nsstat.sent.edns0.responses": 0, "bind9.nsstat.sent.tsig.responses": 0, "bind9.nsstat.sent.tsig0.responses": 0, "bind9.nsstat.successful.queries": 0, "bind9.nsstat.answered.auth.queries": 0, "bind9.nsstat.noanswered.auth.queries": 0, "bind9.nsstat.referral.answered.queries": 0, "bind9.nsstat.empty.queries": 0, "bind9.nsstat.failed.served.queries": 0, "bind9.nsstat.former.queries": 0, "bind9.nsstat.domain.nx.queries": 0, "bind9.nsstat.recursive.queries": 0, "bind9.nsstat.duplicate.queries": 0, "bind9.nsstat.dropped.queries": 0, "bind9.nsstat.failed.queries": 0, "bind9.nsstat.completed.zone.transfers": 0, "bind9.nsstat.request.forward.updates": 0, "bind9.nsstat.forward.response.updates": 0, "bind9.nsstat.forward.failed.updates": 0, "bind9.nsstat.updates": 0, "bind9.nsstat.failed.updates": 0, "bind9.nsstat.bad.prerequisite.updates": 0, "bind9.nsstat.recursive.clients": 0, "bind9.nsstat.dns64": 0, "bind9.nsstat.rate.drops": 0, "bind9.nsstat.rate.slips": 0, "bind9.nsstat.policy.zone.rewrite.responses": 0, "bind9.nsstat.udp.queries.rate": 0, "bind9.nsstat.tcp.queries.rate": 0, "bind9.nsstat.nsid.options": 0, "bind9.nsstat.expired.options": 0, "bind9.nsstat.other.options": 0, "bind9.nsstat.redirect.nx.queries": 0, "bind9.nsstat.redirect.rlookup.nx.queries": 0}, "errors": []}, "************": {"result": {"bind9.nsstat.ipv4.requests": 0, "bind9.nsstat.ipv6.requests": 0, "bind9.nsstat.received.edns0.requests": 0, "bind9.nsstat.bad.edns.requests": 0, "bind9.nsstat.tsig.requests": 0, "bind9.nsstat.received.tsig0.requests": 0, "bind9.nsstat.bad.tsig.requests": 0, "bind9.nsstat.tcp.requests.rate": 0, "bind9.nsstat.rejected.auth.queries": 0, "bind9.nsstat.rejected.recursive.queries": 0, "bind9.nsstat.rejected.zone.transfers": 0, "bind9.nsstat.rejected.updates": 0, "bind9.nsstat.sent.responses": 0, "bind9.nsstat.sent.truncated.responses": 0, "bind9.nsstat.sent.edns0.responses": 0, "bind9.nsstat.sent.tsig.responses": 0, "bind9.nsstat.sent.tsig0.responses": 0, "bind9.nsstat.successful.queries": 0, "bind9.nsstat.answered.auth.queries": 0, "bind9.nsstat.noanswered.auth.queries": 0, "bind9.nsstat.referral.answered.queries": 0, "bind9.nsstat.empty.queries": 0, "bind9.nsstat.failed.served.queries": 0, "bind9.nsstat.former.queries": 0, "bind9.nsstat.domain.nx.queries": 0, "bind9.nsstat.recursive.queries": 0, "bind9.nsstat.duplicate.queries": 0, "bind9.nsstat.dropped.queries": 0, "bind9.nsstat.failed.queries": 0, "bind9.nsstat.completed.zone.transfers": 0, "bind9.nsstat.request.forward.updates": 0, "bind9.nsstat.forward.response.updates": 0, "bind9.nsstat.forward.failed.updates": 0, "bind9.nsstat.updates": 0, "bind9.nsstat.failed.updates": 0, "bind9.nsstat.bad.prerequisite.updates": 0, "bind9.nsstat.recursive.clients": 0, "bind9.nsstat.dns64": 0, "bind9.nsstat.rate.drops": 0, "bind9.nsstat.rate.slips": 0, "bind9.nsstat.policy.zone.rewrite.responses": 0, "bind9.nsstat.udp.queries.rate": 0, "bind9.nsstat.tcp.queries.rate": 0, "bind9.nsstat.nsid.options": 0, "bind9.nsstat.expired.options": 0, "bind9.nsstat.other.options": 0, "bind9.nsstat.redirect.nx.queries": 0, "bind9.nsstat.redirect.rlookup.nx.queries": 0}}}