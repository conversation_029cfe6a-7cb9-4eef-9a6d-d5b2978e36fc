/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.processor;

import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.APIConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.LogForwarderConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.datagram.DatagramSocketOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.NetServer;
import io.vertx.core.net.NetServerOptions;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.LOG_FORWARDER_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.LogForwarder.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogEngineConstants.*;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestLogForwarder
{

    public static final String SESSION_ID = UUID.randomUUID().toString().toLowerCase().trim();
    private static final Logger LOGGER = new Logger(TestLogForwarder.class, MOTADATA_LOG, "Test Log Forwarder");
    private static final String HOST = MotadataConfigUtil.getHost();
    private static final int TCP_PORT = 4321;
    private static final int UDP_PORT = 1234;
    private static final JsonObject CONTEXT = new JsonObject("{\"log.forwarder.name\":\"Log-Forwarder\",\"log.forwarder.type\":\"tcp\",\"log.forwarder.log.type\":\"json\",\"log.forwarder.destination.ip\":\"*************\",\"log.forwarder.destination.port\":5140,\"log.forwarder.state\":\"yes\",\"log.forwarder.context\":{\"entities\":[\"" + HOST + "\"],\"entity.type\":\"event.source\",\"filters\":{\"data.filter\":{\"operator\":\"and\",\"filter\":\"include\",\"groups\":[{\"filter\":\"include\",\"operator\":\"and\",\"conditions\":[{\"operand\":\"message\",\"operator\":\"contain\",\"value\":\"motadata\"}]}]}}},\"session-id\":\"b8b7f986-d205-472e-a8dc-25c727327821\",\"user.name\":\"admin\"}").put(LOG_FORWARDER_DESTINATION_IP, HOST).put(LOG_FORWARDER_DESTINATION_PORT, TCP_PORT);
    private static MessageConsumer<JsonObject> messageConsumer;
    private static Long LOG_FORWARDER_ID = 0L;

    private static NetServer server;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, HOST).put(SOURCE_GROUPS, new JsonArray().add(10000000000017L)).put(EVENT_SOURCE_TYPE, "Linux").put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(PLUGIN_ID, 600004).put(EVENT_CATEGORY, "Linux Syslog"));

        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        try
        {
            if (server != null)
            {
                server.close().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info("After Each -> Server closed successfully...");
                    }
                    else
                    {
                        LOGGER.info("After Each -> Failed to close Server due to " + result.cause());
                    }
                });
            }

            testContext.awaitCompletion(2, TimeUnit.SECONDS);

            testContext.completeNow();
        }
        catch (Exception e)
        {
            LOGGER.error(e);

            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(5000)
    void testTCPLogForwarderProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        server = TestUtil.vertx().createNetServer(new NetServerOptions().setPort(TCP_PORT)).connectHandler(socket ->
        {

            LOGGER.info("New client connected: " + socket.remoteAddress());

            socket.handler(buffer -> LOGGER.info("Received message: " + buffer.toString()));

            socket.closeHandler(handler -> LOGGER.info("Client disconnected: " + socket.remoteAddress()));

        }).listen(TCP_PORT, HOST, result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("TCP Server started on : " + HOST + ":" + TCP_PORT);
            }
            else
            {
                LOGGER.info(String.format("Failed to start TCP server on %s:%s, reason -> %s", HOST, TCP_PORT, result.cause()));
            }
        });

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_FORWARDER_PROFILE_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertNotNull(event);

                    var status = event.getString(STATUS);

                    Assertions.assertNotNull(status);

                    Assertions.assertEquals(STATUS_SUCCEED, status);

                    messageConsumer.unregister(result ->
                    {
                        testContext.completeNow();

                        server.close().onComplete(socket ->
                        {

                            if (socket.succeeded())
                            {
                                LOGGER.info("TCP Connection closed successfully...");
                            }
                            else
                            {
                                LOGGER.info("Failed to close [TCP] server due to " + socket.cause());
                            }

                        });
                    });
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(handler -> testContext.failNow(exception));
            }
        }));

        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_FORWARDER_PROFILE_TEST, CONTEXT.copy().put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    @Timeout(5000)
    void testTCPLogForwarderProfileInvalidConnection(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_FORWARDER_PROFILE_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertNotNull(event);

                    var status = event.getString(STATUS);

                    Assertions.assertNotNull(status);

                    Assertions.assertEquals(STATUS_FAIL, status);

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(handler -> testContext.failNow(exception));
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_FORWARDER_PROFILE_TEST, CONTEXT.copy().put(APIConstants.SESSION_ID, SESSION_ID).put(LOG_FORWARDER_DESTINATION_IP, HOST).put(LOG_FORWARDER_DESTINATION_PORT, UDP_PORT));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @Timeout(5000)
    void testUDPLogForwarderProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_FORWARDER_PROFILE_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertNotNull(event);

                    var status = event.getString(STATUS);

                    Assertions.assertNotNull(status);

                    Assertions.assertEquals(STATUS_FAIL, status);

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(handler -> testContext.failNow(exception));
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_FORWARDER_PROFILE_TEST, CONTEXT.copy().put(APIConstants.SESSION_ID, SESSION_ID).put(LOG_FORWARDER_DESTINATION_IP, HOST).put(LOG_FORWARDER_DESTINATION_PORT, UDP_PORT));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    @Timeout(5000)
    void testUDPLogForwarderProfileInvalidConnection(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_FORWARDER_PROFILE_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertNotNull(event);

                    var status = event.getString(STATUS);

                    Assertions.assertNotNull(status);

                    Assertions.assertEquals(STATUS_FAIL, status);

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(handler -> testContext.failNow(exception));
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_FORWARDER_PROFILE_TEST, CONTEXT.copy().put(APIConstants.SESSION_ID, SESSION_ID).put(LOG_FORWARDER_DESTINATION_IP, "***********").put(LOG_FORWARDER_DESTINATION_PORT, UDP_PORT).put(LOG_FORWARDER_TYPE, LOG_FORWARDER_TYPE_UDP));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(5000)
    void testCreateAndForwardTCPJSONProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var context = CONTEXT.copy();

        context.getJsonObject(LOG_FORWARDER_CONTEXT).put(ENTITIES, new JsonArray().add(HOST));

        server = TestUtil.vertx().createNetServer(new NetServerOptions().setPort(TCP_PORT)).connectHandler(socket ->
        {
            LOGGER.info("New client connected: " + socket.remoteAddress());

            socket.handler(buffer ->
            {

                var message = buffer.toString();

                LOGGER.info("Received parsed log : " + message);

                Assertions.assertTrue(message.contains("message"));

                Assertions.assertTrue(message.contains("motadata"));

                Assertions.assertTrue(message.contains("linux.syslog.event.type"));

                Assertions.assertTrue(message.contains("linux.syslog.priority"));

                Assertions.assertTrue(message.contains("linux.syslog.event.type"));

                Assertions.assertTrue(message.contains("linux.syslog.logsource"));

                Assertions.assertTrue(message.contains("linux.syslog.program"));

                Assertions.assertTrue(message.contains("linux.syslog.facility"));

                Assertions.assertTrue(message.contains("linux.syslog.facility"));

                testContext.completeNow();
            });

        }).listen(TCP_PORT, HOST, result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("TCP Server started on : " + HOST + ":" + TCP_PORT);
            }
            else
            {
                LOGGER.info(String.format("Failed to start TCP server on %s:%s, reason -> %s", HOST, TCP_PORT, result.cause()));
            }
        });

        TestAPIUtil.post(LOG_FORWARDER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            Assertions.assertTrue(body.getString(MESSAGE).contains("Log Forwarder created successfully"));

            LOG_FORWARDER_ID = body.getLong(ID);

            var event = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1726932361,\"message\":\"<6> motadata-Latitude-3420 kernel: [449632.896219] ish-hid {33AECD58-B679-4E54-9BD9-A04D34F0C226}: [hid-ish]: enum_devices_done OK, num_hid_devices=1\",\"event\":\"<6>Sep 24 15:46:51 motadata-Latitude-3420 kernel: [449632.896219] ish-hid {33AECD58-B679-4E54-9BD9-A04D34F0C226}: [hid-ish]: enum_devices_done OK, num_hid_devices=1\",\"event.volume.bytes\":161,\"linux.syslog.event.type\":\"log\",\"linux.syslog.priority\":\"6\",\"linux.syslog.logsource\":\"motadata-Latitude-3420\",\"linux.syslog.program\":\"kernel\",\"linux.syslog.facility\":\"kernelmessages\",\"linux.syslog.severity\":\"Informational\",\"event.category\":\"LinuxSyslog\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1726932358}").put(EVENT_SOURCE, HOST);

            TestUtil.vertx().setTimer(2000, timer -> TestUtil.vertx().eventBus().send(EVENT_LOG_FORWARDER, event));
        })));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(5000)
    void testCreateUDPLogForwarderProfile(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var context = CONTEXT.copy().put(LOG_FORWARDER_LOG_TYPE, LOG_FORWARDER_LOG_TYPE_RAW);

        context.getJsonObject(LOG_FORWARDER_CONTEXT).put(ENTITIES, new JsonArray().add(HOST));

        TestAPIUtil.post(LOG_FORWARDER_API_ENDPOINT, context.put(LOG_FORWARDER_TYPE, LOG_FORWARDER_TYPE_UDP).put(LOG_FORWARDER_DESTINATION_PORT, UDP_PORT).put(LOG_FORWARDER_NAME, "udp-profile"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            Assertions.assertTrue(body.getString(MESSAGE).contains("Log Forwarder created successfully"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    @Timeout(5000)
    void testForwardedLogUsingUDP(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var event = new JsonObject("{\"event.source\":\"127.0.0.1\",\"event.received.time\":1726932361,\"message\":\"<6> motadata-Latitude-3420 kernel: [449632.896219] ish-hid {33AECD58-B679-4E54-9BD9-A04D34F0C226}: [hid-ish]: enum_devices_done OK, num_hid_devices=1\",\"event\":\"<6>Sep 24 15:46:51 motadata-Latitude-3420 kernel: [449632.896219] ish-hid {33AECD58-B679-4E54-9BD9-A04D34F0C226}: [hid-ish]: enum_devices_done OK, num_hid_devices=1\",\"event.volume.bytes\":161,\"linux.syslog.event.type\":\"log\",\"linux.syslog.priority\":\"6\",\"linux.syslog.logsource\":\"motadata-Latitude-3420\",\"linux.syslog.program\":\"kernel\",\"linux.syslog.facility\":\"kernelmessages\",\"linux.syslog.severity\":\"Informational\",\"event.category\":\"LinuxSyslog\",\"event.source.type\":\"Linux\",\"plugin.id\":600004,\"event.timestamp\":1726932358}").put(EVENT_SOURCE, HOST);

        TestUtil.vertx().createDatagramSocket(new DatagramSocketOptions().setIpV6(false).setReusePort(true).setReuseAddress(true)).listen(
                UDP_PORT, HOST, result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("UDP Log listener connected to %s ", UDP_PORT + ":" + MotadataConfigUtil.getVIPIPAddress()));

                        result.result().handler(packet ->
                        {
                            var log = packet.data().getString(0, packet.data().length());

                            LOGGER.info("UDP received msg -> " + log);

                            Assertions.assertNotNull(log);

                            Assertions.assertTrue(log.contains("motadata"));

                            testContext.completeNow();
                        });
                    }
                });

        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().eventBus().send(EVENT_LOG_FORWARDER, event);

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(5000)
    void testCreateUDPLogForwarderWithSourceGroupFilter(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var context = CONTEXT.copy();

        context.getJsonObject(LOG_FORWARDER_CONTEXT).put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName()).put(ENTITIES, new JsonArray().add(10000000000017L));

        TestAPIUtil.post(LOG_FORWARDER_API_ENDPOINT, context.put(LOG_FORWARDER_TYPE, LOG_FORWARDER_TYPE_UDP).put(LOG_FORWARDER_DESTINATION_PORT, UDP_PORT).put(LOG_FORWARDER_NAME, "udp-profile-source-group"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            Assertions.assertTrue(body.getString(MESSAGE).contains("Log Forwarder created successfully"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(5000)
    void testDisableTCPLogForwarderProfileState(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.put(LOG_FORWARDER_API_ENDPOINT + "/" + LOG_FORWARDER_ID + "/state", new JsonObject().put(LOG_FORWARDER_STATE, "no"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            Assertions.assertTrue(LogForwarderConfigStore.getStore().getItem(LOG_FORWARDER_ID).getString(LOG_FORWARDER_STATE).equalsIgnoreCase("no"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(5000)
    void testEnableTCPLogForwarderProfileState(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        server = TestUtil.vertx().createNetServer(new NetServerOptions().setPort(TCP_PORT)).connectHandler(socket ->
        {

            LOGGER.info("New client connected: " + socket.remoteAddress());

            socket.handler(buffer -> LOGGER.info("Received message: " + buffer.toString()));

            socket.closeHandler(handler -> LOGGER.info("Client disconnected: " + socket.remoteAddress()));

        }).listen(TCP_PORT, HOST, result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("TCP Server started on : " + HOST + ":" + TCP_PORT);
            }
            else
            {
                LOGGER.info(String.format("Failed to start TCP server on %s:%s, reason -> %s", HOST, TCP_PORT, result.cause()));
            }
        });

        TestAPIUtil.put(LOG_FORWARDER_API_ENDPOINT + "/" + LOG_FORWARDER_ID + "/state", new JsonObject().put(LOG_FORWARDER_STATE, "yes"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertTrue(LogForwarderConfigStore.getStore().getItem(LOG_FORWARDER_ID).getString(LOG_FORWARDER_STATE).equalsIgnoreCase("yes"));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    @Timeout(5000)
    void testDeleteTCPLogForwarderProfileState(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(LOG_FORWARDER_API_ENDPOINT + "/" + LOG_FORWARDER_ID, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertNotNull(response);

            Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

            Assertions.assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(50, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");

    }


}


