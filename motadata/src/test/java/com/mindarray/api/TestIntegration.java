/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.ErrorMessageConstants.CREDENTIAL_ERROR;
import static com.mindarray.ErrorMessageConstants.INVALID_JSON;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.INTEGRATION_API_ENDPOINT;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestIntegration
{
    private static final Logger LOGGER = new Logger(TestIntegration.class, MOTADATA_API, "Test Integration");

    private static MessageConsumer<JsonObject> messageConsumer;

    private static long serviceNowCredentialProfileId;

    private static long serviceOpsCredentialProfileId;

    private static long jiraCredentialProfileId;

    @BeforeAll
    public static void beforeAll(VertxTestContext testContext)
    {
        var router = Router.router(TestUtil.vertx());

        var subRouter = Router.router(TestUtil.vertx());

        var bodyHandler = BodyHandler.create().setUploadsDirectory(CURRENT_DIR + PATH_SEPARATOR + UPLOADS).setBodyLimit(MAX_BODY_LIMIT_BYTES);

        router.route("/integration-test/*").handler(bodyHandler).subRouter(subRouter);

        initAPI(subRouter);

        var futures = new ArrayList<Future<Void>>();

        var promise = Promise.<Void>promise();

        futures.add(promise.future());

        TestAPIUtil.createCredentialProfile(new JsonObject("{\"credential.profile.name\": \"ServiceNow Credential Profile Test\",\"credential.profile.protocol\": \"HTTP/HTTPS\",\"credential.profile.context\": {\"username\": \"admin\",\"authentication.type\": \"basic\",\"password\": \"admin\"}\n}"), testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                serviceNowCredentialProfileId = result.result();

                promise.complete();

            }
            else
            {
                promise.fail(result.cause());

                testContext.failNow(result.cause());

                LOGGER.error(result.cause());
            }
        });


        var future = Promise.<Void>promise();

        futures.add(future.future());

        TestAPIUtil.createCredentialProfile(new JsonObject("{\"credential.profile.name\" : \"ServiceOps Credential Profile Test \",\"credential.profile.protocol\" : \"HTTP/HTTPS\",\"credential.profile.context\" : {\"authentication.type\" : \"oauth\",\"grant.type\" : \"Password\",\"username\" : \"<EMAIL>\",\"password\" : \"Mind!@#$%^&*(\",\"client.id\" : \"new-client\",\"client.secret\" : \"GGzsnKoJyr1dTCP5Rmiu\"}}"), testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                serviceOpsCredentialProfileId = result.result();

                future.complete();

            }
            else
            {
                future.fail(result.cause());

                testContext.failNow(result.cause());

                LOGGER.error(result.cause());
            }
        });

        var asyncPromise = Promise.<Void>promise();

        futures.add(future.future());

        TestAPIUtil.createCredentialProfile(new JsonObject("{\"credential.profile.name\":\"Jira Credential Profile Test\",\"credential.profile.protocol\":\"HTTP\\/HTTPS\",\"credential.profile.context\":{\"username\":\"Administrator\",\"password\":\"Mind@123\"}}"), testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                jiraCredentialProfileId = result.result();

                asyncPromise.complete();

            }
            else
            {
                asyncPromise.fail(result.cause());

                testContext.failNow(result.cause());

                LOGGER.error(result.cause());
            }
        });


        Future.all(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                TestUtil.vertx().createHttpServer()
                        .requestHandler(router)
                        .listen(6334, response ->
                        {
                            if (response.succeeded())
                            {
                                testContext.completeNow();

                                LOGGER.info("Integration Server is deployed at port 6334");
                            }
                            else
                            {
                                LOGGER.warn(String.format("Error while Deploying Integration Server, %s", response.cause().getMessage()));
                            }
                        });
            }
            else
            {
                testContext.failNow(result.cause());

                LOGGER.error(result.cause());
            }
        });


    }

    private static void initAPI(Router router)
    {
        var counter = new AtomicInteger();

        // servicenow

        // create incident successfully
        router.post("/now/200/api/now/table/incident").handler(routingContext -> routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put(RESULT, new JsonObject().put("number", "INC000" + counter.incrementAndGet()).put("sys_id", "QWERTYUIOP"))).encode()));

        // update incident successfully
        router.put("/now/200/api/now/table/incident/:id").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put(RESULT, new JsonObject().put("number", "INC000" + counter.get()).put("sys_id", routingContext.request().getParam(ID)))).encode()));

        // get one incident
        router.get("/now/200/api/now/table/incident/:id").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonObject().put("number", "INC000" + counter.get()).put("sys_id", routingContext.request().getParam(ID))).encode()));

        // get all incidents
        router.get("/now/200/api/now/table/incident").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject().put("number", "INC000" + counter.get()).put("sys_id", "QWERTYUIOP"))).encode()));

        // create event successfully
        router.post("/now/200/api/now/table/em_event").handler(routingContext -> routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put(RESULT, new JsonObject().put("source", "Motadata AIOps"))).encode()));

        // incident creation failed due to invalid credentials
        router.post("/now/401/api/now/table/incident").handler(routingContext -> routingContext.response().setStatusCode(401).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().put(ERROR, ErrorMessageConstants.INVALID_CREDENTIALS).encode()));

        // incident creation failed due to invalid json response
        router.post("/now/404/api/now/table/incident").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML).end("<html>Instance is hibernating</html>"));

        // incident creation failed due to url timeout
        router.post("/now/504/api/now/table/incident").handler(routingContext ->
                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), handler ->
                        routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put(RESULT, new JsonObject().put("number", "INC0001").put("sys_id", "QWERTYUIOP"))).encode())));

        //sync job successfully
        router.get("/now/200/api/now/table/sys_choice").handler(routingContext ->
        {
            var query = routingContext.request().params().get("sysparm_query").trim().toLowerCase();

            if (query.contains("category"))
            {
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"label\": \"Network\",\"value\": \"network\"}"))).encode());
            }
            else if (query.contains("subcategory"))
            {
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"dependent_value\": \"network\",\"label\": \"IP Address\",\"value\": \"ip address\"}"))).encode());
            }
            else if (query.contains("urgency"))
            {
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"label\": \"3 - Low\",\"sys_id\": \"1be6c35bd12302104f34ba60d8b7c141\"}"))).encode());
            }
            else if (query.contains("impact"))
            {
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"label\": \"1 - High\",\"sys_id\": \"53e6c35bd12302104f34ba60d8b7c140\"}"))).encode());
            }
            else
            {
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().encode());
            }

        });

        router.get("/now/200/api/now/table/cmdb_ci_service").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"parent\": \"\",\"name\": \"SAP Enterprise Services\",\"sys_id\": \"26da329f0a0a0bb400f69d8159bc753d\"}"))).encode()));

        router.get("/now/200/api/now/table/service_offering").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\n\n\"parent\": {\n\"display_value\": \"SAP Enterprise Services\",\n\"value\": \"26da329f0a0a0bb400f69d8159bc753d\"\n},\n\"name\": {\n\"display_value\": \"AB\",\n\"value\": \"AB\"\n},\n\"sys_id\": {\n\"display_value\": \"1d0cc87e83715e10fb44f8f6feaad300\",\n\"value\": \"1d0cc87e83715e10fb44f8f6feaad300\"\n}\n}"))).encode()));

        router.get("/now/200/api/now/table/sys_user_group").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\"sys_id\": \"019ad92ec7230010393d265c95c260dd\",\"name\": \"Problem Analyzers\"}"))).encode()));

        router.get("/now/200/api/now/table/sys_user_grmember").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(RESULT, new JsonArray().add(new JsonObject("{\n\"sys_id\": {\n\"display_value\": \"195ebb573b331300ad3cc9bb34efc4ad\",\n\"value\": \"195ebb573b331300ad3cc9bb34efc4ad\"\n},\n\"user\": {\n\"display_value\": \"Problem Coordinator B\",\n\"link\": \"https://dev182640.service-now.com/api/now/table/sys_user/38cb3f173b331300ad3cc9bb34efc4d6\",\n\"value\": \"38cb3f173b331300ad3cc9bb34efc4d6\"\n}\n}"))).encode()));


        // serviceops

        // generate token successfully
        router.post("/ops/200/api/oauth/token").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put("access_token", "eZasiuZweiuERuibnsdXSOun").encode()));

        // generate token failed due to invalid credentials
        router.post("/ops/400/api/oauth/token").handler(routingContext -> routingContext.response().setStatusCode(400).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put(ERROR, ErrorMessageConstants.INVALID_CREDENTIALS).encode()));

        // generate token failed due to unauthorized access
        router.post("/ops/401/api/oauth/token").handler(routingContext -> routingContext.response().setStatusCode(401).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().encode()));

        // generate token successfully
        router.post("/ops/402/api/oauth/token").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put("access_token", "eZasiuZweiuERuibnsdXSOun").encode()));

        // generate token failed due to invalid json response
        router.post("/ops/404/api/oauth/token").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML).end("<html>Instance is hibernating</html>"));

        // generate token failed due to url timeout
        router.post("/ops/504/api/oauth/token").handler(routingContext ->
                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), handler ->
                        routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put("access_token", "eZasiuZweiuERuibnsdXSOun").encode())));

        // create ticket successfully
        router.post("/ops/200/api/v1/request").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put("name", "INC-000" + counter.incrementAndGet()).put("id", 37636)).encode()));

        // create ticket failed due to token expired
        router.post("/ops/402/api/v1/request").handler(routingContext -> routingContext.response().setStatusCode(401).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put("name", "INC-000" + counter.incrementAndGet()).put("id", 37636)).encode()));

        // update ticket successfully
        router.post("/ops/200/api/request/:id/conversation").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put("name", "INC-000" + counter.get()).put("id", routingContext.request().getParam(ID))).encode()));

        // get ticket successfully
        router.get("/ops/200/api/v1/request/:id").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put("name", "INC-000" + counter.get()).put("id", routingContext.request().getParam(ID)).encode()));

        // close OR reopen ticket successfully
        router.patch("/ops/200/api/v1/request/:id").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject().put("name", "INC-000" + counter.get()).put("id", routingContext.request().getParam(ID))).encode()));

        router.get("/ops/200/api/public/ping").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_TEXT_HTML).end("Hello, How Can I Help You!"));


        // Atlassian Jira

        router.get("/jira/401/rest/api/2/project").handler(routingContext -> routingContext.response().setStatusCode(401).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().encode()));

        router.get("/jira/504/rest/api/2/project").handler(routingContext ->
                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), handler ->
                        routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().put("project", "1").encode())));

        //fetch projects
        router.get("/jira/200/rest/api/2/project").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end("[{\"expand\":\"description,lead,url,projectKeys\",\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/project\\/10001\",\"id\":\"10001\",\"key\":\"AB\",\"name\":\"ABC_Test\",\"avatarUrls\":{\"48x48\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?avatarId=10324\",\"24x24\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=small&avatarId=10324\",\"16x16\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=xsmall&avatarId=10324\",\"32x32\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=medium&avatarId=10324\"},\"projectTypeKey\":\"service_desk\",\"archived\":false}]"));

        router.get("/jira/200/rest/api/2/issue/createmeta/:projectId/issuetypes/:issuetypeId").handler(routingContext ->
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end("{\"maxResults\":500,\"startAt\":0,\"total\":19,\"isLast\":true,\"values\":[{\"required\":false,\"schema\":{\"type\":\"user\",\"system\":\"assignee\"},\"name\":\"Assignee\",\"fieldId\":\"assignee\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/api\\/latest\\/user\\/assignable\\/search?issueKey=null&username=\",\"hasDefaultValue\":false,\"operations\":[\"set\"]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"attachment\",\"system\":\"attachment\"},\"name\":\"Attachment\",\"fieldId\":\"attachment\",\"hasDefaultValue\":false,\"operations\":[]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"component\",\"system\":\"components\"},\"name\":\"Component\\/s\",\"fieldId\":\"components\",\"hasDefaultValue\":false,\"operations\":[\"add\",\"set\",\"remove\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10002\",\"id\":\"10002\",\"name\":\"Active Directory\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10003\",\"id\":\"10003\",\"name\":\"Analytics and Reporting Service\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10004\",\"id\":\"10004\",\"name\":\"Billing Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10005\",\"id\":\"10005\",\"name\":\"Cloud Storage Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10006\",\"id\":\"10006\",\"name\":\"Data Center Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10007\",\"id\":\"10007\",\"name\":\"Email and Collaboration Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10008\",\"id\":\"10008\",\"name\":\"Financial Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10009\",\"id\":\"10009\",\"name\":\"HR Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10010\",\"id\":\"10010\",\"name\":\"Intranet\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10011\",\"id\":\"10011\",\"name\":\"Jira\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10012\",\"id\":\"10012\",\"name\":\"Office Network\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10013\",\"id\":\"10013\",\"name\":\"Payroll Services\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10014\",\"id\":\"10014\",\"name\":\"Printers\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10015\",\"id\":\"10015\",\"name\":\"Public Website\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10016\",\"id\":\"10016\",\"name\":\"VPN Server\",\"description\":\"Created by Jira Service Management\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/component\\/10017\",\"id\":\"10017\",\"name\":\"Webstore Purchasing Services\",\"description\":\"Created by Jira Service Management\"}]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"user\",\"custom\":\"com.atlassian.servicedesk:sd-request-participants\",\"customId\":10000},\"name\":\"Request participants\",\"fieldId\":\"customfield_10000\",\"hasDefaultValue\":false,\"operations\":[\"add\",\"set\",\"remove\"]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"sd-customerorganization\",\"custom\":\"com.atlassian.servicedesk:sd-customer-organizations\",\"customId\":10002},\"name\":\"Organizations\",\"fieldId\":\"customfield_10002\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/servicedesk\\/1\\/customer\\/organisations\\/project\\/10001\\/search?query=\",\"hasDefaultValue\":false,\"operations\":[\"add\",\"set\",\"remove\"]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"user\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker\",\"customId\":10300},\"name\":\"Approvers\",\"fieldId\":\"customfield_10300\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/api\\/1.0\\/users\\/picker?fieldName=customfield_10300&query=\",\"hasDefaultValue\":false,\"operations\":[\"add\",\"set\",\"remove\"]},{\"required\":false,\"schema\":{\"type\":\"option\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:select\",\"customId\":10305},\"name\":\"Impact\",\"fieldId\":\"customfield_10305\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10100\",\"value\":\"Extensive \\/ Widespread\",\"id\":\"10100\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10101\",\"value\":\"Significant \\/ Large\",\"id\":\"10101\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10102\",\"value\":\"Moderate \\/ Limited\",\"id\":\"10102\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10103\",\"value\":\"Minor \\/ Localized\",\"id\":\"10103\",\"disabled\":false}]},{\"required\":false,\"schema\":{\"type\":\"option\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:select\",\"customId\":10311},\"name\":\"Urgency\",\"fieldId\":\"customfield_10311\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10116\",\"value\":\"Critical\",\"id\":\"10116\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10117\",\"value\":\"High\",\"id\":\"10117\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10118\",\"value\":\"Medium\",\"id\":\"10118\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10119\",\"value\":\"Low\",\"id\":\"10119\",\"disabled\":false}]},{\"required\":false,\"schema\":{\"type\":\"option-with-child\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect\",\"customId\":10315},\"name\":\"Product categorization\",\"fieldId\":\"customfield_10315\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10137\",\"value\":\"Document\",\"id\":\"10137\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10133\",\"value\":\"Software\",\"id\":\"10133\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10134\",\"value\":\"Communication\",\"id\":\"10134\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10135\",\"value\":\"Service\",\"id\":\"10135\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10136\",\"value\":\"People\",\"id\":\"10136\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10124\",\"value\":\"Hardware\",\"id\":\"10124\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10125\",\"value\":\"CPD\",\"id\":\"10125\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10126\",\"value\":\"Peripheral\",\"id\":\"10126\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10127\",\"value\":\"Power\",\"id\":\"10127\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10128\",\"value\":\"Component\",\"id\":\"10128\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10129\",\"value\":\"Process Equipment\",\"id\":\"10129\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10130\",\"value\":\"Virtual\",\"id\":\"10130\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10131\",\"value\":\"Disc\",\"id\":\"10131\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10132\",\"value\":\"Tape\",\"id\":\"10132\",\"disabled\":false}]}]},{\"required\":false,\"schema\":{\"type\":\"option-with-child\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect\",\"customId\":10316},\"name\":\"Operational categorization\",\"fieldId\":\"customfield_10316\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10180\",\"value\":\"Server Change\",\"id\":\"10180\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10181\",\"value\":\"Decommission\",\"id\":\"10181\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10182\",\"value\":\"Extend\",\"id\":\"10182\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10183\",\"value\":\"Network\",\"id\":\"10183\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10184\",\"value\":\"Post Implementation\",\"id\":\"10184\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10185\",\"value\":\"Provision\",\"id\":\"10185\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10186\",\"value\":\"Storage\",\"id\":\"10186\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10187\",\"value\":\"Update Configuration\",\"id\":\"10187\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10177\",\"value\":\"Process Request\",\"id\":\"10177\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10178\",\"value\":\"Audit\",\"id\":\"10178\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10179\",\"value\":\"Update\",\"id\":\"10179\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10173\",\"value\":\"Network Request\",\"id\":\"10173\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10174\",\"value\":\"Create\",\"id\":\"10174\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10175\",\"value\":\"Modify\",\"id\":\"10175\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10176\",\"value\":\"Terminate\",\"id\":\"10176\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10167\",\"value\":\"Instance Request\",\"id\":\"10167\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10168\",\"value\":\"Copy\",\"id\":\"10168\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10169\",\"value\":\"Create\",\"id\":\"10169\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10170\",\"value\":\"Move\",\"id\":\"10170\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10171\",\"value\":\"Remove\",\"id\":\"10171\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10172\",\"value\":\"Repair\",\"id\":\"10172\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10138\",\"value\":\"Account Request\",\"id\":\"10138\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10139\",\"value\":\"Firewall\",\"id\":\"10139\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10140\",\"value\":\"Active Directory\",\"id\":\"10140\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10141\",\"value\":\"Printer\",\"id\":\"10141\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10142\",\"value\":\"Operating System\",\"id\":\"10142\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10143\",\"value\":\"Virus scan\",\"id\":\"10143\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10144\",\"value\":\"VPN\",\"id\":\"10144\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10145\",\"value\":\"Break \\/ Fix\",\"id\":\"10145\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10146\",\"value\":\"Monitor\",\"id\":\"10146\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10147\",\"value\":\"Keyboard\",\"id\":\"10147\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10148\",\"value\":\"PC\",\"id\":\"10148\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10149\",\"value\":\"Laptop\",\"id\":\"10149\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10150\",\"value\":\"Hard Drive\",\"id\":\"10150\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10151\",\"value\":\"Docking Station\",\"id\":\"10151\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10188\",\"value\":\"VM Request\",\"id\":\"10188\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10189\",\"value\":\"Extend\",\"id\":\"10189\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10190\",\"value\":\"Provision\",\"id\":\"10190\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10191\",\"value\":\"Remove\",\"id\":\"10191\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10152\",\"value\":\"Configuration\",\"id\":\"10152\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10153\",\"value\":\"Firewall\",\"id\":\"10153\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10154\",\"value\":\"Active Directory\",\"id\":\"10154\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10155\",\"value\":\"Printer\",\"id\":\"10155\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10156\",\"value\":\"Operating System\",\"id\":\"10156\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10157\",\"value\":\"Virus scan\",\"id\":\"10157\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10158\",\"value\":\"VPN\",\"id\":\"10158\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10159\",\"value\":\"User Account\",\"id\":\"10159\",\"disabled\":false}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10160\",\"value\":\"Connectivity\",\"id\":\"10160\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10161\",\"value\":\"Hardware Request\",\"id\":\"10161\",\"disabled\":false,\"children\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10162\",\"value\":\"Add\",\"id\":\"10162\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10163\",\"value\":\"Configure\",\"id\":\"10163\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10164\",\"value\":\"Move\",\"id\":\"10164\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10165\",\"value\":\"New\",\"id\":\"10165\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10166\",\"value\":\"Repair\",\"id\":\"10166\",\"disabled\":false}]}]},{\"required\":false,\"schema\":{\"type\":\"option\",\"custom\":\"com.atlassian.jira.plugin.system.customfieldtypes:select\",\"customId\":10317},\"name\":\"Source\",\"fieldId\":\"customfield_10317\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10192\",\"value\":\"Email\",\"id\":\"10192\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10193\",\"value\":\"Phone\",\"id\":\"10193\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10194\",\"value\":\"Monitoring systems\",\"id\":\"10194\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10195\",\"value\":\"Vendor\\/technical advisory\",\"id\":\"10195\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10196\",\"value\":\"Customer\",\"id\":\"10196\",\"disabled\":false},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/customFieldOption\\/10197\",\"value\":\"Other\",\"id\":\"10197\",\"disabled\":false}]},{\"required\":false,\"schema\":{\"type\":\"string\",\"system\":\"description\"},\"name\":\"Description\",\"fieldId\":\"description\",\"hasDefaultValue\":false,\"operations\":[\"set\"]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"issuelinks\",\"system\":\"issuelinks\"},\"name\":\"Linked Issues\",\"fieldId\":\"issuelinks\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issue\\/picker?currentProjectId=&showSubTaskParent=true&showSubTasks=true¤tIssueKey=null&query=\",\"hasDefaultValue\":false,\"operations\":[\"add\"]},{\"required\":false,\"schema\":{\"type\":\"issuetype\",\"system\":\"issuetype\"},\"name\":\"Issue Type\",\"fieldId\":\"issuetype\",\"hasDefaultValue\":false,\"operations\":[],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issuetype\\/10203\",\"id\":\"10203\",\"description\":\"For system outages or incidents. Created by Jira Service Management.\",\"iconUrl\":\"http:\\/\\/************:8080\\/secure\\/viewavatar?size=xsmall&avatarId=10703&avatarType=issuetype\",\"name\":\"Incident\",\"subtask\":false,\"avatarId\":10703}]},{\"required\":false,\"schema\":{\"type\":\"array\",\"items\":\"string\",\"system\":\"labels\"},\"name\":\"Labels\",\"fieldId\":\"labels\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/api\\/1.0\\/labels\\/suggest?query=\",\"hasDefaultValue\":false,\"operations\":[\"add\",\"set\",\"remove\"]},{\"required\":false,\"schema\":{\"type\":\"priority\",\"system\":\"priority\"},\"name\":\"Priority\",\"fieldId\":\"priority\",\"hasDefaultValue\":true,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/10000\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/blocker.svg\",\"name\":\"Blocker\",\"id\":\"10000\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/2\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/high.svg\",\"name\":\"High\",\"id\":\"2\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/3\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/medium.svg\",\"name\":\"Medium\",\"id\":\"3\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/4\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/low.svg\",\"name\":\"Low\",\"id\":\"4\"},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/10001\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/trivial.svg\",\"name\":\"Minor\",\"id\":\"10001\"}],\"defaultValue\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/priority\\/3\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/priorities\\/medium.svg\",\"name\":\"Medium\",\"id\":\"3\"}},{\"required\":true,\"schema\":{\"type\":\"project\",\"system\":\"project\"},\"name\":\"Project\",\"fieldId\":\"project\",\"hasDefaultValue\":false,\"operations\":[\"set\"],\"allowedValues\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/project\\/10001\",\"id\":\"10001\",\"key\":\"AB\",\"name\":\"ABC_Test\",\"projectTypeKey\":\"service_desk\",\"avatarUrls\":{\"48x48\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?avatarId=10324\",\"24x24\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=small&avatarId=10324\",\"16x16\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=xsmall&avatarId=10324\",\"32x32\":\"http:\\/\\/************:8080\\/secure\\/projectavatar?size=medium&avatarId=10324\"}}]},{\"required\":false,\"schema\":{\"type\":\"user\",\"system\":\"reporter\"},\"name\":\"Reporter\",\"fieldId\":\"reporter\",\"autoCompleteUrl\":\"http:\\/\\/************:8080\\/rest\\/api\\/latest\\/user\\/search?username=\",\"hasDefaultValue\":false,\"operations\":[\"set\"]},{\"required\":true,\"schema\":{\"type\":\"string\",\"system\":\"summary\"},\"name\":\"Summary\",\"fieldId\":\"summary\",\"hasDefaultValue\":false,\"operations\":[\"set\"]}]}"));

        router.get("/jira/200/rest/api/2/issue/createmeta/:projectId/issuetypes").handler(routingContext ->
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end("{\"maxResults\":500,\"startAt\":0,\"total\":7,\"isLast\":true,\"values\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issuetype\\/10203\",\"id\":\"10203\",\"description\":\"For system outages or incidents. Created by Jira Service Management.\",\"iconUrl\":\"http:\\/\\/************:8080\\/secure\\/viewavatar?size=xsmall&avatarId=10703&avatarType=issuetype\",\"name\":\"Incident\",\"subtask\":false}]}"));

        router.get("/jira/200/rest/api/2/user/assignable/search").handler(routingContext ->
                routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end("[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/user?username=Administrator\",\"key\":\"JIRAUSER10000\",\"name\":\"Administrator\",\"emailAddress\":\"<EMAIL>\",\"avatarUrls\":{\"48x48\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=48\",\"24x24\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=24\",\"16x16\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=16\",\"32x32\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=32\"},\"displayName\":\"Administrator\",\"active\":true,\"deleted\":false,\"timeZone\":\"Asia\\/Kolkata\",\"locale\":\"en_US\"}]"));

        router.get("/jira/200/rest/api/2/project/:id/statuses").handler(routingContext -> routingContext.response().setStatusCode(200).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end("[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issuetype\\/10201\",\"id\":\"10201\",\"name\":\"New Feature\",\"subtask\":false,\"statuses\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/1\",\"description\":\"The issue is open and ready for the assignee to start work on it.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/open.png\",\"name\":\"Open\",\"id\":\"1\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/2\",\"id\":2,\"key\":\"new\",\"colorName\":\"default\",\"name\":\"To Do\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10003\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Pending\",\"id\":\"10003\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10006\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Work in progress\",\"id\":\"10006\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/4\",\"description\":\"This issue was once resolved, but the resolution was deemed incorrect. From here issues are either marked assigned or resolved.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/reopened.png\",\"name\":\"Reopened\",\"id\":\"4\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/2\",\"id\":2,\"key\":\"new\",\"colorName\":\"default\",\"name\":\"To Do\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10007\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Done\",\"id\":\"10007\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issuetype\\/10202\",\"id\":\"10202\",\"name\":\"Support\",\"subtask\":false,\"statuses\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10001\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Waiting for support\",\"id\":\"10001\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10002\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Waiting for customer\",\"id\":\"10002\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10003\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Pending\",\"id\":\"10003\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/5\",\"description\":\"A resolution has been taken, and it is awaiting verification by reporter. From here issues are either reopened, or are closed.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/resolved.png\",\"name\":\"Resolved\",\"id\":\"5\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/3\",\"description\":\"This issue is being actively worked on at the moment by the assignee.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/inprogress.png\",\"name\":\"In Progress\",\"id\":\"3\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10004\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Canceled\",\"id\":\"10004\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10005\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Escalated\",\"id\":\"10005\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/6\",\"description\":\"The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/closed.png\",\"name\":\"Closed\",\"id\":\"6\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}}]},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issuetype\\/10200\",\"id\":\"10200\",\"name\":\"Bug\",\"subtask\":false,\"statuses\":[{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/1\",\"description\":\"The issue is open and ready for the assignee to start work on it.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/open.png\",\"name\":\"Open\",\"id\":\"1\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/2\",\"id\":2,\"key\":\"new\",\"colorName\":\"default\",\"name\":\"To Do\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10003\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Pending\",\"id\":\"10003\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10006\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Work in progress\",\"id\":\"10006\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/4\",\"description\":\"This issue was once resolved, but the resolution was deemed incorrect. From here issues are either marked assigned or resolved.\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/statuses\\/reopened.png\",\"name\":\"Reopened\",\"id\":\"4\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/2\",\"id\":2,\"key\":\"new\",\"colorName\":\"default\",\"name\":\"To Do\"}},{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/status\\/10007\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http:\\/\\/************:8080\\/images\\/icons\\/status_generic.gif\",\"name\":\"Done\",\"id\":\"10007\",\"statusCategory\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/statuscategory\\/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}}]}]"));

        router.post("/jira/200/rest/api/2/issue").handler(routingContext -> routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject("{\"id\":\"10206\",\"key\":\"AB-9\",\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issue\\/10206\"}")).encode()));

        router.post("/jira/200/rest/api/2/issue/:id/comment").handler(routingContext -> routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(routingContext.body().asJsonObject().mergeIn(new JsonObject("{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/issue\\/10206\\/comment\\/10201\",\"id\":\"10201\",\"author\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/user?username=Administrator\",\"name\":\"Administrator\",\"key\":\"JIRAUSER10000\",\"emailAddress\":\"<EMAIL>\",\"avatarUrls\":{\"48x48\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=48\",\"24x24\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=24\",\"16x16\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=16\",\"32x32\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=32\"},\"displayName\":\"Administrator\",\"active\":true,\"timeZone\":\"Asia\\/Kolkata\"},\"body\":\"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque eget venenatis elit. Duis eu justo eget augue iaculis fermentum. Sed semper quam laoreet nisi egestas at posuere augue semper.\",\"updateAuthor\":{\"self\":\"http:\\/\\/************:8080\\/rest\\/api\\/2\\/user?username=Administrator\",\"name\":\"Administrator\",\"key\":\"JIRAUSER10000\",\"emailAddress\":\"<EMAIL>\",\"avatarUrls\":{\"48x48\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=48\",\"24x24\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=24\",\"16x16\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=16\",\"32x32\":\"https:\\/\\/www.gravatar.com\\/avatar\\/15d705f272a879254f9f94f9c46f3bab?d=mm&s=32\"},\"displayName\":\"Administrator\",\"active\":true,\"timeZone\":\"Asia\\/Kolkata\"},\"created\":\"2025-03-24T18:37:34.031+0530\",\"updated\":\"2025-03-24T18:37:34.031+0530\"}")).encode()));

        router.get("/jira/200/rest/api/2/issue/:id/transitions").handler(routingContext -> routingContext.response().setStatusCode(201).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject("{\"expand\":\"transitions\",\"transitions\":[{\"id\":\"51\",\"name\":\"Pending\",\"opsbarSequence\":**********,\"to\":{\"self\":\"http://************:8080/rest/api/2/status/10003\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http://************:8080/images/icons/status_generic.gif\",\"name\":\"Pending\",\"id\":\"10003\",\"statusCategory\":{\"self\":\"http://************:8080/rest/api/2/statuscategory/4\",\"id\":4,\"key\":\"indeterminate\",\"colorName\":\"inprogress\",\"name\":\"In Progress\"}}},{\"id\":\"111\",\"name\":\"Done\",\"opsbarSequence\":**********,\"to\":{\"self\":\"http://************:8080/rest/api/2/status/10014\",\"description\":\"This was auto-generated by Jira Service Management during workflow import\",\"iconUrl\":\"http://************:8080/images/icons/status_generic.gif\",\"name\":\"Completed\",\"id\":\"10014\",\"statusCategory\":{\"self\":\"http://************:8080/rest/api/2/statuscategory/3\",\"id\":3,\"key\":\"done\",\"colorName\":\"success\",\"name\":\"Done\"}}}]}").encode()));

        router.post("/jira/200/rest/api/2/issue/:id/transitions").handler(routingContext -> routingContext.response().setStatusCode(204).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).end(new JsonObject().encode()));

    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetDefaultServiceNowIntegration(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICENOW.getName()), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_TYPE));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_CONTEXT));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_ATTRIBUTES));

            Assertions.assertTrue(IntegrationConstants.IntegrationType.SERVICENOW.getName().equalsIgnoreCase(result.getString(Integration.INTEGRATION_TYPE)));

            Assertions.assertEquals(60, result.getJsonObject(Integration.INTEGRATION_CONTEXT).getInteger(TIMEOUT));

            Assertions.assertTrue(result.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.REQUEST_TYPE));

            Assertions.assertEquals("event", result.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(Integration.REQUEST_TYPE));

            Assertions.assertTrue(result.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.ALERT_REOCCURRENCE_ACTION));

            Assertions.assertEquals("create", result.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(Integration.ALERT_REOCCURRENCE_ACTION));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetDefaultServiceOpsIntegration(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_TYPE));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_CONTEXT));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_ATTRIBUTES));

            Assertions.assertTrue(IntegrationConstants.IntegrationType.SERVICEOPS.getName().equalsIgnoreCase(result.getString(Integration.INTEGRATION_TYPE)));

            Assertions.assertEquals(60, result.getJsonObject(Integration.INTEGRATION_CONTEXT).getInteger(TIMEOUT));

            Assertions.assertTrue(result.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.ALERT_REOCCURRENCE_ACTION));

            Assertions.assertEquals("create", result.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(Integration.ALERT_REOCCURRENCE_ACTION));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetActiveIntegrationsBeforeUpdate(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put("active", "yes"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertTrue(result.containsKey(RESULT));

            Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

            Assertions.assertEquals(1, result.getJsonArray(RESULT).size());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testTestServiceNowIncidentIntegration(VertxTestContext testContext)
    {
        var event = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        event.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.SERVICENOW).equalsIgnoreCase(result.getString(MESSAGE)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, event);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testTestServiceNowEventIntegration(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200\", \"timeout\": 60, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"event\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.SERVICENOW).equalsIgnoreCase(result.getString(MESSAGE)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testTestServiceNowIntegrationInvalidCredentials(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/401/\", \"timeout\": 60, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(CREDENTIAL_ERROR));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICENOW.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testTestServiceNowIntegrationInvalidJsonResponse(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/404/\", \"timeout\": 60, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(INVALID_JSON));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICENOW.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testTestServiceNowIntegrationURLTimeout(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/504/\", \"timeout\": 1, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(TIMEOUT));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICENOW.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testTestServiceNowIntegrationProxyEnabled(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 1, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\", \"proxy.enabled\" : \"yes\" }, \"integration.type\": \"ServiceNow\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICENOW.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testUpdateServiceNowIntegrationAutoSyncOff(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\" }, \"integration.type\": \"ServiceNow\"}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L,
                payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testServiceNowSyncJobResult(VertxTestContext testContext)
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(30), timer ->
        {
            var item = IntegrationConfigStore.getStore().getItem(10000000000002L);

            Assertions.assertNotNull(item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES));

            LOGGER.info(String.format("item : %s", item.encode()));

            var attributes = item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES);

            Assertions.assertFalse(attributes.getJsonArray("categories").isEmpty());

            Assertions.assertFalse(attributes.getJsonArray("groups").isEmpty());

            Assertions.assertFalse(attributes.getJsonArray("impacts").isEmpty());

            Assertions.assertFalse(attributes.getJsonArray("urgencies").isEmpty());

            Assertions.assertFalse(attributes.getJsonArray("services").isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testUpdateServiceNowIntegrationProxyEnabled(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"yes\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\", \"proxy.enabled\" : \"yes\" }, \"integration.type\": \"ServiceNow\"}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testUpdateServiceNowIntegrationAutoSyncOn(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60,  \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"yes\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\", \"proxy.enabled\" : \"no\" }, \"integration.type\": \"ServiceNow\"}");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceNowCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetActiveIntegrationsAfterUpdate(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put("active", "yes"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            LOGGER.trace(String.format("received response : %s", result.encode()));

            Assertions.assertTrue(result.containsKey(RESULT));

            Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

            Assertions.assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(Integration.INTEGRATION_TYPE));

            Assertions.assertFalse(result.getJsonArray(RESULT).getJsonObject(0).getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty());

            Assertions.assertFalse(result.getJsonArray(RESULT).getJsonObject(0).getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET).isBlank());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testDeleteIntegration(VertxTestContext testContext)
    {
        TestAPIUtil.delete(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testTestServiceOpsIntegration(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": {\"target\": \"http://localhost:6334/integration-test/ops/200\",\"timeout\": 60,\"source\": \"192\",\"email.notification.recipients\": [\"<EMAIL>\"],\"auto.sync\": \"no\",\"sync.interval\": 2,\"alert.reoccurrence.action\": \"create\"},\"integration.type\": \"ServiceOps\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.SERVICEOPS).equalsIgnoreCase(result.getString(MESSAGE)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testTestServiceOpsIntegrationInvalidCredentials(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/400\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceOps\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(CREDENTIAL_ERROR));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testTestServiceOpsIntegrationUnauthorizedAccess(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/401\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceOps\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains("Unauthorized access"));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testTestServiceOpsIntegrationInvalidJsonResponse(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/404/\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceOps\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(INVALID_JSON));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testTestServiceOpsIntegrationURLTimeout(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/504\", \"timeout\": 1,  \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"ServiceOps\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(TIMEOUT));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testTestServiceOpsIntegrationProxyEnabled(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/200\", \"timeout\": 1, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\", \"proxy.enabled\":\"yes\" }, \"integration.type\": \"ServiceOps\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testUpdateServiceOpsIntegrationProxyEnabled(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/200\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\", \"proxy.enabled\" : \"yes\" }, \"integration.type\": \"ServiceOps\" }");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000001L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    Assertions.assertFalse(IntegrationConfigStore.getStore().getItem(10000000000001L).getString(Integration.INTEGRATION_ACCESS_TOKEN).isBlank());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testUpdateServiceOpsIntegration(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/ops/200\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\", \"proxy.enabled\" : \"no\" }, \"integration.type\": \"ServiceOps\" }");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, serviceOpsCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000001L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    Assertions.assertFalse(IntegrationConfigStore.getStore().getItem(10000000000001L).getString(Integration.INTEGRATION_ACCESS_TOKEN).isBlank());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testGetDefaultAtlassianJiraIntegration(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_TYPE));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_CONTEXT));

            Assertions.assertTrue(result.containsKey(Integration.INTEGRATION_ATTRIBUTES));

            Assertions.assertTrue(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName().equalsIgnoreCase(result.getString(Integration.INTEGRATION_TYPE)));

            Assertions.assertEquals(60, result.getJsonObject(Integration.INTEGRATION_CONTEXT).getInteger(TIMEOUT));

            Assertions.assertTrue(result.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.ALERT_REOCCURRENCE_ACTION));

            Assertions.assertEquals("create", result.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(Integration.ALERT_REOCCURRENCE_ACTION));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testTestAtlassianJiraIntegration(VertxTestContext testContext)
    {
        var payload = new JsonObject("{\"integration.context\": {\"target\": \"http://localhost:6334/integration-test/jira/200\",\"timeout\": 60,\"source\": \"192\",\"email.notification.recipients\": [\"<EMAIL>\"],\"auto.sync\": \"no\",\"sync.interval\": 2,\"alert.reoccurrence.action\": \"create\"},\"integration.type\": \"Atlassian Jira\"}").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, jiraCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).equalsIgnoreCase(result.getString(MESSAGE)));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testTestAtlassianJiraIntegrationInvalidCredentials(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/jira/401\", \"timeout\": 60, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"Atlassian Jira\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, jiraCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(CREDENTIAL_ERROR));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testTestAtlassianJiraIntegrationURLTimeout(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/jira/504\", \"timeout\": 1,  \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\" }, \"integration.type\": \"Atlassian Jira\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, jiraCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_FAIL));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(TIMEOUT));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testTestAtlassianJiraIntegrationProxyEnabled(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/jira/200\", \"timeout\": 1, \"source\": \"192\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"create\", \"proxy.enabled\":\"yes\" }, \"integration.type\": \"Atlassian Jira\" }").put(SESSION_ID, TestUtil.getSessionId());

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, jiraCredentialProfileId);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_INTEGRATION_TEST.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                    Assertions.assertTrue(result.getString(MESSAGE).contains(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()));

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_INTEGRATION_TEST, payload);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testUpdateAtlassianJiraIntegration(VertxTestContext testContext)
    {
        var payload = new JsonObject("{ \"integration.context\": { \"target\": \"http://localhost:6334/integration-test/jira/200\", \"timeout\": 60,\"email.notification.recipients\": [ \"<EMAIL>\" ], \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\", \"proxy.enabled\" : \"no\" }, \"integration.type\": \"Atlassian Jira\" }");

        payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, jiraCredentialProfileId);

        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000004L, payload,
                testContext.succeeding(response -> testContext.verify(() ->
                {
                    Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                    Assertions.assertFalse(response.bodyAsJsonObject().isEmpty());

                    Assertions.assertTrue(response.bodyAsJsonObject().containsKey(ID));

                    Assertions.assertEquals(STATUS_SUCCEED, response.bodyAsJsonObject().getString(STATUS));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testAtlassianJiraSyncJobResult(VertxTestContext testContext)
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(30), timer ->
                testContext.verify(() ->
                {
                    try
                    {
                        var item = IntegrationConfigStore.getStore().getItem(10000000000004L);

                        Assertions.assertNotNull(item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES));

                        LOGGER.info(String.format("item : %s", item.encode()));

                        var attributes = item.getJsonObject(Integration.INTEGRATION_ATTRIBUTES);

                        Assertions.assertFalse(attributes.getJsonArray("projects").isEmpty());

                        Assertions.assertFalse(attributes.getJsonArray("projects").getJsonObject(0).getJsonArray("statuses").isEmpty());

                        Assertions.assertFalse(attributes.getJsonArray("projects").getJsonObject(0).getJsonArray("issue.types").isEmpty());

                        var issueType = attributes.getJsonArray("projects").getJsonObject(0).getJsonArray("issue.types").getJsonObject(0);

                        Assertions.assertFalse(issueType.getJsonArray("assignee").isEmpty());

                        Assertions.assertFalse(issueType.getJsonArray("components").isEmpty());

                        testContext.completeNow();
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        testContext.failNow(exception);
                    }
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testGetActiveIntegrations(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_API_ENDPOINT + "?filter=" + new JsonObject().put("active", "yes"), testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            LOGGER.trace(String.format("received response : %s", result.encode()));

            Assertions.assertTrue(result.containsKey(RESULT));

            Assertions.assertFalse(result.getJsonArray(RESULT).isEmpty());

            Assertions.assertEquals(4, result.getJsonArray(RESULT).size());

            testContext.completeNow();
        })));
    }


}
