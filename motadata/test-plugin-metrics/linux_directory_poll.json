{"***********": {"errors": [], "object.ip": "***********", "objects": [{"object.name": "/var/log"}], "password": "Mind@123", "port": 22, "result": {"system.directory": [{"status": "Up", "system.directory": "/home/<USER>/mind8/linux space", "system.directory.creation.time": "2020-12-09 15:23:29", "system.directory.dirs": 1, "system.directory.files": 1, "system.directory.last.modified.time": "2020-12-09 15:23:29", "system.directory.mode.group": "read write execute", "system.directory.mode.others": "read execute", "system.directory.mode.owner": "read write execute", "system.directory.modified.duration.minutes": 787380, "system.directory.owner": "motadata", "system.directory.size.bytes": 4096}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/var/log"}], "password": "motadata", "port": 22, "result": {"system.directory": [{"status": "Down", "system.directory": "/home/<USER>/mind8/linux space"}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/var/log"}], "password": "motadata", "port": 22, "result": {"system.directory": [{"status": "Down", "system.directory": "/home/<USER>/mind8/linux space"}]}, "timeout": 60, "username": "motadata"}, "***********": {"errors": [], "object.ip": "***********", "objects": [{"object.name": "/var/log"}], "password": "motadata", "port": 22, "result": {"system.directory": [{"status": "Down", "system.directory": "/home/<USER>/mind8/linux space"}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/var/log"}], "password": "motadata", "port": 22, "result": {"system.directory": [{"status": "Down", "system.directory": "/home/<USER>/mind8/linux space"}]}, "timeout": 60, "username": "motadata"}, "************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "/var/log"}], "password": "motadata", "port": 22, "result": {"system.directory": [{"status": "Down", "system.directory": "/home/<USER>/mind8/linux space"}]}, "timeout": 60, "username": "motadata"}}