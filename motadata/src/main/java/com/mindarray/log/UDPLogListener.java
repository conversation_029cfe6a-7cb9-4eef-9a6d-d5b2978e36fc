/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.streaming.StreamingEngine;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.datagram.DatagramSocketOptions;
import io.vertx.core.json.JsonObject;

import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * A verticle responsible for listening for log messages over UDP connections.
 * <p>
 * The UDPLogListener creates a UDP socket that listens on a configured port for
 * incoming log messages. It performs the following tasks:
 * <ul>
 *   <li>Listens for UDP packets on the configured port</li>
 *   <li>Parses incoming log messages</li>
 *   <li>Creates log events with metadata (source IP, timestamp, etc.)</li>
 *   <li>Forwards log events to the appropriate event bus address</li>
 *   <li>Supports streaming log messages for real-time monitoring</li>
 *   <li>Tracks log quota usage</li>
 * </ul>
 * <p>
 * The listener can be configured to send logs directly to the log processing pipeline or
 * to a log processor component based on the PROCESSOR_ENABLED setting.
 * <p>
 * UDP is a connectionless protocol, which makes it suitable for high-volume
 * log collection where some packet loss is acceptable.
 */
public class UDPLogListener extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(UDPLogListener.class, GlobalConstants.MOTADATA_LOG, "UDP Log Listener");

    /**
     * Flag indicating whether log processor is enabled
     */
    private static final boolean PROCESSOR_ENABLED = MotadataConfigUtil.logProcessorEnabled();

    /**
     * Flag indicating whether log streaming is enabled
     */
    private boolean streaming = false;

    /**
     * Initializes and starts the UDP log listener.
     * <p>
     * This method performs the following tasks:
     * <ol>
     *   <li>Creates a UDP socket with appropriate options</li>
     *   <li>Configures the socket to listen on the specified port</li>
     *   <li>Sets up handlers for incoming packets</li>
     *   <li>Sets up an end handler for when the socket is closed</li>
     * </ol>
     *
     * @param promise A promise that will be completed when initialization is done or failed if an error occurs
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Create a UDP socket with IPv6 disabled, port reuse enabled, and address reuse enabled
        vertx.createDatagramSocket(new DatagramSocketOptions().setIpV6(false).setReusePort(true).setReuseAddress(true)).listen(
                MotadataConfigUtil.getUDPPort(), "0.0.0.0", result ->
                {
                    if (result.succeeded())
                    {
                        // Log successful connection
                        LOGGER.info(String.format("UDP Log listener connected to %s ", MotadataConfigUtil.getUDPPort() + ":" + MotadataConfigUtil.getVIPIPAddress()));

                        // Set up handler for incoming UDP packets
                        result.result().handler(packet ->
                        {
                            // Extract log message from packet data
                            var log = packet.data().getString(0, packet.data().length());

                            // Check if we're in EVENT_COLLECTOR mode or if we have quota available
                            if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR || LicenseUtil.updateUsedLogQuota(log.getBytes().length))
                            {
                                // Create event object with metadata
                                var event = new JsonObject()
                                        .put(EventBusConstants.EVENT_VOLUME_BYTES, log.getBytes().length)
                                        .put(EventBusConstants.EVENT, log.trim())
                                        .put(EventBusConstants.EVENT_SOURCE, packet.sender().host())
                                        .put(LogEngineConstants.RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(EVENT_TYPE, EVENT_LOG);

                                // Route event based on processor configuration
                                if (PROCESSOR_ENABLED)
                                {
                                    // Send to log processor
                                    vertx.eventBus().send(EVENT_LOG_PROCESS, event);
                                }
                                else
                                {
                                    // Send directly to log handler
                                    vertx.eventBus().send(EVENT_LOG, event);
                                }

                                // If streaming is enabled, broadcast the event for real-time monitoring
                                if (streaming)
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_BROADCAST, new JsonObject().put(StreamingEngine.STREAMING_TYPE, StreamingEngine.StreamingType.LOG_TAIL.getName()).put(EVENT_CONTEXT, event));
                                }

                                // Log trace information if enabled
                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("log received from UDP source: %s %s", packet.sender().host(), CommonUtil.getString(log)));
                                }
                            }
                        });

                        // Complete the promise to signal successful initialization
                        promise.complete();
                    }
                    else
                    {
                        // Log connection failure
                        LOGGER.info(String.format("UDP Log listener failed to connected to %s ", MotadataConfigUtil.getUDPPort() + ":" + MotadataConfigUtil.getVIPIPAddress()));

                        // Fail the promise with the cause of the failure
                        promise.fail(result.cause());
                    }

                }
                // Set up handler for when the socket is closed
        ).endHandler(result -> LOGGER.info("UDP log listener down..."));


        // Set up a local consumer for change notifications
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            // Handle different types of change notifications
            switch (EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                // Enable streaming when START_LOG_TAIL notification is received
                case START_LOG_TAIL -> streaming = true;

                // Disable streaming when STOP_LOG_TAIL notification is received
                case STOP_LOG_TAIL -> streaming = false;

                // Ignore other notification types
                default ->
                {
                    // do nothing
                }
            }

            // Set up exception handler for the consumer
        }).exceptionHandler(LOGGER::error);
    }
}
