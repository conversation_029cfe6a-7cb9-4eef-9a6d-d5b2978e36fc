/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  23-Jun-2025     Darshan Parmar  MOTADATA-6583 : Container Orchestration Category support added
	20-May-2025     Aagam               MOTADATA-5847 : Added windows build support
*/

package com.mindarray.nms;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.notification.Notification;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;

import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.Discovery.*;
import static com.mindarray.api.Tag.TAG;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;


/**
 * The DiscoveryEngine class is responsible for managing discovery operations within the Motadata platform.
 * It extends the AbstractVerticle class provided by Vert.x to enable asynchronous and concurrent event-driven behavior.
 * <p>
 * Key Responsibilities:
 * - Handles discovery-related events such as discovery run and completion.
 * - Manages worker executors for executing discovery tasks.
 * - Tracks discovery events and idle worker statuses by discovery profile.
 * - Publishes updates and notifications to the event bus for UI and other system components.
 * <p>
 * Internal Components:
 * - workerExecutor: A shared worker executor for performing discovery-related tasks asynchronously.
 * - discoveryEvents: A map that tracks discovery events corresponding to different discovery profiles.
 * - idleWorkersByDiscovery: A map that tracks idle worker count for each running discovery profile.
 * - eventProbes: An integer that represents the number of probes related to events (internal usage).
 * - timeHandlerActive: A flag to indicate whether a time handler is currently active.
 * <p>
 * Event Bus Consumers:
 * 1. EVENT_ENGINE_STATS: Responds to requests for engine statistics such as pending events and idle workers.
 * 2. EVENT_DISCOVERY_RUN: Handles events to initiate discovery process for specified discovery profiles. Coordinates with
 * other system components for event routing and notification handling.
 * 3. EVENT_DISCOVERY_COMPLETE: Processes completion of a discovery process. Handles tasks such as mapping previous results,
 * saving discovery outcomes, and cleaning up old results.
 * <p>
 * Error Handling:
 * - Errors and exceptions encountered during the discovery process are logged and propagated to appropriate event handlers
 * for further processing.
 * - Notifications for failed or incomplete discovery executions are sent to the event bus for UI updates or retry mechanisms.
 * <p>
 * System Interactions:
 * - Publishes notifications about discovery state changes to the UI and other components.
 * - Interacts with the system's configuration stores (e.g., DiscoveryConfigStore, DiscoveryCacheStore) for retrieving and
 * updating discovery configurations and metadata.
 * - Handles health statistics publishing and monitoring for scheduled or manually triggered tasks.
 */
public class DiscoveryEngine extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DiscoveryEngine.class, GlobalConstants.MOTADATA_NMS, "Discovery Engine");

    private final WorkerExecutor workerExecutor = Bootstrap.vertx().createSharedWorkerExecutor("Discovery Engine", MotadataConfigUtil.getDiscoveryWorkers(), 60L, TimeUnit.MINUTES);

    private final Map<Long, List<JsonObject>> discoveryEvents = new HashMap<>();

    private final Map<Long, Integer> idleWorkersByDiscovery = new HashMap<>();

    private int eventProbes;

    private boolean timeHandlerActive = false;

    /**
     * Initializes the DiscoveryEngine verticle and sets up event bus consumers for handling discovery-related events.
     * This method is called when the verticle is deployed by Vert.x.
     * <p>
     * Key responsibilities:
     * 1. Sets up a consumer for ENGINE_STATS events to report health statistics about pending events and idle workers
     * 2. Sets up a consumer for DISCOVERY_RUN events to handle discovery execution requests
     * 3. Sets up a consumer for DISCOVERY_COMPLETE events to process discovery completion
     * 4. Sets up a consumer for DISCOVERY_ABORT events to handle manual abortion of discovery processes
     *
     * @param promise A Promise that should be completed when initialization is done, or failed if initialization fails
     * @throws Exception If any error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
        {
            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_DISCOVERY)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, discoveryEvents.values().stream().map(List::size).reduce(0, Integer::sum))
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkersByDiscovery.values().stream().reduce(0, Integer::sum))));

            }
            else
            {
                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_DISCOVERY)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, discoveryEvents.values().stream().map(List::size).reduce(0, Integer::sum))
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkersByDiscovery.values().stream().reduce(0, Integer::sum))));
            }
        });

        if (Bootstrap.bootstrapType() == BootstrapType.APP && (Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name())) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
        {
            /*
            Consumer that receive discovery run event for particular discovery profile and build target based on discovery profiles
            After building targets will pass event to event router engine for further processing
             */
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DISCOVERY_RUN, message ->
            {
                try
                {
                    var event = message.body();

                    var discoveryId = event.getLong(ID);

                    var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

                    if (discovery.containsKey(DISCOVERY_CONTEXT))
                    {
                        discovery.mergeIn(discovery.getJsonObject(DISCOVERY_CONTEXT));

                        discovery.remove(DISCOVERY_CONTEXT);
                    }

                    discovery.put(DISCOVER_DOWN_INTERFACE_STATUS, MotadataConfigUtil.getDownInterfaceDiscoveryStatus());

                    discoveryEvents.put(discovery.getLong(ID), new ArrayList<>());

                    idleWorkersByDiscovery.put(discovery.getLong(ID), MotadataConfigUtil.getDiscoveryEventQueueSize());

                    if (!event.containsKey(EventBusConstants.EVENT_SCHEDULER)) // for scheduler  discovery is already updated in cache store in custom job
                    {
                        DiscoveryCacheStore.getStore().startDiscovery(discoveryId);

                        DiscoveryConfigStore.getStore().markDiscoveryStatusAsRunning(discoveryId);
                    }

                    LOGGER.debug(String.format("discovery %s started successfully...", discovery.getString(DISCOVERY_NAME)));

                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.RUN_DISCOVERY.name())
                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                            .put(ID, discoveryId));

                    vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY_RUN)
                            .put(EventBusConstants.EVENT_ID, DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId))
                            .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                            .put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)
                            .put(EventBusConstants.EVENT_CONTEXT, discovery).mergeIn(event));

                    // notify ui about discovery state changes
                    EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATE_CHANGE, new JsonObject().put(ID, discoveryId).put(NMSConstants.STATE, NMSConstants.STATE_RUNNING)
                            .put(DISCOVERY_STATUS, DiscoveryConfigStore.getStore().getItem(discoveryId).getString(DISCOVERY_STATUS))); // publish to ui for discovery state changes

                    //#3407 - running discovery can not Abort by another user so publish user name in "ui.event.discovery.progress" event
                    EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROGRESS, new JsonObject().put(STATUS, STATUS_SUCCEED).put(MESSAGE, InfoMessageConstants.DISCOVERY_OBJECT_CALCULATION_STARTED)
                            .put(ID, discoveryId).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));

                    start(discovery, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER).onComplete(result ->
                    {
                        if (result.failed()) // by any reason discovery is failed
                        {
                            var status = String.format("Last ran failed at %s", DateTimeUtil.timestamp());

                            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATE_CHANGE, new JsonObject().put(ID, discoveryId).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING).put(DISCOVERY_STATUS, status));

                            //#3407 - running discovery can not Abort by another user so publish user name in "ui.event.discovery.progress" event
                            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROGRESS, new JsonObject().put(ID, discoveryId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DISCOVERY_START).put(STATUS, STATUS_FAIL).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_START_FAILED, result.cause().getMessage())).put(USER_NAME, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER));

                            LOGGER.warn(String.format("failed to start discovery %s due to %s", discovery.getString(DISCOVERY_NAME), result.cause().getMessage()));

                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_ID, DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId))
                                    .put(MESSAGE, result.cause().getMessage()).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DISCOVERY_START).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                            update(discoveryId, status, null);

                            remove(discoveryId);

                            idleWorkersByDiscovery.remove(discoveryId);

                            vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.COMPLETE_DISCOVERY.name())
                                    .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                    .put(ID, discoveryId));
                        }
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            /*
             * Consumer that receive discovery complete event..
             * 1. Map previous discovery result target -> provision status
             * 2. Prepare discovery results
             * 3. Drop old discovery results
             * 4. Save new discovery results
             */
            vertx.eventBus().<Long>localConsumer(EventBusConstants.EVENT_DISCOVERY_COMPLETE, message ->
            {
                try
                {
                    var discoveryId = message.body();

                    // to handle duplicate result in database -> as remote event router re-assigned discovery event to another collector and at that time first qualified collector also queued that than progress is going above 100 and every time it update the result in database
                    if (discoveryEvents.containsKey(discoveryId) || idleWorkersByDiscovery.containsKey(discoveryId))
                    {
                        remove(discoveryId);

                        idleWorkersByDiscovery.remove(discoveryId);

                        vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.COMPLETE_DISCOVERY.name())
                                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(ID, discoveryId));
                        //dump to db
                        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROGRESS, new JsonObject().put(ID, discoveryId).put(MESSAGE, InfoMessageConstants.DISCOVERY_PROGRESS_DB_RESULT_SAVE_MESSAGE));

                        Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + discoveryId, future ->
                        {
                            var previousDiscoveredObjects = new JsonObject();

                            if (future.succeeded() && !future.result().isEmpty())
                            {
                                for (var index = 0; index < future.result().size(); index++)
                                {
                                    var result = future.result().getJsonObject(index);

                                    previousDiscoveredObjects.put(result.getString(OBJECT_TARGET), result.getString(AIOpsObject.OBJECT_STATE));

                                }
                            }

                            var objects = DiscoveryCacheStore.getStore().getDiscoveredObjects(discoveryId).addAll(DiscoveryCacheStore.getStore().getFailedObjects(discoveryId));

                            if (objects != null && !objects.isEmpty())
                            {
                                var discoveredObjects = prepare(objects, DiscoveryConfigStore.getStore().getItem(discoveryId), previousDiscoveredObjects);

                                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, DiscoveryCacheStore.getStore().getDiscoveryStatistics(discoveryId).put(ID, discoveryId).put(UserNotificationUtil.USER_NOTIFICATION_TYPE, UserNotificationUtil.USER_NOTIFICATION_TYPE_SYSTEM).put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TYPE, EVENT_DISCOVERY));

                                vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_TRACKER, new JsonObject().put(EventBusConstants.EVENT_ID,
                                                DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId)),
                                        reply ->
                                        {
                                            var event = reply.result().body();

                                            if (reply.succeeded() && !discoveredObjects.isEmpty() && event.containsKey(EventBusConstants.EVENT_SCHEDULER))
                                            {
                                                for (var index = 0; index < discoveredObjects.size(); index++)
                                                {
                                                    SchedulerCacheStore.getStore().addSchedulerEvent(event.getLong(EVENT_SCHEDULER), discoveredObjects.getJsonObject(index));
                                                }
                                            }

                                        });

                                drop(discoveryId).onComplete(asyncResult ->
                                        Bootstrap.configDBService().saveAll(DBConstants.TBL_DISCOVERY_RESULT + discoveryId, discoveredObjects, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                                result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        LOGGER.info(String.format("discovery %s result saved successfully...", DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId)));

                                                        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROGRESS, new JsonObject().put(STATUS, STATUS_SUCCEED).put(ID, discoveryId));
                                                    }
                                                    else
                                                    {
                                                        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROGRESS, new JsonObject().put(STATUS, STATUS_FAIL).put(ID, discoveryId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DISCOVERY_RESULT_SAVE).put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())).put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_PROGRESS_DB_RESULT_SAVE_FAILED, result.cause().getMessage())));

                                                        LOGGER.warn(String.format("failed to save discovery %s result in the db...", DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId)));
                                                    }

                                                    vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID,
                                                            DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId)));

                                                    var status = String.format("Last ran at %s", DateTimeUtil.timestamp());

                                                    update(discoveryId, status, discoveredObjects);

                                                    EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATE_CHANGE, new JsonObject().put(ID, discoveryId).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING)
                                                            .put(DISCOVERY_STATUS, status));
                                                }));
                            }

                            else
                            {
                                vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_ID,
                                        DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId)));

                                vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, DiscoveryCacheStore.getStore().getDiscoveryStatistics(discoveryId).put(ID, discoveryId).put(UserNotificationUtil.USER_NOTIFICATION_TYPE, UserNotificationUtil.USER_NOTIFICATION_TYPE_SYSTEM).put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_TYPE, EVENT_DISCOVERY));

                                var status = String.format("Last ran at %s", DateTimeUtil.timestamp());

                                update(discoveryId, status, null);

                                EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATE_CHANGE, new JsonObject().put(ID, discoveryId).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING)
                                        .put(DISCOVERY_STATUS, status));
                            }
                        });
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            /*
             * Consumer that receives discovery abort event
             */
            vertx.eventBus().<Long>localConsumer(EventBusConstants.EVENT_DISCOVERY_ABORT, message ->
            {
                try
                {
                    var discoveryId = message.body();

                    idleWorkersByDiscovery.remove(discoveryId);

                    vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ABORT_DISCOVERY.name())
                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                            .put(ID, discoveryId));

                    if (DiscoveryCacheStore.getStore().discoveryRunning(discoveryId))
                    {
                        abort(discoveryId);

                        vertx.eventBus().send(EventBusConstants.EVENT_ABORT, new JsonObject().put(EventBusConstants.EVENT_ID, DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId)));

                        var status = String.format("Aborted at %s", DateTimeUtil.timestamp());

                        update(discoveryId, status, null);

                        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATE_CHANGE, new JsonObject().put(ID, discoveryId).put(NMSConstants.STATE, NMSConstants.STATE_NOT_RUNNING)
                                .put(DISCOVERY_STATUS, status));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

        }

        else
        {
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DISCOVERY_RUN, message ->
            {

                var event = message.body();

                discoveryEvents.put(event.getLong(ID), new ArrayList<>());

                idleWorkersByDiscovery.put(event.getLong(ID), MotadataConfigUtil.getDiscoveryEventQueueSize());

            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<Long>localConsumer(EventBusConstants.EVENT_DISCOVERY_COMPLETE, message ->
            {

                remove(message.body());

                idleWorkersByDiscovery.remove(message.body());

            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<Long>localConsumer(EventBusConstants.EVENT_DISCOVERY_ABORT, message ->
            {

                idleWorkersByDiscovery.remove(message.body());

                abort(message.body());
            }).exceptionHandler(LOGGER::error);

        }

        discoveryEvents.put(DUMMY_ID, new ArrayList<>());

        idleWorkersByDiscovery.put(DUMMY_ID, MotadataConfigUtil.getDiscoveryEventQueueSize());

        /*
         * Consumer that receives discovery events
         * 1. Timer for every 500 ms to check discovery events and increase the probe count...
         * If any discovery event is received than probe count is zero else increase probe count and after reaching threshold limit of 10 count motadata will cancel timer for save cpu cycle
         */
        vertx.eventBus().<JsonObject>localConsumer(EVENT_DISCOVERY, message ->
        {
            var event = message.body();

            if (discoveryEvents.containsKey(event.getLong(ID)))
            {
                discoveryEvents.get(event.getLong(ID)).add(event);
            }

            if (!timeHandlerActive)
            {
                eventProbes = 0;

                timeHandlerActive = true;

                vertx.setPeriodic(500, timer -> discoveryEvents.keySet().forEach(discoveryId ->
                {
                    try
                    {
                        var iterator = discoveryEvents.get(discoveryId).listIterator();

                        if (iterator.hasNext())
                        {
                            eventProbes = 0;
                        }

                        else
                        {
                            eventProbes++;
                        }

                        if (idleWorkersByDiscovery.get(discoveryId) > 0 && iterator.hasNext())
                        {
                            var batches = new ArrayList<Map<Long, JsonObject>>();

                            var discovery = discoveryEvents.get(discoveryId).getFirst();

                            var metricPlugin = !discoveryId.equals(DUMMY_ID) ? discovery.getString(Metric.METRIC_PLUGIN) : null;

                            // we don't have store access in remote event processor hence check batch probe by plugin.engine or metric plugin is ping
                            // in first condition if category is cloud means only single requests in go engine so in that case we need to consider process timeout as default metric timeout
                            // instead discovery.batch.timeout so put not contains cloud category check...for all other categories batch probe will be decide from hasBatchSupport
                            var batchProbe = (!discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) && PluginEngineConstants.hasBatchSupport(discovery))
                                    || (metricPlugin != null && metricPlugin.equalsIgnoreCase(NMSConstants.MetricPlugin.PING.getName()));

                            var pluginEngine = PluginEngineConstants.PluginEngine.valueOfName(discovery.getString(PluginEngineConstants.PLUGIN_ENGINE));

                            var allocations = 0;

                            var pendingWorkers = idleWorkersByDiscovery.get(discoveryId);

                            var batchSize = batchProbe ? MotadataConfigUtil.getDiscoveryBatchSize() : 1;

                            while (allocations < pendingWorkers && iterator.hasNext())
                            {
                                allocations++;

                                var events = new HashMap<Long, JsonObject>();

                                batches.add(events);

                                for (var index = 0; index < batchSize && iterator.hasNext(); index++)
                                {
                                    var discoveryEvent = iterator.next();

                                    events.put(discoveryEvent.getLong(EVENT_ID), discoveryEvent);

                                    iterator.remove();
                                }

                                if (!events.isEmpty())
                                {
                                    idleWorkersByDiscovery.put(discoveryId, idleWorkersByDiscovery.get(discoveryId) - 1);
                                }
                            }

                            var index = 0;

                            for (var i = 0; i < pendingWorkers; i++)
                            {
                                if (!batches.isEmpty() && index < batches.size())
                                {
                                    var eventIdsByTarget = new HashMap<String, Long>();

                                    var contexts = qualify(batches.get(index), eventIdsByTarget);

                                    index++;

                                    if (contexts != null && !contexts.isEmpty())
                                    {
                                        var targets = new JsonArray(new ArrayList<>(eventIdsByTarget.keySet()));

                                        workerExecutor.<Void>executeBlocking(future ->
                                        {
                                            try
                                            {
                                                for (var eventId : eventIdsByTarget.values())
                                                {
                                                    EventBusConstants.startEvent(eventId, Thread.currentThread().getName());
                                                }

                                                if (runPingCheck(contexts, targets, eventIdsByTarget, contexts.values().stream().findFirst().get()) && runPortCheck(contexts, targets, discovery, eventIdsByTarget, contexts.values().stream().findFirst().get(), discoveryId))
                                                {
                                                    var context = contexts.values().stream().findFirst().get();

                                                    var timeout = batchProbe ? MotadataConfigUtil.getDiscoveryBatchTimeoutSeconds() : context.getInteger(GlobalConstants.TIMEOUT, 60);

                                                    if (discovery.containsKey(DISCOVERY_CREDENTIAL_PROFILES) && !discovery.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES).isEmpty())
                                                    {
                                                        timeout = discoveryId.equals(DUMMY_ID) ? timeout * context.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES).size()
                                                                : timeout * discovery.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES).size();
                                                    }

                                                    WorkerUtil.spawnWorker(contexts, context, eventIdsByTarget.values().stream().map(CommonUtil::getLong).collect(Collectors.toList()), timeout, false, pluginEngine, System.currentTimeMillis(), true);
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                send(contexts, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, INTERNAL_ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()), targets, false, eventIdsByTarget);
                                            }
                                            finally
                                            {
                                                future.complete();
                                            }
                                        }, false, result -> complete(discoveryId));
                                    }
                                    else
                                    {
                                        complete(discoveryId);
                                    }
                                }
                            }
                        }

                        if (eventProbes > 10) //max 10 probes...
                        {
                            //disable timer to save cpu cycle...

                            vertx.cancelTimer(timer);

                            timeHandlerActive = false;
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }));

            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();

    }

    /**
     * Handles the completion of a discovery task for a specific discovery profile.
     * <p>
     * This method is called when a discovery task completes, either successfully or with failure.
     * It manages the cleanup of resources and updates the idle worker count for the discovery profile.
     * <p>
     * Note: The condition check is necessary because sometimes the discovery complete event executes first
     * and deletes the particular discovery ID, which could cause a null pointer exception.
     *
     * @param discoveryId The unique identifier of the discovery profile that has completed
     */
    private void complete(long discoveryId)
    {
        //why need this condition ? -> sometimes discovery complete event executes first and it delete particular discovery id so this line will throw null error
        if (idleWorkersByDiscovery.containsKey(discoveryId))
        {
            if (discoveryId != DUMMY_ID && !DiscoveryCacheStore.getStore().discoveryRunning(discoveryId) && discoveryEvents.get(discoveryId) != null && discoveryEvents.get(discoveryId).isEmpty())
            {
                remove(discoveryId);
            }

            idleWorkersByDiscovery.put(discoveryId, idleWorkersByDiscovery.get(discoveryId) + 1);
        }
    }

    /**
     * Aborts a running discovery process for a specific discovery profile.
     * <p>
     * This method is called when a discovery process needs to be manually aborted.
     * It retrieves all events associated with the discovery ID, deletes them from the event cache store,
     * and sends abort notifications for each event.
     *
     * @param discoveryId The unique identifier of the discovery profile to abort
     */
    private void abort(long discoveryId)
    {
        if (discoveryEvents.containsKey(discoveryId))
        {
            var events = discoveryId == DUMMY_ID ? discoveryEvents.get(discoveryId) : discoveryEvents.remove(discoveryId);

            for (JsonObject event : events)
            {
                EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                send(event.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_MANUAL_ABORTED).put(MESSAGE, "discovery aborted"));
            }
        }
    }

    /**
     * Removes a discovery profile's events from the tracking map.
     * <p>
     * This method is called during cleanup after a discovery process completes or is aborted.
     * It removes the discovery ID from the discoveryEvents map, but only if the ID is greater than DUMMY_ID,
     * which indicates it's not an application discovery.
     *
     * @param discoveryId The unique identifier of the discovery profile to remove from tracking
     */
    private void remove(long discoveryId)
    {
        // if not application discovery than and only remove discovery id
        if (discoveryId > DUMMY_ID)
        {
            discoveryEvents.remove(discoveryId);
        }
    }

    /**
     * Validates and filters discovery context items based on event validity.
     * <p>
     * This method iterates through the provided context map and checks if each event is still valid
     * in the EventCacheStore. If an event is valid, it adds the event ID to the eventIdsByTarget map
     * for tracking. If an event is no longer valid, it removes the item from the context map.
     *
     * @param context          A map of event IDs to their corresponding context JsonObjects
     * @param eventIdsByTarget A map that associates target IPs with their corresponding event IDs
     * @return The filtered context map containing only valid events
     */
    private Map<Long, JsonObject> qualify(Map<Long, JsonObject> context, Map<String, Long> eventIdsByTarget)
    {
        if (context != null)
        {
            var iterator = context.keySet().iterator();

            while (iterator.hasNext())
            {
                var item = context.get(iterator.next());

                if (EventCacheStore.getStore().validItem(item.getLong(EventBusConstants.EVENT_ID)))
                {
                    eventIdsByTarget.put(item.getString(OBJECT_IP), item.getLong(EventBusConstants.EVENT_ID));

                    EventBusConstants.updateEvent(item.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));
                }
                else
                {
                    iterator.remove();
                }
            }
        }
        return context;
    }

    /**
     * Performs port connectivity checks for discovery targets.
     * <p>
     * This method attempts to connect to each target in the targets array on the specified port.
     * For each target, it:
     * 1. Checks if port checking is required for the object type
     * 2. Attempts to connect to the target on the specified port
     * 3. Updates the event status based on connectivity results
     * 4. For PORT type objects, completes the discovery if port is reachable
     * 5. For other object types, updates progress and continues discovery
     *
     * @param contexts         Map of event IDs to their corresponding context JsonObjects
     * @param targets          Array of target IPs or hostnames to check
     * @param discovery        The discovery profile configuration
     * @param eventIdsByTarget Map associating target IPs with their corresponding event IDs
     * @param event            The current event being processed
     * @param discoveryId      The unique identifier of the discovery profile
     * @return true if port check processing completed successfully, false otherwise
     */
    private boolean runPortCheck(Map<Long, JsonObject> contexts, JsonArray targets, JsonObject discovery, Map<String, Long> eventIdsByTarget, JsonObject event, Long discoveryId)
    {
        var status = true;

        try
        {
            var objectType = NMSConstants.Type.valueOfName(event.getString(AIOpsObject.OBJECT_TYPE));

            if (NMSConstants.portCheckRequire(objectType))
            {
                var port = discovery != null && !discoveryId.equals(DUMMY_ID) ? discovery.getInteger(PORT) : event.getInteger(PORT);

                var iterator = targets.iterator();

                while (iterator.hasNext())
                {
                    var target = CommonUtil.getString(iterator.next());

                    var eventId = eventIdsByTarget.get(target);

                    if (!event.containsKey(NMSConstants.PORT_CHECK_STATUS) || (event.containsKey(NMSConstants.PORT_CHECK_STATUS) && event.getString(NMSConstants.PORT_CHECK_STATUS).equalsIgnoreCase(YES)))
                    {
                        if (PortUtil.isConnected(target, port))
                        {
                            EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PORT_CHECKED, DateTimeUtil.timestamp()));

                            if (objectType == NMSConstants.Type.PORT)
                            {
                                eventIdsByTarget.remove(target);

                                send(contexts.remove(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, 100.0)
                                        .put(MESSAGE, DISCOVERY_OBJECT_DISCOVERED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                        .put(NMSConstants.OBJECTS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(AIOpsObject.OBJECT_TARGET, target))));


                                iterator.remove();
                            }
                            else
                            {
                                send(contexts.get(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, 66.66)
                                        .put(MESSAGE, InfoMessageConstants.DISCOVERY_PORT_REACHABLE));
                            }
                        }
                        else
                        {
                            EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PORT_CHECKED, DateTimeUtil.timestamp()));

                            eventIdsByTarget.remove(target);

                            send(contexts.remove(eventId).put(STATUS, STATUS_FAIL).put(DISCOVERY_PROGRESS, 100.0)
                                    .put(MESSAGE, DISCOVERY_PORT_NOT_REACHABLE).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT));

                            iterator.remove();
                        }

                        if (contexts.isEmpty())
                        {
                            status = false;
                        }
                    }
                    else if (event.containsKey(NMSConstants.PORT_CHECK_STATUS) && event.getString(NMSConstants.PORT_CHECK_STATUS).equalsIgnoreCase(NO))
                    {
                        EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PORT_CHECKED, DateTimeUtil.timestamp()));

                        if (objectType == NMSConstants.Type.PORT)
                        {
                            eventIdsByTarget.remove(target);

                            send(contexts.remove(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, 100.0)
                                    .put(MESSAGE, DISCOVERY_OBJECT_DISCOVERED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                    .put(NMSConstants.OBJECTS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(AIOpsObject.OBJECT_TARGET, target))));


                            iterator.remove();
                        }

                        else
                        {
                            send(contexts.get(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, 66.66)
                                    .put(MESSAGE, InfoMessageConstants.DISCOVERY_PORT_REACHABLE));
                        }
                    }

                }
            }
        }
        catch (Exception exception)
        {
            status = false;

            send(contexts, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, INTERNAL_ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()), targets, true, eventIdsByTarget);

            LOGGER.error(exception);
        }

        return status;
    }

    /**
     * Performs ICMP ping checks for discovery targets.
     * <p>
     * This method attempts to ping each target in the targets array to verify network reachability.
     * For each target, it:
     * 1. Attempts to ping the target using ICMP
     * 2. Updates the event status based on ping results
     * 3. For PING type objects, completes the discovery if ping is successful
     * 4. For other object types, updates progress and continues discovery
     * 5. Handles special cases like DNS resolution for hostname targets
     *
     * @param contexts         Map of event IDs to their corresponding context JsonObjects
     * @param targets          Array of target IPs or hostnames to check
     * @param eventIdsByTarget Map associating target IPs with their corresponding event IDs
     * @param event            The current event being processed
     * @return true if ping check processing completed successfully, false otherwise
     */
    private boolean runPingCheck(Map<Long, JsonObject> contexts, JsonArray targets, Map<String, Long> eventIdsByTarget, JsonObject event)
    {
        var status = true;

        var objectType = NMSConstants.Type.valueOfName(event.getString(AIOpsObject.OBJECT_TYPE));

        var category = NMSConstants.getCategory(objectType);

        if (NMSConstants.Category.CLOUD == category || (NMSConstants.pingCheckRequire(objectType) && (event.containsKey(NMSConstants.PING_CHECK_STATUS) && event.getString(NMSConstants.PING_CHECK_STATUS).equalsIgnoreCase(YES)) || objectType == NMSConstants.Type.PING))
        {
            JsonArray probes = null;

            String errorCode = null;

            StackTraceElement[] stackTraceElements = null;

            try
            {
                probes = category == NMSConstants.Category.CLOUD || objectType == NMSConstants.Type.CISCO_MERAKI ? eventIdsByTarget.values().stream().findFirst().map(id ->
                        new JsonArray().add(event.put(STATUS, PingUtil.isReachable("8.8.8.8", id) ? STATUS_UP : STATUS_DOWN))).orElse(new JsonArray())
                        : PingUtil.ping(targets, NMSConstants.PING_CHECK_TIMEOUT_SECONDS, event.getInteger(NMSConstants.PING_CHECK_RETRIES, 3), null, null);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                stackTraceElements = exception.getStackTrace();

                errorCode = exception.getMessage() != null && exception.getMessage().contains(PROCESS_TIMED_OUT) ? ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_PING_FAILED;
            }

            finally
            {
                eventIdsByTarget.values().forEach(eventId -> EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PING_CHECKED, DateTimeUtil.timestamp())));

                var successMessage = category == NMSConstants.Category.CLOUD ? DISCOVERY_INTERNET_CONNECTION_SUCCEEDED : DISCOVERY_PING_SUCCEEDED;

                var errorMessage = category == NMSConstants.Category.CLOUD ? DISCOVERY_INTERNET_CONNECTION_FAILED : DISCOVERY_PING_FAILED;

                if (probes != null && !probes.isEmpty())
                {
                    var progress = 0.0;

                    if (category == NMSConstants.Category.SERVER || category == NMSConstants.Category.VIRTUALIZATION ||
                            category == NMSConstants.Category.HCI || category == NMSConstants.Category.SDN
                            || category == NMSConstants.Category.STORAGE || category == NMSConstants.Category.CONTAINER_ORCHESTRATION || category == NMSConstants.Category.OTHER)
                    {
                        if (objectType == NMSConstants.Type.IBM_TAPE_LIBRARY)
                        {
                            progress = 50.0;
                        }
                        else if (objectType == NMSConstants.Type.PING)
                        {
                            progress = 100.00;
                        }

                        else
                        {
                            progress = 33.33;
                        }
                    }

                    else if (category == NMSConstants.Category.CLOUD)
                    {
                        progress = 50.0;
                    }

                    else if (category == NMSConstants.Category.NETWORK)
                    {
                        if (objectType == NMSConstants.Type.RUCKUS_WIRELESS)
                        {
                            progress = 33.33;
                        }

                        else
                        {
                            progress = 50.0;
                        }
                    }

                    else if (objectType == NMSConstants.Type.PING)
                    {
                        progress = 100.00;
                    }

                    var qualifiedTargets = new ArrayList<>();

                    for (var index = 0; index < probes.size(); index++)
                    {
                        var probe = probes.getJsonObject(index);

                        var eventId = eventIdsByTarget.get(probe.getString(OBJECT_TARGET));

                        if (probe.getString(STATUS).equalsIgnoreCase(STATUS_UP))
                        {
                            qualifiedTargets.add(probe.getString(OBJECT_TARGET));

                            if (objectType == NMSConstants.Type.PING)
                            {
                                eventIdsByTarget.remove(probe.getString(AIOpsObject.OBJECT_TARGET));

                                send(contexts.get(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, progress)
                                        .put(MESSAGE, successMessage)
                                        .put(NMSConstants.OBJECTS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(AIOpsObject.OBJECT_TARGET, probe.getString(AIOpsObject.OBJECT_TARGET)))));

                                contexts.remove(eventId);

                                targets.remove(probe.getString(OBJECT_TARGET));
                            }

                            else
                            {
                                send(contexts.get(eventId).put(STATUS, STATUS_SUCCEED).put(DISCOVERY_PROGRESS, progress)
                                        .put(MESSAGE, successMessage));
                            }
                        }

                        else
                        {
                            eventIdsByTarget.remove(probe.getString(OBJECT_TARGET));

                            send(contexts.remove(eventId).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PING_FAILED).put(STATUS, STATUS_FAIL).put(DISCOVERY_PROGRESS, 100.00)
                                    .put(MESSAGE, errorMessage));

                            targets.remove(probe.getString(OBJECT_TARGET));
                        }
                    }

                    errorCode = probes.isEmpty() ? ErrorCodes.ERROR_CODE_PING_FAILED : ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT;

                    var iterator = contexts.keySet().iterator();

                    while (iterator.hasNext())
                    {
                        var context = contexts.get(iterator.next());

                        if (!qualifiedTargets.contains(context.getString(OBJECT_IP)))
                        {
                            send(context.put(DISCOVERY_PROGRESS, 100.00).put(STATUS, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL)
                                    .put(ERROR_CODE, errorCode)
                                    .put(MESSAGE, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : errorMessage));

                            targets.remove(context.remove(OBJECT_IP));

                            eventIdsByTarget.remove(context.getString(OBJECT_IP));

                            iterator.remove();
                        }
                    }
                }
                else if (errorCode == null) // means process spawning issue for fping due to some environment variables and we don't get any probs at that time result is empty and errorcode is null
                {
                    send(contexts, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, errorMessage, null, targets, true, eventIdsByTarget);
                }

                else
                {
                    send(contexts, errorCode, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : errorMessage, CommonUtil.formatStackTrace(stackTraceElements), targets, true, eventIdsByTarget);
                }
            }

            if (contexts.isEmpty())
            {
                status = false;
            }
        }

        return status;
    }

    /**
     * Sends notifications for failed discovery attempts.
     * <p>
     * This method is called when a discovery operation fails for specific targets.
     * It creates and sends appropriate notifications with error details for each failed target.
     *
     * @param contexts         Map of event IDs to their corresponding context JsonObjects
     * @param errorCode        The error code indicating the type of failure
     * @param message          A human-readable message describing the error
     * @param error            Detailed error information or stack trace
     * @param targets          Array of targets that failed discovery
     * @param connectionProbe  Flag indicating if this is a connection probe failure
     * @param eventIdsByTarget Map associating target IPs with their corresponding event IDs
     */
    private void send(Map<Long, JsonObject> contexts, String errorCode, String message, String error, JsonArray targets, boolean connectionProbe, Map<String, Long> eventIdsByTarget)
    {
        var iterator = contexts.keySet().iterator();

        while (iterator.hasNext())
        {
            var context = contexts.get(iterator.next());

            if (error != null)
            {
                context.put(ERROR, error).put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR, error).put(ERROR_CODE, errorCode).put(MESSAGE, message)));
            }

            else
            {
                context.put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, message).put(ERROR_CODE, errorCode)));
            }

            send(context.put(STATUS, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL)
                    .put(ERROR_CODE, errorCode)
                    .put(DISCOVERY_PROGRESS, 100.00)
                    .put(MESSAGE, String.format(DISCOVERY_FAILED, message)));

            if (targets != null)
            {
                targets.remove(context.getString(OBJECT_IP));
            }

            if (connectionProbe)
            {
                eventIdsByTarget.remove(context.getString(OBJECT_IP));
            }

            iterator.remove();
        }
    }

    /**
     * Initiates a discovery process for a specific discovery profile.
     * <p>
     * This method generates discovery contexts based on the category and type of the discovery profile.
     * It handles different discovery scenarios:
     * - For SERVER, VIRTUALIZATION, NETWORK, OTHER, CLOUD, HCI, SDN, STORAGE categories or URL type:
     * Merges credential contexts based on credential profile selection
     * - For SERVICE_CHECK category: Sets up service check discovery
     * - For other types: Builds contexts based only on target selection (no credentials required)
     * <p>
     * The method creates appropriate contexts for each target, sets up futures for tracking progress,
     * and initiates the actual discovery process.
     *
     * @param discovery The discovery profile configuration as a JsonObject
     * @param user      The username of the user who initiated the discovery
     * @return A Future that completes when the discovery process is initiated, or fails if initialization fails
     */
    private Future<Void> start(JsonObject discovery, String user)
    {

        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        var targets = new ArrayList<JsonObject>();

        try
        {
            var discoveryType = CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TYPE));

            var category = NMSConstants.Category.valueOfName(discovery.getString(Discovery.DISCOVERY_CATEGORY));

            var type = NMSConstants.Type.valueOfName(CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_OBJECT_TYPE)));

            discovery.put(Metric.METRIC_PLUGIN, NMSConstants.getMetricPlugin(type));

            // timeout will be default timeout we have in our metrics db
            discovery.put(GlobalConstants.TIMEOUT, ObjectManagerCacheStore.getStore().getTimeoutByMetricPlugin(discovery.getString(Metric.METRIC_PLUGIN)));

            discovery.put(PLUGIN_ID, ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(discovery.getString(Metric.METRIC_PLUGIN)));

            if (category == NMSConstants.Category.SERVER
                    || category == NMSConstants.Category.VIRTUALIZATION
                    || category == NMSConstants.Category.NETWORK
                    || category == NMSConstants.Category.OTHER
                    || category == NMSConstants.Category.CLOUD
                    || category == NMSConstants.Category.HCI
                    || category == NMSConstants.Category.SDN
                    || category == NMSConstants.Category.STORAGE
                    || category == NMSConstants.Category.CONTAINER_ORCHESTRATION
                    || type == NMSConstants.Type.URL)
            {
                var credentials = category == NMSConstants.Category.NETWORK && discovery.containsKey(DISCOVERY_CONFIG_MANAGEMENT_STATUS) && discovery.getString(DISCOVERY_CONFIG_MANAGEMENT_STATUS).equalsIgnoreCase(YES) ? NMSConstants.filterCredentialProfiles(discovery, false) : CredentialProfileConfigStore.getStore().getItems(discovery.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES));

                if (credentials != null && !credentials.isEmpty())
                {
                    for (var index = 0; index < credentials.size(); index++)
                    {
                        var credential = credentials.getJsonObject(index);

                        credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                    }

                    discovery.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, credentials);
                }
                else if (category == NMSConstants.Category.NETWORK && discovery.containsKey(DISCOVERY_CONFIG_MANAGEMENT_STATUS) && discovery.getString(DISCOVERY_CONFIG_MANAGEMENT_STATUS).equalsIgnoreCase(YES))
                {
                    promise.fail(ErrorMessageConstants.CREDENTIAL_PROFILE_NOT_PROVIDED);
                }

                if (!promise.future().isComplete())
                {
                    if (type == NMSConstants.Type.URL)
                    {
                        startServiceCheckDiscovery(discovery, targets, futures, discoveryType, type);
                    }

                    else if (category == NMSConstants.Category.CLOUD || type == NMSConstants.Type.CISCO_MERAKI)
                    {
                        var context = new JsonObject();

                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        targets.add(context);

                        future.complete();
                    }

                    else
                    {
                        setTargets(discoveryType, type, discovery, targets, new JsonObject(), futures, false);
                    }
                }
            }

            else if (category == NMSConstants.Category.SERVICE_CHECK)
            {
                startServiceCheckDiscovery(discovery, targets, futures, discoveryType, type);
            }

            if (!promise.future().isComplete())
            {
                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        if (!targets.isEmpty()) // means targets is qualified to discover
                        {

                            EventBusConstants.updateEvent(DiscoveryCacheStore.getStore().getDiscoveryEventId(discovery.getLong(ID)),
                                    String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()),
                                    EventBusConstants.EVENT_STATE_RUNNING);

                            // for rest devices update targets in probes.... it is used when user refresh the page or come back to progress page from some other page at that time this probes will update in progress
                            if (category != NMSConstants.Category.CLOUD && category != NMSConstants.Category.HCI && category != NMSConstants.Category.SDN && category != NMSConstants.Category.STORAGE && category != NMSConstants.Category.CONTAINER_ORCHESTRATION) // for cloud we don't have target wise progress hence no need to track their events in probes
                            {
                                var probes = new JsonObject();

                                var iterator = targets.iterator();

                                var doneProbes = 0;

                                while (iterator.hasNext())
                                {
                                    var target = iterator.next();

                                    // in future if we want to increase limit for ping discovery only need to add condition here...now no need to do any validation in API side...
                                    if ((discoveryType == null || !discoveryType.equalsIgnoreCase(DISCOVERY_TYPE_CSV) || doneProbes < DISCOVERY_CSV_MAX_PROBE_LIMIT))
                                    {
                                        var context = new JsonObject().put(OBJECT_IP, target.getString(OBJECT_IP) != null ? target.getString(OBJECT_IP) : target.getString(OBJECT_TARGET))
                                                .put(OBJECT_TARGET, target.getString(OBJECT_TARGET))
                                                .put(DISCOVERY_PROGRESS, 0.0);

                                        if (target.containsKey(AIOpsObject.OBJECT_AGENT))
                                        {
                                            context.put(AIOpsObject.OBJECT_AGENT, target.getLong(AIOpsObject.OBJECT_AGENT));
                                        }

                                        if (!probes.containsKey(target.getString(OBJECT_TARGET)))
                                        {
                                            probes.put(target.getString(OBJECT_TARGET), new JsonArray(new ArrayList<>(1)));
                                        }

                                        probes.getJsonArray(target.getString(OBJECT_TARGET)).add(context);
                                    }

                                    doneProbes++;
                                }

                                DiscoveryCacheStore.getStore().addProbes(discovery.getLong(ID), probes);
                            }

                            DiscoveryCacheStore.getStore().init(discovery.getLong(ID), targets.size()); // update qualified targets size in cache store

                            discovery.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, discovery.containsKey(Discovery.DISCOVERY_METHOD)
                                    ? NMSConstants.DiscoveryMethod.valueOf(discovery.getString(Discovery.DISCOVERY_METHOD)).name() : NMSConstants.DiscoveryMethod.REMOTE.name());

                            if (discovery.getJsonArray(DISCOVERY_EVENT_PROCESSORS) != null)
                            {
                                discovery.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, discovery.getJsonArray(DISCOVERY_EVENT_PROCESSORS));
                            }

                            // update probes to ui
                            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_PROBES, new JsonObject().put(RESULT, targets).put(ID, discovery.getLong(ID)));

                            EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_DISCOVERY_STATISTICS, DiscoveryCacheStore.getStore().getDiscoveryStatistics(discovery.getLong(ID)).put(ID, discovery.getLong(ID)));

                            for (var target : targets)
                            {
                                // if discovery has tags the go with it , else check for target which might have tag from csv (multiple target might have multiple tags)
                                discovery.put(DISCOVERY_USER_TAGS, target.getJsonArray(DISCOVERY_USER_TAGS) != null ? target.getJsonArray(DISCOVERY_USER_TAGS) : discovery.getJsonArray(DISCOVERY_USER_TAGS));

                                target.mergeIn(discovery)
                                        .put(AIOpsObject.OBJECT_TYPE, type.getName()).put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY);

                                vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, target)
                                        .put(EventBusConstants.EVENT_ID, target.getLong(EVENT_ID))
                                        .put(USER_NAME, user)
                                        .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY));

                                vertx.eventBus().send(category == NMSConstants.Category.CLOUD || type == NMSConstants.Type.CISCO_MERAKI ? EventBusConstants.EVENT_ROUTER + EventBusConstants.DEFAULT_ROUTER_INDEX : EventBusConstants.EVENT_ROUTER, target);
                            }

                            promise.complete();
                        }
                        else
                        {
                            promise.fail(ErrorMessageConstants.DISCOVERY_OBJECT_NOT_FOUND);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(CommonUtil.formatStackTrace(exception.getCause().getStackTrace()));
        }

        return promise.future();
    }

    /**
     * Sets up and initiates a service check discovery process.
     * <p>
     * This method is specifically designed to handle service check discoveries, which include
     * URL, DNS, DHCP, and other service-based discovery types. It creates the appropriate context
     * for service check probes and adds them to the targets list.
     * <p>
     * If discovery method is AGENT:
     * - Checks Agent availability
     * - Assigns Agent ID to discovery context based on availability
     * <p>
     * For REMOTE discovery method:
     * - Builds contexts based on target selection
     *
     * @param discovery     The discovery profile configuration as a JsonObject
     * @param targets       List to store the created target contexts
     * @param futures       List of futures for tracking the progress of context creation
     * @param discoveryType The type of discovery (e.g., "auto", "manual")
     * @param type          The specific Type enum value for the discovery object
     */
    private void startServiceCheckDiscovery(JsonObject discovery, List<JsonObject> targets, List<Future<Void>> futures, String discoveryType, NMSConstants.Type type)
    {
        if (discovery.getString(Discovery.DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name()))
        {
            var ids = discovery.getJsonArray(Discovery.DISCOVERY_AGENTS);

            discovery.remove(Discovery.DISCOVERY_AGENTS);

            for (var index = 0; index < ids.size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItemByAgentId(ids.getLong(index));

                if (object != null)
                {
                    var status = ObjectStatusCacheStore.getStore().getItem(object.getLong(ID));

                    // for agent based service check discovery if monitor status is up or not available then and only run discovery
                    if (status == null || status.equalsIgnoreCase(STATUS_UP))
                    {
                        setServiceCheckProbeContext(new JsonObject().put(AIOpsObject.OBJECT_AGENT, ids.getLong(index)), new JsonObject().mergeIn(discovery), targets, futures, discoveryType, type);
                    }
                }
            }

            if (futures.isEmpty()) // means all agents are down so we have to abort the discovery
            {
                LOGGER.warn(String.format(ErrorMessageConstants.NO_MONITOR_AVAILABLE, APIConstants.Entity.AGENT.getName()));

                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                promise.fail(String.format(ErrorMessageConstants.NO_MONITOR_AVAILABLE, APIConstants.Entity.AGENT.getName()));
            }
        }

        else
        {
            setServiceCheckProbeContext(new JsonObject(), discovery, targets, futures, discoveryType, type);
        }
    }

    /**
     * Based on discovery type build target
     * If discovery type is ip.address target is same ... for range it will calculate target from start and end ip....
     * for csv target will be calculate based on csv file where csv file must contain target header and motadata will consider that column as a target
     * for cidr type motadata will use subnet utils library to calculate targets
     */
    /**
     * Sets up discovery targets based on discovery configuration.
     * <p>
     * This method processes the discovery configuration to determine the appropriate targets
     * for discovery. It handles different target specification methods:
     * - IP Range: Processes a range of IP addresses
     * - IP List: Processes a list of individual IP addresses
     * - Host List: Processes a list of hostnames
     * <p>
     * For each target type, it creates appropriate context objects and adds them to the targets list.
     * The method also handles different discovery categories and types, applying specific logic
     * for each combination.
     *
     * @param discoveryType The type of discovery (e.g., "auto", "manual")
     * @param type          The specific Type enum value for the discovery object
     * @param discovery     The discovery profile configuration as a JsonObject
     * @param targets       List to store the created target contexts
     * @param context       Base context object to be extended for each target
     * @param futures       List of futures for tracking the progress of context creation
     * @param agent         Flag indicating if this is an agent-based discovery
     */
    private void setTargets(String discoveryType, NMSConstants.Type type, JsonObject discovery, List<JsonObject> targets, JsonObject context, List<Future<Void>> futures, boolean agent)
    {
        try
        {
            if (discoveryType.equalsIgnoreCase(Discovery.DISCOVERY_TYPE_IP_ADDRESS))
            {
                var target = CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET));

                if (APIConstants.PATTERN_IP_ADDRESS.matcher(target).find() || APIConstants.PATTERN_IPV6_ADDRESS.matcher(target).find())
                {
                    setIPContext(type, context.put(OBJECT_TARGET, target), targets, futures, discovery, agent);
                }

                else
                {
                    setHostContext(type, context.put(OBJECT_TARGET, target), targets, futures, discovery, agent);
                }
            }

            else if (discoveryType.equalsIgnoreCase(Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE))
            {
                var range = CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET));

                var start = range.split("-")[0].trim();

                var end = start.substring(0, start.lastIndexOf('.')) + "." + range.split("-")[1].trim();

                if (APIUtil.validateRange(start, end))
                {
                    var filters = excludeTargets(discovery);

                    CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).stream().filter(item -> !filters.contains(item))
                            .forEach(item -> setIPContext(type, context.put(OBJECT_TARGET, item), targets, futures, discovery, agent));
                }
            }

            else if (discoveryType.equalsIgnoreCase(Discovery.DISCOVERY_TYPE_CSV))
            {
                var csvFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET));

                if (Bootstrap.vertx().fileSystem().existsBlocking(csvFile))
                {
                    var content = CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(csvFile));

                    if (CommonUtil.isNotNullOrEmpty(content))
                    {
                        var header = content.trim().split("\\n")[0];

                        if (CommonUtil.isNotNullOrEmpty(header))
                        {
                            var position = -1;

                            var valid = true;

                            var qualifiedTargets = new HashMap<String, JsonObject>();

                            for (var column : header.trim().split(","))
                            {
                                position++;

                                if (valid && column.trim().equalsIgnoreCase(TARGET))
                                {
                                    valid = false;

                                    var probes = 0;

                                    for (var columns : content.trim().split("\\n"))
                                    {
                                        if (columns.trim().split(",").length > 0)
                                        {
                                            var values = columns.trim().split(",", -1);

                                            var object = values[position].trim();

                                            var objectName = EMPTY_VALUE;

                                            var tags = new JsonArray();

                                            if (values.length >= 3)
                                            {
                                                objectName = values[position + 1].trim();

                                                var count = position + 2;

                                                if (CommonUtil.isNotNullOrEmpty(values[count]) && !values[count].equalsIgnoreCase(TAG))
                                                {
                                                    Arrays.stream(values, count, values.length).map(tag -> tag.trim().replace("\"", "").toLowerCase()).forEach(tags::add);
                                                }
                                            }

                                            if (CommonUtil.isNotNullOrEmpty(object) && !object.equalsIgnoreCase(TARGET) && !objectName.equalsIgnoreCase("monitor_name"))
                                            {
                                                probes++;

                                                qualifiedTargets.put(object, new JsonObject().put(OBJECT_NAME, objectName));

                                                qualifiedTargets.get(object).put(DISCOVERY_USER_TAGS, TagConfigStore.getStore().addItems(tags, Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER));
                                            }
                                        }

                                        if (probes >= DISCOVERY_CSV_MAX_PROBE_LIMIT)
                                        {
                                            break;
                                        }
                                    }
                                }
                            }

                            if (!qualifiedTargets.isEmpty())
                            {
                                var filters = excludeTargets(discovery);

                                qualifiedTargets.entrySet().stream().filter(item -> !filters.contains(item.getKey())).forEach(item ->
                                {
                                    if (APIConstants.PATTERN_IP_ADDRESS.matcher(item.getKey()).find() || APIConstants.PATTERN_IPV6_ADDRESS.matcher(item.getKey()).find())
                                    {
                                        if (item.getValue().containsKey(DISCOVERY_USER_TAGS))
                                        {
                                            context.put(DISCOVERY_USER_TAGS, item.getValue().getJsonArray(DISCOVERY_USER_TAGS));
                                        }

                                        setIPContext(type, context.put(OBJECT_TARGET, item.getKey()).put(OBJECT_NAME, item.getValue().getString(OBJECT_NAME)), targets, futures, discovery, agent);
                                    }
                                    else
                                    {
                                        if (item.getValue().containsKey(DISCOVERY_USER_TAGS))
                                        {
                                            context.put(DISCOVERY_USER_TAGS, item.getValue().getJsonArray(DISCOVERY_USER_TAGS));
                                        }

                                        setHostContext(type, context.put(OBJECT_TARGET, item.getKey()).put(OBJECT_NAME, item.getValue().getString(OBJECT_NAME)), targets, futures, discovery, agent);
                                    }
                                });
                            }
                        }
                    }
                }
            }

            else if (discoveryType.equalsIgnoreCase(Discovery.DISCOVERY_TYPE_CIDR))
            {
                var subnet = new SubnetUtils(CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET)));

                subnet.setInclusiveHostCount(false);

                // ignore network and broadcast address
                var qualifiedTargets = Arrays.stream(subnet.getInfo().getAllAddresses()).filter(item -> !item.endsWith(".0") && !item.endsWith(".255")).collect(Collectors.toSet());

                if (!qualifiedTargets.isEmpty())
                {
                    var filters = excludeTargets(discovery);

                    qualifiedTargets.stream().filter(item -> !filters.contains(item)).forEach(item -> setIPContext(type, context.put(OBJECT_TARGET, item), targets, futures, discovery, agent));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Sets up IP-based discovery contexts for targets.
     * <p>
     * This method creates discovery contexts for IP-based targets. It handles:
     * - Setting up appropriate object types and categories
     * - Configuring credential profiles if needed
     * - Creating event IDs for tracking
     * - Setting up discovery parameters specific to IP-based discoveries
     * <p>
     * The method processes each target and creates a context object with all necessary
     * information for the discovery process to execute.
     *
     * @param type      The specific Type enum value for the discovery object
     * @param context   Base context object to be extended for each target
     * @param targets   List to store the created target contexts
     * @param futures   List of futures for tracking the progress of context creation
     * @param discovery The discovery profile configuration as a JsonObject
     * @param agent     Flag indicating if this is an agent-based discovery
     */
    private void setIPContext(NMSConstants.Type type, JsonObject context, List<JsonObject> targets, List<Future<Void>> futures, JsonObject discovery, boolean agent)
    {
        var target = new JsonObject().mergeIn(context);

        var objectId = NOT_AVAILABLE;

        var category = type == NMSConstants.Type.PING && discovery.containsKey(DISCOVERY_CATEGORY) && discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName())
                ? NMSConstants.Category.valueOfName(discovery.getString(DISCOVERY_CATEGORY)) : NMSConstants.getCategory(type);

        if (category == NMSConstants.Category.SERVICE_CHECK)
        {
            objectId = getServiceCheckObjectId(agent, type, context, discovery);
        }
        else if (type == NMSConstants.Type.PING)
        {
            objectId = ObjectConfigStore.getStore().getItemByIP(target.getString(OBJECT_TARGET)) != null
                    ? ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(target.getString(OBJECT_TARGET))).getInteger(AIOpsObject.OBJECT_ID) : NOT_AVAILABLE;
        }
        else if (category != NMSConstants.Category.HCI && category != NMSConstants.Category.SDN)
        {
            objectId = category == NMSConstants.Category.NETWORK || category == NMSConstants.Category.SERVER || category == NMSConstants.Category.STORAGE || category == NMSConstants.Category.CONTAINER_ORCHESTRATION ? ObjectConfigStore.getStore().getObjectIdByIP(target.getString(OBJECT_TARGET), category)
                    : ObjectConfigStore.getStore().getObjectIdByIP(target.getString(OBJECT_TARGET), type);

            if (objectId == NOT_AVAILABLE)
            {
                objectId = getPingObjectId(category, OBJECT_IP, target.getString(OBJECT_TARGET));
            }
        }

        if (objectId == NOT_AVAILABLE)
        {
            target.put(OBJECT_IP, target.getString(OBJECT_TARGET));

            var future = Promise.<Void>promise();

            futures.add(future.future());

            if (CommonUtil.isNullOrEmpty(target.getString(OBJECT_HOST)))
            {
                DNSCacheStore.getStore().reverseLookup(target.getString(OBJECT_TARGET)).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        target.put(AIOpsObject.OBJECT_HOST, result.result()).put(OBJECT_TARGET, result.result());
                    }
                });
            }

            targets.add(target);

            future.complete();
        }
    }

    /**
     * Sets up hostname-based discovery contexts for targets.
     * <p>
     * This method creates discovery contexts for hostname-based targets. It handles:
     * - Setting up appropriate object types and categories
     * - Configuring credential profiles if needed
     * - Creating event IDs for tracking
     * - Setting up discovery parameters specific to hostname-based discoveries
     * - Resolving hostnames to IP addresses when necessary
     * <p>
     * The method processes each target hostname and creates a context object with all necessary
     * information for the discovery process to execute.
     *
     * @param type      The specific Type enum value for the discovery object
     * @param context   Base context object to be extended for each target
     * @param targets   List to store the created target contexts
     * @param futures   List of futures for tracking the progress of context creation
     * @param discovery The discovery profile configuration as a JsonObject
     * @param agent     Flag indicating if this is an agent-based discovery
     */
    private void setHostContext(NMSConstants.Type type, JsonObject context, List<JsonObject> targets, List<Future<Void>> futures, JsonObject discovery, boolean agent)
    {
        var target = new JsonObject().mergeIn(context);

        var objectId = NOT_AVAILABLE;

        var category = type == NMSConstants.Type.PING && discovery.containsKey(DISCOVERY_CATEGORY) && discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName())
                ? NMSConstants.Category.valueOfName(discovery.getString(DISCOVERY_CATEGORY)) : NMSConstants.getCategory(type);

        if (category == NMSConstants.Category.SERVICE_CHECK)
        {
            objectId = getServiceCheckObjectId(agent, type, context, discovery);
        }

        if (objectId == NOT_AVAILABLE)
        {
            target.put(AIOpsObject.OBJECT_HOST, target.getString(OBJECT_TARGET)).put(OBJECT_IP, target.getString(OBJECT_TARGET));

            var future = Promise.<Void>promise();

            futures.add(future.future());

            DNSCacheStore.getStore().lookup(target.getString(OBJECT_TARGET)).onComplete(result ->
            {
                if (result.succeeded())
                {
                    target.put(OBJECT_IP, result.result());
                }

                targets.add(target);

                future.complete();
            });

        }
    }

    /**
     * Determines the appropriate object ID for ping-based discoveries.
     * <p>
     * This method calculates the object ID to use for ping-based discovery operations
     * based on the category, filter, and value provided. The object ID is used to
     * identify the discovered object in the system.
     * <p>
     * The method handles different categories (SERVER, NETWORK, etc.) and applies
     * specific logic for each to determine the appropriate object ID.
     *
     * @param category The category of the discovery (SERVER, NETWORK, etc.)
     * @param filter   The filter criteria used for the discovery
     * @param value    The value associated with the filter
     * @return The calculated object ID as an integer
     */
    private int getPingObjectId(NMSConstants.Category category, String filter, String value)
    {
        var objectId = NOT_AVAILABLE;

        if (category == NMSConstants.Category.NETWORK || category == NMSConstants.Category.SERVER || category == NMSConstants.Category.VIRTUALIZATION || category == NMSConstants.Category.HCI || category == NMSConstants.Category.SDN
                || category == NMSConstants.Category.STORAGE || category == NMSConstants.Category.CONTAINER_ORCHESTRATION || category == NMSConstants.Category.OTHER)
        {
            var object = ObjectConfigStore.getStore().flatItemByMultipleValues(filter, value, OBJECT_CATEGORY, NMSConstants.Category.OTHER.getName(), AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PING.getName());

            if (object != null)
            {
                objectId = object.getInteger(OBJECT_ID);
            }
        }

        return objectId;
    }

    /**
     * Based on discovery target type build targets
     */
    /**
     * Sets up service check probe contexts for discovery.
     * <p>
     * This method creates discovery contexts specifically for service check probes like
     * URL, DNS, DHCP, etc. It handles:
     * - Setting up appropriate object types and categories
     * - Configuring service-specific parameters
     * - Creating event IDs for tracking
     * - Setting up discovery parameters specific to service check discoveries
     * <p>
     * The method processes the discovery configuration and creates context objects with
     * all necessary information for the service check discovery process to execute.
     *
     * @param context       Base context object to be extended for service check
     * @param discovery     The discovery profile configuration as a JsonObject
     * @param targets       List to store the created target contexts
     * @param futures       List of futures for tracking the progress of context creation
     * @param discoveryType The type of discovery (e.g., "auto", "manual")
     * @param type          The specific Type enum value for the discovery object
     */
    private void setServiceCheckProbeContext(JsonObject context, JsonObject discovery, List<JsonObject> targets, List<Future<Void>> futures, String discoveryType, NMSConstants.Type type)
    {
        var discoveryTargetType = NMSConstants.DiscoveryTargetType.valueOf(CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET_TYPE)));

        if (discoveryTargetType == NMSConstants.DiscoveryTargetType.OBJECT)
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            var target = CommonUtil.getLong(discovery.remove(Discovery.DISCOVERY_TARGET));

            var object = ObjectConfigStore.getStore().getItem(target);

            if (object == null)
            {
                object = ArchivedObjectConfigStore.getStore().getItem(target);
            }

            if (object != null)
            {
                context.put(OBJECT_TARGET, discovery.getString(NMSConstants.URL_ENDPOINT) != null ?
                                object.getString(OBJECT_TARGET) + CommonUtil.getString(discovery.remove(NMSConstants.URL_ENDPOINT)) : object.getString(OBJECT_TARGET))
                        .put(AIOpsObject.OBJECT_HOST, object.getString(AIOpsObject.OBJECT_HOST)).put(OBJECT_IP, object.getString(OBJECT_IP));

                var objectId = getServiceCheckObjectId(context.containsKey(AIOpsObject.OBJECT_AGENT), type, context, discovery);

                if (objectId == NOT_AVAILABLE)
                {
                    targets.add(context);
                }
            }

            promise.complete();
        }
        else if (discoveryTargetType == NMSConstants.DiscoveryTargetType.URL)
        {
            var target = CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET));

            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            if (APIConstants.PATTERN_IP_ADDRESS.matcher(target).find())
            {
                context.put(OBJECT_IP, target).put(OBJECT_TARGET, target);

                DNSCacheStore.getStore().reverseLookup(target).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        context.put(AIOpsObject.OBJECT_HOST, result.result()).put(OBJECT_TARGET, result.result());
                    }

                    if (discovery.getString(NMSConstants.URL_ENDPOINT) != null)
                    {
                        context.put(OBJECT_TARGET, context.getString(OBJECT_TARGET) + CommonUtil.getString(discovery.remove(NMSConstants.URL_ENDPOINT)));
                    }

                    var objectId = context.containsKey(AIOpsObject.OBJECT_AGENT) ? getAgentObjectId(type, context) : getObjectId(type, context);

                    if (objectId == NOT_AVAILABLE)
                    {
                        targets.add(context);
                    }

                    promise.complete();
                });
            }
            else
            {
                context.put(AIOpsObject.OBJECT_HOST, target).put(OBJECT_TARGET, target).put(OBJECT_IP, target);

                DNSCacheStore.getStore().lookup(target).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        context.put(OBJECT_IP, result.result());
                    }

                    if (discovery.getString(NMSConstants.URL_ENDPOINT) != null)
                    {
                        context.put(OBJECT_TARGET, context.getString(OBJECT_TARGET) + CommonUtil.getString(discovery.remove(NMSConstants.URL_ENDPOINT)));
                    }

                    if ((context.containsKey(AIOpsObject.OBJECT_AGENT) ? getAgentObjectId(type, context) : getObjectId(type, context)) == NOT_AVAILABLE)
                    {
                        targets.add(context);
                    }

                    promise.complete();
                });
            }
        }
        else
        {
            if (type == NMSConstants.Type.PING || type == NMSConstants.Type.PORT)
            {
                setTargets(discoveryType, type, discovery, targets, context, futures, context.containsKey(AIOpsObject.OBJECT_AGENT));
            }
            else
            {
                var target = CommonUtil.getString(discovery.remove(Discovery.DISCOVERY_TARGET));

                context.put(OBJECT_TARGET, target);

                if (APIConstants.PATTERN_IP_ADDRESS.matcher(target).find() || APIConstants.PATTERN_IPV6_ADDRESS.matcher(target).find())
                {
                    setIPContext(type, context, targets, futures, discovery, context.containsKey(AIOpsObject.OBJECT_AGENT));
                }
                else
                {
                    setHostContext(type, context, targets, futures, discovery, context.containsKey(AIOpsObject.OBJECT_AGENT));
                }

            }
        }
    }

    /**
     * Determines the appropriate object ID for service check discoveries.
     * <p>
     * This method calculates the object ID to use for service check discovery operations
     * based on the type, context, and discovery configuration. The object ID is used to
     * identify the discovered service check object in the system.
     * <p>
     * The method handles different service check types (URL, DNS, DHCP, etc.) and applies
     * specific logic for each to determine the appropriate object ID.
     *
     * @param agent     Flag indicating if this is an agent-based discovery
     * @param type      The specific Type enum value for the discovery object
     * @param context   The context object containing discovery parameters
     * @param discovery The discovery profile configuration as a JsonObject
     * @return The calculated object ID as an integer
     */
    private int getServiceCheckObjectId(boolean agent, NMSConstants.Type type, JsonObject context, JsonObject discovery)
    {
        int objectId;

        if (type == NMSConstants.Type.PORT)
        {
            objectId = agent ? ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.AGENT.name())
                    .stream().filter(item -> item.getLong(AIOpsObject.OBJECT_AGENT).equals(context.getLong(AIOpsObject.OBJECT_AGENT)))
                    .filter(item -> (item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                            || item.getString(OBJECT_IP).equalsIgnoreCase(context.getString(OBJECT_TARGET))))
                    .filter(item -> item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT).equals(discovery.getInteger(PORT)))
                    .map(item -> item.getInteger(AIOpsObject.OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE)
                    : ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name())
                    .stream().filter(item -> (item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                            || item.getString(OBJECT_IP).equalsIgnoreCase(context.getString(OBJECT_TARGET))))
                    .filter(item -> item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT).equals(discovery.getInteger(PORT)))
                    .map(item -> item.getInteger(AIOpsObject.OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE);
        }
        else if (!agent && type == NMSConstants.Type.PING)
        {
            objectId = ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name())
                    .stream().filter(item -> discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(item.getString(OBJECT_CATEGORY))
                            && (item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                            || item.getString(OBJECT_IP).equalsIgnoreCase(context.getString(OBJECT_TARGET))))
                    .map(item -> item.getInteger(OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE);
        }
        else
        {
            objectId = agent ? getAgentObjectId(type, context) : getObjectId(type, context);
        }

        return objectId;
    }

    /**
     * Determines the appropriate object ID for agent-based discoveries.
     * <p>
     * This method calculates the object ID to use for agent-based discovery operations
     * based on the type and context provided. The object ID is used to identify the
     * discovered object in the system when using agent-based discovery.
     *
     * @param type    The specific Type enum value for the discovery object
     * @param context The context object containing discovery parameters
     * @return The calculated object ID as an integer
     */
    private int getAgentObjectId(NMSConstants.Type type, JsonObject context)
    {
        return ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.AGENT.name())
                .stream().filter(item -> type == NMSConstants.Type.URL ? item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                        : (item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                        || item.getString(OBJECT_IP).equalsIgnoreCase(context.getString(OBJECT_TARGET))))
                .filter(item -> item.getLong(AIOpsObject.OBJECT_AGENT).equals(context.getLong(AIOpsObject.OBJECT_AGENT)))
                .map(item -> item.getInteger(AIOpsObject.OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE);
    }

    /**
     * Determines the appropriate object ID for remote discoveries.
     * <p>
     * This method calculates the object ID to use for remote discovery operations
     * based on the type and context provided. The object ID is used to identify the
     * discovered object in the system when using remote discovery.
     * <p>
     * Similar to getAgentObjectId() but specifically for remote discovery method.
     *
     * @param type    The specific Type enum value for the discovery object
     * @param context The context object containing discovery parameters
     * @return The calculated object ID as an integer
     */
    private int getObjectId(NMSConstants.Type type, JsonObject context)
    {
        return ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name())
                .stream().filter(item -> type == NMSConstants.Type.URL ? item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                        : (item.getString(OBJECT_TARGET).equalsIgnoreCase(context.getString(OBJECT_TARGET))
                        || item.getString(OBJECT_IP).equalsIgnoreCase(context.getString(OBJECT_TARGET))))
                .filter(item -> item.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.REMOTE.name()))
                .map(item -> item.getInteger(AIOpsObject.OBJECT_ID)).findFirst().orElse(NOT_AVAILABLE);
    }

    /**
     * Drops (aborts and cleans up) a running discovery process.
     * <p>
     * This method is called to abort a running discovery process and clean up associated resources.
     * It:
     * 1. Aborts the discovery process
     * 2. Removes the discovery from tracking maps
     * 3. Updates the discovery status in the cache store
     * 4. Publishes notifications about the state change
     *
     * @param discoveryId The unique identifier of the discovery profile to drop
     * @return A Future that completes when the drop operation is complete
     */
    public Future<Void> drop(long discoveryId)
    {
        var promise = Promise.<Void>promise();

        if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
        {
            // agent test cases are failing randomly hence this log is added here to catch the issue.
            LOGGER.info("Discovery id: " + discoveryId + " | " + DiscoveryConfigStore.getStore().getItem(discoveryId));
        }

        Bootstrap.configDBService().drop(DBConstants.TBL_DISCOVERY_RESULT + discoveryId,
                result ->
                        promise.complete());

        return promise.future();
    }

    /**
     * 1. Update discovery statistics in discovery table.
     * 2. If discovery profile contains sms recipients or email recipients motadata notification engine will publish discovery statistics and complete message to that recipients address
     */
    /**
     * Updates the discovery status and results after completion.
     * <p>
     * This method is called when a discovery process completes to update its status and results.
     * It:
     * 1. Updates the discovery status in the configuration store
     * 2. Processes and stores the discovered objects
     * 3. Handles the mapping of previous discovery results with new results
     * 4. Updates the discovery cache with the latest results
     * 5. Publishes notifications about the discovery completion
     *
     * @param discoveryId       The unique identifier of the discovery profile to update
     * @param status            The new status string to set for the discovery
     * @param discoveredObjects Array of objects discovered during the discovery process (can be null)
     */
    private void update(long discoveryId, String status, JsonArray discoveredObjects)
    {

        var eventId = DiscoveryCacheStore.getStore().getDiscoveryEventId(discoveryId);

        var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

        var aborted = status.contains("Aborted");

        var item = !aborted ? DiscoveryCacheStore.getStore().getDiscoveryStatistics(discoveryId).put(Discovery.DISCOVERY_STATUS, status) : new JsonObject().put(DISCOVERY_STATUS, status);

        // for cloud category discovered objects size is total instances size
        if (discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) || discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.HCI.getName())
                || discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SDN.getName()) || discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.STORAGE.getName())
                || discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CONTAINER_ORCHESTRATION.getName()))
        {
            item.put(DISCOVERY_DISCOVERED_OBJECTS, 0);

            if (discoveredObjects != null)
            {
                for (var index = 0; index < discoveredObjects.size(); index++)
                {
                    if (!discoveredObjects.getJsonObject(index).getString(OBJECT_STATE).equalsIgnoreCase("FAILED"))
                    {
                        item.put(DISCOVERY_DISCOVERED_OBJECTS, item.getInteger(DISCOVERY_DISCOVERED_OBJECTS) + 1);
                    }
                }
            }

            if (!item.containsKey(DISCOVERY_FAILED_OBJECTS))
            {
                item.put(DISCOVERY_FAILED_OBJECTS, 0);
            }

            // for cloud total objects is discovered objects + failed objects size -> need to do sum as total objects is always 1
            item.put(DISCOVERY_TOTAL_OBJECTS, item.getInteger(DISCOVERY_DISCOVERED_OBJECTS) + item.getInteger(DISCOVERY_FAILED_OBJECTS));
        }

        Bootstrap.configDBService().update(DBConstants.TBL_DISCOVERY,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, discoveryId),
                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                result ->
                {
                    var promise = Promise.<Void>promise();

                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("discovery %s updated successfully...", DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId)));

                        DiscoveryConfigStore.getStore().updateItem(discoveryId).onComplete(asyncResult -> promise.complete());
                    }

                    else
                    {
                        promise.complete();

                        LOGGER.warn(String.format("failed to update discovery %s...", DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId)));
                    }

                    promise.future().onComplete(asyncResult ->
                    {
                        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_TRACKER, new JsonObject().put(EventBusConstants.EVENT_ID, eventId),
                                reply ->
                                {
                                    var event = reply.result().body();

                                    if (reply.succeeded() && event.containsKey(EventBusConstants.EVENT_SCHEDULER))
                                    {
                                        if (discoveredObjects != null)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SCHEDULER_COMPLETE, event);
                                        }
                                        else
                                        {
                                            SchedulerCacheStore.getStore().clearSchedulerContext(event.getLong(EventBusConstants.EVENT_SCHEDULER));
                                        }
                                    }
                                });

                        if (!aborted) //for abort discovery we don't save result and not save discovered objects hence no need to send email/sms for that as in email/sms discovered objects is not same
                        {
                            if (discovery.getJsonArray(DISCOVERY_EMAIL_RECIPIENTS) != null && !discovery.getJsonArray(DISCOVERY_EMAIL_RECIPIENTS).isEmpty())
                            {
                                var groups = new JsonArray();

                                var builder = new StringBuilder();

                                discovery.getJsonArray(DISCOVERY_GROUPS).forEach(group ->
                                {
                                    builder.setLength(0);

                                    CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group)), builder, null);

                                    groups.add(builder.toString());
                                });

                                Notification.sendEmail(new JsonObject()
                                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(DISCOVERY_NOTIFICATION_SUBJECT, discovery.getString(DISCOVERY_NAME)))
                                        .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_DISCOVERY_HTML_TEMPLATE)
                                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, discovery.getJsonArray(DISCOVERY_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().mergeIn(discovery).mergeIn(item).put(TIME_STAMP, DateTimeUtil.timestamp()).put(DISCOVERY_GROUPS, StringUtils.join(groups, COMMA_SEPARATOR))));
                            }

                            if (discovery.getJsonArray(DISCOVERY_SMS_RECIPIENTS) != null && !discovery.getJsonArray(DISCOVERY_SMS_RECIPIENTS).isEmpty())
                            {

                                Notification.sendSMS(String.format(DISCOVERY_NOTIFICATION_MESSAGE, discovery.getString(DISCOVERY_NAME), item.getValue(DISCOVERY_TOTAL_OBJECTS), item.getValue(DISCOVERY_DISCOVERED_OBJECTS), item.getValue(DISCOVERY_FAILED_OBJECTS), item.getValue(DISCOVERY_PROGRESS)),
                                        discovery.getJsonArray(DISCOVERY_SMS_RECIPIENTS));
                            }
                        }

                        DiscoveryCacheStore.getStore().completeDiscovery(discoveryId);
                    });
                });
    }

    // used to exclude target from discovery

    /**
     * Determines the set of targets to exclude from discovery.
     * <p>
     * This method processes the discovery configuration to identify targets that should be
     * excluded from the discovery process. It handles different exclusion methods:
     * - IP Range exclusion
     * - IP List exclusion
     * - Host List exclusion
     * <p>
     * The method returns a set of target IPs or hostnames that should be excluded from discovery.
     *
     * @param discovery The discovery profile configuration as a JsonObject
     * @return A Set of String values representing targets to exclude from discovery
     */
    private Set<String> excludeTargets(JsonObject discovery)
    {
        var filters = new HashSet<String>();

        try
        {
            if (discovery.containsKey(Discovery.DISCOVERY_EXCLUDE_TARGETS) && discovery.getJsonArray(Discovery.DISCOVERY_EXCLUDE_TARGETS) != null)
            {
                if (discovery.getString(Discovery.DISCOVERY_EXCLUDE_TARGET_TYPE).equalsIgnoreCase(DISCOVERY_TYPE_IP_ADDRESS))
                {
                    discovery.getJsonArray(Discovery.DISCOVERY_EXCLUDE_TARGETS).forEach(target -> filters.add(CommonUtil.getString(target)));
                }

                else if (discovery.getString(Discovery.DISCOVERY_EXCLUDE_TARGET_TYPE).equalsIgnoreCase(DISCOVERY_TYPE_IP_ADDRESS_RANGE))
                {
                    for (var index = 0; index < discovery.getJsonArray(Discovery.DISCOVERY_EXCLUDE_TARGETS).size(); index++)
                    {
                        var range = discovery.getJsonArray(Discovery.DISCOVERY_EXCLUDE_TARGETS).getString(index);

                        var start = range.split("-")[0].trim();

                        var end = start.substring(0, start.lastIndexOf('.')) + "." + range.split("-")[1].trim();

                        if (APIUtil.validateRange(start, end))
                        {
                            CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(ip -> filters.add(CommonUtil.getString(ip)));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return filters;
    }

    /**
     * Main function of discovery used to generate discovery objects based on discovery results
     */
    /**
     * Prepares the final list of discovered objects after a discovery process.
     * <p>
     * This method processes the raw discovered objects and prepares them for storage and display.
     * It:
     * 1. Filters out duplicate objects
     * 2. Maps previous discovery results with new results
     * 3. Handles special cases for different object types
     * 4. Processes object metadata and attributes
     * 5. Applies discovery-specific configurations to objects
     * <p>
     * The method returns a processed array of discovered objects ready for storage.
     *
     * @param discoveredObjects         Array of objects discovered during the current discovery process
     * @param discovery                 The discovery profile configuration as a JsonObject
     * @param previousDiscoveredObjects Objects discovered in previous runs of this discovery profile
     * @return A processed JsonArray of discovered objects
     */
    private JsonArray prepare(JsonArray discoveredObjects, JsonObject discovery, JsonObject previousDiscoveredObjects)
    {
        var items = new JsonArray();

        try
        {
            var groups = discovery.getJsonArray(DISCOVERY_GROUPS);

            var eventProcessors = discovery.getJsonArray(DISCOVERY_EVENT_PROCESSORS);

            var context = discovery.getJsonObject(DISCOVERY_CONTEXT);

            var category = discovery.getString(DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PING.getName())
                    && discovery.containsKey(DISCOVERY_CATEGORY) && discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName())
                    ? NMSConstants.Category.valueOfName(discovery.getString(DISCOVERY_CATEGORY))
                    : NMSConstants.getCategory(NMSConstants.Type.valueOfName(discovery.getString(DISCOVERY_OBJECT_TYPE)));

            for (var index = 0; index < discoveredObjects.size(); index++)
            {
                var discoveredObject = discoveredObjects.getJsonObject(index);

                if (discoveredObject.containsKey(NMSConstants.OBJECTS))
                {
                    var objects = discoveredObject.getJsonArray(NMSConstants.OBJECTS);

                    if (objects != null && !objects.isEmpty() &&
                            (category == NMSConstants.Category.SERVER
                                    || category == NMSConstants.Category.VIRTUALIZATION
                                    || category == NMSConstants.Category.NETWORK
                                    || category == NMSConstants.Category.OTHER
                                    || category == NMSConstants.Category.SERVICE_CHECK
                                    || category == NMSConstants.Category.CLOUD
                                    || category == NMSConstants.Category.HCI
                                    || category == NMSConstants.Category.SDN
                                    || category == NMSConstants.Category.STORAGE
                                    || category == NMSConstants.Category.CONTAINER_ORCHESTRATION))
                    {
                        for (var i = 0; i < objects.size(); i++)
                        {
                            var object = objects.getJsonObject(i);

                            object.put(AIOpsObject.OBJECT_GROUPS, groups);

                            object.put(OBJECT_TAGS, discoveredObject.getJsonArray(DISCOVERY_USER_TAGS) != null ? discoveredObject.getJsonArray(DISCOVERY_USER_TAGS) : new JsonArray());

                            if (!object.containsKey(AIOpsObject.OBJECT_TYPE) && discoveredObject.containsKey(AIOpsObject.OBJECT_TYPE))
                            {
                                object.put(AIOpsObject.OBJECT_TYPE, discoveredObject.getString(AIOpsObject.OBJECT_TYPE));
                            }

                            if (eventProcessors != null && !eventProcessors.isEmpty())
                            {
                                object.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, eventProcessors);
                            }

                            if (context != null && !context.isEmpty())
                            {
                                var objectContext = new JsonObject().mergeIn(context);

                                if (object.getJsonObject(AIOpsObject.OBJECT_CONTEXT) != null) // for linux/hp-ux/solaris/ibm aix we receive object.context which contains mainly bin path so merge it with discovery context
                                {
                                    objectContext.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));
                                }
                                object.put(AIOpsObject.OBJECT_CONTEXT, objectContext);
                            }

                            if (discoveredObject.containsKey(AIOpsObject.OBJECT_DISCOVERY_METHOD))
                            {
                                var discoveryMethod = NMSConstants.DiscoveryMethod.valueOf(discoveredObject.getString(AIOpsObject.OBJECT_DISCOVERY_METHOD));

                                object.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, discoveryMethod.name());

                                if (discoveryMethod == NMSConstants.DiscoveryMethod.AGENT) // for agent based discovery update agent.id in result
                                {
                                    object.put(AIOpsObject.OBJECT_AGENT, discoveredObject.getLong(AIOpsObject.OBJECT_AGENT));
                                }
                            }

                            if (discoveredObject.containsKey(AIOpsObject.OBJECT_CREDENTIAL_PROFILE))
                            {
                                object.put(AIOpsObject.OBJECT_CREDENTIAL_PROFILE, discoveredObject.getLong(AIOpsObject.OBJECT_CREDENTIAL_PROFILE));
                            }

                            if (!object.containsKey(OBJECT_IP) && discoveredObject.containsKey(OBJECT_IP))
                            {
                                object.put(OBJECT_IP, discoveredObject.getString(OBJECT_IP));
                            }

                            if (CommonUtil.isNullOrEmpty(object.getString(AIOpsObject.OBJECT_HOST)))
                            {
                                if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(AIOpsObject.OBJECT_HOST)))
                                {
                                    object.put(AIOpsObject.OBJECT_HOST, discoveredObject.getString(AIOpsObject.OBJECT_HOST));
                                }
                                else if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_TARGET)))
                                {
                                    object.put(AIOpsObject.OBJECT_HOST, discoveredObject.getString(OBJECT_TARGET));
                                }
                            }

                            if (!object.containsKey(OBJECT_TARGET) && discoveredObject.containsKey(OBJECT_TARGET))
                            {
                                object.put(OBJECT_TARGET, discoveredObject.getString(OBJECT_TARGET));
                            }

                            if (discoveredObject.containsKey(OBJECT_NAME) && CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_NAME)))
                            {
                                object.put(OBJECT_NAME, discoveredObject.getString(OBJECT_NAME));
                            }

                            if (CommonUtil.isNullOrEmpty(object.getString(AIOpsObject.OBJECT_NAME)))
                            {
                                if (category != NMSConstants.Category.SERVICE_CHECK && CommonUtil.isNotNullOrEmpty(object.getString(AIOpsObject.OBJECT_HOST)))
                                {
                                    object.put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_HOST));
                                }
                                else if (CommonUtil.isNotNullOrEmpty(object.getString(OBJECT_TARGET)))
                                {
                                    object.put(AIOpsObject.OBJECT_NAME, object.getString(OBJECT_TARGET));
                                }
                                else if (CommonUtil.isNotNullOrEmpty(object.getString(OBJECT_IP)))
                                {
                                    object.put(AIOpsObject.OBJECT_NAME, object.getString(OBJECT_IP));
                                }
                            }

                            if (object.containsKey(AIOpsObject.OBJECT_SYSTEM_OID) && category == NMSConstants.Category.NETWORK) // for network device motadata will update object.type based on device catalog
                            {
                                var type = SNMPDeviceCatalogConfigStore.getStore().getSNMPDeviceType(object.getString(AIOpsObject.OBJECT_SYSTEM_OID));

                                if (type != null && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()))
                                {
                                    object.put(AIOpsObject.OBJECT_TYPE, type);
                                }
                            }

                            object.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.NEW.name()).put(AIOpsObject.OBJECT_CATEGORY, category.getName());

                            if (previousDiscoveredObjects.containsKey(object.getString(OBJECT_TARGET)))
                            {
                                object.put(AIOpsObject.OBJECT_STATE, ObjectConfigStore.getStore().getObjectIdByTarget(object.getString(OBJECT_TARGET), NMSConstants.Type.valueOfName(CommonUtil.getString(object.getString(AIOpsObject.OBJECT_TYPE)))) != NOT_AVAILABLE ?
                                        NMSConstants.State.PROVISION.name() : NMSConstants.State.UNPROVISION.name());
                            }

                            items.add(object);
                        }
                    }
                }
                else if (discoveredObject.containsKey(STATUS) && discoveredObject.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
                {
                    var object = new JsonObject();

                    if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_IP)))
                    {
                        object.put(OBJECT_IP, discoveredObject.getString(OBJECT_IP));
                    }

                    if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_TYPE)))
                    {
                        object.put(OBJECT_TYPE, discoveredObject.getString(OBJECT_TYPE));
                    }

                    if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_TARGET)) || (discoveredObject.containsKey(DISCOVERY_CATEGORY)
                            && (discoveredObject.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) || (discoveredObject.containsKey(OBJECT_TYPE) && discoveredObject.getString(OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_MERAKI.getName())))))
                    {
                        object.put(OBJECT_TARGET, discoveredObject.getString(OBJECT_TARGET, EMPTY_VALUE));
                    }

                    if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(OBJECT_DISCOVERY_METHOD)))
                    {
                        object.put(OBJECT_DISCOVERY_METHOD, discoveredObject.getString(OBJECT_DISCOVERY_METHOD));
                    }

                    if (CommonUtil.isNotNullOrEmpty(discoveredObject.getString(MESSAGE)))
                    {
                        object.put(MESSAGE, discoveredObject.getString(MESSAGE));
                    }

                    object.put(OBJECT_STATE, "FAILED").put(AIOpsObject.OBJECT_CATEGORY, category.getName());

                    items.add(object);

                }
            }

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    /**
     * 1. Update event tracker progress
     * 2. boot sequence is remote event processor publish event to remote engine else response processor for manipulation and progress calculation
     */
    /**
     * Sends a discovery event to the event bus.
     * <p>
     * This method is responsible for publishing discovery events to the event bus
     * for further processing by other components of the system. It handles the
     * routing of events based on their type and content.
     *
     * @param event The discovery event to send as a JsonObject
     */
    private void send(JsonObject event)
    {
        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_DISCOVERY_RESPONSE, event);
    }

    /**
     * Stops the DiscoveryEngine verticle and cleans up resources.
     * <p>
     * This method is called when the verticle is undeployed by Vert.x.
     * It closes the worker executor to ensure proper cleanup of resources
     * and then completes the provided promise.
     *
     * @param promise A Promise that should be completed when shutdown is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        workerExecutor.close();

        promise.complete();
    }
}
