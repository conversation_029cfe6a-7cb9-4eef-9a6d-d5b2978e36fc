{"***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844611499", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587789], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844611499", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:34.700 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624238, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587990, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587789, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "System Info", "metric.object": 58829800587790, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Firewall", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:42.448 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000011, 10000000000009, 10000000000013], "object.host": "fg_firewall.mindarray.com", "object.id": 11, "object.ip": "***********", "object.make.model": "Fortinet FortiGate 100E", "object.name": "fg_firewall.mindarray.com", "object.snmp.device.catalog": 58829800537401, "object.state": "ENABLE", "object.system.oid": ".*******.4.1.12356.101.1.1041", "object.target": "***********", "object.type": "Firewall", "object.vendor": "Fortinet", "oid.group.device.type": "SNMP Device", "oid.group.id": "577a88ec-b226-479b-a7b8-1aca7a416d63", "oid.group.name": "System Info", "oid.group.oids": {".*******.4.1.12356.*********.0": "system.serial.no", ".*******.4.1.12356.*********.0": "system.os.version", ".*******.4.1.12356.*********.0": "fortinet.hardware.sensors", ".*******.4.1.12356.*********.0": "fortinet.processors", ".*******.4.1.12356.*********.0": "fortinet.processor.modules"}, "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 512, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"fortinet.hardware.sensors": 0, "fortinet.processor.modules": 1, "fortinet.processors": 4, "system.os.version": "v5.6.6,build1630,180913 (GA)", "system.serial.no": "FG100E4Q16003525"}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "**********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844603127", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587783], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844603129", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:27.148 pm 10/06/2022", "discovery.target": "**********", "discovery.target.name": "**********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.********.6.2"}], "event.id": 58829800624269, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587905, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587783, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587784, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:40.986 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco_core.motadata.local", "object.id": 9, "object.ip": "**********", "object.make.model": "Cisco Catalyst 93xx Switch Stack", "object.name": "cisco_core.motadata.local", "object.snmp.device.catalog": 58829800549968, "object.state": "ENABLE", "object.system.oid": ".*******.*******.2494", "object.target": "**********", "object.type": "Switch", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 507, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 86, "cisco.huge.buffer.misses": 51, "cisco.large.buffer.misses": 597, "cisco.medium.buffer.misses": 1428, "cisco.small.buffer.misses": 0, "cisco.verylarge.buffer.misses": 0, "invalid.oids": [".*******.*******.********.6.2"]}, "valid.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844607145", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587786], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844607146", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:31.505 pm 10/06/2022", "discovery.target": "************", "discovery.target.name": "************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624252, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587978, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587786, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587787, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:41.954 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "cisco2960.motadata.local", "object.id": 10, "object.ip": "************", "object.make.model": "Cisco Catalyst 2960 Series", "object.name": "cisco2960.motadata.local", "object.snmp.device.catalog": 58829800538399, "object.state": "ENABLE", "object.system.oid": ".*******.*******.697", "object.target": "************", "object.type": "Switch", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 507, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 13785, "cisco.huge.buffer.misses": 1, "cisco.large.buffer.misses": 0, "cisco.medium.buffer.misses": 435, "cisco.small.buffer.misses": 45, "cisco.verylarge.buffer.misses": 0}, "snmp.community": "public", "snmp.version": "v1", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846112590", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588009], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846112590", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:37.60 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.*******.********.5"}], "event.id": 58829800624570, "event.timestamp": 1654846125, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588089, "interface.discovery": "yes", "invalid.oids": [".*******.*******.1.57.0", ".*******.*******.1.58.0", ".*******.*******.1.56.0"], "metric.category": "Network", "metric.credential.profile": 58829800588009, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800588010, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:39.88 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "Leaf1-MIMIC8145", "object.id": 12, "object.ip": "***********", "object.make.model": "Cisco Nexus 9396PX", "object.name": "Leaf1-MIMIC8145", "object.snmp.device.catalog": 58829800549415, "object.state": "ENABLE", "object.system.oid": ".*******.*******2.3.1.3.1508", "object.target": "***********", "object.type": "Switch", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "04c09031-7e65-403b-8b27-f7c42768accc", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.*******.*********.1.10": "system.cpu.percent", ".*******.*******.*********.1.24": "system.1min.avg.cpu.load.percent", ".*******.*******.*********.1.25": "system.5min.avg.cpu.load.percent", ".*******.*******.*********.1.26": "system.15min.avg.cpu.load.percent", ".*******.*******.*********.1.3": "system.cpu.percent", ".*******.*******.*********.1.4": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.5": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.6": "system.cpu.percent", ".*******.*******.*********.1.7": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.8": "system.5min.avg.cpu.percent", ".*******.*******.305.*******": "system.cpu.percent", ".*******.*******.305.*******": "system.memory.used.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 511, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.15min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.percent": 6, "system.5min.avg.cpu.load.percent": 0, "system.5min.avg.cpu.percent": 0, "system.cpu.percent": 0, "system.memory.used.percent": 65}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846121810", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588090], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846121812", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:45.715 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624609, "event.timestamp": 1654846135, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588145, "interface.discovery": "yes", "invalid.oids": [".*******.********.*********.*******.1.2.0"], "metric.category": "Network", "metric.credential.profile": 58829800588090, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "System Info", "metric.object": 58829800588091, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:47.740 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC8806", "object.id": 13, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC8806", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "799f6f3e-ef31-4fde-b47f-fb8e4528914a", "oid.group.name": "System Info", "oid.group.oids": {".*******.********.*********.1.3.0": "system.os.version", ".*******.********.********.7.0": "hp.system.hardware.version", ".*******.********.********.8.0": "hp.system.rom.version", ".*******.********.********.9.0": "system.serial.no"}, "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 508, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"hp.system.hardware.version": "", "hp.system.rom.version": "L.10.01", "system.os.version": "L.10.09", "system.serial.no": "LP535VE009 "}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846130604", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588151], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846130605", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:58:54.513 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.********.*********.*******.*******"}], "event.id": 58829800624649, "event.timestamp": 1654846145, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588207, "interface.discovery": "yes", "invalid.oids": [".*******.4.1.12356.*********.0", ".*******.********.*******.*******.9.0", ".*******.********.**********.*******.1.6.0", ".*******.4.1.12356.*********.0"], "metric.category": "Network", "metric.credential.profile": 58829800588151, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800588152, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:58:56.534 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "hp4202vl-48g-MIMIC9136", "object.id": 14, "object.ip": "*************", "object.make.model": "ProCurve Switch 4202vl-48G", "object.name": "hp4202vl-48g-MIMIC9136", "object.snmp.device.catalog": 58829800527850, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "db6f564f-37b0-4a7d-ad53-26a8b49e07ae", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.********.*********.*******": "system.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 511, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.cpu.percent": 63}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "*************": {"_type": "1", "credential.profile.name": "SNMP-Test-1654846139370", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800588211], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654846139371", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:59:03.840 pm 10/06/2022", "discovery.target": "*************", "discovery.target.name": "*************", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.********.*********.*******.*******"}], "event.id": 58829800624707, "event.timestamp": 1654846155, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800588294, "interface.discovery": "yes", "invalid.oids": [".*******.4.1.12356.*********.0", ".*******.********.*******.*******.9.0", ".*******.********.**********.*******.1.6.0", ".*******.4.1.12356.*********.0"], "metric.category": "Network", "metric.credential.profile": 58829800588211, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800588212, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:59:05.868 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000048, 10000000000013], "object.host": "J4850A-MIMIC9087", "object.id": 15, "object.ip": "*************", "object.make.model": "HP ProCurve Switch 5304XL", "object.name": "J4850A-MIMIC9087", "object.snmp.device.catalog": 58829800561806, "object.state": "ENABLE", "object.system.oid": ".*******.********.*********", "object.target": "*************", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "oid.group.device.type": "SNMP Device", "oid.group.id": "db6f564f-37b0-4a7d-ad53-26a8b49e07ae", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.********.*********.*******": "system.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 511, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.cpu.percent": 99}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844584149", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587765], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844584150", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:07.135 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [{"error": "Unknown Error", "error.code": "MD031", "message": "Unknown Error", "oid": ".*******.********.********.1001"}, {"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.********.********.1001"}, {"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.*******0.43.*******"}, {"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.*******0.********"}, {"error": "Invalid SNMP OID Value", "error.code": "MD063", "message": "Invalid SNMP OID Value", "oid": ".*******.*******.********"}], "event.id": 58829800623976, "event.topic": "remote.event.processor ", "event.type": "plugin.engine", "id": 58829800587766, "interface.discovery": "no", "metric.plugin": "snmp", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:50:37.77 pm 10/06/2022", "object.credential.profile": 58829800587765, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000013], "object.host": "bgp2.bgp2.com", "object.id": 3, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "bgp2.bgp2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group": {"_type": "0", "oid.group.device.type": "SNMP Device", "oid.group.id": "15eb4e01-ffcf-4dcc-b0dd-4fdebfdd0630", "oid.group.name": "System Info", "oid.group.oids": {".*******.********.********.1": "system.serial.no", ".*******.********.********.1001": "system.serial.no", ".*******.*******0.********": "system.description", ".*******.*******0.43.*******": "system.serial.no", ".*******.*******.6.3.0": "system.serial.no", ".*******.*******.********": "system.serial.no", ".*******.*******.********.2.7": "system.description"}, "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar"}, "oid.group.oids": {".*******.********.********.1": "system.serial.no", ".*******.********.********.1001": "system.serial.no", ".*******.*******0.********": "system.description", ".*******.*******0.43.*******": "system.serial.no", ".*******.*******.6.3.0": "system.serial.no", ".*******.*******.********": "system.serial.no", ".*******.*******.********.2.7": "system.description"}, "ping.check.status": "yes", "plugin.engine": "go", "plugin.engine.request": "Network Metric", "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"objects": [{"invalid.oids": [".*******.********.********.1001", ".*******.*******0.43.*******", ".*******.*******0.********", ".*******.*******.********"], "metric.plugin": "snmpscalarmetric", "oid.group.id": "15eb4e01-ffcf-4dcc-b0dd-4fdebfdd0630"}]}, "session-id": "015b9e7b-7359-4d4e-8988-2fd234a712fd", "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844590141", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624045, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587821, "interface.discovery": "no", "invalid.oids": [".*******.*******.305.*******", ".*******.*******.305.*******"], "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800587772, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "04c09031-7e65-403b-8b27-f7c42768accc", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.*******.1.56.0": "system.cpu.percent", ".*******.*******.1.57.0": "system.1min.avg.cpu.percent", ".*******.*******.1.58.0": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.10": "system.cpu.percent", ".*******.*******.*********.1.24": "system.1min.avg.cpu.load.percent", ".*******.*******.*********.1.25": "system.5min.avg.cpu.load.percent", ".*******.*******.*********.1.26": "system.15min.avg.cpu.load.percent", ".*******.*******.*********.1.3": "system.cpu.percent", ".*******.*******.*********.1.4": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.5": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.6": "system.cpu.percent", ".*******.*******.*********.1.7": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.8": "system.5min.avg.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 505, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.15min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.percent": 18, "system.5min.avg.cpu.load.percent": 0, "system.5min.avg.cpu.percent": 18, "system.cpu.percent": 19, "system.memory.used.percent": 20}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-16548445901432", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624045, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587821, "interface.discovery": "no", "invalid.oids": [".*******.*******.305.*******", ".*******.*******.305.*******"], "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800587772, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "04c09031-7e65-403b-8b27-f7c42768accc", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.*******.1.56.0": "system.cpu.percent", ".*******.*******.1.57.0": "system.1min.avg.cpu.percent", ".*******.*******.1.58.0": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.10": "system.cpu.percent", ".*******.*******.*********.1.24": "system.1min.avg.cpu.load.percent", ".*******.*******.*********.1.25": "system.5min.avg.cpu.load.percent", ".*******.*******.*********.1.26": "system.15min.avg.cpu.load.percent", ".*******.*******.*********.1.3": "system.cpu.percent", ".*******.*******.*********.1.4": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.5": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.6": "system.cpu.percent", ".*******.*******.*********.1.7": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.8": "system.5min.avg.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 505, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.15min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.percent": 18, "system.5min.avg.cpu.load.percent": 0, "system.5min.avg.cpu.percent": 18, "system.cpu.percent": 19, "system.memory.used.percent": 20}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844599761", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587780], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844599762", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:23.133 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624140, "event.timestamp": 1654845885, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587843, "interface.discovery": "yes", "invalid.oids": [".*******.*******.********", ".*******.********.********.1001", ".*******.*******0.43.*******", ".*******.*******0.********"], "metric.category": "Network", "metric.credential.profile": 58829800587780, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "System Info", "metric.object": 58829800587781, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 86400, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:54:40.317 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 8, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "15eb4e01-ffcf-4dcc-b0dd-4fdebfdd0630", "oid.group.name": "System Info", "oid.group.oids": {".*******.********.********.1": "system.serial.no", ".*******.*******.6.3.0": "system.serial.no", ".*******.*******.********.2.7": "system.description"}, "oid.group.polling.interval.sec": 86400, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 502, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.description": "CW_SYSDESCR$Cisco IOS Software, 7200 Software (C7200-ADVIPSERVICESK9-M), Version 15.2(4)S5, RELEASE SOFTWARE (fc1)\nTechnical Support: http://www.cisco.com/techsupport\nCopyright (c) 1986-2014 by Cisco Systems, Inc.\nCompiled Thu 20-Feb-14 06:51 by prod_rel+", "system.serial.no": "4279256517"}, "snmp.authentication.password": "ospf1md5", "snmp.authentication.protocol": "MD5", "snmp.security.level": "Authentication No Privacy", "snmp.security.user.name": "ospf1md5", "snmp.version": "v3", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844577937", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587759], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844577939", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:01.186 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624074, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587797, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587759, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587760, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:38:38.401 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "isis2.isis2", "object.id": 1, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "isis2.isis2", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 501, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 0, "cisco.huge.buffer.misses": 0, "cisco.large.buffer.misses": 0, "cisco.medium.buffer.misses": 297, "cisco.small.buffer.misses": 2754, "cisco.verylarge.buffer.misses": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-16548445901411", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624045, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587821, "interface.discovery": "no", "invalid.oids": [".*******.*******.305.*******", ".*******.*******.305.*******"], "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800587772, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "04c09031-7e65-403b-8b27-f7c42768accc", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.*******.1.56.0": "system.cpu.percent", ".*******.*******.1.57.0": "system.1min.avg.cpu.percent", ".*******.*******.1.58.0": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.10": "system.cpu.percent", ".*******.*******.*********.1.24": "system.1min.avg.cpu.load.percent", ".*******.*******.*********.1.25": "system.5min.avg.cpu.load.percent", ".*******.*******.*********.1.26": "system.15min.avg.cpu.load.percent", ".*******.*******.*********.1.3": "system.cpu.percent", ".*******.*******.*********.1.4": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.5": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.6": "system.cpu.percent", ".*******.*******.*********.1.7": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.8": "system.5min.avg.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 505, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.15min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.percent": 18, "system.5min.avg.cpu.load.percent": 0, "system.5min.avg.cpu.percent": 18, "system.cpu.percent": 19, "system.memory.used.percent": 20}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-16548445779373", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587759], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844577939", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:01.186 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624074, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587797, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587759, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587760, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:38:38.401 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "isis2.isis2", "object.id": 1, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "isis2.isis2", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 501, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 0, "cisco.huge.buffer.misses": 0, "cisco.large.buffer.misses": 0, "cisco.medium.buffer.misses": 297, "cisco.small.buffer.misses": 2754, "cisco.verylarge.buffer.misses": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-165484457793709", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587759], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844577939", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:01.186 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624074, "event.timestamp": 1654845825, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587797, "interface.discovery": "no", "metric.category": "Network", "metric.credential.profile": 58829800587759, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587760, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:38:38.401 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "isis2.isis2", "object.id": 1, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "isis2.isis2", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 501, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 0, "cisco.huge.buffer.misses": 0, "cisco.large.buffer.misses": 0, "cisco.medium.buffer.misses": 297, "cisco.small.buffer.misses": 2754, "cisco.verylarge.buffer.misses": 0}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-16548445901312", "credential.profile.protocol": "SNMP V1/V2c", "discovery.category": "Network", "discovery.context": {"interface.discovery": "no", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587771], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844590142", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:13.468 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624045, "event.timestamp": 1654845765, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587821, "interface.discovery": "no", "invalid.oids": [".*******.*******.305.*******", ".*******.*******.305.*******"], "metric.category": "Network", "metric.credential.profile": 58829800587771, "metric.credential.profile.protocol": "SNMP V1/V2c", "metric.discovery.method": "REMOTE", "metric.name": "CPU & Memory Statistics", "metric.object": 58829800587772, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 150, "metric.state": "ENABLE", "metric.type": "Router", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:52:38.346 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000006, 10000000000007, 10000000000013], "object.host": "ospf2.ospf2.com", "object.id": 5, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf2.ospf2.com", "object.snmp.device.catalog": 58829800524436, "object.state": "ENABLE", "object.system.oid": ".*******.*******.222", "object.target": "***********", "object.type": "Router", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "04c09031-7e65-403b-8b27-f7c42768accc", "oid.group.name": "CPU & Memory Statistics", "oid.group.oids": {".*******.*******.1.56.0": "system.cpu.percent", ".*******.*******.1.57.0": "system.1min.avg.cpu.percent", ".*******.*******.1.58.0": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.10": "system.cpu.percent", ".*******.*******.*********.1.24": "system.1min.avg.cpu.load.percent", ".*******.*******.*********.1.25": "system.5min.avg.cpu.load.percent", ".*******.*******.*********.1.26": "system.15min.avg.cpu.load.percent", ".*******.*******.*********.1.3": "system.cpu.percent", ".*******.*******.*********.1.4": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.5": "system.5min.avg.cpu.percent", ".*******.*******.*********.1.6": "system.cpu.percent", ".*******.*******.*********.1.7": "system.1min.avg.cpu.percent", ".*******.*******.*********.1.8": "system.5min.avg.cpu.percent"}, "oid.group.polling.interval.sec": 150, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 505, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"system.15min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.load.percent": 0, "system.1min.avg.cpu.percent": 18, "system.5min.avg.cpu.load.percent": 0, "system.5min.avg.cpu.percent": 18, "system.cpu.percent": 19, "system.memory.used.percent": 20}, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed", "timeout": 60}, "***********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844603127", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [58829800587783], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [10000000000013], "discovery.name": "SNMP-Test1654844603129", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:27.148 pm 10/06/2022", "discovery.target": "***********", "discovery.target.name": "***********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": 58829800624269, "event.timestamp": 1654845945, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 58829800587905, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": 58829800587783, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Buffer Statistics", "metric.object": 58829800587784, "metric.plugin": "snmpscalarmetric", "metric.polling.min.time": 150, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": 10000000000001, "object.category": "Network", "object.creation.time": "12:55:40.986 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000002, 10000000000003, 10000000000004, 10000000000013], "object.host": "ospf1.ospf1.com", "object.id": 9, "object.ip": "***********", "object.make.model": "Cisco 7206 VXR", "object.name": "ospf1.ospf1.com", "object.snmp.device.catalog": 58829800549968, "object.state": "ENABLE", "object.system.oid": ".*******.*******.2494", "object.target": "**********", "object.type": "Switch", "object.vendor": "Cisco Systems", "oid.group.device.type": "SNMP Device", "oid.group.id": "8e6688aa-3e07-4d9c-a14d-019b9f30655c", "oid.group.name": "Buffer Statistics", "oid.group.oids": {".*******.*******.1.12.0": "cisco.verylarge.buffer.misses", ".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.43.0": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses"}, "oid.group.polling.interval.sec": 300, "oid.group.polling.timeout.sec": 60, "oid.group.type": "scalar", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 507, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"cisco.big.buffer.misses": 83, "cisco.extra.large.buffer.misses": 0, "cisco.huge.buffer.misses": 51, "cisco.large.buffer.misses": 53, "cisco.medium.buffer.misses": 1438, "cisco.small.buffer.misses": 4558}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "status": "succeed", "timeout": 60}}