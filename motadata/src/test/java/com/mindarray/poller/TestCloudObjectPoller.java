/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import static com.mindarray.api.AIOpsObject.OBJECT_TYPE;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCloudObjectPoller
{

    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollAWSCloudMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByTarget(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AWS_CLOUD.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollAWSCloudBillingMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByTarget(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AWS_BILLING.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollAWSEC2Metric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "ServiceOps_Saas_AMI(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollAWSSNSMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollAWSDynamoDBMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "motadata8-dynamoDB(ap-south-1)"), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollAWSEBSMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollAWSELBMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollAWSRDSMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "motadata(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPollAWSS3Metric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-northeast-3").put(AIOpsObject.OBJECT_TARGET, "motadata-common(ap-northeast-3)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollAzureCloudMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByTarget(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AWS_CLOUD.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollAzureCosmosDBMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "motadata-freetier").put(AIOpsObject.OBJECT_TARGET, "motadatacosmosdb(motadata-freetier)"), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testPollAzureSQLDatabaseMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "DefaultResourceGroup-EUS2").put(AIOpsObject.OBJECT_TARGET, "master(DefaultResourceGroup-EUS2)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testPollAzureStorageMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "motadatawin(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testPollAzureVMMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "motadata-freetier").put(AIOpsObject.OBJECT_TARGET, "ubuntu-linux2(motadata-freetier)").put(AIOpsObject.OBJECT_NAME, "ubuntu-linux2(motadata-freetier)").put(OBJECT_TYPE, NMSConstants.Type.AZURE_VM.getName()), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testPollAzureWebAppMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testPollAzureBillingMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByTarget(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AZURE_CLOUD.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testPollOffice365Metric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByTarget(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.OFFICE_365.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testPollSharepointOnlineMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_NAME, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testPollExchangeOnlineStatusMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testPollMicrosoftTeamsMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_NAME, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testPollOneDriveMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testPollSharepointOnlineStatusMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testPollMicrosoftTeamsStatusMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollOneDriveStatusMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testPollOffice365StatusMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testPollExchangeOnlineMetric(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testPollAmazonCloudFrontMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "E1RVVPM08N16XT(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testPollAWSAutoScalingMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testPollAWSLambdaMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "us-east-1").put(AIOpsObject.OBJECT_TARGET, "aaalamda(us-east-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testPollAmazonSQSMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "sqs(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testPollAWSElasticBeanstalkMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_REGION, "ap-south-1").put(AIOpsObject.OBJECT_TARGET, "sqs(ap-south-1)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testPollAzureServiceBusMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "servbsq12(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testPollAzureApplicationGatewayMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "appgateway123(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testPollAzureFunctionMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "motadata-freetier").put(AIOpsObject.OBJECT_TARGET, "FunctionApp120210317184034(motadata-freetier)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testPollAzureLoadBalancerMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "lb(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testPollAzureVMScaleSetMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "saleset(cloud-shell-storage-centralindia)"), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testPollAzureCDNMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_TYPE, POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())).getJsonObject(0)).put(AIOpsObject.OBJECT_RESOURCE_GROUP, "cloud-shell-storage-centralindia").put(AIOpsObject.OBJECT_TARGET, "cdntest(cloud-shell-storage-centralindia)"), testContext);
    }
}
