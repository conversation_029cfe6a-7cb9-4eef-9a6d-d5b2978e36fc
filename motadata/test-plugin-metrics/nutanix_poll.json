{"172.16.10.210": {"result": {"nutanix": "NTNX-806419bd-A", "nutanix.acropolis.connection.state": "kConnected", "nutanix.cluster": "Motadata-Cluster", "nutanix.cluster.uuid": "0005f64e-ac3c-aa74-66ad-005056bb7be9", "nutanix.controller.io.bandwidth.bytes.per.sec": 0, "nutanix.controller.io.latency.ms": 5, "nutanix.controller.iops": 0, "nutanix.controller.read.io.bandwidth.bytes.per.sec": 0, "nutanix.controller.read.iops": 0, "nutanix.controller.write.io.bandwidth.bytes.per.sec": 0, "nutanix.controller.write.iops": 0, "nutanix.cpu.capacity.hz": 28800000000, "nutanix.cpu.cores": 12, "nutanix.cpu.frequency.hz": 2400000000, "nutanix.cpu.model": "Intel(R) Xeon(R) CPU E5-4657L v2 @ 2.40GHz", "nutanix.cpu.percent": 11, "nutanix.cpu.sockets": 4, "nutanix.cpu.threads": 12, "nutanix.hypervisor.io.bandwidth.bytes.per.sec": 0, "nutanix.hypervisor.io.latency.ms": 0, "nutanix.hypervisor.iops": 0, "nutanix.hypervisor.name": "Nutanix 20190916.276", "nutanix.hypervisor.read.iops": 0, "nutanix.hypervisor.state": "kAcropolisNormal", "nutanix.hypervisor.type": "kKvm", "nutanix.hypervisor.write.iops": 0, "nutanix.io.bandwidth.bytes.per.sec": 1000, "nutanix.io.latency.ms": 14, "nutanix.iops": 0, "nutanix.memory.capacity.bytes": 33721155584, "nutanix.memory.used.percent": 60, "nutanix.oplog.disk.percent": 10.8, "nutanix.oplog.disk.size.bytes": 72426913110, "nutanix.read.io.bandwidth.bytes.per.sec": 0, "nutanix.read.iops": 0, "nutanix.serial.number": "36d56d40-d1ff-4809-a320-1d5e40187f7c", "nutanix.service.vm.external.ip": "*************", "nutanix.state": "NORMAL", "nutanix.storage.capacity.bytes": 511803343324, "nutanix.storage.free.bytes": 509234542925, "nutanix.storage.tier.das.sata.capacity.bytes": 0, "nutanix.storage.tier.das.sata.free.bytes": 0, "nutanix.storage.tier.das.sata.used.bytes": 0, "nutanix.storage.tier.ssd.capacity.bytes": 511803343324, "nutanix.storage.tier.ssd.free.bytes": 509234542925, "nutanix.storage.tier.ssd.used.bytes": 2568800399, "nutanix.storage.used.bytes": 2568800399, "nutanix.type": "HYPER_CONVERGED", "nutanix.vms": 3, "nutanix.write.io.bandwidth.bytes.per.sec": 0, "nutanix.write.iops": 0}, "errors": []}}