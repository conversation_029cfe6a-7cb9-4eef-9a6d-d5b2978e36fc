{"entity": "Monitor", "table": "tbl_config_object", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "object.name", "title": "Object", "type": "string", "rules": ["required"]}, {"name": "object.host", "title": "Host", "type": "string", "rules": ["required"]}, {"name": "object.ip", "title": "IP", "type": "string"}, {"name": "object.groups", "title": "Group(s)", "type": "list", "rules": ["required"]}, {"name": "object.state", "title": "State", "type": "string", "values": ["ENABLE", "DISABLE", "MAINTENANCE", "ARCHIVE"]}, {"name": "object.event.processors", "title": "Event Processor(s)", "type": "list"}, {"name": "object.custom.fields", "title": "Custom Fields", "type": "map"}]}