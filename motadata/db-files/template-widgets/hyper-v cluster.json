[{"_type": "0", "id": 10000000000808, "visualization.name": "CPU Core", "visualization.description": "CPU HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.cpu.cores"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Cores", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.cluster.cpu.cores.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "hyperv.cluster.cpu.cores"}}}}, {"_type": "0", "id": 10000000000809, "visualization.name": "Disk Utilization", "visualization.description": "Disk Storage Details HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.disk.used.bytes"}, {"type": "metric", "data.point": "hyperv.cluster.disk.capacity.bytes"}, {"type": "metric", "data.point": "hyperv.cluster.disk.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "data.points": [{"label": "Capacity", "value": "hyperv.cluster.disk.capacity.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "hyperv.cluster.disk.free.bytes.last"}, {"label": "Used", "value": "hyperv.cluster.disk.used.bytes.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hyperv.cluster.disk.used.bytes"}}}}, {"_type": "0", "id": 10000000000810, "visualization.name": "Memory", "visualization.description": "Memory Details HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.memory.free.bytes"}, {"type": "metric", "data.point": "hyperv.cluster.memory.used.bytes"}, {"type": "metric", "data.point": "hyperv.cluster.memory.installed.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "data.points": [{"label": "Capacity", "value": "hyperv.cluster.memory.installed.bytes.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "hyperv.cluster.memory.free.bytes.last"}, {"label": "Used", "value": "hyperv.cluster.memory.used.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "hyperv.cluster.memory.used.bytes"}}}}, {"_type": "0", "id": 10000000000811, "visualization.name": "Nodes", "visualization.description": "Nodes HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.nodes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Nodes", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.cluster.nodes.last"}]}, "style": {"icon": {"name": "node-count"}, "color.data.point": "hyperv.cluster.nodes"}}}}, {"_type": "0", "id": 10000000000812, "visualization.name": "Virtual Machines", "visualization.description": "Virtual Machines HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Virtual Machines", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.cluster.virtual.machines.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "hyperv.cluster.virtual.machines"}}}}, {"_type": "0", "id": 10000000000813, "visualization.name": "Logical Processors", "visualization.description": "Logical Processors HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.logical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hyperv.cluster.logical.processors"}]}], "visualization.properties": {"gauge": {"header": {"title": "Logical Processors", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "hyperv.cluster.logical.processors.last"}]}, "style": {"icon": {"name": "processor"}, "color.data.point": "hyperv.cluster.logical.processors"}}}}, {"_type": "0", "id": 10000000000814, "visualization.name": "System Disk Utilization", "visualization.description": "System Disk Utilization HyperV Cluster", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.disk.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.free.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000815, "visualization.name": "System Memory Utilization", "visualization.description": "System Memory HyperV Cluster", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000816, "visualization.name": "Node Details", "visualization.description": "Node Details HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.node~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~logical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.node~memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node", "title": "Cluster Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "hyperv.cluster.node~logical.processors.last", "title": "Logical Processors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~running.virtual.machines.last", "title": "Running VMs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~cpu.cores.last", "title": "CPU Cores", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~virtual.machines.last", "title": "Node VMs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~memory.installed.bytes.last", "title": "Total Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~memory.used.bytes.last", "title": "Used Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.node~memory.free.bytes.last", "title": "Free Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000817, "visualization.name": "Disk Volume Details", "visualization.description": "Disk Volume HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.disk.volume~label", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.volume~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.volume~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.disk.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume", "title": "Disk Volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume~label.last", "title": "Label", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume~capacity.bytes.last", "title": "Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume~free.bytes.last", "title": "Free", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.disk.volume~used.bytes.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000818, "visualization.name": "VMs Details", "visualization.description": "Virtual Machines HyperV Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hyperv.cluster.vm~power.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.vm~server", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.vm~memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hyperv.cluster.vm~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.vm~server.last", "title": "VM Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.vm~power.state.last", "title": "Power State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "hyperv.cluster.vm~memory.free.bytes.last", "title": "Free Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "hyperv.cluster.vm~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000819, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000820, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000821, "visualization.name": "Response Time vs. Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]