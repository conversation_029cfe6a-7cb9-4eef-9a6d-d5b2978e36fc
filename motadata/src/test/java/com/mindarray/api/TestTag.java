/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.patch.Patch8013;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.zeromq.SocketType;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.Agent.AGENT_BUSINESS_HOUR_PROFILE;
import static com.mindarray.api.Agent.AGENT_STATUS_TYPE;
import static com.mindarray.db.DBConstants.FIELD_NAME;
import static com.mindarray.db.DBConstants.FIELD_TYPE;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestTag
{
    private static final Logger LOGGER = new Logger(TestTag.class, "tag-test", "Tag Test");
    private static final String interfaceIndex = "63";
    private static long agentId = DUMMY_ID;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var cipherUtil = new CipherUtil();

        try (var producer = Bootstrap.zcontext().socket(SocketType.PUSH))
        {
            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

            var context = new JsonObject().put("object.ip", "***********")
                    .put("agent.version", MotadataConfigUtil.getVersion())
                    .put("agent.os.name", "linux")
                    .put("objects", new JsonArray().add("***********"))
                    .put("agent.id", "WEFGHIWE")
                    .put("event.type", "agent.registration")
                    .put("object.host", "agent-test-1")
                    .put("agent.configs", new JsonObject().put("agent", new JsonObject()
                            .put("agent.id", "WEFGHIWE")
                            .put("agent.deactivation.status", "no")
                            .put("metric.agent.status", "no")
                            .put("agent.state", "ENABLE")));

            producer.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(context.encode()))).getBytes());

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            LOGGER.trace(String.format("Agent config store, after agent register: %s", AgentConfigStore.getStore().getItems()));

            agentId = AgentConfigStore.getStore().getItemByValue("agent.id", "WEFGHIWE").getLong(ID);

            var clazz = Patch8013.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(han -> testContext.completeNow());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @BeforeAll
    static void afterAll(VertxTestContext testContext)
    {
        LOGGER.trace("Object Config Store Item : " + ObjectConfigStore.getStore().getItems());

        LOGGER.trace("Tag Config Store Item : " + TagConfigStore.getStore().getItems());

        testContext.completeNow();
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetTags(VertxTestContext testContext, TestInfo testInfo)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132");

        object.put(OBJECT_SYSTEM_TAGS, new JsonArray().add("Development"));

        object.put(OBJECT_USER_TAGS, new JsonArray().add("Q.A."));

        object.put(OBJECT_TAGS, TagConfigStore.getStore().addItems(object.getJsonArray(OBJECT_USER_TAGS), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER));

        object.put(OBJECT_TAGS, object.getJsonArray(OBJECT_TAGS).addAll(TagConfigStore.getStore().addItems(object.getJsonArray(OBJECT_SYSTEM_TAGS), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_SYSTEM)));

        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                object, GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handle -> ObjectConfigStore.getStore().updateItem(object.getLong(ID)).onComplete(asyncResult -> promise.complete()));

        promise.future().onComplete(result ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": configDBService update operation completed.");

            TestAPIUtil.get(OBJECT_TAG_API_ENDPOINT,
                    testContext.succeeding(response ->
                            testContext.verify(() ->
                            {
                                LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                                var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                                Assertions.assertNotNull(items);

                                Assertions.assertFalse(items.isEmpty());

                                testContext.completeNow();

                            })));
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetUserTags(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"tag.type\":\"Object\"}");

        TestAPIUtil.get(OBJECT_TAG_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testAddInstanceTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertNotNull(id, "device ********** should be provision");

        TestAPIUtil.get(TAG_API_ENDPOINT + "/instances/" + id, testContext.succeeding(response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var entities = response.bodyAsJsonObject().getJsonObject(RESULT);

            for (var entity : entities) //will receive one instance
            {
                var items = entities.getJsonArray(entity.getKey());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (interfaceIndex.equalsIgnoreCase(item.getString(OBJECT_NAME))) //adding tags in interface instance(object.name = 63)
                    {
                        item.put(INSTANCE_TAGS, new JsonArray().add("instanceTag1").add("instanceKey:value"));
                    }
                    else if ("38".equalsIgnoreCase(item.getString(OBJECT_NAME)))
                    {
                        item.put(INSTANCE_TAGS, new JsonArray().add("instanceTag2").add("instancekey:value2"));
                    }
                    else if ("54".equalsIgnoreCase(item.getString(OBJECT_NAME)))
                    {
                        item.put(INSTANCE_TAGS, new JsonArray().add("instanceTag3").add("instancekey:value3"));
                    }
                }

                TestAPIUtil.put(TAG_API_ENDPOINT + "/instance-tags/" + id, entities, testContext.succeeding(message -> testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": message: " + message.bodyAsJsonObject().encode());

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": MetricConfigStore: " + MetricConfigStore.getStore().getItemsByObject(id));

                    Assertions.assertEquals(200, message.statusCode());

                    testContext.completeNow();
                })));
            }
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetInstanceTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("**********");

        Assertions.assertNotNull(id, "device ********** should be provision");

        try
        {
            LOGGER.info("testGetInstanceTag: Metric: " + MetricConfigStore.getStore().getItemsByObject(CommonUtil.getLong(id)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }


        TestAPIUtil.get(TAG_API_ENDPOINT + "/instances/" + id, testContext.succeeding(response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var entities = response.bodyAsJsonObject().getJsonObject(RESULT);

            for (var entity : entities)
            {
                var items = entities.getJsonArray(entity.getKey());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (interfaceIndex.equalsIgnoreCase(item.getString(OBJECT_NAME)))
                    {
                        Assertions.assertTrue(item.containsKey(INSTANCE_TAGS));

                        Assertions.assertTrue(item.getJsonArray(INSTANCE_TAGS).contains("instanceTag1"));

                        Assertions.assertTrue(item.getJsonArray(INSTANCE_TAGS).contains("instanceKey:value"));

                        testContext.completeNow();
                    }
                    else
                    {
                        Assertions.assertFalse(item.containsKey(INSTANCE_TAGS));

                        testContext.completeNow();
                    }
                }
            }
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testAddBulkMonitorTag(VertxTestContext testContext, TestInfo testInfo)
    {
        var ids = new JsonArray();

        ids.add(ObjectConfigStore.getStore().getItemByIP("**********")).add(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47"));

        TestAPIUtil.post(OBJECT_API_ENDPOINT + "/update", new JsonObject().put("ids", ids).put(AIOpsObject.OBJECT_TAGS, new JsonArray().add("Development:8.0")), testContext.succeeding(response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger("response-code"));

            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            Assertions.assertEquals("Monitor updated successfully", result.getString(MESSAGE));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testAddBulkMonitorCustomGroupBulk(VertxTestContext testContext, TestInfo testInfo)
    {
        var ids = new JsonArray();

        ids.add(ObjectConfigStore.getStore().getItemByIP("**********")).add(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, new JsonObject()
                .put(Group.FIELD_GROUP_NAME, "TestTag")
                .put(Group.FIELD_PARENT_GROUP, 0)
                .put(Group.GROUP_CONTEXT, new JsonObject().put(Group.GROUP_AUTO_ASSIGN, NO)
                        .put(Group.GROUP_OPERATOR, "or")), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                TestAPIUtil.post(OBJECT_API_ENDPOINT + "/update", new JsonObject().put("ids", ids).put(AIOpsObject.OBJECT_GROUPS, new JsonArray().add(asyncResult.result().bodyAsJsonObject().getLong(ID))), testContext.succeeding(response ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                    var result = response.bodyAsJsonObject();

                    Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger("response-code"));

                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                    Assertions.assertEquals("Monitor updated successfully", result.getString(MESSAGE));

                    testContext.completeNow();
                }));
            }
        });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testAddAgentTag(VertxTestContext testContext)
    {
        TestAPIUtil.put(AGENT_API_ENDPOINT + agentId, new JsonObject().put(OBJECT_TAGS, new JsonArray().add("agentTag1")).put(AIOpsObject.OBJECT_GROUPS, new JsonArray().add(10000000000017L)).put(AGENT_BUSINESS_HOUR_PROFILE, 10000000000001L).put(AGENT_STATUS_TYPE, "Heartbeat").put(OBJECT_NAME, "motadata123").put(OBJECT_IP, "***********"), testContext.succeeding(response ->
        {

            var result = response.bodyAsJsonObject();

            LOGGER.trace(String.format("Agent API response: %s", result));

            Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger("response-code"));

            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            Assertions.assertEquals("Agent updated successfully", result.getString(MESSAGE));

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {
                var item = AgentConfigStore.getStore().getItem(agentId);

                if (item.containsKey(OBJECT_TAGS) && !item.getJsonArray(OBJECT_TAGS).isEmpty())
                {
                    var items = TagConfigStore.getStore().addItems(new JsonArray().add("agentTag1"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);

                    Assertions.assertTrue(item.getJsonArray(OBJECT_TAGS).contains(items.getInteger(0)));

                    Assertions.assertTrue(ObjectConfigStore.getStore().getItemByAgentId(agentId).getJsonArray(OBJECT_TAGS).contains(items.getInteger(0)));

                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..event-source should be deregistered");
                    }
                }
            });
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteAgentTag(VertxTestContext testContext)
    {
        TestAPIUtil.put(AGENT_API_ENDPOINT + agentId, new JsonObject().put(OBJECT_TAGS, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray().add(10000000000017L)).put(AGENT_BUSINESS_HOUR_PROFILE, 10000000000001L).put(AGENT_STATUS_TYPE, "Heartbeat").put(OBJECT_NAME, "motadata123").put(OBJECT_IP, "***********"), testContext.succeeding(response ->
        {
            var result = response.bodyAsJsonObject();

            LOGGER.trace(String.format("Agent API response: %s", result));

            Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger("response-code"));

            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            Assertions.assertEquals("Agent updated successfully", result.getString(MESSAGE));

            var retries = new AtomicInteger(3);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {
                var item = AgentConfigStore.getStore().getItem(agentId);

                if (item.containsKey(OBJECT_TAGS) && item.getJsonArray(OBJECT_TAGS).isEmpty())
                {
                    Assertions.assertTrue(item.getJsonArray(OBJECT_TAGS).isEmpty());

                    Assertions.assertTrue(ObjectConfigStore.getStore().getItemByAgentId(agentId).getJsonArray(OBJECT_TAGS).isEmpty());

                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        testContext.failNow("max attempt exceeded..event-source should be deregistered");
                    }
                }
            });
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testAddInstanceTagInVMs(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertNotNull(id, "device ************ should be provision");

        TestAPIUtil.get(TAG_API_ENDPOINT + "/instances/" + id, testContext.succeeding(response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var entities = response.bodyAsJsonObject().getJsonObject(RESULT);

            for (var entity : entities) //will receive one instance
            {
                var items = entities.getJsonArray(entity.getKey());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    item.put(INSTANCE_TAGS, new JsonArray().add("Vms").add("Vms:value"));

                }

                TestAPIUtil.put(TAG_API_ENDPOINT + "/instance-tags/" + id, entities, testContext.succeeding(message -> testContext.verify(() ->
                {

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": message: " + message.bodyAsJsonObject().encode());

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": MetricConfigStore: " + MetricConfigStore.getStore().getItem(CommonUtil.getLong(id)));

                    Assertions.assertEquals(200, message.statusCode());

                    testContext.completeNow();
                })));
            }
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testAddInstanceTagInAccessPoint(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        Assertions.assertNotNull(id, "device *********** should be provision");

        TestAPIUtil.get(TAG_API_ENDPOINT + "/instances/" + id, testContext.succeeding(response ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            var entities = response.bodyAsJsonObject().getJsonObject(RESULT);

            for (var entity : entities) //will receive one instance
            {
                var items = entities.getJsonArray(entity.getKey());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    item.put(INSTANCE_TAGS, new JsonArray().add("accessPoint").add("accessPoint:Cisco"));
                }

                TestAPIUtil.put(TAG_API_ENDPOINT + "/instance-tags/" + id, entities, testContext.succeeding(message -> testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": message: " + message.bodyAsJsonObject().encode());

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": MetricConfigStore: " + MetricConfigStore.getStore().getItem(CommonUtil.getLong(id)));

                    Assertions.assertEquals(200, message.statusCode());

                    testContext.completeNow();

                })));

            }
        }));
    }

    //MOTADATA-4545
    /*
     *  when we call addItems multiple times with same tag,
     *  In first iteration it will add in cache and after that it will store to config store
     * */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testAddMultipleTags(VertxTestContext testContext)
    {
        var hashCode = "bulk-tag".hashCode();

        for (var index = 0; index <= 5; index++)
        {
            Assertions.assertEquals(CommonUtil.getLong(hashCode), TagConfigStore.getStore().addItems(new JsonArray().add("bulk-tag"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER).getLong(0));
        }

        testContext.completeNow();
    }

    /*
     *  there are two type of tags: System (0) and User (1)
     *  if any tag arrives as System tag we will store that tag with System type,
     *  And will never change its type value
     *
     *  but, in user type, it can be refactored due to same tag arrives with system type.
     * */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testTypePriority(VertxTestContext testContext)
    {
        //adding tag as a User tag...
        var hashCode = TagConfigStore.getStore().addItems(new JsonArray().add("system-tag-test"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER).getLong(0);
        //type will be user...
        Assertions.assertTrue(DBConstants.ENTITY_TYPE_USER.equalsIgnoreCase(TagConfigStore.getStore().getItem(hashCode).getString(FIELD_TYPE)));

        //adding same tag but as a System tag...
        Assertions.assertEquals(hashCode, TagConfigStore.getStore().addItems(new JsonArray().add("system-tag-test"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_SYSTEM).getLong(0));
        //type will be override due to system type priority
        Assertions.assertTrue(DBConstants.ENTITY_TYPE_SYSTEM.equalsIgnoreCase(TagConfigStore.getStore().getItem(hashCode).getString(FIELD_TYPE)));

        //now adding same tag as user tag...
        Assertions.assertEquals(hashCode, TagConfigStore.getStore().addItems(new JsonArray().add("system-tag-test"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER).getLong(0));
        //type will not be overridden, because user type is not priority
        Assertions.assertTrue(DBConstants.ENTITY_TYPE_SYSTEM.equalsIgnoreCase(TagConfigStore.getStore().getItem(hashCode).getString(FIELD_TYPE)));

        testContext.completeNow();
    }

    /*
     *  In tagConfigStore tag.type will be JsonArray,
     * If same tag is used in different types, we will store list of types
     * */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAddMultipleTagType(VertxTestContext testContext)
    {
        //adding object tag, tag.type will have one entry of Tag.TagType.OBJECT
        var hashCode = TagConfigStore.getStore().addItems(new JsonArray().add("multiple-type"), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER).getLong(0);

        Assertions.assertTrue(TagConfigStore.getStore().getItem(hashCode).getJsonArray(Tag.TAG_TYPE).contains(Tag.TagType.OBJECT.getName()));

        Assertions.assertEquals(1, TagConfigStore.getStore().getItem(hashCode).getJsonArray(Tag.TAG_TYPE).size());


        //adding same tag in Instance tag, tag.type will have two entries Tag.TagType.OBJECT and Tag.TagType.INSTANCE
        Assertions.assertEquals(hashCode, TagConfigStore.getStore().addItems(new JsonArray().add("multiple-type"), Tag.TagType.INSTANCE.getName(), DBConstants.ENTITY_TYPE_SYSTEM).getLong(0));

        Assertions.assertTrue(TagConfigStore.getStore().getItem(hashCode).getJsonArray(Tag.TAG_TYPE).contains(Tag.TagType.OBJECT.getName()));

        Assertions.assertTrue(TagConfigStore.getStore().getItem(hashCode).getJsonArray(Tag.TAG_TYPE).contains(Tag.TagType.INSTANCE.getName()));

        Assertions.assertEquals(2, TagConfigStore.getStore().getItem(hashCode).getJsonArray(Tag.TAG_TYPE).size());


        testContext.completeNow();
    }
}
