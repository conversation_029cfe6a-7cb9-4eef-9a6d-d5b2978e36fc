/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.store.FlowIPMapperConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class FlowIPMapper extends AbstractAPI
{

    public static final String FLOW_IP_MAPPER_PROFILE_NAME = "flow.ip.mapper.profile.name";

    public static final String FLOW_IP_MAPPER_SOURCE = "flow.ip.mapper.source";

    public static final String FLOW_IP_MAPPER_DESCRIPTION = "flow.ip.mapper.description";

    public static final String MANUAL_MAPPING = "Manual Mapping";

    public static final String FLOW_IP_MAPPER_TARGET = "flow.ip.mapper.target"; //CSV

    private static final Logger LOGGER = new Logger(FlowIPMapper.class, MOTADATA_API, "Flow IP Mapper API");

    public FlowIPMapper()
    {
        super("flow-ip-mappers", FlowIPMapperConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/users").handler(this::getUsers);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    private void getUsers(RoutingContext routingContext)
    {
        try
        {
            var users = new JsonObject();

            var target = FlowIPMapperConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var csvFile = CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + target.getString(FLOW_IP_MAPPER_TARGET);

            if (Bootstrap.vertx().fileSystem().existsBlocking(csvFile))
            {
                Bootstrap.vertx().fileSystem().readFile(csvFile).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        var content = CommonUtil.getString(result.result());

                        if (CommonUtil.isNotNullOrEmpty(content))
                        {
                            var header = content.trim().split("\\n")[0];

                            if (CommonUtil.isNotNullOrEmpty(header))
                            {
                                var position = -1;

                                for (var column : header.trim().split(","))
                                {
                                    position++;

                                    if (column.trim().equalsIgnoreCase("IP"))
                                    {
                                        for (var columns : content.trim().split("\\n"))
                                        {
                                            if (columns.trim().split(",").length > 0)
                                            {
                                                var values = columns.trim().split(",");

                                                var ip = values[position].trim();

                                                var username = values.length == 2 ? values[position + 1].trim() : EMPTY_VALUE;

                                                if (CommonUtil.isNotNullOrEmpty(ip) && CommonUtil.isNotNullOrEmpty(username) && !ip.equalsIgnoreCase("IP") && !username.equalsIgnoreCase("Username"))
                                                {
                                                    users.put(ip, username);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, users));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format("%s is not readable", csvFile)));
                    }
                });
            }
            else
            {
                send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format("%s file not found", csvFile)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
