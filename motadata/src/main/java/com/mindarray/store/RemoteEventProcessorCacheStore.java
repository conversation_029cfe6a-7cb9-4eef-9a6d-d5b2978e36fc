/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_DATASTORE_PING;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;

public class RemoteEventProcessorCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(RemoteEventProcessorCacheStore.class, GlobalConstants.MOTADATA_STORE, "Remote Event Processor Cache Store");
    private static final RemoteEventProcessorCacheStore STORE = new RemoteEventProcessorCacheStore();
    private final Map<Long, JsonObject> items = new ConcurrentHashMap<>();
    private final Map<Long, Long> durations = new ConcurrentHashMap<>();
    private final Map<Long, Integer> progresses = new ConcurrentHashMap<>();


    private RemoteEventProcessorCacheStore()
    {
    }

    public static RemoteEventProcessorCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.vertx().<Void>executeBlocking(future ->
        {
            try
            {
                var buffer = Bootstrap.vertx().fileSystem().existsBlocking(GlobalConstants.REMOTE_EVENT_PROCESSOR_CACHE_PATH) ? Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.REMOTE_EVENT_PROCESSOR_CACHE_PATH) : null;

                if (buffer != null && buffer.length() > 0)
                {
                    var caches = new JsonArray(new String(CodecUtil.toBytes(buffer.getBytes())));

                    for (var index = 0; index < caches.size(); index++)
                    {
                        var cache = caches.getJsonObject(index);

                        items.put(cache.getLong(GlobalConstants.ID), cache);

                        LOGGER.debug(String.format("cache added %s ", cache.encode()));
                    }
                }

                future.complete();

                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
            }
            catch (Exception exception)
            {
                future.fail(exception);

                LOGGER.error(exception);
            }
        }, result ->
        {
            if (result.succeeded())
            {
                promise.complete();
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public void updateItem(long id, JsonObject item)
    {
        items.put(id, item);
    }

    public JsonObject getItem(long id)
    {
        return items.containsKey(id) ? items.get(id).copy() : new JsonObject();
    }

    public void updateDuration(long id, long duration)
    {
        var item = RemoteEventProcessorCacheStore.getStore().getItem(id)
                .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                .put(GlobalConstants.DURATION, duration).put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING);

        RemoteEventProcessorCacheStore.getStore().updateItem(id, item);

        item = RemoteEventProcessorConfigStore.getStore().getItem(id);

        if (RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.valueOf(item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE))) != null)
        {
            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_PING, new JsonObject().put(STATUS, STATUS_UP).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));
        }
    }

    public long getDuration(long id)
    {
        var duration = 0L;

        if (items.containsKey(id))
        {
            duration = items.get(id).getLong(EVENT_TIMESTAMP, 0L);

            if (DateTimeUtil.currentSeconds() - duration >= 30)
            {
                duration = 0L;
            }
        }

        return duration;
    }

    public String getState(long id)
    {
        if (items.containsKey(id))
        {
            return items.get(id).getString(MotadataAppManager.HEARTBEAT_STATE);
        }

        return NMSConstants.STATE_NOT_RUNNING;
    }

    public String getActiveDatastoreProcessor()
    {
        // first priority is for standalone or primary for HA.

        var id = RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.STANDALONE);

        if (id == null || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE) || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING))
        {
            id = RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.PRIMARY);

            if (id == null || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE) || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING))
            {
                id = RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.SECONDARY);

                if (id == null || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE) || getState(id).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING))
                {
                    id = RemoteEventProcessorConfigStore.getStore().getItemByMode(GlobalConstants.InstallationMode.FAILOVER);
                }
            }
        }

        return id != null ? RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID) : EMPTY_VALUE;
    }

    public void updateProgress(long id, int progress)
    {
        progresses.put(id, progress);
    }

    public Integer getProgress(long id)
    {
        return progresses.getOrDefault(id, null);
    }

    public void clearProgress(long id)
    {
        progresses.remove(id);
    }


    public void deleteItem(long id)
    {
        items.remove(id);

        durations.remove(id);
    }

    public void updateDuration(long id, String state)
    {
        if (state.equalsIgnoreCase(GlobalConstants.YES))
        {
            durations.put(id, DateTimeUtil.currentSeconds());
        }
        else
        {
            durations.remove(id);
        }
    }

    public long getStateDuration(long id)
    {
        return durations.getOrDefault(id, 0L);
    }
}
