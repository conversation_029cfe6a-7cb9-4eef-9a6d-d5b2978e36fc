[{"_type": "0", "id": 10000000002186, "visualization.name": "Running Instances", "visualization.description": "Azure MySql Running Instances", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Running Instances", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}, "style": {"icon": {"name": "running-instance"}, "color.data.point": ""}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002187, "visualization.name": "Connections", "visualization.description": "Azure MySql Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "", "value": ""}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Failed", "value": "azure.mysql.server.failed.connections.avg"}, {"label": "Active", "value": "azure.mysql.server.active.connections.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002188, "visualization.name": "Disk IO", "visualization.description": "Azure MySql Disk IO", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk IO", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002189, "visualization.name": "Total Queries", "visualization.description": "Azure MySql Total Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Total Queries", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002190, "visualization.name": "CPU Utilization", "visualization.description": "Azure MySql CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.cpu.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002191, "visualization.name": "Memory Used Percent", "visualization.description": "Azure MySql Memory Used Percent", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.memory.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory Used Percent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.memory.percent.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000002192, "visualization.name": "Top Instances with Highest CPU Utilization", "visualization.description": "Top Instances with Highest CPU Utilization Azure MySql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.cpu.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.cpu.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002193, "visualization.name": "Top Instances with Highest Memory Consumption", "visualization.description": "Top Instances with Highest Memory Consumption Azure MySql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.memory.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.memory.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002194, "visualization.name": "Top Instances with Highest IO Consumption", "visualization.description": "Top Instances with Highest IO Consumption Azure MySql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.io.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.io.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002195, "visualization.name": "TOP Instances By Active Connections", "visualization.description": "TOP Instances By Active Connections Azure MySql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.active.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002196, "visualization.name": "TOP Instances By Failed Connections", "visualization.description": "TOP Instances By Failed Connections Azure MySql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.failed.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002197, "visualization.name": "TOP Instances By Highest Response Time", "visualization.description": "TOP Instances By Highest Response Time Azure MySql", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002198, "visualization.name": "Top Database By Network In", "visualization.description": "Top Database By Network In Azure MySql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.network.ingress.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.network.ingress.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000002199, "visualization.name": "Top Database By Network Out", "visualization.description": "Top Database By Network Out Azure MySql", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.network.egress.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.network.egress.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]