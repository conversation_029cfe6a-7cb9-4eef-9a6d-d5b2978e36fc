[{"id": 10000000000553, "_type": "0", "visualization.name": "Connection", "visualization.description": "MySQL Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.aborted.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.opened.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.aborted.clients"}, {"type": "metric", "data.point": "mysql.aborted.connections"}, {"type": "metric", "data.point": "mysql.connections"}, {"type": "metric", "data.point": "mysql.opened.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "mysql.opened.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "Aborted Connections", "value": "mysql.aborted.connections.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Connections", "value": "mysql.connections.last"}, {"label": "Opened", "value": "mysql.opened.connections.last"}]}}}}, {"id": 10000000000554, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "MySQL Thread", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.running.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.connected.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.created.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.delayed.insert.threads"}, {"type": "metric", "data.point": "mysql.slow.launch.threads"}, {"type": "metric", "data.point": "mysql.running.threads"}, {"type": "metric", "data.point": "mysql.connected.threads"}, {"type": "metric", "data.point": "mysql.created.threads"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Threads", "style": {"font.size": "medium"}, "data.points": [{"label": "Running Threads", "value": "mysql.running.threads.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Connected", "value": "mysql.connected.threads.last"}, {"label": "Created", "value": "mysql.created.threads.last"}]}}}}, {"id": 10000000000555, "_type": "0", "visualization.name": "Task Performance", "visualization.description": "MySQL Task Performance", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.open.tables", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.questions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.slow.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.table.lock.waits"}, {"type": "metric", "data.point": "mysql.table.immediate.locks"}, {"type": "metric", "data.point": "mysql.open.tables"}, {"type": "metric", "data.point": "mysql.questions"}, {"type": "metric", "data.point": "mysql.slow.queries"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "mysql.open.tables", "icon": {"name": "chart-line", "placement": "prefix"}}, "header": {"title": "Task Performance", "style": {"font.size": "medium"}, "data.points": [{"label": "Open Tables", "value": "mysql.open.tables.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Questions", "value": "mysql.questions.last"}, {"label": "Slow Queries", "value": "mysql.slow.queries.last"}]}}}}, {"id": 10000000000556, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON><PERSON>", "visualization.description": "MySQL Cached Thread", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.cached.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.query.cache.hits"}, {"type": "metric", "data.point": "mysql.query.cache.inserts"}, {"type": "metric", "data.point": "mysql.query.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "mysql.cached.threads"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "color.data.point": "mysql.cached.threads", "font.size": "medium", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "mysql.cached.threads.last"}]}}}}, {"id": 10000000000557, "_type": "0", "visualization.name": "Key Efficiency", "visualization.description": "MySQL Key Efficiency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.key.buffer.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.key.write.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.key.read.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.key.used.blocks"}, {"type": "metric", "data.point": "mysql.key.hit.ratio.percent"}, {"type": "metric", "data.point": "mysql.key.reads.per.sec"}, {"type": "metric", "data.point": "mysql.key.writes.per.sec"}, {"type": "metric", "data.point": "mysql.key.buffer.size.bytes"}, {"type": "metric", "data.point": "mysql.key.write.requests"}, {"type": "metric", "data.point": "mysql.key.read.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "mysql.key.buffer.size.bytes", "icon": {"name": "key", "placement": "prefix"}}, "header": {"title": "Key Efficiency", "style": {"font.size": "medium"}, "data.points": [{"label": "Key Buffer <PERSON>", "value": "mysql.key.buffer.size.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Write", "value": "mysql.key.write.requests.last"}, {"label": "Read", "value": "mysql.key.read.requests.last"}]}}}}, {"id": 10000000000558, "_type": "0", "visualization.name": "MySQL InnoDB Buffer Pool", "visualization.description": "MySQL InnoDB Buffer Pool", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.buffer.pool.write.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.read.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "mysql.innodb.data.reads.per.sec"}, {"type": "metric", "data.point": "mysql.innodb.data.writes.per.sec"}, {"type": "metric", "data.point": "mysql.innodb.data.fsyncs.per.sec"}, {"type": "metric", "data.point": "mysql.innodb.log.writes.per.sec"}, {"type": "metric", "data.point": "mysql.innodb.buffer.pool.write.requests.per.sec"}, {"type": "metric", "data.point": "mysql.innodb.buffer.pool.read.requests.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "mysql.innodb.buffer.pool.write.requests.per.sec", "icon": {"name": "inno-db", "placement": "prefix"}}, "header": {"title": "InnoDB Buffer Pool", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Write", "value": "mysql.innodb.buffer.pool.write.requests.per.sec.last"}, {"label": "Read", "value": "mysql.innodb.buffer.pool.read.requests.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000559, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000560, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000561, "_type": "0", "visualization.name": "Query Rates", "visualization.description": "MySQL Query Rates", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.update.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.delete.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.insert.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.select.commands.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000562, "_type": "0", "visualization.name": "Operational Throughput", "visualization.description": "MySQL Operational Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.data.fsyncs", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000563, "_type": "0", "visualization.name": "Connection", "visualization.description": "MySQL Connection", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.aborted.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.aborted.clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000564, "_type": "0", "visualization.name": "Table Locks", "visualization.description": "MySQL Table Locks", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.table.lock.waits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.table.immediate.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000565, "_type": "0", "visualization.name": "MySQL Select Statement Scans", "visualization.description": "MySQL Select Statement Scans", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.select.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.select.ranges.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.select.range.checks.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.select.full.joins.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000566, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "MySQL Thread", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.created.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.running.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.cached.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000567, "_type": "0", "visualization.name": "Table Sorts", "visualization.description": "MySQL Table Sorts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.sort.merge.passes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.sort.ranges.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000568, "_type": "0", "visualization.name": "Task Performance", "visualization.description": "MySQL Task Performance Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.questions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.slow.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.open.tables", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000569, "_type": "0", "visualization.name": "Temp Objects", "visualization.description": "MySQL Temp Objects", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.created.temp.tables.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.created.temp.disk.tables.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.created.temp.files.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000570, "_type": "0", "visualization.name": "Row Statistics", "visualization.description": "MySQL Row Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.deleted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.inserted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.updated.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000571, "_type": "0", "visualization.name": "Process Detail", "visualization.description": "MySQL Process Detail", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.process.host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.db", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.info", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.command", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.process.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.db.last", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.info.last", "title": "Info", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.user.last", "title": "User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.command.last", "title": "Command", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.process.time.ms.last", "title": "Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000572, "_type": "0", "visualization.name": "Row Operations", "visualization.description": "MySQL Row Operations", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.read.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.deleted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.inserted.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.update.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000573, "_type": "0", "visualization.name": "Row Lock", "visualization.description": "MySQL Row Lock", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.row.lock.waits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.row.lock.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000574, "_type": "0", "visualization.name": "Buffer Pool I/O", "visualization.description": "MySQL Buffer Pool I/O", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.buffer.pool.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000575, "_type": "0", "visualization.name": "Database I/O", "visualization.description": "MySQL Database I/O", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.data.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.data.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.data.fsyncs.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.log.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000576, "_type": "0", "visualization.name": "Buffer Pool Requests", "visualization.description": "MySQL Buffer Pool Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.buffer.pool.read.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.write.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000577, "_type": "0", "visualization.name": "Buffer Pool Size", "visualization.description": "MySQL Buffer Pool Size", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.innodb.buffer.pool.pages.data", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.dirty.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.free.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.innodb.buffer.pool.misc.pages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000578, "_type": "0", "visualization.name": "Index Statistics", "visualization.description": "MySQL Index Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.index.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.index.read.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.index.fetches", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mysql.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.index.name.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.index.read.rows.last", "title": "Rows Read", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.index.fetches.last", "title": "Fetches", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000579, "_type": "0", "visualization.name": "Unused Indexes", "visualization.description": "MySQL Unused Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "mysql.unused.index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.unused.index.database.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.unused.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "mysql.unused.index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "mysql.unused.index.database.name.last", "title": "Database Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.unused.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.unused.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "mysql.unused.index.size.bytes.last", "title": "Index Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]