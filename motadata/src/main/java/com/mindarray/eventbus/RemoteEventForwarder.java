/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventbus;


import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.time.Duration;
import java.util.concurrent.locks.LockSupport;

import static com.mindarray.eventbus.EventBusConstants.EVENT_HA_OBSERVER;
import static com.mindarray.eventbus.EventBusConstants.EVENT_REMOTE;

/**
 * The RemoteEventForwarder class forwards events to remote components using ZeroMQ's PUSH socket.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Connecting to remote endpoints for event forwarding</li>
 *   <li>Encrypting and compressing messages before transmission</li>
 *   <li>Formatting messages with appropriate topic headers</li>
 *   <li>Managing connection lifecycle and error handling</li>
 *   <li>Configuring high-water marks to handle back-pressure</li>
 * </ul>
 * <p>
 * The RemoteEventForwarder connects to a specified host and port, and forwards events received
 * from the Vert.x event bus to that endpoint. It adapts its behavior based on the bootstrap type
 * (APP, AGENT, COLLECTOR, MANAGER, etc.) to handle different event types and routing requirements.
 * <p>
 * The class uses ZeroMQ's PUSH socket type, which implements the producer side of the push-pull
 * pattern. This pattern allows for load-balanced message distribution, where messages are
 * distributed among all available consumers.
 * <p>
 * Example usage:
 * <pre>
 * // Create and deploy a RemoteEventForwarder
 * RemoteEventForwarder forwarder = new RemoteEventForwarder(5555, "remote-host");
 * vertx.deployVerticle(forwarder);
 *
 * // Send an event to be forwarded
 * JsonObject event = new JsonObject()
 *     .put("type", "metric-data")
 *     .put("value", 75.5)
 *     .put("timestamp", System.currentTimeMillis());
 *
 * vertx.eventBus().send("EVENT_REMOTE", event);
 * </pre>
 */
public class RemoteEventForwarder extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(RemoteEventForwarder.class, GlobalConstants.MOTADATA_EVENT_BUS, "Remote Event Forwarder");

    private final ZMQ.Socket forwarder;

    private final int forwarderPort;
    private final String forwarderHost;

    public RemoteEventForwarder(int port)
    {
        forwarder = Bootstrap.zcontext().socket(SocketType.PUSH);

        forwarderPort = port;

        forwarderHost = CommonUtil.getRemoteEventPublisher();
    }

    public RemoteEventForwarder(int port, String host)
    {
        forwarder = Bootstrap.zcontext().socket(SocketType.PUSH);

        forwarderPort = port;

        forwarderHost = host;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
            {
                var cipherUtil = new CipherUtil();

                forwarder.setHWM(MotadataConfigUtil.getDatastoreEventQueueSize());

                forwarder.setSndHWM(MotadataConfigUtil.getDatastoreEventQueueSize());

                LOGGER.info(String.format("datastore queue size %s ", MotadataConfigUtil.getDatastoreEventQueueSize()));

                forwarder.setSendTimeOut(0);

                forwarder.setTCPKeepAlive(1);

                forwarder.setLinger(0);

                forwarder.connect("tcp://" + forwarderHost + ":" + forwarderPort);

                LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

                LOGGER.info(String.format("event publisher connected to %s successfully...", forwarderHost + ":" + forwarderPort));

                if (MotadataConfigUtil.getMotadataObserverEventSubscriberPort() == forwarderPort)
                {
                    vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_OBSERVER, message ->
                    {
                        try
                        {
                            if (message.body() != null && !message.body().isEmpty())
                            {
                                forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.MOTADATA_OBSERVER_TOPIC.length())).appendString(EventBusConstants.MOTADATA_OBSERVER_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
                else
                {
                    vertx.eventBus().<JsonObject>localConsumer(EVENT_REMOTE, message ->
                    {
                        try
                        {
                            if (message.body() != null && !message.body().isEmpty())
                            {
                                forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC.length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }

                promise.complete();
            }
            else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR ||
                    Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR ||
                    Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_PROCESSOR ||
                    Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
            {
                var cipherUtil = new CipherUtil();

                forwarder.setSndHWM(MotadataConfigUtil.getEventBacklogSize());

                forwarder.setHWM(MotadataConfigUtil.getEventBacklogSize());

                forwarder.setSendTimeOut(0);

                forwarder.connect("tcp://" + CommonUtil.getRemoteEventPublisher() + ":" + forwarderPort);

                LockSupport.parkNanos(Duration.ofMillis(100).toNanos());  // Eliminate slow subscriber problem

                LOGGER.info(String.format("event publisher connected to %s successfully...", CommonUtil.getRemoteEventSubscriber() + ":" + forwarderPort));

                vertx.eventBus().<JsonObject>localConsumer(EVENT_REMOTE, message ->
                {
                    try
                    {
                        var event = message.body(); // for HA we will pass ha topic so send message on that topic itself

                        if (CommonUtil.isNotNullOrEmpty(event.getString(EventBusConstants.EVENT_TOPIC)))
                        {
                            forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(event.getString(EventBusConstants.EVENT_TOPIC).length())).appendString(event.getString(EventBusConstants.EVENT_TOPIC)).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                        }
                        else
                        {
                            forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort((EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + EventBusConstants.EVENT_TOPIC_DELIMITER).length())).appendString(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC + EventBusConstants.EVENT_TOPIC_DELIMITER).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                }).exceptionHandler(LOGGER::error);

                promise.complete();

            }
            else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.AGENT)
            {
                var cipherUtil = new CipherUtil();

                forwarder.setHWM(AgentConfigUtil.getAgentMaxEventBacklogQueueSize());

                forwarder.setSndHWM(AgentConfigUtil.getAgentMaxEventBacklogQueueSize());

                forwarder.setSendTimeOut(0);

                forwarder.setTCPKeepAlive(1);

                forwarder.setLinger(0);

                forwarder.connect("tcp://" + CommonUtil.getRemoteEventPublisher() + ":" + forwarderPort);

                LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

                LOGGER.info(String.format("event publisher connected to %s successfully...", CommonUtil.getRemoteEventPublisher() + ":" + forwarderPort));

                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_AGENT, message ->
                {
                    try
                    {
                        if (message.body() != null && !message.body().isEmpty())
                        {
                            forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(message.body().encode()))).getBytes());
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });

                promise.complete();
            }
            else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.MANAGER)
            {
                forwarder.setSndHWM(500);

                forwarder.setHWM(500);

                forwarder.setSendTimeOut(0);

                forwarder.setLinger(0);

                forwarder.connect("tcp://" + CommonUtil.getRemoteEventPublisher() + ":" + forwarderPort);

                LockSupport.parkNanos(Duration.ofMillis(100).toNanos());

                LOGGER.info(String.format("event publisher connected to %s successfully...", CommonUtil.getRemoteEventPublisher() + ":" + forwarderPort));

                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_MOTADATA_MANAGER, message ->
                {
                    try
                    {
                        if (message.body() != null && !message.body().isEmpty())
                        {
                            forwarder.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.MOTADATA_MANAGER_TOPIC.length())).appendString(EventBusConstants.MOTADATA_MANAGER_TOPIC).appendString(message.body().encode()).getBytes());
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });

                promise.complete();
            }
            else
            {
                promise.fail(String.format("failed to start %s, reason: invalid boot sequence...", this.getClass().getSimpleName()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            forwarder.close();

            promise.complete();
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }
    }
}
