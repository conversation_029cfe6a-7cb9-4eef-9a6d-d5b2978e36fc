/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  24-Feb-2025		Chandresh		MOTADATA-3680: Added RUNBOOK_PLUGIN_INSTANCES constant for handling multiple instances in runbooks
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities
 *  30-Jul-2025		Vismit		    MOTADATA-6312 Refactored regarding modifications to the AIOps scheduling workflow
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;

/**
 * API handler for Runbook Plugin operations.
 * <p>
 * This class provides REST API endpoints for managing runbook plugins, including:
 * <ul>
 *   <li>Creating, reading, updating, and deleting runbook plugins</li>
 *   <li>Assigning runbooks to objects and groups</li>
 *   <li>Unassigning runbooks from objects and groups</li>
 *   <li>Retrieving references to runbooks from other entities</li>
 *   <li>Managing runbook dependencies and relationships</li>
 * </ul>
 * <p>
 * Runbook plugins are automation scripts that can be executed against various
 * entities in the system, such as objects, groups, or event sources.
 */
public class RunbookPlugin extends AbstractAPI
{
    public static final String RUNBOOK_PLUGIN_NAME = "runbook.plugin.name";
    public static final String RUNBOOK_PLUGIN_TYPE = "runbook.plugin.type";
    public static final String RUNBOOK_PLUGIN_VARIABLES = "runbook.plugin.variables";
    public static final String RUNBOOK_PLUGIN_CONTEXT = "runbook.plugin.context";
    public static final String RUNBOOK_PLUGIN_CATEGORY = "runbook.plugin.category";
    public static final String RUNBOOK_PLUGIN_ENTITY_TYPE = "runbook.plugin.entity.type";
    public static final String RUNBOOK_PLUGIN_ENTITIES = "runbook.plugin.entities";
    public static final String RUNBOOK_PLUGIN_OBJECTS = "runbook.plugin.objects";
    public static final String RUNBOOK_SCHEDULER = "runbook.scheduler";
    public static final String RUNBOOK_RUNNABLE = "runbook.runnable";
    public static final String RUNBOOK_PLUGIN_INSTANCES = "runbook.plugin.instances";
    public static final String RUNBOOK_PLUGIN_CREDENTIAL_PROFILE = "runbook.plugin.credential.profile";
    public static final String RUNBOOK_PLUGIN_LAST_EXECUTION_TIME = "runbook.plugin.last.execution.time";
    public static final String RUNBOOK_PLUGIN_DESCRIPTION = "runbook.plugin.description";
    public static final String RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS = "runbook.plugin.notification.email.recipients";
    public static final String RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT = "runbook.plugin.notification.email.subject";

    private static final Logger LOGGER = new Logger(RunbookPlugin.class, MOTADATA_API, "Runbook Plugin API");

    public RunbookPlugin()
    {
        super("runbook-plugins", RunbookPluginConfigStore.getStore(), new Logger(RunbookPlugin.class, MOTADATA_API, "Runbook Plugin API"));
    }

    /**
     * Initializes the API routes for runbook plugin operations.
     * <p>
     * This method sets up the following endpoints:
     * <ul>
     *   <li>GET /runbook-plugins/:id/references - Get references to a runbook plugin</li>
     *   <li>PUT /runbook-plugins/:id/unassign - Unassign a runbook plugin from entities</li>
     *   <li>PUT /runbook-plugins/:id/assign - Assign a runbook plugin to entities</li>
     * </ul>
     * <p>
     * It also initializes the standard CRUD endpoints through the parent class.
     *
     * @param router The Vert.x router to register the endpoints with
     */
    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        if (!requestBody.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES))
        {
            requestBody.put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(RunbookPlugin.RUNBOOK_PLUGIN_VARIABLES));
        }

        return Future.succeededFuture(requestBody);
    }

    /**
     * Handles the assignment of a runbook plugin to entities.
     * <p>
     * This method:
     * <ul>
     *   <li>Retrieves the runbook plugin by ID</li>
     *   <li>Extracts and merges the runbook context</li>
     *   <li>Gets the current list of assigned entities</li>
     *   <li>Adds new entities that aren't already assigned</li>
     *   <li>Updates the runbook plugin with the new entity list</li>
     * </ul>
     * <p>
     * The method responds with a success message if the assignment is successful,
     * or an error message if the schema file is not found or an exception occurs.
     *
     * @param routingContext The routing context containing the request parameters and body
     */
    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = RunbookPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                if (item.containsKey(RUNBOOK_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(RUNBOOK_PLUGIN_CONTEXT));

                    item.remove(RUNBOOK_PLUGIN_CONTEXT);
                }

                var entities = item.containsKey(RUNBOOK_PLUGIN_ENTITIES) ? item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES) : new JsonArray(new ArrayList<>(1));

                // if entity.type is monitor than monitor id else group id
                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                var ids = new JsonArray();

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (!entities.contains(id))
                    {
                        ids.add(id);
                    }
                }

                if (!ids.isEmpty())
                {
                    updateItem(item, entities.addAll(ids), routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    /**
     * Handles the unassignment of a runbook plugin from entities.
     * <p>
     * This method:
     * <ul>
     *   <li>Retrieves the runbook plugin by ID</li>
     *   <li>Extracts and merges the runbook context</li>
     *   <li>Gets the current list of assigned entities</li>
     *   <li>Removes the specified entities from the list</li>
     *   <li>Updates the runbook plugin with the new entity list</li>
     * </ul>
     * <p>
     * The method responds with a success message if the unassignment is successful,
     * or an error message if the schema file is not found or an exception occurs.
     *
     * @param routingContext The routing context containing the request parameters and body
     */
    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var item = RunbookPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                if (item.containsKey(RUNBOOK_PLUGIN_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(RUNBOOK_PLUGIN_CONTEXT));

                    item.remove(RUNBOOK_PLUGIN_CONTEXT);
                }

                var entities = item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES);

                var changed = false;

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (entities.contains(id))
                    {
                        changed = true;

                        entities.remove(id);
                    }
                }

                if (changed)
                {
                    updateItem(item, entities, routingContext).onComplete(result -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    /**
     * Updates a runbook plugin's entity assignments in the database and cache.
     * <p>
     * This method:
     * <ul>
     *   <li>Updates the runbook plugin in the database with the new entity list</li>
     *   <li>Updates the runbook plugin in the cache to reflect the changes</li>
     * </ul>
     *
     * @param item           The runbook plugin to update
     * @param entities       The new list of assigned entities
     * @param routingContext The routing context containing user and request information
     * @return A Future that completes when the update is done
     */
    private Future<Void> updateItem(JsonObject item, JsonArray entities, RoutingContext routingContext)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                new JsonObject().put(RUNBOOK_PLUGIN_ENTITIES, entities),
                routingContext.user().principal().getString(USER_NAME) != null ? routingContext.user().principal().getString(USER_NAME) : DEFAULT_USER,
                routingContext.request().remoteAddress().host() != null ? routingContext.request().remoteAddress().host() : SYSTEM_REMOTE_ADDRESS,
                result -> RunbookPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult -> promise.complete()));

        return promise.future();
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = RunbookPluginConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT,
                                item.put(NMSConstants.STATE, RunbookCacheStore.getStore().getItem(id) != NOT_AVAILABLE ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING)
                                        .put(RUNBOOK_SCHEDULER, SchedulerConfigStore.getStore().containsObject(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, id, JobScheduler.JobType.RUNBOOK.getName()) ? YES : NO)));

            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        var schedulers = SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, RunbookPluginConfigStore.getStore().getIds(), JobScheduler.JobType.RUNBOOK.getName());

        var items = new JsonArray();

        for (var index = 0; index < entities.size(); index++)
        {
            var item = entities.getJsonObject(index);

            item.put(RUNBOOK_SCHEDULER, schedulers.contains(item.getLong(GlobalConstants.ID)) ? GlobalConstants.YES : GlobalConstants.NO).
                    put(NMSConstants.STATE, RunbookCacheStore.getStore().getItem(item.getLong(ID)) != NOT_AVAILABLE ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

            item.put(RUNBOOK_RUNNABLE, item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES) != null && !item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES).isEmpty());

            items.add(item);
        }

        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        JobScheduler.deleteSchedulers(entity, JobScheduler.JobType.RUNBOOK.getName());

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var item = RunbookPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

            if (item != null && item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES) != null && !item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES).isEmpty())
            {
                if (item.getString(RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                {
                    response.put(APIConstants.Entity.OBJECT.getName(), ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES))));
                }
                else if (item.getString(RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                {
                    response.put(APIConstants.Entity.GROUP.getName(), GroupConfigStore.getStore().getItems(GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES), userGroups)));
                }
            }

            var items = MetricPolicyConfigStore.getStore().getItems();

            var metricPolicies = new JsonArray();

            for (var index = 0; index < items.size(); index++)
            {
                var policy = items.getJsonObject(index);

                if (item != null && isRunbookInUse(policy, item.getLong(ID)))
                {
                    metricPolicies.add(policy);
                }
            }

            if (!metricPolicies.isEmpty())
            {
                response.put(APIConstants.Entity.METRIC_POLICY.getName(), metricPolicies);
            }

            var eventPolicies = new JsonArray();

            items = EventPolicyConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var policy = items.getJsonObject(index);

                if (item != null && isRunbookInUse(policy, item.getLong(ID)))
                {
                    eventPolicies.add(policy);
                }
            }

            if (!eventPolicies.isEmpty())
            {
                response.put(APIConstants.Entity.EVENT_POLICY.getName(), eventPolicies);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var items = RunbookPluginConfigStore.getStore().getItems();

            var userGroups = new JsonArray();

            if (response.containsKey(USER_ID))
            {
                userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));
            }

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var id = CommonUtil.getString(item.getLong(ID));

                var references = 0;


                if (item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES) != null && !item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES).isEmpty())
                {
                    if (item.getString(RUNBOOK_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                    {
                        references = ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES)).size();
                    }

                    else
                    {
                        references = GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(RUNBOOK_PLUGIN_ENTITIES), userGroups).size();
                    }
                }
                var metricPolicies = MetricPolicyConfigStore.getStore().getItems();

                for (var i = 0; i < metricPolicies.size(); i++)
                {
                    var policy = metricPolicies.getJsonObject(i);

                    if (isRunbookInUse(policy, item.getLong(ID)))
                    {
                        references++;
                    }
                }

                var eventPolicies = EventPolicyConfigStore.getStore().getItems();

                for (var i = 0; i < eventPolicies.size(); i++)
                {
                    var policy = eventPolicies.getJsonObject(i);

                    if (isRunbookInUse(policy, item.getLong(ID)))
                    {
                        references++;
                    }
                }

                response.put(id, response.containsKey(id) ? response.getInteger(id) + references : references);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    private boolean isRunbookInUse(JsonObject policy, long id)
    {
        var status = false;

        try
        {
            if (NO.equalsIgnoreCase(policy.getString(PolicyEngineConstants.POLICY_ARCHIVED, NO))
                    && policy.containsKey(PolicyEngineConstants.POLICY_ACTIONS)
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).isEmpty()
                    && policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName())
                    && !policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName()).isEmpty())
            {
                var runbook = policy.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName());

                for (var severity : Severity.values())
                {
                    if (runbook.containsKey(severity.name()) && !runbook.getJsonArray(severity.name()).isEmpty())
                    {
                        var items = runbook.getJsonArray(severity.name());

                        for (var index = 0; index < items.size(); index++)
                        {
                            if (items.getJsonObject(index).containsKey(ID) && items.getJsonObject(index).getLong(ID).equals(id))
                            {
                                status = true;

                                break;
                            }
                        }
                    }

                    if (status)
                    {
                        break;
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return status;
    }
}
