/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     4-Mar-2025      Bharat      MOTADATA-4740: Two factor authentication 2FA | Initial Version
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.store.MailServerConfigStore;
import com.mindarray.store.TwoAuthenticationConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class TwoFactorAuthentication extends AbstractAPI
{
    public static final String TWO_FACTOR_AUTHENTICATION_ENABLE = "two.factor.authentication.enable";

    public static final String TWO_FACTOR_AUTHENTICATION_MODE = "two.factor.authentication.mode";

    public TwoFactorAuthentication()
    {
        super("two-factor-authentication", TwoAuthenticationConfigStore.getStore(), new Logger(TwoFactorAuthentication.class, MOTADATA_API, "Two Factor Authentication API"));
    }

    @Override
    public void init(Router router)
    {
        super.init(router, Set.of(REQUEST_CREATE, REQUEST_DELETE));
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var item = UserConfigStore.getStore().getItem(routingContext.user().principal().getLong(ID));

        if (!MailServerConfigStore.getStore().isConfigured())
        {
            send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                    .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.MAIL_SEVER_NOT_CONFIGURED));

            promise.fail(ErrorMessageConstants.MAIL_SERVICE_FAILED);
        }
        else if (YES.equalsIgnoreCase(routingContext.body().asJsonObject().getString(TWO_FACTOR_AUTHENTICATION_ENABLE)) && (!item.containsKey(User.USER_EMAIL) || item.getString(User.USER_EMAIL).trim().isEmpty()))
        {
            send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                    .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, (ErrorMessageConstants.USER_EMAIL_NOT_SET)));

            promise.fail(ErrorMessageConstants.USER_EMAIL_NOT_SET);
        }
        else
        {
            promise.complete(routingContext.body().asJsonObject());
        }

        return promise.future();
    }

    public enum AuthenticationType
    {
        EMAIL("Email"),
        AUTHENTICATOR_APP("Authenticator App");

        private final String name;

        AuthenticationType(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }
    }
}
