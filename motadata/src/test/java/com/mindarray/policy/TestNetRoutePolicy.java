/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.policy;

import com.mindarray.*;
import com.mindarray.api.APIConstants;
import com.mindarray.api.NetRoute;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.NETROUTE_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.NETROUTE_POLICY_API_ENDPOINT;
import static com.mindarray.aiops.AIOpsConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.Entity.NETROUTE;
import static com.mindarray.api.APIConstants.Entity.NETROUTE_POLICY;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.NetRoute.NETROUTE_DESTINATION;
import static com.mindarray.api.NetRoute.NETROUTE_TAGS;
import static com.mindarray.db.DBConstants.FIELD_TYPE;
import static com.mindarray.db.DBConstants.TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Timeout(120 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestNetRoutePolicy
{
    private static final Logger LOGGER = new Logger(TestNetRoutePolicy.class, MOTADATA_POLICY, "Test NetRoute Policy");

    private static final JsonObject IDS = new JsonObject();

    private static final JsonObject PARAMETERS = new JsonObject();
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "test-parameters.json");

            PARAMETERS.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

            var runbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.WHOIS_LOOKUP.getName());

            if (runbook != null)
            {
                runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT).put(TIMEOUT, 10);

                RunbookPluginConfigStore.getStore().addItem(runbook.getLong(ID), runbook);
            }

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateNetRoutePolicySourceToDestination(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"test case\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"entity.type\": \"NetRoute\", \"metric\": \"netroute.latency.ms\", \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"10\" } }, \"policy.evaluation.type\": \"Source-to-destination\", \"entities\": [], \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.actions\": { \"Notification\": { \"Email\": { \"CRITICAL\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\", \"index\": 0, \"severity\": \"CRITICAL\" } ] }, \"channels\": {} }, \"Renotification\": { \"renotify.acknowledged\": \"yes\", \"CRITICAL\": { \"timer.seconds\": 5, \"recipients\": [ { \"recipient\": \"<EMAIL>\", \"type\": \"email\" } ] } }, \"Integration\": {} }, \"policy.renotify\": \"yes\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency." + System.currentTimeMillis());

        context.put(POLICY_CONTEXT, context.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray().add(NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******").getLong(ID))));

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateNetRoutePolicySourceToDestination2(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"test source to destination\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"metric\": \"netroute.packet.lost.percent\", \"policy.severity\": { \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"10\" } }, \"policy.evaluation.type\": \"Source-to-destination\", \"entities\": [ ], \"filters\": { \"data.filter\": { } } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.actions\": { \"Notification\": { \"Email\": { }, \"channels\": { } }, \"Integration\": { } }, \"policy.renotify\": \"no\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".packet.lost." + System.currentTimeMillis());

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".packet.lost.", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateNetRoutePolicyHopByHop(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"test hop to hop\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"metric\": \"netroute.latency.ms\", \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"10\" } }, \"policy.evaluation.type\": \"Hop-by-hop\", \"entities\": [ ], \"filters\": { \"data.filter\": { } } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.severity\" : \"CRITICAL\", \"policy.actions\": { \"Notification\": { \"Email\": { }, \"channels\": { } }, \"Integration\": { } }, \"policy.renotify\": \"no\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".latency." + System.currentTimeMillis());

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".latency.", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateNetRoutePolicyHopByHop2(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"test hop to hop\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"metric\": \"netroute.packet.lost.percent\", \"policy.severity\": { \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"10\" } }, \"policy.evaluation.type\": \"Hop-by-hop\", \"entities\": [ ], \"filters\": { \"data.filter\": { } } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.severity\" : \"MAJOR\", \"policy.actions\": { \"Notification\": { \"Email\": { }, \"channels\": { } }, \"Integration\": { } }, \"policy.renotify\": \"no\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".packet.lost." + System.currentTimeMillis());

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".packet.lost.", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCreateNetRouteAvailabilityPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"test availability\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"metric\": \"status\", \"policy.severity\": {}, \"policy.evaluation.type\": \"Source-to-destination\", \"entities\": [], \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.actions\": { \"Notification\": { \"Email\": {}, \"channels\": {} }, \"Integration\": {} }, \"policy.renotify\": \"no\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability." + System.currentTimeMillis());

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testInspectNetRouteAvailabilityPolicySourceToDestination(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var timestamp = DateTimeUtil.currentSeconds();

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context);

        testContext.awaitCompletion(7, TimeUnit.SECONDS);

        var items = NetRoutePolicyCacheStore.getStore().getItems(new JsonArray().add(item.getLong(ID)));

        Assertions.assertNotNull(items);

        Assertions.assertFalse(items.isEmpty());

        var key = item.getLong(ID) + SEPARATOR + STATUS + SEPARATOR + NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName();

        var value = items.get(key);

        Assertions.assertNotNull(value);

        Assertions.assertEquals(STATUS, value.getString(METRIC));

        Assertions.assertEquals(IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability."), value.getLong(ID));

        Assertions.assertEquals(item.getLong(ID), value.getLong(ENTITY_ID));

        Assertions.assertEquals(Severity.DOWN.name(), value.getString(SEVERITY));

        assertPolicyDuration(context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability."))
                .put(SEVERITY, Severity.DOWN.name()).put(METRIC, STATUS), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testInspectNetRouteLatencyPolicySourceToDestination(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var timestamp = DateTimeUtil.currentSeconds();

        var policyId = IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency");

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context);

        testContext.awaitCompletion(7, TimeUnit.SECONDS);

        var items = NetRoutePolicyCacheStore.getStore().getItems(new JsonArray().add(item.getLong(ID)));

        Assertions.assertNotNull(items);

        Assertions.assertFalse(items.isEmpty());

        var entity = items.get(item.getLong(ID) + SEPARATOR + NetRouteConstants.NETROUTE_LATENCY + SEPARATOR + NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName());

        Assertions.assertNotNull(entity);

        Assertions.assertEquals(NetRouteConstants.NETROUTE_LATENCY, entity.getString(METRIC));

        Assertions.assertEquals(policyId, entity.getLong(ID));

        Assertions.assertEquals(item.getLong(ID), entity.getLong(ENTITY_ID));

        Assertions.assertEquals(Severity.CRITICAL.name(), entity.getString(SEVERITY));

        assertPolicyDuration(context.put(POLICY_ID, policyId).put(SEVERITY, Severity.CRITICAL.name()).put(METRIC, NetRouteConstants.NETROUTE_LATENCY), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testInspectNetRoutePacketLostPolicySourceToDestination(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {

            var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

            var timestamp = DateTimeUtil.currentSeconds();

            var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

            Assertions.assertNotNull(item);

            context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context);

            testContext.awaitCompletion(7, TimeUnit.SECONDS);

            var items = NetRoutePolicyCacheStore.getStore().getItems(new JsonArray().add(item.getLong(ID)));

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            var entity = items.get(item.getLong(ID) + SEPARATOR + NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT + SEPARATOR + NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName());

            Assertions.assertNotNull(entity);

            Assertions.assertEquals(NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT, entity.getString(METRIC));

            Assertions.assertEquals(IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".packet.lost."), entity.getLong(ID));

            Assertions.assertEquals(item.getLong(ID), entity.getLong(ENTITY_ID));

            Assertions.assertEquals(Severity.MAJOR.name(), entity.getString(SEVERITY));

            assertPolicyDuration(context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".packet.lost."))
                    .put(SEVERITY, Severity.MAJOR.name()).put(METRIC, NetRouteConstants.NETROUTE_PACKET_LOST_PERCENT), testContext);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testInspectNetRouteLatencyPolicyHopByHop(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        try
        {
            var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

            var timestamp = DateTimeUtil.currentSeconds();

            var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

            Assertions.assertNotNull(item);

            context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

            var id = DUMMY_ID;

            for (var entry : AgentConfigStore.getStore().flatMap().entrySet())
            {
                var object = ObjectConfigStore.getStore().getItemByAgentId(entry.getKey());

                if (object != null)
                {
                    id = entry.getKey();

                    break;
                }
            }

            LOGGER.info(String.format("netroute source is : %s and agent item : %s ", id, AgentConfigStore.getStore().getItem(id).encode()));

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context.put(NetRoute.NETROUTE_SOURCE, id));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            assertPolicyTriggerTicks(context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".latency.")), testContext);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testInspectNetRoutePacketLostPolicyHopByHop(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        try
        {
            var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

            var timestamp = DateTimeUtil.currentSeconds();

            var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

            Assertions.assertNotNull(item);

            context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

            var id = DUMMY_ID;

            for (var entry : AgentConfigStore.getStore().flatMap().entrySet())
            {
                var object = ObjectConfigStore.getStore().getItemByAgentId(entry.getKey());

                if (object != null)
                {
                    id = entry.getKey();

                    break;
                }
            }

            LOGGER.info(String.format("netroute source is : %s and agent item : %s ", id, AgentConfigStore.getStore().getItem(id).encode()));

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context.put(NetRoute.NETROUTE_SOURCE, id));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            assertPolicyTriggerTicks(context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".packet.lost.")), testContext);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateNetRoutePolicySourceToDestinationHavingTag(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{ \"policy.name\": \"test source to destination tag\", \"policy.type\": \"NetRoute\", \"policy.context\": { \"entity.type\": \"Tag\", \"metric\": \"netroute.latency.ms\", \"policy.severity\": { \"MAJOR\": { \"policy.condition\": \">\", \"policy.threshold\": \"0\" } }, \"policy.evaluation.type\": \"Source-to-destination\", \"entities\": [ \"test netroute\" ], \"filters\": { \"data.filter\": {} } }, \"policy.title\": \"$$$severity$$$ - $$$path.name$$$\", \"policy.message\": \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on path $$$netroute.name$$$\", \"policy.actions\": { \"Notification\": { \"Email\": {}, \"channels\": {} }, \"Integration\": {} }, \"policy.renotify\": \"no\" }");

        context.put(POLICY_NAME, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency.tag." + System.currentTimeMillis());

        TestAPIUtil.post(NETROUTE_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertCreateEntityTestResult(NetRoutePolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.NETROUTE_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    IDS.put(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency.tag.", response.bodyAsJsonObject().getLong(GlobalConstants.ID));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testInspectNetRoutePolicySourceToDestinationHavingTag(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var policyId = IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency.tag.");

        var timestamp = DateTimeUtil.currentSeconds();

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        context.mergeIn(item).put(EVENT_TIMESTAMP, timestamp);

        context.put(NETROUTE_TAGS, TagConfigStore.getStore().getIdsByItems(new JsonArray().add("test netroute")));

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLL_RESPONSE, context);

        testContext.awaitCompletion(7, TimeUnit.SECONDS);

        var items = NetRoutePolicyCacheStore.getStore().getItems(new JsonArray().add(item.getLong(ID)));

        Assertions.assertNotNull(items);

        Assertions.assertFalse(items.isEmpty());

        var entity = items.get(item.getLong(ID) + SEPARATOR + NetRouteConstants.NETROUTE_LATENCY + SEPARATOR + NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName());

        Assertions.assertNotNull(entity);

        Assertions.assertEquals(NetRouteConstants.NETROUTE_LATENCY, entity.getString(METRIC));

        Assertions.assertEquals(policyId, entity.getLong(ID));

        Assertions.assertEquals(item.getLong(ID), entity.getLong(ENTITY_ID));

        Assertions.assertEquals(Severity.MAJOR.name(), entity.getString(SEVERITY));

        assertPolicyDuration(context.put(POLICY_ID, policyId).put(SEVERITY, Severity.MAJOR.name()).put(METRIC, NetRouteConstants.NETROUTE_LATENCY), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testGetNetRoutePolicyReferencesByNetRoute(VertxTestContext testContext)
    {
        TestAPIUtil.get(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonObject(RESULT).getJsonArray(APIConstants.Entity.NETROUTE.getName());

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    LOGGER.info(String.format("NetRoute policies references by monitor : %s ", items.encode()));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testGetNetRoutePolicyReferencesByTag(VertxTestContext testContext)
    {
        TestAPIUtil.get(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency.tag.") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonObject(RESULT).getJsonArray(APIConstants.Entity.NETROUTE.getName());

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    LOGGER.info(String.format("NetRoute policies references by tag : %s ", items.encode()));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testValidatePolicyAcknowledgement1(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        TestUtil.vertx().eventBus().send(UI_ACTION_POLICY_ACKNOWLEDGE, context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability."))
                .put(NetRoute.NETROUTE_ID, item.getLong(ID)));

        testContext.awaitCompletion(4, TimeUnit.SECONDS);

        var key = item.getLong(ID) + SEPARATOR + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.") + SEPARATOR + STATUS;

        var ack = NetRoutePolicyFlapDurationCacheStore.getStore().getAcknowledgment(key);

        Assertions.assertNotNull(ack);

        Assertions.assertEquals(YES, ack);

        getPolicyDuration().onComplete(result ->
        {
            var records = result.result();

            Assertions.assertNotNull(records);

            Assertions.assertTrue(records.containsKey(key));

            var record = records.getJsonObject(key);

            Assertions.assertNotNull(record);

            Assertions.assertTrue(record.containsKey(POLICY_ACKNOWLEDGE));

            Assertions.assertFalse(record.getString(POLICY_ACKNOWLEDGE).isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testValidatePolicyAcknowledgement2(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        TestUtil.vertx().eventBus().send(UI_ACTION_POLICY_ACKNOWLEDGE, context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.")).put(NetRoute.NETROUTE_ID, item.getLong(ID)));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var key = item.getLong(ID) + SEPARATOR + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.") + SEPARATOR + STATUS;

        var ack = NetRoutePolicyFlapDurationCacheStore.getStore().getAcknowledgment(key);

        Assertions.assertNotNull(ack);

        Assertions.assertEquals(NO, ack);

        getPolicyDuration().onComplete(result ->
        {
            var records = result.result();

            Assertions.assertNotNull(records);

            Assertions.assertTrue(records.containsKey(key));

            var record = records.getJsonObject(key);

            Assertions.assertNotNull(record);

            Assertions.assertFalse(record.containsKey(POLICY_ACKNOWLEDGE));

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testQueryNetRouteSeverity(VertxTestContext testContext)
    {
        var event = new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(UI_EVENT_UUID, "4ad1e808-2ce3-4994-8fc6-b598e54b2f16").put(TYPE, NETROUTE.getName());

        TestUtil.vertx().eventBus().send(EVENT_SEVERITY_QUERY, event);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(EVENT_SEVERITY_QUERY) && message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                LOGGER.info(String.format("%s event received Severity Query %s ", EVENT_SEVERITY_QUERY, context.encode()));

                assertNotNull(context);

                assertFalse(context.isEmpty());

                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testValidatePolicyCommentUpdateAction(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

        Assertions.assertNotNull(item);

        var note = "This is a note for testing netroute";

        TestUtil.vertx().eventBus().send(UI_ACTION_POLICY_COMMENT_UPDATE, context.put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability."))
                .put(POLICY_NOTE, note).put(NetRoute.NETROUTE_ID, item.getLong(ID)));

        testContext.awaitCompletion(4, TimeUnit.SECONDS);

        var key = item.getLong(ID) + SEPARATOR + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.") + SEPARATOR + STATUS;

        getPolicyDuration().onComplete(result ->
        {
            var records = result.result();

            Assertions.assertNotNull(records);

            Assertions.assertTrue(records.containsKey(key));

            var record = records.getJsonObject(key);

            Assertions.assertNotNull(record);

            Assertions.assertTrue(record.containsKey(POLICY_NOTE));

            Assertions.assertEquals(note, record.getString(POLICY_NOTE));

            testContext.completeNow();
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testClearPolicyManual(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var context = PARAMETERS.getJsonObject(testInfo.getTestMethod().get().getName()).copy();

            var policyId = IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".availability.");

            var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******");

            Assertions.assertNotNull(item);

            TestUtil.vertx().eventBus().send(UI_ACTION_POLICY_CLEAR, context.put(POLICY_EVALUATION_TYPE, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName())
                    .put(NetRoute.NETROUTE_ID, item.getLong(ID)).put(POLICY_ID, policyId));

            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            var items = NetRoutePolicyCacheStore.getStore().getItems(new JsonArray().add(item.getLong(ID)));

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            LOGGER.info(String.format("policy items : %s", items));

            var entity = items.get(item.getLong(ID) + SEPARATOR + STATUS + SEPARATOR + NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName());

            Assertions.assertNotNull(entity);

            LOGGER.info(String.format("policy entity : %s", entity.encodePrettily()));

            Assertions.assertEquals(STATUS, entity.getString(METRIC));

            Assertions.assertEquals(policyId, entity.getLong(ID));

            Assertions.assertEquals(item.getLong(ID), entity.getLong(ENTITY_ID));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testGetAllNetRoutePolicy(VertxTestContext testContext)
    {
        TestAPIUtil.get(NETROUTE_POLICY_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var items = body.getJsonArray(RESULT);

            Assertions.assertNotNull(items);

            Assertions.assertFalse(items.isEmpty());

            LOGGER.info(String.format("NetRoute policies : %s ", items.encode()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testGetNetRoutePolicyReferences(VertxTestContext testContext)
    {
        TestAPIUtil.get(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency") + "/references", testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

                    var items = body.getJsonObject(RESULT).getJsonArray(APIConstants.Entity.NETROUTE.getName());

                    Assertions.assertNotNull(items);

                    Assertions.assertFalse(items.isEmpty());

                    LOGGER.info(String.format("NetRoute policies references : %s ", items.encode()));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testSuppressPolicy(VertxTestContext testContext)
    {
        var id = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******").getLong(ID);

        var event = new JsonObject("{ \"action.type\": \"suppress\", \"alert.type\": 1, \"netroute.id\": 71752350899, \"policy.evaluation.type\": \"Source-to-destination\", \"session-id\": \"728f32da-d166-48b1-b169-baf74eeaa65e\", \"user.name\": \"admin\" }");

        event.put(NetRoute.NETROUTE_ID, id).put(PolicyEngineConstants.POLICY_SUPPRESSION_TIME, 10).put(SESSION_ID, TestUtil.getSessionId())
                .put(POLICY_EVALUATION_TYPE, NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()).put(POLICY_TYPE, NETROUTE.getName())
                .put(POLICY_ID, IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency"));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            LOGGER.info(String.format("event received for change notification %s", message.body().encodePrettily()));

            if (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.SUPPRESS_POLICY
                    && message.body().getString(POLICY_TYPE).equalsIgnoreCase(NETROUTE.getName()))
            {
                assertEquals(event.getLong(NetRoute.NETROUTE_ID), message.body().getLong(ENTITY_ID));

                assertEquals(event.getLong(POLICY_ID), message.body().getLong(POLICY_ID));

                messageConsumer.unregister(response -> testContext.completeNow());
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_POLICY_SUPPRESS, event);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testDisableNetRoutePolicy(VertxTestContext testContext)
    {
        TestAPIUtil.put(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency") + "/state", new JsonObject().put(POLICY_STATE, GlobalConstants.NO), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Disabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testEnableNetRoutePolicy(VertxTestContext testContext)
    {
        TestAPIUtil.put(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency") + "/state", new JsonObject().put(POLICY_STATE, YES), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.getString(MESSAGE).contains("Enabled successfully..."));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testUpdateNetRoutePolicy(VertxTestContext testContext)
    {
        var item = NetRoutePolicyConfigStore.getStore().getItem(IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency"), false);

        item.getJsonObject(POLICY_CONTEXT).remove(ENTITIES);

        TestAPIUtil.put(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName() + ".latency"), item, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertEquals(body.getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, NETROUTE_POLICY.getName()));

                    testContext.completeNow();

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testDeletePolicy(VertxTestContext testContext)
    {
        TestAPIUtil.delete(NETROUTE_POLICY_API_ENDPOINT + "/" + IDS.getLong(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName() + ".latency."), response ->
        {
            if (response.succeeded())
            {
                Assertions.assertEquals(SC_OK, response.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_SUCCEED, response.result().bodyAsJsonObject().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testDeleteNetRoute(VertxTestContext testContext)
    {
        var id = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "*******").getLong(ID);

        TestAPIUtil.delete(NETROUTE_API_ENDPOINT + "/" + id, response ->
        {
            if (response.succeeded())
            {
                Assertions.assertEquals(SC_OK, response.result().bodyAsJsonObject().getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_SUCCEED, response.result().bodyAsJsonObject().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testDeleteDefaultNetRoutePolicy(VertxTestContext testContext)
    {
        NetRoutePolicyConfigStore.getStore().getItem(DEFAULT_ID, false).put(FIELD_TYPE, 0);

        TestAPIUtil.delete(NETROUTE_POLICY_API_ENDPOINT + "/" + DEFAULT_ID, response ->
        {
            if (response.succeeded())
            {
                var result = response.result().bodyAsJsonObject();

                LOGGER.info(String.format("API response : %s", result.encodePrettily()));

                Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                NetRoutePolicyConfigStore.getStore().getItem(DEFAULT_ID, false).put(FIELD_TYPE, 1);

                testContext.completeNow();
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    private void assertPolicyDuration(JsonObject context, VertxTestContext testContext)
    {
        getPolicyDuration().onComplete(result ->
        {
            var records = result.result();

            var key = context.getLong(ID) + SEPARATOR + context.getLong(POLICY_ID) + SEPARATOR + context.getString(METRIC);

            Assertions.assertTrue(records.containsKey(key));

            var record = records.getJsonObject(key);

            Assertions.assertNotNull(record);

            Assertions.assertEquals(context.getString(METRIC), record.getString(METRIC));

            Assertions.assertEquals(context.getLong(ID), record.getLong(ENTITY_ID));

            Assertions.assertEquals(context.getString(SEVERITY), record.getString(SEVERITY));

            testContext.completeNow();
        });
    }

    private void assertPolicyTriggerTicks(JsonObject context, VertxTestContext testContext)
    {
        var records = JsonObject.mapFrom(NetRoutePolicyFlapDurationCacheStore.getStore().getTriggerTicks());

        LOGGER.info(String.format("Trigger ticks for hops : %s ", records.encodePrettily()));

        var record = records.getJsonObject(context.getLong(POLICY_ID) + SEPARATOR + context.getLong(ID));

        Assertions.assertNotNull(record);

        Assertions.assertTrue(record.getLong(POLICY_FIRST_TRIGGER_TICK) > 0);

        Assertions.assertTrue(record.getLong(POLICY_LAST_TRIGGER_TICK) > 0);

        testContext.completeNow();
    }

    private Future<JsonObject> getPolicyDuration()
    {
        var promise = Promise.<JsonObject>promise();

        Bootstrap.vertx().eventBus().<JsonObject>request(EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY, new JsonObject(), reply ->
        {
            var duration = reply.result().body();

            LOGGER.info(String.format("durations : %s ", duration.encode()));

            promise.complete(duration);
        });

        return promise.future();
    }
}
