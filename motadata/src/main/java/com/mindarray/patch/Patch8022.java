/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  23-May-2025		Chandresh		MOTADATA-6121: Process rediscovery scheduler auto provision off
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Scheduler;
import com.mindarray.db.DBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.MOTADATA_PATCH;

public class Patch8022 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8022.class, MOTADATA_PATCH, "Patch 8.0.22");

    private static final String VERSION = "8.0.22";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.22");

        futures.add(executeProcessRediscoverySchedulerPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.22");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> executeProcessRediscoverySchedulerPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("executing process rediscovery scheduler patch");

            var ids = new JsonArray();

            var items = SchedulerConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(Scheduler.SCHEDULER_CONTEXT) && item.getJsonObject(Scheduler.SCHEDULER_CONTEXT).containsKey(NMSConstants.REDISCOVER_JOB)
                        && item.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.PROCESS.getName()))
                {
                    ids.add(item.getLong(GlobalConstants.ID));
                }
            }

            if (!ids.isEmpty())
            {
                Bootstrap.configDBService().updateAll(DBConstants.TBL_SCHEDULER,
                        new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(GlobalConstants.VALUE, ids),
                        new JsonObject().put(Scheduler.SCHEDULER_CONTEXT, new JsonObject().put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.PROCESS.getName()).put(NMSConstants.AUTO_PROVISION_STATUS, GlobalConstants.NO)),
                        GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS,
                        result ->
                        {
                            if (result.succeeded())
                            {
                                SchedulerConfigStore.getStore().updateItems(ids).onComplete(asyncResult ->
                                {
                                    LOGGER.info("successfully updated process rediscovery scheduler");

                                    promise.complete();
                                });
                            }
                            else
                            {
                                promise.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }
}
