/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.integration.IntegrationEngine.INTEGRATIONS;
import static com.mindarray.integration.IntegrationEngine.INTEGRATIONS_PENDING_EVENTS;

public class IntegrationCacheStore extends AbstractCacheStore
{
    private static final IntegrationCacheStore STORE = new IntegrationCacheStore();

    private static final Logger LOGGER = new Logger(IntegrationCacheStore.class, GlobalConstants.MOTADATA_STORE, "Integration Cache Store");

    private final Map<String, Object> items = new ConcurrentHashMap<>(); //key: integrationType + policy.id + object.id + metric + instance --> value: ack.id

    private final Map<String, Object> archivedItems = new ConcurrentHashMap<>(); //key: integrationType + policy.id + object.id + metric + instance --> value: ack.id

    private final Map<String, String> itemTypes = new ConcurrentHashMap<>(); //key: policy.id --> value: integrationType

    private final Map<Object, String> policies = new ConcurrentHashMap<>(); //key: ack.id --> value: integrationType + policy.id + object.id + metric + instance

    private final Map<String, Long> profiles = new ConcurrentHashMap<>(); //key: policy.id -> value: integration.profile.id

    //to know ticket belong to which integration. (keeping history data)
    private final Map<Object, String> acks = new ConcurrentHashMap<>(); //key: ack.id --> value: integration type

    //will use for visualization. (keeping history data)
    private final Map<String, Object> ackMappers = new ConcurrentHashMap<>(); //key: integrationType + policy.id + object.id + metric + instance + severity + timestamp --> value: ack.id

    // will use to store pending events for different integration types
    private final Map<IntegrationConstants.IntegrationType, List<JsonObject>> pendingEvents = new ConcurrentHashMap<>(); // integration.type --> pending events

    private final AtomicBoolean updated = new AtomicBoolean();

    private final AtomicBoolean dirty = new AtomicBoolean();

    private IntegrationCacheStore()
    {
    }

    public static IntegrationCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (Bootstrap.bootstrapType() == BootstrapType.APP && Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name()) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + INTEGRATIONS);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                    ((Map<String, Object>) (context.get("items"))).forEach((key, value) ->
                    {
                        items.put(CommonUtil.getString(key), value);

                        policies.put(value, key);
                    });

                    ((Map<String, Object>) (context.get("history"))).forEach((key, value) ->
                    {
                        var tokens = key.split(SEPARATOR_WITH_ESCAPE);

                        ackMappers.put(CommonUtil.getString(key), value);

                        acks.put(value, tokens[0]);

                        archivedItems.put(tokens.length == 8 ? String.join(SEPARATOR, Arrays.copyOfRange(tokens, 0, 6)) : String.join(SEPARATOR, Arrays.copyOfRange(tokens, 0, 5)), value);

                        itemTypes.put(tokens[1], tokens[0]);
                    });

                    if (context.containsKey("profiles") && context.get("profiles") != null)
                    {
                        ((Map<String, Object>) (context.get("profiles"))).forEach((key, value) -> profiles.put(key, CommonUtil.getLong(value)));
                    }

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Items: %s, archived items: %s", items, ackMappers));
                    }


                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }

            file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + INTEGRATIONS_PENDING_EVENTS);

            if (file.exists())
            {
                var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    try
                    {
                        var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                        ((Map<String, Object>) (context.get("pendingEvents"))).forEach((key, value) ->
                                pendingEvents.put(IntegrationConstants.IntegrationType.valueOf(key), (List<JsonObject>) value));
                    }
                    catch (Exception exception)
                    {
                        //do not require manual delete file for old version context

                        Bootstrap.vertx().fileSystem().delete(file.getPath());

                        Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());

                        LOGGER.info(String.format("corrupted context received... %s", exception.getMessage()));
                    }
                }
            }
            else
            {
                Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
            }

            for (var integration : IntegrationConstants.IntegrationType.values())
            {
                if (!pendingEvents.containsKey(integration))
                {
                    pendingEvents.put(integration, new ArrayList<>());
                }
            }

            LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        }

        promise.complete();

        return promise.future();
    }

    public void updateItem(String integrationType, String policyKey, Object value, long timestamp, String severity)
    {
        updated.set(true);

        itemTypes.put(policyKey.split(SEPARATOR_WITH_ESCAPE)[0], integrationType);

        archivedItems.put(integrationType + SEPARATOR + policyKey, value);

        items.put(integrationType + SEPARATOR + policyKey, value);

        policies.put(value, integrationType + SEPARATOR + policyKey);

        if (!CommonUtil.getString(value).isEmpty())
        {
            acks.put(value, integrationType);

            ackMappers.put(integrationType + SEPARATOR + policyKey + SEPARATOR + severity + SEPARATOR + timestamp, value);
        }

        LOGGER.info(String.format("Adding key: %s, value: %s for integration type: %s", integrationType + SEPARATOR + policyKey, value, integrationType));
    }

    // cache the request when create request is generated
    public void updateItem(String policyKey, Long id)
    {
        updated.set(true);

        profiles.put(policyKey, id);
    }

    public void addPendingEvent(IntegrationConstants.IntegrationType integrationType, JsonObject event)
    {
        updated.set(true);

        pendingEvents.get(integrationType).add(event);
    }

    // TODO: in case of multiple servicenow or serviceops instances need to handle thread safety
    public List<JsonObject> getPendingEvents(IntegrationConstants.IntegrationType integrationType)
    {
        return pendingEvents.get(integrationType);
    }

    public void removeItem(String policyKey)
    {
        updated.set(true);

        profiles.remove(policyKey);
    }

    public void remove(String integrationType, String key)
    {
        updated.set(true);

        LOGGER.info(String.format("Removing key: %s, for integration type: %s", integrationType + SEPARATOR + key, integrationType));

        if (key.contains(integrationType))
        {
            if (items.containsKey(key))
            {
                policies.remove(items.remove(key));
            }
        }
        else
        {
            if (items.containsKey(integrationType + SEPARATOR + key))
            {
                policies.remove(items.remove(integrationType + SEPARATOR + key));
            }
        }

    }

    //remove key with policy id or object id
    public void remove(String entity)
    {
        updated.set(true);

        LOGGER.info(String.format("Removing key having ID: %s, for All the integration", entity));

        for (var item : items.entrySet())
        {
            if (item.getKey().contains(entity))
            {
                var entry = items.remove(item.getKey());

                acks.remove(entry);

                policies.remove(entry);
            }
        }
    }

    public void removeProfiles(String entity)
    {
        // remove profile id if associated policy is deleted
        for (var item : profiles.entrySet())
        {
            if (item.getKey().contains(entity))
            {
                profiles.remove(item.getKey());
            }
        }
    }

    public Object getItem(String integrationType, String key)
    {
        return items.getOrDefault(integrationType + SEPARATOR + key, EMPTY_VALUE);
    }

    public Object getArchivedItem(String integrationType, String key)
    {
        return archivedItems.getOrDefault(integrationType + SEPARATOR + key, EMPTY_VALUE);
    }

    public String getPolicy(Object ackId)
    {
        return policies.getOrDefault(ackId, EMPTY_VALUE);
    }

    public String getIntegrationType(String policyId)
    {
        return itemTypes.getOrDefault(policyId, EMPTY_VALUE);
    }

    public String getIntegration(Object key)
    {
        return acks.getOrDefault(key, EMPTY_VALUE);
    }

    public Object getTicketId(String key)
    {
        return ackMappers.getOrDefault(key, EMPTY_VALUE);
    }

    public long getProfileId(String policyId)
    {
        return profiles.getOrDefault(policyId, -1L);
    }

    public boolean updated()
    {
        return updated.get();
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }

    public void setUpdate(boolean value)
    {
        updated.set(value);
    }

    public Map<String, Object> getItems()
    {
        return items;
    }

    public Map<String, Object> getMappers()
    {
        return ackMappers;
    }

    public Map<String, Long> getProfiles()
    {
        return profiles;
    }

    public Map<IntegrationConstants.IntegrationType, List<JsonObject>> getPendingEvents()
    {
        return pendingEvents;
    }
}
