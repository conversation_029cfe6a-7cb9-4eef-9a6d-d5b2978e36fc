/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.ErrorMessageConstants.GROUP_DUPLICATE_CHILD_GROUP;
import static com.mindarray.ErrorMessageConstants.GROUP_MAXIMUM_HIERARCHICAL_DEPTH;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.GROUP_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.USER_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.OBJECT_VENDOR;
import static com.mindarray.api.APIConstants.ENTITY_PROPERTY_COUNT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.Group.AutoAssignmentRule.IP_ADDRESS_RANGE;
import static com.mindarray.api.Group.AutoAssignmentRule.OBJECT_TYPE;
import static com.mindarray.api.Group.*;
import static com.mindarray.api.User.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestGroup
{

    private static final JsonObject CONTEXT = new JsonObject();

    private static final JsonObject GROUP1_CONTEXT = new JsonObject()
            .put(FIELD_GROUP_NAME, "groupParent")
            .put(FIELD_PARENT_GROUP, 0)
            .put(Group.GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, NO));

    private static final JsonObject GROUP2_CONTEXT = new JsonObject()
            .put(FIELD_GROUP_NAME, "groupTestAutoAssign")
            .put(FIELD_PARENT_GROUP, 0)
            .put(Group.GROUP_CONTEXT, new JsonObject()
                    .put(GROUP_AUTO_ASSIGN, YES)
                    .put(IP_ADDRESS_RANGE.getName(), new JsonArray().add("*********-6")));

    private static final JsonObject GROUP_IDS = new JsonObject();

    private static final Logger LOGGER = new Logger(TestGroup.class, MOTADATA_API, "Test Group");

    @Test
    @Order(1)
    void testCreateNetworkGroupAND(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(FIELD_GROUP_NAME, "groupTestNetworkAnd").put(FIELD_PARENT_GROUP, 0)
                .put(GROUP_CONTEXT, new JsonObject().put(GROUP_AUTO_ASSIGN, YES)).put(GROUP_OPERATOR, "and").put(OBJECT_VENDOR, new JsonArray().add("Cisco Systems"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            GROUP_IDS.put("groupTestNetworkAnd", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(2)
    void testCreateGroupHavingAutoAssignRuleByIPRange(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(GROUP_API_ENDPOINT, GROUP2_CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), GROUP2_CONTEXT, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("group.auto.assign", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(3)
    void testUpdateGroupHavingAutoAssignRuleByVendor(VertxTestContext testContext, TestInfo testInfo)
    {
        var group = GROUP2_CONTEXT.copy().put(OBJECT_TYPE.getName(), new JsonArray().add(NMSConstants.Type.WINDOWS.getName())
                .add(NMSConstants.Type.LINUX.getName()));

        TestAPIUtil.put(GROUP_API_ENDPOINT + "/" + CONTEXT.getLong("group.auto.assign"), group,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(GroupConfigStore.getStore(), group, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.GROUP.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));

    }

    @Test
    @Order(4)
    void testGetAllGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(GROUP_API_ENDPOINT
                , testContext.succeeding(httpResponse ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(httpResponse, LOGGER, testInfo.getTestMethod().get().getName());

                            var groups = httpResponse.bodyAsJsonObject().getJsonArray(RESULT);

                            assertNotNull(groups);

                            assertFalse(groups.isEmpty());

                            var items = buildTreeStructure(GroupConfigStore.getStore().getItems());

                            assertNotNull(items);

                            assertFalse(items.isEmpty());

                            Assertions.assertEquals(groups.size(), items.size());

                            testContext.completeNow();
                        })));
    }


    @Test
    @Order(5)
    void testCreateGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(GROUP_API_ENDPOINT, GROUP1_CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), GROUP1_CONTEXT, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("group", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();

                        })));
    }

    @Test
    @Order(6)
    void testCreateDuplicateGroup(VertxTestContext testContext)
    {
        TestAPIUtil.post(GROUP_API_ENDPOINT, GROUP1_CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.GROUP.getName()),
                                    GroupConfigStore.getStore(), FIELD_GROUP_NAME, GROUP1_CONTEXT.getString(FIELD_GROUP_NAME));

                            testContext.completeNow();
                        })));
    }


    @Test
    @Order(7)
    void testCreateDuplicateGroupHavingSameParent(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = GROUP1_CONTEXT.copy().put(FIELD_PARENT_GROUP, TestGroup.CONTEXT.getLong("group"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestGroup.CONTEXT.put("child.group", response.bodyAsJsonObject().getLong(ID));

                            TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertNotNull(httpResponse.bodyAsJsonObject());

                                                assertEquals(SC_BAD_REQUEST, httpResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                assertEquals(GROUP_DUPLICATE_CHILD_GROUP, httpResponse.bodyAsJsonObject().getString(MESSAGE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @Test
    @Order(8)
    void testCreateChildGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "groupChild").put(FIELD_PARENT_GROUP, TestGroup.CONTEXT.getLong("group"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(9)
    void testCreateDuplicateChildGroupHavingSameParentGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var firstChildContext = GROUP1_CONTEXT.copy()
                .put(FIELD_GROUP_NAME, "firstChild")
                .put(FIELD_PARENT_GROUP, CONTEXT.getLong("group"));

        var secondChildContext = GROUP1_CONTEXT.copy()
                .put(FIELD_GROUP_NAME, "secondChild")
                .put(FIELD_PARENT_GROUP, CONTEXT.getLong("group"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, firstChildContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), firstChildContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestAPIUtil.post(GROUP_API_ENDPOINT,
                                    secondChildContext,
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), secondChildContext, httpResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @Test
    @Order(10)
    void testUpdateDuplicateGroup(VertxTestContext testContext)
    {
        var context = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "groupTestAutoAssign");

        TestAPIUtil.put(GROUP_API_ENDPOINT + TestGroup.CONTEXT.getLong("group"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.GROUP.getName()),
                                    GroupConfigStore.getStore(), FIELD_GROUP_NAME, "groupTestAutoAssign");

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(11)
    void testMaximumHierarchicalDepthRule(VertxTestContext testContext, TestInfo testInfo)
    {

        var thirdLevelGroup = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "firstInnerChild").put(FIELD_PARENT_GROUP, CONTEXT.getLong("child.group"));

        TestAPIUtil.post(GROUP_API_ENDPOINT, thirdLevelGroup,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), thirdLevelGroup, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("child.inner.group", response.bodyAsJsonObject().getLong(ID));

                            var fourthLevelGroup = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "secondInnerChild").put(FIELD_PARENT_GROUP, CONTEXT.getLong("child.inner.group"));

                            TestAPIUtil.post(GROUP_API_ENDPOINT, fourthLevelGroup,
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), fourthLevelGroup, httpResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                                                CONTEXT.put("child.inner.group.level.4", httpResponse.bodyAsJsonObject().getLong(ID));

                                                var fifthLevelGroup = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "thirdInnerChild").put(FIELD_PARENT_GROUP, CONTEXT.getLong("child.inner.group.level.4"));

                                                TestAPIUtil.post(GROUP_API_ENDPOINT, fifthLevelGroup,
                                                        testContext.succeeding(fifthLevelResponse ->
                                                                testContext.verify(() ->
                                                                {
                                                                    TestAPIUtil.assertCreateEntityTestResult(GroupConfigStore.getStore(), fifthLevelGroup, fifthLevelResponse.bodyAsJsonObject(),
                                                                            String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.GROUP.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                                                                    CONTEXT.put("child.inner.group.level.5", fifthLevelResponse.bodyAsJsonObject().getLong(ID));

                                                                    var sixLevelNestedChild = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "fourthInnerChild").put(FIELD_PARENT_GROUP, CONTEXT.getLong("child.inner.group.level.5"));

                                                                    TestAPIUtil.post(GROUP_API_ENDPOINT, sixLevelNestedChild,
                                                                            testContext.succeeding(sixLevelResponse ->
                                                                                    testContext.verify(() ->
                                                                                    {
                                                                                        assertNotNull(sixLevelResponse.bodyAsJsonObject());

                                                                                        assertEquals(SC_BAD_REQUEST, sixLevelResponse.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                                                                        assertEquals(ErrorCodes.ERROR_CODE_INTERNAL_ERROR, sixLevelResponse.bodyAsJsonObject().getString(ERROR_CODE));

                                                                                        assertEquals(GROUP_MAXIMUM_HIERARCHICAL_DEPTH, sixLevelResponse.bodyAsJsonObject().getString(MESSAGE));

                                                                                        testContext.completeNow();
                                                                                    })));
                                                                })));
                                            })));
                        })));
    }

    @Test
    @Order(12)
    void testGetUsers(VertxTestContext testContext)
    {
        TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

                    var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                    assertNotNull(items);

                    for (var index = 0; index < items.size(); index++)
                    {
                        if (items.getJsonObject(index).getString(USER_NAME).equalsIgnoreCase("user1")
                                && items.getJsonObject(index).getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM))
                        {
                            CONTEXT.put("custom.user", items.getJsonObject(index).getLong(ID));

                            break;
                        }
                    }

                    testContext.completeNow();

                })));

    }

    @Test
    @Order(13)
    void testDeleteGroupInvalidId(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.delete(GROUP_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->

                        testContext.verify(() ->
                                TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                                        testContext.succeeding(httpResponse ->
                                                testContext.verify(() ->
                                                {

                                                    TestAPIUtil.assertGETRequestTestResult(httpResponse, CONTEXT.getLong("custom.user"), UserConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                                                    testContext.completeNow();

                                                }))))));
    }

    @Test
    @Order(14)
    void testCreateUser(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_GROUPS, new JsonArray()
                .add(TestGroup.CONTEXT.getLong("child.group"))
                .add(TestGroup.CONTEXT.getLong("group"))
                .add(TestGroup.CONTEXT.getLong("child.inner.group")));

        TestAPIUtil.put(USER_API_ENDPOINT + TestGroup.CONTEXT.getLong("custom.user"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(15)
    void testGetGroupReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT + CONTEXT.getLong("group") + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.USER.getName(), 1);

            testContext.completeNow();

        })));
    }


    @Test
    @Order(16)
    void testDeleteActiveGroup(VertxTestContext testContext)
    {
        TestAPIUtil.delete(GROUP_API_ENDPOINT + CONTEXT.getLong("group"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, APIConstants.Entity.GROUP.getName());

            testContext.completeNow();

        })));
    }

    @Test
    @Order(17)
    void testGetChildGroupReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT + CONTEXT.getLong("child.group") + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.USER.getName(), 1);

            testContext.completeNow();

        })));
    }

    @Test
    @Order(18)
    void testDeleteActiveChildGroup0(VertxTestContext testContext)
    {
        TestAPIUtil.delete(GROUP_API_ENDPOINT + CONTEXT.getLong("child.group"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, APIConstants.Entity.GROUP.getName());

            testContext.completeNow();

        })));
    }

    @Test
    @Order(19)
    void testGetAllNestedGroupReference(VertxTestContext testContext)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT + CONTEXT.getLong("child.inner.group") + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.USER.getName(), 1);

            testContext.completeNow();

        })));
    }

    @Test
    @Order(20)
    void testDeleteActiveChildGroup1(VertxTestContext testContext)
    {
        TestAPIUtil.delete(GROUP_API_ENDPOINT + CONTEXT.getLong("child.inner.group"), testContext.succeeding(response -> testContext.verify(() ->
        {

            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, APIConstants.Entity.GROUP.getName());

            testContext.completeNow();

        })));
    }

    @Test
    @Order(21)
    void testUpdateUserWithParentGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = new JsonObject().put(USER_GROUPS, new JsonArray()
                .add(TestGroup.CONTEXT.getLong("group")));

        TestAPIUtil.put(USER_API_ENDPOINT + TestGroup.CONTEXT.getLong("custom.user"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @Test
    @Order(22)
    void testDeleteChildGroup(VertxTestContext testContext)
    {
        TestAPIUtil.delete(GROUP_API_ENDPOINT + CONTEXT.getLong("child.group"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TimeUnit.SECONDS.sleep(2);

            TestAPIUtil.assertDeleteEntityTestResult(GroupConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.GROUP.getName()));

            testContext.completeNow();

        })));
    }

    @Test
    @Order(23)
    void testGetGroupUsedCountIsZeroAfterDeletingChildGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT + "/" + CONTEXT.getLong("child.inner.group")
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            assertEquals(0, result.size());

                            testContext.completeNow();
                        })));
    }

    @Test
    @Order(24)
        //bug - 3513
    void testUpdateSameGroupNameOnDifferentParentGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "firstInnerChild");

        TestAPIUtil.post(GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var group = GROUP1_CONTEXT.copy().put(FIELD_GROUP_NAME, "firstInnerChild");

                            TestAPIUtil.put(GROUP_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID), group,
                                    testContext.succeeding(groupResponse ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertUpdateEntityTestResult(GroupConfigStore.getStore(), group, groupResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.GROUP.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @Test
    @Order(25)
    void testGetReferenceUsedCountByGroup(VertxTestContext testContext)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT, response ->
        {
            if (response.succeeded())
            {
                var item = response.result().bodyAsJsonObject();

                assertNotNull(item);

                assertEquals(HttpStatus.SC_OK, item.getInteger(RESPONSE_CODE));

                assertEquals(STATUS_SUCCEED, item.getString(STATUS));

                assertFalse(item.getJsonArray(RESULT).isEmpty());

                var items = item.getJsonArray(RESULT);

                for (var i = 0; i < items.size(); i++)
                {
                    var group = items.getJsonObject(i);

                    assertTrue(group.containsKey(ENTITY_PROPERTY_COUNT));

                    if (group.containsKey("child"))
                    {
                        var children = group.getJsonArray("child");

                        for (var j = 0; j < children.size(); j++)
                        {
                            assertTrue(children.getJsonObject(j).containsKey(ENTITY_PROPERTY_COUNT));
                        }
                    }
                }

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(response.cause());
            }
        });
    }

    private JsonArray buildTreeStructure(JsonArray groups)
    {
        var items = new JsonArray();

        if (groups != null && !groups.isEmpty())
        {
            var groupIds = new JsonObject();

            for (var index = 0; index < groups.size(); index++)
            {

                var group = groups.getJsonObject(index);

                groupIds.put(CommonUtil.getString(group.getValue(GlobalConstants.ID)), group);
            }

            var disqualifiedGroups = new JsonArray();

            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getJsonObject(index);

                var parentId = CommonUtil.getString(group.getValue(FIELD_PARENT_GROUP));

                if (parentId != null)
                {
                    var parentGroup = groupIds.getJsonObject(parentId);

                    if (parentGroup != null)
                    {

                        if (parentGroup.getJsonArray("child") == null)
                        {
                            parentGroup.put("child", new JsonArray());
                        }

                        parentGroup.getJsonArray("child").add(group);

                        groupIds.put(parentId, parentGroup);

                        groupIds.put(CommonUtil.getString(group.getValue(GlobalConstants.ID)), group);

                        disqualifiedGroups.add(CommonUtil.getString(group.getValue(GlobalConstants.ID)));
                    }
                }
            }

            for (var index = 0; index < disqualifiedGroups.size(); index++)
            {
                groupIds.remove(disqualifiedGroups.getString(index));
            }

            groupIds.getMap().values().forEach(items::add);

        }
        return items;
    }

}
