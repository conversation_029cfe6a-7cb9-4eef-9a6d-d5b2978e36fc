/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	24-Mar-2025     <PERSON><PERSON>         MOTADATA-5432: Either 3 or value returned by "ping.check.packet.retries" will be considered as packet retries i.e. "-c in ping/fping"
	20-May-2025     Aagam               MOTADATA-5847 : Added windows build support
*/

package com.mindarray.nms;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.EventCacheStore;
import com.mindarray.store.MetricCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.api.AIOpsObject.OBJECT_TARGET;
import static com.mindarray.api.Metric.METRIC_CATEGORY;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CONTEXT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_LATENCY;
import static com.mindarray.nms.NMSConstants.*;

/**
 * A Vert.x verticle responsible for polling metrics from monitored systems and devices.
 * <p>
 * The MetricPoller class is a central component of the monitoring system that:
 * <p>
 * 1. Manages worker pools for different types of metric polling:
 * - Regular metrics (SNMP, WMI, etc.)
 * - Availability/ping checks
 * - Network service (port) checks
 * <p>
 * 2. Schedules and executes metric polling operations based on:
 * - Metric category (Network, Server, Database, etc.)
 * - Metric type (regular, availability, network service)
 * - Timeout requirements
 * - Batch processing capabilities
 * <p>
 * 3. Implements load balancing and resource management:
 * - Tracks idle workers across different categories
 * - Distributes polling tasks based on worker availability
 * - Handles metric queuing and prioritization
 * - Implements auto-scaling of worker pools when needed
 * <p>
 * 4. Processes polling results and sends them to appropriate event bus addresses
 * <p>
 * The class uses Vert.x's event-driven, non-blocking architecture to efficiently
 * handle large numbers of concurrent polling operations with minimal resource usage.
 */
public class MetricPoller extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(MetricPoller.class, GlobalConstants.MOTADATA_NMS, "Metric Poller");

    /**
     * Worker executors for regular metric polling operations
     */
    private final List<WorkerExecutor> metricWorkerExecutors = new ArrayList<>();

    /**
     * Worker executors for network service (port) checks
     */
    private final List<WorkerExecutor> networkServiceWorkerExecutors = new ArrayList<>();

    /**
     * Worker executors for availability/ping checks
     */
    private final List<WorkerExecutor> availabilityWorkerExecutors = new ArrayList<>();

    /**
     * Tracks the number of idle workers for each metric category
     */
    private final Map<Category, Integer> idleWorkersByCategory = new EnumMap<>(Category.class);

    /**
     * Tracks the number of idle workers for each metric worker executor
     */
    private final Map<Integer, Integer> idleMetricWorkersByExecutor = new HashMap<>();

    /**
     * Tracks the number of idle workers for each availability worker executor
     */
    private final Map<Integer, Integer> idleAvailabilityWorkersByExecutor = new HashMap<>();

    /**
     * Tracks the number of idle workers for each network service worker executor
     */
    private final Map<Integer, Integer> idleNetworkServiceWorkersByExecutor = new HashMap<>();

    /**
     * Queued metrics organized by category and timeout value type
     */
    private final Map<Category, Map<Byte, List<Long>>> metricsByCategory = new EnumMap<>(Category.class);

    /**
     * Queued batch metrics organized by category and timeout value type
     */
    private final Map<Category, Map<Byte, List<Long>>> batchMetricsByCategory = new EnumMap<>(Category.class);

    /**
     * Counts of discarded metrics by category (due to overload or duplicates)
     */
    private final Map<Category, Integer> discardedMetricsByCategory = new EnumMap<>(Category.class);

    /**
     * Queued availability metrics organized by packet count
     */
    private final Map<Integer, List<Long>> pendingAvailabilityMetrics = new LinkedHashMap<>();

    /**
     * Queued network service metrics
     */
    private final List<Long> pendingNetworkServiceMetrics = new ArrayList<>();

    /**
     * Tracks duplicate metrics to avoid redundant polling operations
     */
    private final Map<String, List<JsonObject>> duplicateMetrics = new HashMap<>();

    /**
     * Current timeout value type for each metric category (used for round-robin scheduling)
     */
    private final Map<Category, MetricTimeoutValueType> metricTimeoutValueTypesByCategory = new EnumMap<>(Category.class);

    /**
     * Total count of idle metric workers across all executors
     */
    private final AtomicInteger idleMetricWorkers = new AtomicInteger();

    /**
     * Total count of idle availability workers across all executors
     */
    private int idleAvailabilityWorkers;

    /**
     * Total count of idle network service workers across all executors
     */
    private int idleNetworkServiceWorkers = MotadataConfigUtil.getNetworkServiceWorkers() * MotadataConfigUtil.getWorkers();

    /**
     * Initializes the MetricPoller verticle and starts all polling operations.
     * <p>
     * This method:
     * 1. Creates worker executor pools for different types of metric polling
     * 2. Sets up worker allocation for different metric categories
     * 3. Registers event bus consumers for handling metric polling requests and stats
     * 4. Starts periodic timers for scheduling different types of metric polling:
     * - Regular metrics (every 1000ms)
     * - Availability/ping checks (every 500ms)
     * - Network service checks (every 100ms)
     * <p>
     * The worker pools are sized based on the deployment type (small, medium, large)
     * and the expected workload for each metric category.
     *
     * @param promise A Promise that will be completed when initialization is finished
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // start 13 worker executors by default ... each work executor will have 2 * deployment worker threads...

        //default total 13 threads likewise (for small deployment)
        // for medium deployment type threads will be default *2 and for large it will be default * 4

        /*it will be in percent
                Network = 3
                Server = 2
                Virtualization = 1
                Database =1
                Middleware =1
                ServiceCheck =1
                WebServer =1
                Cloud =1
                Custom =1
                Other =1 */

        for (var i = 0; i < MotadataConfigUtil.getMetricPollerWorkers(); i++)
        {
            metricWorkerExecutors.add(vertx.createSharedWorkerExecutor("Metric Poller " + i, MotadataConfigUtil.getWorkers(), 60L, TimeUnit.SECONDS));

            idleMetricWorkersByExecutor.put(i, MotadataConfigUtil.getWorkers());
        }

        for (var i = 0; i < idleNetworkServiceWorkers; i++)
        {
            networkServiceWorkerExecutors.add(vertx.createSharedWorkerExecutor("Network Service Poller " + i, MotadataConfigUtil.getWorkers(), 60L, TimeUnit.SECONDS));

            idleNetworkServiceWorkersByExecutor.put(i, MotadataConfigUtil.getWorkers());
        }

        setWorkers();

        var availabilityWorkers = MotadataConfigUtil.getAvailabilityWorkers();

        idleAvailabilityWorkers = availabilityWorkers * MotadataConfigUtil.getWorkers();

        for (var index = 0; index < availabilityWorkers; index++)
        {
            availabilityWorkerExecutors.add(vertx.createSharedWorkerExecutor("Availability Poller " + index, MotadataConfigUtil.getWorkers(), 60L, TimeUnit.SECONDS));

            idleAvailabilityWorkersByExecutor.put(index, MotadataConfigUtil.getWorkers());
        }

        //TODO :- for network device of SNMP V3 credentials if customer using multiple credentials and in one process multiple SNMP V3 credentials metrics qualified then polling behaves randomly and will not work proper..as of now we will go with this known limitation.

        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
        {
            var pendingEvents = new AtomicInteger();

            var pendingBatchEvents = new AtomicInteger();

            var droppedEvents = new AtomicInteger();

            var idleWorkers = new AtomicInteger();

            var categories = new JsonArray();

            metricsByCategory.forEach((metricCategory, metrics) ->
            {

                var categoryPendingMetrics = metrics.values().stream().map(List::size).reduce(0, Integer::sum);

                pendingEvents.addAndGet(categoryPendingMetrics);

                var category = new JsonObject().put("metric.category", metricCategory.getName()).
                        put("metric.category.pending.events", categoryPendingMetrics);

                metrics = batchMetricsByCategory.get(metricCategory);

                if (metrics != null && !metrics.isEmpty())
                {
                    categoryPendingMetrics = metrics.values().stream().map(List::size).reduce(0, Integer::sum);

                    pendingBatchEvents.addAndGet(categoryPendingMetrics);

                    category.put("metric.category.pending.batch.events", categoryPendingMetrics);
                }

                else
                {
                    category.put("metric.category.pending.batch.events", 0);
                }

                var categoryIdleWorkers = idleWorkersByCategory.get(metricCategory);

                idleWorkers.addAndGet(categoryIdleWorkers);

                category.put("metric.category.idle.workers", categoryIdleWorkers);

                if (discardedMetricsByCategory.containsKey(metricCategory))
                {
                    var discardedMetrics = discardedMetricsByCategory.get(metricCategory);

                    droppedEvents.addAndGet(discardedMetrics);

                    category.put("metric.category.discarded.events", discardedMetrics);
                }

                categories.add(category);

            });

            //let's process batchmetric and skip those which already processed..

            batchMetricsByCategory.forEach((metricCategory, metrics) ->
            {
                if (!metricsByCategory.containsKey(metricCategory))
                {
                    var category = new JsonObject().put("metric.category", metricCategory.getName()).
                            put("metric.category.pending.events", 0);

                    var categoryPendingMetrics = metrics.values().stream().map(List::size).reduce(0, Integer::sum);

                    pendingBatchEvents.addAndGet(categoryPendingMetrics);

                    category.put("metric.category.pending.batch.events", categoryPendingMetrics);

                    var categoryIdleWorkers = idleWorkersByCategory.get(metricCategory);

                    idleWorkers.addAndGet(categoryIdleWorkers);

                    category.put("metric.category.idle.workers", categoryIdleWorkers);

                    if (discardedMetricsByCategory.containsKey(metricCategory))
                    {
                        var discardedMetrics = discardedMetricsByCategory.get(metricCategory);

                        droppedEvents.addAndGet(discardedMetrics);

                        category.put("metric.category.discarded.events", discardedMetrics);
                    }

                    categories.add(category);
                }
            });

            categories.add(new JsonObject().put("metric.category", "availability")
                    .put("metric.category.pending.events", pendingAvailabilityMetrics.values().stream().map(List::size).reduce(0, Integer::sum))
                    .put("metric.category.idle.workers", idleAvailabilityWorkers));

            categories.add(new JsonObject().put("metric.category", "network.service")
                    .put("metric.category.pending.events", pendingNetworkServiceMetrics.size())
                    .put("metric.category.idle.workers", idleNetworkServiceWorkers));

            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EventBusConstants.EVENT_METRIC_POLL)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, pendingEvents.get()).put(METRIC_CATEGORY, categories)
                                        .put(HealthUtil.PENDING_BATCH_EVENTS, pendingBatchEvents.get())
                                        .put(HealthUtil.DROPPED_EVENTS, droppedEvents)
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
            else
            {
                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EventBusConstants.EVENT_METRIC_POLL)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, pendingEvents.get()).put(METRIC_CATEGORY, categories)
                                        .put(HealthUtil.PENDING_BATCH_EVENTS, pendingBatchEvents.get())
                                        .put(HealthUtil.DROPPED_EVENTS, droppedEvents)
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
        });

        //metric scheduler .....
        vertx.setPeriodic(1000, timer ->
        {
            try
            {
                if (MetricCacheStore.getStore().getQueuedMetrics() > 0 && idleMetricWorkers.get() > 0)
                {
                    var categories = new ArrayList<NMSConstants.Category>();

                    var pendingMetricsByCategory = new EnumMap<Category, Integer>(Category.class);

                    var pendingBatchMetricsByCategory = new EnumMap<Category, Integer>(Category.class);

                    var pendingMetrics = 0;

                    var pendingBatchMetrics = 0;

                    var metrics = new ArrayList<Map<Long, JsonObject>>();

                    for (var category : Category.values())
                    {
                        pendingBatchMetrics += dequeue(category, categories, pendingBatchMetricsByCategory, metrics, PluginEngineConstants.PluginEngine.GO);

                        pendingMetrics += dequeue(category, categories, pendingMetricsByCategory, metrics, PluginEngineConstants.PluginEngine.PYTHON);
                    }

                    if (idleMetricWorkers.get() > 0)
                    {
                        //means not all category has pending metrics... let's apply load balancing to distribute equal load and utilize idle workers..
                        if (pendingBatchMetrics > 0)
                        {
                            dequeue(pendingBatchMetricsByCategory, categories, metrics, PluginEngineConstants.PluginEngine.GO);
                        }

                        if (pendingMetrics > 0)
                        {
                            dequeue(pendingMetricsByCategory, categories, metrics, PluginEngineConstants.PluginEngine.PYTHON);
                        }
                    }

                    if (!metrics.isEmpty())
                    {
                        var index = 0;

                        for (var entry : idleMetricWorkersByExecutor.entrySet()
                                .stream()
                                .filter(entry -> entry.getValue() > 0)
                                .sorted((Map.Entry.<Integer, Integer>comparingByValue().reversed()))
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet())
                        {
                            var worker = 0;

                            while (worker < entry.getValue() && index < metrics.size())
                            {
                                var category = categories.get(index);

                                var contexts = metrics.get(index);

                                var size = contexts.size();

                                if (size > 0)
                                {
                                    idleMetricWorkersByExecutor.put(entry.getKey(), idleMetricWorkersByExecutor.get(entry.getKey()) - 1);

                                    metricWorkerExecutors.get(entry.getKey()).<String>executeBlocking(future ->
                                    {
                                        try
                                        {
                                            var eventIds = new ArrayList<Long>();

                                            for (var item : contexts.values())
                                            {
                                                eventIds.add(item.getLong(EventBusConstants.EVENT_ID));

                                                EventBusConstants.startEvent(item.getLong(EventBusConstants.EVENT_ID), Thread.currentThread().getName());
                                            }

                                            var latency = System.currentTimeMillis();

                                            var context = contexts.values().stream().findFirst().get();

                                            var pluginEngine = PluginEngineConstants.PluginEngine.valueOfName(context.getString(PluginEngineConstants.PLUGIN_ENGINE));

                                            var qualifiedMetrics = contexts.keySet().stream().map(CommonUtil::getLong).collect(Collectors.toList());

                                            var timeout = pluginEngine == PluginEngineConstants.PluginEngine.GO ? MotadataConfigUtil.getMetricPollerBatchTimeoutSeconds(MetricTimeoutValueType.valueOfName(NMSConstants.getMetricTimeoutValueType(context))) : context.getInteger(TIMEOUT, 60);

                                            WorkerUtil.spawnWorker(contexts, context, eventIds, timeout, false, pluginEngine, latency, true);

                                            latency = System.currentTimeMillis() - latency;

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("Worker thread %s took %s ms with timeout %s to poll %s %s category metrics : %s", Thread.currentThread().getName(), latency, timeout, size, context.getString(METRIC_CATEGORY), qualifiedMetrics));
                                            }
                                        }

                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);
                                        }

                                        finally
                                        {
                                            future.complete();
                                        }
                                    }, false, result ->
                                    {
                                        idleMetricWorkers.incrementAndGet();

                                        idleWorkersByCategory.put(category, idleWorkersByCategory.get(category) + 1);

                                        idleMetricWorkersByExecutor.put(entry.getKey(), idleMetricWorkersByExecutor.get(entry.getKey()) + 1);
                                    });
                                }
                                else
                                {
                                    idleMetricWorkers.incrementAndGet();

                                    idleWorkersByCategory.put(category, idleWorkersByCategory.get(category) + 1);
                                }

                                index++;

                                worker++;
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.setPeriodic(500, timer ->
        { // availability/ping/status scheduler .....

            try
            {
                if (idleAvailabilityWorkers > 0 && !pendingAvailabilityMetrics.isEmpty())
                {
                    var metrics = dequeue();

                    if (!metrics.isEmpty())
                    {
                        var index = 0;

                        for (var entry : idleAvailabilityWorkersByExecutor.entrySet()
                                .stream()
                                .filter(entry -> entry.getValue() > 0)
                                .sorted((Map.Entry.<Integer, Integer>comparingByValue().reversed()))
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet())
                        {
                            var worker = 0;

                            while (worker < entry.getValue() && index < metrics.size())
                            {
                                var contexts = metrics.get(index);

                                if (!contexts.isEmpty())
                                {
                                    idleAvailabilityWorkersByExecutor.put(entry.getKey(), idleAvailabilityWorkersByExecutor.get(entry.getKey()) - 1);

                                    availabilityWorkerExecutors.get(entry.getKey()).<Void>executeBlocking(future ->
                                    {
                                        try
                                        {
                                            var targets = new JsonArray(new ArrayList<>(contexts.keySet()));

                                            var millis = System.currentTimeMillis();

                                            var probes = PingUtil.ping(targets, PING_CHECK_TIMEOUT_SECONDS, PING_CHECK_PACKETS, contexts, Thread.currentThread().getName());

                                            millis = System.currentTimeMillis() - millis;

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("Worker thread %s took %s ms to poll %s %s objects with %s packet", Thread.currentThread().getName(), millis, targets.size(), targets, PING_CHECK_PACKETS));
                                            }

                                            for (var i = 0; i < probes.size(); i++)
                                            {
                                                var result = probes.getJsonObject(i);

                                                send(contexts.remove(result.getString(OBJECT_TARGET))
                                                        .put(EVENT_LATENCY, CommonUtil.getFloat(millis / targets.size()))
                                                        .put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(RESULT, result));

                                                if (duplicateMetrics.containsKey(result.getString(OBJECT_TARGET)))
                                                {
                                                    for (var j = 0; j < duplicateMetrics.get(result.getString(OBJECT_TARGET)).size(); j++)
                                                    {
                                                        send(duplicateMetrics.get(result.getString(OBJECT_TARGET)).get(j)
                                                                .put(EVENT_LATENCY, CommonUtil.getFloat(millis / targets.size()))
                                                                .put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(RESULT, result));
                                                    }

                                                    duplicateMetrics.remove(result.getString(OBJECT_TARGET));
                                                }
                                            }

                                            send(contexts, ErrorCodes.ERROR_CODE_INTERNAL_ERROR, null);

                                            future.complete();
                                        }
                                        catch (Exception exception)
                                        {
                                            future.fail(exception);

                                            send(contexts, exception.getMessage() != null && exception.getMessage().contains(PROCESS_TIMED_OUT) ?
                                                    ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_INTERNAL_ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()));
                                        }
                                    }, false, result ->
                                    {
                                        idleAvailabilityWorkers++;

                                        idleAvailabilityWorkersByExecutor.put(entry.getKey(), idleAvailabilityWorkersByExecutor.get(entry.getKey()) + 1);
                                    });
                                }

                                else
                                {
                                    idleAvailabilityWorkers++;
                                }

                                index++;

                                worker++;
                            }

                        }
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        });

        vertx.setPeriodic(100, timer ->
        { // network service (ports) scheduler .....

            try
            {
                if (idleNetworkServiceWorkers > 0 && !pendingNetworkServiceMetrics.isEmpty())
                {
                    var metrics = new ArrayList<Long>();

                    var iterator = pendingNetworkServiceMetrics.listIterator();

                    while (idleNetworkServiceWorkers > 0 && iterator.hasNext())
                    {
                        metrics.add(iterator.next());

                        iterator.remove();

                        idleNetworkServiceWorkers--;
                    }

                    var index = 0;

                    for (var entry : idleNetworkServiceWorkersByExecutor.entrySet()
                            .stream()
                            .filter(entry -> entry.getValue() > 0)
                            .sorted((Map.Entry.<Integer, Integer>comparingByValue().reversed()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet())
                    {
                        var worker = 0;

                        while (worker < entry.getValue() && index < metrics.size())
                        {
                            if (MetricCacheStore.getStore().queuedMetric(metrics.get(index)))
                            {
                                var metric = MetricCacheStore.getStore().dequeue(metrics.get(index));

                                if (EventCacheStore.getStore().validItem(metric.getLong(EventBusConstants.EVENT_ID)))
                                {
                                    EventBusConstants.updateEvent(metric.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                    idleNetworkServiceWorkersByExecutor.put(entry.getKey(), idleNetworkServiceWorkersByExecutor.get(entry.getKey()) - 1);

                                    networkServiceWorkerExecutors.get(entry.getKey()).<Void>executeBlocking(future ->
                                    {
                                        EventBusConstants.startEvent(metric.getLong(EventBusConstants.EVENT_ID), Thread.currentThread().getName());

                                        if (metric.getJsonArray(NMSConstants.OBJECTS) != null && !metric.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                                        {
                                            var networkServices = new JsonArray();

                                            var millis = System.currentTimeMillis();

                                            for (var poller = 0; poller < metric.getJsonArray(OBJECTS).size(); poller++)
                                            {
                                                try
                                                {
                                                    // object.name is like 22 (SSH) so we need to split it for port
                                                    var objectName = metric.getJsonArray(OBJECTS).getJsonObject(poller).getString(AIOpsObject.OBJECT_NAME);

                                                    var probe = PortUtil.test(metric.getString(OBJECT_IP),
                                                                    CommonUtil.getInteger(objectName.split("\\(")[0].trim()), metric.getInteger(TIMEOUT, 1))
                                                            .put(NMSConstants.NETWORK_SERVICE, objectName);

                                                    networkServices.add(probe.containsKey(RESULT) ? probe.put(NETWORK_SERVICE_LATENCY, CommonUtil.getLong(probe.remove(RESULT))) : probe);
                                                }

                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);
                                                }
                                            }

                                            millis = System.currentTimeMillis() - millis;

                                            if (CommonUtil.debugEnabled())
                                            {
                                                LOGGER.debug(String.format("Worker thread %s took %s ms to poll metric %s of object %s", Thread.currentThread().getName(), millis,
                                                        metric.getString(Metric.METRIC_NAME), metric.getString(AIOpsObject.OBJECT_NAME)));
                                            }

                                            metric.put(EventBusConstants.EVENT_LATENCY, CommonUtil.getFloat(millis));

                                            metric.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS).put(RESULT, new JsonObject().put(NMSConstants.NETWORK_SERVICE, networkServices));

                                        }
                                        else
                                        {
                                            metric.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                                    .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLLER_NO_OBJECT_FOUND, Type.NETWORK_SERVICE.getName()));

                                        }

                                        send(metric);

                                        future.complete();

                                    }, false, result ->
                                    {
                                        idleNetworkServiceWorkers++;

                                        idleNetworkServiceWorkersByExecutor.put(entry.getKey(), idleNetworkServiceWorkersByExecutor.get(entry.getKey()) + 1);
                                    });
                                }

                                else
                                {
                                    idleNetworkServiceWorkers++;
                                }

                            }

                            else
                            {
                                // metric is deleted ... hence just aborting the task... release worker...

                                idleNetworkServiceWorkers++;

                                abort(metrics.get(index));
                            }

                            index++;

                            worker++;
                        }

                    }


                }

            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_POLLER_AUTO_SCALE, message ->
        {
            var event = message.body();

            if (event != null)
            {
                var workers = event.getInteger("workers");

                var categories = event.getJsonArray(EVENT_CONTEXT);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("before execute idle workers: %s, idle workers by category: %s, idle workers by executor: %s, metric workers by executor: %s", idleMetricWorkers.get(), idleWorkersByCategory, idleMetricWorkersByExecutor, metricWorkerExecutors.size()));
                }

                for (var index = 0; index < workers; index++)
                {
                    var i = metricWorkerExecutors.size() + index;

                    metricWorkerExecutors.add(vertx.createSharedWorkerExecutor("Metric Poller " + i, MotadataConfigUtil.getWorkers(), 60L, TimeUnit.SECONDS));

                    idleMetricWorkersByExecutor.put(i, MotadataConfigUtil.getWorkers());
                }

                var extraWorkers = workers * MotadataConfigUtil.getWorkers();

                var workersByCategory = extraWorkers / categories.size();

                var remainder = extraWorkers % categories.size();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("extra workers: %s, workers by category: %s, remainder: %s", extraWorkers, workersByCategory, remainder));
                }

                for (var index = 0; index < categories.size(); index++)
                {
                    var category = categories.getString(index);

                    if (index == 0)
                    {
                        idleWorkersByCategory.put(Category.valueOfName(category), idleWorkersByCategory.get(Category.valueOfName(category)) + workersByCategory + remainder);
                    }
                    else
                    {
                        idleWorkersByCategory.put(Category.valueOfName(category), idleWorkersByCategory.get(Category.valueOfName(category)) + workersByCategory);
                    }
                }

                idleMetricWorkers.set(idleMetricWorkers.get() + extraWorkers);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("after execute idle workers: %s, idle workers by category: %s, idle workers by executor: %s, metric workers by executor: %s", idleMetricWorkers.get(), idleWorkersByCategory, idleMetricWorkersByExecutor, metricWorkerExecutors.size()));
                }

                if (MotadataConfigUtil.devMode())
                {
                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLLER_TEST, new JsonObject().put("categories", categories).put("workers", workers));
                }
            }

        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLL, message ->
        {
            try
            {
                if (message.body() != null)
                {
                    var event = message.body();

                    var category = Category.valueOfName(event.getString(METRIC_CATEGORY));

                    var eventId = MetricCacheStore.getStore().queue(event);

                    if (eventId != NOT_AVAILABLE)
                    {
                        //duplicate event is present.... means poller may be overloaded or poll now button is being clicked multiple times.....

                        if (discardedMetricsByCategory.containsKey(category))
                        {
                            discardedMetricsByCategory.put(category, discardedMetricsByCategory.get(category) + 1);
                        }

                        else
                        {
                            discardedMetricsByCategory.put(category, 1);
                        }

                        EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                        LOGGER.warn(String.format("metric %s of object %s discarded due to over utilization of poller ...", event.getString(Metric.METRIC_NAME), event.getString(AIOpsObject.OBJECT_NAME)));

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().publish(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE,
                                    event.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DUPLICATE).put(MESSAGE, String.format(METRIC_POLL_FAILED, String.format("duplicate event [ref id: %s]", eventId))));

                        }
                        else
                        {
                            vertx.eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE,
                                    event.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DUPLICATE).put(MESSAGE, String.format(METRIC_POLL_FAILED, String.format("duplicate event [ref id: %s]", eventId))));
                        }
                    }

                    else
                    {
                        switch (MetricPlugin.valueOfName(event.getString(Metric.METRIC_PLUGIN)))
                        {
                            case NETWORK_SERVICE -> pendingNetworkServiceMetrics.add(event.getLong(ID));

                            case AVAILABILITY, PING ->
                            {
                                var packet = event.getInteger(PING_CHECK_RETRIES, 3);

                                if (!pendingAvailabilityMetrics.containsKey(packet))
                                {
                                    pendingAvailabilityMetrics.put(packet, new ArrayList<>());
                                }

                                pendingAvailabilityMetrics.get(packet).add(event.getLong(ID));
                            }

                            default ->
                            {
                                // assign batch or normal metrics based on category and metric timeout value type
                                // for example one metric has 60 timeout and second one is 150 timeout then both will qualify in different metric timeout value
                                if (PluginEngineConstants.hasBatchSupport(event))
                                {
                                    batchMetricsByCategory.get(category).get(getMetricTimeoutValueType(event)).add(event.getLong(ID));
                                }
                                else
                                {
                                    metricsByCategory.get(category).get(getMetricTimeoutValueType(event)).add(event.getLong(ID));
                                }
                            }
                        }
                    }
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    /**
     * Dequeues availability metrics for processing.
     * <p>
     * This method:
     * 1. Retrieves pending availability metrics from the queue
     * 2. Groups them by IP address to avoid duplicate polling
     * 3. Batches them for efficient processing (up to 200 metrics per worker)
     * 4. Decrements the idle worker count for each batch created
     * <p>
     * The method handles duplicate IP addresses by:
     * - Keeping only one metric for each unique IP in the batch
     * - Storing duplicates in the duplicateMetrics map for later processing
     * <p>
     * It also implements a probing mechanism to avoid infinite loops when:
     * - No more metrics are available
     * - No more idle workers are available
     *
     * @return A list of maps, where each map contains IP-to-metric mappings for a batch
     */
    private List<Map<String, JsonObject>> dequeue()
    {
        var metrics = new ArrayList<Map<String, JsonObject>>();

        var probes = 0;

        var batchSize = 200; //1 worker means 200 availability metrics...

        try
        {
            while (idleAvailabilityWorkers > 0 && probes < 5) // if probe is greater than 5 means metrics not present or worker not available
            {
                for (var pendingMetrics : pendingAvailabilityMetrics.values())
                {
                    if (idleAvailabilityWorkers > 0)
                    {
                        var iterator = pendingMetrics.iterator();

                        if (iterator.hasNext())
                        {
                            var metric = new HashMap<String, JsonObject>();

                            metrics.add(metric);

                            var count = 0;

                            while (count < batchSize && iterator.hasNext())
                            {
                                var id = iterator.next();

                                if (MetricCacheStore.getStore().queuedMetric(id))
                                {
                                    var event = MetricCacheStore.getStore().dequeue(id);

                                    if (EventCacheStore.getStore().validItem(event.getLong(EventBusConstants.EVENT_ID)))
                                    {
                                        count++;

                                        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                        // if two same ip polls collides because of any reason than we will ping for any one & equivalent result will be sent
                                        if (metric.containsKey(event.getString(OBJECT_IP)))
                                        {
                                            duplicateMetrics.computeIfAbsent(event.getString(OBJECT_IP), value -> new ArrayList<>());

                                            duplicateMetrics.get(event.getString(OBJECT_IP)).add(event);
                                        }
                                        else
                                        {
                                            metric.put(event.getString(OBJECT_IP), event);
                                        }
                                    }
                                }
                                else
                                {
                                    abort(id);
                                }

                                iterator.remove();
                            }

                            idleAvailabilityWorkers--;
                        }
                        else
                        {
                            probes++;
                        }
                    }
                    else
                    {
                        probes++;
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return metrics;
    }

    /**
     * Sends error information for multiple contexts.
     * <p>
     * This method iterates through a map of contexts and processes each one with the extract method,
     * which formats the error information and sends it. After processing each context, it removes
     * it from the map to free up memory and prevent duplicate processing.
     * <p>
     * The method determines the appropriate error message based on the error code:
     * - For PROCESS_TIMEOUT errors: Uses the PROCESS_TIMED_OUT message
     * - For other errors: Uses the INTERNAL_ERROR message
     *
     * @param contexts  A map of target IPs to JsonObject contexts to process
     * @param errorCode The error code to set for all contexts
     * @param error     The detailed error information (stack trace), can be null
     */
    private void send(Map<String, JsonObject> contexts, String errorCode, String error)
    {
        var iterator = contexts.keySet().iterator();

        while (iterator.hasNext())
        {
            extract(contexts.get(iterator.next()), errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? PROCESS_TIMED_OUT : INTERNAL_ERROR, errorCode, error);

            iterator.remove();
        }
    }

    /**
     * Processes error information and prepares it for sending.
     * <p>
     * This method:
     * 1. Formats error information into a standardized structure
     * 2. Sets appropriate status, error code, and message in the context
     * 3. Sends the error information using the send method
     * 4. Handles any duplicate metrics that need the same error information
     * <p>
     * The method creates a structured error object with:
     * - An ERRORS array containing detailed error information
     * - An ERROR field with stack trace (if available)
     * - A STATUS field set to either STATUS_TIME_OUT or STATUS_FAIL based on the error code
     * - An ERROR_CODE field with the specific error code
     * - A MESSAGE field with a formatted error message
     *
     * @param context   The JsonObject to update with error information
     * @param message   The error message to include
     * @param errorCode The error code to set
     * @param error     The detailed error information (stack trace), can be null
     */
    private void extract(JsonObject context, String message, String errorCode, String error)
    {
        if (error == null)
        {
            context.put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(MESSAGE, message).put(ERROR_CODE, errorCode)));
        }
        else
        {
            context.put(ERROR, error).put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR, error).put(ERROR_CODE, errorCode).put(MESSAGE, message)));
        }

        send(context.put(STATUS, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL)
                .put(ERROR_CODE, errorCode)
                .put(MESSAGE, String.format(METRIC_POLL_FAILED, message)));

        if (context.containsKey(OBJECT_IP) && duplicateMetrics.containsKey(context.getString(OBJECT_IP)))
        {
            for (var index = 0; index < duplicateMetrics.get(context.getString(OBJECT_IP)).size(); index++)
            {
                send(duplicateMetrics.get(context.getString(OBJECT_IP)).get(index)
                        .put(STATUS, errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL)
                        .put(ERROR_CODE, errorCode).put(MESSAGE, String.format(METRIC_POLL_FAILED, message)));
            }
        }
    }

    /**
     * Sends metric polling results to the appropriate event bus address.
     * <p>
     * This method:
     * 1. Updates the event tracker with a response sent message
     * 2. Determines the appropriate event bus address based on the bootstrap type:
     * - For COLLECTOR or SECONDARY installation mode: Sends to EVENT_REMOTE with REMOTE_EVENT_PROCESSOR_TOPIC
     * - For other bootstrap types: Publishes to EVENT_METRIC_POLL_RESPONSE
     * <p>
     * The method handles the routing of metric polling results differently based on
     * the deployment architecture. In collector or secondary mode, results are sent
     * to a remote event processor; otherwise, they're published locally.
     *
     * @param event The JsonObject containing the metric polling results to send
     */
    private void send(JsonObject event)
    {
        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        if (Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()))
        {
            vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, event.put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC));
        }
        else
        {
            vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, event);
        }
    }

    /**
     * Handles metrics that have been deleted or disabled.
     * <p>
     * This method:
     * 1. Retrieves the deleted metric from the MetricCacheStore
     * 2. Removes the associated event from the EventCacheStore
     * 3. Logs information about the deleted metric
     * 4. Sends an abort message to the appropriate event bus address
     * <p>
     * The abort message includes:
     * - STATUS_ABORT status
     * - ERROR_CODE_NO_ITEM_FOUND error code
     * - A message explaining that the metric was deleted or disabled
     * <p>
     * In development mode, the message is published (broadcast to all consumers);
     * in production mode, it's sent to a single consumer.
     *
     * @param id The ID of the metric to abort
     */
    private void abort(long id)
    {
        var metric = MetricCacheStore.getStore().getDeletedMetric(id);

        EventCacheStore.getStore().deleteItem(metric.getLong(EventBusConstants.EVENT_ID));

        LOGGER.info(String.format("metric %s of object %s deleted, reason: Either metric deleted or disabled...", metric.getString(Metric.METRIC_NAME), metric.getString(AIOpsObject.OBJECT_NAME)));

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().publish(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE,
                    metric.put(GlobalConstants.STATUS, GlobalConstants.STATUS_ABORT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                            .put(GlobalConstants.MESSAGE, String.format("metric %s of object %s deleted, reason: Either metric deleted or disabled...", metric.getString(Metric.METRIC_NAME), metric.getString(AIOpsObject.OBJECT_NAME))));
        }
        else
        {
            vertx.eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_METRIC_POLL_RESPONSE,
                    metric.put(GlobalConstants.STATUS, GlobalConstants.STATUS_ABORT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                            .put(GlobalConstants.MESSAGE, String.format("metric %s of object %s deleted, reason: Either metric deleted or disabled...", metric.getString(Metric.METRIC_NAME), metric.getString(AIOpsObject.OBJECT_NAME))));
        }
    }

    private int dequeue(Category category, List<Category> metricCategories, Map<Category, Integer> pendingMetricsByCategory, List<Map<Long, JsonObject>> batches, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var result = 0;

        try
        {
            var pendingMetrics = idleWorkersByCategory.get(category) > 0 && idleMetricWorkers.get() > 0 ? pluginEngine == PluginEngineConstants.PluginEngine.GO ? this.batchMetricsByCategory.get(category) : this.metricsByCategory.get(category) : null;

            if (pendingMetrics != null && !pendingMetrics.isEmpty())
            {
                pendingMetricsByCategory.put(category, dequeue(pendingMetrics, idleWorkersByCategory.get(category), category, batches, metricCategories, pluginEngine, category));

                result += pendingMetricsByCategory.get(category);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return result;
    }

    private void dequeue(List<Category> metricCategories, Map<Category, Integer> pendingMetricsByCategory, Map.Entry<Category, Integer> entry1, Map.Entry<Category, Integer> entry2, List<Map<Long, JsonObject>> batches, PluginEngineConstants.PluginEngine pluginEngine)
    {
        try
        {
            var pendingMetrics = pluginEngine == PluginEngineConstants.PluginEngine.GO ? this.batchMetricsByCategory.get(entry1.getKey()) : this.metricsByCategory.get(entry1.getKey());

            if (pendingMetrics != null && !pendingMetrics.isEmpty())
            {
                pendingMetricsByCategory.put(entry1.getKey(), dequeue(pendingMetrics, entry2.getValue(), entry2.getKey(), batches, metricCategories, pluginEngine, entry1.getKey()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private int dequeue(Map<Byte, List<Long>> pendingMetrics, int pendingWorkers, Category category, List<Map<Long, JsonObject>> batches, List<Category> metricCategories, PluginEngineConstants.PluginEngine pluginEngine, Category qualifiedCategory)
    {
        var batchSize = pluginEngine == PluginEngineConstants.PluginEngine.GO ? MotadataConfigUtil.getMetricPollerBatchSize() : 1;

        var workers = 0;

        for (var type : MetricTimeoutValueType.values())
        {
            if (pendingWorkers > 0)
            {
                var assignedWorkers = WorkerUtil.getWorkers(pendingMetrics.get(type.getName()).size(), batchSize, pendingWorkers);

                if (assignedWorkers > 0)
                {
                    pendingWorkers -= assignedWorkers;

                    workers += assignedWorkers;
                }
            }
        }

        var allocations = 0;

        var usedWorkers = 0;

        while (idleMetricWorkers.get() > 0 && allocations < workers)
        {
            allocations++;

            if (qualify(batches, pendingMetrics, batchSize, qualifiedCategory))
            {
                usedWorkers++;

                metricCategories.add(category);
            }
        }

        idleMetricWorkers.set(idleMetricWorkers.get() - usedWorkers);

        idleWorkersByCategory.put(category, idleWorkersByCategory.get(category) - usedWorkers);

        return pendingMetrics.values().stream().map(List::size).reduce(0, Integer::sum);
    }

    private boolean qualify(List<Map<Long, JsonObject>> batches, Map<Byte, List<Long>> pendingMetrics, int batchSize, Category category)
    {
        var valid = false;

        // if batch size is 50 then sometimes full batch not qualified as some metrics ports are unconnected or metrics are deleted so this count will make sure that every time we get exact qualified metrics per batch
        var qualifications = 0; // to qualify exact batch size in one batch

        var batchMetrics = new HashMap<Long, JsonObject>();

        try
        {
            var iterator = pendingMetrics.get(metricTimeoutValueTypesByCategory.get(category).getName()).iterator();

            // update next metric timeout value type for qualifying metrics
            metricTimeoutValueTypesByCategory.put(category, NMSConstants.getIncrementedMetricTimeoutValueType(metricTimeoutValueTypesByCategory.get(category)));

            while (iterator.hasNext() && qualifications < batchSize)
            {
                var id = iterator.next();

                iterator.remove();

                if (MetricCacheStore.getStore().queuedMetric(id))
                {
                    var metric = MetricCacheStore.getStore().dequeue(id);

                    if (EventCacheStore.getStore().validItem(metric.getLong(EventBusConstants.EVENT_ID)))
                    {
                        EventBusConstants.updateEvent(metric.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                        qualifications++;

                        batchMetrics.put(metric.getLong(EventBusConstants.EVENT_ID), metric);
                    }
                }
                else
                {
                    // metric is deleted ... hence just aborting the task...
                    abort(id);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        if (qualifications > 0)
        {
            valid = true;

            batches.add(batchMetrics);
        }

        return valid;
    }

    private void dequeue(Map<Category, Integer> pendingMetricsByCategory, List<Category> metricCategories, List<Map<Long, JsonObject>> batchMetrics, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var sortedByWorkers = idleWorkersByCategory.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.Category, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        pendingMetricsByCategory.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.Category, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet()
                .stream()
                .takeWhile(entry1 -> idleMetricWorkers.get() > 0)
                .forEach(entry1 -> sortedByWorkers.entrySet()
                        .stream()
                        .takeWhile(entry2 -> idleMetricWorkers.get() > 0)
                        .forEach(entry2 -> dequeue(metricCategories, pendingMetricsByCategory, entry1, entry2, batchMetrics, pluginEngine)));
    }

    /**
     * Initializes worker allocation for different metric categories.
     * <p>
     * This method:
     * 1. Sets the total number of idle metric workers based on configuration
     * 2. Allocates workers to each metric category based on their relative importance
     * 3. Initializes data structures for tracking metrics by category and timeout value type
     * <p>
     * The worker allocation follows a percentage-based distribution defined in NMSConstants,
     * where categories like Network and Server receive more workers than less critical
     * categories like Cloud or Custom.
     * <p>
     * This method also initializes the metric queues for both regular and batch metrics,
     * organizing them by category and timeout value type for efficient scheduling.
     */
    private void setWorkers()
    {
        var workers = MotadataConfigUtil.getWorkers();

        idleMetricWorkers.set(MotadataConfigUtil.getMetricPollerWorkers() * workers);

        for (var category : NMSConstants.Category.values())
        {
            var pollingWorkers = NMSConstants.getPollerWorkersByCategory(category);

            if (pollingWorkers > 0)
            {
                pollingWorkers *= workers;

                idleWorkersByCategory.put(category, pollingWorkers);
            }

            metricsByCategory.put(category, new HashMap<>());

            batchMetricsByCategory.put(category, new HashMap<>());

            metricTimeoutValueTypesByCategory.put(category, MetricTimeoutValueType.SMALL);

            for (var type : MetricTimeoutValueType.values())
            {
                metricsByCategory.get(category).put(type.getName(), new ArrayList<>());

                batchMetricsByCategory.get(category).put(type.getName(), new ArrayList<>());
            }
        }
    }

    /**
     * Cleans up resources when the MetricPoller verticle is stopped.
     * <p>
     * This method closes all worker executors to ensure proper resource cleanup:
     * - Regular metric worker executors
     * - Availability/ping check worker executors
     * - Network service worker executors
     * <p>
     * Closing the worker executors ensures that any pending or in-progress tasks
     * are properly completed or terminated, and that thread resources are released.
     *
     * @param promise A Promise that will be completed when cleanup is finished
     * @throws Exception If an error occurs during cleanup
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        metricWorkerExecutors.forEach(WorkerExecutor::close);

        availabilityWorkerExecutors.forEach(WorkerExecutor::close);

        networkServiceWorkerExecutors.forEach(WorkerExecutor::close);

        promise.complete();
    }
}
