/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.store.LogParserConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.ErrorMessageConstants.LOG_FILE_SIZE_LIMIT_EXCEEDED;
import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.GlobalConstants.PLUGIN_ID;
import static com.mindarray.TestAPIConstants.LOG_PARSER_API_ENDPOINT;
import static com.mindarray.api.LogParser.LOG_PARSER_FIELDS;
import static com.mindarray.api.LogParser.LOG_PARSER_LOG_POSITIONS;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.DELETE_EVENT_SOURCE;
import static org.apache.http.HttpStatus.SC_OK;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestLogParser
{
    public static final String SESSION_ID = UUID.randomUUID().toString().toLowerCase().trim();
    private static final String UPLOAD_DIR = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR;
    private static final Logger LOGGER = new Logger(TestLogParser.class, MOTADATA_API, "Test Log Parser");
    private static long id;
    private static MessageConsumer<byte[]> eventDBWriteTestConsumer;
    private static MessageConsumer<JsonObject> messageConsumer;
    private long timerId;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        TestUtil.vertx().fileSystem().writeFileBlocking(UPLOAD_DIR + "e7c1548a-8928-4712-92eb-47c49ff89abe", Buffer.buffer(
                new JsonObject("{\"event.source\":\"************\",\"event.received.time\":1723787682,\"event.category\":\"Other\",\"plugin.id\":500009,\"event.source.type\":\"Other\",\"event.timestamp\":1723787682,\"event.volume.bytes\":154}").encode() + "\n" +
                        new JsonObject("{\"event.source\":\"************\",\"event.received.time\":1723787685,\"event.category\":\"Other\",\"plugin.id\":500009,\"event.source.type\":\"Other\",\"event.timestamp\":1723787685,\"event.volume.bytes\":176}").encode() + "\n" +
                        new JsonObject("{\"event.source\":\"************\",\"event.received.time\":1723787688,\"event.category\":\"Other\",\"plugin.id\":500009,\"event.source.type\":\"Other\",\"event.timestamp\":1723787693,\"event.volume.bytes\":120}").encode()
        ));

        TestUtil.vertx().fileSystem().writeFileBlocking(UPLOAD_DIR + "53530-52a9-44bf-90af-d07df37d004a", Buffer.buffer("""
                <134>1 2024-06-19T15:33:54.144688+05:30 localhost dnsmasq - - - Jun 19 15:33:54 dnsmasq[2059]: query[PTR] *************.in-addr.arpa from 127.0.0.1"""));

        TestUtil.vertx().fileSystem().writeFileBlocking(UPLOAD_DIR + "53530-52a9-44bf-90af-d07df37regex", Buffer.buffer("""
                [Mon Feb 27 15:40:12.434632 2017] [mpm_winnt:notice] [pid 2420:tid 332] mod_authz_core.c(820): [client **********:50752] AH00418: Parent: Created child process 2736
                [Mon Feb 27 15:40:12.746632 2017] [auth_digest:notice] [pid 2736:tid 152] mod_authz_core.c(820): [client **********:50752] AH01757: generating secret for digest authentication ...
                [Mon Feb 27 15:40:12.793432 2017] [mpm_winnt:notice] [pid 2736:tid 152] mod_authz_core.c(820): [client **********:50752] AH00354: Child: Starting 64 worker threads.
                [Tue Feb 28 10:15:51.907923 2017] [mpm_winnt:notice] [pid 2420:tid 332] mod_authz_core.c(820): [client **********:50752] AH00422: Parent: Received shutdown signal -- Shutting down the server.
                [Tue Feb 28 10:15:58.756335 2017] [mpm_winnt:notice] [pid 2736:tid 152] mod_authz_core.c(820): [client **********:50752] AH00364: Child: All worker threads have exited.
                [Tue Feb 28 10:16:17.257968 2017] [mpm_winnt:notice] [pid 2420:tid 332] mod_authz_core.c(820): [client **********:50752] AH00430: Parent: Child process 2736 exited successfully."""));

        var buffer = Buffer.buffer("");

        for (var index = 0; index <= 8000; index++)
        {
            buffer.appendString("[Mon Feb 27 15:40:12.341031 2017] [auth_digest:notice] [pid 2420:tid 332] AH01757: generating secret for digest authentication");
        }

        TestUtil.vertx().fileSystem().writeFileBlocking(UPLOAD_DIR + "size1MbLogFile", buffer);

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40.145").put(EventBusConstants.EVENT, EVENT_LOG).put(GlobalConstants.PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "************").put(EventBusConstants.EVENT, EVENT_LOG).put(GlobalConstants.PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "************").put(EventBusConstants.EVENT, EVENT_LOG).put(GlobalConstants.PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (eventDBWriteTestConsumer != null)
        {
            eventDBWriteTestConsumer.unregister();
        }

        ObjectConfigStore.getStore().initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("reinitialized the object config store...");

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(result.cause());

                LOGGER.warn("unable to initialize object config store");
            }
        });
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        var futures = new ArrayList<Future<Void>>();

        if (eventDBWriteTestConsumer != null)
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            eventDBWriteTestConsumer.unregister(result -> promise.complete());
        }

        if (messageConsumer != null)
        {
            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            messageConsumer.unregister(result -> promise.complete());
        }

        Future.join(futures).onComplete(result -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testParseLog1(VertxTestContext testContext) throws InterruptedException
    {
        var event = new JsonObject("{\"log.parser.event\":\"<86>Feb  8 14:31:21 localhost sshd[4623]: Failed password for invalid user mindarray from ************* port 59682 ssh2\",\"log.parser.type\":\"regex\",\"log.parser.fields\":[],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.log.positions\":[]}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_PATTERN_DETECT.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    var fields = result.getJsonArray(LOG_PARSER_FIELDS);

                    Assertions.assertNotNull(fields);

                    Assertions.assertEquals(1, fields.size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testParseLog2(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var event = new JsonObject("{\"log.parser.name\":\"test\",\"log.parser.event\":\"<38>Feb  3 16:11:43 mindarray-pc7 sshd[29640]: input_userauth_request: invalid user mindrray [preauth]\",\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[],\"log.parser.type\":\"custom\",\"log.parser.plugin\":10000000000004,\"log.parser.source.type\":\"Linux\"}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_PATTERN_DETECT.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    var fields = result.getJsonArray(LOG_PARSER_FIELDS);

                    Assertions.assertNotNull(fields);

                    Assertions.assertEquals(8, fields.size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));
        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_PATTERN_DETECT, event.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testParseLog3(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{\"log.parser.name\":\"vCenter\",\"log.parser.event\":\"<134>1 2024-06-19T15:33:54.144688+05:30 localhost dnsmasq - - - Jun 19 15:33:54 dnsmasq[2059]: query[PTR] *************.in-addr.arpa from 127.0.0.1\",\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":1729605168,\"log.parser.field.name\":\"event.received.time\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"134\",\"log.parser.field.name\":\"temp.priority\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"1\",\"log.parser.field.name\":\"temp.version\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":1718791434,\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"localhost\",\"log.parser.field.name\":\"hostname\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"dnsmasq\",\"log.parser.field.name\":\"application.name\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"localuse0\",\"log.parser.field.name\":\"facility\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"Informational\",\"log.parser.field.name\":\"severity\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"custom\",\"log.parser.plugin\":10000000000018,\"log.parser.source.type\":\"vCenter\",\"sample.file\":\"53530-52a9-44bf-90af-d07df37d004a\",\"maximum.events\":10}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertEquals(1, result.getJsonArray("parsing.statuses").size());

                    Assertions.assertEquals(9, result.getJsonObject("parsed.fields").size());

                    Assertions.assertEquals(1, result.getJsonArray("events").size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testParseLog4(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{\"log.parser.name\":\"Apache Error Log\",\"log.parser.event\":\"[Fri Feb 01 22:03:08.318615 2019] [authz_core:debug] [pid 9:tid 140597881775872] mod_authz_core.c(820): [client **********:50752] AH01626: authorization result of Require all granted: granted\",\"log.parser.log.positions\":[{\"start.position\":1,\"end.position\":32},{\"start.position\":46,\"end.position\":51},{\"start.position\":112,\"end.position\":122},{\"start.position\":130,\"end.position\":191}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"Fri Feb 01 22:03:08.318615 2019\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"debug\",\"log.parser.field.name\":\"severity\",\"log.parser.field.type\":\"none\"},{\"log.parser.field.value\":\"**********\",\"log.parser.field.name\":\"client.ip\",\"log.parser.field.type\":\"ip\"},{\"log.parser.field.value\":\"AH01626: authorization result of Require all granted: grante\",\"log.parser.field.name\":\"log.message\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"Apache HTTP\",\"regex\":\"^\\\\[(\\\\w+ \\\\w+ \\\\d+ \\\\d+:\\\\d+:\\\\d+(?:.\\\\d+)?\\\\s+\\\\d+)\\\\](?:\\\\s+)?\\\\[(?:\\\\w+\\\\:)?(\\\\w+)\\\\]\\\\s+\\\\[(?:\\\\w+\\\\s+\\\\d+(?::)?)?(?:\\\\w+\\\\s+\\\\d+)?\\\\](?:\\\\s+\\\\S+:)?\\\\s+(?:\\\\[client\\\\s+([\\\\w+.]+):\\\\d+\\\\])?(.*)\",\"sample.file\":\"53530-52a9-44bf-90af-d07df37regex\",\"maximum.events\":100}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE.equals(message.body().getString(EVENT_TYPE)))
                {

                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertEquals(6, result.getJsonArray("parsing.statuses").size());

                    Assertions.assertEquals(5, result.getJsonObject("parsed.fields").size());

                    Assertions.assertEquals(6, result.getJsonArray("events").size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testUploadSampleLogFile(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{\"sample.file\":\"53530-52a9-44bf-90af-d07df37d004a\",\"id\":10000000000027}");

        assertLogParserTestResult(testContext);

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_FILE_UPLOAD, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testValidateSampleLogFile(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject("{\"sample.file\": \"53530-52a9-44bf-90af-d07df37d004a\"}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, result.getString(GlobalConstants.STATUS));

                    Assertions.assertEquals(1, result.getJsonArray(GlobalConstants.RESULT).size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                exception.printStackTrace();

                testContext.completeNow();
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    public void testProcessSampleLogFileInvalidSize(VertxTestContext testContext) throws InterruptedException
    {
        var context = new JsonObject("{\"sample.file\": \"size1MbLogFile\"}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertEquals(GlobalConstants.STATUS_FAIL, result.getString(GlobalConstants.STATUS));

                    Assertions.assertEquals(LOG_FILE_SIZE_LIMIT_EXCEEDED, result.getString(GlobalConstants.MESSAGE));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                exception.printStackTrace();

                testContext.completeNow();
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_FILE_READ, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    public void testCreateLogParser1(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"log.parser.name\":\"TestCreateLogParser\",\"log.parser.event\":\"<38>Feb  3 16:11:43 mindarray-pc7 sshd[29640]: input_userauth_request: invalid user mindrray [preauth]\",\"log.parser.log.positions\":[{\"start.position\":4,\"end.position\":19},{\"start.position\":47,\"end.position\":102}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"Feb  3 16:11:43\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"input_userauth_request: invalid user mindrray [preauth\",\"log.parser.field.name\":\"log.message\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"Linux\",\"regex\":\"^\\\\<\\\\d+\\\\>(\\\\w+\\\\s+\\\\d+\\\\s+\\\\d+:\\\\d+:\\\\d+)(?:[^\\\\:]*\\\\:){1}\\\\ (.*)\",\"log.parser.upload\":\"no\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser created successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertNotNull(LogParserConfigStore.getStore().getItemByValue(LogParser.LOG_PARSER_NAME, "TestCreateLogParser"));

            id = response.bodyAsJsonObject().getLong(GlobalConstants.ID);   // to delete this parser in delete-test-case

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    public void testDeleteLogParser(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.delete(LOG_PARSER_API_ENDPOINT + "/" + id, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser deleted successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertNull(LogParserConfigStore.getStore().getItemByValue(LogParser.LOG_PARSER_NAME, "TestCreateLogParser"));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    public void testAssignLogParser1(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"params\":[\"10.20.40.145\"]}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000004/assign", context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser assigned successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            // validate Log Parser Config Store

            var entities = LogParserConfigStore.getStore().getItemByValue(GlobalConstants.ID, 10000000000004L).getJsonArray(LogParser.LOG_PARSER_ENTITIES);

            context.getJsonArray(APIConstants.REQUEST_PARAMS).forEach(e -> Assertions.assertTrue(entities.contains(e), "should contain source " + e));

            // validate Event Object Store
            var retries = new AtomicInteger(3);

            timerId = TestUtil.vertx().setPeriodic(3000, timer ->
            {

                var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "10.20.40.145");

                if (item.getJsonArray(LogEngineConstants.SOURCE_GROUPS).contains(10000000000019L))
                {
                    Assertions.assertTrue(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS).contains(10000000000019L), item.getJsonArray(LogEngineConstants.SOURCE_GROUPS) + " should contain group 10000000000019");

                    Assertions.assertTrue(item.getJsonArray(PLUGIN_ID).contains(600004), item.getJsonArray(PLUGIN_ID) + " should contain plugin id 600004");

                    TestUtil.vertx().cancelTimer(timerId);

                    testContext.completeNow();
                }
                else
                {
                    if (retries.decrementAndGet() <= 0)
                    {
                        TestUtil.vertx().cancelTimer(timerId);

                        testContext.failNow("max attempt exceeded..");
                    }
                }
            });

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    public void testAssignLogParser2(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"params\":[\"10.20.40.145\",\"************\",\"************\"]}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000004/assign", context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser assigned successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            // validate Log Parser Config Store

            var entities = LogParserConfigStore.getStore().getItemByValue(GlobalConstants.ID, 10000000000004L).getJsonArray(LogParser.LOG_PARSER_ENTITIES);

            context.getJsonArray(APIConstants.REQUEST_PARAMS).forEach(e -> Assertions.assertTrue(entities.contains(e), "should contain source " + e));

            // validate Event Object Store

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            var retries = new AtomicInteger(3);

            var sources = new AtomicInteger(0);

            TestUtil.vertx().setPeriodic(5000, timer ->
            {
                sources.set(0);

                for (var object : context.getJsonArray(APIConstants.REQUEST_PARAMS))
                {
                    var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, object.toString());

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": item: " + (item != null ? item.encode() : null));

                    if (item != null && item.getJsonArray(LogEngineConstants.SOURCE_GROUPS).contains(10000000000019L))
                    {

                        Assertions.assertTrue(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS).contains(10000000000019L), item.getJsonArray(LogEngineConstants.SOURCE_GROUPS) + " should contain group 10000000000019");

                        Assertions.assertTrue(item.getJsonArray(PLUGIN_ID).contains(600004), item.getJsonArray(PLUGIN_ID) + " should contain plugin id 600004");

                        if (sources.incrementAndGet() == 3)
                        {
                            testContext.completeNow();
                        }
                    }
                }

                if (retries.decrementAndGet() <= 0)
                {
                    testContext.failNow("max attempt exceeded..");
                }
            });


        })));

        Assertions.assertTrue(testContext.awaitCompletion(20, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    public void testGetLogParserReferences(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.get("/api/v1/settings/log-parsers/10000000000004/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getJsonArray(APIConstants.Entity.EVENT_SOURCE.getName()).getJsonObject(0).getString(EVENT_SOURCE)): " + EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getJsonArray(APIConstants.Entity.EVENT_SOURCE.getName()).getJsonObject(0).getString(EVENT_SOURCE)).encode());

            Assertions.assertNotNull(EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getJsonArray(APIConstants.Entity.EVENT_SOURCE.getName()).getJsonObject(0).getString(EVENT_SOURCE)));

            Assertions.assertEquals(EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "10.20.40.145").getJsonArray(LogEngineConstants.SOURCE_GROUPS), response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getJsonArray(APIConstants.Entity.EVENT_SOURCE.getName()).getJsonObject(0).getJsonArray(LogEngineConstants.SOURCE_GROUPS));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13) // #24915
    public void testGetLogParserReferencesFilterByCategory(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        try
        {
            var field = ObjectConfigStore.getStore().getClass().getSuperclass().getDeclaredField("items");

            field.setAccessible(true);

            var items = (Map<Long, JsonObject>) field.get(ObjectConfigStore.getStore());

            items.put(8487525243206L, new JsonObject("{\"object.ip\":\"************\",\"object.type\":\"Ping\",\"object.category\":\"Service Check\",\"_type\":\"1\",\"user.name\":\"admin\",\"object.discovery.method\":\"REMOTE\",\"object.state\":\"ENABLE\",\"object.host\":\"************\",\"object.name\":\"test\",\"object.context\":{\"ping.check.retries\":1},\"object.user.tags\":[],\"ui.event.uuid\":\"09a272d4-f951-4c63-9448-6f766dd7e8a5\",\"object.target\":\"************\",\"remote.address\":\"0:0:0:0:0:0:0:1\",\"id\":8487525243206,\"object.creation.time\":\"2022/12/14 12:26:36\",\"object.business.hour.profile\":10000000000001,\"object.id\":2}"));

            items.put(8487525243207L, new JsonObject("{\"object.ip\":\"************\",\"object.type\":\"Linux\",\"object.category\":\"Server\",\"_type\":\"1\",\"user.name\":\"admin\",\"object.discovery.method\":\"REMOTE\",\"object.state\":\"ENABLE\",\"object.host\":\"************\",\"object.name\":\"test\",\"object.context\":{\"ping.check.retries\":1},\"object.user.tags\":[],\"ui.event.uuid\":\"09a272d4-f951-4c63-9448-6f766dd7e8a5\",\"object.target\":\"************\",\"remote.address\":\"0:0:0:0:0:0:0:1\",\"id\":8487525243207,\"object.creation.time\":\"2022/12/14 12:26:40\",\"object.business.hour.profile\":10000000000001,\"object.id\":3}"));

            field.set(ObjectConfigStore.getStore(), items);
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        TestAPIUtil.get("/api/v1/settings/log-parsers/10000000000004/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            LOGGER.info("log parser: 10000000000004: " + LogParserConfigStore.getStore().getItem(10000000000004L));

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            var objects = response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getJsonArray(APIConstants.Entity.OBJECT.getName());

            //"Service check" will not qualify as object category but "Server" will qualify for the category.
            for (var index = 0; index < objects.size(); index++)
            {
                var item = objects.getJsonObject(index);

                if (item.containsKey(AIOpsObject.OBJECT_CATEGORY))
                {
                    Assertions.assertNotEquals(NMSConstants.Category.SERVICE_CHECK.getName(), item.getString(AIOpsObject.OBJECT_CATEGORY));
                }

                if (item.containsKey(AIOpsObject.OBJECT_IP) && "************".equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_IP)))
                {
                    Assertions.assertEquals(NMSConstants.Category.SERVER.getName(), item.getString(AIOpsObject.OBJECT_CATEGORY));
                }
            }

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    public void testUnassignLogParser(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"params\":[\"************\",\"************\"]}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000004/unassign", context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser unassigned successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            // validate Log Parser Config Store

            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            var entities = LogParserConfigStore.getStore().getItemByValue(GlobalConstants.ID, 10000000000004L).getJsonArray(LogParser.LOG_PARSER_ENTITIES);

            context.getJsonArray(APIConstants.REQUEST_PARAMS).forEach(e -> Assertions.assertFalse(entities.contains(e), "should not contain source " + e));

            // validate Event Object Store

            for (var object : context.getJsonArray(APIConstants.REQUEST_PARAMS))
            {
                var item = EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, object.toString());

                Assertions.assertTrue(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS).contains(10000000000019L), "should contain group 10000000000019"); // after unassigned, as per the flow we are not deleting the group
            }

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(15, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    public void testAssignLogParserInvalidSource(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"params\":[\"10.20.40.145\"]}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/14141414141414/assign", context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Item Log Parser not found", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertEquals(ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, response.bodyAsJsonObject().getString(GlobalConstants.ERROR_CODE));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    public void testUnassignLogParserInvalidSource(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"params\":[\"************\",\"************\"]}");

        TestAPIUtil.put("/api/v1/settings/log-parsers/14141414141414/unassign", context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_FAIL, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Item Log Parser not found", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertEquals(ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, response.bodyAsJsonObject().getString(GlobalConstants.ERROR_CODE));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    public void testGetAllLogParser(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.get(LOG_PARSER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertFalse(response.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).isEmpty(), "should have at least 1 default parser");

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    public void testCreateLogParser2(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"log.parser.name\":\"TestCreateLogParser\",\"event.source\":\"10.20.40.68\",\"log.parser.event\":\"<38>Feb  3 16:11:43 mindarray-pc7 sshd[29640]: input_userauth_request: invalid user mindrray [preauth]\",\"log.parser.log.positions\":[{\"start.position\":4,\"end.position\":19},{\"start.position\":47,\"end.position\":102}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"Feb  3 16:11:43\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"},{\"log.parser.field.value\":\"input_userauth_request: invalid user mindrray [preauth\",\"log.parser.field.name\":\"log.message\",\"log.parser.field.type\":\"none\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"Linux\",\"regex\":\"^\\\\<\\\\d+\\\\>(\\\\w+\\\\s+\\\\d+\\\\s+\\\\d+:\\\\d+:\\\\d+)(?:[^\\\\:]*\\\\:){1}\\\\ (.*)\",\"log.parser.upload\":\"no\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser created successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertNotNull(LogParserConfigStore.getStore().getItemByValue(LogParser.LOG_PARSER_NAME, "TestCreateLogParser"));

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    public void testCreateLogParser3(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var context = new JsonObject("{\"log.parser.name\":\"test_create_and_upload\",\"log.parser.event\":\"<85>Feb  3 11:26:44 mindarray-pc7 unix_chkpwd[21237]: password check failed for user (mindarray)\",\"log.parser.log.positions\":[{\"start.position\":4,\"end.position\":19}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"Feb  3 11:26:44\",\"log.parser.field.name\":\"timestamp\",\"log.parser.field.type\":\"timestamp\"}],\"log.parser.type\":\"regex\",\"log.parser.source.type\":\"SNMP Device\",\"regex\":\"^\\\\<\\\\d+\\\\>([JFMAMJSONDjfmamjsond]\\\\w+\\\\s{2}\\\\d{1,2}\\\\s\\\\d{1,2}:\\\\d{1,2}:\\\\d{1,2})\",\"sample.file\":\"53530-52a9-44bf-90af-d07df37d004a\",\"log.parser.upload\":\"yes\",\"log.parser.date.time.format\":\"MMM  dd HH:mm:ss\"}");

        TestAPIUtil.post(LOG_PARSER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, response.bodyAsJsonObject().getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Log Parser created successfully", response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

            Assertions.assertNotNull(LogParserConfigStore.getStore().getItemByValue(LogParser.LOG_PARSER_NAME, "test_create_and_upload"));

            testContext.completeNow();

        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    public void testDeregister(VertxTestContext testContext) throws InterruptedException
    {
        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40.145").put(EventBusConstants.EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "************").put(EventBusConstants.EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "************").put(EventBusConstants.EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        var retries = new AtomicInteger(3);

        TestUtil.vertx().setPeriodic(3000, timer ->
        {
            if (EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "10.20.40.145") == null &&
                    EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "************") == null &&
                    EventSourceConfigStore.getStore().getItemByValue(EventBusConstants.EVENT_SOURCE, "************") == null)
            {
                testContext.completeNow();
            }
            else
            {
                if (retries.decrementAndGet() <= 0)
                {
                    testContext.failNow("max attempt exceeded..event-source should be deregistered");
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    public void testParseLogCustomRegex(VertxTestContext testContext) throws InterruptedException
    {
        var event = new JsonObject("{\"log.parser.type\":\"regex\",\"log.parser.fields\":[],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.log.positions\":[],\"regex\":\"(?:<(\\\\d+)>)?((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\\\s.*\\\\d+\\\\s+\\\\d+:\\\\d+:\\\\d+)\\\\s([a-zA-Z0-9\\\\-#\\\\.\\\\(\\\\)\\\\/%&\\\\s]{1,})\\\\s([\\\\w\\\\-]*)(?:[\\\\[\\\\-\\\\/:(](\\\\d+)[\\\\]\\\\-:)\\\\/])?(?:[\\\\[\\\\-:(])?(.*)\"}").put("log.parser.event", "<6>Aug 14 13:08:30 deven-Latitude-3420 kernel: [ 8086.164056] ish-hid {33AECD58-B679-4E54-9BD9-A04D34F0C226}: [hid-ish]: enum_devices_done OK, num_hid_devices=1");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_PATTERN_PARSE.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    var fields = result.getJsonArray(LOG_PARSER_FIELDS);

                    Assertions.assertNotNull(fields);

                    Assertions.assertEquals(5, fields.size());

                    Assertions.assertEquals(5, result.getJsonArray(LOG_PARSER_LOG_POSITIONS).size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_PATTERN_PARSE, event.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    public void testParseJSONLogFileFieldAnalytics(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{\"log.parser.name\":\"test\",\"log.parser.event\":\"{\\\"event.source\\\":\\\"************\\\",\\\"event.received.time\\\":1723787688,\\\"event.category\\\":\\\"Other\\\",\\\"plugin.id\\\":500009,\\\"event.source.type\\\":\\\"Other\\\",\\\"event.timestamp\\\":1723787693,\\\"event.volume.bytes\\\":130}\",\"log.parser.log.positions\":[\"event.source\",\"event.received.time\",\"event.category\",\"plugin.id\",\"event.source.type\",\"event.timestamp\",\"event.volume.bytes\"],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.fields\":[{\"log.parser.field.value\":\"************\",\"log.parser.field.name\":\"event.source\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"1723787688\",\"log.parser.field.name\":\"event.received.time\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"Other\",\"log.parser.field.name\":\"event.category\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"500009\",\"log.parser.field.name\":\"plugin.id\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"Other\",\"log.parser.field.name\":\"event.source.type\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"1723787693\",\"log.parser.field.name\":\"event.timestamp\",\"log.parser.field.type\":\"Any\"},{\"log.parser.field.value\":\"130\",\"log.parser.field.name\":\"event.volume.bytes\",\"log.parser.field.type\":\"Any\"}],\"log.parser.type\":\"json\",\"log.parser.source.type\":\"SNMP Device\",\"sample.file\":\"e7c1548a-8928-4712-92eb-47c49ff89abe\",\"maximum.events\":100}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE.equals(message.body().getString(EVENT_TYPE)))
                {
                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertEquals(3, result.getJsonArray("parsing.statuses").size());

                    Assertions.assertEquals(8, result.getJsonObject("parsed.fields").size());

                    Assertions.assertEquals(3, result.getJsonArray("events").size());

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_SAMPLE_LOG_PARSE, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    private void assertLogParserTestResult(VertxTestContext testContext)
    {
        eventDBWriteTestConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), message -> testContext.verify(() ->
        {
            try
            {
                var result = TestUtil.decodeEventBufferSingleRow(Buffer.buffer(message.body()));

                if (result.getString(PLUGIN_ID).equalsIgnoreCase("600028-vmware.vcenter") && result.getString("source").equalsIgnoreCase("127.0.0.1"))
                {
                    testContext.completeNow();
                }

            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    public void testParseLog5(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        var context = new JsonObject("{\"log.parser.date.time.format\":\"MMMddHH:mm:ss\",\"log.parser.event\":\"<86>Aug 17 15:10:52 aiops9242 sshd[3785191]: pam_unix(sshd:session): session closed for user motadata\",\"log.parser.type\":\"regex\",\"log.parser.fields\":[{\"value\":\"Aug1715:10:52\",\"name\":\"timestamp\",\"type\":\"timestamp\",\"guid\":\"cc21287a-561d-404e-84df-6cac89cc2afd\"},{\"value\":\"session\",\"name\":\"Field2\",\"type\":\"none\",\"guid\":\"ffb795c4-f416-474a-8c71-51d166e801d6\"}],\"log.parser.condition.keywords\":[],\"log.parser.condition\":\"all\",\"log.parser.log.positions\":[{\"start.position\":4,\"end.position\":19},{\"start.position\":59,\"end.position\":66},{\"start.position\":77,\"end.position\":83}],\"regex\":\"^\\\\<\\\\d+\\\\>([JFMAMJSONDjfmamjsond]\\\\w+\\\\s\\\\d{1,2}\\\\s\\\\d{1,2}:\\\\d{1,2}:\\\\d{1,2})(?:[^\\\\:]*\\\\:){2}([^\\\\)]*)\",\"session-id\":\"296d0016-bc30-4772-9e1f-e641aa03f264\",\"user.name\":\"admin\"}");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + SESSION_ID, message -> testContext.verify(() ->
        {
            try
            {
                if (UI_ACTION_LOG_PARSER_PATTERN_DETECT.equals(message.body().getString(EVENT_TYPE)))
                {

                    var result = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT)).getJsonObject(GlobalConstants.RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertEquals(3, result.getJsonArray("log.parser.fields").size());

                    for (var i = 0; i < result.getJsonArray("log.parser.fields").size(); i++)
                    {
                        var parsedField = result.getJsonArray("log.parser.fields").getJsonObject(i);

                        if (parsedField.getString("log.parser.field.value").equalsIgnoreCase("closed"))
                        {
                            testContext.completeNow();
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        }));

        TestUtil.vertx().eventBus().send(UI_ACTION_LOG_PARSER_PATTERN_DETECT, context.put(APIConstants.SESSION_ID, SESSION_ID));

        Assertions.assertTrue(testContext.awaitCompletion(30, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}
