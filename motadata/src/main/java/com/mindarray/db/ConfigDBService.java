/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.util.MotadataConfigUtil;
import io.vertx.codegen.annotations.Fluent;
import io.vertx.codegen.annotations.GenIgnore;
import io.vertx.codegen.annotations.ProxyGen;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.sqlclient.Pool;

@ProxyGen
public interface ConfigDBService
{

    @GenIgnore
    static void create(Vertx vertx, Pool db, Handler<AsyncResult
            <ConfigDBService>> handler)
    {
        new ConfigDBServiceImpl(vertx, db, handler);
    }

    @GenIgnore
    static ConfigDBService createProxy(Vertx vertx, String address)
    {
        return new ConfigDBServiceVertxEBProxy(vertx, address, new DeliveryOptions().setSendTimeout(MotadataConfigUtil.getDBServiceTimeoutMillis()));
    }

    /**
     * NoSQL-based database operations for record storage and retrieval.
     *
     * <p>This section contains methods that handle record-oriented database operations
     * where the primary goal is to store complete JsonObject records. The storage model
     * uses a simple key-value approach with an auto-generated ID as the primary key and
     * a column containing the entire JsonObject record.</p>
     *
     * <p>These methods are designed for scenarios where:</p>
     * <ul>
     *   <li>Schema flexibility is required</li>
     *   <li>Records need to be stored and retrieved as complete units</li>
     *   <li>Complex nested data structures need to be preserved</li>
     *   <li>Rapid prototyping and development is prioritized</li>
     * </ul>
     *
     * <p>All operations support audit logging with user and remote IP tracking
     * for compliance and security purposes.</p>
     */

    @Fluent
    ConfigDBService save(String table, JsonObject record, String user, String remoteIP,
                         Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService saveAll(String table, JsonArray records, String user, String remoteIP,
                            Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService get(String table, JsonObject filter,
                        Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService getById(String table, long id,
                            Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getOne(String table,
                           Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getOneByQuery(String table, JsonObject filter,
                                  Handler<AsyncResult<JsonObject>> handler);

    @Fluent
    ConfigDBService getAll(String table,
                           Handler<AsyncResult<JsonArray>> handler);


    @Fluent
    ConfigDBService delete(String table, JsonObject filter, String user, String remoteIP,
                           Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService deleteAll(String table, JsonObject filter, String user, String remoteIP,
                              Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService drop(String table, Handler<AsyncResult<Void>> handler);

    @Fluent
    ConfigDBService update(String table, JsonObject filter, JsonObject record, String user, String remoteIP,
                           Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService upsert(String table, JsonObject record, String user, String remoteIP,
                           Handler<AsyncResult<Long>> handler);

    @Fluent
    ConfigDBService upsertAll(String table, JsonArray records, String user, String remoteIP,
                              Handler<AsyncResult<JsonArray>> handler);

    @Fluent
    ConfigDBService updateAll(String table, JsonObject filter, JsonObject record, String user, String remoteIP,
                              Handler<AsyncResult<JsonArray>> handler);
}
