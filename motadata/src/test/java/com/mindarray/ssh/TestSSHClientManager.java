/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.ssh;

import com.mindarray.ErrorCodes;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.config.ConfigConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.ArrayList;
import java.util.UUID;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.SSH_CONNECTION_CLOSED;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.config.SSHClientManager.SSH_OPERATION;
import static com.mindarray.config.SSHClientManager.SSH_OUTPUT;
import static com.mindarray.eventbus.EventBusConstants.*;

@ExtendWith(VertxExtension.class)
@Timeout(20 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSSHClientManager
{

    private static final Logger LOGGER = new Logger(TestSSHClientManager.class, "SSH", "SSH-Client-Manager-Test");
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSSHClientManager(VertxTestContext testContext)
    {
        var context = new JsonObject();

        var item = ConfigurationConfigStore.getStore().getItems().getJsonObject(0);

        if (item != null)
        {
            var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME));

            var uuid = CommonUtil.getString(UUID.randomUUID());

            var sessionId = CommonUtil.getString(UUID.randomUUID());

            context.put(ID, item.getLong(ID))
                    .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName())
                    .put(UI_EVENT_UUID, uuid)
                    .put(SESSION_ID, sessionId)
                    .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(SSH_OPERATION, ConfigConstants.ClientOperation.CONNECTION);

            LOGGER.info(String.format("Starting SSH connection for sessionId: %s, UUID: %s, IP: %s", sessionId, uuid, object.getString(AIOpsObject.OBJECT_IP)));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info(String.format("Received event: %s", event.encodePrettily()));

                if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.CONNECTION.getName()))
                {
                    LOGGER.info("SSH connection event detected.");

                    try
                    {
                        Assertions.assertTrue(event.containsKey(SSH_OUTPUT) && event.getString(SSH_OUTPUT).isEmpty(),
                                String.format("SSH_OUTPUT should be empty for connection, but was: %s", event.getString(SSH_OUTPUT)));

                        Assertions.assertTrue(event.containsKey(STATUS) && event.getString(STATUS).equals(STATUS_SUCCEED),
                                String.format("Connection status should be SUCCESS, but was: %s", event.getString(STATUS)));

                        LOGGER.info("SSH connection validated. Sending close command.");

                        TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, new JsonObject()
                                .put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_IP))
                                .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                .put(SSH_OPERATION, ConfigConstants.ClientOperation.CLOSE));
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("Assertion failed during connection validation: %s", exception.getMessage()));

                        testContext.failNow(exception);
                    }
                }
                else if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.CLOSE.getName()))
                {

                    LOGGER.info("SSH close event detected.");

                    try
                    {
                        Assertions.assertTrue(event.containsKey(STATUS) && event.getString(STATUS).equals(STATUS_SUCCEED),
                                String.format("Close status should be SUCCESS,but was: %s", event.getString(STATUS)));

                        LOGGER.info("SSH connection closed successfully.");

                        messageConsumer.unregister(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("Consumer unregistered successfully after close.");

                                testContext.completeNow();
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to unregister consumer: %s", asyncResult.cause().getMessage()));

                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("Assertion failed during close validation: %s", exception.getMessage()));

                        testContext.failNow(exception);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Unexpected event structure or operation: %s", event.encodePrettily()));
                }
            });

            // Send the SSH connection request
            TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, context);
        }
        else
        {
            LOGGER.warn("No Config device Present");

            Assertions.fail("No configuration device found.");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSSHClientManagerBadCredentials(VertxTestContext testContext)
    {
        var context = new JsonObject();

        var items = ConfigurationConfigStore.getStore().getItems();

        var uuids = new ArrayList<>();

        if (items != null)
        {
            for (var item : items)
            {
                var uuid = CommonUtil.getString(UUID.randomUUID());

                var sessionId = CommonUtil.getString(UUID.randomUUID());

                uuids.add(uuid);

                context.put(ID, ((JsonObject) item).getLong(ID))
                        .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName())
                        .put(UI_EVENT_UUID, uuid)
                        .put(SESSION_ID, sessionId)
                        .put(AIOpsObject.OBJECT_IP, "**********")
                        .put(SSH_OPERATION, ConfigConstants.ClientOperation.CONNECTION);

                LOGGER.info(String.format("Sending SSH connection request for sessionId: %s, UUID: %s, IP: **********", sessionId, uuid));

                messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
                {
                    var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.info(String.format("Received event: %s", event.encodePrettily()));

                    if (event.getString(AIOpsObject.OBJECT_IP).equals("**********") &&
                            event.containsKey(UI_EVENT_UUID) && uuids.contains(event.getString(UI_EVENT_UUID)) &&
                            event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.CONNECTION.getName()))
                    {

                        LOGGER.info("SSH Connection event detected for IP: **********");

                        try
                        {
                            Assertions.assertTrue(event.containsKey(SSH_OUTPUT) &&
                                            (event.getString(SSH_OUTPUT).isEmpty() ||
                                                    event.getString(SSH_OUTPUT).equalsIgnoreCase(String.format(SSH_CONNECTION_CLOSED, event.getString(AIOpsObject.OBJECT_IP)))),
                                    String.format("Unexpected SSH_OUTPUT for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                            LOGGER.info(String.format("SSH output validated for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                            messageConsumer.unregister(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("Consumer unregistered successfully.");

                                    testContext.completeNow();
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Failed to unregister consumer: %s", asyncResult.cause().getMessage()));

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.warn(String.format("Assertion failed: %s", exception.getMessage()));

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.warn(String.format("Unexpected event structure or UUID/IP mismatch: %s", event.encodePrettily()));
                    }
                });

                // Send the SSH connection request
                TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, context);
            }
        }
        else
        {
            LOGGER.warn("No Config device Present");

            Assertions.fail("No configuration device found.");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSSHClientManagerInvalidCommand(VertxTestContext testContext)
    {
        var context = new JsonObject();

        var item = ConfigurationConfigStore.getStore().getItems().getJsonObject(0);

        if (item != null)
        {
            var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME));

            var uuid = CommonUtil.getString(UUID.randomUUID());

            var sessionId = CommonUtil.getString(UUID.randomUUID());

            context.put(ID, item.getLong(ID))
                    .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName())
                    .put(UI_EVENT_UUID, uuid)
                    .put(SESSION_ID, sessionId)
                    .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(SSH_OPERATION, ConfigConstants.ClientOperation.EXECUTION.getName());

            LOGGER.info(String.format("Sending SSH execution request for sessionId: %s, UUID: %s, IP: %s",
                    sessionId, uuid, object.getString(AIOpsObject.OBJECT_IP)));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info(String.format("Received event: %s", event.encodePrettily()));

                if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.EXECUTION.getName()))
                {
                    LOGGER.info(String.format("SSH Operation EXECUTION detected for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                    try
                    {
                        Assertions.assertTrue(event.containsKey(SSH_OUTPUT) && !event.getString(SSH_OUTPUT).isEmpty(),
                                String.format("SSH_OUTPUT should not be empty for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        LOGGER.info(String.format("SSH output received for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        Assertions.assertTrue(event.containsKey(STATUS) && event.getString(STATUS).equals(STATUS_FAIL),
                                String.format("Expected failure status for IP: %s, but got: %s", event.getString(AIOpsObject.OBJECT_IP), event.getString(STATUS)));

                        LOGGER.info(String.format("Failure status confirmed for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        // Unregister the consumer
                        messageConsumer.unregister(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("Consumer unregistered successfully.");

                                testContext.completeNow();
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to unregister consumer: %s", asyncResult.cause().getMessage()));

                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("Assertion failed: %s", exception.getMessage()));

                        testContext.failNow(exception);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Unexpected SSH operation or event: %s", event.encodePrettily()));
                }
            });

            // Send the SSH execution request
            TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, context);
        }
        else
        {
            LOGGER.warn("No Config device Present");

            Assertions.fail("No configuration device found.");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSSHClientManagerExecuteCommandOps(VertxTestContext testContext)
    {
        var context = new JsonObject();

        var item = ConfigurationConfigStore.getStore().getItems().getJsonObject(0);

        if (item != null)
        {
            var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME));

            var uuid = CommonUtil.getString(UUID.randomUUID());
            var sessionId = CommonUtil.getString(UUID.randomUUID());

            context.put(ID, item.getLong(ID))
                    .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName())
                    .put(UI_EVENT_UUID, uuid)
                    .put(SESSION_ID, sessionId)
                    .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(SSH_OPERATION, ConfigConstants.ClientOperation.CONNECTION.getName());

            LOGGER.info(String.format("Sending SSH connection request for sessionId: %s, UUID: %s, IP: %s",
                    sessionId, uuid, object.getString(AIOpsObject.OBJECT_IP)));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info(String.format("Received event: %s", event.encodePrettily()));

                if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.CONNECTION.getName()))
                {
                    LOGGER.info(String.format("SSH Operation CONNECTION detected for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                    // Send execution command
                    TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER,
                            new JsonObject().mergeIn(context)
                                    .put(SSH_OPERATION, ConfigConstants.ClientOperation.EXECUTION.getName())
                                    .put("ssh.command", "show version"));

                    LOGGER.info(String.format("Sent EXECUTION command 'show version' for sessionId: %s", sessionId));

                }
                else if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equals(ConfigConstants.ClientOperation.EXECUTION.getName()))
                {
                    LOGGER.info(String.format("SSH Operation EXECUTION detected for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                    try
                    {
                        Assertions.assertTrue(event.containsKey(SSH_OUTPUT) && !event.getString(SSH_OUTPUT).isEmpty(),
                                String.format("SSH_OUTPUT should not be empty for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        LOGGER.info(String.format("SSH output received for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        // Unregister the consumer
                        messageConsumer.unregister(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {

                                LOGGER.info("Consumer unregistered successfully.");

                                testContext.completeNow();

                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to unregister consumer: %s", asyncResult.cause().getMessage()));

                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("Assertion failed: %s", exception.getMessage()));

                        testContext.failNow(exception);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Unexpected SSH operation or event: %s", event.encodePrettily()));
                }
            });

            // Send the SSH connection request
            TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, context);
        }
        else
        {
            LOGGER.warn("No Config device Present");

            Assertions.fail("No configuration device found.");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSSHClientManagerCloseOps(VertxTestContext testContext)
    {
        var context = new JsonObject();

        var item = ConfigurationConfigStore.getStore().getItems().getJsonObject(0);

        if (item != null)
        {
            var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_NAME, item.getString(AIOpsObject.OBJECT_NAME));

            var uuid = CommonUtil.getString(UUID.randomUUID());

            var sessionId = CommonUtil.getString(UUID.randomUUID());

            context.put(ID, item.getLong(ID)).put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName())
                    .put(UI_EVENT_UUID, uuid)
                    .put(SESSION_ID, sessionId)
                    .put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP))
                    .put(SSH_OPERATION, ConfigConstants.ClientOperation.CLOSE.getName());

            LOGGER.info(String.format("Sending SSH close operation request for sessionId: %s, UUID: %s, IP: %s", sessionId, uuid, object.getString(AIOpsObject.OBJECT_IP)));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                LOGGER.info(String.format("Received event: %s", event.encodePrettily()));

                if (event.containsKey(SSH_OPERATION) && event.getString(SSH_OPERATION).equalsIgnoreCase(ConfigConstants.ClientOperation.CLOSE.getName()))
                {
                    LOGGER.info(String.format("SSH Operation CLOSE detected for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                    try
                    {
                        Assertions.assertTrue(event.containsKey(SSH_OUTPUT) && !event.getString(SSH_OUTPUT).isEmpty(),
                                String.format("SSH_OUTPUT should not be empty for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        LOGGER.info(String.format("SSH output is not empty for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        Assertions.assertTrue(event.getString(SSH_OUTPUT).equalsIgnoreCase(String.format("Connection to %s is closed", event.getString(AIOpsObject.OBJECT_IP))),
                                String.format("Expected SSH output to confirm connection closed for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        LOGGER.info(String.format("SSH connection successfully closed for IP: %s", event.getString(AIOpsObject.OBJECT_IP)));

                        // Unregister the consumer
                        messageConsumer.unregister(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("Consumer unregistered successfully.");

                                testContext.completeNow();
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to unregister consumer: %s", asyncResult.cause().getMessage()));

                                testContext.failNow(asyncResult.cause());
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.warn(String.format("Assertion failed: %s", exception.getMessage()));

                        testContext.failNow(exception);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Unexpected SSH operation or event: %s", event.encodePrettily()));
                }
            });

            // Send the SSH close request
            TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, context);
        }
        else
        {
            LOGGER.warn("No Config device Present");

            Assertions.fail("No configuration device found.");
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSSHClientManagerInvalidConfigObject(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            var event = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

            Assertions.assertNotNull(event);

            Assertions.assertFalse(event.isEmpty());

            Assertions.assertEquals(STATUS_FAIL, event.getString(STATUS));

            Assertions.assertEquals(EMPTY_VALUE, event.getString(SSH_OUTPUT));

            Assertions.assertEquals(ErrorCodes.ERROR_CODE_SSH_CONNECTION_FAILED, event.getJsonArray(ERRORS).getJsonObject(0).getString(ERROR_CODE));

            Assertions.assertEquals("Connection failed, check host server and credentials", event.getJsonArray(ERRORS).getJsonObject(0).getString(MESSAGE));

            messageConsumer.unregister(asyncResult -> testContext.completeNow());
        });

        TestUtil.vertx().eventBus().send(EVENT_SSH_CLIENT_MANAGER, new JsonObject().put(ID, NOT_AVAILABLE).put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.getName()).put(SSH_OPERATION, ConfigConstants.ClientOperation.CONNECTION));
    }
}
