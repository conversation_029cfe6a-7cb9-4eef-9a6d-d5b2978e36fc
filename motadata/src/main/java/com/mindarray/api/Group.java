/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_GROUPS;
import static com.mindarray.api.User.USER_NAME;

public class Group extends AbstractAPI
{
    public static final String FIELD_PARENT_GROUP = "group.parent";
    public static final String FIELD_GROUP_NAME = "group.name";
    public static final String GROUP_CONTEXT = "group.context";
    public static final String GROUP_AUTO_ASSIGN = "group.auto.assign";
    public static final String GROUP_OPERATOR = "group.operator";
    private static final Logger LOGGER = new Logger(Group.class, MOTADATA_API, "Group API");
    private static final String CHILD = "child";

    public Group()
    {
        super("groups", GroupConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router);

        router.get("/" + endpoint).handler(this::getAll);

        router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var user = UserConfigStore.getStore().getItem(routingContext.user().principal().getLong(ID));

            if (user.getLong(ID).equals(DEFAULT_ID) || (routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE) != null && routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE).equalsIgnoreCase(YES))) //admin user full rights of all groups // or in topology we are not applying data security so, we will send all groups
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, buildTreeStructure(GroupConfigStore.getStore().getItems())));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, buildTreeStructure(GroupConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS)))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var groups = GroupConfigStore.getStore().getChildren(FIELD_PARENT_GROUP, CommonUtil.getLong(routingContext.request().getParam(ID)))
                    .add(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var items = new JsonArray();

            var futures = new ArrayList<Future<Void>>();

            var references = new JsonObject();

            for (var group : groups)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                this.configStore.getReferenceEntities(CommonUtil.getLong(group)).onComplete(result ->
                {
                    if (result.succeeded() && result.result() != null && !result.result().isEmpty())
                    {
                        result.result().getMap().keySet().forEach(key ->
                        {
                            if (!references.containsKey(key))
                            {
                                references.put(key, new JsonArray());
                            }

                            for (var index = 0; index < result.result().getJsonArray(key).size(); index++)
                            {
                                references.getJsonArray(key).add(result.result().getJsonArray(key).getJsonObject(index).getString(APIConstants.REF_PROPS_BY_ENTITY.get(key)));
                            }
                        });

                        items.add(group);
                    }

                    promise.complete();
                });
            }

            Future.join(futures).onComplete(asyncResult ->
            {
                if (items.isEmpty())
                {
                    Bootstrap.configDBService().deleteAll(DBConstants.TBL_GROUP,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, groups),
                            routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    if (!result.result().isEmpty())
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.GROUP.getName()))
                                                .put(APIConstants.REQUEST_PARAM_IDS, groups));

                                        GroupConfigStore.getStore().deleteItems(groups);
                                    }
                                    else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, APIConstants.Entity.GROUP.getName(), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_SUCCEED));
                                    }
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, APIConstants.Entity.GROUP.getName(), result.cause().getMessage()))
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                }
                            });
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(RESULT, references) // ui will display this references in tooltip
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, APIConstants.Entity.GROUP.getName()))
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return validateContext(routingContext, routingContext.body().asJsonObject().getString(FIELD_GROUP_NAME),
                CommonUtil.getLong(CommonUtil.getString(routingContext.body().asJsonObject().getValue(FIELD_PARENT_GROUP))), false);
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {

        var promise = Promise.<Void>promise();

        this.send(routingContext, entity);

        if (routingContext.user().attributes().containsKey("accessToken") && routingContext.user().attributes().getJsonObject("accessToken").containsKey(ID))
        {
            var userId = routingContext.user().attributes().getJsonObject("accessToken").getLong(ID);

            // in case of admin don't assign group as admin has all rights
            if (userId != CommonUtil.getLong(DEFAULT_ID))
            {
                var userGroups = UserConfigStore.getStore().getItem(userId).getJsonArray(USER_GROUPS);

                Bootstrap.configDBService().update(DBConstants.TBL_USER,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, userId),
                        new JsonObject().put(USER_GROUPS, userGroups.add(entity.getLong(ID))),
                        routingContext.user().principal().getString(User.USER_NAME, DEFAULT_USER),
                        routingContext.request().remoteAddress().host(), result ->
                        {
                            if (result.succeeded())
                            {
                                UserConfigStore.getStore().updateItem(userId);

                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug(String.format("user %s updated successfully with userGroups : %s", userId, userGroups.encode()));
                                }

                                promise.complete();
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                promise.fail(result.cause());
                            }
                        });
            }
        }
        else
        {
            LOGGER.warn(String.format("invalid user id : %s", routingContext.user().attributes().encode()));
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var item = GroupConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            if (item != null)
            {
                validateContext(routingContext, requestBody.containsKey(FIELD_GROUP_NAME) ? requestBody.getString(FIELD_GROUP_NAME) : item.getString(FIELD_GROUP_NAME),
                        requestBody.containsKey(FIELD_PARENT_GROUP) ? requestBody.getLong(FIELD_PARENT_GROUP) : 0L, true).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete(requestBody);
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_VALUE_NOT_FOUND, APIConstants.Entity.GROUP.getName())));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_VALUE_NOT_FOUND, APIConstants.Entity.GROUP.getName()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    private Future<JsonObject> validateContext(RoutingContext routingContext, String groupName, long parentGroupId, boolean updateRequest)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var valid = false;

            var flatGroups = configStore.flatMap();

            if (!flatGroups.isEmpty())
            {
                var parentKey = parentGroupId;

                var depth = CommonUtil.getShort(1);

                while (flatGroups.containsKey(parentKey))
                {
                    depth++;

                    parentKey = flatGroups.get(parentKey).getLong(FIELD_PARENT_GROUP);
                }

                if (depth <= 5)
                {
                    valid = true;
                }
            }

            if (valid) //qualify if depth is less than or equal to three
            {
                var requestBody = routingContext.body().asJsonObject();

                var groups = parentGroupId > 0 ? GroupConfigStore.getStore().getItemsByValue(FIELD_PARENT_GROUP, parentGroupId)
                        : GroupConfigStore.getStore().getItemsByValue(FIELD_GROUP_NAME, groupName);

                var validParent = true;

                for (var index = 0; index < groups.size(); index++)
                {
                    var group = groups.getJsonObject(index);

                    boolean invalidGroup;

                    if (updateRequest)
                    {
                        invalidGroup = group.getString(FIELD_GROUP_NAME) != null && group.getString(FIELD_GROUP_NAME).equalsIgnoreCase(groupName)
                                && group.getLong(GlobalConstants.ID) != null && group.getLong(ID) != CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))
                                && group.getLong(FIELD_PARENT_GROUP) != null && group.getLong(FIELD_PARENT_GROUP) == parentGroupId;
                    }
                    else
                    {
                        invalidGroup = group.getString(FIELD_GROUP_NAME) != null && group.getString(FIELD_GROUP_NAME).equalsIgnoreCase(groupName)
                                && group.getLong(FIELD_PARENT_GROUP) != null && group.getLong(FIELD_PARENT_GROUP) == parentGroupId;
                    }

                    if (invalidGroup)
                    {
                        validParent = false;

                        var message = parentGroupId > 0 ? ErrorMessageConstants.GROUP_DUPLICATE_CHILD_GROUP : String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.GROUP.getName());

                        send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, message));

                        promise.fail(message);

                        break;
                    }
                }

                if (validParent)
                {
                    promise.complete(requestBody);
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(MESSAGE, ErrorMessageConstants.GROUP_MAXIMUM_HIERARCHICAL_DEPTH)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(ERROR, ErrorMessageConstants.GROUP_MAXIMUM_HIERARCHICAL_DEPTH));

                promise.fail(ErrorMessageConstants.GROUP_MAXIMUM_HIERARCHICAL_DEPTH);
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            APIUtil.sendResponse(exception, routingContext);
        }

        return promise.future();
    }

    private JsonArray buildTreeStructure(JsonArray groups)
    {
        var items = new JsonArray();

        try
        {
            if (groups != null && !groups.isEmpty())
            {
                var groupsById = new JsonObject();

                var objects = ObjectConfigStore.getStore().getItems();

                var groupReferences = new HashMap<Long, Integer>();

                for (var index = 0; index < objects.size(); index++)
                {
                    var ids = objects.getJsonObject(index).getJsonArray(AIOpsObject.OBJECT_GROUPS);

                    if (ids != null)
                    {
                        for (var id : ids)
                        {
                            groupReferences.put(CommonUtil.getLong(id), groupReferences.getOrDefault(CommonUtil.getLong(id), 0) + 1);
                        }
                    }
                }

                for (var index = 0; index < groups.size(); index++)
                {
                    var group = groups.getJsonObject(index);

                    group.put(APIConstants.ENTITY_PROPERTY_COUNT, groupReferences.getOrDefault(group.getLong(ID), 0));

                    groupsById.put(CommonUtil.getString(group.getValue(GlobalConstants.ID)), group);
                }

                var disqualifiedGroups = new JsonArray();

                for (var index = 0; index < groups.size(); index++)
                {
                    var group = groups.getJsonObject(index);

                    var parentId = CommonUtil.getString(group.getValue(FIELD_PARENT_GROUP));

                    if (parentId != null)
                    {
                        var parentGroup = groupsById.getJsonObject(parentId);

                        if (parentGroup != null)
                        {
                            if (parentGroup.getJsonArray(CHILD) == null)
                            {
                                parentGroup.put(CHILD, new JsonArray());
                            }

                            parentGroup.getJsonArray(CHILD).add(group);

                            if (!parentGroup.containsKey(APIConstants.ENTITY_PROPERTY_COUNT))
                            {
                                parentGroup.put(APIConstants.ENTITY_PROPERTY_COUNT, groupReferences.getOrDefault(CommonUtil.getLong(parentId), 0));
                            }

                            if (!group.containsKey(APIConstants.ENTITY_PROPERTY_COUNT))
                            {
                                group.put(APIConstants.ENTITY_PROPERTY_COUNT, groupReferences.getOrDefault(group.getLong(ID), 0));
                            }

                            groupsById.put(parentId, parentGroup);

                            groupsById.put(CommonUtil.getString(group.getValue(GlobalConstants.ID)), group);

                            disqualifiedGroups.add(CommonUtil.getString(group.getValue(GlobalConstants.ID)));
                        }
                    }
                }

                for (var index = 0; index < disqualifiedGroups.size(); index++)
                {
                    groupsById.remove(disqualifiedGroups.getString(index));
                }

                groupsById.getMap().values().forEach(items::add);

            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            if (!routingContext.user().principal().getJsonArray(APIConstants.USER_PERMISSIONS).contains(USER_SETTINGS + ":" + READ_WRITE_PERMISSION) && (response != null && !response.isEmpty() && response.containsKey(APIConstants.Entity.USER.getName())))
            {
                var users = new JsonArray();

                var entities = response.getJsonArray(APIConstants.Entity.USER.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    if (entities.getJsonObject(index).getLong(ID).equals(routingContext.user().attributes().getJsonObject("accessToken").getLong(ID)))
                    {
                        users.add(entities.getJsonObject(index));
                    }
                }

                response.put(APIConstants.Entity.USER.getName(), users);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    public enum AutoAssignmentRule
    {
        IP_ADDRESS_RANGE("ip.address.range"),
        OBJECT_TYPE("object.type"),
        OBJECT_VENDOR("object.vendor"),
        OBJECT_NAME("object.name");

        private static final Map<String, AutoAssignmentRule> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AutoAssignmentRule::getName, e -> e)));
        private final String name;

        AutoAssignmentRule(String name)
        {
            this.name = name;
        }

        public static AutoAssignmentRule valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

}
