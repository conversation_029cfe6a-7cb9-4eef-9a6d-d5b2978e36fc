[{"oid.group.id": "1acc4bea-5f12-4f86-ba74-46a0473e5132", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Power Supply Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "power.supply.sensor.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.600.12.1.15", "oid.group.oids": {".*******.4.1.674.10892.5.4.600.12.1.1": "power.supply.sensor.chassis.index", ".*******.4.1.674.10892.5.4.600.12.1.2": "power.supply.sensor.index", ".*******.4.1.674.10892.5.4.600.12.1.5": "power.supply.sensor.status", ".*******.4.1.674.10892.5.4.600.12.1.6": "power.supply.sensor.output.mill.watts", ".*******.4.1.674.10892.5.4.600.12.1.7": "power.supply.sensor.type", ".*******.4.1.674.10892.5.4.600.12.1.15": "power.supply.sensor", ".*******.4.1.674.10892.5.4.600.12.1.16": "power.supply.sensor.input.mill.volts"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.600.12.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}, ".*******.4.1.674.10892.5.4.600.12.1.7": {"1": "Other", "2": "Unknown", "3": "Linear", "4": "Switching", "5": "Battery", "6": "UPS", "7": "Converter", "8": "Regulator", "9": "AC", "10": "DC", "11": "VRM"}}}, {"oid.group.id": "438f3c4e-f1ef-49c8-9950-a1f13b776f4a", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Battery", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.600.50.1.7", "oid.group.oids": {".*******.4.1.674.10892.5.4.600.50.1.1": "battery.chassis.index", ".*******.4.1.674.10892.5.4.600.50.1.2": "battery.index", ".*******.4.1.674.10892.5.4.600.50.1.5": "battery.status", ".*******.4.1.674.10892.5.4.600.50.1.6": "battery.reading", ".*******.4.1.674.10892.5.4.600.50.1.7": "battery"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.600.50.1.6": {"1": "Predictive Failure", "2": "Failed", "4": "Presence Detected"}, ".*******.4.1.674.10892.5.4.600.50.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}}}, {"oid.group.id": "ec8bfa60-0f78-4fdf-aecf-9eebfb74b6ec", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Processor Device", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "processor.device.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.1100.32.1.7", "oid.group.oids": {".*******.4.1.674.10892.5.4.1100.32.1.1": "processor.device.chassis.index", ".*******.4.1.674.10892.5.4.1100.32.1.2": "processor.device.index", ".*******.4.1.674.10892.5.4.1100.32.1.5": "processor.device.status", ".*******.4.1.674.10892.5.4.1100.32.1.6": "processor.device.reading", ".*******.4.1.674.10892.5.4.1100.32.1.7": "processor.device"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.1100.32.1.6": {"1": "Internal Error", "2": "Thermal Trip", "32": "Configuration Error", "128": "Processor Present", "256": "Processor Disabled", "512": "Terminator Present", "1024": "Processor Throttled"}, ".*******.4.1.674.10892.5.4.1100.32.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}}}, {"oid.group.id": "4b9cd117-8ec5-4157-bbbc-37f3ac6e0ce0", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "<PERSON>", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "fan.sensor.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.700.12.1.19", "oid.group.oids": {".*******.4.1.674.10892.5.4.700.12.1.1": "fan.sensor.chassis.index", ".*******.4.1.674.10892.5.4.700.12.1.2": "fan.sensor.index", ".*******.4.1.674.10892.5.4.700.12.1.5": "fan.sensor.status", ".*******.4.1.674.10892.5.4.700.12.1.6": "fan.sensor.speed", ".*******.4.1.674.10892.5.4.700.12.1.7": "fan.sensor.type", ".*******.4.1.674.10892.5.4.700.12.1.8": "fan.sensor.location.name", ".*******.4.1.674.10892.5.4.700.12.1.19": "fan.sensor"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.700.12.1.7": {"1": "Other", "2": "Unknown", "3": "Fan", "4": "Blower", "5": "<PERSON>", "6": "Cabinet Fan", "7": "Power Supply Fan", "8": "Heat Pipe", "9": "Refrigeration", "10": "Active Cooling", "11": "Passive Cooling"}, ".*******.4.1.674.10892.5.4.700.12.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical Upper", "5": "Critical Upper", "6": "Non Recoverable Upper", "7": "Non Critical Lower", "8": "Critical Lower", "9": "Non Recoverable Lower", "10": "Failed"}}}, {"oid.group.id": "1c333825-dc15-4a16-a4ac-12bcc58d04fa", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Temperature Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.700.20.1.8", "oid.group.oids": {".*******.4.1.674.10892.5.4.700.20.1.1": "temperature.sensor.probe.chassis.index", ".*******.4.1.674.10892.5.4.700.20.1.2": "temperature.sensor.probe.index", ".*******.4.1.674.10892.5.4.700.20.1.5": "temperature.sensor.status", "(.*******.4.1.674.10892.5.4.700.20.1.6)/10": "temperature.sensor.temperature.celsius", ".*******.4.1.674.10892.5.4.700.20.1.8": "temperature.sensor"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.700.20.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical Upper", "5": "Critical Upper", "6": "Non Recoverable Upper", "7": "Non Critical Lower", "8": "Critical Lower", "9": "Non Recoverable Lower", "10": "Failed"}}}, {"oid.group.id": "7d3619ab-ee3f-4f8c-b3ea-7abae7087730", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Physical Disk", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "physical.disk.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.********.130.4.1.2", "oid.group.oids": {".*******.4.1.674.10892.********.130.4.1.2": "physical.disk", "(.*******.4.1.674.10892.********.130.4.1.17 * 1024 * 1024)": "physical.disk.space.used.bytes", "(.*******.4.1.674.10892.********.130.4.1.19 * 1024 *1024)": "physical.disk.space.free.bytes", ".*******.4.1.674.10892.********.**********": "physical.disk.status", ".*******.4.1.674.10892.********.**********": "physical.disk.power.status", ".*******.4.1.674.10892.********.**********": "physical.disk.operational.status", ".*******.4.1.674.10892.********.130.4.1.54": "physical.disk.fqdd"}, "oid.group.converters": {".*******.4.1.674.10892.********.**********": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}, ".*******.4.1.674.10892.********.**********": {"1": "Other", "2": "Spun Up", "3": "Spun Down", "4": "Transition"}, ".*******.4.1.674.10892.********.**********": {"1": "Not Applicable", "2": "Rebuild", "3": "Clear", "4": "Co<PERSON>"}}}, {"oid.group.id": "61b9bcf8-1f9c-4cd7-99fb-3679e797150e", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Peripheral Component Interconnect", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "pci.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.1100.80.1.12", "oid.group.oids": {".*******.4.1.674.10892.5.4.1100.80.1.2": "pci.index", ".*******.4.1.674.10892.5.4.1100.80.1.5": "pci.status", ".*******.4.1.674.10892.5.4.1100.80.1.7": "pci.data.bus.width", ".*******.4.1.674.10892.5.4.1100.80.1.8": "pci.manufacturer.name", ".*******.4.1.674.10892.5.4.1100.80.1.9": "pci.description.name", ".*******.4.1.674.10892.5.4.1100.80.1.12": "pci"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.1100.80.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}}}, {"oid.group.id": "0e9ce6fd-214d-4678-a738-64da74ddd899", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Memory Device", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "memory.device.instances", "oid.group.parent.oid": ".*******.4.1.674.10892.5.4.1100.50.1.2", "oid.group.oids": {".*******.4.1.674.10892.5.4.1100.50.1.1": "memory.device.chassis.index", ".*******.4.1.674.10892.5.4.1100.50.1.2": "memory.device", ".*******.4.1.674.10892.5.4.1100.50.1.5": "memory.device.status", ".*******.4.1.674.10892.5.4.1100.50.1.8": "memory.device.location.name", "(.*******.4.1.674.10892.5.4.1100.50.1.14 * 1024)": "memory.device.capacity.bytes", ".*******.4.1.674.10892.5.4.1100.50.1.15": "memory.device.speed"}, "oid.group.converters": {".*******.4.1.674.10892.5.4.1100.50.1.5": {"1": "Other", "2": "Unknown", "3": "Ok", "4": "Non Critical", "5": "Critical", "6": "Non Recoverable"}}}]