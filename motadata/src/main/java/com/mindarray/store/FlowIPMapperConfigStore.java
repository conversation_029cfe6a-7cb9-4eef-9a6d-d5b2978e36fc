/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.FlowIPMapper;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;

public class FlowIPMapperConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(FlowIPMapperConfigStore.class, GlobalConstants.MOTADATA_STORE, "Flow IP Mapper Config Store");

    private static final FlowIPMapperConfigStore STORE = new FlowIPMapperConfigStore();

    private final Map<String, String> mappings = new ConcurrentHashMap<>();  //{"127.0.0.1" : user}

    private FlowIPMapperConfigStore()
    {
        super(DBConstants.TBL_FLOW_IP_MAPPER, LOGGER, false);
    }

    public static FlowIPMapperConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_FLOW_IP_MAPPER, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    mappings.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            update(item.getLong(GlobalConstants.ID), true);
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));
                }

            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                update(id, true);
            }
        });
    }

    @Override
    public void addItem(long id, JsonObject item)
    {
        if (item != null)
        {
            this.items.put(id, item);

            update(id, true);
        }
    }

    @Override
    public void deleteItem(long id)
    {
        update(id, false);

        super.deleteItem(id);

    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        if (ids != null && !ids.isEmpty())
        {
            for (var index = 0; index < ids.size(); index++)
            {
                this.deleteItem(ids.getLong(index));
            }
        }
    }

    /**
     * csv map into IPMappers
     *
     * @param id
     */
    private void update(long id, boolean add)
    {
        try
        {
            var item = items.get(id);

            if (item != null && item.containsKey(FlowIPMapper.FLOW_IP_MAPPER_SOURCE) && item.getString(FlowIPMapper.FLOW_IP_MAPPER_SOURCE).equalsIgnoreCase(FlowIPMapper.MANUAL_MAPPING))
            {
                //INFO : as target is needed to delete so we are not removing from the item.
                update(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + item.getString(FlowIPMapper.FLOW_IP_MAPPER_TARGET), add);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
        Update User in flow as per ip.
     */
    public void setUser(String ip, JsonObject flow)
    {
        var user = mappings.get(ip);

        if (user != null)
        {
            flow.put("user", user);
        }
    }

    private void update(String file, boolean add)
    {
        if (Bootstrap.vertx().fileSystem().existsBlocking(file))
        {
            Bootstrap.vertx().fileSystem().readFile(file).onComplete(result ->
            {
                if (result.succeeded())
                {
                    var content = CommonUtil.getString(result.result());

                    if (CommonUtil.isNotNullOrEmpty(content))
                    {
                        var header = content.trim().split("\\n")[0];

                        if (CommonUtil.isNotNullOrEmpty(header))
                        {
                            var position = -1;

                            var valid = true;

                            for (var column : header.trim().split(","))
                            {
                                position++;

                                if (valid && column.trim().equalsIgnoreCase("IP"))
                                {
                                    valid = false;

                                    for (var columns : content.trim().split("\\n"))
                                    {
                                        if (columns.trim().split(",").length > 0)
                                        {
                                            var values = columns.trim().split(",");

                                            var host = values[position].trim();

                                            var username = values.length == 2 ? values[position + 1].trim() : EMPTY_VALUE;

                                            if (CommonUtil.isNotNullOrEmpty(host) && CommonUtil.isNotNullOrEmpty(username) && !host.equalsIgnoreCase("IP") && !username.equalsIgnoreCase("Username"))
                                            {
                                                if (add)
                                                {
                                                    mappings.put(host, username);
                                                }
                                                else
                                                {
                                                    mappings.remove(host);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    LOGGER.error(result.cause());
                }
            });
        }
    }

}
