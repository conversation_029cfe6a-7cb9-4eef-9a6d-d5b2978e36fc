[{"oid.group.id": "c03c7509-c692-48e7-acd8-f9ecdd99eec6", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "CPU & Memory Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.*******.305.1.1.2.0": "system.memory.used.percent", "(100 * (.*******.*******.6.6.0 - .*******.*******.1.8.0)) / .*******.*******.6.6.0 ": "system.memory.used.percent", "(100 * (.*******.4.1.4998.1.1.5.3.1.1.1.2.20 - .*******.4.1.4998.1.1.5.3.1.1.1.4.20) / .*******.4.1.4998.1.1.5.3.1.1.1.2.20)": "system.cpu.percent", ".*******.*******.1.57.0": "system.1min.avg.cpu.percent", ".*******.4.1.4998.1.1.5.3.2.1.1.3.20": "system.memory.free.bytes", ".*******.*******.1.58.0": "system.5min.avg.cpu.percent", ".*******.4.1.1916.1.1.1.28.0": "system.cpu.percent", ".*******.*******.109.*******.5.1": "system.cpu.percent", ".*******.*******.109.*******.10": "system.cpu.percent", ".*******.*******.305.1.1.1.0": "system.cpu.percent", ".*******.*******.1.56.0": "system.cpu.percent", ".*******.4.1.4981.1.20.1.1.1.9": "system.5min.avg.cpu.percent", "(100 * (.*******.4.1.1991.1.1.2.1.54.0 - .*******.4.1.1991.1.1.2.1.55.0)) / .*******.4.1.1991.1.1.2.1.54.0": "system.memory.used.percent", ".*******.4.1.9.6.1.101.1.9.0": "system.5min.avg.cpu.percent", ".*******.4.1.4981.1.20.*******": "system.1min.avg.cpu.percent", ".*******.4.1.9.6.1.101.1.8.0": "system.1min.avg.cpu.percent", ".*******.*******.22*******.1.18": "system.memory.used.bytes", ".*******.*******.1.8.0": "system.memory.free.bytes", "(.*******.*******.48.1.1.1.5.1*100)/(.*******.*******.48.1.1.1.5.1+.*******.*******.48.*******.1)": "system.memory.used.percent", ".*******.*******.109.*******.5": "system.5min.avg.cpu.percent", ".*******.4.1.1991.1.1.2.1.52.0": "system.cpu.percent", "(100 * (.*******.4.1.2272.1.1.46.0 - .*******.4.1.2272.1.1.48.0)) / .*******.4.1.2272.1.1.46.0 ": "system.memory.used.percent", ".*******.*******.109.*******.4": "system.1min.avg.cpu.percent", ".*******.*******.109.*******.7": "system.1min.avg.cpu.percent", ".*******.*******.109.*******.6": "system.cpu.percent", ".*******.*******.109.1.*******": "system.5min.avg.cpu.percent", ".*******.*******.109.*******.26": "system.15min.avg.cpu.load.percent", "(100 * (.*******.4.1.4998.1.1.5.3.1.1.1.2.21 - .*******.4.1.4998.1.1.5.3.1.1.1.4.21) / .*******.4.1.4998.1.1.5.3.1.1.1.2.21)": "system.cpu.percent", "(.*******.4.1.4998.1.1.5.3.2.1.1.2.20 - .*******.4.1.4998.1.1.5.3.2.1.1.3.20)": "system.memory.used.bytes", ".*******.4.1.2272.1.1.20.0": "system.cpu.percent", "(.*******.*******.48.1.1.1.5 * 100)/(.*******.*******.48.1.1.1.5 + .*******.*******.48.*******)": "system.memory.used.percent", ".*******.*******.22*******.1.20": "system.memory.free.bytes", ".*******.*******.147.1.2.2.2.1.5.40.6": "active.sessions", ".*******.*******.109.*******.25": "system.5min.avg.cpu.load.percent", ".*******.4.1.4998.1.1.5.3.2.1.1.2.20": "system.memory.installed.bytes", ".*******.*******.109.*******.24": "system.1min.avg.cpu.load.percent", "(.*******.*******.22*******.1.18 * 100)/(.*******.*******.22*******.1.18 + .*******.*******.22*******.1.20)": "system.memory.used.percent", ".*******.*******.6.6.0": "system.memory.installed.bytes", "(.*******.4.1.1991.1.1.2.1.54.0 - .*******.4.1.1991.1.1.2.1.55.0)": "system.memory.used.bytes", "(.*******.*******.6.6.0 - .*******.*******.1.8.0)": "system.memory.used.bytes", ".*******.4.1.14179.1.1.5.1": "system.cpu.percent", "(100 * .*******.4.1.4981.1.20.1.1.1.5) / .*******.4.1.4981.1.20.1.1.1.4 ": "system.memory.used.percent", "(100 * (.*******.4.1.4998.1.1.5.3.2.1.1.2.20 - .*******.4.1.4998.1.1.5.3.2.1.1.3.20)) / .*******.4.1.4998.1.1.5.3.2.1.1.2.20": "system.memory.used.percent", ".*******.*******.109.*******.3": "system.cpu.percent", ".*******.4.1.1991.1.1.2.1.54.0": "system.memory.installed.bytes", ".*******.4.1.1991.1.1.2.1.55.0": "system.memory.free.bytes", ".*******.4.1.2021.10.1.3.1": "system.cpu.percent", ".*******.4.1.2021.9.1.9": "system.disk.used.percent"}}, {"oid.group.id": "d845600b-badd-42f7-aa6e-307d8642ca42", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Temperature Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.13.1.3.1.2", "oid.group.oids": {".*******.*******.13.1.3.1.2": "temperature.sensor", ".*******.*******.13.1.3.1.3": "temperature.sensor.reading.celsius", ".*******.*******.13.1.3.1.6": "temperature.sensor.status"}, "oid.group.converters": {".*******.*******.13.1.3.1.6": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}}, {"oid.group.id": "3e75716b-e0fc-4084-8c3a-539f71bf976d", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Voltage Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.13.1.2.1.2", "oid.group.oids": {".*******.*******.13.1.2.1.2": "voltage.sensor", ".*******.*******.13.1.2.1.3": "voltage.sensor.reading.mill.volts", ".*******.*******.13.1.2.1.7": "voltage.sensor.status"}, "oid.group.converters": {".*******.*******.13.1.2.1.7": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}}, {"oid.group.id": "4a0935cc-b15a-46be-9ec1-5702807ab05d", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "<PERSON>", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.13.1.4.1.2", "oid.group.oids": {".*******.*******.13.1.4.1.2": "fan.sensor", ".*******.*******.13.1.4.1.3": "fan.sensor.status"}, "oid.group.converters": {".*******.*******.13.1.4.1.3": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}}, {"oid.group.id": "a183f0d0-91b8-4fe6-b41a-2eb0259cfa06", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Power Supply Sensor", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.13.1.5.1.2", "oid.group.oids": {".*******.*******.13.1.5.1.2": "power.supply.sensor", ".*******.*******.13.1.5.1.3": "power.supply.sensor.status"}, "oid.group.converters": {".*******.*******.13.1.5.1.3": {"1": "Normal", "2": "Warning", "3": "Critical", "4": "Shutdown", "5": "Not Present", "6": "Not Functioning"}}}, {"oid.group.id": "567bf348-3a84-4341-a1de-fbc6b38ad6be", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "<PERSON><PERSON><PERSON> In<PERSON>", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.6.11.1.3", "oid.group.oids": {".*******.*******.6.11.1.3": "chassis.slot", ".*******.*******.6.11.1.9": "chassis.slot.status"}, "oid.group.converters": {".*******.*******.6.11.1.9": {"1": "Not Specified", "2": "Up", "3": "Down", "4": "Standby"}}}, {"oid.group.id": "834ebc48-a03a-41bf-b58b-0ded65750fc7", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "Buffer Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 600, "oid.group.oids": {".*******.*******.1.19.0": "cisco.small.buffer.misses", ".*******.*******.1.27.0": "cisco.medium.buffer.misses", ".*******.4.1.9.********": "cisco.large.buffer.misses", ".*******.*******.1.67.0": "cisco.huge.buffer.misses", ".*******.*******.1.35.0": "cisco.big.buffer.misses", ".*******.*******.1.12.0": "cisco.extra.large.buffer.misses"}}, {"oid.group.id": "25ad1fb5-96ab-4a6b-b70c-8af7de256d20", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "System Info", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 10800, "oid.group.oids": {".*******.********.********": "system.description", ".*******.*******.********.2.7": "system.description", ".*******.********.********.0": "system.serial.number", ".*******.*******.********": "system.serial.number", ".*******.********.********.1": "system.serial.number", ".*******.********.********.1001": "system.serial.number", ".*******.********.********.1": "system.model.number", ".*******.********.********.1001": "system.model.number", ".*******.*******.6.3.0": "system.serial.number"}}, {"oid.group.id": "81952a8a-7037-4edb-b3d2-eed3b0f57fcc", "oid.group.device.type": "Firewall", "oid.group.type": "scalar", "oid.group.name": "Connection Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.*******.49*******.1.0": "cisco.asa.attempted.connections", ".*******.*******.49*******.3.0": "cisco.asa.declined.connections", ".*******.*******.49*******.6.0": "cisco.asa.active.connections.rate", ".*******.*******.49*******.7.0": "cisco.asa.expired.connections", ".*******.*******.491.*******.0": "cisco.asa.aborted.connections"}}, {"oid.group.id": "0763cd1f-dc55-4f2d-abe7-355256535fa4", "oid.group.device.type": "Firewall", "oid.group.type": "scalar", "oid.group.name": "Session Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.*******.392.1.3.1.0": "cisco.asa.active.remote.connections.rate", ".*******.*******.392.*******": "cisco.asa.active.remote.user.sessions.rate", ".*******.*******.392.*******": "cisco.asa.active.remote.group.sessions", ".*******.*******.392.*******": "cisco.asa.remote.in.packets.rate", ".*******.*******.392.*******": "cisco.asa.remote.out.packets.rate", ".*******.*******.392.********": "cisco.asa.remote.received.dropped.packets", ".*******.*******.392.********": "cisco.asa.remote.sent.dropped.packets", ".*******.*******.392.********": "cisco.asa.active.ipsec.sessions", ".*******.*******.392.********": "cisco.asa.active.lan.sessions", ".*******.*******.392.********": "cisco.asa.active.load.balancer.sessions", ".*******.*******.392.********": "cisco.asa.active.svc.sessions", ".*******.*******.392.********": "cisco.asa.active.web.vpn.sessions"}}, {"oid.group.id": "d845600b-bade-42f7-aa6e-305d8642cb67", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Active Processes", "oid.group.polling.timeout.sec": 180, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.correlation.metric": "yes", "oid.group.instance.count.metric.name": "process.instances", "oid.group.parent.oid": ".*******.********.2.1.1", "oid.group.oids": {".*******.********.2.1.1": "process", ".*******.********.2.1.2": "process.name", ".*******.********.2.1.3": "process.id", ".*******.********.2.1.4": "process.path", ".*******.********.2.1.5": "process.description", ".*******.********.2.1.6": "process.type", ".*******.********.2.1.7": "process.status"}, "oid.group.converters": {".*******.********.2.1.6": {"1": "Unknown", "2": "Operating System", "3": "<PERSON><PERSON>", "4": "Application"}, ".*******.********.2.1.7": {"1": "Running", "2": "Runnable", "3": "Not Runnable", "4": "Invalid"}}}, {"oid.group.id": "d845600c-bade-42f7-aa6e-305d8489cb65", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Software Info", "oid.group.polling.timeout.sec": 180, "oid.group.polling.interval.sec": 1500, "oid.group.instance.count.metric.status": "yes", "oid.group.instance.count.metric.name": "software.installed.instances", "oid.group.correlation.metric": "yes", "oid.group.parent.oid": ".*******.********.3.1.1", "oid.group.oids": {".*******.********.3.1.1": "software", ".*******.********.3.1.2": "software.name", ".*******.********.3.1.3": "software.id", ".*******.********.3.1.4": "software.type", ".*******.********.3.1.5": "software.installed.date"}, "oid.group.converters": {".*******.********.3.1.4": {"1": "Unknown", "2": "Operating System", "3": "<PERSON><PERSON>", "4": "Application"}}}, {"oid.group.id": "c03c7509-c691-48e7--acd8-f9ecdd99de23", "oid.group.device.type": "SNMP Device", "oid.group.type": "scalar", "oid.group.name": "Cisco FirePower CPU & Memory Statistics", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 300, "oid.group.oids": {".*******.4.1.1991.1.1.2.1.54.0": "system.memory.installed.bytes", ".*******.4.1.1991.1.1.2.1.55.0": "system.memory.free.bytes", ".*******.*******.826.1.71.20.1.5": "system.15min.avg.cpu.percent", ".*******.*******.826.1.71.20.1.6": "system.cpu.percent", ".*******.*******.826.1.71.20.1.7": "system.5min.avg.cpu.percent", "(1000 * .*******.*******.826.1.71.20.1.11)": "system.memory.used.bytes", "(1000 * .*******.*******.826.1.71.20.1.10)": "system.memory.installed.bytes", "(1000 * .*******.*******.826.1.71.20.1.10) - (1000 * .*******.*******.826.1.71.20.1.11)": "system.memory.free.bytes", "(100 * (.*******.*******.826.1.71.20.1.10 - .*******.*******.826.1.71.20.1.11)) / .*******.*******.826.1.71.20.1.10": "system.memory.used.percent", "(100 * (.*******.*******.826.1.71.20.1.18 - .*******.*******.826.1.71.20.1.17)) / .*******.*******.826.1.71.20.1.18": "system.disk.used.percent", "(1000000 * .*******.*******.826.1.71.20.1.17)": "system.disk.free.bytes", "(1000000 * .*******.*******.826.1.71.20.1.18)": "system.disk.capacity.bytes", "(1000000 * .*******.*******.826.1.71.20.1.18) - (1000000 * .*******.*******.826.1.71.20.1.17)": "system.disk.used.bytes"}}, {"oid.group.id": "567bf348-3a84-4341-a1de-ebc6b38ad623", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Storage Statistics", "oid.group.polling.timeout.sec": 120, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": "*******.*******.826.1.74.23.1.4", "oid.group.oids": {".*******.*******.826.1.74.23.1.4": "cisco.firepower.storage", ".*******.*******.826.1.74.23.1.5": "cisco.firepower.storage.state", ".*******.*******.826.1.74.23.1.7": "cisco.firepower.storage.used.percent", ".*******.*******.826.1.74.23.1.6": "cisco.firepower.storage.capacity.bytes"}, "oid.group.converters": {".*******.*******.826.1.74.23.1.5": {"0": "Unknown", "1": "Optimal", "2": "Failed", "3": "Unresponsive"}}}, {"oid.group.id": "267bf348-3b74-4341-b1de-ebc6b47ad623", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Disk File System Statistics", "oid.group.polling.timeout.sec": 120, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.71.9.1.1", "oid.group.oids": {".*******.*******.826.1.71.9.1.1": "cisco.firepower.disk.file.system", ".*******.*******.826.1.71.9.1.4": "cisco.firepower.disk.file.system.name", ".*******.*******.826.1.71.9.1.7": "cisco.firepower.disk.file.system.mount.point", "1000 * .*******.*******.826.1.71.9.1.6": "cisco.firepower.disk.file.system.free.bytes", "1000 *.*******.*******.826.1.71.9.1.9": "cisco.firepower.disk.file.system.used.bytes", "1000 * .*******.*******.826.1.71.9.1.8": "cisco.firepower.disk.file.system.capacity.bytes"}}, {"oid.group.id": "a4456d0b-badd-42f7-aa6e-307d864e2a32", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Processor Temperature Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.66.2.1.1", "oid.group.oids": {".*******.*******.826.1.66.2.1.1": "temperature.sensor", ".*******.*******.826.1.66.2.1.10": "temperature.sensor.reading.celsius", ".*******.*******.826.1.66.2.1.4": "temperature.sensor.input.current.ampere"}}, {"oid.group.id": "b2256d0b-badd-42f3-aa6e-3a2d864e2a31", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Motherboard Temperature Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.12.24.1.1", "oid.group.oids": {".*******.*******.826.1.12.24.1.1": "motherboard.temperature.sensor", ".*******.*******.826.1.12.24.1.4": "motherboard.temperature.sensor.io.reading.celsius", ".*******.*******.826.1.12.24.1.8": "motherboard.temperature.sensor.rear.reading.celsius", ".*******.*******.826.1.12.24.1.10": "motherboard.temperature.sensor.rear.left.reading.celsius", ".*******.*******.826.1.12.24.1.16": "motherboard.temperature.sensor.rear.right.reading.celsius", ".*******.*******.826.1.12.22.1.12": "motherboard.temperature.sensor.input.voltage.volt"}}, {"oid.group.id": "b3456d1b-badd-42f7-aa62-307d864e2a32", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "I/O Card Temperature Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.20.70.1.1", "oid.group.oids": {".*******.*******.826.1.20.70.1.1": "io.card.temperature.sensor", ".*******.*******.826.1.20.70.1.18": "io.card.temperature.sensor.reading.celsius", ".*******.*******.826.1.20.70.1.13": "io.card.temperature.sensor.proc.reading.celsius", ".*******.*******.826.1.20.70.1.8": "io.card.temperature.sensor.dimm.reading.celsius", ".*******.*******.826.1.20.70.1.4": "io.card.temperature.sensor.ambient.reading.celsius"}}, {"oid.group.id": "5c0945cd-b3ba-46be-9ec1-5702807ab02e", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Cisco FirePower Fan Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.20.40.1.1", "oid.group.oids": {".*******.*******.826.1.20.40.1.1": "fan.sensor", ".*******.*******.826.1.20.40.1.5": "fan.sensor.speed.rpm"}}, {"oid.group.id": "b683f0d0-91b8-4fe6-b41a-2eb3459cfa06", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Cisco FirePower Power Supply Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.20.116.1.1", "oid.group.oids": {".*******.*******.826.1.20.116.1.1": "power.supply.sensor", ".*******.*******.826.1.20.116.1.25": "power.supply.sensor.output.power", ".*******.*******.826.1.20.116.1.21": "power.supply.sensor.output.current.ampere"}}, {"oid.group.id": "2c75216b-a0fc-4164-8c3b-539f74bf974e", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Cisco FirePower Voltage Sensor", "oid.group.polling.timeout.sec": 90, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.12.22.1.1", "oid.group.oids": {".*******.*******.826.1.12.22.1.1": "voltage.sensor", ".*******.*******.826.1.12.22.1.12": "voltage.sensor.input.voltage.volt", ".*******.*******.826.1.12.22.1.8": "voltage.sensor.input.current.ampere"}}, {"oid.group.id": "234bf348-3a84-4341-a2de-fac6b38ad6bd", "oid.group.device.type": "SNMP Device", "oid.group.type": "tabular", "oid.group.name": "Cisco FirePower Chassis Stats", "oid.group.polling.timeout.sec": 60, "oid.group.polling.interval.sec": 1500, "oid.group.parent.oid": ".*******.*******.826.1.20.26.1.1", "oid.group.oids": {".*******.*******.826.1.20.26.1.1": "chassis.stats", ".*******.*******.826.1.20.26.1.4": "chassis.stats.input.power", ".*******.*******.826.1.20.26.1.9": "chassis.stats.output.power"}}]