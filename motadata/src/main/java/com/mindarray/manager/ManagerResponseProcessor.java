/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/

package com.mindarray.manager;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;

import java.io.File;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.DATABASE_BACKUP_COMPLETED_NOTIFICATION_SUBJECT;
import static com.mindarray.InfoMessageConstants.DATABASE_BACKUP_NOTIFICATION_MESSAGE;
import static com.mindarray.agent.AgentConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.BackupProfile.*;
import static com.mindarray.api.MotadataApp.ARTIFACT_UPGRADE_STATUS;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.api.Scheduler.SCHEDULER_EMAIL_RECIPIENTS;
import static com.mindarray.api.Scheduler.SCHEDULER_SMS_RECIPIENTS;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.CommonUtil.isNotNullOrEmpty;
import static com.mindarray.util.DateTimeUtil.convertTime;

/**
 * The ManagerResponseProcessor class is responsible for processing responses and events from various
 * system components in the Motadata application.
 * <p>
 * This class handles different types of events including:
 * <ul>
 *   <li>Backup and restore operations</li>
 *   <li>Application lifecycle events (start, stop, restart)</li>
 *   <li>Upgrade events</li>
 *   <li>State change notifications</li>
 *   <li>Manager upgrade events</li>
 * </ul>
 * <p>
 * The ManagerResponseProcessor maintains communication with remote event processors through
 * periodic heartbeats and processes their responses. It also manages notifications for
 * various system events and updates the status of operations in the system.
 * <p>
 * This class extends Vert.x's AbstractVerticle to leverage the event-driven, non-blocking
 * programming model for handling concurrent operations efficiently.
 */
public class ManagerResponseProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(ManagerResponseProcessor.class, MOTADATA_APP_MANAGER, "Manager Response Processor");

    /**
     * Flag indicating if a write operation is pending
     */
    private boolean writePending = false;

    /**
     * Flag indicating if the data has been modified and needs to be saved
     */
    private boolean dirty = false;

    /**
     * Initializes the ManagerResponseProcessor verticle.
     * <p>
     * This method sets up periodic tasks for:
     * <ul>
     *   <li>Writing pending data to storage</li>
     *   <li>Sending heartbeats to remote event processors</li>
     *   <li>Maintaining connections with datastores</li>
     * </ul>
     * <p>
     * It also registers event bus handlers for processing various types of events
     * including backup, start, stop, restart, delete, upgrade, state change, and
     * manager upgrade events.
     *
     * @param promise the promise to complete when initialization is done
     * @throws Exception if an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.setPeriodic(60000, timer ->
        {
            write();

            for (var remoteEventProcessor : RemoteEventProcessorConfigStore.getStore().flatMap().values())
            {
                if (!remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))
                {
                    // send heartbeat to collector
                    publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC).put(REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID))
                            .put(ID, remoteEventProcessor.getLong(ID)).put(EVENT_TYPE, EVENT_REMOTE_PROCESSOR_HEARTBEAT));
                }
                else
                {
                    // this DATASTORE_CONNECTION_TOPIC separate topic is for sending heartbeat/keep alive to 'all' datastore

                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID))
                            .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.CONNECTION_KEEP_ALIVE.getName()).getBytes()));

                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_QUERY_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID))
                            .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.CONNECTION_KEEP_ALIVE.getName()).getBytes()));

                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_BROKER_OPERATION_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID))
                            .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.CONNECTION_KEEP_ALIVE.getName()).getBytes()));

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("keep alive request send to datastore : uuid %s and mode %s ", remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID), remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));
                    }
                }

                // send heartbeat to collector manager
                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(ID, remoteEventProcessor.getLong(ID)).put(EVENT_TYPE, EVENT_MOTADATA_MANAGER_HEARTBEAT).put(SYSTEM_BOOTSTRAP_TYPE, remoteEventProcessor.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

            }
        });

        update();

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION
                , message ->
                {
                    var event = message.body();

                    if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE && dirty)
                    {
                        dirty = false;

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, "remote-event-processor-cache").put(RESULT, vertx.fileSystem().readFileBlocking(REMOTE_EVENT_PROCESSOR_CACHE_PATH)));
                    }

                }).exceptionHandler(LOGGER::error);


        vertx.eventBus().<JsonObject>localConsumer(EVENT_MANAGER_RESPONSE_PROCESSOR, message ->
        {
            var event = message.body();

            var systemBootstrapType = event.getString(SYSTEM_BOOTSTRAP_TYPE);

            // based on Bootstrap Type will process the response

            if (systemBootstrapType.equalsIgnoreCase(BootstrapType.APP.name()))
            {
                switch (event.getString(EVENT_TYPE))
                {
                    case EVENT_DATABASE_BACKUP -> processBackupEvent(event);

                    case EVENT_DATABASE_RESTORE ->
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug("Restore event : " + event.encodePrettily());
                        }

                        if (event.containsKey(NMSConstants.STATE) && event.getString(NMSConstants.STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                        {
                            EventBusConstants.publish(event.getString(EVENT_TYPE), event);
                        }
                    }

                    case EVENT_MASTER_UPGRADE ->
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug("Upgrade event : " + event.encodePrettily());
                        }

                        // in case of restore/master.upgrade , we will pass the port to UI to open HTTP server.

                        try
                        {
                            if (event.containsKey(NMSConstants.STATE) && event.getString(NMSConstants.STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                            {
                                EventBusConstants.publish(event.getString(EVENT_TYPE), event);
                            }
                            else if (event.containsKey(STATUS))
                            {
                                var artifact = ArtifactConfigStore.getStore().getItem(event.getLong(ID))
                                        .put(ARTIFACT_UPGRADE_STATUS, event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) ?
                                                String.format("Last upgraded at %s", DateTimeUtil.timestamp())
                                                : String.format("Last upgrade failed at %s", DateTimeUtil.timestamp()))
                                        .put(MESSAGE, event.getString(MESSAGE, EMPTY_VALUE));

                                if (event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    event.put(MESSAGE, String.format(InfoMessageConstants.UPGRADE_SUCCEEDED, "Master Server", RemoteEventProcessorConfigStore.getStore().getItem(event.getLong(ID)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST)));
                                }
                                else
                                {
                                    event.put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED, "Master Server", RemoteEventProcessorConfigStore.getStore().getItem(event.getLong(ID)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST)));
                                }

                                publishUserNotificationEvent(event);

                                LOGGER.info(String.format("upgrading artifact : %s", artifact.encodePrettily()));

                                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(MotadataApp.ARTIFACT_TYPE, BootstrapType.APP.name()).put(ID, event.getLong(ID)).put(EVENT_COPY_REQUIRED, false));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    case EVENT_MANAGER_UPGRADE -> processManagerUpgradeEvent(event);
                }
            }

            else if (systemBootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                getContext(message.body()).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        var response = result.result();

                        if (CommonUtil.traceEnabled())
                        {

                            LOGGER.trace(String.format("received agent manager event %s", CommonUtil.removeSensitiveFields(response, true)));
                        }

                        switch (response.getString(EVENT_TYPE))
                        {
                            case EVENT_AGENT_START -> processStartEvent(response);
                            case EVENT_AGENT_STOP -> processStopEvent(response);
                            case EVENT_AGENT_RESTART -> processRestartEvent(response);
                            case EVENT_AGENT_DELETE -> processDeleteEvent(response);
                            case EVENT_AGENT_UPGRADE -> processUpgradeEvent(response);
                            case EVENT_AGENT_DISABLE, EVENT_AGENT_ENABLE -> processStateEvent(response);
                            case EVENT_ACKNOWLEDGEMENT ->
                            {
                            }
                            default ->
                                    LOGGER.warn(String.format("unknown event type: %s for agent manager %s event", response.getString(EVENT_TYPE), response.getString(AGENT_UUID)));
                        }
                    }
                });
            }

            else if (systemBootstrapType.equalsIgnoreCase(BootstrapType.COLLECTOR.name())
                    || systemBootstrapType.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name())
                    || systemBootstrapType.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                    || systemBootstrapType.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                    || systemBootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                switch (event.getString(EVENT_TYPE))
                {
                    case EVENT_REMOTE_PROCESSOR_START ->
                            process(event, "start", EVENT_REMOTE_PROCESSOR_START, InfoMessageConstants.START_SUCCEEDED, NMSConstants.STATE_RUNNING);

                    case EVENT_REMOTE_PROCESSOR_STOP ->
                            process(event, "stop", EVENT_REMOTE_PROCESSOR_STOP, InfoMessageConstants.STOP_SUCCEEDED, NMSConstants.STATE_NOT_RUNNING);

                    case EVENT_REMOTE_PROCESSOR_RESTART ->
                            process(event, "restart", EVENT_REMOTE_PROCESSOR_RESTART, InfoMessageConstants.RESTART_SUCCEEDED, NMSConstants.STATE_RUNNING);

                    case EVENT_REMOTE_PROCESSOR_DELETE ->
                            process(event, "delete", EVENT_REMOTE_PROCESSOR_DELETE, InfoMessageConstants.DELETE_SUCCEEDED, null);

                    case EVENT_REMOTE_PROCESSOR_UPGRADE -> processUpgrade(event);

                    case EVENT_ACKNOWLEDGEMENT ->
                    {
                    }

                    default -> LOGGER.warn(String.format("invalid event %s", event));
                }
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_REMOTE_PROCESSOR_HEARTBEAT + ".status.query", message ->
        {
            var items = new JsonArray();

            for (var remoteEventProcessor : RemoteEventProcessorConfigStore.getStore().flatMap().values())
            {
                var item = RemoteEventProcessorCacheStore.getStore().getItem(remoteEventProcessor.getLong(ID));

                items.add(new JsonObject().put(GlobalConstants.ID, remoteEventProcessor.getLong(ID))
                        .put(GlobalConstants.STATUS, item.getString(STATUS))
                        .put(MotadataAppManager.HEARTBEAT_STATE, item.getString(MotadataAppManager.HEARTBEAT_STATE))
                        .put(GlobalConstants.DURATION, DateTimeUtil.convertTime(item.getLong(DURATION)))
                        .put(EVENT_TIMESTAMP, item.getLong(EVENT_TIMESTAMP)));
            }

            publish(message.body().getString(SESSION_ID), UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_REMOTE_PROCESSOR_HEARTBEAT).put(RESULT, items));
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_AGENT_HEARTBEAT + ".status.query", message ->
        {
            var items = new JsonArray();

            for (var agent : AgentConfigStore.getStore().flatMap().values())
            {
                var item = AgentCacheStore.getStore().getItem(agent.getLong(ID));

                var event = new JsonObject();

                event.put(STATUS, item.getString(STATUS))
                        .put(MotadataAppManager.HEARTBEAT_STATE, item.getString(MotadataAppManager.HEARTBEAT_STATE))
                        .put(EVENT_TIMESTAMP, item.getLong(EVENT_TIMESTAMP))
                        .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(ID, agent.getLong(ID))
                        .put(DURATION, convertTime(item.getLong(DURATION)))
                        .put(AGENT_STATE, agent.getString(AGENT_STATE));

                var healthContext = item.getJsonObject(AGENT_HEALTH_DIAGNOSIS, new JsonObject());

                var agentConfigs = new JsonObject(agent.getString(AGENT_CONFIGS)).getJsonObject(AGENT);

                // metric agent stop status
                if (agentConfigs.getString(METRIC_AGENT_STATUS).equalsIgnoreCase(NO))
                {
                    var status = healthContext.getJsonObject(AgentConstants.Agent.METRIC.getName(), new JsonObject());

                    status.put(SEVERITY, "STOP");

                    healthContext.put(AgentConstants.Agent.METRIC.getName(), status);
                }

                // log agent stop status
                if (agentConfigs.getString(LOG_AGENT_STATUS).equalsIgnoreCase(NO))
                {
                    var status = healthContext.getJsonObject(AgentConstants.Agent.LOG.getName(), new JsonObject());

                    status.put(SEVERITY, "STOP");

                    healthContext.put(AgentConstants.Agent.LOG.getName(), status);

                    item = ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID));

                    if (item.containsKey(AIOpsObject.OBJECT_TYPE) && item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase("Windows"))
                    {
                        status = healthContext.getJsonObject(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName(), new JsonObject());

                        status.put(SEVERITY, "STOP");

                        healthContext.put(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName(), status);
                    }
                }

                // packet agent stop status
                if (agentConfigs.getString(PACKET_AGENT_STATUS).equalsIgnoreCase(NO))
                {
                    var status = healthContext.getJsonObject(AgentConstants.Agent.PACKET.getName(), new JsonObject());

                    status.put(SEVERITY, "STOP");

                    healthContext.put(AgentConstants.Agent.PACKET.getName(), status);
                }

                if (healthContext != null && !healthContext.isEmpty())
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("health of agent %s : %s", agent.getString(AGENT_UUID), healthContext.encode()));
                    }

                    event.put(EVENT_AGENT_HEALTH, healthContext);
                }

                items.add(event);
            }

            publish(message.body().getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_HEARTBEAT).put(RESULT, items));
        });

        promise.complete();
    }

    /**
     * Publishes an event to the appropriate event bus address.
     * <p>
     * This method validates the event object and routes it to the correct event bus address
     * based on the event topic. It adds a unique event ID to each event before publishing.
     *
     * @param event the JSON object containing the event data to publish
     */
    private void publishEvent(JsonObject event)
    {
        if (!event.isEmpty() && isNotNullOrEmpty(event.getString(EVENT_TYPE)) && isNotNullOrEmpty(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
        {
            event.put(EVENT_ID, CommonUtil.newEventId());

            if (REMOTE_EVENT_PROCESSOR_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, event);
            }
            else if (MOTADATA_MANAGER_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, event);
            }
        }

        else
        {
            LOGGER.warn(String.format("failed to send event %s", event));
        }

    }

    /**
     * Processes backup events and manages backup snapshots.
     * <p>
     * This method handles the completion of backup operations by:
     * <ul>
     *   <li>Storing backup results in the database</li>
     *   <li>Managing backup retention based on storage profile settings</li>
     *   <li>Sending notifications about backup completion</li>
     *   <li>Publishing events to update the UI</li>
     * </ul>
     * <p>
     * Different backup types (e.g., report database backups) may have specific handling requirements.
     *
     * @param event the JSON object containing backup event data
     */
    private void processBackupEvent(JsonObject event)
    {
        try
        {
            if (event.getString(BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfileType.REPORT_DB.getName()))
            {
                //send it to DB for Notification....
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_OPERATION_TOPIC)
                        .put(EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.STORE_BACKUP.getName()).getBytes()));
            }

            var item = BackupProfileConfigStore.getStore().getItemByValue(BACKUP_PROFILE_TYPE, event.getString(BACKUP_PROFILE_TYPE));

            var id = item.getLong(ID);

            var storageProfile = StorageProfileConfigStore.getStore().getItem(item.getLong(BACKUP_STORAGE_PROFILE));

            var snapshot = new JsonObject()
                    .put(STATUS, event.getString(STATUS))
                    .put(GlobalConstants.DURATION, event.getLong(BACKUP_END_TIME, DateTimeUtil.currentMilliSeconds()) - event.getLong(BACKUP_START_TIME))
                    .put(BACKUP_START_TIME, DateTimeUtil.timestamp(event.getLong(BACKUP_START_TIME)))
                    .put(BACKUP_END_TIME, DateTimeUtil.timestamp(event.getLong(BACKUP_END_TIME, DateTimeUtil.currentMilliSeconds())))
                    .put(ERRORS, event.getJsonArray(ERRORS))
                    .put(DatastoreConstants.BACKUP_SIZE_BYTES, event.getInteger(DatastoreConstants.BACKUP_SIZE_BYTES, 0))
                    .put(BACKUP_STORAGE_PROFILE, item.getLong(BACKUP_STORAGE_PROFILE))
                    .put(GlobalConstants.SRC_FILE_PATH, event.getString(GlobalConstants.SRC_FILE_PATH));

            getBackupSnapshots(id).onComplete(asyncResult ->
            {
                var snapshots = asyncResult.result();

                // for LOCAL , we are performing retention of 7 days.
                // for OTHER, we are storing latest 1 successful backup result

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("current snapshot %s ", snapshot.encode()));
                }

                if (storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL).equalsIgnoreCase(StorageProfile.StorageProtocol.LOCAL.getName()))
                {
                    qualifyRetentionDays(storageProfile, item, snapshot, snapshots);
                }
                else if (snapshot.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    snapshots.clear();
                }

                snapshots.add(snapshot);

                Bootstrap.configDBService().drop(TBL_BACKUP_SNAPSHOT + id, result ->
                {
                    if (result.succeeded())
                    {
                        Bootstrap.configDBService().saveAll(TBL_BACKUP_SNAPSHOT + id, snapshots, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                response ->
                                {
                                    if (response.succeeded())
                                    {
                                        LOGGER.info(String.format("result saved successfully for backup %s ", item.getString(BACKUP_PROFILE_NAME)));

                                        vertx.eventBus().send(EventBusConstants.EVENT_SCHEDULER_COMPLETE, event);

                                        notify(event.getLong(EventBusConstants.EVENT_SCHEDULER), snapshot, item, storageProfile);
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("failed to save backup result for %s", item.getString(BACKUP_PROFILE_NAME)));
                                    }

                                    EventBusConstants.publish(UI_ACTION_BACKUP_STOP, event);
                                });
                    }
                    else
                    {
                        EventBusConstants.publish(UI_ACTION_BACKUP_STOP, event);

                        LOGGER.error(result.cause());
                    }
                });
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Retrieves backup snapshots for a specific backup profile.
     * <p>
     * This method queries the configuration database to get all backup snapshots
     * associated with the specified backup profile ID. If the query fails, it
     * returns an empty JsonArray to prevent null pointer exceptions.
     *
     * @param id the ID of the backup profile
     * @return a Future containing a JsonArray of backup snapshots
     */
    private Future<JsonArray> getBackupSnapshots(long id)
    {
        var promise = Promise.<JsonArray>promise();

        Bootstrap.configDBService().getAll(TBL_BACKUP_SNAPSHOT + id, result ->
        {
            if (result.succeeded())
            {
                promise.complete(result.result());
            }
            else
            {
                LOGGER.error(result.cause());

                promise.complete(new JsonArray());
            }
        });

        return promise.future();
    }

    /**
     * Sends notifications about backup operation results.
     * <p>
     * This method handles the notification process for backup operations by:
     * <ul>
     *   <li>Retrieving the scheduler configuration for the backup</li>
     *   <li>Sending email notifications with appropriate templates based on success/failure</li>
     *   <li>Sending SMS notifications if configured</li>
     * </ul>
     * <p>
     * The method formats different notification content based on whether the backup
     * succeeded or failed, including relevant details like duration, start time,
     * and error messages.
     *
     * @param id             the ID of the scheduler that triggered the backup
     * @param result         the JSON object containing the backup operation result
     * @param item           the backup profile configuration
     * @param storageProfile the storage profile used for the backup
     */
    private void notify(long id, JsonObject result, JsonObject item, JsonObject storageProfile)
    {
        try
        {
            var scheduler = SchedulerConfigStore.getStore().getItem(id);

            if (scheduler != null && scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
            {
                var context = new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png");

                if (result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    context.put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(DATABASE_BACKUP_COMPLETED_NOTIFICATION_SUBJECT, item.getString(BackupProfile.BACKUP_PROFILE_NAME)));

                    context.put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("successful.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS));

                    context.put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS))
                            .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_DATABASE_BACKUP_SUCCESSFUL_HTML_TEMPLATE)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(BACKUP_PROFILE_TYPE, item.getString(BACKUP_PROFILE_TYPE)).put(BACKUP_START_TIME, result.getString(BACKUP_START_TIME)).put(DURATION, DurationFormatUtils.formatDurationWords(result.getLong(DURATION), true, false)).put(StorageProfile.STORAGE_PROFILE_PROTOCOL, storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL)).put(TIME_STAMP, DateTimeUtil.timestamp()));
                }
                else
                {
                    var error = ErrorMessageConstants.INTERNAL_ERROR;

                    context.put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(ErrorMessageConstants.DATABASE_BACKUP_FAILED_NOTIFICATION_SUBJECT, item.getString(BackupProfile.BACKUP_PROFILE_NAME)));

                    if (result.getJsonArray(ERRORS) != null && !result.getJsonArray(ERRORS).isEmpty())
                    {
                        error = result.getJsonArray(ERRORS).getJsonObject(0).getString(MESSAGE);
                    }

                    context.put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS));

                    context.put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_DATABASE_BACKUP_FAILED_HTML_TEMPLATE);
                    context.put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(BACKUP_PROFILE_TYPE, item.getString(BACKUP_PROFILE_TYPE)).put(BACKUP_START_TIME, result.getString(BACKUP_START_TIME)).put(DURATION, DurationFormatUtils.formatDurationWords(result.getLong(DURATION), true, false)).put(StorageProfile.STORAGE_PROFILE_PROTOCOL, storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL)).put(MESSAGE, error).put(TIME_STAMP, DateTimeUtil.timestamp()));
                }

                Notification.sendEmail(context);
            }

            if (scheduler != null && scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
            {
                Notification.sendSMS(String.format(DATABASE_BACKUP_NOTIFICATION_MESSAGE, item.getString(result.getString(BACKUP_PROFILE_TYPE))),
                        scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Manages backup retention based on configured policies.
     * <p>
     * This method implements the backup retention policy for configuration database backups.
     * When the number of successful backups exceeds the maximum allowed (MAX_LOCAL_BACKUP_COPIES),
     * it removes the oldest successful backup to maintain the retention limit.
     * <p>
     * The method only applies retention policies to configuration database backups
     * and only processes local storage profiles.
     *
     * @param storageProfile the storage profile used for the backup
     * @param item           the backup profile configuration
     * @param result         the result of the current backup operation
     * @param snapshots      the array of existing backup snapshots
     */
    private void qualifyRetentionDays(JsonObject storageProfile, JsonObject item, JsonObject result, JsonArray snapshots)
    {
        if (item.getString(BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName()))
        {
            var count = snapshots.stream().filter(snapshot -> JsonObject.mapFrom(snapshot).getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)).count();

            count = result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) ? count + 1 : count;

            if (count > MAX_LOCAL_BACKUP_COPIES)
            {
                LOGGER.info(String.format("%s backup profile qualified for retention", item.getString(BACKUP_PROFILE_NAME)));

                while (!snapshots.isEmpty())
                {
                    var snapshot = snapshots.getJsonObject(0);

                    if (snapshot.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        var tokens = snapshot.getString(SRC_FILE_PATH).split(GlobalConstants.PATH_SEPARATOR.equalsIgnoreCase("\\") ? "\\\\" : GlobalConstants.PATH_SEPARATOR);

                        var storagePath = storageProfile.getJsonObject(StorageProfile.STORAGE_PROFILE_CONTEXT).getString(STORAGE_PATH);

                        FileUtils.deleteQuietly(new File(storagePath.endsWith(PATH_SEPARATOR) ? storagePath + tokens[tokens.length - 1] : storagePath + PATH_SEPARATOR + tokens[tokens.length - 1]));

                        snapshots.remove(0);

                        break;
                    }
                    else
                    {
                        snapshots.remove(0);
                    }
                }
            }
        }
    }

    /**
     * Retrieves the context information for an event.
     * <p>
     * This method enriches the event object with additional context information
     * such as the session ID and entity ID by querying the event tracker.
     * <p>
     * For events that already have context information (like heartbeat, health,
     * and registration events), the method simply returns the original event.
     *
     * @param event the event object to enrich with context information
     * @return a Future containing the enriched event object
     */
    private Future<JsonObject> getContext(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        if (event.getLong(EVENT_ID) != null) // heartbeat,health and registration event body have no event.id key
        {
            vertx.eventBus().<JsonObject>request(EVENT_TRACKER, event, reply ->
            {
                if (reply.succeeded())
                {
                    var context = reply.result().body();

                    event.put(ID, context.getJsonObject(EVENT_CONTEXT).getLong(ID))
                            .put(SESSION_ID, context.getJsonObject(EVENT_CONTEXT).getString(SESSION_ID));
                }

                promise.complete(event);
            });
        }
        else
        {
            promise.complete(event);
        }

        return promise.future();
    }

    /**
     * Processes agent start events.
     * <p>
     * This method handles the response from agent start operations by:
     * <ul>
     *   <li>Updating the agent status in the configuration store</li>
     *   <li>Enabling metrics for the agent if necessary</li>
     *   <li>Sending notifications about the agent start status</li>
     *   <li>Publishing UI update events</li>
     * </ul>
     * <p>
     * The method handles both successful and failed agent start operations.
     *
     * @param event the JSON object containing the agent start event data
     */
    private void processStartEvent(JsonObject event)
    {
        try
        {
            LOGGER.info(String.format("agent start event: %s", event.encodePrettily()));

            var eventSource = event.getString(AGENT_TYPE);

            complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

            if (AGENT.equalsIgnoreCase(eventSource)) // start agent event is for main agent
            {
                var agentId = event.getLong(ID);

                var agent = AgentConfigStore.getStore().getItem(agentId);

                if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
                {
                    if (agent.containsKey(AGENT_STATUS_TYPE) && agent.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentConstants.AgentStatusType.PING.getName()))
                    {

                        vertx.eventBus().send(EVENT_METRIC_ENABLE, MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(ObjectConfigStore.getStore().getItemByAgentId(agentId).getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName())).put(Metric.METRIC_STATE, NMSConstants.State.ENABLE.name()));
                    }

                    event.put(MESSAGE, String.format(InfoMessageConstants.START_SUCCEEDED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT,
                            new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_START)
                                    .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING)
                                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                    .put(DURATION, convertTime(DateTimeUtil.currentSeconds()))
                                    .put(ID, agentId).put(AGENT_UUID, agent.getString(AGENT_UUID))
                                    .put(MESSAGE, event.getString(MESSAGE))
                                    .put(AGENT_STATE, agent.getString(AGENT_STATE)));

                    updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agentId), STATUS_UP, DateTimeUtil.currentSeconds(), NMSConstants.STATE_RUNNING, DateTimeUtil.currentSeconds());
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_START).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(ID, agentId).put(MESSAGE, event.getString(MESSAGE)));
                }

                publishUserNotificationEvent(event);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent stop events.
     * <p>
     * This method handles the response from agent stop operations by:
     * <ul>
     *   <li>Updating the agent status in the configuration store</li>
     *   <li>Disabling metrics for the agent if necessary</li>
     *   <li>Sending notifications about the agent stop status</li>
     *   <li>Publishing UI update events</li>
     * </ul>
     * <p>
     * The method handles both successful and failed agent stop operations and
     * updates the agent state accordingly.
     *
     * @param event the JSON object containing the agent stop event data
     */
    private void processStopEvent(JsonObject event)
    {
        try
        {
            var eventSource = event.getString(AGENT_TYPE);

            var agentId = event.getLong(ID);

            complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

            // If failed to start agent by any reason then we will send motadata agent manager to stop that agent and at that time here id is null
            if (agentId != null && AgentConstants.AGENT.equalsIgnoreCase(eventSource))
            {
                var agent = AgentConfigStore.getStore().getItem(agentId);

                if (GlobalConstants.STATUS_SUCCEED.equals(event.getString(GlobalConstants.STATUS)))
                {
                    LOGGER.info(String.format("agent %s stopped successfully...", agentId));

                    event.put(MESSAGE, String.format(InfoMessageConstants.STOP_SUCCEEDED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                    if (agent.containsKey(AGENT_STATUS_TYPE) && agent.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentConstants.AgentStatusType.PING.getName()))
                    {

                        vertx.eventBus().send(EVENT_METRIC_DISABLE, MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(ObjectConfigStore.getStore().getItemByAgentId(agentId).getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName())).put(Metric.METRIC_STATE, NMSConstants.State.DISABLE.name()));
                    }

                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_STOP)
                            .put(DURATION, convertTime(DateTimeUtil.currentSeconds()))
                            .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING)
                            .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                            .put(ID, agentId).put(AGENT_UUID, agent.getString(AGENT_UUID))
                            .put(MESSAGE, event.getString(MESSAGE))
                            .put(AGENT_STATE, agent.getString(AGENT_STATE)));

                    if (agent.containsKey(AGENT_STATE) && agent.getString(AGENT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                    {
                        updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agentId), agent.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentStatusType.HEARTBEAT.getName()) ? agent.getString(AGENT_STATUS) : STATUS_DOWN, DateTimeUtil.currentSeconds(), NMSConstants.STATE_NOT_RUNNING, DateTimeUtil.currentSeconds());
                    }
                    else
                    {
                        AgentCacheStore.getStore().updateItem(agentId, new JsonObject().put(STATUS, GlobalConstants.STATUS_DOWN)
                                .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING)
                                .put(DURATION, DateTimeUtil.currentSeconds())
                                .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));
                    }
                }
                else
                {
                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_STOP)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ID, agentId).put(AGENT_UUID, agent.getString(AGENT_UUID))
                            .put(MESSAGE, event.getString(MESSAGE)));
                }

                publishUserNotificationEvent(event);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent restart events.
     * <p>
     * This method handles the response from agent restart operations by:
     * <ul>
     *   <li>Updating the agent status in the configuration store</li>
     *   <li>Sending notifications about the agent restart status</li>
     *   <li>Publishing UI update events</li>
     * </ul>
     * <p>
     * The method handles both successful and failed agent restart operations and
     * updates the agent state accordingly.
     *
     * @param event the JSON object containing the agent restart event data
     */
    private void processRestartEvent(JsonObject event)
    {
        try
        {
            var agentId = event.getLong(ID);

            complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

            if (event.getString(AGENT_TYPE) == null || AGENT.equalsIgnoreCase(event.getString(AGENT_TYPE)))
            {
                if (GlobalConstants.STATUS_SUCCEED.equals(event.getString(GlobalConstants.STATUS)))
                {
                    event.put(MESSAGE, String.format(InfoMessageConstants.RESTART_SUCCEEDED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_RESTART)
                            .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING)
                            .put(DURATION, convertTime(DateTimeUtil.currentSeconds()))
                            .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                            .put(ID, agentId)
                            .put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                            .put(MESSAGE, event.getString(MESSAGE)));

                    updateStatus(ObjectConfigStore.getStore().getItemByAgentId(agentId), STATUS_UP, DateTimeUtil.currentSeconds(), NMSConstants.STATE_RUNNING, DateTimeUtil.currentSeconds());
                }

                else
                {
                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_RESTART)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ID, agentId).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                            .put(MESSAGE, event.getString(MESSAGE)));
                }
            }

            publishUserNotificationEvent(event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent delete events.
     * <p>
     * This method handles the response from agent delete operations by:
     * <ul>
     *   <li>Sending notifications about the agent deletion status</li>
     *   <li>Publishing UI update events</li>
     *   <li>Handling any cleanup required after agent deletion</li>
     * </ul>
     * <p>
     * The method handles both successful and failed agent delete operations.
     *
     * @param event the JSON object containing the agent delete event data
     */
    private void processDeleteEvent(JsonObject event)
    {
        try
        {
            var agentId = event.getLong(ID);

            complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

            if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
            {
                event.put(MESSAGE, String.format(InfoMessageConstants.DELETE_SUCCEEDED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_DELETE).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(ID, agentId).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                        .put(MESSAGE, event.getString(MESSAGE)));
            }

            else
            {
                publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_DELETE).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(SESSION_ID, event.getString(SESSION_ID)).put(ID, agentId)
                        .put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId)).put(MESSAGE, event.getString(MESSAGE)));
            }

            publishUserNotificationEvent(event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent upgrade events.
     * <p>
     * This method handles the response from agent upgrade operations by:
     * <ul>
     *   <li>Tracking and updating the upgrade progress</li>
     *   <li>Updating the artifact configuration with upgrade status</li>
     *   <li>Sending notifications about the upgrade status</li>
     *   <li>Publishing UI update events</li>
     * </ul>
     * <p>
     * The method handles both successful and failed upgrade operations and
     * maintains a record of the upgrade history.
     *
     * @param event the JSON object containing the agent upgrade event data
     */
    private void processUpgradeEvent(JsonObject event)
    {
        try
        {
            LOGGER.info(String.format("agent %s upgrade process %s", event.getLong(ID), event.getInteger(PROGRESS)));

            AgentCacheStore.getStore().updateProgress(event.getLong(ID), event.getInteger(PROGRESS));

            if (event.getInteger(PROGRESS) == 100 || event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
            {
                var agentId = event.getLong(ID);

                complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

                var artifact = ArtifactConfigStore.getStore().getItem(agentId)
                        .put(ARTIFACT_UPGRADE_STATUS, event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) ?
                                String.format("Last upgraded at %s", DateTimeUtil.timestamp())
                                : String.format("Last upgrade failed at %s", DateTimeUtil.timestamp()))
                        .put(MESSAGE, event.getString(MESSAGE, EMPTY_VALUE));

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact).put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()).put(ID, agentId).put(EVENT_COPY_REQUIRED, false));

                AgentCacheStore.getStore().clearProgress(agentId);

                var item = AgentCacheStore.getStore().getItem(agentId)
                        .put(DURATION, DateTimeUtil.currentSeconds()).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());

                if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
                {
                    item.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING)
                            .put(STATUS, STATUS_UP);

                    event.put(MESSAGE, String.format(InfoMessageConstants.UPGRADE_SUCCEEDED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_UPGRADE)
                            .put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                            .put(EVENT_TIMESTAMP, item.getLong(EVENT_TIMESTAMP))
                            .put(DURATION, convertTime(item.getLong(DURATION)))
                            .put(ID, agentId).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                            .put(MESSAGE, event.getString(MESSAGE)));
                }
                else
                {
                    item.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING)
                            .put(STATUS, STATUS_DOWN);

                    event.put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED, APIConstants.Entity.AGENT.getName(), ObjectConfigStore.getStore().getAgentObjectName(agentId)));

                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_UPGRADE)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ID, agentId).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(agentId))
                            .put(MESSAGE, event.getString(MESSAGE)));
                }

                AgentCacheStore.getStore().updateItem(agentId, item);

                publish(UI_NOTIFICATION_AGENT_UPGRADE_PROGRESS, event.put(ARTIFACT_UPGRADE_STATUS, artifact.getString(ARTIFACT_UPGRADE_STATUS)).put(AGENT_VERSION, ArtifactConfigStore.getStore().getItem(agentId).getString(MotadataApp.ARTIFACT_VERSION)));

                publishUserNotificationEvent(event);
            }
            else
            {
                publish(UI_NOTIFICATION_AGENT_UPGRADE_PROGRESS, event);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes agent state change events.
     * <p>
     * This method handles agent enable/disable operations by:
     * <ul>
     *   <li>Completing the event processing with appropriate bootstrap type</li>
     *   <li>Updating agent configuration in the database</li>
     *   <li>Refreshing the agent configuration in the cache store</li>
     *   <li>Publishing notifications to the UI about the state change</li>
     * </ul>
     * <p>
     * The method handles both successful and failed state change operations,
     * ensuring that the agent configuration is properly updated and all relevant
     * components are notified about the state change.
     *
     * @param event the JSON object containing the agent state change event data
     */
    private void processStateEvent(JsonObject event)
    {
        complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.AGENT.name()));

        if (STATUS_SUCCEED.equalsIgnoreCase(event.getString(STATUS)))
        {
            event.put(MESSAGE, String.format(event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_DISABLE) ? InfoMessageConstants.AGENT_DISABLED_SUCCEEDED : InfoMessageConstants.AGENT_ENABLED_SUCCEEDED, ObjectConfigStore.getStore().getAgentObjectName(event.getLong(ID))));

            //update new configuration
            Bootstrap.configDBService().update(TBL_AGENT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                    new JsonObject().put(AGENT_CONFIGS, event.getJsonObject(AGENT_CONFIGS).encode()),
                    event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER, event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            AgentConfigStore.getStore().updateItem(event.getLong(ID));

                            publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE)
                                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(ID, event.getLong(ID)).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(event.getLong(ID)))
                                    .put(MESSAGE, event.getString(MESSAGE)));
                        }

                        else
                        {
                            LOGGER.warn(String.format("failed to update agent configuration for agent %s", event.getLong(ID)));
                        }
                    });
        }

        else
        {
            publish(event.getString(SESSION_ID), UI_NOTIFICATION_AGENT, new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_CONFIGURATION_CHANGE).put(STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ID, event.getLong(ID)).put(AGENT_UUID, AgentConfigStore.getStore().getAgentUUID(event.getLong(ID)))
                    .put(MESSAGE, event.getString(MESSAGE)));
        }

        publishUserNotificationEvent(event);
    }

    /**
     * Processes manager upgrade events.
     * <p>
     * This method handles the response from manager upgrade operations by:
     * <ul>
     *   <li>Propagating successful upgrades to all agent managers</li>
     *   <li>Updating remote event processors with the new manager version</li>
     *   <li>Coordinating the upgrade process across the distributed system</li>
     * </ul>
     * <p>
     * When the master manager is successfully upgraded, this method ensures that
     * all connected agent managers and remote event processors are also updated
     * to maintain version consistency across the system.
     *
     * @param event the JSON object containing the manager upgrade event data
     */
    private void processManagerUpgradeEvent(JsonObject event)
    {
        // if master manager upgrade succeed then send event to all agent/collector's manager/
        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("Manager Upgrade event : %s", event.encodePrettily()));
        }

        var uuid = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name()).getString(REMOTE_EVENT_PROCESSOR_UUID);

        if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
        {
            var items = AgentConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(AGENT_UUID) && RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, item.getString(AGENT_UUID)) == null)
                {
                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                            .put(EVENT_TYPE, EVENT_MANAGER_UPGRADE)
                            .put(AGENT_UUID, item.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                            .put(MotadataAppManager.PATCH_ARTIFACT_FILE, event.getString(MotadataAppManager.PATCH_ARTIFACT_FILE)));

                    LOGGER.info(String.format("updating manager of %s agent", ObjectConfigStore.getStore().getAgentObjectName(item.getLong(ID))));
                }
            }

            items = RemoteEventProcessorConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(REMOTE_EVENT_PROCESSOR_UUID) && !item.getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(uuid))
                {
                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                            .put(EVENT_TYPE, EVENT_MANAGER_UPGRADE)
                            .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                            .put(DISABLED, YES)
                            .put(MotadataAppManager.PATCH_ARTIFACT_FILE, event.getString(MotadataAppManager.PATCH_ARTIFACT_FILE))
                            .put(REMOTE_EVENT_PROCESSOR_TYPE, item.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                    LOGGER.info(String.format("updating manager of %s remote event processor ", item.getString(REMOTE_EVENT_PROCESSOR_HOST)));
                }
            }
        }
        else
        {
            LOGGER.warn("master manager upgrade failed ...");
        }
    }

    /**
     * Updates the status of an object in the system.
     * <p>
     * This method updates the availability status of an object by:
     * <ul>
     *   <li>Retrieving the metric configuration for the object</li>
     *   <li>Updating the status, timestamp, heartbeat state, and duration</li>
     *   <li>Publishing the updated status to the metric poll response event bus</li>
     * </ul>
     * <p>
     * This method is primarily used to update the availability status of agents
     * and other monitored objects in the system.
     *
     * @param object        the object whose status needs to be updated
     * @param status        the new status value (e.g., "up", "down")
     * @param timestamp     the timestamp of the status update
     * @param heartbeatType the type of heartbeat state
     * @param duration      the duration value for the status
     */
    private void updateStatus(JsonObject object, String status, long timestamp, String heartbeatType, long duration)
    {
        if (object != null)
        {
            var item = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName()));

            if (item != null)
            {
                item.mergeIn(object).mergeIn(item.getJsonObject(Metric.METRIC_CONTEXT));

                item.put(RESULT, new JsonObject().put(STATUS, status).put(AIOpsObject.OBJECT_TARGET, object.getString(AIOpsObject.OBJECT_IP))).put(EVENT_TYPE, EVENT_METRIC_POLL).put(STATUS, STATUS_SUCCEED).put(EVENT_TIMESTAMP, timestamp).put(MotadataAppManager.HEARTBEAT_STATE, heartbeatType).put(DURATION, duration);

                vertx.eventBus().publish(EVENT_METRIC_POLL_RESPONSE, item);
            }
        }
    }

    /**
     * Processes remote event processor responses.
     * <p>
     * This method handles responses from remote event processors by:
     * <ul>
     *   <li>Completing the event processing with appropriate bootstrap type</li>
     *   <li>Updating the status of remote event processors in the cache store</li>
     *   <li>Publishing notifications to the UI about the event status</li>
     *   <li>Formatting appropriate messages based on the event outcome</li>
     * </ul>
     * <p>
     * The method handles both successful and failed operations, updating the system
     * state accordingly and ensuring that all relevant components are notified.
     *
     * @param event         the JSON object containing the event data
     * @param filter        the filter to identify the specific remote event processor
     * @param action        the action type being performed (e.g., start, stop, restart)
     * @param messagePrefix the prefix for the message to be displayed
     * @param state         the state to set for the remote event processor
     */
    private void process(JsonObject event, String filter, String action, String messagePrefix, String state)
    {
        try
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("remote event processor %s event: %s", filter, event.encodePrettily()));
            }

            complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.COLLECTOR.name()));

            var id = event.getLong(ID);

            if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
            {
                if (state != null)
                {
                    var item = RemoteEventProcessorCacheStore.getStore().getItem(id)
                            .put(DURATION, DateTimeUtil.currentSeconds())
                            .put(STATUS, STATUS_UP)
                            .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(MotadataAppManager.HEARTBEAT_STATE, state);

                    RemoteEventProcessorCacheStore.getStore().updateItem(id, item);
                }

                event.put(MESSAGE, String.format(messagePrefix, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

                EventBusConstants.publish(event.getString(SESSION_ID), UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EventBusConstants.EVENT_TYPE, action)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(DURATION, DateTimeUtil.currentSeconds())
                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                        .put(ID, id).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                        .put(MESSAGE, event.getString(MESSAGE))
                        .put(MotadataAppManager.HEARTBEAT_STATE, state));
            }
            else
            {
                EventBusConstants.publish(event.getString(SESSION_ID), UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EventBusConstants.EVENT_TYPE, action)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                        .put(ID, id).put(MESSAGE, event.getString(MESSAGE)));
            }

            EventBusConstants.publishUserNotificationEvent(event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes upgrade events for remote event processors.
     * <p>
     * This method handles the upgrade process for remote event processors by:
     * <ul>
     *   <li>Tracking and updating the upgrade progress in the cache store</li>
     *   <li>Handling completion of the upgrade process (success or failure)</li>
     *   <li>Updating artifact configuration with upgrade status information</li>
     *   <li>Notifying the UI about the upgrade status</li>
     *   <li>Updating the remote event processor state based on upgrade outcome</li>
     * </ul>
     * <p>
     * The method manages both successful and failed upgrades, ensuring that
     * the system state is properly updated and all relevant components are notified.
     *
     * @param event the JSON object containing the upgrade event data
     */
    private void processUpgrade(JsonObject event)
    {
        try
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("remote event processor %s upgrade event: %s", event.getLong(ID), event.getInteger(PROGRESS)));
            }

            RemoteEventProcessorCacheStore.getStore().updateProgress(event.getLong(ID), event.getInteger(PROGRESS));

            if (event.getInteger(PROGRESS) == 100 || event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
            {
                var id = event.getLong(ID);

                complete(event, event.getString(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.COLLECTOR.name()));

                var artifact = ArtifactConfigStore.getStore().getItem(id)
                        .put(ARTIFACT_UPGRADE_STATUS, event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) ?
                                String.format("Last upgraded at %s", DateTimeUtil.timestamp())
                                : String.format("Last upgrade failed at %s", DateTimeUtil.timestamp()))
                        .put(MESSAGE, event.getString(MESSAGE, EMPTY_VALUE));

                vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_ARTIFACT.name()).put(EVENT_CONTEXT, artifact)
                        .put(MotadataApp.ARTIFACT_TYPE, RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE)).put(ID, id));

                RemoteEventProcessorCacheStore.getStore().clearProgress(id);

                var item = RemoteEventProcessorCacheStore.getStore().getItem(id)
                        .put(DURATION, DateTimeUtil.currentSeconds()).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());

                if (GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(event.getString(GlobalConstants.STATUS)))
                {
                    event.put(MESSAGE, String.format(InfoMessageConstants.UPGRADE_SUCCEEDED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST)));

                    item.put(STATUS, STATUS_UP).put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_RUNNING);

                    EventBusConstants.publish(event.getString(SESSION_ID), UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_UPGRADE)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                            .put(DURATION, DateTimeUtil.currentSeconds())
                            .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                            .put(ID, id).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                            .put(MESSAGE, event.getString(MESSAGE)));
                }
                else
                {
                    item.put(STATUS, STATUS_DOWN).put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_RUNNING);

                    event.put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName(), RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST)));

                    EventBusConstants.publish(event.getString(SESSION_ID), UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_UPGRADE)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                            .put(ID, id).put(MESSAGE, event.getString(MESSAGE)));
                }

                RemoteEventProcessorCacheStore.getStore().updateItem(id, item);

                publish(UI_NOTIFICATION_REMOTE_EVENT_PROCESSOR_UPGRADE_PROGRESS, event.put(ARTIFACT_UPGRADE_STATUS, artifact.getString(ARTIFACT_UPGRADE_STATUS)).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, ArtifactConfigStore.getStore().getItem(id).getString(MotadataApp.ARTIFACT_VERSION)));

                EventBusConstants.publishUserNotificationEvent(event);
            }
            else
            {
                EventBusConstants.publish(UI_NOTIFICATION_REMOTE_EVENT_PROCESSOR_UPGRADE_PROGRESS, event);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Completes an operation and updates the system state accordingly.
     * <p>
     * This method handles the completion of operations for different bootstrap types:
     * <ul>
     *   <li>For agents, it updates the agent cache and enables the agent if it was disabled</li>
     *   <li>For collectors, event processors, and datastores, it updates their cache and publishes
     *       notification events</li>
     * </ul>
     * <p>
     * The method ensures that after an operation completes, the system components are
     * properly enabled to allow further operations.
     *
     * @param event         the JSON object containing the event data
     * @param bootstrapType the type of bootstrap component (AGENT, COLLECTOR, etc.)
     */
    private void complete(JsonObject event, String bootstrapType)
    {
        try
        {
            if (bootstrapType.equalsIgnoreCase(BootstrapType.AGENT.name()))
            {
                if (event.containsKey(ID)) //improvements - 3636 -> now allow user to perform another actions
                {
                    var item = AgentCacheStore.getStore().getItem(event.getLong(ID));

                    AgentCacheStore.getStore().updateDuration(event.getLong(ID), NO);

                    if (item.containsKey(AGENT_DISABLE) && item.getString(AGENT_DISABLE).equalsIgnoreCase(YES))
                    {
                        AgentCacheStore.getStore().updateItem(event.getLong(ID), item.put(AGENT_DISABLE, NO));

                        EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().put(EVENT_TYPE, EVENT_AGENT_ACTION).put(ID, event.getLong(ID)).put(AGENT_DISABLE, NO));
                    }
                }
            }
            else if (bootstrapType.equalsIgnoreCase(BootstrapType.COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_PROCESSOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.EVENT_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.FLOW_COLLECTOR.name())
                    || bootstrapType.equalsIgnoreCase(BootstrapType.DATASTORE.name()))
            {
                if (event.containsKey(ID)) //now allow user to perform another actions
                {
                    RemoteEventProcessorCacheStore.getStore().updateDuration(event.getLong(ID), NO);

                    EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EVENT_TYPE, EVENT_REMOTE_EVENT_PROCESSOR_ACTION).put(ID, event.getLong(ID))
                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                            .put(DISABLED, NO));
                }
            }

            EventBusConstants.complete(event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Monitors and updates the status of remote event processors.
     * <p>
     * This method sets up a periodic task that runs every 10 seconds to:
     * <ul>
     *   <li>Check the availability of remote event processors</li>
     *   <li>Mark processors as down if no heartbeat is received for 30 seconds</li>
     *   <li>Re-enable processors that have been disabled for more than 90 seconds</li>
     *   <li>Update the UI with the current status of processors</li>
     *   <li>Detect and report datastore connectivity issues</li>
     * </ul>
     * <p>
     * The method implements an automatic recovery mechanism for processors that
     * might be temporarily unavailable, preventing them from being permanently disabled.
     */
    private void update()
    {
        /*
         * Execute timer on every 10 sec to check remote event processor availability
         * If last received heartbeat is greater than 30 sec delete that event processor from cache store that will mark it as a down
         */
        vertx.setPeriodic(10000L, timer ->
        {
            var items = RemoteEventProcessorConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                try
                {
                    var item = items.getJsonObject(index);

                    // Get the duration for which the remote event processor has been in its current state
                    var duration = RemoteEventProcessorCacheStore.getStore().getStateDuration(item.getLong(ID));

                    // Auto-recovery mechanism: If a remote event processor has been disabled for more than 90 seconds,
                    // automatically re-enable it to prevent permanent disablement due to temporary issues.
                    // This helps in recovering from transient network problems or brief service interruptions.
                    if (duration > 0 && (DateTimeUtil.currentSeconds() - duration) >= 90)
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug(String.format("%s remote event processor action state enabled, reason : %s", item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), "flap timer limit exceeded..."));
                        }

                        RemoteEventProcessorCacheStore.getStore().updateDuration(item.getLong(ID), NO);

                        EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EVENT_TYPE, EVENT_REMOTE_EVENT_PROCESSOR_ACTION).put(ID, item.getLong(ID))
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                .put(DISABLED, NO));
                    }

                    var cache = RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID));

                    if (!cache.containsKey(EVENT_TIMESTAMP))
                    {
                        cache.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                    }

                    // Datastore health monitoring: Datastores are critical components that require immediate attention when down
                    // If a datastore hasn't sent a heartbeat for 10+ seconds, consider it down and publish its status immediately
                    // This shorter timeout (compared to the 30 seconds for other processors) reflects the critical nature of datastores
                    if (item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()) && DateTimeUtil.currentSeconds() - cache.getLong(EVENT_TIMESTAMP) >= 10)
                    {
                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_PING, new JsonObject().put(STATUS, STATUS_DOWN).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));

                        LOGGER.warn(String.format("datastore : %s is down : duration : %s ", item.getString(REMOTE_EVENT_PROCESSOR_UUID), DateTimeUtil.currentSeconds() - cache.getLong(EVENT_TIMESTAMP)));
                    }

                    // Remote event processor availability check: Mark processors as down if no heartbeat received for 30+ seconds
                    // This is the standard timeout for non-datastore processors (datastores use a shorter 10-second timeout)
                    if (DateTimeUtil.currentSeconds() - cache.getLong(EVENT_TIMESTAMP) >= 30)
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("item is not reachable %s ", cache.encode()));
                        }

                        // Initialize heartbeat state if not already set
                        if (!cache.containsKey(MotadataAppManager.HEARTBEAT_STATE))
                        {
                            cache.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_REACHABLE);
                        }

                        // State transition handling: If the processor was previously running, we need to:
                        // 1. Update its state to NOT_REACHABLE
                        // 2. Set the duration to when it became unreachable (current time minus timeout period)
                        // This helps in tracking how long the processor has been unreachable
                        if (cache.getString(MotadataAppManager.HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_RUNNING))
                        {
                            cache.put(MotadataAppManager.HEARTBEAT_STATE, NMSConstants.STATE_NOT_REACHABLE).put(DURATION, DateTimeUtil.currentSeconds() - 30);
                        }

                        cache.put(STATUS, STATUS_DOWN);

                        RemoteEventProcessorCacheStore.getStore().updateItem(item.getLong(ID), cache);

/*                            EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().mergeIn(item).mergeIn(cache)
                                .put(DURATION, DateTimeUtil.convertTime(cache.getLong(DURATION)))
                                .put(EventBusConstants.EVENT_TYPE, EVENT_REMOTE_PROCESSOR_HEARTBEAT)
                                .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                .put(MotadataAppManager.HEARTBEAT_STATE, cache.getString(MotadataAppManager.HEARTBEAT_STATE))
                                .put(EVENT_TIMESTAMP, cache.getLong(EVENT_TIMESTAMP)));*/
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }

    /**
     * Writes remote event processor cache data to persistent storage.
     * <p>
     * This method performs the following operations:
     * <ul>
     *   <li>Checks if a write operation is already pending to avoid concurrent writes</li>
     *   <li>Executes the write operation in a blocking context to prevent I/O interference</li>
     *   <li>Collects all remote event processor data from the cache store</li>
     *   <li>Compresses the data and writes it to the cache file</li>
     * </ul>
     * <p>
     * The method uses a flag to track pending write operations and ensures that
     * the cache file is created if it doesn't exist. The data is compressed before
     * writing to minimize storage requirements.
     */
    private void write()
    {
        var promise = Promise.<Void>promise();

        if (!writePending)
        {
            writePending = true;

            vertx.<Void>executeBlocking(future ->
            {
                try
                {
                    if (RemoteEventProcessorConfigStore.getStore().existItems())
                    {
                        if (CommonUtil.debugEnabled())
                        {
                            LOGGER.debug("remote event processor cache updating started successfully....");
                        }

                        var items = new JsonArray();

                        for (var remoteEventProcessor : RemoteEventProcessorConfigStore.getStore().flatMap().values())
                        {
                            var item = RemoteEventProcessorCacheStore.getStore().getItem(remoteEventProcessor.getLong(ID));

                            item.remove(DISABLED); // do not need to save disable status on cache

                            items.add(item.put(ID, remoteEventProcessor.getLong(ID)));
                        }

                        if (!vertx.fileSystem().existsBlocking(REMOTE_EVENT_PROCESSOR_CACHE_PATH))
                        {
                            vertx.fileSystem().createFileBlocking(REMOTE_EVENT_PROCESSOR_CACHE_PATH);
                        }

                        var bytes = CodecUtil.compress(items.encode());

                        if (bytes != null && bytes.length > 0)
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("updating %s bytes of remote event processor cache", bytes.length));
                            }

                            dirty = true;

                            vertx.fileSystem().writeFileBlocking(REMOTE_EVENT_PROCESSOR_CACHE_PATH, Buffer.buffer(bytes));

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug("remote event processor cache updated successfully....");
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                future.complete();
            }, false, result ->
            {
                writePending = false;

                promise.complete();
            });
        }
        else
        {
            promise.complete();
        }

        promise.future();
    }

    /**
     * Stops the ManagerResponseProcessor verticle.
     * <p>
     * This method is called when the verticle is undeployed. It completes the provided
     * promise to signal that the verticle has been successfully stopped.
     * <p>
     * In this implementation, the method simply completes the promise without
     * performing any additional cleanup, as there are no resources that need to be
     * explicitly released.
     *
     * @param promise the promise to complete when the verticle is stopped
     * @throws Exception if an error occurs during the stop process
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        promise.complete();
    }
}
