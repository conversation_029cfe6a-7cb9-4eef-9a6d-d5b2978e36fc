/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*   Change Logs:
 *   Date           Author              Notes
 *   06-Feb-2025    <PERSON><PERSON> Sharma        Added Compliance as Plugin Engine Request
 *   20-Feb-2025    Pruthviraj          Added NetRoute as Plugin Engine Request
 *   24-Mar-2025    Sankalp             Added constants
 *   16-Apr-2025    Bharat              MOTADATA-5798: Enhance compliance in the plugin engine by implementing batching for CLI sessions.
 *   20-May-2025    Aagam               MOTADATA-5847 : Added windows build support
 */


package com.mindarray.plugin;

import com.mindarray.api.Metric;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.api.TopologyPlugin;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.ObjectManagerCacheStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.MOTADATA_PLUGIN_ENGINE;
import static com.mindarray.nms.NMSConstants.PYTHON_METRIC_TYPES;

/**
 * The PluginEngineConstants class provides constants, enums, and utility methods used by the plugin engine system.
 * It defines the types of plugin engine requests, plugin engines, and various configuration parameters.
 * <p>
 * This class serves as a central repository for all constants related to plugin engine operations,
 * ensuring consistency across the system and making it easier to manage configuration parameters.
 * <p>
 * Key features include:
 * 1. Constants for plugin engine requests and parameters
 * 2. Enums for request types and plugin engine types
 * 3. Worker allocation configuration for different request types
 * 4. Utility methods for determining plugin engine types and batch support
 */
public final class PluginEngineConstants
{
    /**
     * Key for specifying the type of plugin engine request
     */
    public static final String PLUGIN_ENGINE_REQUEST = "plugin.engine.request";

    /**
     * Key for specifying the plugin engine to use
     */
    public static final String PLUGIN_ENGINE = "plugin.engine";

    /**
     * Key for command to be executed (used for SSH, PowerShell, and JDBC)
     */
    public static final String COMMAND = "command";

    /**
     * Key for script content (used for custom plugin types)
     */
    public static final String SCRIPT = "script";

    /**
     * Key for parsing script content
     */
    public static final String PARSING_SCRIPT = "parsing.script";

    /**
     * Key for script language (python, go, etc.)
     */
    public static final String SCRIPT_LANGUAGE = "script.language";

    /**
     * Key for script protocol (SSH, HTTP, PowerShell, etc.)
     */
    public static final String SCRIPT_PROTOCOL = "script.protocol";

    /**
     * Key for plugin engine runtimes configuration
     */
    public static final String PLUGIN_ENGINE_RUNTIMES = "plugin.engine.runtimes";

    /**
     * Logger instance for the PluginEngineConstants class
     */
    private static final Logger LOGGER = new Logger(PluginEngineConstants.class, MOTADATA_PLUGIN_ENGINE, "Plugin Engine Constants");

    /**
     * Default plugin engine runtime from configuration
     */
    private static final String PLUGIN_ENGINE_RUNTIME = MotadataConfigUtil.getPluginEngine();

    /**
     * Worker allocation percentages for different request types.
     * This map defines how the available workers should be distributed among different types of requests.
     * The percentages determine the proportion of workers allocated to each request type.
     * <p>
     * For example:
     * - RUNBOOK: 27% of workers
     * - STREAMING: 20% of workers
     * - NETWORK_METRIC: 14% of workers
     * - PING: 21% of workers
     * - PING_PROBE: 20% of workers
     * - COMPLIANCE: 5% of workers
     * - CONFIG: 15% of workers
     */
    private static final Map<PluginEngineRequest, Integer> PLUGIN_ENGINE_WORKERS_BY_REQUEST_PERCENT = Map.ofEntries(Map.entry(PluginEngineRequest.RUNBOOK, 27),
            Map.entry(PluginEngineRequest.STREAMING, 20),
            Map.entry(PluginEngineRequest.NETWORK_METRIC, 14),
            Map.entry(PluginEngineRequest.PING, 21),
            Map.entry(PluginEngineRequest.PING_PROBE, 20),
            Map.entry(PluginEngineRequest.COMPLIANCE, 5),
            Map.entry(PluginEngineRequest.CONFIG, 15));

    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private PluginEngineConstants()
    {
    }

    /**
     * Calculates the number of workers to allocate for a specific request type.
     * This method uses the percentage defined in PLUGIN_ENGINE_WORKERS_BY_REQUEST_PERCENT
     * and the total number of plugin engine workers to determine the allocation.
     *
     * @param request The plugin engine request type
     * @return The number of workers to allocate (minimum 1)
     */
    public static Integer getPluginEngineWorkersByRequest(PluginEngineRequest request)
    {
        var workers = (Math.round(CommonUtil.getFloat(PLUGIN_ENGINE_WORKERS_BY_REQUEST_PERCENT.getOrDefault(request, 0) * MotadataConfigUtil.getPluginEngineWorkers()) / 100));

        return workers == 0 ? 1 : workers;
    }

    /**
     * Determines if the given context represents a custom plugin.
     * This method checks various plugin type fields in the context to determine
     * if it's a custom plugin (topology, runbook, or metric).
     *
     * @param context The JSON context object to check
     * @return true if the context represents a custom plugin, false otherwise
     */
    public static boolean isCustomPlugin(JsonObject context)
    {
        var result = false;

        if (context.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE))
        {
            result = context.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.CUSTOM.getName());
        }
        else if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_TYPE))
        {
            result = context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE).equalsIgnoreCase(Runbook.RunbookPluginType.CUSTOM_SCRIPT.getName());
        }
        else if (context.containsKey(Metric.METRIC_PLUGIN))
        {
            result = context.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.CUSTOM.getName());
        }

        return result;
    }

    /**
     * Determines the appropriate plugin engine to use based on the event context.
     * This method analyzes the event type and other properties to select the correct
     * plugin engine (GO, PYTHON, etc.) for processing the event.
     * <p>
     * The selection logic considers:
     * - Event type (topology, plugin engine, metric poll, etc.)
     * - Plugin type (SNMP, custom script, etc.)
     * - Request type (runbook, network metric, config, etc.)
     *
     * @param event The JSON event object to analyze
     * @return The name of the plugin engine to use, or null if not determinable
     */
    public static String getPluginEngine(JsonObject event)
    {
        String pluginEngine = null;

        try
        {
            var eventType = event.getString(EventBusConstants.EVENT_TYPE);

            if (EventBusConstants.EVENT_TOPOLOGY.equalsIgnoreCase(eventType))
            {
                pluginEngine = event.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.SNMP.getName()) ? PluginEngine.GO.getName() : PluginEngine.PYTHON.getName();
            }
            else if (EventBusConstants.EVENT_PLUGIN_ENGINE.equalsIgnoreCase(eventType))
            {
                if (event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(PluginEngineRequest.RUNBOOK.name()))
                {
                    pluginEngine = switch (Runbook.RunbookPluginType.valueOfName(event.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE)))
                    {
                        case SNMP, POWERSHELL_SCRIPT, HTTP_SCRIPT, SSH_SCRIPT, TRACE_ROUTE, MAC_SCANNER ->
                                PluginEngine.GO.getName();

                        case DATABASE_SCRIPT, CUSTOM_SCRIPT, WHOIS_LOOKUP -> PluginEngine.PYTHON.getName();

                        default -> null;
                    };
                }
                else if (event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(PluginEngineRequest.NETWORK_METRIC.getName()))
                {
                    pluginEngine = PluginEngine.GO.getName();
                }
                else if (event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(PluginEngineRequest.CONFIG.getName()) || event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(PluginEngineRequest.COMPLIANCE.getName()))
                {
                    pluginEngine = PluginEngine.GO.getName();
                }
                //TODO
                /*else if (event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(PluginEngineRequest.STREAMING.getName())
                        && event.getString(StreamingEngineManager.STREAMING_TYPE).equalsIgnoreCase(StreamingEngineManager.StreamingType.METRIC_POLL.getName()))
                {
                    pluginEngine = ObjectManagerCacheStore.getStore().getPluginEngineByMetricPlugin(NMSConstants.getMetricPlugin(NMSConstants.Type.valueOfName(event.getString(Object.OBJECT_TYPE))));
                }*/
            }
            else if (NMSConstants.MetricPlugin.CUSTOM.getName().equalsIgnoreCase(event.getString(Metric.METRIC_PLUGIN)))
            {
                // Reason: Any custom script (having metric.poll as event type) by default qualifying for Python plugin-engine, as we have default entry in ObjectManagerCacheStore for custom-scripts.

                pluginEngine = !event.containsKey(EventBusConstants.EVENT) ? event.getString(PluginEngineConstants.SCRIPT_LANGUAGE).equals(PluginEngine.GO.getName()) ? PluginEngine.GO.getName() : PluginEngine.PYTHON.getName() : null;
            }
            else if (EventBusConstants.EVENT_REDISCOVER.equalsIgnoreCase(eventType) || EventBusConstants.EVENT_METRIC_POLL.equalsIgnoreCase(eventType) || EventBusConstants.EVENT_DISCOVERY.equalsIgnoreCase(eventType))
            {
                if (event.getString(Metric.METRIC_PLUGIN) != null && event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.OBJECT_STATUS.getName()) && event.getString(Metric.METRIC_TYPE) != null && PYTHON_METRIC_TYPES.contains(event.getString(Metric.METRIC_TYPE)))
                {
                    pluginEngine = PLUGIN_ENGINE_RUNTIME;
                }
                else
                {
                    pluginEngine = event.getString(Metric.METRIC_PLUGIN) != null ? ObjectManagerCacheStore.getStore().getPluginEngineByMetricPlugin(event.getString(Metric.METRIC_PLUGIN)) : null;

                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return pluginEngine;
    }

    /**
     * Gets the plugin engine for a specific plugin.
     * This method retrieves the plugin engine associated with the given metric plugin
     * from the ObjectManagerCacheStore.
     *
     * @param plugin The name of the plugin
     * @return The name of the plugin engine to use
     */
    public static String getPluginEngine(String plugin)
    {
        return ObjectManagerCacheStore.getStore().getPluginEngineByMetricPlugin(plugin);
    }

    /**
     * Checks if a plugin supports batch processing.
     * Currently, only the GO plugin engine supports batch processing.
     *
     * @param plugin The name of the plugin to check
     * @return true if the plugin supports batch processing, false otherwise
     */
    public static boolean hasBatchSupport(String plugin)
    {
        var pluginEngine = getPluginEngine(plugin);

        return pluginEngine != null && pluginEngine.equalsIgnoreCase(PluginEngine.GO.getName());
    }

    /**
     * Checks if an event can be processed in batch mode.
     * This method checks if the event specifies a plugin engine that supports batch processing.
     * Currently, only the GO plugin engine supports batch processing.
     *
     * @param event The event to check
     * @return true if the event can be processed in batch mode, false otherwise
     */
    public static boolean hasBatchSupport(JsonObject event)
    {
        return event.getString(PLUGIN_ENGINE) != null && event.getString(PLUGIN_ENGINE).equalsIgnoreCase(PluginEngine.GO.getName());
    }

    /**
     * Enum representing different types of plugin engine requests.
     * Each request type corresponds to a specific functionality in the system
     * and may be processed differently by the plugin engine.
     */
    public enum PluginEngineRequest
    {
        /**
         * Request for streaming data processing
         */
        STREAMING("Streaming"),

        /**
         * Request for runbook operations (MAC Scanner, Runbook Notifications, RCA)
         */
        RUNBOOK("Runbook"),

        /**
         * Request for ping availability checks (used by runbooks)
         */
        PING("Ping"),

        /**
         * Request for ping probe operations (availability checker with large packets)
         */
        PING_PROBE("Ping Probe"),

        /**
         * Request for network metric collection
         */
        NETWORK_METRIC("Network Metric"),

        /**
         * Request for configuration operations
         */
        CONFIG("Config"),

        /**
         * Request for compliance checking operations
         */
        COMPLIANCE("Compliance"),

        /**
         * Request for network route operations
         */
        NETROUTE("NetRoute");

        /**
         * Map of request names to enum values for efficient lookup
         */
        private static final Map<String, PluginEngineRequest> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(PluginEngineRequest::getName, e -> e)));

        /**
         * The display name of the request type
         */
        private final String name;

        /**
         * Constructor for PluginEngineRequest enum.
         *
         * @param name The display name of the request type
         */
        PluginEngineRequest(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the given name.
         *
         * @param name The name to look up
         * @return The corresponding enum value, or null if not found
         */
        public static PluginEngineRequest valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of this request type.
         *
         * @return The display name
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Enum representing different types of plugin engines.
     * Each engine type corresponds to a specific runtime environment
     * that can execute plugin code.
     */
    public enum PluginEngine
    {
        /**
         * Python runtime environment
         */
        PYTHON("python"),

        /**
         * Go runtime environment
         */
        GO("go"),

        /**
         * Java runtime environment
         */
        JAVA("java"),

        /**
         * Node.js runtime environment
         */
        NODE("node"),

        /**
         * DOTNET runtime environment
         */
        DOTNET("dotnet");

        /**
         * Map of engine names to enum values for efficient lookup
         */
        private static final Map<String, PluginEngine> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(PluginEngine::getName, e -> e)));

        /**
         * The name of the plugin engine
         */
        private final String name;

        /**
         * Constructor for PluginEngine enum.
         *
         * @param name The name of the plugin engine
         */
        PluginEngine(String name)
        {
            this.name = name;
        }

        /**
         * Gets the enum value corresponding to the given name.
         *
         * @param name The name to look up
         * @return The corresponding enum value, or null if not found
         */
        public static PluginEngine valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the name of this plugin engine.
         *
         * @return The name of the plugin engine
         */
        public String getName()
        {
            return name;
        }
    }


}
