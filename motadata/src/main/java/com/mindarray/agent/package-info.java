/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The agent package provides classes for managing and processing agent-related operations in the Motadata system.
 * <p>
 * Agents are components that collect data from various sources (metrics, logs, packets, etc.) and send them to the
 * Motadata system for processing and analysis. This package contains classes for managing agent lifecycle, processing
 * agent events, and handling agent-specific operations.
 * <p>
 * Key components in this package include:
 * <ul>
 *   <li>{@link com.mindarray.agent.AgentManager} - Manages agent operations like discovery, polling, and command execution</li>
 *   <li>{@link com.mindarray.agent.AgentEventProcessor} - Processes events from agents, including registration and health events</li>
 *   <li>{@link com.mindarray.agent.AgentCacheManager} - Manages caching of agent configurations and data</li>
 *   <li>{@link com.mindarray.agent.MetricAgentEventProcessor} - Specifically processes events from metric agents</li>
 *   <li>{@link com.mindarray.agent.AgentConstants} - Contains constants and enums used throughout the agent package</li>
 * </ul>
 * <p>
 * The agent system supports different types of agents:
 * <ul>
 *   <li>Metric Agents - Collect performance metrics from systems</li>
 *   <li>Log Agents - Collect and process log data</li>
 *   <li>Packet Agents - Collect network packet data</li>
 *   <li>Windows Event Log Agents - Collect Windows event log data</li>
 * </ul>
 * <p>
 * Agent health is monitored through heartbeats and ping mechanisms, with thresholds for CPU and memory usage
 * to ensure optimal performance.
 *
 * @since 1.0
 */
package com.mindarray.agent;