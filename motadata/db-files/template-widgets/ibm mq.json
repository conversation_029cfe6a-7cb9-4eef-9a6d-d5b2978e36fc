[{"id": 10000000000425, "_type": "0", "visualization.name": "Sessions", "visualization.description": "IBMMQ Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.sessions", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ibm.mq.sessions.last"}]}}}}, {"id": 10000000000426, "_type": "0", "visualization.name": "Channels", "visualization.description": "IBMMQ Channels", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.channels", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.channels"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.channels", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Channels", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ibm.mq.channels.last"}]}}}}, {"id": 10000000000427, "_type": "0", "visualization.name": "Queues", "visualization.description": "IBMMQ Queues", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.queues", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.queues"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.queues", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Queues", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ibm.mq.queues.last"}]}}}}, {"id": 10000000000428, "_type": "0", "visualization.name": "Messages", "visualization.description": "IBMMQ Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.pending.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.pending.messages", "icon": {"name": "messages", "placement": "prefix"}}, "header": {"title": "Messages", "style": {"font.size": "medium"}}, "footer": {"title": "Pending", "style": {"font.size": "large"}, "data.points": [{"label": "Pending", "value": "ibm.mq.pending.messages.last"}]}}}}, {"id": 10000000000429, "_type": "0", "visualization.name": "Topics", "visualization.description": "IBMMQ Topics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.topics", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.topics"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.topics", "icon": {"name": "comments", "placement": "prefix"}}, "header": {"title": "Topics", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ibm.mq.topics.last"}]}}}}, {"id": 10000000000430, "_type": "0", "visualization.name": "Listeners", "visualization.description": "IBMMQ Listeners", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.listeners", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ibm.mq.listeners"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "ibm.mq.listeners", "icon": {"name": "listeners", "placement": "prefix"}}, "header": {"title": "Listeners", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ibm.mq.listeners.last"}]}}}}, {"_type": "0", "id": 10000000000431, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000432, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000433, "_type": "0", "visualization.name": "Listeners Details", "visualization.description": "IBMMQ Listeners Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.listener~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.listener~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.listener~sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.listener~backlog", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.listener~availability", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ibm.mq.listener", "title": "Listener", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ibm.mq.listener~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ibm.mq.listener~port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ibm.mq.listener~sessions.last", "title": "Sessions", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ibm.mq.listener~backlog.last", "title": "Backlog", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "ibm.mq.listener~availability.last", "title": "Availability", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}, {"id": 10000000000434, "_type": "0", "visualization.name": "Message Details", "visualization.description": "IBMMQ Message Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.pending.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000435, "_type": "0", "visualization.name": "Queue Traffic", "visualization.description": "IBMMQ Queue Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000436, "_type": "0", "visualization.name": "Session", "visualization.description": "IBMMQ Session", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000437, "_type": "0", "visualization.name": "Queue Details", "visualization.description": "IBMMQ Queue Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.queue~availability", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue~pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue~usage", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue~last.get.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue~last.put.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ibm.mq.queue", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ibm.mq.queue~availability.last", "title": "Availability", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ibm.mq.queue~pending.messages.last", "title": "Pending Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ibm.mq.queue~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ibm.mq.queue~usage.last", "title": "Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ibm.mq.queue~last.get.time.last", "title": "Last Get Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "ibm.mq.queue~last.put.time.last", "title": "Last Put Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}, {"id": 10000000000438, "_type": "0", "visualization.name": "Channel Details", "visualization.description": "IBMMQ Channel Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.channel~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~availability", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~sent.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~received.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~sent.buffers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~received.buffers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.channel~batches", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ibm.mq.channel", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"width.percent": 12}}, {"name": "ibm.mq.channel~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"width.percent": 12}}, {"name": "ibm.mq.channel~availability.last", "title": "Availability", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"width.percent": 19}}, {"name": "ibm.mq.channel~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"width.percent": 23}}, {"name": "ibm.mq.channel~messages.last", "title": "Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"width.percent": 27}}, {"name": "ibm.mq.channel~pending.messages.last", "title": "Pending Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"width.percent": 29}}, {"name": "ibm.mq.channel~sent.bytes.last", "title": "Send (MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "ibm.mq.channel~received.bytes.last", "title": "Received (MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "ibm.mq.channel~sent.buffers.last", "title": "<PERSON><PERSON><PERSON> Sent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "ibm.mq.channel~received.buffers.last", "title": "<PERSON><PERSON><PERSON> Received", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "ibm.mq.channel~batches.last", "title": "Batches", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}]}}}, {"id": 10000000000439, "_type": "0", "visualization.name": "Topic Details", "visualization.description": "IBMMQ Topic Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.topic~availability", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~publishers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~subscribers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~alteration.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~alteration.date", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~model.durable.queue", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.topic~string", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ibm.mq.topic", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ibm.mq.topic~availability.last", "title": "Availability", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ibm.mq.topic~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ibm.mq.topic~publishers.last", "title": "Publishers", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ibm.mq.topic~subscribers.last", "title": "Subscribers", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "ibm.mq.topic~alteration.time.last", "title": "Alternation Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "ibm.mq.topic~alteration.date.last", "title": "Alternation Date", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "ibm.mq.topic~model.durable.queue.last", "title": "Model Durable <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "ibm.mq.topic~string.last", "title": "Topic String", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}]}}}, {"id": 10000000000440, "_type": "0", "visualization.name": "Handlers Details", "visualization.description": "IBMMQ Handlers Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ibm.mq.queue.handler~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue.handler~user.identifier", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue.handler~open.input.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue.handler~appl.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue.handler~open.options", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ibm.mq.queue.handler~channel.name", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ibm.mq.queue.handler", "title": "Handler", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "ibm.mq.queue.handler~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ibm.mq.queue.handler~user.identifier.last", "title": "User Identifier", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ibm.mq.queue.handler~open.input.type.last", "title": "Open Input Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ibm.mq.queue.handler~appl.type.last", "title": "Appl Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "ibm.mq.queue.handler~open.options.last", "title": "Open Options", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "ibm.mq.queue.handler~channel.name.last", "title": "Channel Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}]