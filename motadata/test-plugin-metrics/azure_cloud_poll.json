{"127.0.0.1": {"4": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}, "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.service.down.instance.discovery": "yes", "cloud.tenant.id": "5b4acec3-**************-98c654cc6c87", "errors": [], "plugin.id": 4, "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "result": {"azure.application.gateway": [{"azure.application.gateway": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Network/applicationGateways/appgateway123", "azure.application.gateway.subscription": "Azure subscription 1", "azure.etag": "W/\"bc79d58f-7cb2-4ea1-b51b-62a069b09e91\"", "azure.location": "southcentralus", "azure.name": "appgateway123", "azure.provisioning.state": "Succeeded", "azure.service": "appgateway123(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure Application Gateway", "azure.sku.name": "Standard_Small", "azure.type": "Microsoft.Network/applicationGateways"}], "azure.application.gateway.instances": 1, "azure.cdn": [{"azure.cdn": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourcegroups/cloud-shell-storage-centralindia/providers/Microsoft.Cdn/profiles/cdntest", "azure.cdn.subscription": "Azure subscription 1", "azure.location": "Global", "azure.name": "cdntest", "azure.service": "cdntest(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure CDN", "azure.status": "Active", "azure.type": "Microsoft.Cdn/profiles", "status": "Up"}], "azure.cdn.profiles": 1, "azure.cosmos.db": [{"azure.cosmos.db": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.DocumentDB/databaseAccounts/motadatacosmosdb", "azure.cosmos.db.document.endpoint": "https://motadatacosmosdb.documents.azure.com:443/", "azure.cosmos.db.read.locations": "West US", "azure.cosmos.db.region.id": "motadatacosmosdb-westus", "azure.cosmos.db.subscription": "Azure subscription 1", "azure.cosmos.db.write.locations": "West US", "azure.location": "West US", "azure.provisioning.state": "Succeeded", "azure.service": "motadatacosmosdb(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Cosmos DB", "azure.type": "Standard"}], "azure.cosmosdb.instances": 1, "azure.function": [{"azure.function": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Web/sites/FunctionApp120210317184034", "azure.function.app.subscription": "Azure subscription 1", "azure.location": "South Central US", "azure.name": "FunctionApp120210317184034", "azure.service": "FunctionApp120210317184034(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Function", "azure.status": "Running", "azure.type": "Microsoft.Web/sites", "status": "Up"}], "azure.functions": 1, "azure.loadbalancer": [{"azure.etag": "W/\"39da8c6c-91ba-47b7-ace6-45fbfca0dffe\"", "azure.loadbalancer": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Network/loadBalancers/lb", "azure.loadbalancer.subscription": "Azure subscription 1", "azure.location": "centralindia", "azure.name": "lb", "azure.provisioning.state": "Succeeded", "azure.service": "lb(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure Load Balancer", "azure.sku.name": "Standard", "azure.type": "Microsoft.Network/loadBalancers"}], "azure.loadbalancer.instances": 1, "azure.servicebus": [{"azure.location": "South Central US", "azure.name": "servbsq12", "azure.service": "servbsq12(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure Service Bus", "azure.servicebus": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.ServiceBus/namespaces/servbsq12", "azure.servicebus.creation.time": " 358 days 0 hour 47 minutes 19 seconds", "azure.servicebus.creation.time.seconds": 30934039, "azure.servicebus.subscription": "Azure subscription 1", "azure.sku.name": "Basic", "azure.status": "Active", "azure.type": "Microsoft.ServiceBus/namespaces", "status": "Up"}], "azure.servicebus.instances": 1, "azure.sql.database": [{"azure.location": "East US 2", "azure.name": "aqasde", "azure.service": "aqasde(DefaultResourceGroup-EUS2)", "azure.service.resource.group": "DefaultResourceGroup-EUS2", "azure.service.type": "Azure SQL Database", "azure.sql.database": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/DefaultResourceGroup-EUS2/providers/Microsoft.Sql/servers/newmotadatasqlserver/databases/aqasde", "azure.sql.database.creation.time": " 358 days 0 hour 47 minutes 43 seconds", "azure.sql.database.creation.time.seconds": 30934063, "azure.sql.database.earliest.restore.date": "2022-06-02T04:52:10.2493861Z", "azure.sql.database.server": "newmotadatasqlserver", "azure.sql.database.server.fqdn": "newmotadatasqlserver.database.windows.net", "azure.sql.database.storage.size.bytes": "**********", "azure.sql.database.subscription": "Azure subscription 1", "azure.status": "Online", "status": "Up"}, {"azure.location": "East US 2", "azure.name": "master", "azure.service": "master(DefaultResourceGroup-EUS2)", "azure.service.resource.group": "DefaultResourceGroup-EUS2", "azure.service.type": "Azure SQL Database", "azure.sql.database": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/DefaultResourceGroup-EUS2/providers/Microsoft.Sql/servers/newmotadatasqlserver/databases/master", "azure.sql.database.creation.time": " 457 days 17 hours 20 minutes 5 seconds", "azure.sql.database.creation.time.seconds": 39547205, "azure.sql.database.server": "newmotadatasqlserver", "azure.sql.database.server.fqdn": "newmotadatasqlserver.database.windows.net", "azure.sql.database.storage.size.bytes": "53687091200", "azure.sql.database.subscription": "Azure subscription 1", "azure.status": "Online", "status": "Up"}], "azure.sql.databases": 2, "azure.storage": [{"azure.provisioning.state": "Succeeded", "azure.service": "motadatawin(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Storage/storageAccounts/motadatawin", "azure.storage.creation.time": " 807 days 19 hours 51 minutes 0 second", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "eastus2", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "csg10032000a5192286(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Storage/storageAccounts/csg10032000a5192286", "azure.storage.creation.time": " 810 days 19 hours 4 minutes 4 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "centralindia", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "motadatafreetierdiag(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Storage/storageAccounts/motadatafreetierdiag", "azure.storage.creation.time": " 814 days 21 hours 58 minutes 21 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "eastus", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "motadatblobstorage(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Storage/storageAccounts/motadatblobstorage", "azure.storage.creation.time": " 813 days 4 hours 52 minutes 30 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "eastus", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "motadatafreetierdiag678(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Storage/storageAccounts/motadatafreetierdiag678", "azure.storage.creation.time": " 804 days 0 hour 53 minutes 51 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "southcentralus", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "blobstorageaccountesting(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Storage/storageAccounts/blobstorageaccountesting", "azure.storage.creation.time": " 805 days 21 hours 20 minutes 19 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "centralindia", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}, {"azure.provisioning.state": "Succeeded", "azure.service": "storageaccountmotadaf95(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure Storage", "azure.storage": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Storage/storageAccounts/storageaccountmotadaf95", "azure.storage.creation.time": " 462 days 0 hour 43 minutes 48 seconds", "azure.storage.creation.time.seconds": ********, "azure.storage.location": "centralindia", "azure.storage.state": "available", "azure.storage.subscription": "Azure subscription 1"}], "azure.storage.accounts": 7, "azure.vm": [{"azure.location": "eastus2", "azure.provisioning.state": "Succeeded", "azure.service": "win-motadataVM(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure VM", "azure.sku.name": "2012-R2-Datacenter", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm": "5f757afe-f672-40b4-9e9f-17c29cfbb3f7", "azure.vm.computer.name": "win-motadataVM", "azure.vm.os.disk": "win-motadataVM_OsDisk_1_5e95fff87b554ef29d0bae8df345fe49-new", "azure.vm.os.type": "Windows", "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "**************", "azure.vm.publisher": "MicrosoftWindowsServer", "azure.vm.size": "Standard_B1s", "azure.vm.subscription": "Azure subscription 1", "status": "Up"}, {"azure.location": "southcentralus", "azure.provisioning.state": "Succeeded", "azure.service": "ubuntu-linux2(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure VM", "azure.sku.name": "18.04-LTS", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm": "06e89917-b696-4b47-9b11-19a3fe8d8354", "azure.vm.computer.name": "ubuntu-linux2", "azure.vm.os.disk": "ubuntu-linux2_disk1_32cd06c927df45699d73f7557ababf00", "azure.vm.os.type": "Linux", "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "************", "azure.vm.publisher": "Canonical", "azure.vm.size": "Standard_B1ls", "azure.vm.subscription": "Azure subscription 1", "status": "Up"}, {"azure.location": "eastus", "azure.provisioning.state": "Succeeded", "azure.service": "itsmtestinstance(DefaultResourceGroup-EUS)", "azure.service.resource.group": "DefaultResourceGroup-EUS", "azure.service.type": "Azure VM", "azure.sku.name": "20_04-lts-gen2", "azure.status": "VM running", "azure.type": "Microsoft.Compute/virtualMachines", "azure.vm": "e8205e1b-151e-4d4e-a96d-1b20dbacc60b", "azure.vm.computer.name": "itsmtestinstance", "azure.vm.os.disk": "itsmtestinstance_OsDisk_1_b9ac6d67289c41ea82d6e31b62c2b469", "azure.vm.os.type": "Linux", "azure.vm.private.ip.address": "********", "azure.vm.public.ip.address": "*************", "azure.vm.publisher": "canonical", "azure.vm.size": "Standard_B1ls", "azure.vm.subscription": "Azure subscription 1", "status": "Up"}], "azure.vms": 3, "azure.vmscaleset": [{"azure.location": "centralindia", "azure.service": "saleset(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure VM Scale Set", "azure.sku.name": "Standard_B1ls", "azure.vmscaleset": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Compute/virtualMachineScaleSets/saleset", "azure.vmscaleset.subscription": "Azure subscription 1"}], "azure.vmscaleset.instances": 1, "azure.webapp": [{"azure.location": "South Central US", "azure.service": "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)", "azure.service.resource.group": "cloud-shell-storage-centralindia", "azure.service.type": "Azure WebApp", "azure.status": "Running", "azure.webapp": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/cloud-shell-storage-centralindia/providers/Microsoft.Web/sites/azuresamplewebapp20200323124332", "azure.webapp.default.host": "azuresamplewebapp20200323124332.azurewebsites.net", "azure.webapp.subscription": "Azure subscription 1", "status": "Up"}, {"azure.location": "Central US", "azure.service": "testmotadataweb(motadata-freetier)", "azure.service.resource.group": "motadata-freetier", "azure.service.type": "Azure WebApp", "azure.status": "Running", "azure.webapp": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.Web/sites/testmotadataweb", "azure.webapp.default.host": "testmotadataweb.azurewebsites.net", "azure.webapp.subscription": "Azure subscription 1", "status": "Up"}], "azure.webapps": 2}, "status": "succeed"}}