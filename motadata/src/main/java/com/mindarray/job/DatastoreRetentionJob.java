/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/
package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_DATASTORE_RETENTION_TRIGGER;

/**
 * The DatastoreRetentionJob class is responsible for managing data retention in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs two main tasks:
 * <ul>
 *   <li>Triggering datastore retention by publishing an event to the event bus</li>
 *   <li>Cleaning up cache files based on a configurable retention policy</li>
 * </ul>
 * <p>
 * The job is scheduled to run every day at midnight (12:00 AM) to ensure that
 * old data is regularly purged from the system, preventing excessive disk usage
 * and maintaining system performance.
 */
public class DatastoreRetentionJob implements Job
{
    /**
     * CRON expression for scheduling this job to run every day at midnight (12:00 AM)
     */
    public static final String DATASTORE_RETENTION_JOB_CRON_EXPRESSION = "0 0 0 ? * * *"; // At 00:00:00am every day

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(DatastoreRetentionJob.class, GlobalConstants.MOTADATA_JOB, "Datastore Retention Job");

    /**
     * Executes the datastore retention job.
     * <p>
     * This method performs two main tasks:
     * <ol>
     *   <li>Triggers datastore event retention by publishing an event to the event bus</li>
     *   <li>Cleans up cache files based on the configured retention policy</li>
     * </ol>
     * <p>
     * The datastore retention is handled by other components that listen for the
     * EVENT_DATASTORE_RETENTION_TRIGGER event. The cache file cleanup is handled
     * directly by this class through the {@link #cleanup()} method.
     *
     * @param context the context in which the job is executed
     * @throws JobExecutionException if an error occurs during job execution
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        try
        {
            // Trigger datastore event retention by publishing an event
            LOGGER.info("executing datastore retention job...");
            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_RETENTION_TRIGGER, null);

            // Clean up cache files based on retention policy
            LOGGER.info("cache file clean up job started ....");
            cleanup();
            LOGGER.info("cache file clean up job completed ....");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Cleans up cache files based on the configured retention policy.
     * <p>
     * This method deletes cache files that are older than the retention period
     * configured in the system. It works by:
     * <ol>
     *   <li>Creating a set of dates to exclude from deletion (recent dates within the retention period)</li>
     *   <li>Checking each file in the cache directory</li>
     *   <li>Deleting files that don't match the excluded dates</li>
     * </ol>
     * <p>
     * The retention period is determined by {@link MotadataConfigUtil#getCacheFilesRetentionDays()}.
     */
    private void cleanup()
    {
        try
        {
            // Create a set of dates to exclude from deletion (files within retention period)
            var excludeDates = new HashSet<String>();

            // Get the date format used in cache file names
            var formatter = new SimpleDateFormat(DateTimeUtil.CORRELATED_METRIC_CACHE_FILE_TIMESTAMP_FORMAT);

            // Start with current time
            var currentTimeMillis = System.currentTimeMillis();

            // Build a set of formatted dates to keep (one for each day in the retention period)
            while (excludeDates.size() != MotadataConfigUtil.getCacheFilesRetentionDays())
            {
                // Add the formatted date to the exclusion set
                excludeDates.add(formatter.format(new Date(currentTimeMillis)));

                // Move back one day (86400000 milliseconds = 24 hours)
                currentTimeMillis -= 86400000;
            }

            // Get the cache directory
            var cacheDir = new File(CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR);

            if (cacheDir.exists())
            {
                var files = cacheDir.listFiles();

                if (files != null)
                {
                    for (var file : files)
                    {
                        // Split the filename by dash to extract the date components
                        var tokens = file.getName().split(DASH_SEPARATOR);

                        // Check if the file has enough tokens and its date is not in the exclusion set
                        if (tokens.length >= 3 && !excludeDates.contains(
                                tokens[tokens.length - 3] + DASH_SEPARATOR +
                                        tokens[tokens.length - 2] + DASH_SEPARATOR +
                                        tokens[tokens.length - 1]))
                        {
                            // Log and delete files outside the retention period
                            LOGGER.info(String.format("cache file deleted : %s ", file.getName()));
                            file.delete();
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
