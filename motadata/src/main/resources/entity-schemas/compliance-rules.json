{"entity": "Compliance Rule", "table": "tbl_config_compliance_rule", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "compliance.rule.name", "title": "Rule Name", "type": "string", "rules": ["required", "unique"]}, {"name": "compliance.rule.description", "title": "Description", "type": "string"}, {"name": "compliance.rule.category", "title": "Category", "type": "string", "rules": ["required"], "values": ["Network"]}, {"name": "compliance.rule.runbooks", "title": "Runbook Actions", "type": "list"}, {"name": "compliance.rule.auto.remediation", "title": "Actions Auto Remediation", "type": "string"}, {"name": "compliance.rule.severity", "title": "Rule Severity", "type": "string", "rules": ["required"], "values": ["CRITICAL", "HIGH", "MEDIUM", "LOW", "INFO"]}, {"name": "compliance.rule.tags", "title": "Tags", "type": "list"}, {"name": "compliance.rule.rationale", "title": "Rationale", "type": "string"}, {"name": "compliance.rule.impact", "title": "Impact", "type": "string"}, {"name": "compliance.rule.default.value", "title": "Default Value", "type": "string"}, {"name": "compliance.rule.references", "title": "References", "type": "string"}, {"name": "compliance.rule.additional.information", "title": "Additional Information", "type": "string"}], "entries": [{"type": "inline", "records": [{"compliance.rule.name": "Enable 'aaa new-model'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": "no aaa new-model", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Globally enable authentication, authorization and accounting (AAA) using the new-model command.\n```\nhostname(config)#aaa new-model\n```", "compliance.rule.description": "This command enables the AAA access control system.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) services provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices.", "compliance.rule.impact": "Implementing Cisco AAA is significantly disruptive as former access methods are immediately disabled. Therefore, before implementing Cisco AAA, the organization should carefully review and plan their authentication criteria (logins & passwords, challenges & responses, and token technologies), authorization methods, and accounting requirements.", "compliance.rule.default.value": "AAA is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a2.html#GUID-E05C2E00-C01E-4053-9D12-EC37C7E8EEC5", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Enable 'aaa authentication login'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa authentication login", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA authentication method(s) for login authentication.\n```\nhostname(config)#aaa authentication login {default | aaa_list_name} [passwd-expiry]\n[method1] [method2]\n\n```", "compliance.rule.description": "Sets authentication, authorization and accounting (AAA) authentication at login.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using AAA authentication for interactive management access to the device provides consistent, centralized control of your network. The default under AAA (local or network) is to require users to log in using a valid user name and password. This rule applies for both local and network AAA. Fallback mode should also be enabled to allow emergency access to the router or switch in the event that the AAA server was unreachable, by utilizing the LOCAL keyword after the AAA server-tag.", "compliance.rule.impact": "Implementing Cisco AAA is significantly disruptive as former access methods are immediately disabled. Therefore, before implementing Cisco AAA, the organization should carefully review and plan their authentication methods such as logins and passwords, challenges and responses, and which token technologies will be used.", "compliance.rule.default.value": "AAA authentication at login is disabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-3DB1CC8A-4A98-400B-A906-C42F265C7EA2", "compliance.rule.additional.information": "Only “the default method list is automatically applied to all interfaces except those that have a named method list explicitly defined. A defined method list overrides the default method list.” (1)", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Enable 'aaa authentication enable default'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa authentication enable", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA authentication method(s) for enable authentication.\n```\nhostname(config)#aaa authentication enable default {method1} enable \n```", "compliance.rule.description": "Authenticates users who access privileged EXEC mode when they use the enable command.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using AAA authentication for interactive management access to the device provides consistent, centralized control of your network. The default under AAA (local or network) is to require users to log in using a valid user name and password. This rule applies for both local and network AAA.", "compliance.rule.impact": "Enabling Cisco AAA 'authentication enable' mode is significantly disruptive as former access methods are immediately disabled. Therefore, before enabling 'aaa authentication enable default' mode, the organization should plan and implement authentication logins and passwords, challenges and responses, and token technologies.", "compliance.rule.default.value": "By default, fallback to the local database is disabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-4171D649-2973-4707-95F3-9D96971893D0", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'login authentication for 'line vty'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "login authentication", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line vty", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure management lines to require login using the default or a named AAA authentication list. This configuration must be set individually for all line types.\n```\nhostname(config)#line vty {line-number} [<em>ending-line-number]\nhostname(config-line)#login authentication {default | aaa_list_name}\n```", "compliance.rule.description": "Authenticates users who access the router or switch remotely through the VTY port.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using AAA authentication for interactive management access to the device provides consistent, centralized control of your network. The default under AAA (local or network) is to require users to log in using a valid user name and password. This rule applies for both local and network AAA.", "compliance.rule.impact": "Enabling Cisco AAA 'login authentication for line VTY' is significantly disruptive as former access methods are immediately disabled. Therefore, before enabling Cisco AAA 'login authentication for line VTY', the organization should plan and implement authentication logins and passwords, challenges and responses, and token technologies.", "compliance.rule.default.value": "Login authentication is not enabled.\n\nUses the default set with aaa authentication login.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-k1.html#GUID-297BDF33-4841-441C-83F3-4DA51C3C7284", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'login authentication for 'ip http'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*ip\\s+http\\s+authentication(\\s+\\S+)?", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure management lines to require login using the default or a named AAA authentication list. This configuration must be set individually for all line types.\n```\nhostname#(config)ip http secure-server\nhostname#(config)ip http authentication {default | _aaa\\_list\\_name_}\n```", "compliance.rule.description": "If account management functions are not automatically enforced, an attacker could gain privileged access to a vital element of the network security architecture", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using AAA authentication for interactive management access to the device provides consistent, centralized control of your network. The default under AAA (local or network) is to require users to log in using a valid user name and password. This rule applies for both local and network AAA.", "compliance.rule.impact": "Enabling Cisco AAA 'line login' is significantly disruptive as former access methods are immediately disabled. Therefore, before enabling Cisco AAA 'line login', the organization should plan and implement authentication logins and passwords, challenges and responses, and token technologies.", "compliance.rule.default.value": "Login authentication is not enabled.\n\nUses the default set with aaa authentication login.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-k1.html#GUID-297BDF33-4841-441C-83F3-4DA51C3C7284", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'aaa accounting' to log all privileged use commands using 'commands 15'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa accounting commands \\d+", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA accounting for commands.\n```\nhostname(config)#aaa accounting commands 15 {default | list-name | guarantee-first}\n{start-stop | stop-only | none} {radius | group group-name}\n```", "compliance.rule.description": "Runs accounting for all commands at the specified privilege level.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) systems provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices. AAA Accounting provides a management and audit trail for user and administrative sessions through TACACS+.", "compliance.rule.impact": "Enabling 'aaa accounting' for privileged commands records and sends activity to the accounting servers and enables organizations to monitor and analyze privileged activity.", "compliance.rule.default.value": "AAA accounting is disabled.", "compliance.rule.references": "", "compliance.rule.additional.information": "Valid privilege level entries are integers from 0 through 15.", "compliance.rule.controls": [{"compliance.rule.control.version": "8.2", "compliance.rule.control.name": "Collect Audit Logs", "compliance.rule.control.description": "Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'aaa accounting connection'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa accounting connection", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA accounting for connections.\n```\nhostname(config)#aaa accounting connection {default | list-name | guarantee-first} \n{start-stop | stop-only | none} {radius | group group-name}\n```", "compliance.rule.description": "Provides information about all outbound connections made from the network access server.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) systems provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices. AAA Accounting provides a management and audit trail for user and administrative sessions through RADIUS and TACACS+.", "compliance.rule.impact": "Implementing aaa accounting connection creates accounting records about connections from the network access server. Organizations should regular monitor these connection records for exceptions, remediate issues, and report findings regularly.", "compliance.rule.default.value": "AAA accounting is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-0520BCEF-89FB-4505-A5DF-D7F1389F1BBA", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'aaa accounting exec'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa accounting exec", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA accounting for EXEC shell session.\n```\nhostname(config)#aaa accounting exec {default | list-name | guarantee-first} \n{start-stop | stop-only | none} {radius | group group-name}\n```", "compliance.rule.description": "Runs accounting for the EXEC shell session.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) systems provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices. AAA Accounting provides a management and audit trail for user and administrative sessions through RADIUS and TACACS+.", "compliance.rule.impact": "Enabling aaa accounting exec creates accounting records for the EXEC terminal sessions on the network access server. These records include start and stop times, usernames, and date information. Organizations should regularly monitor these records for exceptions, remediate issues, and report findings.", "compliance.rule.default.value": "AAA accounting is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-0520BCEF-89FB-4505-A5DF-D7F1389F1BBA", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.2", "compliance.rule.control.name": "Collect Audit Logs", "compliance.rule.control.description": "Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'aaa accounting network'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa accounting network", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA accounting for connections.\n```\nhostname(config)#aaa accounting network {default | list-name | guarantee-first} \n{start-stop | stop-only | none} {radius | group group-name}\n```", "compliance.rule.description": "Runs accounting for all network-related service requests.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) systems provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices. AAA Accounting provides a management and audit trail for user and administrative sessions through RADIUS and TACACS+.", "compliance.rule.impact": "Implementing aaa accounting network creates accounting records for a method list including ARA, PPP, SLIP, and NCPs sessions. Organizations should regular monitor these records for exceptions, remediate issues, and report findings.", "compliance.rule.default.value": "AAA accounting is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-0520BCEF-89FB-4505-A5DF-D7F1389F1BBA", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.2", "compliance.rule.control.name": "Collect Audit Logs", "compliance.rule.control.description": "Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'aaa accounting system'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "aaa accounting system", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure AAA accounting system.\n```\nhostname(config)#aaa accounting system {default | list-name | guarantee-first} \n{start-stop | stop-only | none} {radius | group group-name}\n```", "compliance.rule.description": "Performs accounting for all system-level events not associated with users, such as reloads.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Authentication, authorization and accounting (AAA) systems provide an authoritative source for managing and monitoring access for devices. Centralizing control improves consistency of access control, the services that may be accessed once authenticated and accountability by tracking services accessed. Additionally, centralizing access control simplifies and reduces administrative costs of account provisioning and de-provisioning, especially when managing a large number of devices. AAA Accounting provides a management and audit trail for user and administrative sessions through RADIUS and TACACS+.", "compliance.rule.impact": "Enabling aaa accounting system creates accounting records for all system-level events. Organizations should regular monitor these records for exceptions, remediate issues, and report findings regularly.", "compliance.rule.default.value": "AAA accounting is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a1.html#GUID-0520BCEF-89FB-4505-A5DF-D7F1389F1BBA", "compliance.rule.additional.information": "When system accounting is used and the accounting server is unreachable at system startup time, the system will not be accessible for approximately two minutes.", "compliance.rule.controls": [{"compliance.rule.control.version": "8.2", "compliance.rule.control.name": "Collect Audit Logs", "compliance.rule.control.description": "Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'privilege 1' for local users", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.block.criteria": {"compliance.rule.block.start": "username", "compliance.rule.block.end": "!"}, "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "username.*privilege\\s1\\s.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Set the local user to privilege level 1.\n```\nhostname(config)#username <LOCAL_USERNAME> privilege 1 \n```", "compliance.rule.description": "Sets the privilege level for the user.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Default device configuration does not require strong user authentication potentially enabling unfettered access to an attacker that is able to reach the device. Creating a local account with privilege level 1 permissions only allows the local user to access the device with EXEC-level permissions and will be unable to modify the device without using the enable password. In addition, require the use of an encrypted password as well (see Section ******* - Require Encrypted User Passwords).", "compliance.rule.impact": "Organizations should create policies requiring all local accounts with 'privilege level 1' with encrypted passwords to reduce the risk of unauthorized access. Default configuration settings do not provide strong user authentication to the device.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/s1/sec-cr-t2-z.html#GUID-34B3E43E-0F79-40E8-82B6-A4B5F1AFF1AD", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": **************}, {"compliance.rule.name": "Set 'transport input ssh' for 'line vty' connections", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "transport input ssh", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line vty", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Apply SSH to transport input on all VTY management lines\n```\nhostname(config)#line vty <line-number> <ending-line-number>\nhostname(config-line)#transport input ssh \n```", "compliance.rule.description": "Selects the Secure Shell (SSH) protocol.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Configuring VTY access control restricts remote access to only those authorized to manage the device and prevents unauthorized users from accessing the system.", "compliance.rule.impact": "To reduce risk of unauthorized access, organizations should require all VTY management line protocols to be limited to ssh.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/termserv/command/reference/tsv_s1.html#wp1069219", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "6.5", "compliance.rule.control.name": "Require MFA for Administrative Access", "compliance.rule.control.description": "Require MFA for all administrative access accounts, where supported, on all enterprise assets, whether managed on-site or through a third-party provider.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "4.5", "compliance.rule.control.name": "Use Multifactor Authentication For All Administrative Access", "compliance.rule.control.description": "Use multi-factor authentication and encrypted channels for all administrative account access.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'no exec' for 'line aux 0'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "no exec", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line aux", "compliance.rule.block.end": "end", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "no exec", "operator": "NONE"}]}}, "compliance.rule.remediation": "Disable the EXEC process on the auxiliary port.\n```\nhostname(config)#line aux 0\nhostname(config-line)#no exec\n```", "compliance.rule.description": "The 'no exec' command restricts a line to outgoing connections only.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Unused ports should be disabled, if not required, since they provide a potential access path for attackers. Some devices include both an auxiliary and console port that can be used to locally connect to and configure the device. The console port is normally the primary port used to configure the device; even when remote, backup administration is required via console server or Keyboard, Video, Mouse (KVM) hardware. The auxiliary port is primarily used for dial-up administration via an external modem; instead, use other available methods.", "compliance.rule.impact": "Organizations can reduce the risk of unauthorized access by disabling the 'aux' port with the 'no exec' command. Conversely, not restricting access through the 'aux' port increases the risk of remote unauthorized access.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/D_through_E.html#GUID-429A2B8C-FC26-49C4-94C4-0FD99C32EC34", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000013}, {"compliance.rule.name": "Create 'access-list' for use with 'line vty'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "access-list\\s[0-9]+$", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the VTY ACL that will be used to restrict management access to the device.\n```\nhostname(config)#access-list <vty_acl_number> permit tcp <vty_acl_block_with_mask> any\nhostname(config)#access-list <vty_acl_number> permit tcp host <vty_acl_host> any\nhostname(config)#deny ip any any log\n```", "compliance.rule.description": "Access lists control the transmission of packets on an interface, control Virtual Terminal Line (VTY) access, and restrict the contents of routing updates. The Cisco IOS software stops checking the extended access list after a match occurs.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "VTY ACLs control what addresses may attempt to log in to the router. Configuring VTY lines to use an ACL, restricts the sources where a user can manage the device. You should limit the specific host(s) and or network(s) authorized to connect to and configure the device, via an approved protocol, to those individuals or systems authorized to administer the device. For example, you could limit access to specific hosts, so that only network managers can configure the devices only by using specific network management workstations. Make sure you configure all VTY lines to use the same ACL.", "compliance.rule.impact": "Organizations can reduce the risk of unauthorized access by implementing access-lists for all VTY lines. Conversely, using VTY lines without access-lists increases the risk of unauthorized access.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a2.html#GUID-9EA733A3-1788-4882-B8C3-AB0A2949120C", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000014}, {"compliance.rule.name": "Set 'access-class' for 'line vty'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "line\\svty\\s\\d+\\s\\d+", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line vty", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure remote management access control restrictions for all VTY lines.\n```\nhostname(config)#line vty <line-number> <ending-line-number>\nhostname(config-line)# access-class <vty_acl_number> in\n```", "compliance.rule.description": "The 'access-class' setting restricts incoming and outgoing connections between a particular vty (into a Cisco device) and the networking devices associated with addresses in an access list.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Restricting the type of network devices, associated with the addresses on the access-list, further restricts remote access to those devices authorized to manage the device and reduces the risk of unauthorized access.", "compliance.rule.impact": "Applying 'access'class' to line VTY further restricts remote access to only those devices authorized to manage the device and reduces the risk of unauthorized access. Conversely, using VTY lines with 'access class' restrictions increases the risks of unauthorized access.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a2.html#GUID-FB9BC58A-F00A-442A-8028-1E9E260E54D3", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000015}, {"compliance.rule.name": "Set 'exec-timeout' to less than or equal to 10 minutes for 'line aux 0'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*(exec-timeout)\\s*((10)|([0-9]))\\s*[\\d+]*\\s*", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line aux", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure device timeout (10 minutes or less) to disconnect sessions after a fixed idle time.\n```\nhostname(config)#line aux 0\nhostname(config-line)#exec-timeout <timeout_in_minutes> <timeout_in_seconds>\n```", "compliance.rule.description": "If no input is detected during the interval, the EXEC facility resumes the current connection. If no connections exist, the EXEC facility returns the terminal to the idle state and disconnects the incoming session.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This prevents unauthorized users from misusing abandoned sessions. For example, if the network administrator leaves for the day and leaves a computer open with an enabled login session accessible. There is a trade-off here between security (shorter timeouts) and usability (longer timeouts). Review your local policies and operational needs to determine the best timeout value. In most cases, this should be no more than 10 minutes.", "compliance.rule.impact": "Organizations should prevent unauthorized use of unattended or abandoned sessions by an automated control. Enabling 'exec-timeout' with an appropriate length of minutes or seconds prevents unauthorized access of abandoned sessions.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/D_through_E.html#GUID-76805E6F-9E89-4457-A9DC-5944C8FE5419", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000016}, {"compliance.rule.name": "Set 'exec-timeout' to less than or equal to 10 minutes 'line console 0'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*(exec-timeout)\\s*((10)|([0-9]))\\s*[\\d+]*\\s*", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line con", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure device timeout (10 minutes or less) to disconnect sessions after a fixed idle time.\n```\nhostname(config)#line con 0\nhostname(config-line)#exec-timeout <timeout_in_minutes> <timeout_in_seconds>\n```", "compliance.rule.description": "If no input is detected during the interval, the EXEC facility resumes the current connection. If no connections exist, the EXEC facility returns the terminal to the idle state and disconnects the incoming session.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This prevents unauthorized users from misusing abandoned sessions. For example, if the network administrator leaves for the day and leaves a computer open with an enabled login session accessible. There is a trade-off here between security (shorter timeouts) and usability (longer timeouts). Review your local policies and operational needs to determine the best timeout value. In most cases, this should be no more than 10 minutes.", "compliance.rule.impact": "Organizations should prevent unauthorized use of unattended or abandoned sessions by an automated control. Enabling 'exec-timeout' with an appropriate length reduces the risk of unauthorized access of abandoned sessions.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/D_through_E.html#GUID-76805E6F-9E89-4457-A9DC-5944C8FE5419", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000017}, {"compliance.rule.name": "Set 'exec-timeout' to less than or equal to 10 minutes 'line vty'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*(exec-timeout)\\s*((10)|([0-9]))\\s*[\\d+]*\\s*", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "line vty", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure device timeout (10 minutes or less) to disconnect sessions after a fixed idle time.\n```\nhostname(config)#line vty {line_number} [ending_line_number]\nhostname(config-line)#exec-timeout <<span>timeout_in_minutes> <timeout_in_seconds</span>>\n```", "compliance.rule.description": "If no input is detected during the interval, the EXEC facility resumes the current connection. If no connections exist, the EXEC facility returns the terminal to the idle state and disconnects the incoming session.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This prevents unauthorized users from misusing abandoned sessions. For example, if the network administrator leaves for the day and leaves a computer open with an enabled login session accessible. There is a trade-off here between security (shorter timeouts) and usability (longer timeouts). Review your local policies and operational needs to determine the best timeout value. In most cases, this should be no more than 10 minutes.", "compliance.rule.impact": "Organizations should prevent unauthorized use of unattended or abandoned sessions by an automated control. Enabling 'exec-timeout' with an appropriate length of minutes or seconds prevents unauthorized access of abandoned sessions.", "compliance.rule.default.value": "", "compliance.rule.references": "https://www.cisco.com/c/en/us/td/docs/switches/datacenter/mds9000/sw/command/b_cisco_mds_9000_cr_book/l_commands.html#wp3716128869", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000018}, {"compliance.rule.name": "Set 'transport input none' for 'line aux 0'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "line aux 0 | incl input transports", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "Allowed input transports are none.", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the inbound connections on the auxiliary port.\n```\nhostname(config)#line aux 0\nhostname(config-line)#transport input none \n```", "compliance.rule.description": "When you want to allow only an outgoing connection on a line, use the no exec command.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Unused ports should be disabled, if not required, since they provide a potential access path for attackers. Some devices include both an auxiliary and console port that can be used to locally connect to and configure the device. The console port is normally the primary port used to configure the device; even when remote, backup administration is required via console server or Keyboard, Video, Mouse (KVM) hardware. The auxiliary port is primarily used for dial-up administration via an external modem; instead, use other available methods.", "compliance.rule.impact": "Organizations should prevent all unauthorized access of auxiliary ports by disabling all protocols using the 'transport input none' command.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/termserv/command/reference/tsv_s1.html#wp1069219", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000019}, {"compliance.rule.name": "Set 'http Secure-server' limit", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip http secure-server", "occurrence": 1, "operator": "NONE"}]}, "compliance.rule.remediation": "```\nhostname(config)#ip http max-connections 2\n```", "compliance.rule.description": "Device management includes the ability to control the number of administrators and management sessions that manage a device. Limiting the number of allowed administrators and sessions per administrator based on account type, role, or access type is helpful in limiting risks related to denial-of-service (DoS) attacks.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This requirement addresses concurrent sessions for administrative accounts and does not address concurrent sessions by a single administrator via multiple administrative accounts. The maximum number of concurrent sessions should be defined based upon mission needs and the operational environment for each system. At a minimum, limits must be set for SSH, HTTPS, account of last resort, and root account sessions. Center for Internet Security recommends a limit of 2", "compliance.rule.impact": "", "compliance.rule.default.value": "", "compliance.rule.references": "NIST SP 800-53 :: AC-10:https://www.tenable.com/audits/items/DISA_STIG_Cisco_IOS_XE_Switch_NDM_v2r1.audit:392b7a414732407810fa9d87c5a0d581", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": **************}, {"compliance.rule.name": "Set 'exec-timeout' to less than or equal to 10 min on 'ip http'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip http timeout-policy", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure device timeout (10 minutes or less) to disconnect sessions after a fixed idle time.\n```\nip http timeout-policy idle 600 life {nnnn} requests {nn}\n```", "compliance.rule.description": "If no input is detected during the interval, the EXEC facility resumes the current connection. If no connections exist, the EXEC facility returns the terminal to the idle state and disconnects the incoming session.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This prevents unauthorized users from misusing abandoned sessions. For example, if the network administrator leaves for the day and leaves a computer open with an enabled login session accessible. There is a trade-off here between security (shorter timeouts) and usability (longer timeouts). Review your local policies and operational needs to determine the best timeout value. In most cases, this should be no more than 10 minutes.\n\nThis prevents unauthorized users from misusing abandoned sessions. For example, if the network administrator leaves for the day and leaves a computer open with an enabled login session accessible. There is a trade-off here between security (shorter timeouts) and usability (longer timeouts). Review your local policies and operational needs to determine the best timeout value. In most cases, this should be no more than 10 minutes.", "compliance.rule.impact": "", "compliance.rule.default.value": "disabled", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/D_through_E.html#GUID-76805E6F-9E89-4457-A9DC-5944C8FE5419", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000021}, {"compliance.rule.name": "Set the 'banner-text' for 'banner exec'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "banner exec", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the EXEC banner presented to a user when accessing the devices enable prompt.\n```\n\nhostname(config)#banner exec c\nEnter TEXT message. End with the character 'c'.\n<banner-text>\nc\n```", "compliance.rule.description": "This command specifies a message to be displayed when an EXEC process is created (a line is activated, or an incoming connection is made to a vty). Follow this command with one or more blank spaces and a delimiting character of your choice. Then enter one or more lines of text, terminating the message with the second occurrence of the delimiting character.\n\nWhen a user connects to a router, the message-of-the-day (MOTD) banner appears first, followed by the login banner and prompts. After the user logs in to the router, the EXEC banner or incoming banner will be displayed, depending on the type of connection. For a reverse Telnet login, the incoming banner will be displayed. For all other connections, the router will display the EXEC banner.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "\"Network banners are electronic messages that provide notice of legal rights to users of computer networks. From a legal standpoint, banners have four primary functions.\n\n- First, banners may be used to generate consent to real-time monitoring under Title III.\n- Second, banners may be used to generate consent to the retrieval of stored files and records pursuant to ECPA.\n- Third, in the case of government networks, banners may eliminate any Fourth Amendment \"reasonable expectation of privacy\" that government employees or other users might otherwise retain in their use of the government's network under O'Connor v. Ortega, 480 U.S. 709 (1987).\n- Fourth, in the case of a non-government network, banners may establish a system administrator's \"common authority\" to consent to a law enforcement search pursuant to United States v. <PERSON>, 415 U.S. 164 (1974).\" (US Department of Justice APPENDIX A: Sample Network Banner Language)", "compliance.rule.impact": "Organizations provide appropriate legal notice(s) and warning(s) to persons accessing their networks by using a 'banner-text' for the banner exec command.", "compliance.rule.default.value": "No banner is set by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/A_through_B.html#GUID-0DEF5B57-A7D9-4912-861F-E837C82A3881", "compliance.rule.additional.information": "The default is no banner.", "compliance.rule.controls": [{"compliance.rule.control.version": "14.1", "compliance.rule.control.name": "Establish and Maintain a Security Awareness Program", "compliance.rule.control.description": "Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "17.3", "compliance.rule.control.name": "Implement a Security Awareness Program", "compliance.rule.control.description": "Create a security awareness program for all workforce members to complete on a regular basis to ensure they understand and exhibit the necessary behaviors and skills to help ensure the security of the organization. The organization's security awareness program should be communicated in a continuous and engaging manner.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000022}, {"compliance.rule.name": "Set the 'banner-text' for 'banner login'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "banner login", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the device so a login banner presented to a user attempting to access the device.\n```\n\nhostname(config)#banner login c\nEnter TEXT message. End with the character 'c'.\n<banner-text>\nc\n```", "compliance.rule.description": "Follow the banner login command with one or more blank spaces and a delimiting character of your choice. Then enter one or more lines of text, terminating the message with the second occurrence of the delimiting character.\n\nWhen a user connects to the router, the message-of-the-day (MOTD) banner (if configured) appears first, followed by the login banner and prompts. After the user successfully logs in to the router, the EXEC banner or incoming banner will be displayed, depending on the type of connection. For a reverse Telnet login, the incoming banner will be displayed. For all other connections, the router will display the EXEC banner.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "\"Network banners are electronic messages that provide notice of legal rights to users of computer networks. From a legal standpoint, banners have four primary functions.\n\n- First, banners may be used to generate consent to real-time monitoring under Title III.\n- Second, banners may be used to generate consent to the retrieval of stored files and records pursuant to ECPA.\n- Third, in the case of government networks, banners may eliminate any Fourth Amendment \"reasonable expectation of privacy\" that government employees or other users might otherwise retain in their use of the government's network under O'Connor v. Ortega, 480 U.S. 709 (1987).\n- Fourth, in the case of a non-government network, banners may establish a system administrator's \"common authority\" to consent to a law enforcement search pursuant to United States v. <PERSON>, 415 U.S. 164 (1974).\" (US Department of Justice APPENDIX A: Sample Network Banner Language)", "compliance.rule.impact": "Organizations provide appropriate legal notice(s) and warning(s) to persons accessing their networks by using a 'banner-text' for the banner login command.", "compliance.rule.default.value": "No banner is set by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/A_through_B.html#GUID-FF0B6890-85B8-4B6A-90DD-1B7140C5D22F", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "14.1", "compliance.rule.control.name": "Establish and Maintain a Security Awareness Program", "compliance.rule.control.description": "Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "17.3", "compliance.rule.control.name": "Implement a Security Awareness Program", "compliance.rule.control.description": "Create a security awareness program for all workforce members to complete on a regular basis to ensure they understand and exhibit the necessary behaviors and skills to help ensure the security of the organization. The organization's security awareness program should be communicated in a continuous and engaging manner.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000023}, {"compliance.rule.name": "Set the 'banner-text' for 'banner motd'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "banner motd", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the message of the day (MOTD) banner presented when a user first connects to the device.\n```\n\nhostname(config)#banner motd c\nEnter TEXT message. End with the character 'c'.\n<banner-text>\nc\n```", "compliance.rule.description": "This MOTD banner is displayed to all terminals connected and is useful for sending messages that affect all users (such as impending system shutdowns). Use the no exec-banner or no motd-banner command to disable the MOTD banner on a line. The no exec-banner command also disables the EXEC banner on the line.\n\nWhen a user connects to the router, the MOTD banner appears before the login prompt. After the user logs in to the router, the EXEC banner or incoming banner will be displayed, depending on the type of connection. For a reverse Telnet login, the incoming banner will be displayed. For all other connections, the router will display the EXEC banner.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "\"Network banners are electronic messages that provide notice of legal rights to users of computer networks. From a legal standpoint, banners have four primary functions.\n\n- First, banners may be used to generate consent to real-time monitoring under Title III.\n- Second, banners may be used to generate consent to the retrieval of stored files and records pursuant to ECPA.\n- Third, in the case of government networks, banners may eliminate any Fourth Amendment \"reasonable expectation of privacy\" that government employees or other users might otherwise retain in their use of the government's network under O'Connor v. Ortega, 480 U.S. 709 (1987).\n- Fourth, in the case of a non-government network, banners may establish a system administrator's \"common authority\" to consent to a law enforcement search pursuant to United States v. <PERSON>, 415 U.S. 164 (1974).\" (US Department of Justice APPENDIX A: Sample Network Banner Language)", "compliance.rule.impact": "Organizations provide appropriate legal notice(s) and warning(s) to persons accessing their networks by using a 'banner-text' for the banner motd command.", "compliance.rule.default.value": "No banner is set by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/A_through_B.html#GUID-7416C789-9561-44FC-BB2A-D8D8AFFB77DD", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "14.1", "compliance.rule.control.name": "Establish and Maintain a Security Awareness Program", "compliance.rule.control.description": "Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "17.3", "compliance.rule.control.name": "Implement a Security Awareness Program", "compliance.rule.control.description": "Create a security awareness program for all workforce members to complete on a regular basis to ensure they understand and exhibit the necessary behaviors and skills to help ensure the security of the organization. The organization's security awareness program should be communicated in a continuous and engaging manner.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000024}, {"compliance.rule.name": "Set the 'banner-text' for 'webauth banner'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip admission auth-proxy-banner http", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the webauth banner presented when a user connects to the device.\n```\nhostname(config)#ip admission auth-proxy-banner http {banner-text | filepath}\n```", "compliance.rule.description": "This banner is displayed to all terminals connected and is useful for sending messages that affect all users (such as impending system shutdowns). Use the no exec-banner or no motd-banner command to disable the banner on a line. The no exec-banner command also disables the EXEC banner on the line.\n\nWhen a user connects to the router, the MOTD banner appears before the login prompt. After the user logs in to the router, the EXEC banner or incoming banner will be displayed, depending on the type of connection. For a reverse Telnet login, the incoming banner will be displayed. For all other connections, the router will display the EXEC banner.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "\"Network banners are electronic messages that provide notice of legal rights to users of computer networks. From a legal standpoint, banners have four primary functions.\n\n- First, banners may be used to generate consent to real-time monitoring under Title III.\n- Second, banners may be used to generate consent to the retrieval of stored files and records pursuant to ECPA.\n- Third, in the case of government networks, banners may eliminate any Fourth Amendment \"reasonable expectation of privacy\" that government employees or other users might otherwise retain in their use of the government's network under O'Connor v. Ortega, 480 U.S. 709 (1987).\n- Fourth, in the case of a non-government network, banners may establish a system administrator's \"common authority\" to consent to a law enforcement search pursuant to United States v. <PERSON>, 415 U.S. 164 (1974).\" (US Department of Justice APPENDIX A: Sample Network Banner Language)", "compliance.rule.impact": "Organizations provide appropriate legal notice(s) and warning(s) to persons accessing their networks by using a 'banner-text' for the banner motd command.", "compliance.rule.default.value": "No banner is set by default", "compliance.rule.references": "https://www.cisco.com/c/en/us/td/docs/switches/lan/catalyst9500/software/release/16-9/configuration_guide/sec/b_169_sec_9500_cg/configuring_web_based_authentication.html", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "14.1", "compliance.rule.control.name": "Establish and Maintain a Security Awareness Program", "compliance.rule.control.description": "Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "17.3", "compliance.rule.control.name": "Implement a Security Awareness Program", "compliance.rule.control.description": "Create a security awareness program for all workforce members to complete on a regular basis to ensure they understand and exhibit the necessary behaviors and skills to help ensure the security of the organization. The organization's security awareness program should be communicated in a continuous and engaging manner.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000025}, {"compliance.rule.name": "Set 'password' for 'enable secret'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "enable secret", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure a strong, enable secret password.\n```\nhostname(config)#enable secret 9 {ENABLE_SECRET_PASSWORD} \n```", "compliance.rule.description": "Enable secret password type 5 and enable secret password type 5 must be migrated to the stronger password type 8 or 9. IF a device is upgraded from IOS XE 16.9 or later the type 5 is auto converted to type 9.\n\nUse the enable secret command to provide an additional layer of security over the enable password. The enable secret command provides better security by storing the enable secret password using a nonreversible cryptographic function. The added layer of security encryption provides is useful in environments where the password crosses the network or is stored on a TFTP server.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Requiring the enable secret setting protects privileged EXEC mode. By default, a strong password is not required, a user can just press the Enter key at the Password prompt to start privileged mode. The enable password command causes the device to enforce use of a password to access privileged mode. Enable secrets use a one-way cryptographic hash (MD5). This is preferred to Level 7 enable passwords that use a weak, well-known, and easily reversible encryption algorithm.", "compliance.rule.impact": "Default device configuration does not require strong user authentication potentially enabling unfettered access to an attacker that is able to reach the device. Creating a local account with an encrypted password enforces login authentication and provides a fallback authentication mechanism for configuration in a named method list in a situation where centralized authentication, authorization, and accounting services are unavailable. The following is the type of encryption the device will allow as of 15.3: Type 0 this mean the password will not be encrypted when router store it in Run/Start Files command: enable password cisco123\n\nType 4 this mean the password will be encrypted when router store it in Run/Start Files using SHA-256 which apps like <PERSON> can crack but will take long time command : enable secret 4 Rv4kArhts7yA2xd8BD2YTVbts (notice above is not the password string it self but the hash of the password)\n\nthis type is deprecated starting from IOS 15.3(3)\n\n**_Type 5_** this mean the password will be encrypted when router store it in Run/Start Files using MD5 which apps like <PERSON> can crack but will take long time command: enable secret 5 00271A5307542A02D22842 (notice above is not the password string it self but the hash of the password) or enable secret cisco123 (notice above is the password string it self)\n\n**_Type 7_** this mean the password will be encrypted when router store it in Run/Start Files using Vigenere cipher which any website with type7 reverser can crack it in less than one second command : ena password cisco123 service password-encryption\n\n**_Type 8_**\n\nthis mean the password will be encrypted when router store it in Run/Start Files using PBKDF2-SHA-256\n\nstarting from IOS 15.3(3).\n\nPassword-Based Key Derivation Function 2 (PBKDF2) with Secure Hash Algorithm, 26-bits (SHA-256) as the hashing algorithm\n\nExample :\nR1(config)#enable algorithm-type sha256 secret cisco\n\nR1(config)#do sh run | i enable\n\nenable secret 8 $8$mTj4RZG8N9ZDOk$elY/asfm8kD3iDmkBe3hD2r4xcA/0oWS5V3os.O91u.\n\nExample :\nR1(config)# username yasser algorithm-type sha256 secret cisco\n\nR1# show running-config | inc username\n\nusername yasser secret 8 $8$dsYGNam3K1SIJO$7nv/35M/qr6t.dVc7UY9zrJDWRVqncHub1PE9UlMQFs\n\n**_Type 9_**\n\nthis means the password will be encrypted when router store it in Run/Start Files using scrypt as the hashing algorithm.\n\nstarting from IOS 15.3(3)\n\nExample :\nR1(config)#ena algorithm-type scrypt secret cisco\n\nR1(config)#do sh run | i enable\n\nenable secret 9 $9$WnArItcQHW/uuE$x5WTLbu7PbzGDuv0fSwGKS/KURsy5a3WCQckmJp0MbE\n\nExample :\nR1(config)# username demo9 algorithm-type scrypt secret cisco\n\nR1# show running-config | inc username\n\nusername demo9 secret 9 $9$nhEmQVczB7dqsO$X.HsgL6x1il0RxkOSSvyQYwucySCt7qFm4v7pqCxkKM\n\nImportant Notes:\n\n1-If you configure type 8 or type 9 passwords and then downgrade to a release that does not support type 8 and type 9 passwords, you must configure the type 5 passwords before downgrading. If not, you are locked out of the device and a password recovery is required.\n\n2-Starting from IOS 15.3(3)The 4 keyword was deprecated and support for type 8 and type 9 algorithms were added and The warning message for removal of support for the type 4 algorithm was added\ntect privileged EXEC mode through policies requiring the 'enabling secret' setting, which enforces a one-way cryptographic hash (MD5).", "compliance.rule.default.value": "No enable secret password setup by default", "compliance.rule.references": "https://www.cisco.com/c/en/us/td/docs/switches/lan/catalyst9600/software/release/16-12/configuration_guide/sec/b_1612_sec_9600_cg/controlling_switch_access_with_passwords_and_privilege_levels.html", "compliance.rule.additional.information": "Note: You cannot recover a lost encrypted password. You must clear NVRAM and set a new password.", "compliance.rule.controls": [{"compliance.rule.control.version": "5.4", "compliance.rule.control.name": "Restrict Administrator Privileges to Dedicated Administrator Accounts", "compliance.rule.control.description": "Restrict administrator privileges to dedicated administrator accounts on enterprise assets. Conduct general computing activities, such as internet browsing, email, and productivity suite use, from the user’s primary, non-privileged account.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Ensure the Use of Dedicated Administrative Accounts", "compliance.rule.control.description": "Ensure that all users with administrative account access use a dedicated or secondary account for elevated activities. This account should only be used for administrative activities and not internet browsing, email, or similar activities.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Enable 'service password-encryption'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "service password-encryption", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Enable password encryption service to protect sensitive access passwords in the device configuration.\n```\n\nhostname(config)#service password-encryption\n```", "compliance.rule.description": "When password encryption is enabled, the encrypted form of the passwords is displayed when a more system:running-config command is entered.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This requires passwords to be encrypted in the configuration file to prevent unauthorized users from learning the passwords just by reading the configuration. When not enabled, many of the device's passwords will be rendered in plain text in the configuration file. This service ensures passwords are rendered as encrypted strings preventing an attacker from easily determining the configured value.", "compliance.rule.impact": "Organizations implementing 'service password-encryption' reduce the risk of unauthorized users learning clear text passwords to Cisco IOS configuration files. However, the algorithm used is not designed to withstand serious analysis and should be treated like clear-text.", "compliance.rule.default.value": "Service password encryption is not set by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/s1/sec-cr-s1.html#GUID-CC0E305A-604E-4A74-8A1A-975556CE5871", "compliance.rule.additional.information": "Caution: This command does not provide a high level of network security. If you use this command, you should also take additional network security measures.\n\nNote: You cannot recover a lost encrypted password. You must clear NVRAM and set a new password.", "compliance.rule.controls": [{"compliance.rule.control.version": "3.11", "compliance.rule.control.name": "Encrypt Sensitive Data at Rest", "compliance.rule.control.description": "Encrypt sensitive data at rest on servers, applications, and databases containing sensitive data. Storage-layer encryption, also known as server-side encryption, meets the minimum requirement of this Safeguard. Additional encryption methods may include application-layer encryption, also known as client-side encryption, where access to the data storage device(s) does not permit access to the plain-text data.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.4", "compliance.rule.control.name": "Encrypt or Hash all Authentication Credentials", "compliance.rule.control.description": "Encrypt or hash with a salt all authentication credentials when stored.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000027}, {"compliance.rule.name": "Set 'username secret' for all local users", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*\\bsecret\\b.*", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "username", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Create a local user with an encrypted, complex (not easily guessed) password.\n```\n\nhostname(config)#username {{em}LOCAL_USERNAME{/em}} secret {{em}LOCAL_PASSWORD{/em}}\n```", "compliance.rule.description": "Username secret password type 5 and enable secret password type 5 must be migrated to the stronger password type 8 or 9. IF a device is upgraded from IOS XE 16.9 or later the type 5 is auto converted to type 9. \n\nThe username secret command provides an additional layer of security over the username password.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Default device configuration does not require strong user authentication potentially enabling unfettered access to an attacker that is able to reach the device. Creating a local account with an encrypted password enforces login authentication and provides a fallback authentication mechanism for configuration in a named method list in a situation where centralized authentication, authorization, and accounting services are unavailable.\nThe following is the type of encryption the device will allow as of 15.3:\nType 0\nthis mean the password will not be encrypted when router store it in Run/Start Files\ncommand:\nenable password cisco123\n\nType 4\nthis mean the password will be encrypted when router store it in Run/Start Files using SHA-256\nwhich apps like <PERSON> can crack but will take long time\ncommand :\nenable secret 4 Rv4kArhts7yA2xd8BD2YTVbts\n(notice above is not the password string it self but the hash of the password)\n\nthis type is deprecated starting from IOS 15.3(3)\n\nType 5\nthis mean the password will be encrypted when router store it in Run/Start Files using MD5\nwhich apps like <PERSON> can crack but will take long time\ncommand:\nenable secret 5 00271A5307542A02D22842\n(notice above is not the password string it self but the hash of the password)\nor\nenable secret cisco123\n(notice above is the password string it self)\n\nType 7\nthis mean the password will be encrypted when router store it in Run/Start Files using Vigenere cipher\nwhich any website with type7 reverser can crack it in less than one second\ncommand :\nena password cisco123\nservice password-encryption\n\nType 8\n\nthis mean the password will be encrypted when router store it in Run/Start Files using PBKDF2-SHA-256\n\nstarting from IOS 15.3(3).\n\nPassword-Based Key Derivation Function 2 (PBKDF2) with Secure Hash Algorithm, 26-bits (SHA-256) as the hashing algorithm\n\nExample :\n\nR1(config)#enable algorithm-type sha256 secret cisco\n\nR1(config)#do sh run | i enable\n\nenable secret 8 $8$mTj4RZG8N9ZDOk$elY/asfm8kD3iDmkBe3hD2r4xcA/0oWS5V3os.O91u.\n\nExample :\n\nR1(config)# username yasser algorithm-type sha256 secret cisco\n\nR1# show running-config | inc username\n\nusername yasser secret 8 $8$dsYGNam3K1SIJO$7nv/35M/qr6t.dVc7UY9zrJDWRVqncHub1PE9UlMQFs\n\nType 9\n\nthis mean the password will be encrypted when router store it in Run/Start Files using scrypt as the hashing algorithm.\n\nstarting from IOS 15.3(3)\n\nExample :\n\nR1(config)#ena algorithm-type scrypt secret cisco\n\nR1(config)#do sh run | i enable\n\nenable secret 9 $9$WnArItcQHW/uuE$x5WTLbu7PbzGDuv0fSwGKS/KURsy5a3WCQckmJp0MbE\n\nExample :\n\nR1(config)# username demo9 algorithm-type scrypt secret cisco\n\nR1# show running-config | inc username\n\nusername demo9 secret 9 $9$nhEmQVczB7dqsO$X.HsgL6x1il0RxkOSSvyQYwucySCt7qFm4v7pqCxkKM\n\nImportant Notes:\n\n1-If you configure type 8 or type 9 passwords and then downgrade to a release that does not support type 8 and type 9 passwords, you must configure the type 5 passwords before downgrading. If not, you are locked out of the device and a password recovery is required.\n\n2-Starting from IOS 15.3(3)The 4 keyword was deprecated and support for type 8 and type 9 algorithms were added and The warning message for removal of support for the type 4 algorithm was added", "compliance.rule.impact": "Organizations implementing 'username secret' across their enterprise reduce the risk of unauthorized users gaining access to Cisco IOS devices by applying a MD5 hash and encrypting user passwords.", "compliance.rule.default.value": "No passwords are set by default", "compliance.rule.references": "https://www.cisco.com/c/en/us/td/docs/switches/lan/catalyst9600/software/release/16-12/configuration_guide/sec/b_1612_sec_9600_cg/controlling_switch_access_with_passwords_and_privilege_levels.html", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "3.11", "compliance.rule.control.name": "Encrypt Sensitive Data at Rest", "compliance.rule.control.description": "Encrypt sensitive data at rest on servers, applications, and databases containing sensitive data. Storage-layer encryption, also known as server-side encryption, meets the minimum requirement of this Safeguard. Additional encryption methods may include application-layer encryption, also known as client-side encryption, where access to the data storage device(s) does not permit access to the plain-text data.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.4", "compliance.rule.control.name": "Encrypt or Hash all Authentication Credentials", "compliance.rule.control.description": "Encrypt or hash with a salt all authentication credentials when stored.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000028}, {"compliance.rule.name": "Set 'no snmp-server' to disable SNMP when unused", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "show snmp community", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "%SNMP agent not enabled", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable SNMP read and write access if not in used to monitor and/or manage device.\n```\nhostname(config)#no snmp-server \n```", "compliance.rule.description": "If not in use, disable simple network management protocol (SNMP), read and write access.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SNMP read access allows remote monitoring and management of the device.", "compliance.rule.impact": "Organizations not using SNMP should require all SNMP services to be disabled by running the 'no snmp-server' command.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-book.html", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000029}, {"compliance.rule.name": "Unset 'private' for 'snmp-server community'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": "snmp-server community private", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the default SNMP community string `private`\n```\nhostname(config)#no snmp-server community {private}\n```", "compliance.rule.description": "An SNMP community string permits read-only access to all objects.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The default community string \"private\" is well known. Using easy to guess, well known community string poses a threat that an attacker can effortlessly gain unauthorized access to the device.", "compliance.rule.impact": "To reduce the risk of unauthorized access, Organizations should disable default, easy to guess, settings such as the 'private' setting for snmp-server community.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s2.html#GUID-2F3F13E4-EE81-4590-871D-6AE1043473DE", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000030}, {"compliance.rule.name": "Unset 'public' for 'snmp-server community'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": "snmp-server community public", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the default SNMP community string \"public\"\n```\n\nhostname(config)#no snmp-server community {public} \n```", "compliance.rule.description": "An SNMP community string permits read-only access to all objects.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The default community string \"public\" is well known. Using easy to guess, well known community string poses a threat that an attacker can effortlessly gain unauthorized access to the device.", "compliance.rule.impact": "To reduce the risk of unauthorized access, Organizations should disable default, easy to guess, settings such as the 'public' setting for snmp-server community.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s2.html#GUID-2F3F13E4-EE81-4590-871D-6AE1043473DE", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000031}, {"compliance.rule.name": "Do not set 'RW' for any 'snmp-server community'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": "snmp-server\\s+community\\s+.*\\s+RW", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable SNMP write access.\n```\n\nhostname(config)#no snmp-server community {<em>write_community_string</em>} \n```", "compliance.rule.description": "Specifies read-write access. Authorized management stations can both retrieve and modify MIB objects.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Enabling SNMP read-write enables remote management of the device. Unless absolutely necessary, do not allow simple network management protocol (SNMP) write access.", "compliance.rule.impact": "To reduce the risk of unauthorized access, Organizations should disable the SNMP 'write' access for snmp-server community.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s2.html#GUID-2F3F13E4-EE81-4590-871D-6AE1043473DE", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000032}, {"compliance.rule.name": "Set the ACL for each 'snmp-server community'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "snmp-server community public RO\\s+(\\d+)", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure authorized SNMP community string and restrict access to authorized management systems.\n```\n\nhostname(config)#snmp-server community <<em>community_string</em>> ro {<em>snmp_access-list_number | \n<span>snmp_access-list_name</span></em><span>}</span>\n```", "compliance.rule.description": "This feature specifies a list of IP addresses that are allowed to use the community string to gain access to the SNMP agent.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "If ACLs are not applied, then anyone with a valid SNMP community string can potentially monitor and manage the router. An ACL should be defined and applied for all SNMP access to limit access to a small number of authorized management stations segmented in a trusted management zone. If possible, use SNMPv3 which uses authentication, authorization, and data privatization (encryption).", "compliance.rule.impact": "To reduce the risk of unauthorized access, Organizations should enable access control lists for all snmp-server communities and restrict the access to appropriate trusted management zones. If possible, implement SNMPv3 to apply authentication, authorization, and data privatization (encryption) for additional benefits to the organization.", "compliance.rule.default.value": "No ACL is set for SNMP", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s2.html#GUID-2F3F13E4-EE81-4590-871D-6AE1043473DE", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000033}, {"compliance.rule.name": "Create an 'access-list' for use with SNMP", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "snmp-server community ([a-zA-Z0-9]+) (RO|RW) ([a-zA-Z0-9]+)", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure SNMP ACL for restricting access to the device from authorized management stations segmented in a trusted management zone.\n```\n\nhostname(config)#access-list <<em>snmp_acl_number</em>> permit <<em>snmp_access-list</em>>\nhostname(config)#access-list deny any log \n```", "compliance.rule.description": "You can use access lists to control the transmission of packets on an interface, control Simple Network Management Protocol (SNMP) access, and restrict the contents of routing updates. The Cisco IOS software stops checking the extended access list after a match occurs.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SNMP ACLs control what addresses are authorized to manage and monitor the device via SNMP. If ACLs are not applied, then anyone with a valid SNMP community string may monitor and manage the router. An ACL should be defined and applied for all SNMP community strings to limit access to a small number of authorized management stations segmented in a trusted management zone.", "compliance.rule.impact": "", "compliance.rule.default.value": "SNMP does not use an access list.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/a1/sec-cr-a2.html#GUID-9EA733A3-1788-4882-B8C3-AB0A2949120C", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000034}, {"compliance.rule.name": "Set 'snmp-server host' when using SNMP", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "snmp-server host", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure authorized SNMP trap community string and restrict sending messages to authorized management systems.\n```\n\nhostname(config)#snmp-server host {ip_address} {trap_community_string} {notification-type} \n```", "compliance.rule.description": "SNMP notifications can be sent as traps to authorized management systems.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "If SNMP is enabled for device management and device alerts are required, then ensure the device is configured to submit traps only to authorize management systems.", "compliance.rule.impact": "Organizations using SNMP should restrict sending SNMP messages only to explicitly named systems to reduce unauthorized access.", "compliance.rule.default.value": "A recipient is not specified to receive notifications.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s5.html#GUID-D84B2AB5-6485-4A23-8C26-73E50F73EE61", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000035}, {"compliance.rule.name": "Set 'snmp-server enable traps snmp'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "snmp-server enable traps", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Enable SNMP traps.\n```\nhostname(config)#snmp-server enable traps snmp authentication linkup linkdown coldstart \n```", "compliance.rule.description": "SNMP notifications can be sent as traps to authorized management systems.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SNMP has the ability to submit traps .", "compliance.rule.impact": "Organizations using SNMP should restrict trap types only to explicitly named traps to reduce unintended traffic. Enabling SNMP traps without specifying trap type will enable all SNMP trap types.", "compliance.rule.default.value": "SNMP notifications are disabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s3.html#GUID-EB3EB677-A355-42C6-A139-85BA30810C54", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "12.8", "compliance.rule.control.name": "Establish and Maintain Dedicated Computing Resources for All Administrative Work", "compliance.rule.control.description": "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access.", "compliance.rule.control.ig": ["ig3"]}, {"compliance.rule.control.version": "11.7", "compliance.rule.control.name": "Manage Network Infrastructure Through a Dedicated Network", "compliance.rule.control.description": "Manage the network infrastructure across network connections that are separated from the business use of that network, relying on separate VLANs or, preferably, on entirely different physical connectivity for management sessions for network devices.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000036}, {"compliance.rule.name": "Set 'priv' for each 'snmp-server group' using SNMPv3", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*snmp-server group.*v3\\s*priv", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "For each SNMPv3 group created on your router add privacy options by issuing the following command...\n```\n\nhostname(config)#snmp-server group {<em>group_name</em>} v3 priv\n```", "compliance.rule.description": "Specifies authentication of a packet with encryption when using SNMPv3", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SNMPv3 provides much improved security over previous versions by offering options for Authentication and Encryption of messages. When configuring a user for SNMPv3 you have the option of using a range of encryption schemes, or no encryption at all, to protect messages in transit. AES128 is the minimum strength encryption method that should be deployed.", "compliance.rule.impact": "Organizations using SNMP can significantly reduce the risks of unauthorized access by using the 'snmp-server group v3 priv' setting to encrypt messages in transit.", "compliance.rule.default.value": "No SNMP server groups are configured.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s5.html#GUID-56E87D02-C56F-4E2D-A5C8-617E31740C3F", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000037}, {"compliance.rule.name": "Require 'aes 128' as minimum for 'snmp-server user' when using SNMPv3", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*snmp-server user.*v3 auth sha.*priv aes 128.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "For each SNMPv3 user created on your router add privacy options by issuing the following command.\n```\n\nhostname(config)#snmp-server user {user_name} {group_name} v3 auth sha {auth_password} priv aes 128 {priv_password} {acl_name_or_number}\n```", "compliance.rule.description": "Specify the use of a minimum of 128-bit AES algorithm for encryption when using SNMPv3.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SNMPv3 provides much improved security over previous versions by offering options for Authentication and Encryption of messages. When configuring a user for SNMPv3 you have the option of using a range of encryption schemes, or no encryption at all, to protect messages in transit. AES128 is the minimum strength encryption method that should be deployed.", "compliance.rule.impact": "Organizations using SNMP can significantly reduce the risks of unauthorized access by using the 'snmp-server user' setting with appropriate authentication and privacy protocols to encrypt messages in transit.", "compliance.rule.default.value": "SNMP username as not set by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/snmp/command/nm-snmp-cr-s5.html#GUID-4EED4031-E723-4B84-9BBF-610C3CF60E31", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "3.10", "compliance.rule.control.name": "Encrypt Sensitive Data in Transit", "compliance.rule.control.description": "Encrypt sensitive data in transit. Example implementations can include: Transport Layer Security (TLS) and Open Secure Shell (OpenSSH).", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "18.5", "compliance.rule.control.name": "Use Only Standardized and Extensively Reviewed Encryption Algorithms", "compliance.rule.control.description": "Use only standardized and extensively reviewed encryption algorithms.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000038}, {"compliance.rule.name": "Set 'no cdp run'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "no cdp run", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable Cisco Discovery Protocol (CDP) service globally.\n```\n\nhostname(config)#no cdp run\n```", "compliance.rule.description": "Disable Cisco Discovery Protocol (CDP) service at device level.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The Cisco Discovery Protocol is a proprietary protocol that Cisco devices use to identify each other on a LAN segment. It is useful only in network monitoring and troubleshooting situations but is considered a security risk because of the amount of information provided from queries. In addition, there have been published denial-of-service (DoS) attacks that use CDP. CDP should be completely disabled unless necessary.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting network protocols and explicitly require disabling all insecure or unnecessary protocols.", "compliance.rule.default.value": "Enabled on all platforms except the Cisco 10000 Series Edge Services Router", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/cdp/command/cdp-cr-a1.html#GUID-E006FAC8-417E-4C3F-B732-4D47B0447750", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000039}, {"compliance.rule.name": "Set 'no ip bootp server'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*ip dhcp bootp ignore.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the bootp server.\n```\n\nhostname(config)#ip dhcp bootp ignore\n```", "compliance.rule.description": "Disable the Bootstrap Protocol (BOOTP) service on your routing device.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "BootP allows a router to issue IP addresses. This should be disabled unless there is a specific requirement.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting network protocols and explicitly require disabling all insecure or unnecessary protocols such as 'ip bootp server'.", "compliance.rule.default.value": "Enabled", "compliance.rule.references": "Cisco IOS software receives Cisco Discovery Protocol information", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000040}, {"compliance.rule.name": "Set 'no service dhcp'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*no service dhcp.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the DHCP server.\n```\n\nhostname(config)#<strong>no service dhcp</strong>\n```", "compliance.rule.description": "Disable the Dynamic Host Configuration Protocol (DHCP) server and relay agent features on your router.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The DHCP server supplies automatic configuration parameters, such as dynamic IP address, to requesting systems. A dedicated server located in a secured management zone should be used to provide DHCP services instead. Attackers can potentially be used for denial-of-service (DoS) attacks.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting network protocols and explicitly require disabling all insecure or unnecessary protocols such as the Dynamic Host Configuration Protocol (DHCP).", "compliance.rule.default.value": "Enabled by default, but also requires a DHCP pool to be set to activate the DHCP server.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/ipaddr/command/ipaddr-r1.html#GUID-1516B259-AA28-4839-B968-8DDBF0B382F6", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000041}, {"compliance.rule.name": "Set 'no ip identd'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": ".*identd.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the ident server.\n```\nhostname(config)#no ip identd\n```", "compliance.rule.description": "Disable the identification (identd) server.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Identification protocol enables identifying a user's transmission control protocol (TCP) session. This information disclosure could potentially provide an attacker with information about users.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting network protocols and explicitly require disabling all insecure or unnecessary protocols such as the identification protocol (identd).", "compliance.rule.default.value": "Disabled by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/solutions/Enterprise/Security/Baseline_Security/sec_chap4.html#wp1056539", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000042}, {"compliance.rule.name": "Set 'service tcp-keepalives-in'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*service tcp-keepalives-in.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Enable TCP keepalives-in service:\n```\n\nhostname(config)#service tcp-keepalives-in\n```", "compliance.rule.description": "Generate keepalive packets on idle incoming network connections.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Stale connections use resources and could potentially be hijacked to gain illegitimate access. The TCP keepalives-in service generates keepalive packets on idle incoming network connections (initiated by remote host). This service allows the device to detect when the remote host fails and drop the session. If enabled, keepalives are sent once per minute on idle connections. The connection is closed within five minutes if no keepalives are received or immediately if the host replies with a reset packet.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting how long to allow terminated sessions and enforce this policy through the use of 'tcp-keepalives-in' command.", "compliance.rule.default.value": "Disabled by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/R_through_setup.html#GUID-1489ABA3-2428-4A64-B252-296A035DB85E", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000043}, {"compliance.rule.name": "Set 'service tcp-keepalives-out'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*service tcp-keepalives-out.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Enable TCP keepalives-out service:\n```\n\nhostname(config)#service tcp-keepalives-out\n```", "compliance.rule.description": "Generate keepalive packets on idle outgoing network connections.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Stale connections use resources and could potentially be hijacked to gain illegitimate access. The TCP keepalives-in service generates keepalive packets on idle incoming network connections (initiated by remote host). This service allows the device to detect when the remote host fails and drop the session. If enabled, keepalives are sent once per minute on idle connections. The closes connection is closed within five minutes if no keepalives are received or immediately if the host replies with a reset packet.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting how long to allow terminated sessions and enforce this policy through the use of 'tcp-keepalives-out' command.", "compliance.rule.default.value": "Disabled by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/R_through_setup.html#GUID-9321ECDC-6284-4BF6-BA4A-9CEEF5F993E5", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000044}, {"compliance.rule.name": "Set 'no service pad'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*no service pad.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable the PAD service.\n```\n\nhostname(config)#no service pad\n```", "compliance.rule.description": "Disable X.25 Packet Assembler/Disassembler (PAD) service.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "If the PAD service is not necessary, disable the service to prevent intruders from accessing the X.25 PAD command set on the router.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy restricting unnecessary services such as the 'PAD' service.", "compliance.rule.default.value": "Enabled by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/wan/command/wan-s1.html#GUID-C5497B77-3FD4-4D2F-AB08-1317D5F5473B", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000045}, {"compliance.rule.name": "Set version 2 for 'ip ssh version'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip ssh version 2", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the router to use SSH version 2\n```\n\nhostname(config)#ip ssh version 2\n```", "compliance.rule.description": "Specify the version of Secure Shell (SSH) to be run on a router", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "SSH Version 1 has been subject to a number of serious vulnerabilities and is no longer considered to be a secure protocol, resulting in the adoption of SSH Version 2 as an Internet Standard in 2006.\n\nCisco routers support both versions, but due to the weakness of SSH Version 1 only the later standard should be used.", "compliance.rule.impact": "To reduce the risk of unauthorized access, organizations should implement a security policy to review their current protocols to ensure the most secure protocol versions are in use.", "compliance.rule.default.value": "SSH is not enabled by default. When enabled, SSH operates in compatibility mode (versions 1 and 2 supported).", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i3.html#GUID-170AECF1-4B5B-462A-8CC8-999DEDC45C21", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000046}, {"compliance.rule.name": "Set the 'hostname'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*hostname\\s*\\S+", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure an appropriate host name for the router.\n```\n\nhostname(config)#hostname {<em>router_name</em>}\n```", "compliance.rule.description": "The hostname is used in prompts and default configuration filenames.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The domain name is prerequisite for setting up SSH.", "compliance.rule.impact": "Organizations should plan the enterprise network and identify an appropriate host name for each router.", "compliance.rule.default.value": "The default hostname is Router.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/F_through_K.html#GUID-F3349988-EC16-484A-BE81-4C40110E6625", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000047}, {"compliance.rule.name": "Set the 'ip domain-name'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*ip\\s+domain(-|\\s+)name\\s+\\S+", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure an appropriate domain name for the router.\n```\n\nhostname (config)#ip domain-name {<em>domain-name</em>}\n```", "compliance.rule.description": "Define a default domain name that the Cisco IOS software uses to complete unqualified hostnames", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The domain name is a prerequisite for setting up SSH.", "compliance.rule.impact": "Organizations should plan the enterprise network and identify an appropriate domain name for the router.", "compliance.rule.default.value": "No domain is set.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/ipaddr/command/ipaddr-i3.html#GUID-A706D62B-9170-45CE-A2C2-7B2052BE2CAB", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000048}, {"compliance.rule.name": "Set 'seconds' for 'ip ssh timeout' for 60 seconds or less", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip ssh time-out\\s(60|[1-5]?[0-9])", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the SSH timeout\n```\n\nhostname(config)#ip ssh time-out [<em>60</em>] \n```", "compliance.rule.description": "The time interval that the router waits for the SSH client to respond before disconnecting an uncompleted login attempt.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This reduces the risk of an administrator leaving an authenticated session logged in for an extended period of time.", "compliance.rule.impact": "Organizations should implement a security policy requiring minimum timeout settings for all network administrators and enforce the policy through the 'ip ssh timeout' command.", "compliance.rule.default.value": "SSH in not enabled by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i3.html#GUID-5BAC7A2B-0A25-400F-AEE9-C22AE08513C6", "compliance.rule.additional.information": "This cannot exceed 120 seconds.", "compliance.rule.controls": [{"compliance.rule.control.version": "4.3", "compliance.rule.control.name": "Configure Automatic Session Locking on Enterprise Assets", "compliance.rule.control.description": "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "16.11", "compliance.rule.control.name": "Lock Workstation Sessions After Inactivity", "compliance.rule.control.description": "Automatically lock workstation sessions after a standard period of inactivity.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}], "id": 10000000000049}, {"compliance.rule.name": "Set maximum value for 'ip ssh authentication-retries'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip ssh authentication-retries", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the SSH timeout: 3 or less\n```\n\nhostname(config)#ip ssh authentication-retries [<em>3</em>]\n```", "compliance.rule.description": "The number of retries before the SSH login session disconnects.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This limits the number of times an unauthorized user can attempt a password without having to establish a new SSH login attempt. This reduces the potential for success during online brute force attacks by limiting the number of login attempts per SSH connection.", "compliance.rule.impact": "Organizations should implement a security policy limiting the number of authentication attempts for network administrators and enforce the policy through the 'ip ssh authentication-retries' command.", "compliance.rule.default.value": "SSH is not enabled by default. When set, the default value is 3.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i3.html#GUID-5BAC7A2B-0A25-400F-AEE9-C22AE08513C6", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000050}, {"compliance.rule.name": "Set 'logging enable'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*logging host .*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Enable system logging.\n```\n\nhostname(config)#archive\nhostname(config-archive)#log config\nhostname(config-archive-log-cfg)#logging enable\nhostname(config-archive-log-cfg)#end\n```", "compliance.rule.description": "Enable logging of system messages.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Logging provides a chronological record of activities on the Cisco device and allows monitoring of both operational and security related events.", "compliance.rule.impact": "Enabling the Cisco IOS 'logging enable' command enforces the monitoring of technology risks for the organizations' network devices.", "compliance.rule.default.value": "Logging is not enabled/", "compliance.rule.references": "https://community.cisco.com/t5/networking-knowledge-base/how-to-configure-logging-in-cisco-ios/ta-p/3132434", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000051}, {"compliance.rule.name": "Set 'buffer size' for 'logging buffered'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*logging buffered \\d+.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure buffered logging (with minimum size). Recommended size is 64000.\n```\n\nhostname(config)#logging buffered [<em>log_buffer_size</em>]\n```", "compliance.rule.description": "Enable system message logging to a local buffer.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "The device can copy and store log messages to an internal memory buffer. The buffered data is available only from a router exec or enabled exec session. This form of logging is useful for debugging and monitoring when logged in to a router.", "compliance.rule.impact": "Data forensics is effective for managing technology risks and an organization can enforce such policies by enabling the 'logging buffered' command.", "compliance.rule.default.value": "No logging buffer is set by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/netmgmt/command/reference/nm_09.html#wp1060051", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000052}, {"compliance.rule.name": "Set 'logging console critical'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*logging console critical.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure console logging level.\n```\n\nhostname(config)#logging console critical\n```", "compliance.rule.description": "Verify logging to device console is enabled and limited to a rational severity level to avoid impacting system performance and management.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This configuration determines the severity of messages that will generate console messages. Logging to console should be limited only to those messages required for immediate troubleshooting while logged into the device. This form of logging is not persistent; messages printed to the console are not stored by the router. Console logging is handy for operators when they use the console.", "compliance.rule.impact": "Logging critical messages at the console is important for an organization managing technology risk. The 'logging console' command should capture appropriate severity messages to be effective.", "compliance.rule.default.value": "Tthe default is to log all messages", "compliance.rule.references": "", "compliance.rule.additional.information": "The console is a slow display device. In message storms some logging messages may be silently dropped when the console queue becomes full. Set severity levels accordingly.", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000053}, {"compliance.rule.name": "Set IP address for 'logging host'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "sh running-config | inc logging host", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "logging host ((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Designate one or more syslog servers by IP address.\n```\n\nhostname(config)#logging host {syslog_server}\n```", "compliance.rule.description": "Log system messages and debug output to a remote host.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Cisco routers can send their log messages to a Unix-style Syslog service. A syslog service simply accepts messages and stores them in files or prints them according to a simple configuration file. This form of logging is best because it can provide protected long-term storage for logs (the devices internal logging buffer has limited capacity to store events.) In addition, logging to an external system is highly recommended or required by most security standards. If desired or required by policy, law and/or regulation, enable a second syslog server for redundancy.", "compliance.rule.impact": "Logging is an important process for an organization managing technology risk. The 'logging host' command sets the IP address of the logging host and enforces the logging process.", "compliance.rule.default.value": "System logging messages are not sent to any remote host.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/netmgmt/command/reference/nm_09.html#wp1082864", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "13.1", "compliance.rule.control.name": "Centralize Security Event Alerting", "compliance.rule.control.description": "Centralize security event alerting across enterprise assets for log correlation and analysis. Best practice implementation requires the use of a SIEM, which includes vendor-defined event correlation alerts. A log analytics platform configured with security-relevant correlation alerts also satisfies this Safeguard.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.6", "compliance.rule.control.name": "Tune Security Event Alerting Thresholds", "compliance.rule.control.description": "Tune security event alerting thresholds monthly, or more frequently.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000054}, {"compliance.rule.name": "Set 'logging trap informational'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "sh log | incl Trap logging", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+Trap logging: level informational, 14288 message lines logged", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure SNMP trap and syslog logging level.\n```\n\nhostname(config)#logging trap informational\n```", "compliance.rule.description": "Limit messages logged to the syslog servers based on severity level informational.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This determines the severity of messages that will generate simple network management protocol (SNMP) trap and or syslog messages. This setting should be set to either \"debugging\" (7) or \"informational\" (6), but no lower.", "compliance.rule.impact": "Logging is an important process for an organization managing technology risk. The 'logging trap' command sets the severity of messages and enforces the logging process.", "compliance.rule.default.value": "Disabled", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/netmgmt/command/reference/nm_09.html#wp1015177", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000055}, {"compliance.rule.name": "Set 'service timestamps debug datetime'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "^service timestamps debug datetime.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure debug messages to include timestamps.\n```\n\nhostname(config)#service timestamps debug datetime {<em>msec</em>} show-timezone\n```", "compliance.rule.description": "Configure the system to apply a time stamp to debugging messages or system logging messages", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Including timestamps in log messages allows correlating events and tracing network attacks across multiple devices. Enabling service timestamp to mark the time log messages were generated simplifies obtaining a holistic view of events enabling faster troubleshooting of issues or attacks.", "compliance.rule.impact": "Logging is an important process for an organization managing technology risk and establishing a timeline of events is critical. The 'service timestamps' command sets the date and time on entries sent to the logging host and enforces the logging process.", "compliance.rule.default.value": "Time stamps are applied to debug and logging messages.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/R_through_setup.html#GUID-DC110E59-D294-4E3D-B67F-CCB06E607FC6", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000056}, {"compliance.rule.name": "Set 'logging source interface'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "^logging source.*$", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Bind logging to the loopback interface.\n```\n\nhostname(config)#logging source-interface loopback {<em>loopback_interface_number</em>}\n```", "compliance.rule.description": "Specify the source IPv4 or IPv6 address of system logging packets", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is required so that the router sends log messages to the logging server from a consistent IP address.", "compliance.rule.impact": "Logging is an important process for an organization managing technology risk and establishing a consistent source of messages for the logging host is critical. The 'logging source interface loopback' command sets a consistent IP address to send messages to the logging host and enforces the logging process.", "compliance.rule.default.value": "The wildcard interface address is used.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios/netmgmt/command/reference/nm_09.html#wp1095099", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000057}, {"compliance.rule.name": "Set 'login success/failure logging'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*login on-failure log.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "```\nhostname(config)#login on-failure log\nhostname(config)#login on-success log\nhostname(config)#end\n```", "compliance.rule.description": "Without generating audit records that are specific to the security and mission needs of the organization, it would be difficult to establish, correlate, and investigate the events relating to an incident or identify those responsible for one.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Audit records can be generated from various components within the information system (e.g., module or policy filter).", "compliance.rule.impact": "", "compliance.rule.default.value": "", "compliance.rule.references": "https://www.cisco.com/c/en/us/td/docs/ios-xml/ios/config-mgmt/configuration/xe-16-6/config-mgmt-xe-16-6-book/cm-config-logger.pdf", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.5", "compliance.rule.control.name": "Collect Detailed Audit Logs", "compliance.rule.control.description": "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.3", "compliance.rule.control.name": "Enable Detailed Logging", "compliance.rule.control.description": "Enable system logging to include detailed information such as an event source, date, user, timestamp, source addresses, destination addresses, and other useful elements.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000058}, {"compliance.rule.name": "Set 'ip address' for 'ntp server'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "sh ntp associations", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure at least one external NTP Server using the following commands\n```\n\nhostname(config)#ntp server {ntp-server_ip_address}\nor \nhostname(config)#ntp server {ntp server vrf [vrf name] ip address}\n```", "compliance.rule.description": "Use this command if you want to allow the system to synchronize the system software clock with the specified NTP server.", "compliance.rule.severity": "CRITICAL", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "To ensure that the time on your Cisco router is consistent with other devices in your network, at least two (and preferably at least three) NTP Server/s external to the router should be configured.\n\nEnsure you also configure consistent timezone and daylight savings time setting for all devices. For simplicity, the default of Coordinated Universal Time (UTC).", "compliance.rule.impact": "Organizations should establish multiple Network Time Protocol (NTP) hosts to set consistent time across the enterprise. Enabling the 'ntp server ip address' enforces encrypted authentication between NTP hosts.", "compliance.rule.default.value": "No servers are configured by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/bsm/command/bsm-cr-n1.html#GUID-255145EB-D656-43F0-B361-D9CBCC794112:https://www.cisco.com/c/en/us/td/docs/ios-xml/ios/bsm/command/bsm-cr-book/bsm-cr-n1.html#wp3294676008", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000059}, {"compliance.rule.name": "Set 'ntp authenticate'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ntp", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure NTP authentication:\n```\n\nhostname(config)#ntp authenticate\n```", "compliance.rule.description": "Enable NTP authentication.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using authenticated NTP ensures the Cisco device only permits time updates from authorized NTP servers.", "compliance.rule.impact": "Organizations should establish three Network Time Protocol (NTP) hosts to set consistent time across the enterprise. Enabling the 'ntp authenticate' command enforces authentication between NTP hosts.", "compliance.rule.default.value": "NTP authentication is not enabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/bsm/command/bsm-cr-n1.html#GUID-8BEBDAF4-6D03-4C3E-B8D6-6BCBC7D0F324", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000060}, {"compliance.rule.name": "Set 'ntp authentication-key'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ntp authentication-key", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure at the NTP key ring and encryption key using the following command\n```\n\nhostname(config)#ntp authentication-key {ntp_key_id} md5 {ntp_key_hash}\n```", "compliance.rule.description": "Define an authentication key for Network Time Protocol (NTP).", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Using an authentication key provides a higher degree of security as only authenticated NTP servers will be able to update time for the Cisco device.", "compliance.rule.impact": "Organizations should establish three Network Time Protocol (NTP) hosts to set consistent time across the enterprise. Enabling the 'ntp authentication-key' command enforces encrypted authentication between NTP hosts.", "compliance.rule.default.value": "No authentication key is defined for NTP.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/bsm/command/bsm-cr-n1.html#GUID-0435BFD1-D7D7-41D4-97AC-7731C11226BC", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000061}, {"compliance.rule.name": "Set the 'ntp trusted-key'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ntp trusted-key", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the NTP trusted key using the following command\n```\n\nhostname(config)#ntp trusted-key {ntp_key_id}\n```", "compliance.rule.description": "Ensure you authenticate the identity of a system to which Network Time Protocol (NTP) will synchronize", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This authentication function provides protection against accidentally synchronizing the system to another system that is not trusted, because the other system must know the correct authentication key.", "compliance.rule.impact": "Organizations should establish three Network Time Protocol (NTP) hosts to set consistent time across the enterprise. Enabling the 'ntp trusted-key' command enforces encrypted authentication between NTP hosts.", "compliance.rule.default.value": "Authentication of the identity of the system is disabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/bsm/command/bsm-cr-n1.html#GUID-89CA798D-0F12-4AE8-B382-DE10CBD261DB", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000062}, {"compliance.rule.name": "Set 'key' for each 'ntp server'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ntp server", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure each NTP Server to use a key ring using the following command.\n```\n\nhostname(config)#ntp server {<em>ntp-server_ip_address</em>}{key <em>ntp_key_id</em>} \n```", "compliance.rule.description": "Specifies the authentication key for NTP.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This authentication feature provides protection against accidentally synchronizing the ntp system to another system that is not trusted, because the other system must know the correct authentication key.", "compliance.rule.impact": "Organizations should establish three Network Time Protocol (NTP) hosts to set consistent time across the enterprise. Enabling the 'ntp server key' command enforces encrypted authentication between NTP hosts.", "compliance.rule.default.value": "No NTP key is set by default", "compliance.rule.references": "", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000063}, {"compliance.rule.name": "Create a single 'interface loopback'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "interface Loopback", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Define and configure one loopback interface.\n```\n\nhostname(config)#interface loopback <<em>number</em>>\nhostname(config-if)#ip address <<em>loopback_ip_address</em>> <<em>loopback_subnet_mask</em>> \n```", "compliance.rule.description": "Configure a single loopback interface.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Software-only loopback interface that emulates an interface that is always up. It is a virtual interface supported on all platforms.\n\nAlternate loopback addresses create a potential for abuse, mis-configuration, and inconsistencies. Additional loopback interfaces must be documented and approved prior to use by local security personnel.", "compliance.rule.impact": "Organizations should plan and establish 'loopback interfaces' for the enterprise network. Loopback interfaces enable critical network information such as OSPF Router IDs and provide termination points for routing protocol sessions.", "compliance.rule.default.value": "There are no loopback interfaces defined by default.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set AAA 'source-interface'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*(radius|tacacs) source-interface.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Bind AAA services to the loopback interface.\n```\n\nHostname(config)#ip radius source-interface loopback {loopback_interface_number}\nor\nHostname(config)#aaa group server tacacs+ {group_name} hostname(config-sg-tacacs+)#ip tacacs source-interface {loopback_interface_number}\n```", "compliance.rule.description": "Force AAA to use the IP address of a specified interface for all outgoing AAA packets", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is required so that the AAA server (RADIUS or TACACS+) can easily identify routers and authenticate requests by their IP address.", "compliance.rule.impact": "Organizations should design and implement authentication, authorization, and accounting (AAA) services for effective monitoring of enterprise network devices. Binding AAA services to the source-interface loopback enables these services.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i2.html#GUID-22E8B211-751F-48E0-9C76-58F0FE0AABA8:http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i3.html#GUID-54A00318-CF69-46FC-9ADC-313BFC436713", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "5.6", "compliance.rule.control.name": "Centralize Account Management", "compliance.rule.control.description": "Centralize account management through a directory or identity service.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "16.2", "compliance.rule.control.name": "Configure Centralized Point of Authentication", "compliance.rule.control.description": "Configure access for all accounts through as few centralized points of authentication as possible, including network, security, and cloud systems.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'ntp source' to Loopback Interface", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*ntp source.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Bind the NTP service to the loopback interface.\n```\n\nhostname(config)#ntp source loopback {<em>loopback_interface_number}</em>\n```", "compliance.rule.description": "Use a particular source address in Network Time Protocol (NTP) packets.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Set the source address to be used when sending NTP traffic. This may be required if the NTP servers you peer with filter based on IP address.", "compliance.rule.impact": "Organizations should plan and implement network time protocol (NTP) services to establish official time for all enterprise network devices. Setting 'ntp source loopback' enforces the proper IP address for NTP services.", "compliance.rule.default.value": "Source address is determined by the outgoing interface.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/bsm/command/bsm-cr-n1.html#GUID-DF29FBFB-E1C0-4E5C-9013-D4CE59CA0B88", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "8.4", "compliance.rule.control.name": "Standardize Time Synchronization", "compliance.rule.control.description": "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported.", "compliance.rule.control.ig": ["ig2", "ig3"]}, {"compliance.rule.control.version": "6.1", "compliance.rule.control.name": "Utilize Three Synchronized Time Sources", "compliance.rule.control.description": "Use at least three synchronized time sources from which all servers and network devices retrieve time information on a regular basis so that timestamps in logs are consistent.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000066}, {"compliance.rule.name": "Set 'ip tftp source-interface' to the Loopback Interface", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*tftp source-interface.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Bind the TFTP client to the loopback interface.\n```\n\nhostname(config)#ip tftp source-interface loopback {<em>loobpback_interface_number</em>}\n```", "compliance.rule.description": "Specify the IP address of an interface as the source address for TFTP connections.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is required so that the TFTP servers can easily identify routers and authenticate requests by their IP address.", "compliance.rule.impact": "Organizations should plan and implement trivial file transfer protocol (TFTP) services in the enterprise by setting 'tftp source-interface loopback', which enables the TFTP servers to identify routers and authenticate requests by IP address.", "compliance.rule.default.value": "The address of the closest interface to the destination is selected as the source address.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/fundamentals/command/F_through_K.html#GUID-9AA27050-A578-47CD-9F1D-5A8E2B449209", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'no ip source-route'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s*no ip source-route.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable source routing.\n```\n\nhostname(config)#no ip source-route\n```", "compliance.rule.description": "Disable the handling of IP datagrams with source routing header options.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Source routing is a feature of IP whereby individual packets can specify routes. This feature is used in several kinds of attacks. Cisco routers normally accept and process source routes. Unless a network depends on source routing, it should be disabled.", "compliance.rule.impact": "Organizations should plan and implement network policies to ensure unnecessary services are explicitly disabled. The 'ip source-route' feature has been used in several attacks and should be disabled.", "compliance.rule.default.value": "Enabled by default", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/ipaddr/command/ipaddr-i4.html#GUID-C7F971DD-358F-4B43-9F3E-244F5D4A3A93", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000068}, {"compliance.rule.name": "Set 'no ip proxy-arp'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+no ip proxy-arp", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Disable proxy ARP on all interfaces.\n```\n\nhostname(config)#interface {interface}\nhostname(config-if)#no ip proxy-arp\n\n```", "compliance.rule.description": "Disable proxy ARP on all interfaces.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Address Resolution Protocol (ARP) provides resolution between IP and MAC Addresses (or other Network and Link Layer addresses on none IP networks) within a Layer 2 network.\n\nProxy ARP is a service where a device connected to one network (in this case the Cisco router) answers ARP Requests which are addressed to a host on another network, replying with its own MAC Address and forwarding the traffic on to the intended host.\n\nSometimes used for extending broadcast domains across WAN links, in most cases Proxy ARP on enterprise networks is used to enable communication for hosts with mis-configured subnet masks, a situation which should no longer be a common problem. Proxy ARP effectively breaks the LAN Security Perimeter, extending a network across multiple Layer 2 segments. Using Proxy ARP can also allow other security controls such as PVLAN to be bypassed.", "compliance.rule.impact": "Organizations should plan and implement network policies to ensure unnecessary services are explicitly disabled. The 'ip proxy-arp' feature effectively breaks the LAN security perimeter and should be disabled.", "compliance.rule.default.value": "Enabled", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/ipaddr/command/ipaddr-i4.html#GUID-AEB7DDCB-7B3D-4036-ACF0-0A0250F3002E", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000069}, {"compliance.rule.name": "Set 'no interface tunnel'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "not contain", "result.pattern": "interface Tunnel", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Remove any tunnel interfaces.\n```\n\nhostname(config)#no interface tunnel {<em>instance</em>}\n```", "compliance.rule.description": "Verify no tunnel interfaces are defined.", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Tunnel interfaces should not exist in general. They can be used for malicious purposes. If they are necessary, the network admin's should be well aware of them and their purpose.", "compliance.rule.impact": "Organizations should plan and implement enterprise network security policies that disable insecure and unnecessary features that increase attack surfaces such as 'tunnel interfaces'.", "compliance.rule.default.value": "No tunnel interfaces are defined", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000070}, {"compliance.rule.name": "Set 'ip verify unicast source reachable-via'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.type": "cli", "compliance.rule.command": "sh ip int | incl verify source", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+IP verify source reachable-via RX, allow default", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure uRPF.\n```\n\nhostname(config)#interface {<em>interface_name</em>}\nhostname(config-if)#ip verify unicast source reachable-via rx allow-default\n```", "compliance.rule.description": "Examines incoming packets to determine whether the source address is in the Forwarding Information Base (FIB) and permits the packet only if the source is reachable through the interface on which the packet was received (sometimes referred to as strict mode).", "compliance.rule.severity": "HIGH", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Enabled uRPF helps mitigate IP spoofing by ensuring only packet source IP addresses only originate from expected interfaces. Configure unicast reverse-path forwarding (uRPF) on all external or high risk interfaces.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that protect the confidentiality, integrity, and availability of network devices. The 'unicast Reverse-Path Forwarding' (uRPF) feature dynamically uses the router table to either accept or drop packets when arriving on an interface.", "compliance.rule.default.value": "Unicast <PERSON><PERSON><PERSON> is disabled.", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i3.html#GUID-2ED313DB-3D3F-49D7-880A-************:https://community.cisco.com/t5/routing/ip-verify-unicast-source-reachable-via-rx/td-p/1710172", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": **************}, {"compliance.rule.name": "Set 'ip access-list extended' to Forbid Private Source Addresses from External Networks", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": ".*ip access-list \\S+.*", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure ACL for private source address restrictions from external networks.\n```\n\nhostname(config)#ip access-list extended {<span><em>name | number</em>} \n</span><span>hostname(config-nacl)#deny ip {</span><em>internal_networks</em>} any log\nhostname(config<span>-nacl</span>)#deny ip ********* ************* any log\nhostname(config<span>-nacl</span>)#deny ip 10.0.0.0 ************* any log\nhostname(config<span>-nacl</span>)#deny ip 0.0.0.0 ************* any log\nhostname(config<span>-nacl</span>)#deny ip ********** ************ any log\nhostname(config<span>-nacl</span>)#deny ip *********** *********** any log\nhostname(config<span>-nacl</span>)#deny ip ********* ********* any log\nhostname(config<span>-nacl</span>)#deny ip *********** *********** any log\nhostname(config<span>-nacl</span>)#deny ip ********* ************** any log\nhostname(config<span>-nacl</span>)#deny ip host *************** any log\nhostname(config<span>-nacl</span>)#permit {protocol} {source_ip} {source_mask} {destination} {destination_mask} log\nhostname(config<span>-nacl</span>)#deny any any log\nhostname(config)#interface <external_<em>interface</em>>\nhostname(config-if)#access-group <<em>access-list</em>> in\n\n```", "compliance.rule.description": "This command places the router in access-list configuration mode, where you must define the denied or permitted access conditions by using the deny and permit commands.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Configuring access controls can help prevent spoofing attacks. To reduce the effectiveness of IP spoofing, configure access control to deny any traffic from the external network that has a source address that should reside on the internal network. Include local host address or any reserved private addresses (RFC 1918).\n\nEnsure the `permit `rule(s) above the final `deny `rule only allow traffic according to your organization's least privilege policy.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that explicitly separate internal from external networks. Adding 'ip access-list' explicitly permitting and denying internal and external networks enforces these policies.", "compliance.rule.default.value": "No access list defined", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i1.html#GUID-BD76E065-8EAC-4B32-AF25-04BA94DD2B11", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000072}, {"compliance.rule.name": "Set inbound 'ip access-group' on the External Interface", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "ip access-group\\s*\\d+\\s*in", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "interface", "compliance.rule.block.end": "!"}}, "compliance.rule.remediation": "Apply the access-group for the external (untrusted) interface\n```\n\nhostname(config)#interface {external_interface}\nhostname(config-if)#ip access-group {name | number} in\n```", "compliance.rule.description": "This command places the router in access-list configuration mode, where you must define the denied or permitted access conditions by using the deny and permit commands.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Configuring access controls can help prevent spoofing attacks. To reduce the effectiveness of IP spoofing, configure access control to deny any traffic from the external network that has a source address that should reside on the internal network. Include local host address or any reserved private addresses (RFC 1918).\n\nEnsure the `permit `rule(s) above the final `deny `rule only allow traffic according to your organization's least privilege policy.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies explicitly permitting and denying access based upon access lists. Using the 'ip access-group' command enforces these policies by explicitly identifying groups permitted access.", "compliance.rule.default.value": "No access-group defined", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF:http://www.cisco.com/en/US/docs/ios-xml/ios/security/d1/sec-cr-i1.html#GUID-D9FE7E44-7831-4C64-ACB8-840811A0C993", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000073}, {"compliance.rule.name": "Set 'key chain'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "key chain [A-Za-z0-9]+", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "key chain", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Establish the key chain.\n```\n\nhostname(config)#key chain {<em>key-chain_name</em>}\n```", "compliance.rule.description": "Define an authentication key chain to enable authentication for routing protocols. A key chain must have at least one key and can have up to 2,147,483,647 keys.\n\nNOTE: Only DRP Agent, EIGRP, and RIPv2 use key chains.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Routing protocols such as DRP Agent, EIGRP, and RIPv2 use key chains for authentication.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using 'key chains' for routing protocols enforces these policies.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_pi/command/iri-cr-a1.html#GUID-A62E89F5-0B8B-4CF0-B4EB-08F2762D88BB", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000074}, {"compliance.rule.name": "Set 'key'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+key\\s+\\d", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "key chain", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure the key number.\n```\n\nhostname(config-keychain)#key {<em>key-number</em>}\n```", "compliance.rule.description": "Configure an authentication key on a key chain.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the routing authentication setup", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using 'key numbers' for key chains for routing protocols enforces these policies.", "compliance.rule.default.value": "", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_pi/command/iri-cr-a1.html#GUID-3F31B2E0-0E4B-4F49-A4A8-8ADA1CA0D73F", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000075}, {"compliance.rule.name": "Set 'key-string'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "/\\s+key-string\\s+.+", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "key chain", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure the key string.\n```\n\nhostname(config-keychain-key)#key-string <<em>key-string</em>> \n```", "compliance.rule.description": "Configure the authentication string for a key.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the routing authentication setup", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using 'key strings' for key chains for routing protocols enforces these policies.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_pi/command/iri-cr-a1.html#GUID-D7A8DC18-2E16-4EA5-8762-8B68B94CC43E", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000076}, {"compliance.rule.name": "Set 'address-family ipv4 autonomous-system'", "compliance.rule.category": "Network", "compliance.rule.block.criteria": {"compliance.rule.block.start": "router eigrp", "compliance.rule.block.end": "end"}, "compliance.rule.context": {"compliance.rule.check.category": "advance", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+address-family", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the EIGRP address family.\n```\n\nhostname(config)#router eigrp <<em>virtual-instance-name</em>>\nhostname(config-router)#address-family ipv4 autonomous-system {<em>eigrp_as-number</em>}\n```", "compliance.rule.description": "Configure the EIGRP address family.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Rationale: EIGRP is a true multi-protocol routing protocol and the 'address-family' feature enables restriction of exchanges with specific neighbors", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using 'address-family' for EIGRP enforces these policies by restricting the exchanges between predefined network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-67388D6C-AE9C-47CA-8C35-2A2CF9FA668E:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-C03CFC8A-3CE3-4CF9-9D65-52990DBD3377", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000077}, {"compliance.rule.name": "Set 'af-interface default'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+af-interface default", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "router eigrp", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure the EIGRP address family.\n```\n\nhostname(config)#router eigrp <<em>virtual-instance-name</em>>\nhostname(config-router)#address-family ipv4 autonomous-system {<em>eigrp_as-number</em>}\nhostname(config-router-af)#af-interface default \n```", "compliance.rule.description": "Defines user defaults to apply to EIGRP interfaces that belong to an address-family.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Part of the EIGRP address-family setup", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using 'af-interface default' for EIGRP interfaces enforces these policies by restricting the exchanges between predefined network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-67388D6C-AE9C-47CA-8C35-2A2CF9FA668E:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-C03CFC8A-3CE3-4CF9-9D65-52990DBD3377:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-DC0EF1D3-DFD4-45DF-A553-FA432A3E7233", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000078}, {"compliance.rule.name": "Set 'authentication key-chain'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+authentication key-chain [A-Za-z0-9]+", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "router eigrp", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure the EIGRP address family key chain.\n```\n\nhostname(config)#router eigrp <virtual-instance-name>\nhostname(config-router)#address-family ipv4 autonomous-system {eigrp_as-number}\nhostname(config-router-af)#af-interface {interface-name}\nhostname(config-router-af-interface)#authentication key-chain {eigrp_key-chain_name}\n```", "compliance.rule.description": "Configure the EIGRP address family key chain.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the EIGRP authentication configuration", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using the address-family 'key chain' for EIGRP enforces these policies by restricting the exchanges between predefined network devices.", "compliance.rule.default.value": "No key chains are specified for EIGRP", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-67388D6C-AE9C-47CA-8C35-2A2CF9FA668E:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-C03CFC8A-3CE3-4CF9-9D65-52990DBD3377:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-6B6ED6A3-1AAA-4EFA-B6B8-9BF11EEC37A0", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000079}, {"compliance.rule.name": "Set 'authentication mode md5'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+authentication mode md5", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "router eigrp", "compliance.rule.block.end": "end"}}, "compliance.rule.remediation": "Configure the EIGRP address family authentication mode.\n```\n\nhostname(config)#router eigrp <virtual-instance-name>\nhostname(config-router)#address-family ipv4 autonomous-system {eigrp_as-number}\nhostname(config-router-af)#af-interface {interface-name}\nhostname(config-router-af-interface)#authentication mode md5\n```", "compliance.rule.description": "Configure authentication to prevent unapproved sources from introducing unauthorized or false service messages.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the EIGRP authentication configuration", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using the 'authentication mode' for EIGRP address-family or service-family packets enforces these policies by restricting the type of authentication between network devices.", "compliance.rule.default.value": "Not defined", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-67388D6C-AE9C-47CA-8C35-2A2CF9FA668E:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-C03CFC8A-3CE3-4CF9-9D65-52990DBD3377:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-a1.html#GUID-A29E0EF6-4CEF-40A7-9824-367939001B73", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000080}, {"compliance.rule.name": "Set 'ip authentication key-chain eigrp'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\sip authentication key-chain eigrp [0-9]+ [A-Za-z0-9]+", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "interface", "compliance.rule.block.end": "!"}}, "compliance.rule.remediation": "Configure the interface with the EIGRP key chain.\n```\n\nhostname(config)#interface {<em>interface_name</em>}\nhostname(config-if)#ip authentication key-chain eigrp {<em>eigrp_as-number</em>} {<em>eigrp_key-chain_name</em>}\n```", "compliance.rule.description": "Specify the type of authentication used in Enhanced Interior Gateway Routing Protocol (EIGRP) packets per interface.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Configuring EIGRP authentication key-chain number and name to restrict packet exchanges between network devices.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Configuring the interface with 'ip authentication key chain' for EIGRP by name and number enforces these policies by restricting the exchanges between network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-0B344B46-5E8E-4FE2-A3E0-D92410CE5E91", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000081}, {"compliance.rule.name": "Set 'ip authentication mode eigrp'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+ip authentication mode eigrp [0-9]+ md5", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "interface", "compliance.rule.block.end": "!"}}, "compliance.rule.remediation": "Configure the interface with the EIGRP authentication mode.\n```\n\nhostname(config)#interface {<em>interface_name</em>}\nhostname(config-if)#ip authentication mode eigrp {<em><span>eigrp_as-number</span></em><span>}</span> md5\n```", "compliance.rule.description": "Configure authentication to prevent unapproved sources from introducing unauthorized or false routing messages.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the EIGRP authentication configuration", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Configuring the interface with 'ip authentication mode' for EIGRP by number and mode enforces these policies by restricting the exchanges between network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_eigrp/command/ire-i1.html#GUID-8D1B0697-8E96-4D8A-BD20-536956D68506", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "4.2", "compliance.rule.control.name": "Establish and Maintain a Secure Configuration Process for Network Infrastructure", "compliance.rule.control.description": "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard.", "compliance.rule.control.ig": ["ig1", "ig2", "ig3"]}, {"compliance.rule.control.version": "9.2", "compliance.rule.control.name": "Ensure Only Approved Ports, Protocols and Services Are Running", "compliance.rule.control.description": "Ensure that only network ports, protocols, and services listening on a system with validated business needs, are running on each system.", "compliance.rule.control.ig": ["ig2", "ig3"]}], "id": 10000000000082}, {"compliance.rule.name": "Set 'authentication message-digest' for OSPF area", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "area\\s+\\d+\\s+authentication\\s+message-digest", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure the Message Digest option for OSPF.\n```\n\nhostname(config)#router ospf <<em>ospf_process-id</em>>\nhostname(config-router)#area <<em>ospf_area-id</em>> authentication message-digest \n```", "compliance.rule.description": "Enable MD5 authentication for OSPF.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the OSPF authentication setup.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Configuring the area 'authentication message-digest' for OSPF enforces these policies by restricting exchanges between network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_ospf/command/ospf-i1.html#GUID-3D5781A3-F8DF-4760-A551-6A3AB80A42ED:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_ospf/command/ospf-a1.html#GUID-81D0F753-D8D5-494E-9A10-B15433CFD445", "compliance.rule.additional.information": "The authentication type must be the same for all routers and access servers in an area. The authentication password for all OSPF routers on a network must be the same if they are to communicate with each other via OSPF", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000083}, {"compliance.rule.name": "Set 'ip ospf message-digest-key md5'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "advanced", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "\\s+ip ospf message-digest-key \\d .*\\bmd5\\s([a-zA-Z0-9@#$%^&*!_+\\-=]+).*", "occurrence": -1, "operator": "NONE"}], "compliance.rule.block.criteria": {"compliance.rule.block.start": "interface", "compliance.rule.block.end": "!"}}, "compliance.rule.remediation": "Configure the appropriate interface(s) for Message Digest authentication\n```\n\nhostname(config)#interface {<em>interface_name</em>}\nhostname(config-if)#ip ospf message-digest-key {<em>ospf_md5_key-id</em>} md5 {<em>ospf_md5_key</em>}\n```", "compliance.rule.description": "Enable Open Shortest Path First (OSPF) Message Digest 5 (MD5) authentication.", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "This is part of the OSPF authentication setup", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Configuring the proper interface(s) for 'ip ospf message-digest-key md5' enforces these policies by restricting exchanges between network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/interface/command/ir-i1.html#GUID-0D6BDFCD-3FBB-4D26-A274-C1221F8592DF:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_ospf/command/ospf-i1.html#GUID-939C79FF-8C09-4D5A-AEB5-DAF25038CA18", "compliance.rule.additional.information": "", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": **************}, {"compliance.rule.name": "Set 'neighbor password'", "compliance.rule.category": "Network", "compliance.rule.context": {"compliance.rule.check.category": "basic", "compliance.rule.check.type": "Config File", "compliance.rule.conditions": [{"condition": "contain", "result.pattern": "neighbor\\s+\\d{1,3}(\\.\\d{1,3}){3}\\s+password\\s+\\S+", "occurrence": -1, "operator": "NONE"}]}, "compliance.rule.remediation": "Configure BGP neighbor authentication where feasible.\n```\n\nhostname(config)#router bgp <<em>bgp_as-number</em>>\nhostname(config-router)#neighbor <<em>bgp_neighbor-ip</em> | <em>peer-group-name</em>> password <<em>password</em>> \n```", "compliance.rule.description": "Enable message digest5 (MD5) authentication on a TCP connection between two BGP peers", "compliance.rule.severity": "MEDIUM", "compliance.rule.tags": [**********, *********, *********, -*********, -*********], "compliance.rule.rationale": "Enforcing routing authentication reduces the likelihood of routing poisoning and unauthorized routers from joining BGP routing.", "compliance.rule.impact": "Organizations should plan and implement enterprise security policies that require rigorous authentication methods for routing protocols. Using the 'neighbor password' for BGP enforces these policies by restricting the type of authentication between network devices.", "compliance.rule.default.value": "Not set", "compliance.rule.references": "http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_bgp/command/bgp-n1.html#GUID-A8900842-ECF3-42D3-B188-921BE0EC060B:http://www.cisco.com/en/US/docs/ios-xml/ios/iproute_bgp/command/bgp-m1.html#GUID-159A8006-F0DF-4B82-BB71-C39D2C134205", "compliance.rule.additional.information": "MD5 authentication between two BGP peers, meaning that each segment sent on the TCP connection between the peers is verified. MD5 authentication must be configured with the same password on both BGP peers.", "compliance.rule.controls": [{"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}, {"compliance.rule.control.version": "", "compliance.rule.control.name": "Explicitly Not Mapped", "compliance.rule.control.description": "Explicitly Not Mapped", "compliance.rule.control.ig": []}], "id": 10000000000085}], "version": "1.3"}]}