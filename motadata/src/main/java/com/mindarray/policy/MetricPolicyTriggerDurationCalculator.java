/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  20-Mar-2025     Chopra Deven    MOTADATA-5370: Added a check to get duration data of the policy from the policy key.
 *
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.MetricPolicy;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.store.MetricPolicyFlapDurationCacheStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.ObjectManagerCacheStore;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;

/**
 * MetricPolicyTriggerDurationCalculator tracks and calculates the duration of policy triggers and state changes.
 * <p>
 * This class:
 * 1. Maintains information about how long policies have been in specific states
 * 2. Tracks policy flaps (state changes) and their durations
 * 3. Persists policy state information for high availability and recovery
 * 4. Handles policy acknowledgments and comments
 * 5. Provides duration data for visualization and reporting
 * <p>
 * The calculator runs as a Vert.x verticle and communicates with other components
 * through the event bus to update and retrieve policy duration information.
 */
public class MetricPolicyTriggerDurationCalculator extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(MetricPolicyTriggerDurationCalculator.class, GlobalConstants.MOTADATA_POLICY, "Metric Policy Trigger Duration Calculator");
    private static final int POLICY_FLAP_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getPolicyFlapFlushTimerSeconds();
    private final JsonObject objectPolicyStatuses = new JsonObject();
    private final JsonObject policyTriggerTicks = new JsonObject();
    private final AtomicBoolean policyTriggerTickDirty = new AtomicBoolean();
    private final AtomicBoolean policyStatusDirty = new AtomicBoolean();
    private boolean updated = false;

    /**
     * Starts the MetricPolicyTriggerDurationCalculator verticle.
     * This method:
     * 1. Loads persisted policy trigger ticks and flap durations from backup files
     * 2. Sets up event bus handlers for duration calculation, queries, and acknowledgments
     * 3. Initializes periodic timers for flushing data to disk
     * 4. Registers with the high availability system for cache synchronization
     *
     * @param promise A promise that will be completed when startup is finished or failed if an error occurs
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            var flushTimerSeconds = new AtomicInteger(POLICY_FLAP_FLUSH_TIMER_SECONDS);

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_TRIGGER_TICKS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    policyTriggerTicks.mergeIn(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))));

                    LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.METRIC_POLICY_TRIGGER_TICKS));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }

            file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap().forEach((key, value) -> objectPolicyStatuses.put(key, JsonObject.mapFrom(value)));

                    LOGGER.info(String.format("%s loaded from the backup file...", PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE, message ->
            {
                try
                {

                    if (!message.body().isEmpty())
                    {

                        var event = message.body();

                        var key = event.getLong(ENTITY_ID) + SEPARATOR + event.getLong(ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !CommonUtil.getString(event.getValue(INSTANCE)).isEmpty() ? event.getString(GlobalConstants.METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getString(GlobalConstants.METRIC));

                        if (objectPolicyStatuses.containsKey(key))
                        {
                            var duration = objectPolicyStatuses.getJsonObject(key).getInteger(DURATION, 0);

                            if (!objectPolicyStatuses.getJsonObject(key).getString(SEVERITY).equalsIgnoreCase(event.getString(SEVERITY)))   // flap change
                            {
                                /*
                                 * There are two case
                                 * 1. previous.flap.timestamp = event.timestamp (auto_clear) -> we get duration 10, we add duration in event.timestamp
                                 * 2. previous.flap.timestamp > event.timestamp (polling delay) -> we get duration < difference, we add difference + 1 to maintain sequence of flap
                                 * */

                                var previousFlapTimeStamp = objectPolicyStatuses.getJsonObject(key).getLong(PREVIOUS_FLAP_TIMESTAMP);

                                var currentTimeStamp = event.getLong(EVENT_TIMESTAMP);

                                var flapDiffSeconds = event.getLong(EVENT_TIMESTAMP) - previousFlapTimeStamp;

                                if (flapDiffSeconds <= 0)
                                {
                                    currentTimeStamp = event.getLong(EVENT_TIMESTAMP) + (duration <= Math.abs(flapDiffSeconds) ? 1 + Math.abs(flapDiffSeconds) : duration);

                                    flapDiffSeconds = currentTimeStamp - previousFlapTimeStamp;
                                }

                                send(objectPolicyStatuses.getJsonObject(key).put(DURATION, flapDiffSeconds));   // if flap then send data to DB..

                                // if flap then notify to refresh respective widgets
                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName())).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(event.getLong(ENTITY_ID)))))));

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("Current severity for policy.key %s of %s is %s with %s is %d for duration %d with flapDiff %d and new Severity %s with timestamp is %d which triggered for value : %s", key, event.getString(AIOpsObject.OBJECT_IP), objectPolicyStatuses.getJsonObject(key).getString(SEVERITY), PREVIOUS_FLAP_TIMESTAMP, previousFlapTimeStamp, duration, flapDiffSeconds, event.getString(SEVERITY), event.getLong(EVENT_TIMESTAMP), event.getString(VALUE)));
                                }

                                objectPolicyStatuses.getJsonObject(key).mergeIn(event).put(PolicyEngineConstants.POLICY_NOTE, EMPTY_VALUE).put(PREVIOUS_FLAP_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(PREVIOUS_FLAP_VALUE, event.getValue(VALUE)).remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);

                                if ((event.getLong(EVENT_TIMESTAMP) - previousFlapTimeStamp) <= 0) // MOTADATA-558 in case of auto-clear and polling scheduler clashes
                                {
                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("!!Altered timestamp!! ip : %s, %s : %d, event.timestamp : %d, duration : %d,updated %s : %d", event.getString(AIOpsObject.OBJECT_IP), PREVIOUS_FLAP_TIMESTAMP, previousFlapTimeStamp, event.getLong(EVENT_TIMESTAMP), duration, PREVIOUS_FLAP_TIMESTAMP, currentTimeStamp));
                                    }

                                    objectPolicyStatuses.getJsonObject(key).put(PREVIOUS_FLAP_TIMESTAMP, currentTimeStamp);
                                }

                                MetricPolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), true);
                            }
                            else
                            {
                                objectPolicyStatuses.getJsonObject(key).mergeIn(event);
                            }
                        }
                        else
                        {
                            objectPolicyStatuses.put(key, event.put(DURATION, 0).put(PolicyEngineConstants.POLICY_NOTE, EMPTY_VALUE).put(PREVIOUS_FLAP_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(PREVIOUS_FLAP_VALUE, event.getValue(VALUE)));

                            MetricPolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), true);

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_VISUALIZATION_REALTIME_WIDGET_REFRESH, new JsonObject().put(EVENT_CONTEXT, new JsonObject().put(VisualizationConstants.PLUGIN, DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName() + DASH_SEPARATOR + ObjectManagerCacheStore.getStore().getMetricName(DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName())).put(NMSConstants.OBJECT, new JsonArray().add(CommonUtil.getString(ObjectConfigStore.getStore().getObjectId(event.getLong(ENTITY_ID)))))));
                        }

                        if (!policyTriggerTicks.containsKey(key))
                        {
                            updated = true;

                            policyTriggerTicks.put(key, System.currentTimeMillis());
                        }

                        event.put(PolicyEngineConstants.POLICY_FIRST_TRIGGER_TICK, policyTriggerTicks.getLong(key));
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY, message ->
            {
                var request = message.body();

                if (!request.isEmpty() && request.containsKey(PolicyEngineConstants.POLICY_KEY))    // if policy key is present then return only that policy data
                {
                    message.reply(objectPolicyStatuses.getJsonObject(request.getString(PolicyEngineConstants.POLICY_KEY), new JsonObject()));
                }
                else
                {
                    message.reply(new JsonObject().mergeIn(objectPolicyStatuses));
                }

            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_TICK_QUERY, message -> message.reply(new JsonObject().mergeIn(policyTriggerTicks)));

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLICY_ACKNOWLEDGE, message ->
            {
                var event = message.body();

                var policy = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

                var key = event.getLong(AIOpsObject.OBJECT_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !CommonUtil.getString(event.getValue(INSTANCE)).isEmpty() ? policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC) + SEPARATOR + event.getValue(INSTANCE) : policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC));

                var context = new JsonObject().mergeIn(event).put(PolicyEngineConstants.POLICY_ACKNOWLEDGE_TIME, DateTimeUtil.currentMilliSeconds());

                context.remove(AIOpsObject.OBJECT_ID);

                context.remove(PolicyEngineConstants.POLICY_ID);

                context.remove(INSTANCE);

                if (context.getString(ACKNOWLEDGED).equalsIgnoreCase(YES))
                {
                    objectPolicyStatuses.getJsonObject(key).put(PolicyEngineConstants.POLICY_ACKNOWLEDGE, context.encode());

                    MetricPolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), false);

                }
                else
                {
                    objectPolicyStatuses.getJsonObject(key).remove(PolicyEngineConstants.POLICY_ACKNOWLEDGE);

                    MetricPolicyFlapDurationCacheStore.getStore().update(key, objectPolicyStatuses.getJsonObject(key), false);

                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_POLICY_COMMENT_UPDATE, message ->
            {
                var event = message.body();

                var policy = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

                var key = event.getLong(AIOpsObject.OBJECT_ID) + SEPARATOR + event.getLong(PolicyEngineConstants.POLICY_ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !CommonUtil.getString(event.getValue(INSTANCE)).isEmpty() ? policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC) + SEPARATOR + event.getValue(INSTANCE) : policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(METRIC));

                objectPolicyStatuses.getJsonObject(key).put(PolicyEngineConstants.POLICY_NOTE, event.getString(PolicyEngineConstants.POLICY_NOTE));
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.DELETE_OBJECT && (!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null))
                {
                    objectPolicyStatuses.getMap().keySet().removeIf(key -> CommonUtil.getLong(key.split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));
                }
                else if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.UPDATE_CACHE)
                {
                    if (policyTriggerTickDirty.get())
                    {
                        policyTriggerTickDirty.set(false);

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, PolicyEngineConstants.METRIC_POLICY_TRIGGER_TICKS).put(STATUS, STATUS_SUCCEED).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_TRIGGER_TICKS)));
                    }

                    if (policyStatusDirty.get())
                    {
                        policyStatusDirty.set(false);

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS).put(STATUS, STATUS_SUCCEED).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS)));
                    }
                }
            }).exceptionHandler(LOGGER::error);

            vertx.setPeriodic(10 * 1000L, timer -> write());

            vertx.setPeriodic(30 * 1000L, timer ->
            {
                if (updated)
                {
                    policyTriggerTickDirty.set(true);

                    updated = false;

                    vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_TRIGGER_TICKS,
                            Buffer.buffer(CodecUtil.compress(policyTriggerTicks.encode().getBytes())));
                }

                flushTimerSeconds.set(flushTimerSeconds.get() - 30);

                if (flushTimerSeconds.get() <= 0 && !objectPolicyStatuses.isEmpty())
                {
                    policyStatusDirty.set(true);

                    vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(objectPolicyStatuses).encode().getBytes())));

                    flushTimerSeconds.set(POLICY_FLAP_FLUSH_TIMER_SECONDS);
                }
            });

            MetricPolicyFlapDurationCacheStore.getStore().initStore();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    /**
     * Updates policy durations by incrementing the duration counters.
     * This method is called periodically to:
     * 1. Increment the duration for all active policy states
     * 2. Clean up entries for objects that no longer exist
     * <p>
     * The duration is incremented by 10 seconds each time this method is called.
     */
    private void write()
    {
        var deletedEntries = new HashSet<String>();

        for (var entry : objectPolicyStatuses.getMap().entrySet())
        {
            try
            {
                var value = (JsonObject) entry.getValue();

                var objectId = value.getLong(APIConstants.ENTITY_ID);

                var object = ObjectConfigStore.getStore().getItem(objectId, false);

                if (object != null)
                {
                    value.put(DURATION, value.getInteger(DURATION, 0) + 10);
                }
                else
                {
                    // just for safer purpose will be keeping check if object null and its keys not removed so will be removing keys forcefully
                    deletedEntries.add(entry.getKey());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        if (!deletedEntries.isEmpty())
        {
            deletedEntries.forEach(objectPolicyStatuses::remove);
        }
    }

    /**
     * Stops the MetricPolicyTriggerDurationCalculator verticle.
     * This method ensures that all policy state information is persisted to disk
     * before the verticle is undeployed, preserving the data for recovery.
     *
     * @param promise A promise that will be completed when shutdown is finished
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        LOGGER.info("Undeploying verticle MetricTriggerDurationCalculator...");

        if (!objectPolicyStatuses.isEmpty())
        {
            vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + PolicyEngineConstants.METRIC_POLICY_FLAP_DURATIONS,
                    Buffer.buffer(CodecUtil.compress(new JsonObject().mergeIn(objectPolicyStatuses).encode().getBytes())));
        }

        promise.complete();
    }

    /**
     * Sends policy flap information to the datastore.
     * This method:
     * 1. Creates a buffer with policy flap data including timestamps, durations, and state information
     * 2. Formats the data for storage in the event datastore
     * 3. Resets the duration counter after recording the flap
     *
     * @param value The policy flap information to be sent to the datastore
     */
    private void send(JsonObject value)
    {
        try
        {
            var buffer = Buffer.buffer();

            appendBytes(buffer, value.getLong(PREVIOUS_FLAP_TIMESTAMP), DatastoreConstants.PluginId.METRIC_POLICY_DURATION_METRIC.getName());

            updateEventFieldBatch(AIOpsObject.OBJECT_ID, CommonUtil.getString(ObjectConfigStore.getStore().getItem(value.getLong(APIConstants.ENTITY_ID), false).getInteger(AIOpsObject.OBJECT_ID)), buffer);

            updateEventFieldBatch(INSTANCE, value.containsKey(INSTANCE) && !value.getString(INSTANCE).isEmpty() ? CommonUtil.getString(value.getValue(INSTANCE)) : EMPTY_VALUE, buffer);

            updateEventFieldBatch(PolicyEngineConstants.POLICY_ID, CommonUtil.getString(value.getLong(ID)), buffer);

            updateEventFieldBatch(DURATION, CommonUtil.getString(value.getInteger(DURATION)), buffer);

            updateEventFieldBatch(SEVERITY, value.getString(SEVERITY), buffer);

            updateEventFieldBatch(MetricPolicy.POLICY_THRESHOLD, CommonUtil.getString(value.getValue(MetricPolicy.POLICY_THRESHOLD, EMPTY_VALUE)), buffer);

            updateEventFieldBatch(VALUE, CommonUtil.getString(value.getValue(PREVIOUS_FLAP_VALUE, value.getValue(VALUE))), buffer);

            updateEventFieldBatch(METRIC, CommonUtil.getString(value.getValue(METRIC)), buffer);

            updateEventFieldBatch(PolicyEngineConstants.POLICY_TYPE, CommonUtil.getString(value.getValue(PolicyEngineConstants.POLICY_TYPE)), buffer);

            vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.EVENT.getName(), buffer.getBytes());

            value.put(DURATION, 0);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Initializes a buffer with common event metadata.
     * This method sets up the buffer with timestamp, plugin information,
     * and datastore format details that are required for all events.
     *
     * @param bytes     The buffer to initialize
     * @param timestamp The event timestamp in epoch seconds
     * @param pluginId  The ID of the plugin generating the event
     */
    private void appendBytes(Buffer bytes, long timestamp, int pluginId)
    {
        bytes.setLongLE(0, timestamp);

        var plugin = pluginId + DASH_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName().toLowerCase();

        bytes.appendIntLE(plugin.length());

        bytes.appendString(plugin);

        bytes.appendByte(DatastoreConstants.DatastoreFormat.HORIZONTAL.getName().byteValue());

        bytes.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.METRIC_POLICY_FLAP_HISTORY.ordinal()));

        bytes.appendShortLE(CommonUtil.getShort(EMPTY_VALUE.length()));

        bytes.appendString(EMPTY_VALUE);
    }

    /**
     * Updates a field in the event buffer for batch processing.
     * This method formats and appends a field-value pair to the buffer
     * according to the datastore's expected format, handling different
     * data types appropriately.
     *
     * @param column The name of the field to update
     * @param value  The string value to set for the field
     * @param buffer The buffer to append the field-value pair to
     */
    private void updateEventFieldBatch(String column, String value, Buffer buffer)
    {
        try
        {
            var category = DatastoreConstants.DataCategory.STRING.getName();

            if (column.equalsIgnoreCase(PolicyEngineConstants.POLICY_ID) || column.equalsIgnoreCase(DURATION) || column.equalsIgnoreCase(AIOpsObject.OBJECT_ID))
            {
                category = DatastoreConstants.DataCategory.NUMERIC.getName();
            }

            buffer.appendByte(category);

            buffer.appendIntLE(column.length());

            buffer.appendString(column);

            if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
            {
                buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
            }
            else
            {
                var bytes = value.getBytes(StandardCharsets.UTF_8);

                buffer.appendIntLE(bytes.length);

                buffer.appendBytes(bytes);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
