/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date          Author              Notes
 *   2025-02-04    Smit Prajapati      MOTADATA-4954  Alert Sound Notification for Event Policy
 *   2025-02-11    Chandresh           MOTADATA-446   Event Policy Template related enhancements
 *   2025-02-25	   Darshan Parmar	   MOTADATA-5215  SonarQube Suggestions Resolution
 *   2025-02-27    Chopra Deven        MOTADATA-4973   Dumping Event source for Event Policy in case of notification trigger action event and policy trigger event
 *   2025-02-28    Smit Prajapati      MOTADATA-4956   Added try-cache block for exception handling
 *   2025-03-25    Smit Prajapati      MOTADATA-5435  Flow back-pressure mechanism.
 *   9-Apr-2025    Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *   2-Jun-2025    Smit Prajapati      MOTADATA-6418: EventPolicyInspector/EventPolicyAggregator Support
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.job.JobScheduler;
import com.mindarray.store.EventPolicyConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.EventPolicy.POLICY_RESULT_BY;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * EventPolicyAggregator is responsible for collecting, grouping, and preparing event statistics
 * required for policy evaluation.
 * <p>
 * This class listens for incoming events, applies group-by logic (if defined in policy),
 * maintains aggregation metrics like sum, count, and average, and sends the grouped data
 * to the EventPolicyInspector for evaluation.
 * <p>
 * It does not evaluate policy conditions itself but supports the inspector with structured data.
 * <p>
 * Responsibilities:
 * <ul>
 *   <li>Extracting group keys based on policy configuration</li>
 *   <li>Maintaining aggregation statistics (sum, count, avg)</li>
 *   <li>Forwarding processed group stats to the inspector for evaluation</li>
 *   <li>Managing memory constraints (e.g., top N groups)</li>
 * </ul>
 */
public class EventPolicyAggregator extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(EventPolicyAggregator.class, GlobalConstants.MOTADATA_POLICY, "Event Policy Aggregator");

    /**
     * Maximum number of top groups to process in group-based policies
     */
    private static final int TOP_N_GROUPS = MotadataConfigUtil.getEventPolicyTopNGroups();

    /**
     * Maximum number of unique groups allowed in a policy
     */
    private static final int MAX_GROUPS = MotadataConfigUtil.getEventPolicyMaxGroups();

    /**
     * Map of policy IDs to their context information
     */
    private final Map<Long, JsonObject> policies = new HashMap<>(64);

    /**
     * Map of policy IDs to their group statistics
     */
    private final Map<Long, Map<String, GroupStat>> policyGroupStats = new HashMap<>(64);

    /**
     * Storage for event column mappings
     */
    private final JsonObject eventColumns = new JsonObject();

    /**
     * Map of policy IDs to their grouping counter columns (for policies with group-by clauses)
     */
    private final Map<Long, String[]> groupingCounters = new HashMap<>();

    /**
     * Event engine for processing events
     */
    private EventEngine eventEngine;

    /**
     * Initializes the EventPolicyInspector verticle.
     * <p>
     * This method sets up event bus consumers, periodic timers, and initializes the event engine.
     * It loads existing policies from the store and prepares them for evaluation.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
        {
            eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

            try
            {
                var items = EventPolicyConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var policy = items.getJsonObject(index);

                    if (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO))
                    {
                        policies.put(policy.getLong(ID), enrich(policy));
                    }
                }

                init();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                {
                    update(eventColumns, tokens, false);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::aggregate).start(vertx, promise);

        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            try
            {
                var event = message.body();

                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {

                    case ADD_POLICY, UPDATE_POLICY ->
                    {
                        var item = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        policies.put(item.getLong(ID), enrich(item));

                        init(item, item.getLong(ID));
                    }

                    case DELETE_POLICY ->
                    {
                        policies.remove(event.getLong(ID));

                        clear(event.getLong(ID));
                    }

                    case DISABLE_POLICY_ACTION_TRIGGER ->
                    {
                        var item = SchedulerConfigStore.getStore().getItem(event.getLong(ID));

                        if (item != null)
                        {
                            Bootstrap.configDBService().delete(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                            {
                                if (future.succeeded())
                                {
                                    SchedulerConfigStore.getStore().deleteItem(event.getLong(ID));

                                    JobScheduler.removeJob(event.getLong(ID));
                                }
                            });
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        }).exceptionHandler(LOGGER::error);

        // Registers a local consumer to handle policy stats fetch requests.
        // This is triggered by the EventPolicyInspector to collect current group statistics
        // for a given policy ID. The collected stats are processed and sent back via event bus.
        vertx.eventBus().<Long>localConsumer(EVENT_EVENT_POLICY_AGGREGATOR_QUERY, message ->
        {

            var id = message.body();

            var stats = policyGroupStats.get(id);

            var values = new ArrayList<>(stats.values());

            var aggregator = policies.get(id).getJsonObject(POLICY_CONTEXT).getString(VisualizationConstants.AGGREGATOR);

            GroupStatSorter.sort(values, aggregator, TOP_N_GROUPS);

            vertx.eventBus().send(EVENT_EVENT_POLICY_AGGREGATOR_RESPONSE, new JsonObject().put(ID, id).put(RESULT, transform(values)));

            // Reset state
            stats.clear();
        });
    }

    /**
     * Initializes policy group statistics for all loaded policies.
     * <p>
     * This method is called during startup to prepare the data structures
     * needed for policy evaluation, particularly for policies with group-by clauses.
     */
    private void init()
    {
        for (var entry : policies.entrySet())
        {
            var policyContext = entry.getValue().getJsonObject(POLICY_CONTEXT);

            if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
            {
                policyGroupStats.put(entry.getKey(), new HashMap<>());
            }
        }
    }

    /**
     * Initializes or reinitializes a specific policy.
     * <p>
     * This method is called when a policy is added or updated to prepare
     * the data structures needed for its evaluation.
     *
     * @param policy   The policy configuration
     * @param policyId The ID of the policy
     */
    private void init(JsonObject policy, long policyId)
    {
        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        if (policyContext.containsKey(POLICY_RESULT_BY) && !policyContext.getJsonArray(POLICY_RESULT_BY).isEmpty())
        {
            policyGroupStats.put(policyId, new HashMap<>());

            groupingCounters.remove(policyId);
        }
    }

    /**
     * Clears all data structures associated with a policy.
     * <p>
     * This method is called when a policy is deleted to clean up any resources
     * associated with it.
     *
     * @param policyId The ID of the policy to clear
     */
    private void clear(long policyId)
    {
        policyGroupStats.remove(policyId);

        groupingCounters.remove(policyId);
    }

    /**
     * Aggregates an event against in-memory policies.
     * <p>
     * This method evaluates an incoming event against applicable policies stored in memory.
     * It handles different types of events including traps and regular events, and manages
     * the policy evaluation lifecycle.
     *
     * @param event The event to inspect
     */
    private void aggregate(JsonObject event) // inspect for in-memory policies
    {
        try
        {

            var policy = policies.get(event.getLong(POLICY_ID));

            if (policy != null)
            {
                var context = policy.getJsonObject(POLICY_CONTEXT);

                if (!event.containsKey(EVENT_TIMESTAMP))
                {
                    event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                {
                    var stats = policyGroupStats.get(policy.getLong(ID));

                    if (stats.size() > MAX_GROUPS)
                    {
                        LOGGER.warn(String.format("policy: %s, Unique groups are more than %d, increase max group count or configure policy", policy.getString(POLICY_NAME), MAX_GROUPS));

                        var values = new ArrayList<>(stats.values());

                        var aggregator = context.getString(VisualizationConstants.AGGREGATOR);

                        GroupStatSorter.sort(values, aggregator, TOP_N_GROUPS);

                        stats.clear();

                        for (var value : values)
                        {
                            stats.put(value.group, value);
                        }
                    }
                    else
                    {
                        var groupByFields = context.getJsonArray(POLICY_RESULT_BY);

                        var addGroupingCounters = false;

                        if (!groupingCounters.containsKey(policy.getLong(ID)))
                        {
                            groupingCounters.put(policy.getLong(ID), new String[4]);

                            addGroupingCounters = true;
                        }

                        // Switched from StringBuilder to StringJoiner for cleaner and slightly more efficient handling of delimited string construction in loops.
                        var joiner = new StringJoiner(COLUMN_SEPARATOR);

                        for (var i = 0; i < groupByFields.size(); i++)
                        {
                            joiner.add(String.valueOf(event.getValue(groupByFields.getString(i))));

                            if (addGroupingCounters)
                            {
                                groupingCounters.get(policy.getLong(ID))[i] = groupByFields.getString(i);
                            }
                        }

                        var groupingKey = joiner.toString();

                        var stat = stats.computeIfAbsent(groupingKey, GroupStat::new);

                        stat.count++;

                        if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName())
                                || context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                        {
                            stat.sum += CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));
                        }
                    }
                }
                else
                {
                    // simple policy (no group by)
                    var stats = policyGroupStats.computeIfAbsent(policy.getLong(ID), k -> new HashMap<>());

                    var stat = stats.computeIfAbsent("default", GroupStat::new);

                    stat.count++;

                    if (context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName())
                            || context.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                    {
                        stat.sum += CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enriches a policy configuration with additional information.
     * <p>
     * This method processes the policy configuration to ensure it contains
     * all necessary information for evaluation, such as converting range values
     * to the appropriate format.
     *
     * @param policy The policy configuration to enrich
     * @return The enriched policy configuration
     */
    private JsonObject enrich(JsonObject policy)
    {
        try
        {
            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
            {
                var values = context.getString(VALUE).split(HASH_SEPARATOR);

                context.put(VALUE, new JsonArray().add(values[0]).add(values[1]));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return policy;
    }

    /**
     * This method convert GroupStat Object to JsonObject which can be sent through event bus
     * returned output:
     * {
     * grouping_1 -> [count, sum, grouping_1],
     * }
     **/

    private JsonObject transform(Collection<GroupStat> stats)
    {
        var result = new JsonObject();

        for (var stat : stats)
        {
            result.put(stat.group, new JsonArray()
                    .add(stat.count)
                    .add(stat.sum)
                    .add(stat.group));
        }

        return result;
    }

    /**
     * Stops the EventPolicyInspector verticle.
     * <p>
     * This method is called when the verticle is being stopped and ensures
     * that the event engine is properly shut down.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }

    /**
     * Inner class for tracking statistics for a group in policy evaluation.
     * <p>
     * This class maintains the group identifier, sum of values, and count of events
     * for calculating aggregations like sum, average, and count.
     */
    private static class GroupStat
    {
        /**
         * The group identifier
         */
        final String group;

        /**
         * Sum of values for this group
         */
        long sum;

        /**
         * Count of events for this group
         */
        int count;

        /**
         * Constructs a new GroupStat for the specified group.
         *
         * @param group The group identifier
         */
        GroupStat(String group)
        {
            this.group = group;
        }

        /**
         * Calculates the average value for this group.
         *
         * @return The average value, or 0 if count is 0
         */
        long avg()
        {
            return count == 0 ? 0 : sum / count;
        }
    }

    /**
     * Utility class for sorting group statistics.
     * <p>
     * This class provides methods for sorting group statistics based on different
     * aggregation criteria and limiting the results to the top N groups.
     */
    private static class GroupStatSorter
    {
        /**
         * Sorts a list of group statistics and keeps only the top N groups.
         * <p>
         * This method uses a priority queue to efficiently find the top N groups
         * based on the specified aggregator (sum, avg, or count) without sorting the entire list.
         *
         * @param values     The list of group statistics to sort
         * @param aggregator The aggregation type to use for sorting (sum, avg, or count)
         * @param topN       The number of top groups to keep
         */
        public static void sort(List<GroupStat> values, String aggregator, int topN)
        {
            Comparator<GroupStat> comparator;

            if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()))
            {
                comparator = Comparator.comparingLong((GroupStat stat) -> stat.sum).reversed();
            }
            else if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
            {
                comparator = Comparator.comparingLong(GroupStat::avg).reversed();
            }
            else
            {
                comparator = Comparator.comparingInt((GroupStat stat) -> stat.count).reversed();
            }

            // Use Priority Queue for optimal TopK performance
            sort(values, comparator, topN);
        }

        /**
         * Uses a priority queue to efficiently find the top K elements.
         * <p>
         * This approach maintains a min-heap of size K, which significantly outperforms
         * traditional sorting for large datasets. The priority queue only keeps the K largest
         * elements seen so far, making it very memory efficient and fast.
         *
         * @param values     List of GroupStat objects to process
         * @param comparator Comparator to use for sorting
         * @param k          Number of top elements to keep
         */
        private static void sort(List<GroupStat> values,
                                 Comparator<GroupStat> comparator,
                                 int k)
        {
            if (values.isEmpty() || k <= 0)
            {
                return;
            }

            if (values.size() <= k)
            {
                // If we have fewer elements than k, just sort the list
                values.sort(comparator);

                return;
            }

            // Create a min heap with the reverse of our comparator
            // This keeps the K largest elements with the smallest at the top for easy replacement
            var queue = new PriorityQueue<>(k, comparator.reversed());

            // Process each element
            for (var value : values)
            {
                if (queue.size() < k)
                {
                    // If we haven't reached capacity, add the element
                    queue.offer(value);
                }
                else if (comparator.compare(value, queue.peek()) > 0)
                {
                    // If this element is larger than the smallest in our heap, replace it
                    queue.poll();

                    queue.offer(value);
                }
            }

            // Clear the original list
            values.clear();

            // Convert heap to array for sorting
            var elements = queue.toArray(new GroupStat[0]);

            // Sort the result in the desired order
            Arrays.sort(elements, comparator);

            // Add the sorted elements back to the list
            Collections.addAll(values, elements);
        }


    }
}
