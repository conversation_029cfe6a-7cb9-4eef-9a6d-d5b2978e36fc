/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		<PERSON><PERSON>		MOTADATA-4878: added support for availability datastore
 *  25-Feb-2025		<PERSON><PERSON>		MOTADATA-5218: solved issue where for garbage column every time same data type ordinal value gets appended instead of single type. For ex : on hitting column mapper query, mapper.data.categories -> [0,0,0,0,0,0,0,0,] is returned instead it must be mapper.data.categories -> [0]
 *  26-Feb-2025		Darshan Parmar  MOTADATA-5215: SonarQube Suggestions Resolution
 *  10-Jun-2025		Pruthvi		    Added support for delete column in metric column mapper
 *  20-Jun-2025		Sankalp		    MOTADATA-5513 : Removed started.time.seconds from default data type string
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.datastore.DatastoreConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.*;

public class DatastoreManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DatastoreManager.class, GlobalConstants.MOTADATA_DATASTORE, "Datastore Manager");
    private static final int EVENT_DATASTORE_WORKER_INSTANCES = MotadataConfigUtil.getEventDatastoreWorkerInstances();
    private static final int METRIC_DATASTORE_WORKER_INSTANCES = MotadataConfigUtil.getMetricDatastoreWorkerInstances();
    private static final int AVAILABILITY_DATASTORE_WORKER_INSTANCES = MotadataConfigUtil.getAvailabilityDatastoreWorkerInstances();
    private final Map<String, String> items = new HashMap<>(); //items by uuid
    private final Set<String> eventCategories = new HashSet<>();
    private JsonObject metricColumns;
    private JsonObject eventColumns;
    private JsonObject indexableColumns;
    private boolean categoryUpdated = false;
    private boolean indexableColumnMapperUpdated = false;
    private boolean eventColumnMapperUpdated = false;
    private boolean metricColumnMapperUpdated = false;
    private boolean dirtyMetricColumnMapper = false;
    private boolean dirtyEventColumnMapper = false;
    private boolean dirtyIndexableColumnMapper = false;
    private boolean dirtyCategory = false;
    private Set<String> shadowCounters;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            eventColumns = new JsonObject();

            indexableColumns = new JsonObject();

            metricColumns = new JsonObject();

            shadowCounters = new HashSet<>();

            loadColumns(metricColumns, DatastoreConstants.METRIC_COLUMNS);

            loadColumns(eventColumns, EVENT_COLUMNS);

            loadColumns(indexableColumns, INDEXABLE_COLUMNS);

            loadShadowCounters(shadowCounters);

            loadCategories();

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                try
                {
                    if (event.containsKey(CHANGE_NOTIFICATION_TYPE) && ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == ChangeNotificationType.UPDATE_CACHE)
                    {
                        if (dirtyEventColumnMapper)
                        {
                            dirtyEventColumnMapper = false;

                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, EVENT_COLUMNS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + EVENT_COLUMNS)));
                        }

                        if (dirtyIndexableColumnMapper)
                        {
                            dirtyIndexableColumnMapper = false;

                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, INDEXABLE_COLUMNS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INDEXABLE_COLUMNS)));
                        }

                        if (dirtyCategory)
                        {
                            dirtyCategory = false;

                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, EVENT_CATEGORIES).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + EVENT_CATEGORIES)));
                        }

                        if (dirtyMetricColumnMapper)
                        {
                            dirtyMetricColumnMapper = false;

                            HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, METRIC_COLUMNS).put(RESULT, vertx.fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + METRIC_COLUMNS)));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.setPeriodic(10000, timer ->
            {
                try
                {
                    if (eventColumnMapperUpdated)
                    {
                        eventColumnMapperUpdated = false;

                        dirtyEventColumnMapper = true;

                        vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + EVENT_COLUMNS,
                                Buffer.buffer(CodecUtil.compress(eventColumns.encode().getBytes())));
                    }

                    if (indexableColumnMapperUpdated)
                    {
                        indexableColumnMapperUpdated = false;

                        dirtyIndexableColumnMapper = true;

                        vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INDEXABLE_COLUMNS,
                                Buffer.buffer(CodecUtil.compress(indexableColumns.encode().getBytes())));
                    }

                    if (metricColumnMapperUpdated)
                    {
                        metricColumnMapperUpdated = false;

                        dirtyMetricColumnMapper = true;

                        vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + DatastoreConstants.METRIC_COLUMNS,
                                Buffer.buffer(CodecUtil.compress(metricColumns.encode().getBytes())));
                    }

                    if (categoryUpdated)
                    {
                        categoryUpdated = false;

                        dirtyCategory = true;

                        vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + EVENT_CATEGORIES,
                                Buffer.buffer(CodecUtil.compress(new JsonArray(new ArrayList<>(eventCategories)).encode().getBytes())));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_INDEXABLE_COLUMN_QUERY, message ->
            {
                var event = message.body();

                try
                {
                    if (!event.isEmpty())
                    {
                        var indexableColumns = new HashSet<>();

                        var pluginIds = event.getJsonArray(GlobalConstants.PLUGIN_ID);

                        for (var index = 0; index < pluginIds.size(); index++)
                        {
                            var tokens = pluginIds.getString(index).split(GlobalConstants.DASH_SEPARATOR);

                            if (CommonUtil.getInteger(tokens[0]) != DatastoreConstants.PluginId.CORRELATED_METRIC.getName())
                            {
                                if (CommonUtil.getInteger(tokens[0]) == DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() || CommonUtil.getInteger(tokens[0]) == DatastoreConstants.PluginId.LOG_EVENT_STAT.getName())
                                {
                                    indexableColumns.add(EVENT_SOURCE);

                                    indexableColumns.add(LogEngineConstants.EVENT_CATEGORY);

                                    indexableColumns.add(LogEngineConstants.EVENT_SOURCE_TYPE);

                                    indexableColumns.add(LogEngineConstants.EVENT_SEVERITY);
                                }

                                else if (CommonUtil.getInteger(tokens[0]) == DatastoreConstants.PluginId.FLOW_EVENT.getName())
                                {
                                    indexableColumns.add(EVENT_SOURCE);

                                    indexableColumns.addAll(FlowEngineConstants.FLOW_INDEXABLE_COLUMNS.getList());
                                }

                                else
                                {

                                    var plugin = pluginIds.getString(index).replace(" ", ".").toLowerCase();

                                    if (CommonUtil.getInteger(tokens[0]) > DatastoreConstants.PluginId.LOG_EVENT.getName())//log plugin
                                    {
                                        indexableColumns.add(EVENT_SOURCE);

                                        indexableColumns.add(LogEngineConstants.EVENT_CATEGORY);

                                        indexableColumns.add(LogEngineConstants.EVENT_SOURCE_TYPE);

                                        indexableColumns.add(LogEngineConstants.EVENT_SEVERITY);
                                    }


                                    if (this.indexableColumns.containsKey(plugin) && this.indexableColumns.getValue(plugin) != null && !this.indexableColumns.getJsonArray(plugin).isEmpty())
                                    {
                                        indexableColumns.addAll(this.indexableColumns.getJsonArray(plugin).getList());
                                    }

                                    else
                                    {
                                        //as indexable column not updated then show all columns for grouping and filtering purpose

                                        for (var entry : eventColumns.getMap().entrySet())
                                        {
                                            var column = JsonObject.mapFrom(entry.getValue());

                                            if (column.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).contains(CommonUtil.getInteger(tokens[0])) && column.getString(DatastoreConstants.MAPPER_EVENT_CATEGORY).equalsIgnoreCase(tokens[1]))
                                            {
                                                indexableColumns.add(entry.getKey());
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        indexableColumns.remove(EVENT);

                        indexableColumns.remove(LogEngineConstants.EVENT_PATTERN_ID);

                        indexableColumns.remove(GlobalConstants.MESSAGE);

                        message.reply(new JsonObject().put(GlobalConstants.RESULT, new ArrayList<>(indexableColumns)));
                    }
                }

                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EVENT_COLUMN_MAPPER_UPDATE, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                    {
                        update(eventColumns, event.getString(MAPPER).split(COLUMN_SEPARATOR, -1), false);

                        eventColumnMapperUpdated = true;
                    }
                    else if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                    {
                        update(metricColumns, event.getString(MAPPER).split(COLUMN_SEPARATOR, -1), true);

                        metricColumnMapperUpdated = true;
                    }
                    else if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.DELETE_METRIC_COLUMN.name()))
                    {
                        metricColumns.remove(event.getString(MAPPER));

                        LOGGER.info(String.format("mapper removed: %s", event.getString(MAPPER)));

                        metricColumnMapperUpdated = true;
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, message ->
            {
                try
                {
                    message.reply(new JsonObject().put(EVENT_COLUMNS, eventColumns));
                }
                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, message ->
            {
                try
                {
                    message.reply(new JsonObject().put(DatastoreConstants.METRIC_COLUMNS, metricColumns));
                }
                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<String>localConsumer(EVENT_EVENT_CATEGORY_UPDATE, message ->
            {
                try
                {
                    if (!message.body().isEmpty() && !eventCategories.contains(message.body()))
                    {
                        eventCategories.add(message.body());

                        categoryUpdated = true;
                    }
                }
                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EVENT_INDEXABLE_COLUMN_UPDATE, message ->
            {
                var event = message.body();

                try
                {
                    if (!event.isEmpty())
                    {
                        for (var entry : event.getJsonObject(EVENT_CONTEXT).getMap().entrySet())
                        {
                            indexableColumns.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray());

                            for (var column : JsonObject.mapFrom(entry.getValue()).getMap().entrySet())
                            {
                                if (!indexableColumns.getJsonArray(entry.getKey()).contains(column.getKey()))
                                {
                                    indexableColumns.getJsonArray(entry.getKey()).add(column.getKey());

                                    indexableColumnMapperUpdated = true;
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            var remoteType = "remote-";

            var metricType = EventBusConstants.EVENT_DATASTORE_WRITE + GlobalConstants.DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.METRIC.getName();

            var eventType = EventBusConstants.EVENT_DATASTORE_WRITE + GlobalConstants.DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.EVENT.getName();

            var availabilityType = EventBusConstants.EVENT_DATASTORE_WRITE + GlobalConstants.DOT_SEPARATOR + DatastoreCategory.AVAILABILITY.getName();

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DATASTORE_REGISTER, message ->
            {
                var uuid = message.body().getString(REMOTE_EVENT_PROCESSOR_UUID);

                if (!uuid.isEmpty() && !items.containsKey(uuid))
                {
                    LOGGER.info(String.format("%s  : %s datastore registration event received...", uuid, message.body().getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));

                    Bootstrap.startEngine(new LocalEventRouter(metricType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, METRIC_DATASTORE_WORKER_INSTANCES, MetricDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), MetricDatastore.class.getSimpleName() + " " + uuid, null)
                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(eventType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, EVENT_DATASTORE_WORKER_INSTANCES, EventDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), EventDatastore.class.getSimpleName() + " " + uuid, null))
                            .compose(future -> Bootstrap.startEngine(new LocalEventRouter(availabilityType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, AVAILABILITY_DATASTORE_WORKER_INSTANCES, AvailabilityDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), AvailabilityDatastore.class.getSimpleName() + " " + uuid, null))
                            .onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    items.put(uuid, message.body().getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE));

                                    if (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()) || Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.SECONDARY.name()))
                                    {
                                        // all the files coming from DR/Secondary to Primary , we'll write in remote folder/
                                        Bootstrap.startEngine(new LocalEventRouter(remoteType + metricType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, METRIC_DATASTORE_WORKER_INSTANCES, MetricDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), "remote-" + MetricDatastore.class.getSimpleName() + " " + uuid, null)
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(remoteType + eventType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, EVENT_DATASTORE_WORKER_INSTANCES, EventDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), "remote-" + EventDatastore.class.getSimpleName() + " " + uuid, null))
                                                .compose(future -> Bootstrap.startEngine(new LocalEventRouter(remoteType + availabilityType + GlobalConstants.DOT_SEPARATOR + uuid, EVENT_TIMESTAMP, AVAILABILITY_DATASTORE_WORKER_INSTANCES, AvailabilityDatastore.class.getCanonicalName(), true, false, EventRouter.DYNAMIC), "remote-" + AvailabilityDatastore.class.getSimpleName() + " " + uuid, null))
                                                .onComplete(asyncResult ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        LOGGER.info(String.format("%s remote datastore registration event completed...", uuid));
                                                    }
                                                });
                                    }

                                    LOGGER.info(String.format("%s datastore registration event completed...", uuid));
                                }
                                else
                                {
                                    LOGGER.error(result.cause());
                                }
                            });
                }

            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<byte[]>localConsumer(metricType, message -> items.keySet().forEach(entry -> vertx.eventBus().send(metricType + GlobalConstants.DOT_SEPARATOR + entry, new JsonObject().put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE.getName()).put(EVENT_TIMESTAMP, (ByteUtil.readLongValue(message.body())))
                    .put(EVENT_CONTEXT, CodecUtil.compress(message.body()))))).exceptionHandler(LOGGER::error);

            vertx.eventBus().<byte[]>localConsumer(eventType, message -> items.keySet().forEach(entry -> vertx.eventBus().send(eventType + GlobalConstants.DOT_SEPARATOR + entry, new JsonObject().put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE.getName()).put(EVENT_TIMESTAMP, (ByteUtil.readLongValue(message.body())))
                    .put(EVENT_CONTEXT, CodecUtil.compress(message.body()))))).exceptionHandler(LOGGER::error);

            vertx.eventBus().<byte[]>localConsumer(availabilityType, message -> items.keySet().forEach(entry -> vertx.eventBus().send(availabilityType + GlobalConstants.DOT_SEPARATOR + entry, new JsonObject().put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.SAVE.getName()).put(EVENT_TIMESTAMP, (ByteUtil.readLongValue(message.body())))
                    .put(EVENT_CONTEXT, CodecUtil.compress(message.body()))))).exceptionHandler(LOGGER::error);

            if (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()))
            {
                // secondary will send updated data here
                vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_DATASTORE_SECONDARY_SYNC, message ->
                {
                    var event = message.body();

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format(" datastore uuid %s , category %s received from %s ", event.getString(REMOTE_EVENT_PROCESSOR_UUID), event.getString(DATASTORE_CATEGORY), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));
                    }

                    if (Objects.equals(event.getInteger(DATASTORE_CATEGORY), DatastoreCategory.METRIC.getName()))
                    {
                        vertx.eventBus().send(remoteType + metricType + DOT_SEPARATOR + event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event);
                    }
                    else if (Objects.equals(event.getInteger(DATASTORE_CATEGORY), DatastoreCategory.EVENT.getName()))
                    {
                        vertx.eventBus().send(remoteType + eventType + DOT_SEPARATOR + event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event);
                    }
                    else if (Objects.equals(event.getInteger(DATASTORE_CATEGORY), DatastoreCategory.AVAILABILITY.getName()))
                    {
                        vertx.eventBus().send(remoteType + availabilityType + DOT_SEPARATOR + event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event);
                    }

                }).exceptionHandler(LOGGER::error);

                // this timer will request to secondary/failover app for updated event data when secondary/failover was primary app
                vertx.setPeriodic(MotadataConfigUtil.getDatastorePollTimerSeconds(), timer ->
                {
                    var items = RemoteEventProcessorConfigStore.getStore().getItemsByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, GlobalConstants.BootstrapType.APP.name());

                    if (!items.isEmpty())
                    {
                        for (var index = 0; index < items.size(); index++)
                        {
                            var item = items.getJsonObject(index);

                            if (!item.getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) && !item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()))                                                 // if secondary/failover uuid and current uuid is same , means secondary/failover became primary. no need to send request
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug(String.format("request send to %s : %s , uuid %s for updated event", item.getString(REMOTE_EVENT_PROCESSOR_TYPE), item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE), item.getString(REMOTE_EVENT_PROCESSOR_UUID)));
                                }

                                this.items.keySet().forEach(uuid ->
                                {
                                    var context = new JsonObject().put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                            .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(EVENT_TYPE, EVENT_HA_DATASTORE_SECONDARY_SYNC).put(DatastoreConstants.DATASTORE_UUID, uuid)
                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                            .put(DatastoreConstants.DATASTORE_CATEGORY, DatastoreConstants.DatastoreCategory.EVENT.getName());

                                    vertx.eventBus().send(EVENT_PUBLICATION, context);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("requesting for datastore event category to %s ", uuid));
                                    }
                                });
                            }
                        }
                    }
                });

                // this timer will request to secondary/failover app for updated metric data when secondary/failover was primary app
                vertx.setPeriodic(MotadataConfigUtil.getDatastorePollTimerSeconds(), timer ->
                {
                    var items = RemoteEventProcessorConfigStore.getStore().getItemsByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, GlobalConstants.BootstrapType.APP.name());

                    if (!items.isEmpty())
                    {
                        for (var index = 0; index < items.size(); index++)
                        {
                            var item = items.getJsonObject(index);

                            if (!item.getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) && !item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()))                                                 // if secondary/failover uuid and current uuid is same , means secondary/failover became primary. no need to send request
                            {
                                if (CommonUtil.debugEnabled())
                                {
                                    LOGGER.debug(String.format("request send to %s : %s , uuid %s for updated event", item.getString(REMOTE_EVENT_PROCESSOR_TYPE), item.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE), item.getString(REMOTE_EVENT_PROCESSOR_UUID)));
                                }

                                this.items.keySet().forEach(uuid ->
                                {
                                    var context = new JsonObject().put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                            .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(EVENT_TYPE, EVENT_HA_DATASTORE_SECONDARY_SYNC).put(DatastoreConstants.DATASTORE_UUID, uuid)
                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                            .put(DatastoreConstants.DATASTORE_CATEGORY, DatastoreCategory.METRIC.getName());

                                    vertx.eventBus().send(EVENT_PUBLICATION, context);

                                    context = new JsonObject().put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                                            .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(EVENT_TYPE, EVENT_HA_DATASTORE_SECONDARY_SYNC).put(DatastoreConstants.DATASTORE_UUID, uuid)
                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                            .put(DatastoreConstants.DATASTORE_CATEGORY, DatastoreCategory.AVAILABILITY.getName());

                                    vertx.eventBus().send(EVENT_PUBLICATION, context);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace(String.format("requesting for datastore metric/availability category to %s ", uuid));
                                    }
                                });
                            }
                        }
                    }
                });

                vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_ACKNOWLEDGEMENT, message ->
                {
                    var event = message.body();

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("ack received for %s ", CommonUtil.getByteValue(event.getValue(HA_SYNC_TYPE))));

                    }

                    if (HASyncType.valueOfName(CommonUtil.getByteValue(event.getValue(HA_SYNC_TYPE))) == HASyncType.CONFIG)
                    {
                        Bootstrap.setSyncDone(Boolean.TRUE);
                    }
                });
            }
            else if (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.SECONDARY.name())
                    || Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.FAILOVER.name()))
            {
                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_HA_DATASTORE_SECONDARY_SYNC, message ->
                {
                    var context = message.body();

                    var datastoreUUID = context.getString(DatastoreConstants.DATASTORE_UUID);

                    if (items.containsKey(datastoreUUID))
                    {
                        context.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, datastoreUUID).put(EVENT_TYPE, EVENT_HA_DATASTORE_SECONDARY_SYNC);

                        if (Objects.equals(context.getInteger(DATASTORE_CATEGORY), DatastoreCategory.METRIC.getName()))
                        {
                            send(remoteType, metricType, context, METRIC_DATASTORE_WORKER_INSTANCES);
                        }
                        else if (Objects.equals(context.getInteger(DATASTORE_CATEGORY), DatastoreCategory.EVENT.getName()))
                        {
                            send(remoteType, eventType, context, EVENT_DATASTORE_WORKER_INSTANCES);
                        }
                        else if (Objects.equals(context.getInteger(DATASTORE_CATEGORY), DatastoreCategory.AVAILABILITY.getName()))
                        {
                            send(remoteType, availabilityType, context, AVAILABILITY_DATASTORE_WORKER_INSTANCES);
                        }
                    }
                    else
                    {
                        LOGGER.info(String.format("datastore item does not exist , uuid : %s ", datastoreUUID));
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void loadCategories()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + EVENT_CATEGORIES);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonArray(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).forEach(category -> eventCategories.add(CommonUtil.getString(category)));

                    LOGGER.info("loaded event categories");
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void loadColumns(JsonObject columns, String filter)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + filter);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    columns.mergeIn(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))));

                    LOGGER.info(String.format("%s loaded from the backup file...", filter));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(JsonObject columns, String[] tokens, boolean metric)
    {
        try
        {
            var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()].toLowerCase();

            var garbageColumn = false;

            if (!columns.containsKey(column))
            {
                columns.put(column, new JsonObject());
            }

            var mapper = columns.getJsonObject(column);

            var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS, new JsonArray(new ArrayList<>(1)));

            if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
            {
                mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
            }

            var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES, new JsonArray(new ArrayList<>(1)));

            if (metric)
            {
                mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);

                //#24371 add garbage column
                if (mapper.getString(DatastoreConstants.MAPPER_STATUS).equalsIgnoreCase(GlobalConstants.NO) && (column.endsWith(".id") || column.endsWith(".uptime") || column.endsWith(".version") || column.endsWith(".state") ||
                        column.endsWith(".status") || column.endsWith(".creation.time.seconds") || column.endsWith(".uptime.sec") ||
                        column.endsWith(".uptime.seconds") || column.endsWith(".started.time")))
                {
                    garbageColumn = true;
                }
            }

            if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))
            {
                mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);
            }

            if (!metric && tokens.length > 4)
            {
                mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, tokens[4]);
            }

            if (metric && tokens.length > 4)
            {
                mapper.put(DatastoreConstants.MAPPER_DURATION, tokens[4]);
            }

            if (!categories.contains(CommonUtil.getInteger(tokens[0])))
            {
                if (garbageColumn)
                {
                    mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(DatastoreConstants.DataCategory.STRING.getName()));
                }
                else
                {
                    mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
                }
            }

            if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
            {
                //25286 Bug lowercase
                mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0].toLowerCase());
            }

            if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) && mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))
            {
                mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);
            }

            if (shadowCounters.contains(column))
            {
                mapper.put(MAPPER_SHADOW_COUNTER, YES);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    /*
       Return HashSet For Load Shadow Counters Values ...
     */
    private void loadShadowCounters(Set<String> shadowCounters)
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + DB_DIR + GlobalConstants.PATH_SEPARATOR + SHADOW_COUNTERS_FILE);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    new JsonObject(buffer).getMap().forEach((key, value) -> shadowCounters.add(CommonUtil.getString(value)));

                    LOGGER.info(SHADOW_COUNTERS_FILE + " is Loaded...");
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(String remoteType, String datastoreType, JsonObject context, int datastoreInstances)
    {
        for (var index = 1; index <= datastoreInstances; index++)
        {
            // this for normal datastore backlog ex : for HA
            vertx.eventBus().send(datastoreType + DOT_SEPARATOR + context.getString(REMOTE_EVENT_PROCESSOR_UUID) + DOT_SEPARATOR + index + ".active", context.getString(REMOTE_EVENT_PROCESSOR_UUID));

            // this for remote-datastore backlog at secondary ex : for HA + DC-DR
            vertx.eventBus().send(remoteType + datastoreType + DOT_SEPARATOR + context.getString(REMOTE_EVENT_PROCESSOR_UUID) + DOT_SEPARATOR + index + ".active", context.getString(REMOTE_EVENT_PROCESSOR_UUID));
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        promise.complete();
    }
}
