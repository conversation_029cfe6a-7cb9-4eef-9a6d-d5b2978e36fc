[{"_type": "0", "id": 10000000000919, "visualization.name": "Node Count", "visualization.description": "Node Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.nodes", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.nodes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "windows.cluster.nodes", "icon": {"name": "node-count", "placement": "prefix"}}, "header": {"title": "Node Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.cluster.nodes.last"}]}}}}, {"_type": "0", "id": 10000000000920, "visualization.name": "Resources", "visualization.description": "Resources", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.online.resources", "aggregator": "last", "entity.type": "monitor"}, {"data.point": "windows.cluster.offline.resources", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.online.resources"}, {"type": "metric", "data.point": "windows.cluster.offline.resources"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "availability.resources.ms", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Resources", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Online", "value": "windows.cluster.online.resources.last"}, {"label": "Offline", "value": "windows.cluster.offline.resources.last"}]}}}}, {"_type": "0", "id": 10000000000921, "visualization.name": "Outstanding Messages", "visualization.description": "Outstanding Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.outstanding.messages", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.outstanding.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "windows.cluster.outstanding.messages", "icon": {"name": "messages", "placement": "prefix"}}, "header": {"title": "Outstanding Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.cluster.outstanding.messages.last"}]}}}}, {"_type": "0", "id": 10000000000922, "visualization.name": "RHS Processes", "visualization.description": "RHS Processes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.rhs.processes", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.rhs.processes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "RHS Processes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.cluster.rhs.processes.last"}]}}}}, {"_type": "0", "id": 10000000000923, "visualization.name": "Reconnect Count", "visualization.description": "Reconnect Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.network~reconnects", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.network~reconnects"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "windows.cluster.network~reconnects", "icon": {"name": "reconnect-count", "placement": "prefix"}}, "header": {"title": "Reconnect Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.cluster.network~reconnects.last"}]}}}}, {"_type": "0", "id": 10000000000924, "visualization.name": "Message Queue Length", "visualization.description": "Message Queue Length", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.network~normal.message.queue.length", "aggregator": "last", "entity.type": "monitor"}], "correlated.data.points": [{"type": "metric", "data.point": "windows.cluster.network~normal.message.queue.length"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Message Queue Length", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.cluster.network~normal.message.queue.length.last"}]}}}}, {"id": 10000000000925, "visualization.name": "Network Messages", "visualization.description": "Network Messages", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.network~received.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.network~sent.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000000926, "visualization.name": "Disk Volume Details", "visualization.description": "System Disk Volume Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.disk.volume~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.disk.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "windows.cluster.disk.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "windows.cluster.disk.volume~capacity.bytes.last", "alias": "system.disk.volume~capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "windows.cluster.disk.volume~free.bytes.last", "alias": "system.disk.volume~free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "windows.cluster.disk.volume~used.bytes.last", "alias": "system.disk.volume~used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000927, "visualization.name": "Cluster Messages", "visualization.description": "Cluster Messages", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.sent.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.sent.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000000928, "visualization.name": "Network Traffic/Sec", "visualization.description": "Network Traffic/Sec", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.network~received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.network~sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000000929, "visualization.name": "RHS Details", "visualization.description": "RHS Details", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.rhs.restarts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.rhs.processes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000000930, "visualization.name": "Network Traffic", "visualization.description": "Network Traffic", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.network~sent.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.network~received.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000000931, "visualization.name": "Resource Failure", "visualization.description": "Resource Failure", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.cluster.resource~failure.deadlocks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.resource~failure.access.violations", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.cluster.resource~failures", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}]