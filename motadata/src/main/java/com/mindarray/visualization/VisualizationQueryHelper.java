/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.visualization;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;
import static com.mindarray.visualization.VisualizationConstants.VisualizationGrouping.MONITOR;

public class VisualizationQueryHelper extends AbstractVerticle
{
    public static final Logger LOGGER = new Logger(VisualizationQueryHelper.class, MOTADATA_VISUALIZATION, "Visualization Query Helper");

    private final Map<Long, Message<JsonObject>> queries = new HashMap<>();

    private final Map<Long, Long> queryTrackers = new HashMap<>();

    private final JsonObject mappers = new JsonObject();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer(EVENT_VISUALIZATION_QUERY, message ->
        {
            try
            {
                var event = message.body();

                var sessionId = UUID.randomUUID().toString();

                event.put(SESSION_ID, sessionId);

                var id = CommonUtil.newEventId();

                if (event.containsKey(ID))
                {
                    event.put(EVENT_CONTEXT, new JsonObject().put(ID, event.getLong(ID)).put(SESSION_ID, sessionId).put(UI_EVENT_UUID, UUID.randomUUID().toString()).put(User.USER_NAME, event.getString(User.USER_NAME)));

                    event.put(ID, event.getLong(ID));

                    queries.put(id, message);

                    queryTrackers.put(id, DateTimeUtil.currentMilliSeconds());

                    vertx.eventBus().send(EVENT_VISUALIZATION, event.put(VisualizationConstants.VISUALIZATION_DECODE_RESPONSE, YES).put(REQUEST_ID, id).put(EVENT_TYPE, EVENT_VISUALIZATION_QUERY_RESPONSE));

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("visualization context : %s ", event.encode()));
                    }
                }
                else
                {
                    var context = prepareVisualizationContext(event);

                    queries.put(id, message);

                    queryTrackers.put(id, DateTimeUtil.currentMilliSeconds());

                    vertx.eventBus().send(EVENT_VISUALIZATION, context.put(VisualizationConstants.VISUALIZATION_DECODE_RESPONSE, YES).put(ID, -1).put(REQUEST_ID, id).put(EVENT_TYPE, EVENT_VISUALIZATION_QUERY_RESPONSE));

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("visualization context : %s ", context.encode()));
                    }
                }

                //test case purposes
                if (MotadataConfigUtil.devMode())
                {
                    id = event.containsKey(ID) ? event.getLong(ID) : id;

                    if (!queries.containsKey(id))
                    {
                        queries.put(id, message);

                        queryTrackers.put(id, DateTimeUtil.currentMilliSeconds());
                    }

                    vertx.eventBus().send(EVENT_VISUALIZATION_QUERY_RESPONSE, event.put(VisualizationConstants.VISUALIZATION_DECODE_RESPONSE, YES).put(ID, event.getLong(ID, -1L)).put(REQUEST_ID, id).put(RESULT, new JsonObject().put(RESULT, new JsonArray().add(new JsonObject()))));
                }

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("query send to visualization of id : %s ", id));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(ERRORS, new JsonObject().put(ERROR, exception.getStackTrace())
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR))
                        .put(MESSAGE, String.format(ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION, exception.getMessage()))
                        .put(MESSAGE, ErrorMessageConstants.INTERNAL_ERROR));
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_VISUALIZATION_QUERY_RESPONSE, message ->
        {
            var event = message.body();

            var id = event.getLong(REQUEST_ID);

            if (id != null && queries.containsKey(id))
            {
                try
                {
                    var result = event.getJsonObject(RESULT) != null ? event.getJsonObject(RESULT).getJsonArray(RESULT) : null;

                    if (result != null && !result.isEmpty())
                    {
                        event.put(STATUS, STATUS_SUCCEED);
                    }
                    else
                    {
                        event.put(STATUS, STATUS_FAIL).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, event.getJsonObject(RESULT).getString(ERROR, UNKNOWN)).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, event.getJsonObject(RESULT).getString(ERROR, UNKNOWN))));
                    }

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("response received of id : %s with status : %s , time taken : %s ms", id, event.getString(STATUS), DateTimeUtil.currentMilliSeconds() - queryTrackers.get(id)));
                    }

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("response received : %s ", event.encode()));
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    event.put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.INTERNAL_ERROR).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, exception.getStackTrace()).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(MESSAGE, String.format(ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION, exception.getMessage()))));
                }

                finally
                {
                    queryTrackers.remove(id);

                    queries.remove(id).reply(event);
                }
            }

        });

        vertx.setPeriodic(10 * 1000L, timer ->
        {
            try
            {
                if (!queries.isEmpty())
                {
                    for (var id : queries.keySet())
                    {
                        if (DateTimeUtil.currentMilliSeconds() - queryTrackers.get(id) > 20 * 1000L)                                // if any exception occur in app or datastore fail to respond or take time too long , need to return response to API. 20 sec max time.
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("request id : %s , Timed out with duration : %s ms", id, DateTimeUtil.currentMilliSeconds() - queryTrackers.get(id)));
                            }

                            queryTrackers.remove(id);

                            queries.remove(id).reply(new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.TIMED_OUT).put(ERRORS, new JsonArray().add(new JsonObject().put(ERROR, ErrorMessageConstants.TIMED_OUT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_TIMEOUT).put(MESSAGE, ErrorMessageConstants.TIMED_OUT))));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                {
                    var column = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1)[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

                    if (!mappers.containsKey(column.replaceAll("~", ".")))
                    {
                        mappers.put(column.replaceAll("~", "."), column);
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>request(EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
        {
            if (reply.succeeded())
            {
                for (var column : reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS).fieldNames())
                {
                    mappers.put(column.replaceAll("~", "."), column);
                }

                promise.complete();
            }
            else
            {
                promise.fail(reply.cause());
            }
        });
    }

    // preparing context for visualization based on user input
    private JsonObject prepareVisualizationContext(JsonObject event)
    {
        var context = new JsonObject();

        context.put(User.USER_NAME, event.getString(User.USER_NAME)).put(SESSION_ID, event.getString(SESSION_ID))
                .put(VisualizationConstants.VISUALIZATION_CATEGORY, event.getString(VisualizationConstants.VISUALIZATION_CATEGORY))
                .put(DISCARD_DUMMY_ROWS, event.getValue(DISCARD_DUMMY_ROWS));

        if (event.containsKey("result.by") && !event.getJsonArray("result.by").isEmpty())
        {
            context.put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray(event.getJsonArray("result.by").stream().map(filter -> filter.toString().toLowerCase()).toList()));
        }

        if (event.containsKey("granularity"))
        {
            context.put(VisualizationConstants.VISUALIZATION_GRANULARITY, event.getString("granularity"));
        }

        context.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray());

        var queries = event.getJsonArray("queries");

        var dataPoints = new JsonArray();

        for (var index = 0; index < queries.size(); index++)
        {
            var query = queries.getJsonObject(index);

            var dataPoint = new JsonObject().put(DATA_POINT, mappers.containsKey(query.getString(DATA_POINT)) ? mappers.getString(query.getString(DATA_POINT)) : query.getString(DATA_POINT)).put(AGGREGATOR, query.getString(AGGREGATOR));

            dataPoint.put(ENTITY_TYPE, query.getString(ENTITY_TYPE, MONITOR.getName()).toLowerCase());

            dataPoint.put(ENTITIES, query.getJsonArray(ENTITIES, new JsonArray()));

            dataPoints.add(dataPoint);
        }

        var dataFilter = new JsonObject();

        if (event.containsKey(DATA_FILTER) && event.getJsonObject(DATA_FILTER).containsKey(CONDITION_GROUPS))
        {
            var groups = new JsonArray(event.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).stream().limit(3).toList());

            dataFilter.put(CONDITION_GROUPS, new JsonArray()).put(FILTER, event.getJsonObject(DATA_FILTER).getString(FILTER, "include")).put(OPERATOR, event.getJsonObject(DATA_FILTER).getString(OPERATOR, "and"));

            for (var i = 0; i < groups.size(); i++)
            {
                var conditions = new JsonArray(groups.getJsonObject(i).getJsonArray(CONDITIONS).stream().limit(3).toList());

                for (var j = 0; j < conditions.size(); j++)
                {
                    var condition = conditions.getJsonObject(j);

                    condition.put(OPERAND, mappers.containsKey(condition.getString(OPERAND)) ? mappers.getString(condition.getString(OPERAND)) : condition.getString(OPERAND));
                }

                dataFilter.getJsonArray(CONDITION_GROUPS).add(new JsonObject().put(CONDITIONS, conditions).put(OPERATOR, groups.getJsonObject(i).getString(OPERATOR, "and")).put(FILTER, groups.getJsonObject(i).getString(FILTER, "include")));
            }
        }

        var resultFilter = new JsonObject();

        if (event.containsKey(RESULT_FILTER) && event.getJsonObject(RESULT_FILTER).containsKey(CONDITIONS))
        {
            var conditions = new JsonArray(event.getJsonObject(RESULT_FILTER).getJsonArray(CONDITIONS).stream().limit(3).toList());       // only 3 filter is allowed

            for (var index = 0; index < conditions.size(); index++)
            {
                var condition = conditions.getJsonObject(index);

                condition.put(OPERAND, mappers.containsKey(condition.getString(OPERAND)) ? mappers.getString(condition.getString(OPERAND)) : condition.getString(OPERAND));
            }

            resultFilter.put(CONDITIONS, conditions).put(OPERATOR, event.getJsonObject(RESULT_FILTER).getString(OPERATOR, "and")).put(FILTER, event.getJsonObject(RESULT_FILTER).getString(FILTER, "include"));
        }

        context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).add(new JsonObject().put(DATA_POINTS, dataPoints).put(VISUALIZATION_RESULT_BY, context.getJsonArray(VISUALIZATION_RESULT_BY, new JsonArray())).put(TYPE, event.getString(TYPE).toLowerCase()).put(FILTERS, new JsonObject().put(DATA_FILTER, dataFilter).put(DRILL_DOWN_FILTER, new JsonObject()).put(RESULT_FILTER, resultFilter)));

        if (context.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()))
        {
            context.put(VISUALIZATION_PROPERTIES, new JsonObject().put(VisualizationCategory.HISTOGRAM.getName().toLowerCase(), new JsonObject().put(SORTING, new JsonObject().put(LIMIT, event.getInteger(LIMIT, 10) > 100 ? 100 : event.getInteger(LIMIT, 10)).put("order", "desc"))));
        }

        context.put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(RELATIVE_TIMELINE, VisualizationTimeline.CUSTOM.getName()).mergeIn(event.getJsonObject("timeline"))).put(VISUALIZATION_TYPE, "Area");

        return context;
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        promise.complete();
    }
}
