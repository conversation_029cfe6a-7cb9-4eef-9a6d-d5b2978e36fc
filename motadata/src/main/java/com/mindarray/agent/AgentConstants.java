/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.agent;

import com.mindarray.Bootstrap;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.AgentConfigUtil;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Agent.AGENT_CONFIGS;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_METRIC_DISABLE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_METRIC_ENABLE;

/**
 * Constants and utility methods related to agent functionality in the Motadata system.
 * <p>
 * This class provides:
 * <ul>
 *   <li>Constants for agent types, statuses, and configurations</li>
 *   <li>Threshold values for agent health monitoring</li>
 *   <li>Utility methods for agent operations</li>
 *   <li>Enumerations for agent types and status types</li>
 * </ul>
 * <p>
 * The constants defined here are used throughout the agent package to ensure
 * consistency in naming and values. This class also contains utility methods
 * for common agent operations like updating metrics.
 */
public final class AgentConstants
{

    /**
     * Key for CPU usage percentage in agent health metrics
     */
    public static final String CPU_PERCENT = "cpu.percent";

    /**
     * Key for memory usage in megabytes in agent health metrics
     */
    public static final String MEMORY_MB = "memory.mb";

    /**
     * Key for agent-related configuration and events
     */
    public static final String AGENT = "agent";

    /**
     * Key for identifying the type of agent (metric, log, packet, etc.)
     */
    public static final String AGENT_TYPE = "agent.type";

    /**
     * Key for agent health diagnosis information
     */
    public static final String AGENT_HEALTH_DIAGNOSIS = "agent.health.diagnosis";

    /**
     * Key for metric agent status (enabled/disabled)
     */
    public static final String METRIC_AGENT_STATUS = "metric.agent.status";

    /**
     * Key for packet agent status (enabled/disabled)
     */
    public static final String PACKET_AGENT_STATUS = "packet.agent.status";

    /**
     * Key for log agent status (enabled/disabled)
     */
    public static final String LOG_AGENT_STATUS = "log.agent.status";

    /**
     * Key for packet agent application configurations
     */
    public static final String PACKET_AGENT_APPLICATIONS = "applications";

    /**
     * Flag indicating whether agent restart is required after configuration changes
     */
    public static final String AGENT_RESTART_REQUIRED = "agent.restart.required";

    /**
     * Interval in seconds between agent ping operations for health checking
     */
    public static final int AGENT_PING_INTERVAL_SECONDS = AgentConfigUtil.getAgentPingTimerSeconds();

    /**
     * Path to the agent cache directory for storing temporary agent data
     */
    public static final String AGENT_CACHE_PATH = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "agent-cache";

    /**
     * Filename for compressed agent logs when downloading
     */
    public static final String AGENT_COMPRESSED_LOG = "agent-logs.zip";

    /**
     * Set of all supported agent types in the system
     */
    public static final Set<String> agents = Set.of(Agent.METRIC.getName(), Agent.PACKET.getName(), Agent.LOG.getName(), Agent.WINDOWS_EVENT_LOG.getName());

    /**
     * Index for memory warning threshold in agent health thresholds array
     */
    public static final int MEMORY_WARNING_THRESHOLD_MB = 0;

    /**
     * Index for memory critical threshold in agent health thresholds array
     */
    public static final int MEMORY_CRITICAL_THRESHOLD_MB = 1;

    /**
     * Index for CPU warning threshold in agent health thresholds array
     */
    public static final int CPU_WARNING_THRESHOLD_PERCENT = 2;

    /**
     * Index for CPU critical threshold in agent health thresholds array
     */
    public static final int CPU_CRITICAL_THRESHOLD_PERCENT = 3;

    private AgentConstants()
    {
    }

    /**
     * Updates metrics based on agent status changes.
     * <p>
     * This method compares the old and new metric agent status. If the status has changed
     * (enabled to disabled or vice versa), it enables or disables all metrics associated
     * with the agent except for availability metrics.
     * <p>
     * The method performs the following steps:
     * <ol>
     *   <li>Extracts the new metric agent status from the event</li>
     *   <li>Compares it with the current status stored in the agent configuration</li>
     *   <li>If different, retrieves all metrics associated with the agent's object</li>
     *   <li>Filters out availability metrics (which are always kept enabled)</li>
     *   <li>Sends enable/disable events for each metric based on the new status</li>
     * </ol>
     *
     * @param event The event containing agent configuration changes
     */
    public static void updateMetric(JsonObject event)
    {
        // Extract the new metric agent status from the event
        var status = new JsonObject(event.getString(AGENT_CONFIGS)).getJsonObject(AGENT)
                .getString(METRIC_AGENT_STATUS);

        // Compare old and new metric agent state and if state is different,
        // enable/disable metrics (except availability) based on new state
        if (!status.equalsIgnoreCase(new JsonObject(AgentConfigStore.getStore().getItem(event.getLong(ID))
                .getString(AGENT_CONFIGS)).getJsonObject(AGENT)
                .getString(METRIC_AGENT_STATUS)))
        {
            // Get all metrics associated with the agent's object, excluding availability metrics
            MetricConfigStore.getStore().getItemsByObject(ObjectConfigStore.getStore().getItemByAgentId(event.getLong(ID)).getLong(ID))
                    .stream().filter(item -> !item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                    .forEach(item -> Bootstrap.vertx().eventBus().send(
                            // Send enable or disable event based on the new status
                            status.equalsIgnoreCase(YES) ? EVENT_METRIC_ENABLE : EVENT_METRIC_DISABLE,
                            // Create event with metric ID and user information
                            new JsonObject()
                                    .put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                    .put(ID, item.getLong(ID))
                                    .put(DEFAULT_USER, event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER)));
        }
    }

    /**
     * Enumeration of agent types supported by the Motadata system.
     * <p>
     * Each agent type represents a different data collection mechanism:
     * <ul>
     *   <li>METRIC - Collects performance metrics from systems</li>
     *   <li>LOG - Collects and processes log data</li>
     *   <li>WINDOWS_EVENT_LOG - Collects Windows event log data</li>
     *   <li>PACKET - Collects network packet data</li>
     * </ul>
     */
    public enum Agent
    {
        /**
         * Agent type for collecting performance metrics from systems
         */
        METRIC("metric"),

        /**
         * Agent type for collecting and processing log data
         */
        LOG("log"),

        /**
         * Agent type for collecting Windows event log data
         */
        WINDOWS_EVENT_LOG("windows.event.log"),

        /**
         * Agent type for collecting network packet data
         */
        PACKET("packet");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, Agent> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Agent::getName, e -> e)));

        /**
         * The string name of the agent type
         */
        private final String name;

        /**
         * Constructor for Agent enum.
         *
         * @param name The string name of the agent type
         */
        Agent(String name)
        {
            this.name = name;
        }

        /**
         * Gets an Agent enum value by its string name.
         *
         * @param name The string name to look up
         * @return The corresponding Agent enum value, or null if not found
         */
        public static Agent valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this agent type.
         *
         * @return The string name
         */
        public String getName()
        {
            return name;
        }
    }

    /**
     * Enumeration of agent status monitoring types.
     * <p>
     * Defines the different mechanisms used to monitor agent health:
     * <ul>
     *   <li>HEARTBEAT - Agent actively sends heartbeat signals to the server</li>
     *   <li>PING - Server pings the agent to check its availability</li>
     * </ul>
     */
    public enum AgentStatusType
    {
        /**
         * Status monitoring via agent-initiated heartbeat signals
         */
        HEARTBEAT("Heartbeat"),

        /**
         * Status monitoring via server-initiated ping requests
         */
        PING("Ping");

        /**
         * Map for efficient lookup of enum values by name
         */
        private static final Map<String, AgentStatusType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AgentStatusType::getName, e -> e)));

        /**
         * The string name of the status type
         */
        private final String name;

        /**
         * Constructor for AgentStatusType enum.
         *
         * @param name The string name of the status type
         */
        AgentStatusType(String name)
        {
            this.name = name;
        }

        /**
         * Gets an AgentStatusType enum value by its string name.
         *
         * @param name The string name to look up
         * @return The corresponding AgentStatusType enum value, or null if not found
         */
        public static AgentStatusType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the string name of this status type.
         *
         * @return The string name
         */
        public String getName()
        {
            return name;
        }
    }
}
