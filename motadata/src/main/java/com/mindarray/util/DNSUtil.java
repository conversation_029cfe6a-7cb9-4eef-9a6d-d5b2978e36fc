/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.DNSCacheStore;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.dns.DnsClient;
import io.vertx.core.dns.DnsClientOptions;
import io.vertx.core.json.JsonObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;

public class DNSUtil extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DNSUtil.class, GlobalConstants.MOTADATA_UTIL, "DNS Util");

    private DnsClient dnsClient = null;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            dnsClient = vertx.createDnsClient(new DnsClientOptions().setRecursionDesired(true));

            vertx.eventBus().<String>localConsumer(EventBusConstants.EVENT_DNS_REVERSE_LOOKUP, message -> dnsClient.reverseLookup(message.body(), result ->
            {

                if (result.succeeded())
                {
                    message.reply(result.result());

                    if (result.result() != null)
                    {
                        Bootstrap.configDBService().save(DBConstants.TBL_DNS_RECORD, new JsonObject().put("ip", message.body()).put("host", result.result()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                        {
                        });

                    }
                }
                else
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, result.cause().getMessage());

                    LOGGER.warn(String.format("failed to perform reverse lookup for %s", message.body()));

                    LOGGER.error(result.cause());
                }

            })).exceptionHandler(LOGGER::error);


            vertx.eventBus().<String>localConsumer(EventBusConstants.EVENT_DNS_LOOKUP, message -> dnsClient.lookup(message.body(), result ->
            {

                if (result.succeeded())
                {
                    message.reply(result.result());

                    if (result.result() != null)
                    {
                        Bootstrap.configDBService().save(DBConstants.TBL_DNS_RECORD, new JsonObject().put("ip", result.result()).put("host", message.body()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                        {
                        });

                    }
                }
                else
                {
                    message.fail(GlobalConstants.NOT_AVAILABLE, result.cause().getMessage());

                    LOGGER.warn(String.format("failed to perform lookup for %s", message.body()));

                    LOGGER.error(result.cause());
                }

            })).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_CACHE_FLUSH, message ->
                    vertx.executeBlocking(future ->
                    {
                        try
                        {
                            var arguments = new ArrayList<String>();

                            arguments.add("bash");

                            arguments.add("-c");

                            arguments.add("sudo systemd-resolve --flush-caches");

                            var processBuilder = new ProcessBuilder(arguments);

                            processBuilder.directory(new File(CURRENT_DIR + PATH_SEPARATOR));

                            var process = processBuilder.start();

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("DNS cache flush process %s started", process.pid()));
                            }

                            var output = new StringBuilder();

                            var bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));

                            String line;

                            while ((line = bufferedReader.readLine()) != null)
                            {
                                output.append(line).append(NEW_LINE);
                            }

                            bufferedReader.close();

                            process.getErrorStream().close();

                            process.getOutputStream().close();

                            process.getInputStream().close();

                            process.destroy();

                            if (!output.isEmpty()) //means getting some error so will not clear records
                            {
                                future.fail("Failed to flush DNS cache.....");

                                LOGGER.warn("Failed to flush DNS cache.....");
                            }
                            else
                            {
                                Bootstrap.configDBService().drop(DBConstants.TBL_DNS_RECORD, result ->
                                {
                                    if (result.succeeded())
                                    {
                                        DNSCacheStore.getStore().clear();

                                        LOGGER.info("DNS records flushed successfully...");

                                        future.complete();
                                    }
                                    else
                                    {
                                        LOGGER.error(result.cause());

                                        future.fail(result.cause().getMessage());
                                    }
                                });
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            future.fail(exception);
                        }
                    }, false, result ->
                    {
                    })).exceptionHandler(LOGGER::error);

            promise.complete();
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }
}
