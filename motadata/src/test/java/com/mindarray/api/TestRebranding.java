/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.RebrandingConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

import static com.mindarray.TestAPIConstants.REBRANDING_API_ENDPOINT;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestRebranding
{

    private static final Logger LOGGER = new Logger(TestRebranding.class, GlobalConstants.MOTADATA_API, "Rebranding Test");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testUpdateRebrandingLogo(VertxTestContext testContext) throws Exception
    {
        var item = RebrandingConfigStore.getStore().getItem();

        Assertions.assertNotNull(item);

        var uuid = UUID.randomUUID().toString();

        FileUtils.writeStringToFile(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + uuid), "Test " + System.currentTimeMillis(), StandardCharsets.UTF_8);

        TestAPIUtil.put(REBRANDING_API_ENDPOINT + "/" + item.getLong(GlobalConstants.ID),
                item.put(Rebranding.REBRANDING_LOGO_NAME, "Motadata").put(Rebranding.REBRANDING_LOGO, uuid), testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            Assertions.assertEquals(200, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

                            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetClientLogo(VertxTestContext testContext)
    {
        TestAPIUtil.get("/client-logo", testContext.succeeding(response ->
        {
            var body = response.bodyAsJsonObject();

            Assertions.assertEquals(200, body.getInteger(APIConstants.RESPONSE_CODE));

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Motadata", body.getJsonObject(GlobalConstants.RESULT).getString(Rebranding.REBRANDING_LOGO_NAME));

            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

            testContext.completeNow();

        }));
    }

    //#24338
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateRebrandingLogoDarkTheme(VertxTestContext testContext) throws Exception
    {
        var item = RebrandingConfigStore.getStore().getItem();

        Assertions.assertNotNull(item);

        var uuid = UUID.randomUUID().toString();

        FileUtils.writeStringToFile(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + uuid), "Test " + System.currentTimeMillis(), StandardCharsets.UTF_8);

        TestAPIUtil.put(REBRANDING_API_ENDPOINT + "/" + item.getLong(GlobalConstants.ID),
                item.put(Rebranding.REBRANDING_DARK_LOGO_NAME, "Motadata_dark").put(Rebranding.REBRANDING_DARK_LOGO, uuid), testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var body = response.bodyAsJsonObject();

                            LOGGER.info(String.format("API response : %s ", body.encode()));

                            Assertions.assertEquals(200, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

                            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetClientLogoDarkTheme(VertxTestContext testContext)
    {
        TestAPIUtil.get("/client-logo", testContext.succeeding(response ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.info(String.format("API response : %s ", body.encode()));

            Assertions.assertEquals(HttpStatus.SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertEquals("Motadata_dark", body.getJsonObject(GlobalConstants.RESULT).getString(Rebranding.REBRANDING_DARK_LOGO_NAME));

            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

            testContext.completeNow();

        }));

    }
}
