/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.GlobalConstants;
import com.mindarray.api.PasswordPolicy;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.ActiveUserCacheStore;
import com.mindarray.store.PasswordPolicyConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.Duration;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.LocalDateTime;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.*;
import static com.mindarray.db.DBConstants.ENTITY_TYPE_USER;
import static com.mindarray.db.DBConstants.FIELD_TYPE;

/**
 * The SystemNotificationJob class is responsible for sending system notifications in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs regular checks for conditions that
 * require user notifications, such as expiring passwords and licenses. It sends notifications
 * through the UI and via email to ensure that users are aware of important system events.
 * <p>
 * The job performs the following tasks:
 * <ol>
 *   <li>Verifying password expiry and sending notifications to users</li>
 *   <li>Verifying license expiry and sending notifications to administrators</li>
 * </ol>
 * <p>
 * The job is scheduled to run every 3 hours to ensure timely notifications. Email notifications
 * for password expiry are only sent at midnight to avoid sending too many emails.
 */
public class SystemNotificationJob implements Job
{
    /**
     * CRON expression for scheduling this job to run every 3 hours
     */
    public static final String NOTIFICATION_JOB_CRON_EXPRESSION = "0 0 */3 ? * *";

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(SystemNotificationJob.class, GlobalConstants.MOTADATA_JOB, "System Notification Job");

    /**
     * URL of the Motadata platform web interface.
     * <p>
     * This URL is used in email notifications to provide users with a link to the platform.
     * It's constructed based on the system configuration (HTTP/HTTPS, host, port).
     */
    private static final String URL = (MotadataConfigUtil.httpsEnabled() ? "https" : "http") + "://" + MotadataConfigUtil.getHost() + COLON_SEPARATOR + MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name());

    /**
     * Executes the system notification job.
     * <p>
     * This method performs the following operations:
     * <ol>
     *   <li>Verifies password expiry and sends notifications to users</li>
     *   <li>Verifies license expiry and sends notifications to administrators</li>
     * </ol>
     * <p>
     * Email notifications for password expiry are only sent at midnight (when hour and minute are both 0)
     * to avoid sending too many emails.
     *
     * @param jobExecutionContext the context in which the job is executed
     * @throws JobExecutionException if an error occurs during job execution
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException
    {
        try
        {
            // Verify password expiry and send notifications
            // Email notifications are only sent at midnight (when hour and minute are both 0)
            verifyPasswordExpiry(LocalDateTime.now().getHour() == 0 && LocalDateTime.now().getMinute() == 0);

            // Verify license expiry and send notifications to administrators
            verifyLicenseExpiry();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Verifies password expiry and sends notifications to users.
     * <p>
     * This method checks for users whose passwords are expiring within 3 days and sends
     * notifications to them. It sends UI notifications to all active users and can also
     * send email notifications if the sendMail parameter is true.
     * <p>
     * The method performs the following operations:
     * <ol>
     *   <li>Gets all active users from the cache store</li>
     *   <li>Gets the password policy configuration</li>
     *   <li>For each active user, calculates the remaining days until password expiry</li>
     *   <li>Sends UI notifications to users whose passwords are expiring within 3 days</li>
     *   <li>If sendMail is true, sends email notifications to users whose passwords are expiring within 3 days</li>
     * </ol>
     *
     * @param sendMail true to send email notifications, false to only send UI notifications
     */
    private void verifyPasswordExpiry(boolean sendMail)
    {
        try
        {
            // Get all active users from the cache store
            var items = ActiveUserCacheStore.getStore().getItems();

            // Get the password policy configuration
            var item = PasswordPolicyConfigStore.getStore().getItem();

            // Send UI notifications to active users whose passwords are expiring
            for (var entry : items.entrySet())
            {
                // Get the user from the store
                var user = UserConfigStore.getStore().getItem(CommonUtil.getLong(entry.getValue().getValue(User.USER_ID)));

                // Check if the user is valid, password expiry is enabled, and the user is a system user
                if (user != null && !user.isEmpty() &&
                        item.getString(PasswordPolicy.PASSWORD_POLICY_EXPIRY).equalsIgnoreCase(YES) &&
                        user.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM) &&
                        user.getString(FIELD_TYPE).equalsIgnoreCase(ENTITY_TYPE_USER))
                {
                    // Calculate the remaining days until password expiry
                    // 86400000L = number of milliseconds in a day
                    var remainingDays = item.getInteger(PasswordPolicy.PASSWORD_POLICY_EXPIRY_DAYS) -
                            (System.currentTimeMillis() - user.getLong(USER_PASSWORD_LAST_UPDATED_TIME)) / 86400000L;

                    // If the password is expiring within 3 days, send a UI notification
                    if (remainingDays <= 3)
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("%s user password is expiring in %s days", user.getString(USER_NAME), remainingDays));
                        }

                        // Send notification to the UI
                        EventBusConstants.publish(entry.getKey(),
                                EventBusConstants.UI_NOTIFICATION_PASSWORD_EXPIRE,
                                new JsonObject().put("remaining.days", remainingDays));
                    }
                }
            }

            // Send email notifications if requested (typically only at midnight)
            if (sendMail)
            {
                // Get all users from the store (not just active ones)
                var users = UserConfigStore.getStore().getItems();

                // Process each user
                for (var index = 0; index < users.size(); index++)
                {
                    var user = users.getJsonObject(index);

                    // Check if the user is valid, password expiry is enabled, and the user is a system user
                    if (item.getString(PasswordPolicy.PASSWORD_POLICY_EXPIRY).equalsIgnoreCase(YES) &&
                            user.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM) &&
                            user.getString(FIELD_TYPE).equalsIgnoreCase(ENTITY_TYPE_USER))
                    {
                        // Calculate the remaining days until password expiry
                        var remainingDays = item.getInteger(PasswordPolicy.PASSWORD_POLICY_EXPIRY_DAYS) -
                                (System.currentTimeMillis() - user.getLong(USER_PASSWORD_LAST_UPDATED_TIME)) / 86400000L;

                        // If the password is expiring within 3 days and the user has an email address, send an email
                        if (remainingDays <= 3 && user.getValue(User.USER_EMAIL) != null)
                        {
                            // Send email notification with appropriate template and subject
                            Notification.sendEmail(new JsonObject()
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS,
                                            new JsonArray()
                                                    .add(remainingDays > 0 ? "expiring.png" : "expired.png")
                                                    .addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT,
                                            remainingDays > 0 ? "Attention - Your Password is about to Expire" :
                                                    "Attention - Your Password has Expired")
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS,
                                            new JsonArray().add(user.getString(User.USER_EMAIL)))
                                    .put(Notification.TEMPLATE_NAME, remainingDays > 0 ? Notification.EMAIL_NOTIFICATION_PASSWORD_ABOUT_TO_EXPIRE_HTML_TEMPLATE : Notification.EMAIL_NOTIFICATION_PASSWORD_EXPIRED_HTML_TEMPLATE)
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, user.put("remaining.days", remainingDays).put("link", URL)));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Verifies license expiry and sends notifications to administrators.
     * <p>
     * This method checks if the license is expiring at specific intervals (30, 15, 7, 3, 1, 0, or -1 days)
     * and sends UI notifications to administrators. This ensures that administrators are aware of
     * upcoming license expiry and can take appropriate action.
     * <p>
     * The method performs the following operations:
     * <ol>
     *   <li>Gets all active users from the cache store</li>
     *   <li>Calculates the remaining days until license expiry</li>
     *   <li>If the remaining days matches one of the notification intervals, sends UI notifications to administrators</li>
     * </ol>
     */
    private void verifyLicenseExpiry()
    {
        try
        {
            // Get all active users from the cache store
            var items = ActiveUserCacheStore.getStore().getItems();

            // Calculate the remaining days until license expiry
            var remainingDays = Duration.millis((LicenseUtil.LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds())).toStandardDays().getDays();

            // If the license has already expired, set remainingDays to -1
            if (LicenseUtil.LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds() < 0)
            {
                remainingDays = -1;
            }

            // Send notifications at specific intervals (30, 15, 7, 3, 1, 0, or -1 days)
            if (remainingDays == 30 || remainingDays == 15 || remainingDays == 7 || remainingDays == 3 ||
                    remainingDays == 1 || remainingDays == 0 || remainingDays == -1)
            {
                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("license is expiring in %s days", remainingDays));
                }

                // Process each active user
                for (var entry : items.entrySet())
                {
                    // Get the user from the store
                    var item = UserConfigStore.getStore().getItem(CommonUtil.getLong(entry.getValue().getValue(User.USER_ID)));

                    // Send notifications only to users with admin role (role ID = DEFAULT_ID)
                    if (item != null && !item.isEmpty() && item.getLong(USER_ROLE).equals(CommonUtil.getLong(DEFAULT_ID)))
                    {
                        // Send notification to the UI
                        EventBusConstants.publish(entry.getKey(),
                                EventBusConstants.UI_NOTIFICATION_LICENSE_EXPIRE,
                                new JsonObject().put("remaining.days", remainingDays));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
