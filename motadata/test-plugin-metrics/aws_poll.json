{"127.0.0.1": {"4": {"cloud.active.services": {"AWS AutoScaling": "ap-south-1", "AWS CloudFront": "Global", "AWS DynamoDB": "ap-south-1,us-west-1", "AWS EBS": "ap-south-1", "AWS EC2": "ap-south-1", "AWS ELB": "ap-south-1", "AWS ElasticBeanstalk": "ap-south-1", "AWS Lambda": "us-east-1,ap-south-1", "AWS RDS": "ap-south-1", "AWS S3": "Global", "AWS SNS": "us-east-1,ap-south-1,us-west-1", "AWS SQS": "us-east-1,ap-south-1"}}, "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "errors": [], "metric.plugin.id": 4, "object.account.id": "************", "status": "succeed", "result": {"aws.application.elb.instances": 1, "aws.autoscaling": [{"aws.autoscaling": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "aws.autoscaling.health.check.type": "EC2", "aws.availability.zone": " ap-south-1b, ap-south-1a", "aws.instance.creation.time": " 356 days 19 hours 53 minutes 38 seconds", "aws.instance.creation.time.seconds": ********, "aws.region": "ap-south-1", "aws.service": "awseb-e-kypxnsq6kf-stack-AWSEBAutoScalingGroup-L61D4U4K90I0(ap-south-1)", "aws.service.type": "AWS AutoScaling"}], "aws.autoscaling.groups": 1, "aws.cloudfront": [{"aws.cloudfront": "E1RVVPM08N16XT(Global)", "aws.cloudfront.domain.name": "ddmxnfvn1ssd3.cloudfront.net", "aws.cloudfront.http.version": "", "aws.cloudfront.ipv6.enabled": "true", "aws.cloudfront.last.modified.time": " 356 days 19 hours 56 minutes 22 seconds", "aws.region": "Global", "aws.service": "E1RVVPM08N16XT(Global)", "aws.service.type": "Amazon CloudFront", "aws.status": "Enabled"}], "aws.cloudfront.services": 1, "aws.dynamodb": [{"aws.dynamodb": "motadata8-dynamoDB(ap-south-1)", "aws.dynamodb.db.name": "motadata8-dynamoDB", "aws.dynamodb.table.size.bytes": 24, "aws.instance.creation.time": " 812 days 22 hours 46 minutes 1 seconds", "aws.instance.creation.time.seconds": 70238761, "aws.region": "ap-south-1", "aws.service": "motadata8-dynamoDB(ap-south-1)", "aws.service.type": "Amazon DynamoDB", "aws.status": "ACTIVE"}, {"aws.dynamodb": "testdb(us-west-1)", "aws.dynamodb.db.name": "testdb", "aws.dynamodb.table.size.bytes": 0, "aws.instance.creation.time": " 811 days 16 hours 58 minutes 9 seconds", "aws.instance.creation.time.seconds": 70131489, "aws.region": "us-west-1", "aws.service": "testdb(us-west-1)", "aws.service.type": "Amazon DynamoDB", "aws.status": "ACTIVE"}], "aws.dynamodb.tables": 2, "aws.ebs": [{"aws.availability.zone": "ap-south-1b", "aws.ebs": "vol-033a217b9b307a8fe(ap-south-1)", "aws.ebs.volume.attached.time": " 812 days 22 hours 53 minutes 54 seconds", "aws.ebs.volume.attached.time.sec": 70239234, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 812 days 22 hours 53 minutes 54 seconds", "aws.ebs.volume.creation.time.seconds": 70239234, "aws.ebs.volume.ec2.instance.id": "i-08aca528c807a46f0", "aws.ebs.volume.size.bytes": 8589934592, "aws.ebs.volume.snapshot.id": "snap-0c635284a54ad3364", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-033a217b9b307a8fe(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}, {"aws.availability.zone": "ap-south-1b", "aws.ebs": "vol-0537e607fc8a15359(ap-south-1)", "aws.ebs.volume.attached.time": " 69 days 16 hours 58 minutes 28 seconds", "aws.ebs.volume.attached.time.sec": 6022708, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 69 days 16 hours 58 minutes 28 seconds", "aws.ebs.volume.creation.time.seconds": 6022708, "aws.ebs.volume.ec2.instance.id": "i-05ed31c26565ddbd1", "aws.ebs.volume.size.bytes": 53687091200, "aws.ebs.volume.snapshot.id": "snap-0ed7eb835e8501dfa", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-0537e607fc8a15359(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}, {"aws.availability.zone": "ap-south-1c", "aws.ebs": "vol-097a8109cf74546e3(ap-south-1)", "aws.ebs.volume.attached.time": " 32 days 19 hours 13 minutes 14 seconds", "aws.ebs.volume.attached.time.sec": 2833994, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 32 days 19 hours 13 minutes 14 seconds", "aws.ebs.volume.creation.time.seconds": 2833994, "aws.ebs.volume.ec2.instance.id": "i-05c90b35d30b9d3d6", "aws.ebs.volume.size.bytes": 42949672960, "aws.ebs.volume.snapshot.id": "snap-0157b90ba1f335bec", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-097a8109cf74546e3(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}, {"aws.availability.zone": "ap-south-1a", "aws.ebs": "vol-0ef1cdd875c16187e(ap-south-1)", "aws.ebs.volume.attached.time": " 804 days 15 hours 48 minutes 25 seconds", "aws.ebs.volume.attached.time.sec": 69522505, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 804 days 15 hours 48 minutes 25 seconds", "aws.ebs.volume.creation.time.seconds": 69522505, "aws.ebs.volume.ec2.instance.id": "i-0a851d6b9a9193f96", "aws.ebs.volume.size.bytes": 8589934592, "aws.ebs.volume.snapshot.id": "snap-0c635284a54ad3364", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-0ef1cdd875c16187e(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}, {"aws.availability.zone": "ap-south-1a", "aws.ebs": "vol-0a2978c982278b5ad(ap-south-1)", "aws.ebs.volume.attached.time": " 496 days 15 hours 24 minutes 0 second", "aws.ebs.volume.attached.time.sec": 42909840, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 496 days 15 hours 24 minutes 0 second", "aws.ebs.volume.creation.time.seconds": 42909840, "aws.ebs.volume.ec2.instance.id": "i-0c1ede17541c33a71", "aws.ebs.volume.size.bytes": 107374182400, "aws.ebs.volume.snapshot.id": "snap-049cf278783d5f5ba", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-0a2978c982278b5ad(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}, {"aws.availability.zone": "ap-south-1a", "aws.ebs": "vol-0da9c0644d6c8fd92(ap-south-1)", "aws.ebs.volume.attached.time": " 356 days 19 hours 53 minutes 30 seconds", "aws.ebs.volume.attached.time.sec": 30830010, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 356 days 19 hours 53 minutes 30 seconds", "aws.ebs.volume.creation.time.seconds": 30830010, "aws.ebs.volume.ec2.instance.id": "i-015017f0d4b79fca2", "aws.ebs.volume.size.bytes": 8589934592, "aws.ebs.volume.snapshot.id": "snap-08bf252c640b45beb", "aws.ebs.volume.type": "gp2", "aws.region": "ap-south-1", "aws.service": "vol-0da9c0644d6c8fd92(ap-south-1)", "aws.service.type": "Amazon EBS", "aws.state": "in-use"}], "aws.ebs.volumes": 6, "aws.ec2": [{"aws.availability.zone": "ap-south-1a", "aws.ec2": "windows-wsus(ap-south-1)", "aws.ec2.instance.id": "i-0c1ede17541c33a71", "aws.ec2.instance.type": "t3a.xlarge", "aws.ec2.monitoring": "disabled", "aws.ec2.public.dns.name": "ec2-13-126-137-173.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "**************", "aws.region": "ap-south-1", "aws.service": "windows-wsus(ap-south-1)", "aws.service.type": "Amazon EC2", "aws.state": "running", "status": "Up"}, {"aws.availability.zone": "ap-south-1a", "aws.ec2": "Sampleebs-env(ap-south-1)", "aws.ec2.instance.id": "i-015017f0d4b79fca2", "aws.ec2.instance.type": "t2.micro", "aws.ec2.monitoring": "disabled", "aws.ec2.public.dns.name": "ec2-3-108-60-205.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "************", "aws.region": "ap-south-1", "aws.service": "Sampleebs-env(ap-south-1)", "aws.service.type": "Amazon EC2", "aws.state": "running", "status": "Up"}, {"aws.availability.zone": "ap-south-1c", "aws.ec2": "serviceops_test_saas(ap-south-1)", "aws.ec2.instance.id": "i-05c90b35d30b9d3d6", "aws.ec2.instance.type": "t3a.medium", "aws.ec2.monitoring": "disabled", "aws.ec2.public.dns.name": "ec2-65-2-64-109.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "***********", "aws.region": "ap-south-1", "aws.service": "serviceops_test_saas(ap-south-1)", "aws.service.type": "Amazon EC2", "aws.state": "running", "status": "Up"}, {"aws.availability.zone": "ap-south-1b", "aws.ec2": "serviceops-saas(ap-south-1)", "aws.ec2.instance.id": "i-05ed31c26565ddbd1", "aws.ec2.instance.type": "t3a.medium", "aws.ec2.monitoring": "disabled", "aws.ec2.public.dns.name": "ec2-3-109-115-213.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "*************", "aws.region": "ap-south-1", "aws.service": "serviceops-saas(ap-south-1)", "aws.service.type": "Amazon EC2", "aws.state": "running", "status": "Up"}], "aws.ec2.instances": 4, "aws.elasticbeanstalk": [{"aws.elasticbeanstalk": "Sampleebs-env(ap-south-1)", "aws.elasticbeanstalk.creation.time": " 356 days 19 hours 54 minutes 13 seconds", "aws.elasticbeanstalk.creation.time.seconds": 30830053, "aws.elasticbeanstalk.environment.health.status": "Ok", "aws.elasticbeanstalk.environment.id": "e-kypxnsq6kf", "aws.elasticbeanstalk.solution.stack": "64bit Amazon Linux 2 v3.3.1 running Python 3.7", "aws.elasticbeanstalk.state": "Ready", "aws.region": "ap-south-1", "aws.service": "Sampleebs-env(ap-south-1)", "aws.service.type": "AWS ElasticBeanstalk"}], "aws.elasticbeanstalk.environments": 1, "aws.elb": [{"aws.availability.zone": "ap-south-1aap-south-1b", "aws.elb": "networkelb(ap-south-1)", "aws.elb.instance.created.time": " 355 days 20 hours 34 minutes 35 seconds", "aws.elb.instance.created.time.sec": 30746075, "aws.elb.type": "network", "aws.elb.vpc.id": "vpc-b9e834d0", "aws.region": "ap-south-1", "aws.service": "networkelb(ap-south-1)", "aws.service.type": "AWS ELB", "aws.state": "active"}, {"aws.availability.zone": "ap-south-1cap-south-1bap-south-1a", "aws.elb": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "aws.elb.instance.created.time": " 356 days 19 hours 53 minutes 53 seconds", "aws.elb.instance.created.time.sec": 30830033, "aws.elb.type": "application", "aws.elb.vpc.id": "vpc-b9e834d0", "aws.region": "ap-south-1", "aws.service": "awseb-AWSEB-1IM1CO0A9EMG1(ap-south-1)", "aws.service.type": "AWS ELB", "aws.state": "active"}], "aws.elb.instances": 2, "aws.lambda": [{"aws.lambda": "aaalamda(us-east-1)", "aws.lambda.code.size.bytes": 269, "aws.lambda.memory.size.bytes": 134217728, "aws.lambda.revision.id": "786c7635-b027-4d98-8664-365cbebb4c4e", "aws.lambda.role": "arn:aws:iam::************:role/service-role/aaalamda-role-1la3o6kx", "aws.lambda.runtime.environment": "python3.7", "aws.lambda.version": "$LATEST", "aws.region": "us-east-1", "aws.service": "aaalamda(us-east-1)", "aws.service.type": "AWS Lambda"}, {"aws.lambda": "testlambda(ap-south-1)", "aws.lambda.code.size.bytes": 274, "aws.lambda.memory.size.bytes": 134217728, "aws.lambda.revision.id": "b2b30160-f377-47fa-aeb6-e0ecc31a17e1", "aws.lambda.role": "arn:aws:iam::************:role/service-role/testlambda-role-lk2btmd5", "aws.lambda.runtime.environment": "python2.7", "aws.lambda.version": "$LATEST", "aws.region": "ap-south-1", "aws.service": "testlambda(ap-south-1)", "aws.service.type": "AWS Lambda"}], "aws.lambda.functions": 2, "aws.network.elb.instances": 1, "aws.rds": [{"aws.availability.zone": "ap-south-1b", "aws.rds": "database-1-instance-1(ap-south-1)", "aws.rds.allocated.storage.bytes": 1073741824, "aws.rds.db.engine.name": "aurora-postgresql", "aws.rds.instance.id": "db-F2UYMHDFR32T7YRWBMNF3LXR4A", "aws.region": "ap-south-1", "aws.service": "database-1-instance-1(ap-south-1)", "aws.service.type": "Amazon RDS", "aws.status": "available"}, {"aws.availability.zone": "ap-south-1c", "aws.rds": "devauroraitsm-instance-1(ap-south-1)", "aws.rds.allocated.storage.bytes": 1073741824, "aws.rds.db.engine.name": "aurora-postgresql", "aws.rds.instance.id": "db-KONGJFZZMLINKJRYFIJPYAVODY", "aws.region": "ap-south-1", "aws.service": "devauroraitsm-instance-1(ap-south-1)", "aws.service.type": "Amazon RDS", "aws.status": "available"}, {"aws.availability.zone": "ap-south-1b", "aws.rds": "motadata(ap-south-1)", "aws.rds.allocated.storage.bytes": 21474836480, "aws.rds.db.engine.name": "mysql", "aws.rds.instance.id": "db-UTOCIB3E5QIJ2AOXZ4LEQZOSSM", "aws.region": "ap-south-1", "aws.service": "motadata(ap-south-1)", "aws.service.type": "Amazon RDS", "aws.status": "available"}, {"aws.availability.zone": "ap-south-1b", "aws.rds": "serviceops-postgres(ap-south-1)", "aws.rds.allocated.storage.bytes": 21474836480, "aws.rds.db.engine.name": "postgres", "aws.rds.instance.id": "db-SUNYHVS6OYMQDR6W5CWBI454LA", "aws.region": "ap-south-1", "aws.service": "serviceops-postgres(ap-south-1)", "aws.service.type": "Amazon RDS", "aws.status": "available"}], "aws.rds.instances": 4, "aws.running.ec2.instances": 4, "aws.s3": [{"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "awsplugintests3(Global)", "aws.s3.bucket.creation.time": " 810 days 13 hours 53 minutes 26 seconds", "aws.s3.bucket.creation.time.seconds": 70034006, "aws.service": "awsplugintests3(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "docs.motadata.com(Global)", "aws.s3.bucket.creation.time": " 1317 days 21 hours 9 minutes 17 seconds", "aws.s3.bucket.creation.time.seconds": 113864957, "aws.service": "docs.motadata.com(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "elasticbeanstalk-ap-south-1-************(Global)", "aws.s3.bucket.creation.time": " 434 days 22 hours 9 minutes 28 seconds", "aws.s3.bucket.creation.time.seconds": 37577368, "aws.service": "elasticbeanstalk-ap-south-1-************(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "us-east-1", "aws.region": "Global", "aws.s3": "elasticbeanstalk-us-east-1-************(Global)", "aws.s3.bucket.creation.time": " 368 days 22 hours 55 minutes 53 seconds", "aws.s3.bucket.creation.time.seconds": 31877753, "aws.service": "elasticbeanstalk-us-east-1-************(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "itsm-docs.motadata.com(Global)", "aws.s3.bucket.creation.time": " 1266 days 21 hours 37 minutes 27 seconds", "aws.s3.bucket.creation.time.seconds": 109460247, "aws.service": "itsm-docs.motadata.com(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "mindarray(Global)", "aws.s3.bucket.creation.time": " 2052 days 17 hours 55 minutes 50 seconds", "aws.s3.bucket.creation.time.seconds": 177357350, "aws.service": "mindarray(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "motadata-common(Global)", "aws.s3.bucket.creation.time": " 1140 days 22 hours 42 minutes 46 seconds", "aws.s3.bucket.creation.time.seconds": 98577766, "aws.service": "motadata-common(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "us-east-1", "aws.region": "Global", "aws.s3": "motadata2016(Global)", "aws.s3.bucket.creation.time": " 2052 days 17 hours 54 minutes 47 seconds", "aws.s3.bucket.creation.time.seconds": 177357287, "aws.service": "motadata2016(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "us-east-1", "aws.region": "Global", "aws.s3": "motadataserviceops(Global)", "aws.s3.bucket.creation.time": " 557 days 19 hours 8 minutes 36 seconds", "aws.s3.bucket.creation.time.seconds": 48193716, "aws.service": "motadataserviceops(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "serviceopsdiscoverytestserviceopstest(Global)", "aws.s3.bucket.creation.time": " 14 days 19 hours 13 minutes 13 seconds", "aws.s3.bucket.creation.time.seconds": 1278793, "aws.service": "serviceopsdiscoverytestserviceopstest(Global)", "aws.service.type": "Amazon S3"}, {"aws.location": "ap-south-1", "aws.region": "Global", "aws.s3": "serviceopstestbucket(Global)", "aws.s3.bucket.creation.time": " 39 days 17 hours 7 minutes 42 seconds", "aws.s3.bucket.creation.time.seconds": 3431262, "aws.service": "serviceopstestbucket(Global)", "aws.service.type": "Amazon S3"}], "aws.s3.buckets": 11, "aws.sns": [{"aws.region": "us-east-1", "aws.service": "MyTopic(us-east-1)", "aws.service.type": "Amazon SNS", "aws.sns": "MyTopic(us-east-1)", "aws.sns.subscriptions": 1}, {"aws.region": "ap-south-1", "aws.service": "MyTopic(ap-south-1)", "aws.service.type": "Amazon SNS", "aws.sns": "MyTopic(ap-south-1)", "aws.sns.subscriptions": 3}, {"aws.region": "ap-south-1", "aws.service": "TestTopic2(ap-south-1)", "aws.service.type": "Amazon SNS", "aws.sns": "TestTopic2(ap-south-1)"}, {"aws.region": "ap-south-1", "aws.service": "dynamodb(ap-south-1)", "aws.service.type": "Amazon SNS", "aws.sns": "dynamodb(ap-south-1)", "aws.sns.subscriptions": 2}, {"aws.region": "ap-south-1", "aws.service": "testsns(ap-south-1)", "aws.service.type": "Amazon SNS", "aws.sns": "testsns(ap-south-1)", "aws.sns.subscriptions": 2}, {"aws.region": "us-west-1", "aws.service": "dynamodb(us-west-1)", "aws.service.type": "Amazon SNS", "aws.sns": "dynamodb(us-west-1)", "aws.sns.subscriptions": 1}], "aws.sns.topics": 6, "aws.sqs": [{"aws.region": "us-east-1", "aws.service": "qsq(us-east-1)", "aws.service.type": "Amazon SQS", "aws.sqs": "qsq(us-east-1)"}, {"aws.region": "ap-south-1", "aws.service": "sqs(ap-south-1)", "aws.service.type": "Amazon SQS", "aws.sqs": "sqs(ap-south-1)"}], "aws.sqs.queues": 2, "aws.stopped.ec2.instances": 0, "aws.vpc": [{"aws.region": "us-east-2", "aws.service": "12(us-east-2)", "aws.service.type": "AWS VPC", "aws.vpc": "12(us-east-2)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-1d32c674", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-northeast-3", "aws.service": "12(ap-northeast-3)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-northeast-3)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-f49fed9d", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "us-west-2", "aws.service": "12(us-west-2)", "aws.service.type": "AWS VPC", "aws.vpc": "12(us-west-2)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-5fc8a03b", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-south-1", "aws.service": "12(ap-south-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-south-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-398a5250", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-south-1", "aws.service": "21(ap-south-1)", "aws.service.type": "AWS VPC", "aws.vpc": "21(ap-south-1)", "aws.vpc.cidr.block": "172.16.10.0/24", "aws.vpc.dhcp.options.id": "dopt-398a5250", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "no", "aws.vpc.state": "available"}, {"aws.region": "ap-northeast-2", "aws.service": "12(ap-northeast-2)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-northeast-2)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-7cabc215", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "us-west-1", "aws.service": "12(us-west-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(us-west-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-d4e2c5b1", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ca-central-1", "aws.service": "12(ca-central-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ca-central-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-8d3bf2e4", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "eu-west-3", "aws.service": "12(eu-west-3)", "aws.service.type": "AWS VPC", "aws.vpc": "12(eu-west-3)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-e2a2be8b", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "eu-north-1", "aws.service": "12(eu-north-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(eu-north-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-c0ab75a9", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "eu-west-1", "aws.service": "12(eu-west-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(eu-west-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-5edf313a", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "sa-east-1", "aws.service": "12(sa-east-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(sa-east-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-c3d490a6", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-northeast-1", "aws.service": "12(ap-northeast-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-northeast-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-95ebd9f0", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "eu-west-2", "aws.service": "12(eu-west-2)", "aws.service.type": "AWS VPC", "aws.vpc": "12(eu-west-2)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-c475b9ad", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-southeast-1", "aws.service": "12(ap-southeast-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-southeast-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-8f3d23ea", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "us-east-1", "aws.service": "12(us-east-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(us-east-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-efad968b", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "ap-southeast-2", "aws.service": "12(ap-southeast-2)", "aws.service.type": "AWS VPC", "aws.vpc": "12(ap-southeast-2)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-d784a4b2", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}, {"aws.region": "eu-central-1", "aws.service": "12(eu-central-1)", "aws.service.type": "AWS VPC", "aws.vpc": "12(eu-central-1)", "aws.vpc.cidr.block": "**********/16", "aws.vpc.dhcp.options.id": "dopt-fb241292", "aws.vpc.instance.tenancy": "default", "aws.vpc.is.default": "yes", "aws.vpc.state": "available"}], "aws.vpc.instances": 18}}}