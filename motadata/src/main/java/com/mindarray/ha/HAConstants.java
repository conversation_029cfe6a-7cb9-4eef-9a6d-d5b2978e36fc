/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */

package com.mindarray.ha;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_TABLE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;

public final class HAConstants
{
    public static final String CACHE_NAME = "cache.name";
    public static final String HA_MODE = "ha.mode";
    public static final String HA_SYNC_OPERATION = "ha.sync.operation";
    public static final String HA_SYNC_TYPE = "ha.sync.type";
    public static final String OBSERVER_CACHE = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "observer-cache";

    private static final Logger LOGGER = new Logger(HAManager.class, MOTADATA_HA, "HA Constants");


    private HAConstants()
    {
    }

    // sync config table with record only...for save and save all operations
    public static void sync(String table, JsonArray records, String user, byte operation, String database)
    {
        if (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()))
        {
            var timestamp = DateTimeUtil.currentSeconds();

            var buffer = Buffer.buffer("UTF-8");

            var bytes = new JsonObject().put(ENTITY_TABLE, table).put(DATABASE, database).put(HA_SYNC_OPERATION, operation).put(USER_NAME, user).put(REMOTE_ADDRESS, MOTADATA_SYSTEM).put(DBConstants.DB_RECORDS, records).encode().getBytes();

            buffer.setLongLE(0, timestamp).appendIntLE(bytes.length).appendBytes(bytes);

            Bootstrap.vertx().eventBus().send(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + Bootstrap.getRegistrationId(), new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(EVENT_CONTEXT, buffer.getBytes()));
        }
    }

    // sync config table with query and record both...
    public static void sync(String table, JsonObject query, JsonObject record, String user, String remoteIP, byte operation, String database)
    {
        if (Bootstrap.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()))
        {
            var context = new JsonObject();

            var timestamp = DateTimeUtil.currentSeconds();

            var buffer = Buffer.buffer("UTF-8");

            buffer.setLongLE(0, timestamp);

            if (query != null)
            {
                context.put(DBConstants.DB_QUERY, query);
            }

            if (record != null && !record.isEmpty())
            {
                context.put(DBConstants.DB_RECORDS, new JsonArray().add(record));
            }

            context.put(ENTITY_TABLE, table)
                    .put(USER_NAME, user).put(REMOTE_ADDRESS, remoteIP)
                    .put(HA_SYNC_OPERATION, operation)
                    .put(DATABASE, database);

            var bytes = context.encode().getBytes();

            buffer.setLongLE(0, timestamp).appendIntLE(bytes.length).appendBytes(bytes);

            Bootstrap.vertx().eventBus().send(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + Bootstrap.getRegistrationId(), new JsonObject().put(EVENT_TIMESTAMP, timestamp).put(EVENT_CONTEXT, buffer.getBytes()));
        }
    }

    // notify observer for cache changes
    public static void notifyObserver(JsonObject event)
    {
        if (event.getBinary(RESULT) != null && event.getBinary(RESULT).length > 0)
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("updated cache file is %s with size %s bytes .... sending event to observer ", event.getString(CACHE_NAME), event.getBinary(RESULT).length));
            }

            Bootstrap.vertx().eventBus().send(EVENT_HA_OBSERVER, event.put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.WRITE.getName()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                    .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(EVENT_COPY_REQUIRED, false)
                    .put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC));
        }
    }

    public static void switchOverIP(Object type)
    {
        try
        {
            if (!MotadataConfigUtil.isHAOverWAN())
            {
                if (!MotadataConfigUtil.getVIPCommand().isEmpty() && !MotadataConfigUtil.getVIPIPAddress().isEmpty())
                {
                    switch (HASyncOperation.valueOfName(CommonUtil.getByteValue(type)))
                    {
                        case ATTACHE ->
                        {
                            var vipCommand = String.format(MotadataConfigUtil.getVIPCommand(), MotadataConfigUtil.getVIPIPAddress(), STATUS_UP.toLowerCase());

                            LOGGER.info(String.format("%s executed command ", vipCommand));

                            Runtime.getRuntime().exec(vipCommand);
                        }

                        case DETACH ->
                        {
                            var vipCommand = String.format(MotadataConfigUtil.getVIPCommand(), MotadataConfigUtil.getVIPIPAddress(), STATUS_DOWN.toLowerCase());

                            LOGGER.info(String.format("%s executed command ", vipCommand));

                            Runtime.getRuntime().exec(vipCommand);
                        }

                        default ->
                        {
                            // do nothing
                        }
                    }
                }
                else
                {
                    LOGGER.warn("Can not attach/detach VIP , reason : VIP command not found...");
                }
            }
            else
            {
                LOGGER.info("It is HA over WAN. so can not assign VIP");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public enum HASyncType
    {
        METRIC((byte) 0),
        EVENT((byte) 1),
        CONFIG((byte) 2);

        private static final Map<Byte, HASyncType> values = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(HASyncType::getName, operation -> operation)));
        private final Byte name;

        HASyncType(Byte name)
        {
            this.name = name;
        }

        public static HASyncType valueOfName(Byte name)
        {
            return values.get(name);
        }

        public Byte getName()
        {
            return name;
        }
    }

    public enum HASyncOperation
    {
        SAVE((byte) 0),
        SAVE_ALL((byte) 1),

        UPDATE((byte) 2),
        UPDATE_ALL((byte) 3),

        DELETE((byte) 4),
        DELETE_ALL((byte) 5),
        DROP((byte) 6),
        UPSERT((byte) 7),

        READ((byte) 8),
        WRITE((byte) 9),
        ATTACHE((byte) 10),
        DETACH((byte) 11);

        private static final Map<Byte, HASyncOperation> values = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(HASyncOperation::getName, operation -> operation)));
        private final Byte name;

        HASyncOperation(Byte name)
        {
            this.name = name;
        }

        public static HASyncOperation valueOfName(Byte name)
        {
            return values.get(name);
        }

        public Byte getName()
        {
            return name;
        }
    }


}
