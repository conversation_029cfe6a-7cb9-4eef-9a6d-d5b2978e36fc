/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.integration;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.circuitbreaker.OpenCircuitException;
import io.vertx.circuitbreaker.RetryPolicy;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import org.apache.http.HttpStatus;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Integration.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ID;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_NAME;

/**
 * Integration class for Atlassian Jira ticketing system.
 * <p>
 * This class provides functionality to:
 * - Create and update issues in Jira
 * - Synchronize attributes (projects, issue types, statuses, fields, users) with Jira
 * - Handle circuit breaking for reliable communication with Jira
 * - Process integration events from the event bus
 * - Send notifications when integration operations succeed or fail
 * <p>
 * The integration uses Jira's REST API to communicate with the Jira instance.
 * It implements a circuit breaker pattern to handle connection issues and retries.
 */
public class AtlassianJiraIntegration extends AbstractVerticle
{
    public static final String FETCH_ISSUE_METADATA_BASED_ON_ISSUE = "rest/api/2/issue/createmeta/%s/issuetypes/%s?maxResults=500";
    public static final String FETCH_ISSUE_TYPES_BASED_ON_PROJECT = "rest/api/2/issue/createmeta/%s/issuetypes?maxResults=500";
    public static final String FETCH_PROJECTS = "rest/api/2/project";
    public static final Set<String> DEFAULT_JIRA_FIELDS = Set.of("priority", "components");
    public static final String ASSIGNEE = "assignee";
    public static final String REST_API_2_USER_ASSIGNABLE_SEARCH_PROJECT_S_ISSUE_TYPE_S = "rest/api/2/user/assignable/search?project=%s&issueType=%s";
    public static final String FIELD_ID = "fieldId";
    public static final String ALLOWED_VALUES = "allowedValues";
    public static final String VALUES = "values";
    public static final String ISSUE_TYPES = "issue.types";
    public static final String KEY1 = "key";
    public static final String PROJECTS = "projects";
    private static final Logger LOGGER = new Logger(AtlassianJiraIntegration.class, INTEGRATION_DIR, "Atlassian Jira Integration");
    private static final String REPLY_TOPIC = IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION + EventBusConstants.EVENT_REPLY;
    // constants
    private static final String DESCRIPTION_TEMPLATE = "Object Name: $$$object.name$$$\nIP / Host: $$$object.ip$$$\nObject Type: $$$object.type$$$\nMetric: $$$counter$$$\nMetric Value: $$$value$$$\nSeverity: $$$severity$$$\nPolicy Name: $$$policy.name$$$\nPolicy Type: $$$policy.type$$$\nMessage: $$$policy.message$$$";
    // api payload related constants
    private static final String DESCRIPTION = "description";
    private static final String SUBJECT = "subject";
    private static final String NAME = "name";
    private final StringBuilder builder = new StringBuilder(0);
    private final Map<Long, JsonObject> integrationProfiles = new HashMap<>();
    private final AtomicBoolean RUNNING = new AtomicBoolean();
    // circuit breaker
    private final CircuitBreaker breaker = CircuitBreaker.create("atlassian-************************-breaker", Bootstrap.vertx(), new CircuitBreakerOptions()
                    .setMaxFailures(1)
                    .setMaxRetries(MotadataConfigUtil.getIntegrationMaxRetries())
                    .setTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationTimeoutSeconds()))
                    .setResetTimeout(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationPingTimerSeconds()))
            )
            .retryPolicy(RetryPolicy.constantDelay(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getIntegrationRetryTimerSeconds())));
    private EventEngine eventEngine;
    private Set<String> mappers;
    private Long timerId;
    private JsonObject integration;
    private WebClient webClient = WebClientUtil.getWebClient();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.ATLASSIAN_JIRA.getName());

        var eventEnginePromise = Promise.<Void>promise();

        eventEngine = new EventEngine().setEventType(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION)
                .setEventQueueSize(MotadataConfigUtil.getIntegrationEventQueueSize())
                .setBlockingEvent(true).setLogger(LOGGER).setEventHandler(this::process).start(vertx, eventEnginePromise);

        eventEnginePromise.future().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    // register circuit breaker handlers
                    breaker.openHandler(handler ->
                            LOGGER.warn("connection break! circuit is in open state!"));

                    breaker.halfOpenHandler(handler ->
                    {
                        if (integration != null && !integration.isEmpty())
                        {
                            var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                            if (context != null && !context.isEmpty())
                            {
                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "rest/api/2/project" : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/rest/api/2/project";

                                LOGGER.info(String.format("reconnecting to : %s", url));

                                var credentialProfile = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                                if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE)).send(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK)
                                                    {
                                                        LOGGER.info(String.format("connected to : %s", url));

                                                        breaker.reset();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        breaker.open();
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    breaker.open();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                breaker.open();
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn("invalid atlassian jira context");

                                breaker.open();
                            }
                        }
                        else
                        {
                            LOGGER.warn("invalid atlassian jira configuration");

                            breaker.open();
                        }
                    });

                    breaker.closeHandler(handler ->
                    {
                        LOGGER.info("processing pending events");

                        var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA);

                        if (pendingEvents != null && !pendingEvents.isEmpty())
                        {
                            IntegrationCacheStore.getStore().setUpdate(true);

                            while (!pendingEvents.isEmpty())
                            {
                                process(pendingEvents.removeFirst());
                            }
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        switch (ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
                        {
                            case UPDATE_INTEGRATION ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null
                                            && event.getLong(ID).equals(IntegrationConstants.IntegrationId.ATLASSIAN_JIRA.getName()))
                                    {
                                        integration = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.ATLASSIAN_JIRA.getName());

                                        if (event.containsKey(Integration.INTEGRATION_CONTEXT))
                                        {
                                            var context = event.getJsonObject(INTEGRATION_CONTEXT);

                                            if (context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES)
                                                    && WebClientUtil.getProxyOptions() != null)
                                            {
                                                webClient = WebClientUtil.getProxyWebClient();
                                            }
                                            else
                                            {
                                                webClient = WebClientUtil.getWebClient();
                                            }

                                            // sync will work with proxy also
                                            syncAttributes();

                                            if (context.containsKey(Integration.AUTO_SYNC)
                                                    && context.getString(Integration.AUTO_SYNC).equalsIgnoreCase(YES)
                                                    && context.containsKey(Integration.SYNC_INTERVAL))
                                            {
                                                if (timerId != null)
                                                {
                                                    vertx.cancelTimer(timerId);
                                                }

                                                timerId = vertx.setPeriodic(TimeUnit.HOURS.toMillis(context.getInteger(SYNC_INTERVAL, 8)), timer -> syncAttributes());
                                            }
                                            else if (context.containsKey(AUTO_SYNC) && context.getString(AUTO_SYNC).equalsIgnoreCase(NO))
                                            {
                                                if (timerId != null)
                                                {
                                                    vertx.cancelTimer(timerId);
                                                }
                                            }
                                        }
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case ADD_INTEGRATION_PROFILE, UPDATE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null && event.containsKey(IntegrationProfile.INTEGRATION)
                                            && event.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.ATLASSIAN_JIRA.getName()))
                                    {
                                        integrationProfiles.put(event.getLong(ID), IntegrationProfileConfigStore.getStore().getItem(event.getLong(ID)));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            case DELETE_INTEGRATION_PROFILE ->
                            {
                                try
                                {
                                    var event = message.body();

                                    if (event.containsKey(ID) && event.getValue(ID) != null)
                                    {
                                        integrationProfiles.remove(event.getLong(ID));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }
                        }
                    });

                    var items = IntegrationProfileConfigStore.getStore().getItems();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var item = items.getJsonObject(index);

                        if (item.containsKey(IntegrationProfile.INTEGRATION)
                                && item.getLong(IntegrationProfile.INTEGRATION).equals(IntegrationConstants.IntegrationId.ATLASSIAN_JIRA.getName()))
                        {
                            integrationProfiles.put(item.getLong(ID), item);
                        }
                    }

                    if (integration != null && !integration.isEmpty()
                            && integration.getValue(ProxyServer.PROXY_ENABLED) != null
                            && integration.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null)
                    {
                        webClient = WebClientUtil.getProxyWebClient();
                    }

                    var pendingEvents = IntegrationCacheStore.getStore().getPendingEvents(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA);

                    if (pendingEvents != null && !pendingEvents.isEmpty())
                    {
                        IntegrationCacheStore.getStore().setUpdate(true);

                        while (!pendingEvents.isEmpty())
                        {
                            // when AIOps restart we need to enqueue this pending events into event engine we can't directly call process method otherwise event engine pending event count will go below 0
                            vertx.eventBus().send(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, JsonObject.mapFrom(pendingEvents.removeFirst()));
                        }
                    }

                    // get integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_RESPONSE_GET, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (integration != null && !integration.isEmpty())
                            {

                                var context = integration.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "rest/api/2/issue/" + event.getString(IntegrationEngine.ACK_ID)
                                        : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/rest/api/2/issue/" + event.getString(IntegrationEngine.ACK_ID);

                                var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

                                if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .send(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {

                                                if (CommonUtil.traceEnabled())
                                                {
                                                    LOGGER.trace(String.format("received response with status code : %s and response body %s", asyncResult.result().statusCode(), asyncResult.result().bodyAsString()));
                                                }

                                                if (asyncResult.result().statusCode() == HttpStatus.SC_OK)
                                                {

                                                    if (CommonUtil.validJSONResponse(asyncResult.result())
                                                            && !asyncResult.result().bodyAsJsonObject().containsKey("errorMessages"))
                                                    {
                                                        var response = asyncResult.result().bodyAsJsonObject();

                                                        var redirectionUrl = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                                                ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "browse/" + response.getString("key")
                                                                : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/browse/" + response.getString("key");

                                                        message.reply(event.mergeIn(response).put(STATUS, STATUS_SUCCEED).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put(GlobalConstants.TARGET, redirectionUrl));
                                                    }
                                                    else
                                                    {
                                                        message.fail(HttpStatus.SC_BAD_REQUEST, "Invalid Json response!");
                                                    }
                                                }
                                                else
                                                {
                                                    message.fail(asyncResult.result().statusCode(), String.format("Failed to get incident with status code : %s", asyncResult.result().statusCode()));
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, asyncResult.cause().getMessage());
                                            }
                                        });
                            }
                            else
                            {
                                LOGGER.warn(String.format("Invalid integration type : %s", event.getString(Integration.INTEGRATION_TYPE)));

                                message.fail(HttpStatus.SC_BAD_REQUEST, String.format("Invalid integration type : %s", event.getString(INTEGRATION_TYPE)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    // test integration
                    vertx.eventBus().<JsonObject>localConsumer(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION_TEST, message ->
                    {
                        var event = message.body();

                        try
                        {
                            if (event.containsKey(Integration.INTEGRATION_CONTEXT) && !event.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty())
                            {
                                var context = event.getJsonObject(Integration.INTEGRATION_CONTEXT);

                                var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                                        ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "rest/api/2/project" : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/rest/api/2/project";

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("executing request POST : %s", url));
                                }

                                var webClient = context.getValue(ProxyServer.PROXY_ENABLED) != null && context.getString(ProxyServer.PROXY_ENABLED).equalsIgnoreCase(YES) && WebClientUtil.getProxyOptions() != null ? WebClientUtil.getProxyWebClient() : WebClientUtil.getWebClient();

                                var credentialProfile = CredentialProfileConfigStore.getStore().getItem(context.getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE), true);

                                if (credentialProfile.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                {
                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                }

                                webClient.requestAbs(HttpMethod.GET, url)
                                        .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                                        .timeout(TimeUnit.SECONDS.toMillis(context.getLong(TIMEOUT, 60L)))
                                        .send().onComplete(asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    var response = asyncResult.result();

                                                    if (CommonUtil.traceEnabled())
                                                    {
                                                        LOGGER.trace(String.format("integration test response received with status code %s and response %s", response.statusCode(), response.bodyAsString()));
                                                    }

                                                    if (response.statusCode() == HttpStatus.SC_OK || response.statusCode() == HttpStatus.SC_CREATED)
                                                    {
                                                        message.reply(event.put(STATUS, STATUS_SUCCEED)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_SUCCEEDED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName())));
                                                    }
                                                    else if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                                                    {
                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), CREDENTIAL_ERROR)));
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(asyncResult.result().statusCode());

                                                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                                .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), asyncResult.result().statusCode())));
                                                    }
                                                }
                                                else
                                                {
                                                    LOGGER.error(asyncResult.cause());

                                                    message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                            .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), asyncResult.cause().getMessage())));
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), exception.getMessage())));

                                                LOGGER.error(exception);
                                            }
                                        });
                            }
                            else
                            {
                                message.reply(new JsonObject().put(STATUS, STATUS_FAIL)
                                        .put(MESSAGE, String.format(InfoMessageConstants.INTEGRATION_TEST_FAILED, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), INTERNAL_ERROR)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, exception.getMessage());
                        }
                    });

                    promise.complete();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });
    }

    /**
     * Processes integration events and performs appropriate actions in Jira.
     * <p>
     * This method:
     * 1. Validates the integration and profile configurations
     * 2. Determines the appropriate action based on event data:
     * - Creates a new issue
     * - Updates an existing issue
     * - Closes an issue when severity is CLEAR
     * - Reopens a previously closed issue if configured
     * 3. Sends a response back to the event bus when processing is complete
     * <p>
     * The method handles various scenarios including:
     * - Auto-closing issues based on configuration
     * - Reopening issues for recurring alerts
     * - Creating new issues for first-time alerts
     *
     * @param event The event object containing information for Jira integration
     */
    private void process(JsonObject event)
    {
        try
        {
            if (integration != null && !integration.isEmpty())
            {
                var item = integrationProfiles.get(event.getLong(ID));

                if (item != null && !item.isEmpty())
                {
                    // merge configuration
                    event.mergeIn(integration.getJsonObject(Integration.INTEGRATION_CONTEXT));

                    // merge profile context
                    event.put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, item.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

                    var autoClose = CommonUtil.getString(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS)).equalsIgnoreCase(YES);

                    var ackId = IntegrationCacheStore.getStore().getItem(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                    if (ackId != null && !CommonUtil.getString(ackId).isEmpty() && !ackId.equals(NOT_AVAILABLE)) //Update Ticket
                    {
                        if (Severity.CLEAR.name().equalsIgnoreCase(event.getString(SEVERITY)))
                        {
                            LOGGER.info(String.format("closing ticket with id : %s", ackId));

                            if (autoClose)
                            {
                                put(event, CommonUtil.getString(ackId), event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(IntegrationProfile.AUTO_CLOSE_TICKET_STATE, EMPTY_VALUE), false);
                            }
                            else
                            {
                                vertx.eventBus().send(REPLY_TOPIC, event);

                                if (MotadataConfigUtil.devMode())
                                {
                                    vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put("statusName", "Resolved"));
                                }
                            }

                            IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));
                        }
                        else
                        {
                            LOGGER.info(String.format("updating ticket with id : %s", ackId));

                            post(event, CommonUtil.getString(ackId));
                        }
                    }
                    else if (event.containsKey(IntegrationEngine.CREATE_TICKET) && event.getBoolean(IntegrationEngine.CREATE_TICKET)) // Create Ticket
                    {
                        var reopen = false;

                        if (event.containsKey(Integration.ALERT_REOCCURRENCE_ACTION)
                                && event.getString(Integration.ALERT_REOCCURRENCE_ACTION).equalsIgnoreCase(IntegrationConstants.AlertReoccurrenceAction.REOPEN.getName()))
                        {
                            var historyId = IntegrationCacheStore.getStore().getArchivedItem(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));

                            if (historyId != null && !CommonUtil.getString(historyId).isEmpty() && !historyId.equals(NOT_AVAILABLE))
                            {
                                reopen = true;

                                LOGGER.info(String.format("reopening ticket with id : %s", historyId));

                                put(event, CommonUtil.getString(historyId), event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT).getString(Integration.ALERT_REOCCURRENCE_STATUS), true);
                            }
                        }

                        if (!reopen) // means create ticket
                        {
                            LOGGER.info("creating ticket");
                            post(event, null);
                        }
                    }
                    else
                    {
                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("ticket will be not created for the severity : %s", event.getString(SEVERITY)));
                        }

                        vertx.eventBus().send(REPLY_TOPIC, event);
                    }
                }
                else
                {
                    LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())));

                    vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format(ErrorMessageConstants.RUNBOOK_EXECUTION_FAILED, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.INTEGRATION_PROFILE.getName())))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }
            }
            else
            {
                LOGGER.warn(String.format("integration failed %s, reason: %s", event.getLong(ID), String.format("%s integration not configured", IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName())));

                vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, String.format("%s integration not configured", IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Creates or updates an issue in Jira using a POST request.
     * <p>
     * This method:
     * 1. Retrieves credential information for authentication
     * 2. Determines the appropriate API endpoint based on whether this is a new issue or an update
     * 3. Prepares the request payload with issue details
     * 4. Sends the request to Jira using the circuit breaker pattern
     * 5. Processes the response:
     * - Updates the integration cache with issue information on success
     * - Logs warnings for invalid responses
     * - Sends email notifications for failures
     *
     * @param event The event object containing information for the issue
     * @param ackId The acknowledgement ID of the existing issue (null for new issues)
     */
    private void post(JsonObject event, String ackId)
    {
        var id = EMPTY_VALUE;

        var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

        if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

            item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
        }

        var request = new JsonObject();

        if (ackId != null)
        {
            id = ackId.split(HASH_SEPARATOR)[1];
        }

        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                ? event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "rest/api/2/issue/"
                : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/rest/api/2/issue/";

        if (CommonUtil.isNotNullOrEmpty(id)) // update ticket
        {
            url += id + "/comment";

            request.mergeIn(prepareUpdateRequestPayload(event));
        }
        else // create ticket
        {
            request.mergeIn(prepareCreateRequestPayload(event));
        }

        var requestUrl = url;

        var requestId = id;

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("executing api : %s with request body : %s", requestUrl, request.encode()));
        }

        execute(() -> webClient.requestAbs(HttpMethod.POST, requestUrl)
                .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .putHeader("Content-Type", "application/json")
                .sendJsonObject(request), event).onComplete(asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    var result = asyncResult.result();

                    if (result.containsKey(STATUS) && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        vertx.eventBus().send(REPLY_TOPIC, result);

                        IntegrationConstants.dump(result.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()), mappers, builder, LOGGER);

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()));
                        }

                        var response = result.containsKey(RESULT) ? result.getJsonObject(RESULT) : null;

                        if (CommonUtil.isNullOrEmpty(requestId) && response != null && !response.isEmpty()) // ticket created
                        {
                            if (response.containsKey(KEY) && CommonUtil.isNotNullOrEmpty(response.getString(KEY))
                                    && response.containsKey(ID) && CommonUtil.isNotNullOrEmpty(response.getString(ID)))
                            {
                                IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), response.getString(KEY, EMPTY_VALUE) + HASH_SEPARATOR + response.getString(ID, EMPTY_VALUE), event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid response received : %s", result.encode()));
                            }
                        }
                    }
                    else
                    {
                        IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA, event);

                        LOGGER.warn(String.format("received failed response : %s", result.encode()));
                    }
                }
                else
                {
                    LOGGER.warn("integration is down!");

                    IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA, event);

                    LOGGER.error(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    /**
     * Updates the status of an existing issue in Jira using a PUT request.
     * <p>
     * This method:
     * 1. Retrieves credential information for authentication
     * 2. Constructs the API URL for the transition endpoint
     * 3. Retrieves available transitions for the issue
     * 4. Finds the appropriate transition ID based on the target status
     * 5. Sends a transition request to update the issue status
     * 6. Processes the response:
     * - Updates the integration cache when reopening an issue
     * - Removes the issue from the cache when closing it
     * - Sends notifications of success or failure
     *
     * @param event  The event object containing information for the issue update
     * @param ackId  The acknowledgement ID of the existing issue
     * @param status The target status to transition the issue to
     * @param reopen Flag indicating whether this is a reopen operation
     */
    private void put(JsonObject event, String ackId, String status, boolean reopen)
    {
        var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

        if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
        {
            item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

            item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
        }

        var id = ackId.split(HASH_SEPARATOR)[1];

        var key = ackId.split(HASH_SEPARATOR)[0];

        if (reopen)
        {
            IntegrationCacheStore.getStore().updateItem(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY), key + HASH_SEPARATOR + id, event.getLong(EVENT_TIMESTAMP), event.getString(SEVERITY));
        }
        else
        {
            IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName(), event.getString(POLICY_ID) + SEPARATOR + event.getString(PolicyEngineConstants.POLICY_KEY));
        }

        var url = event.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/") ?
                event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + String.format("rest/api/2/issue/%s/transitions", id) : event.getString(GlobalConstants.TARGET, EMPTY_VALUE) + String.format("/rest/api/2/issue/%s/transitions", id);

        webClient.requestAbs(HttpMethod.GET, url)
                .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                .putHeader("Content-Type", "application/json")
                .send().onComplete(asyncResult ->
                {
                    try
                    {
                        if (asyncResult.succeeded())
                        {
                            var transitionId = EMPTY_VALUE;

                            var transitions = asyncResult.result().bodyAsJsonObject().getJsonArray("transitions");

                            for (var index = 0; index < transitions.size(); index++)
                            {

                                if (status.equalsIgnoreCase(transitions.getJsonObject(index).getString("name")))
                                {
                                    transitionId = transitions.getJsonObject(index).getString(ID);
                                }
                            }

                            // if the workflow of jira integration have same transition as we defined it will move the transition
                            // if not it will just drop the process
                            if (!EMPTY_VALUE.equalsIgnoreCase(transitionId))
                            {
                                var request = new JsonObject().put("transition", new JsonObject().put(ID, transitionId));

                                execute(() -> webClient.requestAbs(HttpMethod.POST, url)
                                        .basicAuthentication(item.getString(USERNAME, EMPTY_VALUE), item.getString(PASSWORD, EMPTY_VALUE))
                                        .timeout(TimeUnit.SECONDS.toMillis(event.getLong(TIMEOUT, 60L)))
                                        .putHeader("Content-Type", "application/json")
                                        .sendJson(request), event).onComplete(result ->
                                {
                                    try
                                    {
                                        if (result.succeeded())
                                        {
                                            vertx.eventBus().send(REPLY_TOPIC, event);

                                            if (MotadataConfigUtil.devMode())
                                            {
                                                if (reopen)
                                                {
                                                    vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put("statusName", "Reopened"));
                                                }
                                                else
                                                {
                                                    vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put("statusName", "Resolved"));
                                                }

                                            }

                                        }
                                        else
                                        {
                                            LOGGER.warn("integration is down!");

                                            IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA, event);

                                            LOGGER.error(asyncResult.cause());
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }

                                });
                            }
                            else
                            {
                                LOGGER.warn(ErrorMessageConstants.TRANSITION_STATUS);

                                vertx.eventBus().send(REPLY_TOPIC, event.put(STATUS, STATUS_ABORT).put(MESSAGE, ErrorMessageConstants.TRANSITION_STATUS));
                            }
                        }
                        else
                        {
                            LOGGER.warn("integration is down!");

                            IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA, event);

                            LOGGER.error(asyncResult.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
    }

    /**
     * Prepares the request payload for creating an issue in Jira.
     * <p>
     * This method:
     * 1. Merges the integration profile context into the fields object
     * 2. Removes configuration keys that are not needed for the Jira API
     * 3. Constructs the summary (title) and detailed description based on event data
     * 4. Uses placeholder substitution to include dynamic information in the description
     * <p>
     * The method handles different scenarios:
     * - Including instance information when available
     * - Falling back to default values when policy information is not available
     *
     * @param event The event object containing information for the issue
     * @return A JsonObject containing the prepared request payload with fields for the Jira issue
     */
    private JsonObject prepareCreateRequestPayload(JsonObject event)
    {
        var fields = new JsonObject();

        try
        {
            fields.mergeIn(event.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT));

            // remove the keys that are used by us.
            fields.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATE);

            fields.remove(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS);

            fields.remove(STATUS);

            fields.remove(ALERT_REOCCURRENCE_STATUS);

            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                if (event.containsKey(INSTANCE))
                {
                    fields.put("summary", event.getString(SUBJECT, item.getString(POLICY_NAME, EMPTY_VALUE) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE) + " - " + event.getString(INSTANCE, EMPTY_VALUE)));

                    fields.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE.replace("</ul>", EMPTY_VALUE) + "<li><p>Instance: <strong>$$$instance$$$</strong></p></li>"), event.getString(MESSAGE, EMPTY_VALUE)));
                }
                else
                {
                    fields.put("summary", event.getString(SUBJECT, item.getString(POLICY_NAME) + " - " + event.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE) + " (" + event.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE) + ")" + " - " + event.getString(METRIC, EMPTY_VALUE)));

                    fields.put(DESCRIPTION, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
            else
            {
                LOGGER.warn(String.format("incorrect policy id request received : %s", event.getLong(POLICY_ID)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return new JsonObject().put("fields", fields);
    }

    /**
     * Prepares the request payload for updating an existing issue in Jira.
     * <p>
     * This method:
     * 1. Retrieves the policy information associated with the event
     * 2. Constructs a comment body containing details about the event
     * 3. Uses placeholder substitution to include dynamic information in the comment
     * <p>
     * The comment includes information such as:
     * - Object name and IP
     * - Metric name and value
     * - Severity level
     * - Policy name and type
     * - Custom message
     *
     * @param event The event object containing information for the issue update
     * @return A JsonObject containing the prepared update payload with comment body
     */
    private JsonObject prepareUpdateRequestPayload(JsonObject event)
    {
        var context = new JsonObject();

        try
        {
            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

            if (item != null)
            {
                context.put("body", PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, event.getString(DESCRIPTION, DESCRIPTION_TEMPLATE), event.getString(MESSAGE, EMPTY_VALUE)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return context;
    }

    /**
     * Executes an HTTP request to Jira using the circuit breaker pattern.
     * <p>
     * This method:
     * 1. Wraps the HTTP request in a circuit breaker to handle connection issues
     * 2. Processes the response from Jira:
     * - Validates the HTTP status code (200 OK or 201 Created)
     * - Validates that the response is valid JSON
     * - Extracts the result from the response
     * 3. Handles various error scenarios:
     * - Circuit open exceptions (connection issues)
     * - Invalid responses
     * - HTTP errors
     * 4. Sends email notifications for failures when configured
     * <p>
     * The circuit breaker pattern helps prevent cascading failures when
     * Jira is unavailable by failing fast and allowing for retries.
     *
     * @param supplier A supplier function that returns a Future with the HTTP request
     * @param event    The event object containing information for error handling
     * @return A Future containing the processed response as a JsonObject
     */
    private Future<JsonObject> execute(Supplier<Future<HttpResponse<Buffer>>> supplier, JsonObject event)
    {
        var future = Promise.<JsonObject>promise();

        try
        {
            // execute
            breaker.<JsonObject>execute(promise -> supplier.get().onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        var response = result.result();

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("received response with status code : %s and response body %s", response.statusCode(), response.bodyAsString()));
                        }

                        if ((response.statusCode() == HttpStatus.SC_OK || response.statusCode() == HttpStatus.SC_CREATED) && response.bodyAsJsonObject() != null)
                        {
                            if (CommonUtil.validJSONResponse(response))
                            {
                                promise.complete(event.put(STATUS, STATUS_SUCCEED).put(RESULT, response.bodyAsJsonObject()));
                            }
                            else
                            {
                                promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                            }
                        }
                        else if (response.statusCode() == HttpStatus.SC_NO_CONTENT)
                        {
                            // while jira transition change it returns nothing
                            promise.complete(event.put(STATUS, STATUS_SUCCEED).put(RESULT, new JsonObject()));
                        }
                        else
                        {
                            promise.fail(String.format("ticket creation failed with status code %s and response : %s", response.statusCode(), response.bodyAsBuffer().toString()));
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception.getCause());
                }
            })).onComplete(result ->
            {
                if (result.succeeded())
                {
                    future.complete(result.result());
                }
                else
                {
                    sendMail(result.cause().getMessage(), event);

                    future.fail(result.cause());
                }
            });
        }
        catch (OpenCircuitException exception) // if the circuit is in open state in that case if any event reaches here to execute then circuit will raise this exception hence will add into pending events
        {
            LOGGER.error(exception);

            IntegrationCacheStore.getStore().addPendingEvent(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA, event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception.getCause());
        }

        return future.future();
    }

    /**
     * Sends an email notification when a Jira integration operation fails.
     * <p>
     * This method:
     * 1. Saves the error message to a temporary file
     * 2. Constructs an email notification with:
     * - The error message as an attachment
     * - Standard icons and images
     * - A formatted HTML template with error details
     * 3. Sends the email using the email notification event bus
     * 4. Cleans up the temporary file after sending
     * 5. Sends a test integration event in development mode
     * <p>
     * The email includes details such as:
     * - Timestamp of the failure
     * - Integration type (Atlassian Jira)
     * - Error message
     * - Target Jira instance URL
     *
     * @param message The error message to include in the notification
     * @param event   The event object containing recipient information and other details
     */
    private void sendMail(String message, JsonObject event)
    {
        var fileName = DateTimeUtil.currentSeconds() + "-response.txt";

        vertx.fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName, Buffer.buffer(message));

        // send fail over email
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EMAIL_NOTIFICATION, new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(fileName).add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(INTEGRATION_FAILED_NOTIFICATION_SUBJECT, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()))
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, event.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS))
                        .put(Notification.TEMPLATE_NAME, Notification.EMAIL_INTEGRATION_FAILED_HTML_TEMPLATE)
                        .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().put(TIME_STAMP, DateTimeUtil.timestamp(System.currentTimeMillis())).put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put(MESSAGE, "Failed to create ticket!").put(GlobalConstants.TARGET, event.getString(GlobalConstants.TARGET))), new DeliveryOptions().setSendTimeout(600000L),
                reply ->
                        vertx.fileSystem().deleteBlocking(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName));

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().send("test.integration", event.put(INTEGRATION_TYPE, IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName()).put(MESSAGE, message));
        }
    }

    /**
     * Synchronizes Jira attributes with the local system.
     * <p>
     * This method:
     * 1. Retrieves credential information for authentication
     * 2. Constructs the base URL for Jira API calls
     * 3. Initiates synchronization of projects and their related attributes:
     * - Projects
     * - Issue types
     * - Issue statuses
     * - Issue fields
     * - Users
     * 4. Updates the integration configuration in the database when synchronization completes
     * <p>
     * The synchronization process builds a hierarchical structure of Jira configuration
     * data that can be used when creating and updating issues.
     */
    private void syncAttributes()
    {
        try
        {
            if (!RUNNING.get())
            {
                LOGGER.info("Sync job started!");

                RUNNING.set(true);

                var item = CredentialProfileConfigStore.getStore().getItem(integration.getJsonObject(INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, 0L), true);

                if (integration != null && !integration.isEmpty()
                        && integration.containsKey(INTEGRATION_CONTEXT) && !integration.getJsonObject(INTEGRATION_CONTEXT).isEmpty()
                        && integration.getJsonObject(INTEGRATION_CONTEXT).containsKey(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE)
                        && item != null)
                {
                    var context = integration.getJsonObject(INTEGRATION_CONTEXT);

                    if (item.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                    {
                        item.mergeIn(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                        item.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                    }

                    var url = context.getString(GlobalConstants.TARGET, EMPTY_VALUE).trim().endsWith("/")
                            ? context.getString(GlobalConstants.TARGET, EMPTY_VALUE) : context.getString(GlobalConstants.TARGET, EMPTY_VALUE) + "/";

                    var futures = new ArrayList<Future<Void>>();

                    futures.add(syncProjects(url, integration.getJsonObject(INTEGRATION_ATTRIBUTES), item.put(TIMEOUT, context.getLong(TIMEOUT, 60L))));

                    Future.join(futures).onComplete(result ->
                            Bootstrap.configDBService().update(DBConstants.TBL_INTEGRATION,
                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, integration.getLong(ID)),
                                    integration, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                    {
                                        RUNNING.set(false);

                                        if (asyncResult.succeeded())
                                        {
                                            IntegrationConfigStore.getStore().updateItem(integration.getLong(ID));
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());
                                        }
                                    }));
                }
                else
                {
                    LOGGER.warn("pre condition failed for sync job to run");

                    RUNNING.set(false);
                }
            }
            else
            {
                LOGGER.warn("Sync job is already running!");
            }
        }
        catch (Exception exception)
        {
            RUNNING.set(false);

            LOGGER.error(exception);
        }
    }

    /**
     * Synchronizes projects from Jira.
     * <p>
     * This method:
     * 1. Retrieves all projects from Jira's project API endpoint
     * 2. For each project, extracts key information (ID, key, name)
     * 3. Initiates synchronization of issue types, statuses, and fields for each project
     * 4. Updates the integration attributes with the synchronized project information
     * <p>
     * The method uses asynchronous HTTP requests and returns a Future that completes
     * when the synchronization is finished.
     *
     * @param url               The base URL for Jira API calls
     * @param attributes        The integration attributes object to be updated
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncProjects(String url, JsonObject attributes, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        webClient.getAbs(url + FETCH_PROJECTS)
                .timeout(TimeUnit.SECONDS.toMillis(credentialProfile.getLong(TIMEOUT)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                var items = response.bodyAsJsonArray();

                                if (items != null)
                                {
                                    var projects = new JsonArray();

                                    var futures = new ArrayList<Future<Void>>();

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = items.getJsonObject(index);

                                        var project = new JsonObject();

                                        project.put(ID, item.getString(ID));

                                        project.put(KEY1, item.getString(KEY1));

                                        project.put(NAME, item.getString(NAME));

                                        futures.add(syncIssueTypes(url, project, credentialProfile));

                                        futures.add(syncStatus(url, project, credentialProfile));

                                        projects.add(project);
                                    }

                                    attributes.put(PROJECTS, projects);

                                    Future.join(futures).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            if (!projects.isEmpty())
                                            {
                                                attributes.put(PROJECTS, projects);
                                            }

                                            promise.complete();
                                        }
                                        else
                                        {
                                            promise.fail(asyncResult.cause());
                                        }
                                    });

                                }
                                else
                                {
                                    LOGGER.warn("invalid json array response received in project sync");

                                    promise.fail("invalid json array response received in project sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in project sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in project sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes issue statuses for a specific project from Jira.
     * <p>
     * This method:
     * 1. Retrieves all available statuses for the project from Jira's status API endpoint
     * 2. Maps each status to a local data structure with ID and name
     * 3. Adds the status information to the project object
     * <p>
     * Issue statuses represent the current state of an issue in the workflow
     * (e.g., "Open", "In Progress", "Resolved", "Closed").
     *
     * @param url               The base URL for Jira API calls
     * @param project           The project object to be updated with status information
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncStatus(String url, JsonObject project, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        LOGGER.debug(String.format("status sync start with url : %s", url + String.format("rest/api/2/project/%s/statuses", project.getString(ID))));

        webClient.getAbs(url + String.format("rest/api/2/project/%s/statuses", project.getString(ID)))
                .timeout(TimeUnit.SECONDS.toMillis(credentialProfile.getLong(TIMEOUT)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                var statuses = new JsonArray();

                                var items = response.bodyAsJsonArray();

                                for (var i = 0; i < items.size(); i++)
                                {
                                    var status = items.getJsonObject(i).getJsonArray("statuses");

                                    for (var j = 0; j < status.size(); j++)
                                    {

                                        var item = status.getJsonObject(j).getString("name").toLowerCase();

                                        if (!statuses.contains(item))
                                        {
                                            statuses.add(item);
                                        }
                                    }
                                }

                                if (!statuses.isEmpty())
                                {
                                    project.put("statuses", statuses);
                                }

                                promise.complete();

                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in statuses sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in statuses sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes issue types for a specific project from Jira.
     * <p>
     * This method:
     * 1. Retrieves all available issue types for the project from Jira's issue types API endpoint
     * 2. Maps each issue type to a local data structure with ID and name
     * 3. For each issue type, initiates synchronization of its metadata fields
     * 4. For each issue type, initiates synchronization of assignable users
     * 5. Adds the issue type information to the project object
     * <p>
     * Issue types represent different kinds of issues that can be created in Jira
     * (e.g., "Bug", "Task", "Story", "Epic").
     *
     * @param url               The base URL for Jira API calls
     * @param project           The project object to be updated with issue type information
     * @param credentialProfile The credential profile for authentication
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncIssueTypes(String url, JsonObject project, JsonObject credentialProfile)
    {
        var promise = Promise.<Void>promise();

        // hardcore limit of 500 issue types as those can be the max same will be done for users fetch
        // confirmed with PMG team as not expecting more than 500 users.

        LOGGER.debug(String.format("issue type sync start with url : %s", url + String.format(FETCH_ISSUE_TYPES_BASED_ON_PROJECT, project.getString(ID))));

        webClient.getAbs(url + String.format(FETCH_ISSUE_TYPES_BASED_ON_PROJECT, project.getString(ID)))
                .timeout(TimeUnit.SECONDS.toMillis(credentialProfile.getLong(TIMEOUT)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    var issueTypes = new JsonArray();

                                    var items = response.bodyAsJsonObject().getJsonArray(VALUES);

                                    var futures = new ArrayList<Future<Void>>();

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = items.getJsonObject(index);

                                        var issueType = new JsonObject();

                                        issueType.put(ID, item.getString(ID));

                                        issueType.put(NAME, item.getString(NAME));

                                        issueType.put(ASSIGNEE, new JsonArray());

                                        futures.add(syncIssueMetaFields(url, project.getString(ID), credentialProfile, issueType));

                                        futures.add(syncUsers(url, project.getString(KEY1), credentialProfile, issueType));

                                        issueTypes.add(issueType);
                                    }

                                    Future.join(futures).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            if (!issueTypes.isEmpty())
                                            {
                                                project.put(ISSUE_TYPES, issueTypes);
                                            }

                                            promise.complete();
                                        }
                                        else
                                        {
                                            promise.fail(asyncResult.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in issue types sync");

                                    promise.fail("invalid json response received in issue types sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in issue types sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in issue types sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes metadata fields for a specific issue type in a project from Jira.
     * <p>
     * This method:
     * 1. Retrieves all available fields for the issue type from Jira's createmeta API endpoint
     * 2. Processes fields like priority and components that have predefined allowed values
     * 3. Maps each field and its allowed values to a local data structure
     * 4. Adds the field information to the issue type object
     * <p>
     * Metadata fields represent the various fields that can be set when creating or updating
     * an issue of this type, including both system fields and custom fields.
     *
     * @param url               The base URL for Jira API calls
     * @param projectId         The ID of the project
     * @param credentialProfile The credential profile for authentication
     * @param issueType         The issue type object to be updated with field information
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncIssueMetaFields(String url, String projectId, JsonObject credentialProfile, JsonObject issueType)
    {
        var promise = Promise.<Void>promise();

        LOGGER.debug(String.format("issue meta fields sync start with url : %s", url + String.format(FETCH_ISSUE_METADATA_BASED_ON_ISSUE, projectId, issueType.getString(ID))));

        webClient.getAbs(url + String.format(FETCH_ISSUE_METADATA_BASED_ON_ISSUE, projectId, issueType.getString(ID)))
                .timeout(TimeUnit.SECONDS.toMillis(credentialProfile.getLong(TIMEOUT)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                if (CommonUtil.validJSONResponse(response))
                                {
                                    var items = response.bodyAsJsonObject().getJsonArray(VALUES);

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = items.getJsonObject(index);

                                        if (DEFAULT_JIRA_FIELDS.stream().anyMatch(item.getString(FIELD_ID)::contains))
                                        {
                                            issueType.put(item.getString(FIELD_ID), item.getJsonArray(ALLOWED_VALUES));
                                        }
                                    }

                                    promise.complete();
                                }
                                else
                                {
                                    LOGGER.warn("invalid json response received in issue metadata sync");

                                    promise.fail("invalid json response received in issue metadata sync");
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in issue metadata sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in issue metadata sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    /**
     * Synchronizes assignable users for a specific issue type in a project from Jira.
     * <p>
     * This method:
     * 1. Retrieves all users that can be assigned to issues of this type in this project
     * 2. Maps each user to a local data structure with key information (ID, name, email)
     * 3. Adds the user information to the issue type object
     * <p>
     * This information is used when creating or updating issues to provide a list of
     * valid assignees for the issue.
     *
     * @param url               The base URL for Jira API calls
     * @param projectIKey       The key of the project
     * @param credentialProfile The credential profile for authentication
     * @param issueType         The issue type object to be updated with user information
     * @return A Future that completes when the synchronization is finished
     */
    private Future<Void> syncUsers(String url, String projectIKey, JsonObject credentialProfile, JsonObject issueType)
    {
        var promise = Promise.<Void>promise();

        LOGGER.debug(String.format("user sync start with url : %s", url + String.format(REST_API_2_USER_ASSIGNABLE_SEARCH_PROJECT_S_ISSUE_TYPE_S, projectIKey, issueType.getString(ID))));

        webClient.getAbs(url + String.format(REST_API_2_USER_ASSIGNABLE_SEARCH_PROJECT_S_ISSUE_TYPE_S, projectIKey, issueType.getString(ID)))
                .timeout(TimeUnit.SECONDS.toMillis(credentialProfile.getLong(TIMEOUT)))
                .basicAuthentication(credentialProfile.getString(USERNAME, EMPTY_VALUE), credentialProfile.getString(PASSWORD, EMPTY_VALUE))
                .send(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var response = result.result();

                            if (response.statusCode() == HttpStatus.SC_OK)
                            {
                                var items = response.bodyAsJsonArray();

                                if (items != null && !items.isEmpty())
                                {
                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var name = items.getJsonObject(index).getString(NAME);

                                        issueType.getJsonArray(ASSIGNEE).add(name);
                                    }

                                    promise.complete();
                                }
                                else
                                {
                                    LOGGER.warn(String.format("No users found for project %s and issue type %s", projectIKey, issueType.getString(ID)));

                                    promise.fail(String.format("No users found for project %s and issue type %s", projectIKey, issueType.getString(ID)));
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("invalid status code received in user sync : %s", result.result().statusCode()));

                                promise.fail(String.format("invalid status code received in user sync : %s", result.result().statusCode()));
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
