{"***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"bgp.peer": [{"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 200, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}, {"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 100, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Juniper Networks", "port": 161, "rediscover.job": "Network Metric", "result": {"bgp.peer": [{"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 100, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"bgp.peer": [{"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 100, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}, {"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 200, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"errors": [], "metric.timeout": 60, "object.ip": "***********", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"bgp.peer": [{"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 100, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}, {"bgp.local.peer.address": "***********", "bgp.peer": "***********", "bgp.peer.remote.as": 200, "bgp.peer.status": "Established", "bgp.peer.time": " 0 day 0 hour 0 minute  0 second", "bgp.peer.updated.time": " 0 day 0 hour 0 minute  0 second", "bgp.remote.peer.address": "***********"}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}