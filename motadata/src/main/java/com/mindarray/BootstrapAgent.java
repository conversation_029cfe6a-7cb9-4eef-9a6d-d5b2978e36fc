/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.agent.AgentCacheManager;
import com.mindarray.agent.AgentConstants;
import com.mindarray.agent.AgentManager;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.RemoteEventForwarder;
import com.mindarray.eventbus.RemoteEventSubscriber;
import com.mindarray.eventbus.RemoteSessionManager;
import com.mindarray.job.JobScheduler;
import com.mindarray.util.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;

public class BootstrapAgent
{
    private static final Logger LOGGER = new Logger(BootstrapAgent.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Agent");
    private static String agentUUID;

    private static String localAddress = GlobalConstants.EMPTY_VALUE;

    public static String getAgentUUID()
    {
        return agentUUID;
    }

    public static void setAgentUUID(String agentUUID)
    {
        BootstrapAgent.agentUUID = agentUUID;
    }

    public static String getLocalAddress()
    {
        return localAddress;
    }

    private static void startAgent()
    {
        var futures = new ArrayList<Future<Object>>();

        var forwarderPort = CommonUtil.getEventPublisherPort();

        for (var index = 0; index < MotadataConfigUtil.getEventPublishers(); index++)
        {
            var promise = Promise.promise();

            futures.add(promise.future());

            var connection = PortUtil.test(CommonUtil.getRemoteEventPublisher(), forwarderPort + index, 3);

            if (MotadataConfigUtil.devMode() || connection.containsKey(GlobalConstants.STATUS) && connection.getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_UP))
            {
                localAddress = connection.getString(GlobalConstants.IP_ADDRESS);

                LOGGER.debug("local address is " + localAddress);

                Bootstrap.startEngine(new RemoteEventForwarder(forwarderPort + index), RemoteEventForwarder.class.getSimpleName() + " " + (forwarderPort + index), null).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        promise.complete();
                    }
                    else
                    {
                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                LOGGER.warn(String.format("failed to establish connection with %s:%s", CommonUtil.getRemoteEventPublisher(), forwarderPort + index));

                promise.fail(String.format("failed to establish connection with %s:%s", CommonUtil.getRemoteEventPublisher(), forwarderPort + index));

                break;
            }
        }

        Future.join(futures).onComplete(asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                try
                {
                    LOGGER.debug("sending for registration");

                    EventBusConstants.registerAgent(true);

                    var promises = new ArrayList<Future<Object>>();

                    var subscriberPort = CommonUtil.getEventSubscriberPort();

                    for (var index = 0; index < MotadataConfigUtil.getEventSubscribers(); index++)
                    {
                        var promise = Promise.promise();

                        promises.add(promise.future());

                        var connection = PortUtil.test(CommonUtil.getRemoteEventSubscriber(), subscriberPort + index, 3);

                        if (MotadataConfigUtil.devMode() || connection.containsKey(GlobalConstants.STATUS) && connection.getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_UP))
                        {
                            Bootstrap.startEngine(new RemoteEventSubscriber(subscriberPort + index), RemoteEventSubscriber.class.getSimpleName() + " " + (subscriberPort + index), null).onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(result.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.warn(String.format("failed to establish connection with %s:%s", CommonUtil.getRemoteEventSubscriber(), subscriberPort + index));

                            promise.fail(String.format("failed to establish connection with %s:%s", CommonUtil.getRemoteEventSubscriber(), subscriberPort + index));

                            break;
                        }
                    }

                    Future.join(promises).compose(future -> Bootstrap.startEngine(new AgentManager(), AgentManager.class.getSimpleName(), null))
                            .compose(future -> Bootstrap.startEngine(new AgentCacheManager(), AgentCacheManager.class.getSimpleName(), null))
                            .compose(future -> Bootstrap.startEngine(new HealthUtil(), HealthUtil.class.getSimpleName(), null))
                            .compose(future -> Bootstrap.startEngine(new RemoteSessionManager(), RemoteSessionManager.class.getSimpleName(), null))
                            .onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    JobScheduler.init();

                                    // first time when agent start, manually it will start all child agents
                                    AgentConstants.agents.forEach(agent -> ProcessUtil.start(agent, EventBusConstants.EVENT_AGENT_START));

                                    LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                                    LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.bootstrapType()));
                                }
                                else
                                {
                                    Bootstrap.stop(result.cause());
                                }
                            });
                }
                catch (Exception exception)
                {
                    Bootstrap.stop(exception.getCause());
                }
            }
            else
            {
                Bootstrap.stop(asyncResult.cause());
            }
        });
    }

    /*
     *   Port details for Agent/RemoteEventProcessor:
     *
     *   event.publisher.port : start from 9449
     *   event.subscriber.port :start from 9444
     * */

    public void start()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "agent.json");

            if (file.exists())
            {
                MotadataConfigUtil.loadConfigs(new JsonObject(Files.readString(new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json").toPath(), StandardCharsets.UTF_8)));

                LOGGER.info("motadata config loaded");

                AgentConfigUtil.loadAgentConfigs(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                LOGGER.info("agent config loaded");

                Bootstrap.logSystemConfig();

                PortUtil.init();

                WebClientUtil.init();

                ProcessUtil.setProcessors();

                startAgent();
            }
            else
            {
                LOGGER.warn(String.format("failed to find %s configuration file, hence aborting motadata boot process....", file.getName()));

                System.exit(0);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
