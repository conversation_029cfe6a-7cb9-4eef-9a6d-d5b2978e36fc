#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
import socket
import time
from base64 import b64encode

import paramiko
from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


class CiscoFirewallInterfaceSecurityMetricPlugin:

    def __init__(self):

        super().__init__()

        _client = None

        self.errors = []

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        try:
            if context.get(Constant.OBJECT_VENDOR) is not None and context.get(Constant.OBJECT_VENDOR) == Constant.VENDOR_CISCO_SYSTEMS:

                self._init(context)

                if self._client is not None:

                    output = self._execute_command(context)

                    if output is not None and len(output) > 0:

                        self._set_interface_metrics(output, result.get(Constant.RESULT))
            else:
                result[Constant.STATUS] = Constant.STATUS_FAIL

                result[Constant.ERROR_CODE] = Constant.ERROR_CODE_BAD_REQUEST

                result[Constant.MESSAGE] = "Invalid Vendor Type"

        except Exception as exception:

            self.errors.append({Constant.ERROR: str(Logger.get_stack_trace()),
                    Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        finally:

            if len(self.errors) > 0:
                result[Constant.ERRORS] = self.errors

            if len(result.get(Constant.RESULT)) > 0:
                result[Constant.STATUS] = Constant.STATUS_SUCCEED

        return result

    def _init(self, context):

        try:

            self._client = paramiko.SSHClient()

            self._client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            self._client.connect(context.get(Constant.OBJECT_IP), username=context.get(Constant.USER_NAME), password=context.get(Constant.PASSWORD),
                                 look_for_keys=False, allow_agent=False)

        except paramiko.AuthenticationException as exception:

            self.errors.append(
                {Constant.ERROR: str(exception), Constant.MESSAGE: Constant.ERROR_MESSAGE_INVALID_CREDENTIALS.format(str(context.get(Constant.OBJECT_IP)), str(context.get(Constant.PORT))), Constant.ERROR_CODE: Constant.ERROR_CODE_INVALID_CREDENTIALS})

        except paramiko.SSHException as exception:

            self.errors.append(
                {Constant.ERROR: str(exception), Constant.MESSAGE: Constant.ERROR_MESSAGE_CONNECTION_FAILED.format("SSH", str(context.get(Constant.OBJECT_IP)), str(context.get(Constant.PORT))), Constant.ERROR_CODE: Constant.ERROR_CODE_CONNECTION_FAILED})

        except (socket.timeout, TimeoutError) as exception:

            self.errors.append(
                {Constant.ERROR: str(exception), Constant.MESSAGE: Constant.ERROR_MESSAGE_CONNECTION_TIMEOUT.format("SSH", str(context.get(Constant.OBJECT_IP)), str(context.get(Constant.PORT))), Constant.ERROR_CODE: Constant.ERROR_CODE_TIMEOUT})

        except Exception as exception:

            self.errors.append(
                {Constant.ERROR: str(Logger.get_stack_trace()),  Constant.MESSAGE: str(exception), Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR})

        return None

    def _set_interface_metrics(self, output, result):

        tokens = str(output, 'utf-8').split("nameif")[1].split("\n")

        interfaces = []

        for token in tokens :

            if "Security" not in token and len(token.split())    > 2:

                interface = {}

                token = token.split()

                interface[Constant.INTERFACE] = token[1]

                interface["interface.security"] = token[2]

                interfaces.append(interface)

        if len(interfaces) > 0:

            result[Constant.INTERFACE] = interfaces

    def _execute_command(self, context):

        remote_conn = self._client.invoke_shell()

        remote_conn.send('enable\n')

        remote_conn.send(context.get("password") + '\n')

        remote_conn.send("show nameif\n")

        command_timeout = int(context.get("command.timeout")) if context.get("command.timeout") is not None else 1

        time.sleep(command_timeout)

        return remote_conn.recv(99999)

if __name__ == '__main__':

    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(CiscoFirewallInterfaceSecurityMetricPlugin().collect(Util.load_plugin_context(str(args.context))).encode()).decode(), end=Constant.BLANK_STRING)