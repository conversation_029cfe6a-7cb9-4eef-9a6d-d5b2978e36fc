/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	24-Mar-2025     Chandresh           MOTADATA-5428: Docker objects polling support for all child plugins
*/

package com.mindarray.nms;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.nms.NMSConstants.*;


/**
 * MetricScheduler is responsible for scheduling and managing the polling of metrics for monitored objects.
 * It periodically checks which metrics need to be polled based on their configured polling intervals,
 * validates business hours, prepares the necessary context information, and sends polling requests
 * to the appropriate handlers via the event bus.
 * <p>
 * This class handles various conditions such as:
 * - Checking if objects are within their business hours
 * - Validating object status (UP, DOWN, UNREACHABLE, etc.)
 * - Merging context information from objects, credentials, and metrics
 * - Handling special cases for different plugin types
 * - Processing manual polling requests from the UI
 */
public class MetricScheduler extends AbstractVerticle
{

    private static final Logger LOGGER = new Logger(MetricScheduler.class, GlobalConstants.MOTADATA_NMS, "Metric Scheduler");

    private static final int MINIMUM_POLLING_INTERVAL = 10;
    private static final JsonArray DAYS =
            new JsonArray(Arrays.asList("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"));
    private static final boolean CORRELATION_ENABLED = MotadataConfigUtil.correlationEnabled();
    private final List<Long> metrics = new ArrayList<>();

    /**
     * Initializes the MetricScheduler verticle.
     * <p>
     * This method sets up:
     * 1. A periodic timer that runs every MINIMUM_POLLING_INTERVAL seconds to check for metrics that need polling
     * 2. An event bus consumer to handle manual polling requests from the UI
     * <p>
     * The periodic timer:
     * - Retrieves metrics that need to be polled
     * - Validates business hours, object status, and other conditions
     * - Prepares the necessary context information
     * - Sends polling requests to the appropriate handlers via the event bus
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.setPeriodic(MINIMUM_POLLING_INTERVAL * 1000L, timer ->
        {
            // Run the scheduler every MINIMUM_POLLING_INTERVAL seconds
            // This is the main polling loop that checks for metrics that need to be polled

            // Clear the metrics list before populating it with metrics that need polling
            metrics.clear();

            MetricCacheStore.getStore().updateMetricInterval(MINIMUM_POLLING_INTERVAL, metrics);

            if (!metrics.isEmpty())
            {
                var timestamp = DateTimeUtil.currentSeconds();

                for (var id : metrics)
                {
                    try
                    {
                        var metric = MetricConfigStore.getStore().getItem(id);

                        if (metric != null)
                        {
                            var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                            if (object != null)
                            {

                                if (validBusinessHour(object.getLong(AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE)))
                                {
                                    if (metric.containsKey(Metric.METRIC_CREDENTIAL_PROFILE))
                                    {
                                        var credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                                        if (credential != null)
                                        {
                                            object.remove(GlobalConstants.ID);

                                            credential.remove(GlobalConstants.ID);

                                            if (object.containsKey(AIOpsObject.OBJECT_CONTEXT))
                                            {
                                                object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                                                object.remove(AIOpsObject.OBJECT_CONTEXT);
                                            }

                                            if (credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT))
                                            {
                                                credential.mergeIn(credential.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                                credential.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                                            }

                                            metric.mergeIn(object);

                                            if (metric.containsKey(Metric.METRIC_CONTEXT))
                                            {
                                                metric.mergeIn(metric.getJsonObject(Metric.METRIC_CONTEXT));

                                                metric.remove(Metric.METRIC_CONTEXT);
                                            }

                                            //Cloud metric update last poll time in metric
                                            //ignore main cloud plugin aws and azure
                                            if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(Category.CLOUD.getName()) && !MetricCacheStore.getStore().getMetricPollTimestamps(metric.getLong(Metric.METRIC_OBJECT)).isEmpty() && (!(object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.AWS_CLOUD.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(Type.AZURE_CLOUD.getName()))))
                                            {
                                                metric.put(CommonUtil.getString(metric.getValue(PLUGIN_ID)), new JsonObject().put(Metric.METRIC_POLLING_TIME, metric.getInteger(Metric.METRIC_POLLING_TIME)).put(NMSConstants.LAST_POLL_TIME, MetricCacheStore.getStore().getMetricPollTimestamps(metric.getLong(Metric.METRIC_OBJECT)).getLong(EventBusConstants.EVENT_TIMESTAMP)));
                                            }

                                            if (metric.containsKey(NMSConstants.SNMP_OID_GROUP_ID))
                                            {
                                                //merge oid group oids for particular oid.group.id
                                                metric.mergeIn(SNMPTemplateOIDGroupCacheStore.getStore()
                                                        .getItem(CommonUtil.getString(metric.remove(NMSConstants.SNMP_OID_GROUP_ID))));

                                                if (metric.getString(NMSConstants.SNMP_OID_GROUP_TYPE).equalsIgnoreCase(OIDGroupType.TABULAR.getName()) &&
                                                        metric.containsKey(NMSConstants.SNMP_OID_GROUP_PARENT_OID)) //for tabular metric group
                                                {
                                                    //put oid name in parent.oid key
                                                    metric.put(NMSConstants.SNMP_OID_GROUP_PARENT_OID, metric.getJsonObject(NMSConstants.SNMP_OID_GROUP_OIDS).getString(metric.getString(NMSConstants.SNMP_OID_GROUP_PARENT_OID)));
                                                }
                                            }

                                            // MOTADATA-5428: this allows us to provision object in main metric plugin only and pass all objects present there into all it's downstream plugins
                                            if (UPSTREAM_PLUGIN_MAPPERS.containsKey(metric.getString(Metric.METRIC_PLUGIN)))
                                            {
                                                var item = MetricConfigStore.getStore().getItemByMetricPlugin(ObjectConfigStore.getStore().getItemByObjectId(object.getInteger(AIOpsObject.OBJECT_ID)).getLong(ID), UPSTREAM_PLUGIN_MAPPERS.get(metric.getString(Metric.METRIC_PLUGIN)));

                                                if (item != NOT_AVAILABLE)
                                                {
                                                    var context = MetricConfigStore.getStore().getItem(item).getJsonObject(Metric.METRIC_CONTEXT);

                                                    if (context != null && context.getValue(OBJECTS) != null && !context.getJsonArray(OBJECTS).isEmpty())
                                                    {
                                                        metric.put(OBJECTS, context.getJsonArray(OBJECTS));
                                                    }
                                                }
                                            }

                                            var eventId = CommonUtil.newEventId();

                                            metric.put(EventBusConstants.EVENT_ID, eventId).put(ID, id);

                                            vertx.eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_ID, eventId)
                                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL)
                                                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                                    .put(USER_NAME, DEFAULT_USER)
                                                    .put(EventBusConstants.EVENT_CONTEXT, metric));

                                            var valid = true;

                                            //abort event if metric is qualified in rediscover job metric plugins and object is not present
                                            if (NMSConstants.REDISCOVER_JOB_METRIC_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)) &&
                                                    (metric.getJsonArray(OBJECTS) == null || metric.getJsonArray(OBJECTS).isEmpty()))
                                            {
                                                valid = false;

                                                metric.put(EventBusConstants.EVENT_TIMESTAMP, timestamp);

                                                //why put master uuid here bcz if any object is not found then event will not forward to event router and at that time
                                                //we don't have uuid...so continue that event flow in task manager we put master uuid so it will avoid null error in responseprocessor
                                                metric.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

                                                vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, metric.put(STATUS, STATUS_ABORT)
                                                        .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLLER_NO_OBJECT_FOUND, metric.getString(Metric.METRIC_NAME)))))
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                                        .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLLER_NO_OBJECT_FOUND, metric.getString(Metric.METRIC_NAME))));
                                            }

                                            //if device goes down and correlation is not enabled, then we will put unreachable stat in its child instances
                                            if (((metric.containsKey(OBJECTS) && DISCOVERABLE_INSTANCES.containsKey(metric.getString(Metric.METRIC_PLUGIN)) && !CORRELATION_ENABLED) || MotadataConfigUtil.devMode()) && STATUS_DOWN.equalsIgnoreCase(ObjectStatusCacheStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT))))
                                            {
                                                valid = false;

                                                var objects = metric.getJsonArray(OBJECTS);

                                                var items = new JsonArray();

                                                var instance = DISCOVERABLE_INSTANCES.get(metric.getString(Metric.METRIC_PLUGIN));

                                                if (objects != null && !objects.isEmpty())
                                                {
                                                    for (var j = 0; j < objects.size(); j++)
                                                    {
                                                        var item = new JsonObject();

                                                        var entry = objects.getJsonObject(j);

                                                        if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
                                                        {
                                                            item.put(instance, entry.getString(INTERFACE_INDEX));
                                                        }
                                                        else if (entry.containsKey(AIOpsObject.OBJECT_NAME))
                                                        {
                                                            item.put(instance, entry.getString(AIOpsObject.OBJECT_NAME));
                                                        }
                                                        else
                                                        {
                                                            item.put(instance, entry.getString(instance));
                                                        }

                                                        if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                                                        {
                                                            item.put(PORT, CommonUtil.getInteger(entry.getString(AIOpsObject.OBJECT_NAME).split("\\(")[0].trim()));
                                                        }

                                                        item.put(STATUS, STATUS_UNREACHABLE);

                                                        items.add(item);
                                                    }

                                                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, metric.put(STATUS, STATUS_SUCCEED).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL).put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(RESULT, new JsonObject().put(DISCOVERABLE_INSTANCES.get(metric.getString(Metric.METRIC_PLUGIN)), items)));
                                                }
                                            }


                                            if (valid)
                                            {
                                                metric.put(EventBusConstants.EVENT_TIMESTAMP, timestamp);

                                                // port checking for qualified object types and if port is up and present in network service cache store than only schedule else fail that metric polling with invalid port error message
                                                if (!NMSConstants.isStatusCheckMetric(metric.getString(Metric.METRIC_PLUGIN))
                                                        && !metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName())
                                                        && NMSConstants.portCheckRequire(Type.valueOfName(metric.getString(Metric.METRIC_TYPE))) && metric.containsKey(PORT))
                                                {
                                                    var status = NetworkServiceCacheStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT), metric.getInteger(PORT));

                                                    valid = status == null || status.equalsIgnoreCase(STATUS_UP);
                                                }

                                                if (valid)
                                                {
                                                    metric.put(EventBusConstants.EVENT_TIMESTAMP, timestamp);
                                                    // for snmp scalar metric check that any valid oid is present and if not present then do not qualify that metric
                                                    if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName()) && metric.getJsonArray(SNMP_OID_GROUP_INVALID_OIDS) != null)
                                                    {
                                                        var iterator = metric.getJsonObject(SNMP_OID_GROUP_OIDS).iterator();

                                                        while (iterator.hasNext())
                                                        {
                                                            if (metric.getJsonArray(SNMP_OID_GROUP_INVALID_OIDS).contains(iterator.next().getKey()))
                                                            {
                                                                iterator.remove();
                                                            }
                                                        }

                                                        if (metric.getJsonObject(SNMP_OID_GROUP_OIDS).isEmpty())
                                                        {
                                                            valid = false;

                                                            LOGGER.warn(String.format("metric %s of object %s could not be scheduled to poll, reason: %s ...", metric.getString(Metric.METRIC_NAME), object.getString(AIOpsObject.OBJECT_NAME), ErrorMessageConstants.SNMP_OID_GROUP_INVALID));

                                                            vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE,
                                                                    metric.put(GlobalConstants.STATUS, STATUS_FAIL)
                                                                            .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_OID_GROUP).put(MESSAGE, ErrorMessageConstants.SNMP_OID_GROUP_INVALID)))
                                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_OID_GROUP)
                                                                            .put(MESSAGE, String.format(ErrorMessageConstants.METRIC_POLL_FAILED, ErrorMessageConstants.SNMP_OID_GROUP_INVALID)));

                                                        }
                                                    }

                                                    if (valid)
                                                    {
                                                        // for last poll time
                                                        metric.put(EventBusConstants.EVENT_TIMESTAMP, timestamp);

                                                        if (metric.getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(Category.NETWORK.getName()))
                                                        {
                                                            metric.put(SNMP_CHECK_TIMEOUT_SECONDS, NMSConstants.getSNMPCheckTimeoutSeconds(MetricTimeoutValueType.valueOfName(NMSConstants.getMetricTimeoutValueType(metric))));
                                                        }

                                                        var status = ObjectStatusCacheStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                                                        if (status != null)
                                                        {
                                                            var statusCheckMetric = NMSConstants.isStatusCheckMetric(metric.getString(Metric.METRIC_PLUGIN)) || object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(Category.SERVICE_CHECK.getName());

                                                            if (CommonUtil.traceEnabled())
                                                            {
                                                                LOGGER.trace(String.format("object : %s with status : %s ", object.getString(AIOpsObject.OBJECT_NAME), status));

                                                            }

                                                            // Handle different object statuses and determine if the metric should be polled
                                                            switch (status)
                                                            {
                                                                case STATUS_DISABLE, STATUS_MAINTENANCE ->
                                                                {
                                                                    // Don't poll metrics for disabled or maintenance objects
                                                                    valid = false;

                                                                    if (statusCheckMetric)
                                                                    {
                                                                        // For availability metrics, update the status in the cache
                                                                        metric.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

                                                                        ObjectStatusCacheStore.getStore().updateItem(metric.getLong(Metric.METRIC_OBJECT), status, timestamp);
                                                                    }
                                                                }

                                                                case STATUS_DOWN ->
                                                                {
                                                                    if (!statusCheckMetric)
                                                                    {
                                                                        // If status is down, only poll availability metrics
                                                                        // For other metrics, don't poll and update instance statuses
                                                                        valid = false;

                                                                        // Update status of child instances if present
                                                                        if (metric.containsKey(OBJECTS) && !metric.getJsonArray(OBJECTS).isEmpty())
                                                                        {
                                                                            var items = ObjectStatusCacheStore.getStore().getInstanceItems(metric.getLong(Metric.METRIC_OBJECT), false);

                                                                            if (items != null)
                                                                            {
                                                                                for (var entry : items.entrySet())
                                                                                {
                                                                                    if (entry.getKey().contains(INSTANCE_SEPARATOR))
                                                                                    {
                                                                                        // If parent object is down, child instances should be marked as unreachable
                                                                                        ObjectStatusCacheStore.getStore().updateItem(metric.getLong(Metric.METRIC_OBJECT), entry.getKey(), status.equalsIgnoreCase(STATUS_DOWN) ? STATUS_UNREACHABLE : entry.getValue(), timestamp);
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }

                                                                case STATUS_UNREACHABLE ->
                                                                {
                                                                    // By default, don't poll metrics for unreachable objects
                                                                    valid = false;

                                                                    if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(Category.SERVICE_CHECK.getName()))
                                                                    {
                                                                        // Exception: For service check category, poll even if status is unreachable
                                                                        valid = true;
                                                                    }
                                                                    else if (statusCheckMetric)
                                                                    {
                                                                        // For availability metrics, update the status in the cache
                                                                        metric.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());

                                                                        ObjectStatusCacheStore.getStore().updateItem(metric.getLong(Metric.METRIC_OBJECT), status, timestamp);
                                                                    }
                                                                }

                                                                default ->
                                                                    // For other statuses (like UP, Unknown), continue with availability polling & for other metrics check if they are eligible for polling
                                                                        valid = NMSConstants.isStatusCheckMetric(metric.getString(Metric.METRIC_PLUGIN)) || ObjectStatusCacheStore.getStore().eligible(metric.getLong(Metric.METRIC_OBJECT), EMPTY_VALUE);
                                                            }
                                                        }

                                                        if (valid)
                                                        {
                                                            vertx.eventBus().send(EventBusConstants.EVENT_ROUTER, metric.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_METRIC_POLL).mergeIn(credential, true));

                                                            if (CommonUtil.debugEnabled())
                                                            {
                                                                LOGGER.debug(String.format("metric %s of object %s is scheduled to poll", metric.getString(Metric.METRIC_NAME), metric.getString(AIOpsObject.OBJECT_NAME)));
                                                            }
                                                        }
                                                    }
                                                }

                                                else
                                                {
                                                    LOGGER.warn(String.format("metric %s of object %s could not be scheduled to poll, reason: %s ...", metric.getString(Metric.METRIC_NAME), object.getString(AIOpsObject.OBJECT_NAME), String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, metric.getInteger(PORT))));

                                                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, metric.put(STATUS, STATUS_FAIL)
                                                            .put(ERRORS, new JsonArray(new ArrayList<JsonObject>(1)).add(new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT).put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, metric.getInteger(PORT)))))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_PORT)
                                                            .put(MESSAGE, String.format(ErrorMessageConstants.PORT_CONNECTION_FAILED, metric.getInteger(PORT))));
                                                }
                                            }
                                        }

                                        else
                                        {
                                            LOGGER.warn(String.format("metric %s of object %s could not be scheduled to poll, no valid credential profile found in the store...", metric.getString(Metric.METRIC_NAME), object.getString(AIOpsObject.OBJECT_NAME)));
                                        }

                                    }

                                    else
                                    {
                                        LOGGER.warn(String.format("metric %s of object %s could not be scheduled to poll, no valid credential profile found...", metric.getString(Metric.METRIC_NAME), object.getString(AIOpsObject.OBJECT_NAME)));
                                    }
                                }
                                else
                                {
                                    LOGGER.warn(String.format("metric %s of object %s skipped to poll, business hour is not valid...", metric.getString(Metric.METRIC_NAME), object.getString(AIOpsObject.OBJECT_NAME)));
                                }
                            }

                            else
                            {
                                LOGGER.debug(String.format("object %s removed from scheduler...", metric.getLong(Metric.METRIC_OBJECT)));
                            }
                        }

                        else
                        {
                            LOGGER.debug(String.format("metric %s removed from scheduler...", id));
                        }

                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }
        });

        // manual poll event came from UI side
        vertx.eventBus().<Long>localConsumer(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, message ->
        {
            LOGGER.info(String.format("manual poll event received for object %s", ObjectConfigStore.getStore().getObjectName(message.body())));

            MetricConfigStore.getStore().getItemsByObject(message.body()).stream().filter(item -> !item.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.INLINE.name()) && item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                    .forEach(metric -> MetricCacheStore.getStore().addMetric(metric.getLong(ID), 10));
        });

        promise.complete();
    }

    /**
     * Validates whether the current time is within the business hours defined for an object.
     * <p>
     * This method checks:
     * - If the businessHourId is DEFAULT_ID, it always returns true
     * - Otherwise, it retrieves the business hour profile from the store
     * - Checks if the current day of the week is included in the business hours
     * - Checks if the current hour is included in the allowed hours for the current day
     *
     * @param businessHourId The ID of the business hour profile to validate
     * @return true if the current time is within the business hours, false otherwise
     */
    private boolean validBusinessHour(long businessHourId)
    {

        // Default to invalid business hour
        var valid = false;

        try
        {
            // If businessHourId is DEFAULT_ID, always consider it valid (24x7)
            if (businessHourId == DEFAULT_ID)
            {
                valid = true;
            }
            else
            {
                // Get the business hour profile from the store
                var item = BusinessHourConfigStore.getStore().getItem(businessHourId);

                // Get the current date and time
                var calendar = Calendar.getInstance();

                if (item != null && item.containsKey(BusinessHour.BUSINESS_HOUR_CONTEXT))
                {
                    // Get the allowed hours for the current day of the week
                    // Calendar.DAY_OF_WEEK is 1-based (Sunday=1), so subtract 1 to match our 0-based DAYS array
                    var hours = item.getJsonObject(BusinessHour.BUSINESS_HOUR_CONTEXT)
                            .getJsonArray(DAYS.getString(calendar.get(Calendar.DAY_OF_WEEK) - 1));

                    if (hours != null && !hours.isEmpty())
                    {
                        // Check if the current hour is in the allowed hours for today
                        for (var index = 0; index < hours.size(); index++)
                        {
                            if (calendar.get(Calendar.HOUR_OF_DAY) == hours.getInteger(index))
                            {
                                // Current hour is allowed, so business hour is valid
                                valid = true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return valid;
    }


}
