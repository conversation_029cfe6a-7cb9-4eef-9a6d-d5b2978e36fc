[{"_type": "0", "id": 10000000020016, "visualization.name": "Local Capacity", "visualization.description": "Cluster Storage capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.local.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.local.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.local.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.local.used.percent"}, {"type": "metric", "data.point": "hpe.storeonce.local.capacity.bytes"}, {"type": "metric", "data.point": "hpe.storeonce.local.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Local Capacity", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "hpe.storeonce.local.capacity.bytes.last"}, {"label": "Free", "value": "hpe.storeonce.local.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.storeonce.local.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hpe.storeonce.local.used.percent"}}}}, {"_type": "0", "id": 10000000020017, "visualization.name": "Cloud Capacity", "visualization.description": "Cloud capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.cloud.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cloud.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cloud.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.cloud.used.percent"}, {"type": "metric", "data.point": "hpe.storeonce.cloud.capacity.bytes"}, {"type": "metric", "data.point": "hpe.storeonce.cloud.free.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Cloud Capacity", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "hpe.storeonce.cloud.capacity.bytes.last"}, {"label": "Free", "value": "hpe.storeonce.cloud.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "hpe.storeonce.cloud.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "hpe.storeonce.cloud.used.percent"}}}}, {"_type": "0", "id": 10000000020018, "visualization.name": "Pool", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.pools", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.pools"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "storage-pool"}, "font.size": "medium", "color.data.point": "hpe.storeonce.pools"}, "header": {"title": "Pool", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.storeonce.pools.last"}]}}}}, {"_type": "0", "id": 10000000020019, "visualization.name": "volume", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.volumes"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "database"}, "font.size": "medium", "color.data.point": "hpe.storeonce.volumes"}, "header": {"title": "Volume", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.storeonce.volumes.last"}]}}}}, {"_type": "0", "id": 10000000020020, "visualization.name": "Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.disks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.disks"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "disk", "placement": "prefix"}, "font.size": "medium", "color.data.point": "hpe.storeonce.disks"}, "header": {"title": "Disk", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.storeonce.disks.last"}]}}}}, {"_type": "0", "id": 10000000020021, "visualization.name": "Service Set", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.service.sets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "hpe.storeonce.service.sets"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "service-set"}, "font.size": "medium", "color.data.point": "hpe.storeonce.service.sets"}, "header": {"title": "Service Set", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "hpe.storeonce.service.sets.last"}]}}}}, {"_type": "0", "id": 10000000020022, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000020023, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000020024, "visualization.name": "Capacity Utilization", "visualization.description": "HPE Capacity Utilization", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.local.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.local.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.local.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000020025, "visualization.name": "Port Details", "visualization.category": "Grid", "visualization.type": "power supply", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.port~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.port~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.port~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.port~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.port~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Port Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.port", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.port~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.port~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "hpe.storeonce.port~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status"}, {"name": "hpe.storeonce.port~type.last", "title": "type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.storeonce.port~speed.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}}, {"_type": "0", "id": 10000000020026, "visualization.name": "Cage Details", "visualization.category": "Grid", "visualization.type": "Cage Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.cage~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cage~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cage~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cage~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cage~warranty.serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.cage~drive.carrier", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Cage Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.cage", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.cage~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.cage~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 4}, {"name": "hpe.storeonce.cage~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.storeonce.cage~serial.number.last", "title": "SERIAL NUMBER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.storeonce.cage~warranty.serial.number.last", "title": "WARRANTY SERIAL NUMBER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.storeonce.cage~drive.carrier.last", "title": "DRIVE CARRIER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"_type": "0", "id": 10000000020027, "visualization.name": "RAID Controller Details", "visualization.category": "Grid", "visualization.type": "RAID Controller Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.raid.controller~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.raid.controller~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.raid.controller~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.raid.controller~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "RAID Controller Details", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "hpe.storeonce.raid.controller", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.raid.controller~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.raid.controller~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4, "type": "status"}, {"name": "hpe.storeonce.raid.controller~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.storeonce.raid.controller~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"_type": "0", "id": 10000000020028, "visualization.name": "Disk Summary", "visualization.category": "Grid", "visualization.type": "node", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.disk~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.disk~location", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Disk Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.disk", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.disk~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.disk~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "hpe.storeonce.disk~type.last", "title": "Disk Type", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5, "type": "port"}, {"name": "hpe.storeonce.disk~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.storeonce.disk~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "hpe.storeonce.disk~speed.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.storeonce.disk~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.storeonce.disk~capacity.bytes.last", "title": "Total capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}]}}}, {"_type": "0", "id": 10000000020029, "visualization.name": "Volume Summary", "visualization.category": "Grid", "visualization.type": "Cage Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.volume~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.volume~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.volume~local.device", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.volume~type", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Volume Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.volume", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.volume~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.volume~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "hpe.storeonce.volume~capacity.bytes.last", "title": "TOTAL CAPACITY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "hpe.storeonce.volume~local.device.last", "title": "LOCAL DEVICE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"]}}, {"name": "hpe.storeonce.volume~type.last", "title": "TYPE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}]}}}, {"_type": "0", "id": 10000000020030, "visualization.name": "Pool Summary", "visualization.category": "Grid", "visualization.type": "Pool Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.pool~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.pool~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.pool~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Pool Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.pool", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.pool~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.pool~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "hpe.storeonce.pool~capacity.bytes.last", "title": "TOTAL CAPACITY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}]}}}, {"_type": "0", "id": 10000000020031, "visualization.name": "Service Set Summary", "visualization.category": "Grid", "visualization.type": "Service Set Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.service.set~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~health.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~dedupe.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service.set~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Service Set Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.service.set", "title": "ssid", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.service.set~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.service.set~health.status.last", "title": "Health", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "hpe.storeonce.service.set~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "hpe.storeonce.service.set~capacity.bytes.last", "title": "Total capacity", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 6, "type": "port"}, {"name": "hpe.storeonce.service.set~used.bytes.last", "title": "Used capacity", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7, "type": "port"}, {"name": "hpe.storeonce.service.set~free.bytes.last", "title": "FREE CAPACITY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "hpe.storeonce.service.set~dedupe.ratio.percent.last", "title": "DEPUDE RATIO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.storeonce.service.set~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}]}}}, {"_type": "0", "id": 10000000020032, "visualization.name": "Services Summary", "visualization.category": "Grid", "visualization.type": "Services Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "hpe.storeonce.service~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~health.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~disk.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~user.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~cloud.disk.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~dedupe.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~items", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~files", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~replication.role", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "hpe.storeonce.service~encryption", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "view": "Services Summary", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "hpe.storeonce.service", "title": "uuid", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "hpe.storeonce.service~type.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "hpe.storeonce.service~name.last", "title": "STORE/LIBRARY/SHARE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4}, {"name": "hpe.storeonce.service~health.status.last", "title": "Health", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status"}, {"name": "hpe.storeonce.service~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "hpe.storeonce.service~disk.bytes.last", "title": "DISK BYTES", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 7}, {"name": "hpe.storeonce.service~user.bytes.last", "title": "USER BYTES", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8}, {"name": "hpe.storeonce.service~cloud.disk.bytes.last", "title": "CLOUD DISK BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9}, {"name": "hpe.storeonce.service~dedupe.ratio.percent.last", "title": "DEPUDE RATIO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10}, {"name": "hpe.storeonce.service~items.last", "title": "ITEMS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "hpe.storeonce.service~files.last", "title": "FILE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "hpe.storeonce.service~replication.role.last", "title": "REPLICATION ROLE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}, {"name": "hpe.storeonce.service~encryption.last", "title": "ENCRYPTION", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {}}]}}}]