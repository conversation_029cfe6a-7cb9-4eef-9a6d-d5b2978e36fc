/*
 * Copyright (c) Motadata 2025. All rights reserved.
 */

package main

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"motadatasdk/consts"
	"motadatasdk/motadatatypes"
	"motadatasdk/utils"
	"os"
)

func main() {

	context, err := utils.LoadPluginContext(os.Args[2:][0])

	if err != nil {

		bytes, _ := json.Marshal(motadatatypes.MotadataMap{

			consts.Status: consts.StatusFail,

			consts.Errors: []motadatatypes.MotadataStringMap{
				{
					consts.ErrorCode: consts.ErrorCodeInternalError,
					consts.Error:     fmt.Sprintf("%v", err),
					consts.Message:   "Failed to load context",
				}},
		})

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)

	} else {

		result := make(motadatatypes.MotadataMap)

		run(result, context)

		bytes, err := json.Marshal(result)

		if err != nil {

			bytes, _ = json.Marshal(motadatatypes.MotadataMap{

				consts.Status: consts.StatusFail,

				consts.Errors: []motadatatypes.MotadataStringMap{
					{
						consts.ErrorCode: consts.ErrorCodeInternalError,
						consts.Error:     fmt.Sprintf("%v", err),
						consts.Message:   "Invalid Result",
					}},
			})
		}

		fmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)
	}
}

func run(result, context motadatatypes.MotadataMap) {

	//write your code here...

	//after successful execution of the code, make sure that result contains key 'status' with the value 'succeed'
	result[consts.Status] = consts.StatusSucceed

}
