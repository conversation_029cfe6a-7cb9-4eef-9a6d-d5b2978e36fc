/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.store.AuthTokenConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.PersonalAccessToken.PERSONAL_ACCESS_TOKEN;
import static com.mindarray.api.User.*;
import static org.apache.http.HttpStatus.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(30 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public
class TestMyAccount
{
    private static final Logger LOGGER = new Logger(TestMyAccount.class, MOTADATA_API, "Test MyAccount");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllUsers(VertxTestContext testContext)
    {
        TestAPIUtil.get(MY_ACCOUNT_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            body = body.getJsonObject(RESULT);

            Assertions.assertNotNull(body);

            Assertions.assertFalse(body.isEmpty());

            Assertions.assertTrue(body.containsKey(APIConstants.Entity.USER.getName()));

            Assertions.assertTrue(body.containsKey(APIConstants.Entity.USER_ROLE.getName()));

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateMyAccount(VertxTestContext testContext)
    {
        TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + "/" + 1234L,
                new JsonObject().put(USER_NAME, "adminUser"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_FORBIDDEN, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(ErrorMessageConstants.MY_ACCOUNT_UPDATE_ACTION_NOT_ALLOWED, body.getString(MESSAGE));

                            assertEquals(STATUS_FAIL, body.getString(STATUS));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateSystemUserAccount(VertxTestContext testContext, TestInfo testInfo)
    {
        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "user1");

        Assertions.assertNotNull(user);

        LOGGER.info(String.format("Updating user %s", user.encode()));

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, user.getString(USER_NAME))
                .put(USER_PASSWORD, user.getString(USER_PASSWORD)), asyncResponse ->
        {
            if (asyncResponse.succeeded())
            {
                var response = asyncResponse.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                LOGGER.info(String.format("Successfully updated user with response : %s ", body.encode()));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + user.getLong(ID),
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        user.put(User.USER_OLD_PASSWORD, user.getString(User.USER_PASSWORD))
                                .put(User.USER_PASSWORD, user.getString(User.USER_PASSWORD) + "-new").put(USER_NAME, user.getString(USER_NAME)),
                        testContext.succeeding(asyncResult ->
                                testContext.verify(() ->
                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), timer ->
                                        {
                                            LOGGER.info(String.format("My account updated successfully with response : %s ", asyncResult.bodyAsJsonObject().encode()));

                                            LOGGER.info(String.format("Updating new user %s", UserConfigStore.getStore().getItemByValue(USER_NAME, "user1").encode()));

                                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), user, asyncResult.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.MY_ACCOUNT.getName()), new JsonArray().add(User.USER_PREFERENCES).add(User.USER_PASSWORD_LAST_UPDATED_TIME).add(User.USER_TEMPORARY_PASSWORD_CREATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                                            assertNull(AuthTokenConfigStore.getStore().getItemByValue(USER_ID, user.getLong(ID)));

                                            testContext.completeNow();
                                        }))));
            }
            else
            {
                LOGGER.error(asyncResponse.cause());

                testContext.failNow(asyncResponse.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testUpdateSystemUserAccountInvalidPassword0(VertxTestContext testContext)
    {
        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "user1");

        Assertions.assertNotNull(user);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, user.getString(USER_NAME))
                .put(USER_PASSWORD, user.getString(USER_PASSWORD)), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var response = asyncResult.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + "/" + user.getLong(ID),
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        user.put(User.USER_OLD_PASSWORD, "123456")
                                .put(User.USER_PASSWORD, user.getString(User.USER_PASSWORD)).put(USER_NAME, user.getString(USER_NAME)),
                        testContext.succeeding(asyncResponse ->
                                testContext.verify(() ->
                                {
                                    assertEquals(SC_BAD_REQUEST, asyncResponse.statusCode());

                                    var result = asyncResponse.bodyAsJsonObject();

                                    Assertions.assertNotNull(result);

                                    assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                                    assertEquals(STATUS_FAIL, result.getString(STATUS));

                                    assertEquals(ErrorCodes.ERROR_CODE_INVALID_CREDENTIALS, result.getString(ERROR_CODE));

                                    assertEquals(ErrorMessageConstants.INCORRECT_OLD_PASSWORD, result.getString(MESSAGE));

                                    testContext.completeNow();
                                })));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                testContext.failNow(asyncResult.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateSystemUserAccountInvalidPassword1(VertxTestContext testContext)
    {
        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "user1");

        Assertions.assertNotNull(user);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, user.getString(USER_NAME))
                .put(USER_PASSWORD, user.getString(USER_PASSWORD)), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var response = asyncResult.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + "/" + user.getLong(ID),
                        new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN)),
                        user.put(User.USER_OLD_PASSWORD, user.getString(User.USER_PASSWORD))
                                .put(User.USER_PASSWORD, "admin").put(USER_NAME, user.getString(USER_NAME)),
                        testContext.succeeding(asyncResponse ->
                                testContext.verify(() ->
                                {
                                    assertEquals(SC_BAD_REQUEST, asyncResponse.statusCode());

                                    var result = asyncResponse.bodyAsJsonObject();

                                    Assertions.assertNotNull(result);

                                    assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                                    assertEquals(STATUS_FAIL, result.getString(STATUS));

                                    assertEquals(ErrorCodes.ERROR_CODE_PASSWORD_POLICY_NOT_SATISFIED, result.getString(ERROR_CODE));

                                    assertEquals(ErrorMessageConstants.PASSWORD_POLICY_NOT_SATISFIED, result.getString(MESSAGE));

                                    testContext.completeNow();
                                })));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                testContext.failNow(asyncResult.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    @Timeout(120 * 1000)
    void testUpdateLDAPUserAccount(VertxTestContext testContext, TestInfo testInfo)
    {
        var user = UserConfigStore.getStore().getItemByValue(USER_NAME, "chandu");

        Assertions.assertNotNull(user);

        LOGGER.info("LDAP User : " + user);

        var context = new JsonObject().put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_NAME, "Test Update LDAP User Account")
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_USER, user.getLong(ID))
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_VALIDITY, "30 days")
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_DESCRIPTION, "This is test token");

        TestAPIUtil.post(USER_API_ENDPOINT + "/" + user.getLong(ID) + "/generate-token", context, response ->
        {
            if (response.succeeded())
            {
                var body = response.result().bodyAsJsonObject();

                LOGGER.info(String.format("API response : %s ", body.encode()));

                Assertions.assertNotNull(response);

                Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

                Assertions.assertFalse(body.getString(RESULT).isEmpty());

                TestAPIUtil.post(PERSONAL_ACCESS_TOKEN_API_ENDPOINT, context.put(PERSONAL_ACCESS_TOKEN, body.getString(RESULT)), asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var result = asyncResult.result().bodyAsJsonObject();

                        LOGGER.info(String.format("API response : %s ", result.encode()));

                        Assertions.assertNotNull(result);

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        TestAPIUtil.put(MY_ACCOUNT_API_ENDPOINT + "/" + user.getLong(ID),
                                new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(RESULT)),
                                user, asyncResponse ->
                                {
                                    try
                                    {

                                        if (asyncResponse.succeeded())
                                        {
                                            LOGGER.info(asyncResponse.result().bodyAsString());

                                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), user, asyncResponse.result().bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.MY_ACCOUNT.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                                            testContext.completeNow();
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResponse.cause());

                                            testContext.failNow(asyncResponse.cause().getMessage());
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        testContext.failNow(exception);
                                    }

                                });

                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(response.cause());

                LOGGER.error(response.cause());
            }
        });
    }
}
