/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.nms;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.SNMPTrapListenerConfiguration;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SNMPTrapListenerConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonObject;
import org.snmp4j.*;
import org.snmp4j.mp.MPv1;
import org.snmp4j.mp.MPv2c;
import org.snmp4j.mp.MPv3;
import org.snmp4j.mp.MessageProcessingModel;
import org.snmp4j.security.*;
import org.snmp4j.security.nonstandard.PrivAESWith3DESKeyExtension;
import org.snmp4j.smi.Address;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.UdpAddress;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import org.snmp4j.util.MultiThreadedMessageDispatcher;
import org.snmp4j.util.ThreadPool;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.DEFAULT_ID;
import static com.mindarray.GlobalConstants.EMPTY_VALUE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;

/**
 * SNMPTrapListener is responsible for receiving and processing SNMP trap messages from network devices.
 * <p>
 * This class provides functionality to:
 * - Initialize and start SNMP trap listeners for different SNMP versions (v1, v2c, v3)
 * - Configure security protocols and authentication mechanisms for SNMP v3
 * - Process incoming trap messages and forward them to the appropriate handlers
 * - Manage listener lifecycle (start, stop, cleanup)
 * - Support various encryption and authentication protocols
 * <p>
 * The listener supports multiple SNMP versions simultaneously and handles the complexities
 * of SNMP v3 security including authentication, privacy, and user-based security models.
 * <p>
 * This class is implemented as a singleton with static methods to ensure only one
 * instance of each listener type exists in the system.
 */
public class SNMPTrapListener
{
    private static final Logger LOGGER = new Logger(SNMPTrapListener.class, GlobalConstants.MOTADATA_NMS, "SNMP Trap Listener");
    private static final Map<Long, JsonObject> items = new ConcurrentHashMap<>();
    private static Snmp v1v2Listener = null;

    private static Snmp v3Listener = null;

    private static MessageDispatcher v1v2MessageDispatcher = null;

    private static MessageDispatcher v3MessageDispatcher = null;

    private SNMPTrapListener()
    {
    }

    public static void init()
    {
        try
        {
            var usm = new USM(SecurityProtocols.getInstance().addDefaultProtocols(), new OctetString(MPv3.createLocalEngineID()), 0);

            usm.setEngineDiscoveryEnabled(true);

            SecurityModels.getInstance().addSecurityModel(usm);

            SecurityProtocols.getInstance().addAuthenticationProtocol(new AuthSHA());

            SecurityProtocols.getInstance().addAuthenticationProtocol(new AuthMD5());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES128());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES192());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES256());

            SecurityProtocols.getInstance().addPrivacyProtocol(new Priv3DES());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivDES());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES256C());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES256G());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES192C());

            SecurityProtocols.getInstance().addPrivacyProtocol(new PrivAES192G());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void start()
    {
        try
        {
            var item = SNMPTrapListenerConfigStore.getStore().getItem();

            items.put(DEFAULT_ID, item);

            if (item.containsKey(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_STATUS) && item.getString(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_STATUS).equalsIgnoreCase(GlobalConstants.YES))
            {
                v1v2MessageDispatcher = new MultiThreadedMessageDispatcher(ThreadPool.create("SNMP Trap v1/v2C Dispatcher Pool",
                        MotadataConfigUtil.getTrapDispatchers()), new MessageDispatcherImpl());

                try (TransportMapping<UdpAddress> transport = new DefaultUdpTransportMapping(new UdpAddress(item.getInteger(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_PORT))))
                {
                    v1v2Listener = new Snmp(v1v2MessageDispatcher, transport);
                }

                v1v2Listener.getMessageDispatcher().addMessageProcessingModel(new MPv1());

                v1v2Listener.getMessageDispatcher().addMessageProcessingModel(new MPv2c());

                v1v2Listener.listen();

                v1v2Listener.addCommandResponder(SNMPTrapListener::send);

                LOGGER.info(String.format("snmp v1/v2c trap listener started on port %s", item.getInteger(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V1_V2_PORT)));
            }

            if (item.containsKey(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_STATUS) && item.getString(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_STATUS).equalsIgnoreCase(GlobalConstants.YES))
            {
                v3MessageDispatcher = new MultiThreadedMessageDispatcher(ThreadPool.create("SNMP Trap v3 Dispatcher Pool",
                        MotadataConfigUtil.getTrapDispatchers()), new MessageDispatcherImpl());

                try (TransportMapping<UdpAddress> transport = new DefaultUdpTransportMapping(new UdpAddress(item.getInteger(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_PORT))))
                {
                    v3Listener = new Snmp(v3MessageDispatcher, transport);
                }

                v3Listener.getMessageDispatcher().addMessageProcessingModel(new MPv3());

                var securityUserName = item.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_USERNAME);

                OID authProtocol = null;

                OID privacyProtocol = null;

                if (item.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL).equalsIgnoreCase(SNMPTrapListenerConfiguration.AUTHENTICATION_NO_PRIVACY) || item.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL).equalsIgnoreCase(SNMPTrapListenerConfiguration.AUTHENTICATION_PRIVACY))
                {
                    authProtocol = switch (item.getString(SNMPTrapListenerConfiguration.SNMP_V3_AUTHENTICATION_PROTOCOL))
                    {
                        case SNMPTrapListenerConfiguration.MD5 -> AuthMD5.ID;

                        case SNMPTrapListenerConfiguration.SHA -> AuthSHA.ID;

                        case SNMPTrapListenerConfiguration.SHA224 -> AuthHMAC128SHA224.ID;

                        case SNMPTrapListenerConfiguration.SHA256 -> AuthHMAC192SHA256.ID;

                        case SNMPTrapListenerConfiguration.SHA384 -> AuthHMAC256SHA384.ID;

                        case SNMPTrapListenerConfiguration.SHA512 -> AuthHMAC384SHA512.ID;

                        default -> null;
                    };
                }

                if (item.getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL).equalsIgnoreCase(SNMPTrapListenerConfiguration.AUTHENTICATION_PRIVACY))
                {
                    privacyProtocol = switch (item.getString(SNMPTrapListenerConfiguration.SNMP_V3_PRIVACY_PROTOCOL))
                    {
                        case SNMPTrapListenerConfiguration.DES -> PrivDES.ID;

                        case SNMPTrapListenerConfiguration.DES_3 -> Priv3DES.ID;

                        case SNMPTrapListenerConfiguration.AES_128 -> PrivAES128.ID;

                        case SNMPTrapListenerConfiguration.AES_192 -> PrivAES192.ID;

                        case SNMPTrapListenerConfiguration.AES_256 -> PrivAES256.ID;

                        case SNMPTrapListenerConfiguration.AES_256C -> PrivAES256C.ID;

                        case SNMPTrapListenerConfiguration.AES_256G -> PrivAES256G.ID;

                        case SNMPTrapListenerConfiguration.AES_192C -> PrivAES192C.ID;

                        case SNMPTrapListenerConfiguration.AES_192G -> PrivAES192G.ID;

                        default -> null;
                    };
                }

                v3Listener.getUSM().addUser(new OctetString(securityUserName), new UsmUser(new OctetString(securityUserName), authProtocol,
                        new OctetString(item.getString(SNMPTrapListenerConfiguration.SNMP_V3_AUTHENTICATION_PASSWORD) != null ? item.getString(SNMPTrapListenerConfiguration.SNMP_V3_AUTHENTICATION_PASSWORD) : EMPTY_VALUE),
                        privacyProtocol, new OctetString(item.getString(SNMPTrapListenerConfiguration.SNMP_V3_PRIVATE_PASSWORD) != null ? item.getString(SNMPTrapListenerConfiguration.SNMP_V3_PRIVATE_PASSWORD) : EMPTY_VALUE)));

                v3Listener.listen();

                v3Listener.addCommandResponder(SNMPTrapListener::send);

                LOGGER.info(String.format("snmp v3 trap listener started on port %s", item.getInteger(SNMPTrapListenerConfiguration.SNMP_TRAP_LISTENER_V3_PORT)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    //send trap event to trap engine for further processing
    private static <A extends Address> void send(CommandResponderEvent<A> commandResponderEvent)
    {
        try
        {
            var address = commandResponderEvent.getPeerAddress().toString().substring(0, commandResponderEvent.getPeerAddress().toString().indexOf('/'));

            var valid = true;

            if (commandResponderEvent.getMessageProcessingModel() == MessageProcessingModel.MPv1 || commandResponderEvent.getMessageProcessingModel() == MessageProcessingModel.MPv2c)
            {
                if (commandResponderEvent.getSecurityName() != null)
                {
                    var community = new String(commandResponderEvent.getSecurityName());

                    if (!items.get(DEFAULT_ID).getString(SNMPTrapListenerConfiguration.SNMP_COMMUNITY).equalsIgnoreCase(community))
                    {
                        valid = false;
                    }
                }
            }
            else if (commandResponderEvent.getMessageProcessingModel() == MessageProcessingModel.MPv3)
            {
                var securityLevel = switch (items.get(DEFAULT_ID).getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_LEVEL))
                {
                    case SNMPTrapListenerConfiguration.NO_AUTHENTICATION_NO_PRIVACY -> SecurityLevel.NOAUTH_NOPRIV;

                    case SNMPTrapListenerConfiguration.AUTHENTICATION_NO_PRIVACY -> SecurityLevel.AUTH_NOPRIV;

                    case SNMPTrapListenerConfiguration.AUTHENTICATION_PRIVACY -> SecurityLevel.AUTH_PRIV;

                    default -> 0;
                };

                if (commandResponderEvent.getSecurityLevel() == SecurityLevel.AUTH_NOPRIV || commandResponderEvent.getSecurityLevel() == SecurityLevel.AUTH_PRIV || commandResponderEvent.getSecurityLevel() == SecurityLevel.NOAUTH_NOPRIV)
                {
                    if (commandResponderEvent.getSecurityLevel() != securityLevel)
                    {
                        valid = false;
                    }
                    else if (commandResponderEvent.getSecurityLevel() == SecurityLevel.NOAUTH_NOPRIV && securityLevel == SecurityLevel.NOAUTH_NOPRIV && commandResponderEvent.getSecurityName() != null)
                    {
                        var community = new String(commandResponderEvent.getSecurityName());

                        if (!items.get(DEFAULT_ID).getString(SNMPTrapListenerConfiguration.SNMP_V3_SECURITY_USERNAME).equalsIgnoreCase(community))
                        {
                            valid = false;
                        }
                    }
                }
            }

            if (valid)
            {
                if (address.contains("/"))
                {
                    address = address.replace("/", EMPTY_VALUE);
                }

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_TRAP, new JsonObject().put(EventBusConstants.EVENT, CodecUtil.toBytes(commandResponderEvent.getPDU()))
                        .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(EVENT_SOURCE, address)
                        .put(NMSConstants.SNMP_TRAP_VERSION, commandResponderEvent.getMessageProcessingModel()));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void stop()
    {
        try
        {
            if (v1v2Listener != null)
            {
                cleanup(v1v2MessageDispatcher);

                v1v2MessageDispatcher = null;

                v1v2Listener.close();

                LOGGER.info("snmp v1/v2c trap listener stopped successfully...");
            }

            if (v3Listener != null)
            {
                cleanup(v3MessageDispatcher);

                v3MessageDispatcher = null;

                v3Listener.getUSM().removeAllUsers();

                v3Listener.close();

                LOGGER.info("snmp v3 trap listener stopped successfully...");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private static void cleanup(MessageDispatcher messageDispatcher)
    {
        if (messageDispatcher != null)
        {
            messageDispatcher.stop();

            messageDispatcher.getTransportMappings().forEach(transportMapping ->
            {
                try
                {
                    transportMapping.close();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
    }

    static class PrivAES192G extends PrivAESWith3DESKeyExtension
    {
        public static final OID ID = new OID("1.3.6.1.4.1.14832.1.3");

        public PrivAES192G()
        {
            super(24);
        }

        @Override
        public OID getDefaultID()
        {
            return (OID) ID.clone();
        }
    }

    static class PrivAES192C extends PrivAESWith3DESKeyExtension
    {
        public static final OID ID = new OID("1.3.6.1.4.1.9.12.6.1.1");

        public PrivAES192C()
        {
            super(24);
        }

        @Override
        public OID getDefaultID()
        {
            return (OID) ID.clone();
        }
    }

    static class PrivAES256C extends PrivAESWith3DESKeyExtension
    {
        public static final OID ID = new OID("1.3.6.1.4.1.9.12.6.1.2");

        public PrivAES256C()
        {
            super(32);
        }

        @Override
        public OID getDefaultID()
        {
            return (OID) ID.clone();
        }
    }

    static class PrivAES256G extends PrivAESWith3DESKeyExtension
    {
        public static final OID ID = new OID("1.3.6.1.4.1.14832.1.4");

        public PrivAES256G()
        {
            super(32);
        }

        @Override
        public OID getDefaultID()
        {
            return (OID) ID.clone();
        }
    }
}
