/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;

public class LogParserPluginConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(LogParserPluginConfigStore.class, GlobalConstants.MOTADATA_STORE, "Log Parser Plugin Config Store");

    private static final LogParserPluginConfigStore STORE = new LogParserPluginConfigStore();

    private LogParserPluginConfigStore()
    {

        super(DBConstants.TBL_LOG_PARSER_PLUGIN, LOGGER, true);
    }

    public static LogParserPluginConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result -> Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_LOG_PARSER_PLUGIN.name()).put(GlobalConstants.ID, id)));
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.addItem(id).onComplete(result -> Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_LOG_PARSER_PLUGIN.name()).put(GlobalConstants.ID, id)));
    }

    @Override
    public void deleteItem(long id)
    {
        super.deleteItem(id);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_LOG_PARSER_PLUGIN.name()).put(GlobalConstants.ID, id));
    }
}
