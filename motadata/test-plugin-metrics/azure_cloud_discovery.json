{"127.0.0.1": {"_type": "1", "credential.profile.name": "Azure-Cloud-Test1655203790900", "discovery.category": "Cloud", "discovery.credential.profiles": [{"_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.service.down.instance.discovery": "", "cloud.tenant.id": "5b4acec3-**************-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "id": ***************, "objects": [{"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "FunctionApp120210317184034(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "FunctionApp120210317184034(motadata-freetier)", "object.type": "Azure Function", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)", "object.type": "Azure WebApp", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "testmotadataweb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "testmotadataweb(motadata-freetier)", "object.type": "Azure WebApp", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "cdntest(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "cdntest(cloud-shell-storage-centralindia)", "object.type": "Azure CDN", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "aqasde(DefaultResourceGroup-EUS2)", "object.resource.group": "DefaultResourceGroup-EUS2", "object.target": "aqasde(DefaultResourceGroup-EUS2)", "object.type": "Azure SQL Database", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "master(DefaultResourceGroup-EUS2)", "object.resource.group": "DefaultResourceGroup-EUS2", "object.target": "master(DefaultResourceGroup-EUS2)", "object.type": "Azure SQL Database", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "lb(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "lb(cloud-shell-storage-centralindia)", "object.type": "Azure Load Balancer"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatawin(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "motadatawin(cloud-shell-storage-centralindia)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "csg10032000a5192286(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "csg10032000a5192286(cloud-shell-storage-centralindia)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatafreetierdiag(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatafreetierdiag(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatblobstorage(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatblobstorage(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatafreetierdiag678(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatafreetierdiag678(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "blobstorageaccountesting(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "blobstorageaccountesting(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "storageaccountmotadaf95(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "storageaccountmotadaf95(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "hd4tj0siterecovasrcache(Site-recovery-vault-RG)", "object.resource.group": "Site-recovery-vault-RG", "object.target": "hd4tj0siterecovasrcache(Site-recovery-vault-RG)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "mrlrkmsiterecovasrcache(Site-recovery-vault-RG-1)", "object.resource.group": "Site-recovery-vault-RG-1", "object.target": "mrlrkmsiterecovasrcache(Site-recovery-vault-RG-1)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "x5bmy7siterecovasrcache(Site-recovery-vault-RG-1)", "object.resource.group": "Site-recovery-vault-RG-1", "object.target": "x5bmy7siterecovasrcache(Site-recovery-vault-RG-1)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "saleset(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "saleset(cloud-shell-storage-centralindia)", "object.type": "Azure VM Scale Set"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "appgateway123(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "appgateway123(cloud-shell-storage-centralindia)", "object.type": "Azure Application Gateway"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "**************", "object.name": "win-motadataVM(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "win-motadataVM(cloud-shell-storage-centralindia)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "************", "object.name": "ubuntu-linux2(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "ubuntu-linux2(motadata-freetier)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "*************", "object.name": "itsmtestinstance(DefaultResourceGroup-EUS)", "object.resource.group": "DefaultResourceGroup-EUS", "object.target": "itsmtestinstance(DefaultResourceGroup-EUS)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatacosmosdb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatacosmosdb(motadata-freetier)", "object.type": "Azure Cosmos DB"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "servbsq12(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "servbsq12(cloud-shell-storage-centralindia)", "object.type": "Azure Service Bus", "status": "Up"}], "plugin.id": "13", "status": "succeed"}], "discovery.discovered.objects": 0, "discovery.event.processors": [176993700471165], "discovery.groups": [1000**********], "discovery.name": "Azure-Cloud-Test-*************", "discovery.progress": 50, "discovery.status": "Not Run Yet", "discovery.total.objects": 0, "event.id": ***************, "event.topic": "remote.event.processor ", "event.type": "discovery", "id": ***************, "message": "Internet connection succeeded", "metric.plugin": "azure", "object.credential.profile": ***************, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.type": "Azure Cloud", "objects": [{"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "FunctionApp120210317184034(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "FunctionApp120210317184034(motadata-freetier)", "object.type": "Azure Function", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "azuresamplewebapp20200323124332(cloud-shell-storage-centralindia)", "object.type": "Azure WebApp", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "testmotadataweb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "testmotadataweb(motadata-freetier)", "object.type": "Azure WebApp", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "cdntest(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "cdntest(cloud-shell-storage-centralindia)", "object.type": "Azure CDN", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "aqasde(DefaultResourceGroup-EUS2)", "object.resource.group": "DefaultResourceGroup-EUS2", "object.target": "aqasde(DefaultResourceGroup-EUS2)", "object.type": "Azure SQL Database", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "master(DefaultResourceGroup-EUS2)", "object.resource.group": "DefaultResourceGroup-EUS2", "object.target": "master(DefaultResourceGroup-EUS2)", "object.type": "Azure SQL Database", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "lb(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "lb(cloud-shell-storage-centralindia)", "object.type": "Azure Load Balancer"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatawin(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "motadatawin(cloud-shell-storage-centralindia)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "csg10032000a5192286(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "csg10032000a5192286(cloud-shell-storage-centralindia)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatafreetierdiag(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatafreetierdiag(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatblobstorage(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatblobstorage(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatafreetierdiag678(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatafreetierdiag678(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "blobstorageaccountesting(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "blobstorageaccountesting(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "storageaccountmotadaf95(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "storageaccountmotadaf95(motadata-freetier)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "hd4tj0siterecovasrcache(Site-recovery-vault-RG)", "object.resource.group": "Site-recovery-vault-RG", "object.target": "hd4tj0siterecovasrcache(Site-recovery-vault-RG)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "mrlrkmsiterecovasrcache(Site-recovery-vault-RG-1)", "object.resource.group": "Site-recovery-vault-RG-1", "object.target": "mrlrkmsiterecovasrcache(Site-recovery-vault-RG-1)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "x5bmy7siterecovasrcache(Site-recovery-vault-RG-1)", "object.resource.group": "Site-recovery-vault-RG-1", "object.target": "x5bmy7siterecovasrcache(Site-recovery-vault-RG-1)", "object.type": "Azure Storage"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "saleset(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "saleset(cloud-shell-storage-centralindia)", "object.type": "Azure VM Scale Set"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "appgateway123(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "appgateway123(cloud-shell-storage-centralindia)", "object.type": "Azure Application Gateway"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "**************", "object.name": "win-motadataVM(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "win-motadataVM(cloud-shell-storage-centralindia)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "************", "object.name": "ubuntu-linux2(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "ubuntu-linux2(motadata-freetier)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.ip": "*************", "object.name": "itsmtestinstance(DefaultResourceGroup-EUS)", "object.resource.group": "DefaultResourceGroup-EUS", "object.target": "itsmtestinstance(DefaultResourceGroup-EUS)", "object.type": "Azure VM", "status": "Up"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "motadatacosmosdb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.target": "motadatacosmosdb(motadata-freetier)", "object.type": "Azure Cosmos DB"}, {"object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.context": {"13": {"cloud.active.services": {"Azure Application Gateway": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure CDN": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Cosmos DB": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier"], "Azure Function": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"], "Azure Load Balancer": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure SQL Database": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS2"], "Azure Service Bus": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure Storage": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_Site-recovery-vault-RG-1"], "Azure VM": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_motadata-freetier", "5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_DefaultResourceGroup-EUS"], "Azure VM Scale Set": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1_|@#|_cloud-shell-storage-centralindia"], "Azure WebApp": ["5807cfb0-41a6-4da6-b920-71d934d4a2af_|@#|_Azure subscription 1"]}}}, "object.name": "servbsq12(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.target": "servbsq12(cloud-shell-storage-centralindia)", "object.type": "Azure Service Bus", "status": "Up"}], "plugin.engine": "go", "plugin.id": 13, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "status": "succeed", "timeout": 120}}