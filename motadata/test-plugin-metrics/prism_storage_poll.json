{"172.16.10.212": {"result": {"prism.storage.capacity.bytes": 1530291496340, "prism.storage.container": [{"prism.storage.container": "default-container-61434032387555", "prism.storage.container.capacity.bytes": 511489405404, "prism.storage.container.cluster": "Motadata-Cluster", "prism.storage.container.compression": "false", "prism.storage.container.erasure.encoding": "off", "prism.storage.container.free.bytes": 509244076508, "prism.storage.container.free.percent": 99.5610214263917, "prism.storage.container.io.latency.ms": 0, "prism.storage.container.iops": -1, "prism.storage.container.on.disk.deduplication": "OFF", "prism.storage.container.read.iops": -1, "prism.storage.container.replication.factor": 1, "prism.storage.container.used.bytes": 2245328896, "prism.storage.container.used.percent": 0.4389785736082894, "prism.storage.container.write.iops": -1}, {"prism.storage.container": "NutanixManagementShare", "prism.storage.container.capacity.bytes": 509558014428, "prism.storage.container.cluster": "Motadata-Cluster", "prism.storage.container.compression": "true", "prism.storage.container.erasure.encoding": "off", "prism.storage.container.free.bytes": 509244076508, "prism.storage.container.free.percent": 99.93839015163908, "prism.storage.container.io.latency.ms": 0, "prism.storage.container.iops": -1, "prism.storage.container.on.disk.deduplication": "OFF", "prism.storage.container.read.iops": -1, "prism.storage.container.replication.factor": 1, "prism.storage.container.used.bytes": 313937920, "prism.storage.container.used.percent": 0.06160984836091889, "prism.storage.container.write.iops": -1}, {"prism.storage.container": "SelfServiceContainer", "prism.storage.container.capacity.bytes": 509244076508, "prism.storage.container.cluster": "Motadata-Cluster", "prism.storage.container.compression": "false", "prism.storage.container.erasure.encoding": "off", "prism.storage.container.free.bytes": 509244076508, "prism.storage.container.free.percent": 100, "prism.storage.container.io.latency.ms": 0, "prism.storage.container.iops": -1, "prism.storage.container.on.disk.deduplication": "OFF", "prism.storage.container.read.iops": -1, "prism.storage.container.replication.factor": 1, "prism.storage.container.used.bytes": 0, "prism.storage.container.used.percent": 0, "prism.storage.container.write.iops": -1}], "prism.storage.containers": 3, "prism.storage.free.bytes": 1527732229524, "prism.storage.pool": [{"prism.storage.pool": "default-storage-pool-61434032387555", "prism.storage.pool.capacity.bytes": 511803343324, "prism.storage.pool.cluster": "Motadata-Cluster", "prism.storage.pool.disks": 2}], "prism.storage.used.bytes": 2559266816, "prism.volume.group": [{"prism.volume.group": "Motadata-Volume", "prism.volume.group.capacity.bytes": 10737418240, "prism.volume.group.disks": 1}]}, "errors": []}}