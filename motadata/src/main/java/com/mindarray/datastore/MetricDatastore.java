/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  6-Feb-2025		<PERSON><PERSON>wari		MOTADATA-4878: updated event engine related configurations
 *
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_CATEGORY;
import static com.mindarray.eventbus.EventBusConstants.*;

public class MetricDatastore extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(MetricDatastore.class, MOTADATA_DATASTORE, "Metric Datastore");
    private static final boolean STANDALONE_OR_PRIMARY = Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.STANDALONE.name()) || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name());
    private final AtomicLong duration = new AtomicLong();
    private DatastoreEngine eventEngine;
    private boolean closed = false;
    private String uuid;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            vertx.eventBus().<JsonObject>localConsumer(EVENT_DATASTORE_PING, message ->
            {
                var event = message.body();

                if (uuid.equalsIgnoreCase(event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updating status for  %s : %s , event type : %s", event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID), event.getString(STATUS), config().getString(EVENT_TYPE)));
                    }

                    if (event.getString(STATUS).equalsIgnoreCase(STATUS_UP))
                    {
                        // if database gets up after being down more than 30 minutes then load all files
                        if (closed)
                        {
                            vertx.eventBus().send(config().getString(EventBusConstants.EVENT_TYPE) + ".load", uuid);

                            duration.set(0);

                            closed = false;

                            LOGGER.info(String.format("loading files for : %s ", config().getString(EVENT_TYPE)));
                        }

                        vertx.eventBus().send(config().getString(EventBusConstants.EVENT_TYPE) + ".active", uuid);

                        LOGGER.debug(String.format("active : type  %s ", config().getString(EVENT_TYPE)));
                    }
                    else
                    {
                        duration.getAndIncrement();
                    }

                    // 180 * 10 = 1800 sec(30 min) . if datastore is down for more than 30 minutes
                    // then close all event files object from in memory

                    if (duration.get() > MotadataConfigUtil.getDatastoreFileCloseTimerSeconds())
                    {
                        vertx.eventBus().send(config().getString(EventBusConstants.EVENT_TYPE) + ".close", uuid);

                        duration.set(0);

                        closed = true;

                        LOGGER.info(String.format("closing files for : %s ", config().getString(EVENT_TYPE)));
                    }
                }
            });

            vertx.eventBus().<Void>localConsumer(EVENT_DATASTORE_RETENTION_TRIGGER, message ->
                    DatastoreConstants.runRetention(config().getString(EventBusConstants.EVENT_TYPE), DateTimeUtil.currentMilliSeconds() - MotadataConfigUtil.getDatastoreRetentionDays() * 86400000L, LOGGER));

            eventEngine = new DatastoreEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                    .setPersistEventOffset(true).setEventQueueSize(MotadataConfigUtil.getMetricDatastoreEngineQueueSize()).setStatus(false).setLogger(LOGGER).setEventHandler(this::send).start(vertx, promise);

            uuid = config().getString(EventBusConstants.EVENT_TYPE).split(DOT_SEPARATOR_ESCAPE)[3];

            vertx.eventBus().<Void>localConsumer(config().getString(EventBusConstants.EVENT_TYPE) + ".inactive", message ->
            {
                LOGGER.debug(String.format("inactivate : type  %s ", config().getString(EVENT_TYPE)));

                eventEngine.setStatus(false);
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(JsonObject event)
    {
        if (STANDALONE_OR_PRIMARY)
        {
            vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE + DOT_SEPARATOR + DatastoreConstants.DatastoreCategory.METRIC.getName(), event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, uuid).put(EVENT_TOPIC, DATASTORE_BROKER_OPERATION_TOPIC));

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("event sent to datastore %s , timestamp : %s", uuid, event.getLong(EVENT_TIMESTAMP)));
            }
        }
        else
        {
            vertx.eventBus().send(EVENT_REMOTE, event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, uuid).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode()).put(DATASTORE_CATEGORY, DatastoreConstants.DatastoreCategory.METRIC.getName()).put(EVENT_TYPE, EVENT_HA_DATASTORE_SECONDARY_SYNC));

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("datastore event sent to primary app with timestamp : %s ", event.getLong(EVENT_TIMESTAMP)));
            }
        }

        if (MotadataConfigUtil.devMode())
        {
            vertx.eventBus().send("test.datastore.metric", event);
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            eventEngine.stop(vertx, promise);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
