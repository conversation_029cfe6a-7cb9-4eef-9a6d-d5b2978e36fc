/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  16-Apr-2025		Bharat		    MOTADATA-5798: Enhance compliance in the plugin engine by implementing batching for CLI sessions.
 *  22-Apr-2025		sankalp		    MOTADATA-5810 : Added runbook case to pass context timeout to spawn worker.
 *  20-May-2025     Aagam           MOTADATA-5847 : Added windows build support
 */

package com.mindarray.plugin;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.EventCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.PROCESS_TIMED_OUT;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_PLUGIN_ENGINE;

/**
 * The PluginEngine class is a core component of the Motadata system responsible for managing and executing
 * plugin-related events. It extends AbstractVerticle from the Vert.x framework to leverage its event-driven
 * architecture.
 * <p>
 * This engine handles various types of plugin requests (such as PING, STREAMING, COMPLIANCE, RUNBOOK) by:
 * 1. Receiving events from the event bus
 * 2. Queuing events based on their type
 * 3. Processing events using worker executors
 * 4. Managing worker allocation for different request types
 * 5. Handling batch processing for supported plugin types
 * 6. Sending responses back through the event bus
 * <p>
 * The engine implements sophisticated event scheduling and worker management to ensure efficient
 * resource utilization while processing potentially high volumes of plugin requests.
 */
public class PluginEngine extends AbstractVerticle
{
    /**
     * Logger instance for the PluginEngine class
     */
    private static final Logger LOGGER = new Logger(PluginEngine.class, MOTADATA_PLUGIN_ENGINE, "Plugin Engine");

    /**
     * Map to store all events by their event ID.
     * Uses LinkedHashMap to maintain insertion order which helps in processing events in FIFO order.
     */
    private final Map<Long, JsonObject> events = new LinkedHashMap<>();

    /**
     * Map to organize events by their request type for non-batch processing.
     * Each request type has a list of event IDs that need to be processed.
     */
    private final Map<PluginEngineConstants.PluginEngineRequest, List<Long>> eventsByPluginEngineRequest = new EnumMap<>(PluginEngineConstants.PluginEngineRequest.class);

    /**
     * Map to organize events by their request type for batch processing.
     * Similar to eventsByPluginEngineRequest but specifically for requests that support batching.
     */
    private final Map<PluginEngineConstants.PluginEngineRequest, List<Long>> batchEventsByPluginEngineRequest = new EnumMap<>(PluginEngineConstants.PluginEngineRequest.class);

    /**
     * Worker executor for processing plugin events asynchronously.
     * Creates a shared worker pool with a configured number of workers and a 60-minute timeout.
     */
    private final WorkerExecutor workerExecutor = Bootstrap.vertx().createSharedWorkerExecutor("Plugin Engine", MotadataConfigUtil.getPluginEngineWorkers(), 60L, TimeUnit.MINUTES);

    /**
     * Counter for tracking the number of available workers.
     * Initialized with the total number of workers configured for the plugin engine.
     */
    private final AtomicInteger idleWorkers = new AtomicInteger(MotadataConfigUtil.getPluginEngineWorkers());

    /**
     * Map to track available workers for each request type.
     * Workers are allocated to different request types based on configuration.
     */
    private final Map<PluginEngineConstants.PluginEngineRequest, Integer> idleWorkersByPluginEngineRequest = new EnumMap<>(PluginEngineConstants.PluginEngineRequest.class);

    /**
     * Counter for tracking the number of consecutive event probes that found no events to process.
     * Used to determine when to disable the timer to save CPU cycles.
     */
    private int eventProbes;

    /**
     * Flag indicating whether the periodic timer for processing events is active.
     * Helps prevent creating multiple timers.
     */
    private boolean timeHandlerActive = false;

    /**
     * Initializes the PluginEngine verticle.
     * This method sets up event handlers, initializes data structures, and starts listening for events.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        for (var pluginEngineRequest : PluginEngineConstants.PluginEngineRequest.values())
        {
            eventsByPluginEngineRequest.put(pluginEngineRequest, new ArrayList<>());

            batchEventsByPluginEngineRequest.put(pluginEngineRequest, new ArrayList<>());

            idleWorkersByPluginEngineRequest.put(pluginEngineRequest, PluginEngineConstants.getPluginEngineWorkersByRequest(pluginEngineRequest));
        }

        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
        {
            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_PLUGIN_ENGINE)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, events.size())
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
            else
            {
                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_PLUGIN_ENGINE)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, events.size())
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EVENT_PLUGIN_ENGINE, message ->
        {
            if (message.body() != null)
            {
                var event = message.body();

                if (event.containsKey(EventBusConstants.EVENT_ID) && event.containsKey(PluginEngineConstants.PLUGIN_ENGINE_REQUEST))
                {
                    var pluginEngineRequest = PluginEngineConstants.PluginEngineRequest.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST));

                    if (pluginEngineRequest != PluginEngineConstants.PluginEngineRequest.PING && pluginEngineRequest != PluginEngineConstants.PluginEngineRequest.PING_PROBE
                            && !PluginEngineConstants.hasBatchSupport(event))
                    {
                        eventsByPluginEngineRequest.get(pluginEngineRequest).add(event.getLong(EventBusConstants.EVENT_ID));
                    }
                    else
                    {
                        batchEventsByPluginEngineRequest.get(pluginEngineRequest).add(event.getLong(EventBusConstants.EVENT_ID));
                    }

                    events.put(event.getLong(EventBusConstants.EVENT_ID), event);
                }
            }

            if (!timeHandlerActive)
            {
                eventProbes = 0;

                timeHandlerActive = true;

                vertx.setPeriodic(500, timer ->
                {
                    // Check if there are any events to process
                    if (!events.isEmpty())
                    {
                        // Reset the event probe counter since we found events
                        eventProbes = 0;

                        // Only process events if we have available workers
                        if (idleWorkers.get() > 0)
                        {
                            // List to store the request types for each batch of events
                            var pluginEngineRequests = new ArrayList<PluginEngineConstants.PluginEngineRequest>();

                            // List to store the qualified request types (may differ from original request type)
                            var qualifiedPluginEngineRequests = new ArrayList<PluginEngineConstants.PluginEngineRequest>();  // for plugin engine request selection

                            // Maps to track pending events by request type
                            var pendingEventsByPluginEngineRequest = new EnumMap<PluginEngineConstants.PluginEngineRequest, Integer>(PluginEngineConstants.PluginEngineRequest.class);
                            var pendingBatchEventsByPluginEngineRequest = new EnumMap<PluginEngineConstants.PluginEngineRequest, Integer>(PluginEngineConstants.PluginEngineRequest.class);

                            // Counters for tracking pending events
                            var pendingEvents = 0;
                            var pendingBatchEvents = 0;

                            // List to store batches of event IDs for processing
                            var batchEvents = new ArrayList<List<Long>>();

                            // List to store the plugin engine type for each batch
                            var pluginEngines = new ArrayList<PluginEngineConstants.PluginEngine>();  // for plugin engine selection (python/go)

                            // Process each type of plugin engine request
                            for (var pluginEngineRequest : PluginEngineConstants.PluginEngineRequest.values())
                            {
                                // First dequeue batch events (GO engine) for this request type
                                pendingBatchEvents += dequeue(pluginEngineRequest, pluginEngineRequests, pendingBatchEventsByPluginEngineRequest, batchEvents, qualifiedPluginEngineRequests, pluginEngines, PluginEngineConstants.PluginEngine.GO);

                                // Then dequeue non-batch events (PYTHON engine) for this request type
                                pendingEvents += dequeue(pluginEngineRequest, pluginEngineRequests, pendingEventsByPluginEngineRequest, batchEvents, qualifiedPluginEngineRequests, pluginEngines, PluginEngineConstants.PluginEngine.PYTHON);
                            }

                            // If we still have idle workers after the initial dequeue, process remaining events by priority
                            if (idleWorkers.get() > 0)
                            {
                                // Process remaining batch events first (GO engine)
                                if (pendingBatchEvents > 0)
                                {
                                    dequeue(pendingBatchEventsByPluginEngineRequest, pluginEngineRequests, batchEvents, pluginEngines, PluginEngineConstants.PluginEngine.GO, qualifiedPluginEngineRequests);
                                }

                                // Then process remaining non-batch events (PYTHON engine)
                                if (pendingEvents > 0)
                                {
                                    dequeue(pendingEventsByPluginEngineRequest, pluginEngineRequests, batchEvents, pluginEngines, PluginEngineConstants.PluginEngine.PYTHON, qualifiedPluginEngineRequests);
                                }
                            }

                            // Process each batch of events that was dequeued
                            for (var index = 0; index < batchEvents.size(); index++)
                            {
                                // Get the request type, qualified request type, and plugin engine for this batch
                                var request = pluginEngineRequests.get(index);
                                var qualifiedRequest = qualifiedPluginEngineRequests.get(index);
                                var pluginEngine = pluginEngines.get(index);

                                // Lists and maps to organize event data for processing
                                var eventIds = new ArrayList<Long>();
                                var contexts = new HashMap<Long, JsonObject>();
                                var targets = new JsonArray();
                                var eventIdsByTarget = new HashMap<String, List<Long>>();

                                // Check if this is a ping-related request (special handling required)
                                var pingProbe = qualifiedRequest == PluginEngineConstants.PluginEngineRequest.PING_PROBE || qualifiedRequest == PluginEngineConstants.PluginEngineRequest.PING;

                                // Process each event ID in the current batch
                                for (var eventId : batchEvents.get(index))
                                {
                                    // Remove the event from the queue as we're about to process it
                                    var context = events.remove(eventId);

                                    // Verify the event is still valid before processing
                                    if (context != null && EventCacheStore.getStore().validItem(eventId))
                                    {
                                        // Add to the list of events to process
                                        eventIds.add(eventId);

                                        // Update event tracking with qualification timestamp
                                        EventBusConstants.updateEvent(eventId, String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_QUALIFIED, DateTimeUtil.timestamp()));

                                        // Store the event context for processing
                                        contexts.put(eventId, context);

                                        // For ping requests, organize events by target IP for batch processing
                                        if (pingProbe)
                                        {
                                            targets.add(context.getString(AIOpsObject.OBJECT_IP));

                                            // Group events by target IP to handle responses efficiently
                                            eventIdsByTarget.computeIfAbsent(context.getString(AIOpsObject.OBJECT_IP), value -> new ArrayList<>()).add(eventId);
                                        }
                                    }
                                }

                                // Only process if we have valid contexts to work with
                                if (!contexts.isEmpty())
                                {
                                    // Execute the processing in a worker thread to avoid blocking the event loop
                                    workerExecutor.executeBlocking(future ->
                                    {
                                        // Mark the start of event processing for each event
                                        for (var eventId : eventIds)
                                        {
                                            EventBusConstants.startEvent(eventId, Thread.currentThread().getName());
                                        }

                                        // Get a representative context from the batch for common parameters
                                        var context = contexts.values().stream().findFirst().get();

                                        try
                                        {
                                            // Handle different types of requests with specific processing logic
                                            switch (qualifiedRequest)
                                            {
                                                case STREAMING ->
                                                {
                                                    // Streaming requests need a longer timeout (300 seconds)
                                                    WorkerUtil.spawnWorker(contexts, context, eventIds, 300, true, pluginEngine, System.currentTimeMillis(), true);

                                                    // For streaming, immediately send success response to complete event tracking
                                                    send(context.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)); // for streaming pass event to plugin engine response to complete event tracking from event router
                                                }

                                                // Handle ping requests with special ping probe functionality
                                                case PING_PROBE, PING ->
                                                        probePing(targets, qualifiedRequest).onComplete(result ->
                                                        {

                                                            try
                                                            {
                                                                if (result.succeeded())
                                                                {
                                                                    for (var count = 0; count < result.result().size(); count++)
                                                                    {
                                                                        var probe = result.result().getJsonObject(count);

                                                                        if (probe.containsKey(NMSConstants.PING_LATENCY))
                                                                        {
                                                                            probe.put(EventBusConstants.EVENT_LATENCY, CommonUtil.getFloat(probe.getValue(NMSConstants.PING_LATENCY)));
                                                                        }

                                                                        if (eventIdsByTarget.containsKey(probe.getString(AIOpsObject.OBJECT_TARGET)))
                                                                        {
                                                                            for (var eventId : eventIdsByTarget.get(probe.getString(AIOpsObject.OBJECT_TARGET)))
                                                                            {
                                                                                var event = contexts.remove(eventId);

                                                                                // request from runbook engine
                                                                                if (event.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_NAME))
                                                                                {
                                                                                    event.put(RESULT, probe);
                                                                                }

                                                                                if (probe.getString(STATUS).equalsIgnoreCase(STATUS_UP))
                                                                                {
                                                                                    send(event.put(STATUS, STATUS_SUCCEED).put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));
                                                                                }
                                                                                else
                                                                                {
                                                                                    send(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_PING_FAILED));
                                                                                }
                                                                            }

                                                                            eventIdsByTarget.remove(probe.getString(AIOpsObject.OBJECT_TARGET));
                                                                        }

                                                                        targets.remove(probe.getString(AIOpsObject.OBJECT_TARGET));

                                                                    }

                                                                    var errorCode = ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT;

                                                                    var status = STATUS_TIME_OUT;

                                                                    if (result.result().isEmpty())
                                                                    {
                                                                        status = STATUS_FAIL;

                                                                        errorCode = ErrorCodes.ERROR_CODE_PING_FAILED;
                                                                    }

                                                                    for (var target : targets)
                                                                    {
                                                                        if (eventIdsByTarget.containsKey(CommonUtil.getString(target)))
                                                                        {
                                                                            for (var id : eventIdsByTarget.get(CommonUtil.getString(target)))
                                                                            {
                                                                                send(contexts.remove(id).put(STATUS, status).put(ERROR_CODE, errorCode));
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                else
                                                                {
                                                                    var errorCode = result.cause() != null && result.cause().getMessage().contains(PROCESS_TIMED_OUT) ?
                                                                            ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT : ErrorCodes.ERROR_CODE_PING_FAILED;

                                                                    var status = errorCode.equalsIgnoreCase(ErrorCodes.ERROR_CODE_PROCESS_TIMEOUT) ? STATUS_TIME_OUT : STATUS_FAIL;

                                                                    for (var target : targets)
                                                                    {
                                                                        if (eventIdsByTarget.containsKey(CommonUtil.getString(target)))
                                                                        {
                                                                            for (var id : eventIdsByTarget.get(CommonUtil.getString(target)))
                                                                            {
                                                                                send(contexts.remove(id).put(STATUS, status).put(ERROR_CODE, errorCode));
                                                                            }
                                                                        }
                                                                    }
                                                                }

                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);
                                                            }
                                                        });

                                                // Compliance requests need a longer timeout (600 seconds by default)
                                                case COMPLIANCE -> WorkerUtil.spawnWorker(contexts, context, eventIds,
                                                        context.getInteger(TIMEOUT, 600), // Use 600 seconds as default timeout for compliance
                                                        false, pluginEngine, System.currentTimeMillis(), true);

                                                // Runbook requests use either the specified timeout or the configured batch timeout
                                                case RUNBOOK -> WorkerUtil.spawnWorker(contexts, context, eventIds,
                                                        // Use context timeout if provided, otherwise use the configured batch timeout
                                                        context.containsKey(TIMEOUT) ? context.getInteger(TIMEOUT, 60) : MotadataConfigUtil.getPluginEngineProbeBatchTimeoutSeconds(),
                                                        false, pluginEngine, System.currentTimeMillis(), true);

                                                // Default handling for other request types
                                                default -> WorkerUtil.spawnWorker(contexts, context, eventIds,
                                                        // GO engine uses batch timeout, PYTHON engine uses context timeout or 60 seconds
                                                        pluginEngine == PluginEngineConstants.PluginEngine.GO ? MotadataConfigUtil.getPluginEngineProbeBatchTimeoutSeconds() : context.getInteger(TIMEOUT, 60),
                                                        false, pluginEngine, System.currentTimeMillis(), true);
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);
                                        }
                                        finally
                                        {
                                            future.complete();
                                        }
                                    }, false, result -> complete(request));
                                }
                                else
                                {
                                    complete(request);
                                }
                            }
                        }
                    }
                    else
                    {
                        eventProbes++;
                    }

                    if (eventProbes > 10) //max 10 probes...
                    {
                        //disable timer to save cpu cycle...

                        vertx.cancelTimer(timer);

                        timeHandlerActive = false;
                    }
                });
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_PLUGIN_ENGINE_ABORT, message ->
        {
            var event = message.body();

            if (events.containsKey(event.getLong(EventBusConstants.EVENT_ID)))
            {
                eventsByPluginEngineRequest.get(PluginEngineConstants.PluginEngineRequest.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST))).remove(event.getLong(EventBusConstants.EVENT_ID));

                batchEventsByPluginEngineRequest.get(PluginEngineConstants.PluginEngineRequest.valueOfName(event.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST))).remove(event.getLong(EventBusConstants.EVENT_ID));

                EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                vertx.eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE,
                        events.remove(event.getLong(EventBusConstants.EVENT_ID)).put(STATUS, STATUS_ABORT)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_MANUAL_ABORTED).put(MESSAGE, "User aborted the event"));
            }
        });

        promise.complete();
    }

    /**
     * Performs ping probes on the specified targets.
     * This method uses PingUtil to execute ping operations and returns the results as a Future.
     *
     * @param targets          JsonArray containing the IP addresses to ping
     * @param qualifiedRequest The type of ping request (PING or PING_PROBE)
     * @return Future containing the ping results as a JsonArray
     */
    private Future<JsonArray> probePing(JsonArray targets, PluginEngineConstants.PluginEngineRequest qualifiedRequest)
    {
        var promise = Promise.<JsonArray>promise();

        try
        {
            promise.complete(PingUtil.ping(targets, NMSConstants.PING_CHECK_TIMEOUT_SECONDS, qualifiedRequest == PluginEngineConstants.PluginEngineRequest.PING_PROBE ? MotadataConfigUtil.getPluginEnginePingPackets() : 3, null, null));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Marks a worker as idle after completing a request.
     * Increments the idle worker count for both the global counter and the request-specific counter.
     *
     * @param pluginEngineRequest The type of request that was completed
     */
    private void complete(PluginEngineConstants.PluginEngineRequest pluginEngineRequest)
    {
        idleWorkers.getAndIncrement();

        idleWorkersByPluginEngineRequest.put(pluginEngineRequest, idleWorkersByPluginEngineRequest.get(pluginEngineRequest) + 1);
    }

    /**
     * Sends an event response through the event bus.
     * Updates the event tracker with the response sent timestamp and routes the event
     * to the appropriate destination based on the bootstrap type and installation mode.
     *
     * @param event The event response to send
     */
    private void send(JsonObject event)
    {
        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        if (Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()))
        {
            vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, event.put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC));
        }
        else
        {
            vertx.eventBus().send(EventBusConstants.EVENT_PLUGIN_ENGINE_RESPONSE, event);
        }
    }

    /**
     * Dequeues events for a specific plugin engine request type.
     * This method checks if there are idle workers available for the request type and
     * dequeues events accordingly, preparing them for processing.
     *
     * @param pluginEngineRequest                The request type to dequeue events for
     * @param pluginEngineRequests               List to collect the request types for dequeued events
     * @param pendingEventsByPluginEngineRequest Map to track pending events by request type
     * @param batches                            List to collect batches of event IDs for processing
     * @param qualifiedPluginEngineRequests      List to collect qualified request types
     * @param pluginEngines                      List to collect plugin engine types for processing
     * @param pluginEngine                       The plugin engine type (PYTHON, GO, etc.)
     * @return The number of pending events after dequeueing
     */
    private int dequeue(PluginEngineConstants.PluginEngineRequest pluginEngineRequest, List<PluginEngineConstants.PluginEngineRequest> pluginEngineRequests,
                        Map<PluginEngineConstants.PluginEngineRequest, Integer> pendingEventsByPluginEngineRequest, List<List<Long>> batches, List<PluginEngineConstants.PluginEngineRequest> qualifiedPluginEngineRequests,
                        List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var pendingEvents = 0;

        try
        {
            var events = idleWorkersByPluginEngineRequest.get(pluginEngineRequest) > 0 && idleWorkers.get() > 0 ? pluginEngine == PluginEngineConstants.PluginEngine.GO ? this.batchEventsByPluginEngineRequest.get(pluginEngineRequest) : this.eventsByPluginEngineRequest.get(pluginEngineRequest) : null;

            if (events != null && !events.isEmpty())
            {
                pendingEventsByPluginEngineRequest.put(pluginEngineRequest,
                        dequeue(events, idleWorkersByPluginEngineRequest.get(pluginEngineRequest), pluginEngineRequest, batches, pluginEngineRequests, pluginEngineRequest, qualifiedPluginEngineRequests, pluginEngines, pluginEngine));

                pendingEvents += pendingEventsByPluginEngineRequest.get(pluginEngineRequest);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return pendingEvents;
    }

    /**
     * Dequeues events from a list and organizes them into batches for processing.
     * This method determines the appropriate batch size based on the request type and plugin engine,
     * creates batches of events, and updates worker allocation.
     *
     * @param events            List of event IDs to dequeue
     * @param pendingEvents     Number of pending events for the request type
     * @param request           The original request type
     * @param batches           List to collect batches of event IDs
     * @param requests          List to collect request types for each batch
     * @param qualifiedRequest  The qualified request type (may differ from original request)
     * @param qualifiedRequests List to collect qualified request types for each batch
     * @param pluginEngines     List to collect plugin engine types for each batch
     * @param pluginEngine      The plugin engine type to use
     * @return The number of remaining events after dequeueing
     */
    private int dequeue(List<Long> events, int pendingEvents, PluginEngineConstants.PluginEngineRequest request, List<List<Long>> batches, List<PluginEngineConstants.PluginEngineRequest> requests,
                        PluginEngineConstants.PluginEngineRequest qualifiedRequest, List<PluginEngineConstants.PluginEngineRequest> qualifiedRequests,
                        List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var iterator = events.listIterator();

        var batchSize = pluginEngine == PluginEngineConstants.PluginEngine.GO || qualifiedRequest == PluginEngineConstants.PluginEngineRequest.PING ? MotadataConfigUtil.getPluginEngineProbeBatchSize() : 1;

        if (qualifiedRequest == PluginEngineConstants.PluginEngineRequest.STREAMING)
        {
            batchSize = 1;
        }

        else if (qualifiedRequest == PluginEngineConstants.PluginEngineRequest.PING_PROBE)
        {
            batchSize = MotadataConfigUtil.getPluginEnginePingBatchSize();
        }

        var workers = WorkerUtil.getWorkers(events.size(), batchSize, pendingEvents);

        var allocations = 0;

        while (allocations < workers && iterator.hasNext())
        {
            var batchEvents = new ArrayList<Long>();

            batches.add(batchEvents);

            requests.add(request);

            qualifiedRequests.add(qualifiedRequest); // for plugin engine request selection

            pluginEngines.add(pluginEngine);

            for (var index = 0; index < batchSize && iterator.hasNext(); index++)
            {
                batchEvents.add(iterator.next());

                iterator.remove();
            }

            allocations++;
        }

        idleWorkers.set(idleWorkers.get() - workers);

        idleWorkersByPluginEngineRequest.put(request, pendingEvents - workers);

        return events.size();
    }

    /**
     * Dequeues events based on pending events and available workers.
     * This method is used in the prioritized dequeueing process where events are dequeued
     * based on the number of pending events and available workers.
     *
     * @param requests                           List to collect request types for dequeued events
     * @param pendingEventsByPluginEngineRequest Map to track pending events by request type
     * @param entry1                             Map entry containing the request type with pending events
     * @param entry2                             Map entry containing the request type with available workers
     * @param batches                            List to collect batches of event IDs
     * @param qualifiedRequests                  List to collect qualified request types
     * @param pluginEngines                      List to collect plugin engine types
     * @param pluginEngine                       The plugin engine type to use
     */
    private void dequeue(List<PluginEngineConstants.PluginEngineRequest> requests, Map<PluginEngineConstants.PluginEngineRequest, Integer> pendingEventsByPluginEngineRequest,
                         Map.Entry<PluginEngineConstants.PluginEngineRequest, Integer> entry1, Map.Entry<PluginEngineConstants.PluginEngineRequest, Integer> entry2, List<List<Long>> batches,
                         List<PluginEngineConstants.PluginEngineRequest> qualifiedRequests, List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine)
    {
        try
        {
            var pendingEvents = pluginEngine == PluginEngineConstants.PluginEngine.GO ? this.batchEventsByPluginEngineRequest.get(entry1.getKey()) : this.eventsByPluginEngineRequest.get(entry1.getKey());

            if (pendingEvents != null && !pendingEvents.isEmpty())
            {
                pendingEventsByPluginEngineRequest.put(entry1.getKey(), dequeue(pendingEvents, entry2.getValue(), entry2.getKey(), batches, requests, entry1.getKey(), qualifiedRequests, pluginEngines, pluginEngine));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Prioritizes and dequeues events based on the number of pending events and available workers.
     * This method sorts request types by the number of pending events and available workers,
     * then dequeues events in priority order until no more workers are available.
     *
     * @param pendingEventsByPluginEngineRequest Map of pending events by request type
     * @param pluginEngineRequests               List to collect request types for dequeued events
     * @param batchEvents                        List to collect batches of event IDs
     * @param pluginEngines                      List to collect plugin engine types
     * @param pluginEngine                       The plugin engine type to use
     * @param qualifiedPluginEngineRequests      List to collect qualified request types
     */
    private void dequeue(Map<PluginEngineConstants.PluginEngineRequest, Integer> pendingEventsByPluginEngineRequest, List<PluginEngineConstants.PluginEngineRequest> pluginEngineRequests, List<List<Long>> batchEvents,
                         List<PluginEngineConstants.PluginEngine> pluginEngines, PluginEngineConstants.PluginEngine pluginEngine, List<PluginEngineConstants.PluginEngineRequest> qualifiedPluginEngineRequests)
    {
        var sortedByWorkers = idleWorkersByPluginEngineRequest.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<PluginEngineConstants.PluginEngineRequest, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        pendingEventsByPluginEngineRequest.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<PluginEngineConstants.PluginEngineRequest, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet()
                .stream()
                .takeWhile(entry1 -> idleWorkers.get() > 0)
                .forEach(entry1 -> sortedByWorkers.entrySet()
                        .stream()
                        .takeWhile(entry2 -> idleWorkers.get() > 0)
                        .forEach(entry2 -> dequeue(pluginEngineRequests, pendingEventsByPluginEngineRequest, entry1, entry2, batchEvents, qualifiedPluginEngineRequests, pluginEngines, pluginEngine)));
    }

    /**
     * Stops the PluginEngine verticle.
     * Closes the worker executor and completes the promise.
     *
     * @param promise Promise to be completed when the verticle is stopped
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        workerExecutor.close();

        promise.complete();
    }
}
