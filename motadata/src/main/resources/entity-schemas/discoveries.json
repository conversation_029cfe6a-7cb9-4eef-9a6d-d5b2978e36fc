{"entity": "Discovery", "table": "tbl_config_discovery", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "discovery.name", "title": "Discovery Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "discovery.event.processors", "title": "<PERSON><PERSON> Poller(s)", "type": "list"}, {"name": "discovery.category", "title": "Discovery Category", "type": "string", "rules": ["required"], "values": ["Server", "Cloud", "Network", "Virtualization", "Service Check", "HCI", "SDN", "Storage", "Container Orchestration", "Other"]}, {"name": "discovery.email.recipients", "title": "Discovery Email Recipient(s)", "type": "list"}, {"name": "discovery.sms.recipients", "title": "Discovery SMS Recipient(s)", "type": "list"}]}