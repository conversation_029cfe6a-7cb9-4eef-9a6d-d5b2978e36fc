/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.CommonUtil.isNotNullOrEmpty;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;

public class RemoteEventProcessor extends AbstractAPI
{
    public static final String REMOTE_EVENT_PROCESSOR_UUID = "remote.event.processor.uuid";
    public static final String REMOTE_EVENT_PROCESSOR_HOST = "remote.event.processor.host";
    public static final String REMOTE_EVENT_PROCESSOR_IP = "remote.event.processor.ip";

    public static final String REMOTE_EVENT_PROCESSOR_TYPE = "remote.event.processor.type";
    public static final String REMOTE_EVENT_PROCESSOR_VERSION = "remote.event.processor.version";

    public static final String REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE = "remote.event.processor.installation.mode";

    private static final Logger LOGGER = new Logger(RemoteEventProcessor.class, MOTADATA_API, "Remote Event Processor API");

    public RemoteEventProcessor()
    {
        super("remote-event-processors", RemoteEventProcessorConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint).handler(this::getAll);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.post("/" + endpoint + "/:id/start").handler(this::validateAction).handler(this::start);

            router.post("/" + endpoint + "/:id/stop").handler(this::validateAction).handler(this::stop);

            router.post("/" + endpoint + "/:id/restart").handler(this::validateAction).handler(this::restart);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var requestBody = routingContext.body().asJsonObject();

        requestBody.remove(REMOTE_EVENT_PROCESSOR_UUID);

        promise.complete(requestBody);

        return promise.future();
    }

    @Override
    protected Future<Void> afterGet(JsonObject entity, RoutingContext routingContext)
    {
        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, enrich(entity)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        try
        {
            var items = new JsonArray();

            for (var index = 0; index < entities.size(); index++)
            {
                items.add(enrich(entities.getJsonObject(index)));
            }

            this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, SC_OK).put(RESULT, items));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    //enrich remote event processor status from cache store
    private JsonObject enrich(JsonObject entity)
    {
        var item = new JsonObject().mergeIn(entity).mergeIn(RemoteEventProcessorCacheStore.getStore().getItem(entity.getLong(ID)));

        if (item.containsKey(DURATION))
        {
            item.put(DURATION, DateTimeUtil.convertTime(item.getLong(DURATION)));
        }

        item.put(DISABLED, RemoteEventProcessorCacheStore.getStore().getStateDuration(entity.getLong(ID)) > 0 ? YES : NO);


        return item;
    }

    @Override
    protected Future<JsonObject> beforeDelete(RoutingContext routingContext)
    {
        var item = this.configStore.getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

        routingContext.setBody(Buffer.buffer(new JsonObject().put(ID, item.getLong(ID)).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)).toString()));

        return super.beforeDelete(routingContext);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        var remoteEventProcessorType = RemoteEventProcessorConfigStore.getStore().getItem(entity.getLong(ID)) != null ? RemoteEventProcessorConfigStore.getStore().getItem(entity.getLong(ID)).getString(REMOTE_EVENT_PROCESSOR_TYPE) : BootstrapType.COLLECTOR;

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, routingContext.body().asJsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_ARTIFACT.name())
                .put(ID, entity.getLong(ID))
                .put(MotadataApp.ARTIFACT_TYPE, routingContext.body().asJsonObject().getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) ? BootstrapType.APP.name() : BootstrapType.COLLECTOR.name()));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_REMOTE_POLLER.name())
                .put(ID, entity.getLong(ID)));

        RemoteEventProcessorCacheStore.getStore().deleteItem(entity.getLong(ID));

        publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EVENT_REMOTE_PROCESSOR_DELETE).put(ID, entity.getLong(ID))
                .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                .put(REMOTE_EVENT_PROCESSOR_UUID, routingContext.body().asJsonObject().getString(REMOTE_EVENT_PROCESSOR_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, remoteEventProcessorType));

        return super.afterDelete(entity, routingContext);
    }

    private void start(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var item = RemoteEventProcessorConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                LOGGER.debug(String.format("starting remote event processor %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_START).put(ID, id)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME)).put(DISABLED, YES)
                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID))
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(SYSTEM_BOOTSTRAP_TYPE, item.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.START_REQUESTED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName())));
            }
            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void stop(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var item = RemoteEventProcessorConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                LOGGER.debug(String.format("stopping remote event processor %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_STOP).put(ID, id)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(DISABLED, YES)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, item.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.STOP_REQUESTED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName())));
            }
            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void restart(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var item = RemoteEventProcessorConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                LOGGER.debug(String.format("restarting remote event processor %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_REMOTE_PROCESSOR_RESTART).put(ID, id)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(DISABLED, YES)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                        .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, item.getString(REMOTE_EVENT_PROCESSOR_TYPE)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.RESTART_REQUESTED, APIConstants.Entity.REMOTE_EVENT_PROCESSOR.getName())));
            }
            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void publishEvent(JsonObject event)
    {
        if (!event.isEmpty() && isNotNullOrEmpty(event.getString(EVENT_TYPE)) && isNotNullOrEmpty(event.getString(REMOTE_EVENT_PROCESSOR_UUID)))
        {
            event.put(EVENT_ID, CommonUtil.newEventId());

            if (CommonUtil.isNotNullOrEmpty(event.getString(DISABLED))) // prevent user to perform any action
            {
                RemoteEventProcessorCacheStore.getStore().updateDuration(event.getLong(ID), event.getString(DISABLED));

                EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EVENT_TYPE, EVENT_REMOTE_EVENT_PROCESSOR_ACTION).put(ID, event.getLong(ID))
                        .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(DISABLED, event.getString(DISABLED)));
            }

            var context = new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                    .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                    .put(USER_NAME, event.remove(USER_NAME))
                    .put(EventBusConstants.EVENT_TYPE, event.getString(EVENT_TYPE));

            if (event.getString(UI_EVENT_UUID) != null)
            {
                context.put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID));
            }

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, context);

            if (AGENT_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION, event);
            }
            else if (MOTADATA_MANAGER_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
            {
                Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, event);
            }

        }
        else
        {
            LOGGER.warn(String.format("failed to send event %s", event));
        }

    }

    private void validateAction(RoutingContext routingContext)
    {
        var item = RemoteEventProcessorCacheStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

        if (item == null)
        {
            this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                    .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.REMOTE_EVENT_PROCESSOR.getName())));
        }
        else if (item.containsKey(DISABLED) && item.getString(DISABLED).equalsIgnoreCase(YES)) //if any operation is running on remote event processor system will not allow user to perform operation
        {
            this.send(routingContext, new JsonObject().put(ID, CommonUtil.getLong(routingContext.request().getParam(ID)))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_REMOTE_EVENT_PROCESSOR_BUSY)
                    .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_BUSY,
                            item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))));
        }
        else
        {
            routingContext.next();
        }
    }
}

