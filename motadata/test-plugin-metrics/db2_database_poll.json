{"172.16.8.165": {"result": {"db2.db.path": "/database/data/db2inst1/NODE0000/SQL00001/MEMBER0000/", "started.time.sec": 19803, "started.time": "0 day, 5 hours, 30 minutes, 3 seconds", "db2.db.status": "ACTIVE", "db2.db.alias": "TESTDB", "db2.db.last.backup": 0, "db2.db.location": "LOCAL", "db2.db.dynamic.sql.queries": 1, "db2.db.failed.sql.queries": 1, "db2.db.internal.commits": 5, "db2.db.commits.rate": 2, "db2.db.rollbacks.rate": 0, "db2.db.deadlocks": 0, "db2.db.ddl.sql.queries.rate": 0, "db2.db.select.sql.queries.rate": 1, "db2.db.internal.rollbacks": 5, "db2.db.package.cache.inserts.rate": 1, "db2.db.package.cache.lookups.rate": 1, "db2.db.active.hash.joins.rate": 0, "db2.db.sorts.rate": 0, "db2.db.hash.joins.rate": 0, "db2.db.sort.overflows": 0, "db2.db.active.olap.functions": 0, "db2.db.lock.list.memory.bytes": 38400, "db2.db.active.sorts": 0, "db2.db.connected.applications": 1, "db2.db.executing.applications": 1, "db2.db.connections": 2, "db2.db.secondary.connections": 23, "db2.db.package.cache.overflows": 0, "db2.db.locks": 4, "db2.db.direct.reads.rate": 0, "db2.db.direct.reads.requests.rate": 0, "db2.db.direct.writes.rate": 0, "db2.db.direct.writes.requests.rate": 0, "db2.db.lock.waiters": 0, "db2.db.lock.escalations": 0, "db2.db.deleted.rows.rate": 0, "db2.db.inserted.rows.rate": 0, "db2.db.updated.rows.rate": 0, "db2.db.row.reads.rate": 1048, "db2.db.log.reads.rate": 0, "db2.db.log.read.ops.rate": 0, "db2.db.log.write.ops.rate": 0, "db2.db.free.log.space.bytes": 158605312, "db2.db.used.secondary.log.files": 0, "db2.db.used.log.space.bytes": 0, "db2.db.log.writes.rate": 0, "db2.db.exclusive.lock.escalations": 0, "db2.db.lock.waits": 0, "db2.db.lock.timeouts": 0, "db2.db.catalog.cache.inserts.rate": 21, "db2.db.catalog.cache.lookups.rate": 28, "db2.db.catalog.cache.overflows": 0, "db2.db.successful.sql.queries": 2, "db2.db.unit.works": 7, "db2.db.package.cache.hit.ratio.percent": 25.0, "db2.db.lock.waiting.percent": 0.0, "db2.db.log.space.bytes": 158605312, "db2.db.log.space.used.percent": 0.0, "db2.db.database.buffer.pool.hit.ratio.percent": 0, "db2.db.index.page.hit.ratio.percent": 0, "db2.db.data.page.hit.ratio.percent": 0, "db2.db.catalog.cache.hit.ratio.percent": 0.0}, "errors": []}, "fd00:1:1:1::132": {"result": {"db2.db.path": "/database/data/db2inst1/NODE0000/SQL00001/MEMBER0000/", "started.time.sec": 19803, "started.time": "0 day, 5 hours, 30 minutes, 3 seconds", "db2.db.status": "ACTIVE", "db2.db.alias": "TESTDB", "db2.db.last.backup": 0, "db2.db.location": "LOCAL", "db2.db.dynamic.sql.queries": 1, "db2.db.failed.sql.queries": 1, "db2.db.internal.commits": 5, "db2.db.commits.rate": 2, "db2.db.rollbacks.rate": 0, "db2.db.deadlocks": 0, "db2.db.ddl.sql.queries.rate": 0, "db2.db.select.sql.queries.rate": 1, "db2.db.internal.rollbacks": 5, "db2.db.package.cache.inserts.rate": 1, "db2.db.package.cache.lookups.rate": 1, "db2.db.active.hash.joins.rate": 0, "db2.db.sorts.rate": 0, "db2.db.hash.joins.rate": 0, "db2.db.sort.overflows": 0, "db2.db.active.olap.functions": 0, "db2.db.lock.list.memory.bytes": 38400, "db2.db.active.sorts": 0, "db2.db.connected.applications": 1, "db2.db.executing.applications": 1, "db2.db.connections": 2, "db2.db.secondary.connections": 23, "db2.db.package.cache.overflows": 0, "db2.db.locks": 4, "db2.db.direct.reads.rate": 0, "db2.db.direct.reads.requests.rate": 0, "db2.db.direct.writes.rate": 0, "db2.db.direct.writes.requests.rate": 0, "db2.db.lock.waiters": 0, "db2.db.lock.escalations": 0, "db2.db.deleted.rows.rate": 0, "db2.db.inserted.rows.rate": 0, "db2.db.updated.rows.rate": 0, "db2.db.row.reads.rate": 1048, "db2.db.log.reads.rate": 0, "db2.db.log.read.ops.rate": 0, "db2.db.log.write.ops.rate": 0, "db2.db.free.log.space.bytes": 158605312, "db2.db.used.secondary.log.files": 0, "db2.db.used.log.space.bytes": 0, "db2.db.log.writes.rate": 0, "db2.db.exclusive.lock.escalations": 0, "db2.db.lock.waits": 0, "db2.db.lock.timeouts": 0, "db2.db.catalog.cache.inserts.rate": 21, "db2.db.catalog.cache.lookups.rate": 28, "db2.db.catalog.cache.overflows": 0, "db2.db.successful.sql.queries": 2, "db2.db.unit.works": 7, "db2.db.package.cache.hit.ratio.percent": 25.0, "db2.db.lock.waiting.percent": 0.0, "db2.db.log.space.bytes": 158605312, "db2.db.log.space.used.percent": 0.0, "db2.db.database.buffer.pool.hit.ratio.percent": 0, "db2.db.index.page.hit.ratio.percent": 0, "db2.db.data.page.hit.ratio.percent": 0, "db2.db.catalog.cache.hit.ratio.percent": 0.0}, "errors": []}, "172.16.8.244": {"result": {"db2.db.path": "/export/home/<USER>/db2inst1/NODE0000/SQL00001/MEMBER0000/", "started.time.sec": 1, "started.time": "0 day, 0 hour, 0 minute, 1 second", "db2.db.status": "ACTIVE", "db2.db.alias": "SAMPLE", "db2.db.location": "LOCAL", "db2.db.dynamic.sql.queries": 1, "db2.db.failed.sql.queries": 0, "db2.db.internal.commits": 4, "db2.db.commits.rate": 0, "db2.db.rollbacks.rate": 0, "db2.db.deadlocks": 0, "db2.db.ddl.sql.queries.rate": 0, "db2.db.select.sql.queries.rate": 1, "db2.db.internal.rollbacks": 4, "db2.db.package.cache.inserts.rate": 1, "db2.db.package.cache.lookups.rate": 1, "db2.db.active.hash.joins.rate": 0, "db2.db.sorts.rate": 0, "db2.db.hash.joins.rate": 0, "db2.db.sort.overflows": 0, "db2.db.active.olap.functions": 0, "db2.db.lock.list.memory.bytes": 12800, "db2.db.active.sorts": 0, "db2.db.connected.applications": 1, "db2.db.executing.applications": 1, "db2.db.connections": 2, "db2.db.secondary.connections": 13, "db2.db.package.cache.overflows": 0, "db2.db.locks": 3, "db2.db.direct.reads.rate": 0, "db2.db.direct.reads.requests.rate": 0, "db2.db.direct.writes.rate": 0, "db2.db.direct.writes.requests.rate": 0, "db2.db.lock.waiters": 0, "db2.db.lock.escalations": 0, "db2.db.deleted.rows.rate": 0, "db2.db.inserted.rows.rate": 0, "db2.db.updated.rows.rate": 0, "db2.db.row.reads.rate": 637, "db2.db.log.reads.rate": 0, "db2.db.log.read.ops.rate": 0, "db2.db.log.write.ops.rate": 0, "db2.db.free.log.space.bytes": 52988000, "db2.db.used.secondary.log.files": 0, "db2.db.used.log.space.bytes": 0, "db2.db.log.writes.rate": 0, "db2.db.exclusive.lock.escalations": 0, "db2.db.lock.waits": 0, "db2.db.lock.timeouts": 0, "db2.db.catalog.cache.inserts.rate": 15, "db2.db.catalog.cache.lookups.rate": 22, "db2.db.catalog.cache.overflows": 0, "db2.db.successful.sql.queries": 1, "db2.db.unit.works": 4, "db2.db.package.cache.hit.ratio.percent": 31.82, "db2.db.lock.waiting.percent": 0.0, "db2.db.log.space.bytes": 52988000, "db2.db.log.space.used.percent": 0.0, "db2.db.database.buffer.pool.hit.ratio.percent": 0, "db2.db.index.page.hit.ratio.percent": 0, "db2.db.data.page.hit.ratio.percent": 0, "db2.db.catalog.cache.hit.ratio.percent": 0.0}, "errors": []}}