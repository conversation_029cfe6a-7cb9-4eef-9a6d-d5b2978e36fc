[{"_type": "0", "id": 10000000002303, "visualization.name": "Uptime", "visualization.description": "Uptime Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Uptime", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "cisco.wireless.access.point~started.time.seconds.last"}]}, "style": {"icon": {"name": "stopwatch"}, "color.data.point": "cisco.wireless.access.point~started.time.seconds"}}}}, {"_type": "0", "id": 10000000002302, "visualization.name": "Clients", "visualization.description": "Clients Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.point~clients"}]}], "visualization.properties": {"gauge": {"header": {"title": "Clients", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "cisco.wireless.access.point~clients.last"}]}, "style": {"icon": {"name": "users"}, "color.data.point": "cisco.wireless.access.point~clients"}}}}, {"_type": "0", "id": 10000000002329, "visualization.name": "Radios", "visualization.description": "Radios Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~slots", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.point~slots"}]}], "visualization.properties": {"gauge": {"header": {"title": "Radios", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "cisco.wireless.access.point~slots.last"}]}, "style": {"icon": {"name": "radios"}, "color.data.point": "cisco.wireless.access.point~slots"}}}}, {"_type": "0", "id": 10000000002301, "visualization.name": "AP Traffic", "visualization.description": "AP Traffic Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.result.by": ["monitor"], "visualization.category": "Grid", "visualization.type": "KPI Gauge", "instance.type.filter": {"cisco.wireless.access.point": "cisco.wireless.client~ap"}, "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.client~traffic.sent.bytes.per.sec"}, {"type": "metric", "data.point": "cisco.wireless.client~traffic.received.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"header": {"title": "AP Traffic", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "cisco.wireless.client~traffic.sent.bytes.per.sec.avg"}, {"label": "Received", "value": "cisco.wireless.client~traffic.received.bytes.per.sec.avg"}]}, "style": {"icon": {"name": "traffic"}, "color.data.point": "cisco.wireless.client~traffic.received.bytes.per.sec"}}}}, {"_type": "0", "id": 10000000002299, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.point~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Utilization", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "cisco.wireless.access.point~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "cisco.wireless.access.point~cpu.percent"}}}}, {"_type": "0", "id": 10000000002300, "visualization.name": "Memory Usage", "visualization.description": "Memory Usage Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~memory.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.point~memory.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Usage", "data.points": [], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "cisco.wireless.access.point~memory.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "cisco.wireless.access.point~memory.percent"}}}}, {"_type": "0", "id": 10000000002304, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002305, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "cisco.wireless.access.point~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002308, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "instance.type.filter": {"cisco.wireless.access.point": "cisco.wireless.client~ap"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002307, "visualization.name": "Client Details Wireless Cisco Access Point", "visualization.description": "Client Details Wireless Cisco Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "instance.type.filter": {"cisco.wireless.access.point": "cisco.wireless.client~ap"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~ap", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~snr", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~sent.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~received.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"icon": {"name": "check-circle", "placement": "prefix", "conditions": [{"icon": "check-circle", "operator": "=", "value": "Associated"}, {"icon": "times-circle", "operator": "!=", "value": "Associated"}]}}}, {"name": "cisco.wireless.client~ap.last", "title": "Access Point", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "cisco.wireless.client~snr.last", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "cisco.wireless.client~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "formula": {"operation": "combine", "columns": ["cisco.wireless.client~traffic.sent.bytes.per.sec.last", "cisco.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.wireless.client~traffic.sent.bytes.per.sec.last", "title": "TX Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "cisco.wireless.client~traffic.received.bytes.per.sec.last", "title": "RX Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "Packets", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 9, "formula": {"operation": "combine", "columns": ["cisco.wireless.client~sent.packets.per.sec.last", "cisco.wireless.client~received.packets.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.wireless.client~sent.packets.per.sec.last", "title": "Sent Packets", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "cisco.wireless.client~received.packets.per.sec.last", "title": "Received Packets", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "cisco.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 12, "style": {}}, {"name": "cisco.wireless.client~ap.mac.address.last", "title": "MAC Address", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}, {"name": "cisco.wireless.client~signal.strength.dbm.last", "title": "Signal Strength", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 14, "style": {}}, {"name": "cisco.wireless.client~started.time.seconds.last", "title": "Connected Since", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 15, "style": {}}]}}}]