/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.discovery;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Discovery;
import com.mindarray.api.Scheduler;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.net.PemKeyCertOptions;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import io.vertx.ext.web.multipart.MultipartForm;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.apache.http.HttpHeaders;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_TARGET;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.Discovery.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.util.CronExpressionUtil.CRON_DAILY;
import static com.mindarray.util.CronExpressionUtil.CRON_ONCE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.CONCURRENT)
@Timeout(130 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestCSVRangeObjectDiscovery
{

    public static final JsonObject TEST_PARAMETERS = new JsonObject();
    public static final JsonObject DISCOVERY_PROFILES = new JsonObject();
    public static final Map<Long, VertxTestContext> DISCOVERY_ITEMS = new HashMap<>();
    private static final Logger LOGGER = new Logger(TestCSVRangeObjectDiscovery.class, "discovery-test", "Discovery Test");
    private static Set<String> targets = new HashSet<>();
    private static Map<String, Object> discoveryContext = new HashMap<>();

    private static MessageConsumer<JsonObject> discoveryStatusMessageHandler;

    private static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        assertDiscoveryConsumerSetup();

        TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

        try
        {
            var paramFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "discovery-parameters.json");

            var csvFile = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "csv-range-discovery.json");


            if (paramFile.exists() && csvFile.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                DISCOVERY_PROFILES.mergeIn(new JsonObject(Files.readString(paramFile.toPath(), StandardCharsets.UTF_8)));

                TEST_PARAMETERS.mergeIn(new JsonObject(Files.readString(csvFile.toPath(), StandardCharsets.UTF_8)));

                assertDiscoveryTestResult();

                //#3468
                assertDiscoveryStatusTestResult();

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    //#3468 #3407
    private static void assertDiscoveryStatusTestResult()
    {
        try
        {
            discoveryStatusMessageHandler = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
            {
                var testContext = (VertxTestContext) discoveryContext.get("test-context");

                if (message.body().containsKey(EVENT_TYPE))
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(CommonUtil.getLong(discoveryContext.get("discovery-id"))))
                    {
                        if (message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISCOVERY_STATE_CHANGE))
                        {
                            Assertions.assertTrue(eventContext.containsKey(DISCOVERY_STATUS));

                            Assertions.assertTrue(eventContext.containsKey(STATE));

                            if (eventContext.getString(STATE).equalsIgnoreCase(STATE_RUNNING))
                            {
                                assertTrue(eventContext.getString(DISCOVERY_STATUS).contains("Discovery is running, started at "));
                            }
                            else if (eventContext.getString(STATE).equalsIgnoreCase(STATE_NOT_RUNNING))
                            {
                                assertTrue(eventContext.getString(DISCOVERY_STATUS).contains("Last ran at "));

                                testContext.completeNow();
                            }
                        }
                        else if (message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISCOVERY_PROGRESS) && eventContext.getString(USER_NAME) != null)
                        {
                            Assertions.assertTrue(eventContext.getString(USER_NAME).equalsIgnoreCase(DEFAULT_USER));
                        }
                    }


                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            messageConsumer.unregister(result -> ((VertxTestContext) discoveryContext.get("test-context")).failNow(exception));
        }
    }

    private static void assertDiscoveryTestResult()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var testContext = (VertxTestContext) discoveryContext.get("test-context");

                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (discoveryContext.containsKey("test-state-event") && discoveryContext.get("test-state-event").equals(true))
                {
                    if (eventContext.getJsonObject(EVENT_CONTEXT).containsKey(DISCOVERY_STATUS) && eventContext.getJsonObject(EVENT_CONTEXT).containsKey(STATE))
                    {
                        testContext.completeNow();
                    }
                }
                else
                {
                    if (eventContext.getString(EVENT_STATE) != null && eventContext.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED))
                    {
                        if (eventContext.getJsonObject(EVENT_CONTEXT) != null && !eventContext.getJsonObject(EVENT_CONTEXT).isEmpty() && eventContext.getJsonObject(EVENT_CONTEXT).getString(OBJECT_TARGET) != null
                                && targets.contains(eventContext.getJsonObject(EVENT_CONTEXT).getString(OBJECT_TARGET)))
                        {
                            Assertions.assertTrue(targets.remove(eventContext.getJsonObject(EVENT_CONTEXT).getString(OBJECT_TARGET)));
                        }
                        else if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(CommonUtil.getString(discoveryContext.get("discovery-name"))) && eventContext.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED))
                        {
                            Assertions.assertTrue(targets.isEmpty());

                            // it's not feasible that everytime discovery takes same time to execute hence commenting this condition as this creates a issue in some random failure of test cases..
                            //Assertions.assertTrue(System.currentTimeMillis() - CommonUtil.getLong(discoveryContext.get("current-time")) <= CommonUtil.getLong(discoveryContext.get("max-time")));
                        }

                        // if first event came of event.state.completed than complete it or check discovery state first and than complete..
                        testContext.completeNow();
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception.getCause());
            }
        });
    }

    private static void post(MultipartForm form, Handler<AsyncResult<HttpResponse<Buffer>>> handler)
    {
        WebClient client = WebClient.create(TestUtil.vertx(), new WebClientOptions()
                .setTryUseCompression(true)
                .setPemKeyCertOptions(new PemKeyCertOptions()
                        .setCertPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-cert.pem")
                        .setKeyPath(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem"))
                .setSsl(true)
                .setUseAlpn(true)
                .setVerifyHost(false)
                .setTrustAll(true)
                .setMaxPoolSize(1));

        var request = client.post(MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()), "localhost", UPLOAD_ENDPOINT)
                .putHeader(HttpHeaders.CONTENT_TYPE, "multipart/form-data");

        request.sendMultipartForm(form, handler);
    }

    private static Future<Set<String>> getCSVDiscoveryTargets(VertxTestContext testContext, String fileName)
    {
        var promise = Promise.<Set<String>>promise();

        try
        {
            var csvFilePath = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + fileName;

            TestUtil.vertx().fileSystem().exists(csvFilePath, result ->
            {
                if (result.succeeded() && result.result())
                {
                    var content = CommonUtil.getString(TestUtil.vertx().fileSystem().readFileBlocking(csvFilePath));

                    if (content != null && !content.isEmpty())
                    {
                        var header = content.trim().split("\\n")[0];

                        if (CommonUtil.isNotNullOrEmpty(header))
                        {
                            for (var value : content.trim().split("\\n"))
                            {
                                var object = value.trim().split(",")[0].trim();

                                if (CommonUtil.isNotNullOrEmpty(object) && !object.equalsIgnoreCase(TARGET))
                                {
                                    targets.add(object);
                                }
                            }
                            promise.complete(targets);
                        }
                    }
                }
                else
                {
                    promise.fail(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            testContext.failNow(exception);
        }

        return promise.future();
    }

    private static void runDiscoveryCSVTest(VertxTestContext testContext, JsonObject item)
    {
        if (item.getString(DISCOVERY_TYPE).equalsIgnoreCase(DISCOVERY_TYPE_CSV))
        {
            var form = MultipartForm.create()
                    .textFileUpload("csvFile", item.getString(DISCOVERY_TARGET), GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR + "test" + GlobalConstants.PATH_SEPARATOR + "resources" +
                            GlobalConstants.PATH_SEPARATOR + item.getString(DISCOVERY_TARGET), "text/csv");

            post(form, testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        var result = response.bodyAsJsonObject();

                        Assertions.assertNotNull(result);

                        assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        assertTrue(result.containsKey(RESULT));

                        assertFalse(result.getString(RESULT).isEmpty());

                        getCSVDiscoveryTargets(testContext, result.getString(RESULT)).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                TestAPIUtil.put("/api/v1/settings/discoveries/" + CommonUtil.getString(item.getLong(ID)), new JsonObject().put(DISCOVERY_TARGET, result.getString(RESULT)), asyncResponse ->
                                {

                                    if (asyncResponse.succeeded())
                                    {
                                        runDiscoveryTest(testContext, item);
                                    }
                                    else
                                    {
                                        testContext.failNow(asyncResponse.cause());
                                    }
                                });
                            }
                            else
                            {
                                Assertions.fail(String.format("failed to get csv target for %s", item.getString(DISCOVERY_TYPE)));
                            }
                        });
                    })));
        }
        else
        {
            TestAPIUtil.put("/api/v1/settings/discoveries/" + CommonUtil.getString(item.getLong(ID)), new JsonObject().put(DISCOVERY_TARGET, item.getString(DISCOVERY_TARGET)), result ->
            {

                if (result.succeeded())
                {
                    runDiscoveryTest(testContext, item);
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
    }

    private static void setIPRangeDiscoveryTargets(VertxTestContext testContext, String range)
    {
        try
        {
            var start = range.split("-")[0].trim();

            var end = start.substring(0, start.lastIndexOf('.')) + "." + range.split("-")[1].trim();

            targets = CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end));
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    private static void setCIDRDiscoveryTargets(VertxTestContext testContext, String cidr)
    {
        try
        {
            var subnet = new SubnetUtils(cidr);

            subnet.setInclusiveHostCount(false);

            for (var target : subnet.getInfo().getAllAddresses())
            {
                target = CommonUtil.getString(target);

                if (!target.endsWith(".0") && !target.endsWith(".255"))
                {
                    targets.add(target);
                }
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

    }

    public static void runDiscoveryTest(VertxTestContext testContext, JsonObject item)
    {
        DISCOVERY_ITEMS.put(item.getLong(ID), testContext);

        TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, item.getLong(ID)), new JsonObject(), result -> testContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
    }

    private static void assertDiscoveryConsumerSetup()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().getBinary(EVENT_CONTEXT) != null)
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (eventContext.containsKey(EVENT_NAME) && eventContext.getString(EVENT_NAME).equalsIgnoreCase("discovery"))
                {
                    var context = eventContext.getJsonObject(EVENT_CONTEXT);

                    if (DISCOVERY_ITEMS.containsKey(context.getLong(ID)) && eventContext.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED))
                    {
                        DISCOVERY_ITEMS.get(context.getLong(ID)).verify(() ->
                        {
                            assertEquals(EVENT_STATE_COMPLETED, eventContext.getString(EVENT_STATE));

                            DISCOVERY_ITEMS.get(context.getLong(ID)).completeNow();

                            DISCOVERY_ITEMS.remove(context.getLong(ID));
                        });
                    }
                }
            }
        });
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        messageConsumer.unregister(result -> testContext.completeNow());
    }

    @BeforeEach
    void unregisterMessageHandler(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(2000, TimeUnit.MILLISECONDS);

        discoveryContext = new HashMap<>();

        targets.clear();

        testContext.completeNow();
    }

    @Test
    void testLinuxRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("linux").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testLinuxRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_NAME, "Linux IPRange").put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, "ssh-************").getLong(GlobalConstants.ID))).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testLinuxCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("linux").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testLinuxCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_NAME, "Linux CIDR").put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(CredentialProfileConfigStore.getStore().getItemByValue(CredentialProfile.CREDENTIAL_PROFILE_NAME, "ssh-************").getLong(GlobalConstants.ID))).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testLinuxCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("linux").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testLinuxCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testLinuxCSVTagDiscovery(VertxTestContext testContext) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testLinuxCSVDiscovery").getString(Discovery.DISCOVERY_NAME));

        LOGGER.info(String.format("linux discovery context : %s ", context.encode()));

        TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + context.getLong(ID) + "/result", asyncResult ->
        {
            try
            {
                if (asyncResult.succeeded())
                {
                    Assertions.assertNotNull(asyncResult.result());

                    var response = asyncResult.result().bodyAsJsonObject();

                    Assertions.assertNotNull(response);

                    LOGGER.info(String.format("discovery result : %s ", response.encode()));

                    Assertions.assertEquals(STATUS_SUCCEED, response.getString(STATUS));

                    Assertions.assertEquals(SC_OK, response.getInteger(RESPONSE_CODE));

                    Assertions.assertTrue(response.containsKey(RESULT));

                    var objects = response.getJsonArray(RESULT);

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = objects.getJsonObject(index);

                        Assertions.assertFalse(object.getJsonArray(AIOpsObject.OBJECT_TAGS).isEmpty());

                        for (var tag : object.getJsonArray(AIOpsObject.OBJECT_TAGS))
                        {
                            var item = TagConfigStore.getStore().getItem(CommonUtil.getLong(tag));

                            assertNotNull(item);

                            assertTrue(StringUtils.isAllLowerCase(item.getString("tag")));
                        }
                    }

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(asyncResult.cause());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                testContext.failNow(exception);
            }
        });
    }

    @Test
    void testWindowsRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("windows").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testWindowsRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);


    }

    @Test
    void testWindowsCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("windows").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testWindowsCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testWindowsCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("windows").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testWindowsCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testNetworkRangeDiscoveryPluginEngineInfoLogLevel(VertxTestContext testContext)
    {
        try
        {
            var range = TEST_PARAMETERS.getJsonObject("network").getString("range");

            setIPRangeDiscoveryTargets(testContext, range);

            CommonUtil.setLogLevel(LOG_LEVEL_INFO);

            var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testNetworkRangeDiscoveryPluginEngineInfoLogLevel").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

            runDiscoveryCSVTest(testContext, context);
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @Test
    void testNetworkRangeDiscoveryPluginEngineDebugLogLevel(VertxTestContext testContext)
    {
        try
        {
            var range = TEST_PARAMETERS.getJsonObject("network").getString("range");

            setIPRangeDiscoveryTargets(testContext, range);

            CommonUtil.setLogLevel(LOG_LEVEL_DEBUG);

            var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testNetworkRangeDiscoveryPluginEngineDebugLogLevel").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

            runDiscoveryCSVTest(testContext, context);

        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }
    }

    @Test
    void testNetworkCIDRDiscovery(VertxTestContext testContext)
    {
        CommonUtil.setLogLevel(LOG_LEVEL_TRACE);

        var cidr = TEST_PARAMETERS.getJsonObject("network").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testSNMPv1CIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);

    }

    @Test
    void testNetworkCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("network").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testNetworkCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testESXiRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("esxi").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testESXiRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testESXiCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("esxi").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testESXiCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testESXiCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("esxi").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testESXiCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testHyperVRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("hyperv").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testHyperVCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("hyperv").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);

    }

    @Test
    void testHyperVCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("hyperv").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testHyperVClusterRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("hyperv.cluster").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVClusterRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testHyperVClusterCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("hyperv.cluster").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVClusterCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testHyperVClusterCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("hyperv.cluster").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testHyperVClusterCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testCitrixRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("citrix").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testCitrixRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testCitrixCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("citrix").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testCitrixCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testCitrixCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("citrix").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testCitrixCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPingRangeDiscovery(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("ping").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPingRangeDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPingCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("ping").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPingCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPingCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("ping").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPingCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPortCIDRDiscovery(VertxTestContext testContext)
    {
        var cidr = TEST_PARAMETERS.getJsonObject("port").getString("cidr");

        setCIDRDiscoveryTargets(testContext, cidr);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPortCIDRDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CIDR).put(Discovery.DISCOVERY_TARGET, cidr).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPortCSVDiscovery(VertxTestContext testContext)
    {
        var csv = TEST_PARAMETERS.getJsonObject("port").getString("csv");

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPortCSVDiscovery").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_CSV).put(Discovery.DISCOVERY_TARGET, csv).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        runDiscoveryCSVTest(testContext, context);
    }

    @Test
    void testPortRangeDiscoveryConcurrentScheduler(VertxTestContext testContext)
    {
        var range = TEST_PARAMETERS.getJsonObject("port").getString("range");

        setIPRangeDiscoveryTargets(testContext, range);

        var context = DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject("testPortRangeDiscoveryConcurrentScheduler").getString(Discovery.DISCOVERY_NAME)).copy().put(Discovery.DISCOVERY_TYPE, Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

        var values = new SimpleDateFormat("dd-MM-yyyy HH:mm").format(new Date()).split(" ");

        var tokens = values[1].split(":");

        var lastDigit = CommonUtil.getInteger(values[1].split(":")[1]);

        if (lastDigit == 59)
        {
            tokens[0] = CommonUtil.getString(CommonUtil.getInteger(tokens[0]) + 1);

            tokens[1] = "00";
        }

        else
        {
            tokens[1] = CommonUtil.getString(CommonUtil.getInteger(tokens[1]) + 1);
        }

        var scheduler = new JsonObject().put(Scheduler.SCHEDULER_START_DATE, values[0])
                .put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(tokens[0] + ":" + tokens[1]))
                .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName())
                .put(Scheduler.SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, YES)
                        .put(OBJECTS, new JsonArray().add(context.getLong(ID))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                    scheduler.put(Scheduler.SCHEDULER_TIMELINE, CRON_DAILY);

                                    TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler, testContext.succeeding(schedulerResponse ->
                                            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), handler ->
                                            {
                                                TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                                TestAPIUtil.delete(DISCOVERY_API_ENDPOINT + "/" + context.getLong(ID), testContext.succeeding(httpResponse -> testContext.verify(() ->
                                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), timer ->
                                                        {
                                                            TestAPIUtil.assertDeleteEntityTestResult(DiscoveryConfigStore.getStore(), httpResponse.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "Discovery"));

                                                            testContext.completeNow();
                                                        }))));
                                            })));
                                }))));
    }

    @Test
    void testExcludeIPAddressRangeDiscovery(VertxTestContext vertxTestContext)
    {
        messageConsumer.unregister();

        discoveryStatusMessageHandler.unregister();

        var discoveryParameters = new JsonObject("""
                {
                  "discovery.category": "Network",
                  "discovery.object.type": "SNMP Device",
                  "discovery.name": "Filter IP Address Range With CIDR",
                  "discovery.type": "cidr",
                  "discovery.target": "***********/24",
                  "discovery.groups": [
                    10000000000013
                  ],
                  "discovery.credential.profiles": [
                    10000000000001
                  ],
                  "discovery.context": {
                    "ping.check.status": "yes",
                    "port": 161
                  },
                  "discovery.status": "Not Run Yet",
                  "discovery.total.objects": 0,
                  "discovery.discovered.objects": 0,
                  "discovery.exclude.target.type":"ip.address.range",
                  "discovery.exclude.targets": ["***********-41", "************-89", "************-100","***********02-200"],
                  "_type": "1"
                }""");

        TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                vertxTestContext.verify(() ->
                {
                    assertEquals(SC_OK, discoveryResponse.statusCode());

                    var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                    var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

                    Assertions.assertNotNull(discovery);

                    assertDiscoveryTargetFilterTestResult(discovery, 69, vertxTestContext);

                    TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), result ->
                            vertxTestContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
                })));
    }

    @Test
    void testExcludeIPAddressDiscovery(VertxTestContext vertxTestContext)
    {
        messageConsumer.unregister();

        var discoveryParameters = new JsonObject("""
                {
                \t"discovery.category": "Network",
                \t"discovery.object.type": "SNMP Device",
                \t"discovery.name": "Filter IP Address With IP Range",
                \t"discovery.type": "ip.address.range",
                \t"discovery.target": "************-50",
                \t"discovery.groups": [
                \t\t10000000000013
                \t],
                \t"discovery.credential.profiles": [
                \t\t10000000000001
                \t],
                \t"discovery.context": {
                \t\t"ping.check.status": "yes",
                \t\t"port": 161
                \t},
                \t"discovery.status": "Not Run Yet",
                \t"discovery.total.objects": 0,
                \t"discovery.discovered.objects": 0,
                \t"discovery.exclude.target.type": "ip.address",
                \t"discovery.exclude.targets": [
                \t\t"************", "************"
                \t],
                \t"_type": "1"
                }""");

        TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                vertxTestContext.verify(() ->
                {
                    assertEquals(SC_OK, discoveryResponse.statusCode());

                    var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                    var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

                    Assertions.assertNotNull(discovery);

                    assertDiscoveryTargetFilterTestResult(discovery, 6, vertxTestContext);

                    TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), result ->
                            vertxTestContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
                })));
    }

    private void assertDiscoveryTargetFilterTestResult(JsonObject discovery, int targetLength, VertxTestContext vertxTestContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISCOVERY_PROBES))
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (eventContext.containsKey(ID) && eventContext.getLong(ID).equals(discovery.getLong(ID)))
                    {
                        Assertions.assertTrue(eventContext.containsKey(GlobalConstants.RESULT));

                        Assertions.assertEquals(targetLength, eventContext.getJsonArray(GlobalConstants.RESULT).size());

                        TestUtil.vertx().eventBus().send(EVENT_DISCOVERY_ABORT, eventContext.getLong(ID));

                        messageConsumer.unregister(result -> vertxTestContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(result -> vertxTestContext.failNow(exception));
            }
        });
    }
}
