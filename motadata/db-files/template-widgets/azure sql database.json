[{"_type": "0", "id": 10000000000276, "visualization.name": "Connections", "visualization.description": "MSSQL Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.successful.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.failed.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.successful.connections"}, {"type": "metric", "data.point": "azure.sql.database.failed.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.failed.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Successful", "value": "azure.sql.database.successful.connections.last"}, {"label": "Failed", "value": "azure.sql.database.failed.connections.last"}]}}}}, {"_type": "0", "id": 10000000000277, "visualization.name": "Storage", "visualization.description": "MSSQL Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.storage.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.data.storage.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.storage.size.bytes"}, {"type": "metric", "data.point": "azure.sql.database.data.storage.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.data.storage.used.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "azure.sql.database.data.storage.used.bytes.last"}, {"label": "Total", "value": "azure.sql.database.storage.size.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000278, "visualization.name": "Session Utilization", "visualization.description": "MSSQL Session Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.session.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.session.utilization.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.session.utilization.percent", "icon": {"name": "stopwatch", "placement": "prefix"}}, "header": {"title": "Session Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.session.utilization.percent.last"}]}}}}, {"_type": "0", "id": 10000000000279, "visualization.name": "Worker Utilization", "visualization.description": "MSSQL Worker Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.worker.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.worker.utilization.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.worker.utilization.percent", "icon": {"name": "settings", "placement": "prefix"}}, "header": {"title": "Worker Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.worker.utilization.percent.last"}]}}}}, {"_type": "0", "id": 10000000000280, "visualization.name": "Process Utilization", "visualization.description": "MSSQL Process Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.sql.server.process.core.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.sql.server.process.core.utilization.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.sql.server.process.core.utilization.percent", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "Process Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.sql.server.process.core.utilization.percent.last"}]}}}}, {"_type": "0", "id": 10000000000281, "visualization.name": "Connection Blocked By Firewall", "visualization.description": "MSSQL Connection Blocked By Firewall", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.firewall.blocked.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.sql.database.firewall.blocked.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.sql.database.firewall.blocked.connections", "icon": {"name": "rejected-connections", "placement": "prefix"}}, "header": {"title": "Connection Blocked By Firewall", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.sql.database.firewall.blocked.connections.last"}]}}}}, {"_type": "0", "id": 10000000000282, "visualization.name": "Resource Utilization", "visualization.description": "MSSQL Resource Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.dtu.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.in.memory.oltp.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000283, "visualization.name": "Performance Statistics", "visualization.description": "MSSQL Performance Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.session.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.worker.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000284, "visualization.name": "DTU Utilization", "visualization.description": "MSSQL DTU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.used.dtu", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.dtu.limit", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000285, "visualization.name": "Database Log & Data IO Utilization", "visualization.description": "MSSQL Database Log & Data IO Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.log.io.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.data.io.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000286, "visualization.name": "Database Storage Utilization", "visualization.description": "MSSQL Database Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.data.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000287, "visualization.name": "Temp DB Details", "visualization.description": "MSSQL Temp DB Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.tempdb.data.file.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.tempdb.log.file.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000288, "visualization.name": "Connection Statistics", "visualization.description": "MSSQL Connection Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.successful.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.sql.database.firewall.blocked.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000289, "visualization.name": "Database Deadlocks", "visualization.description": "MSSQL Database Deadlocks", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.deadlocks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000290, "visualization.name": "Replication Details", "visualization.description": "MSSQL Replication Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.sql.database.replication.links", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]