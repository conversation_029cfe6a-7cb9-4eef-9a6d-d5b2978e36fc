/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.service;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.OBJECT_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.OBJECT_TYPE;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(30 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestMetricEnricher
{
    private static final Logger LOGGER = new Logger(TestMetricEnricher.class, GlobalConstants.MOTADATA_NMS, "Metric Enricher Test");
    private static final String ENRICHER_TEST_EVENT = EventBusConstants.EVENT_METRIC_ENRICHER + ".test";
    private static final String TEMP_ENRICHER = "temp.enricher";
    private static String metricName = EMPTY_VALUE;
    private static String instanceType = EMPTY_VALUE;
    private static long objectId = 0L;
    private static long metricId = 0L;
    private static JsonArray metrics;
    private static VertxTestContext vertxTestContext;
    private static MessageConsumer<JsonObject> messageConsumer;


    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var retries = new AtomicInteger();

        var promise = Promise.<Void>promise();

        for (var index = 0; index < MotadataConfigUtil.getMetricEnricherInstances(); index++)
        {
            Bootstrap.undeployVerticle(EventBusConstants.EVENT_METRIC_ENRICHER + "." + index).onComplete(undeploy ->
            {
                retries.getAndIncrement();

                if (retries.get() == MotadataConfigUtil.getMetricEnricherInstances())
                {
                    promise.complete();
                }
            });
        }

        promise.future().onComplete(response ->
                Bootstrap.startEngine(new LocalEventRouter(TEMP_ENRICHER, AIOpsObject.OBJECT_ID,
                                1, "com.mindarray.nms.MetricEnricher", false), TEMP_ENRICHER, null)
                        .onComplete(result ->
                        {
                            assertMetricRateTestResult();

                            testContext.completeNow();
                        }));
    }

    private static void assertMetricRateTestResult()
    {
        Bootstrap.vertx().eventBus().<JsonObject>localConsumer(ENRICHER_TEST_EVENT, message ->
        {
            try
            {
                var event = message.body();

                if (metricName.equalsIgnoreCase(event.getString(Metric.METRIC_PLUGIN)) && event.containsKey("assertion.required"))
                {
                    var polledMetrics = 0;

                    if (instanceType != null)
                    {
                        LOGGER.debug("Event received for assertion::" + event);

                        for (var item : event.getJsonObject(RESULT).getJsonArray(instanceType))
                        {
                            var instance = JsonObject.mapFrom(item);

                            for (var key : metrics)
                            {
                                var metric = CommonUtil.getString(key);

                                if (instance.containsKey(metric))
                                {
                                    polledMetrics++;
                                }
                            }
                        }
                    }
                    else
                    {

                        for (var key : event.getJsonObject(RESULT).getMap().keySet())
                        {
                            if (metrics.contains(key))
                            {
                                polledMetrics++;
                            }
                        }
                    }

                    if ((instanceType != null && polledMetrics >= metrics.size()) || metrics.size() == polledMetrics)
                    {
                        vertxTestContext.completeNow();
                    }
                    else
                    {
                        vertxTestContext.failNow("Counters Not Added");
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @BeforeEach
    void unregisterContext(VertxTestContext testContext)
    {
        try
        {
            testContext.awaitCompletion(2, TimeUnit.SECONDS);
        }
        catch (Exception ignored)
        {
        }

        metricName = EMPTY_VALUE;

        metrics = null;

        vertxTestContext = null;

        instanceType = null;

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testWindowsMetricRate(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        vertxTestContext = testContext;

        var previousPoll = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var currentPoll = TestConstants.prepareParams(testInfo.getTestMethod().get().getName() + "1");

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::132");

        Assertions.assertNotNull(object);

        objectId = object.getLong(ID);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(previousPoll.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                metricId = result.getLong(ID);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        previousPoll.mergeIn(result);

        currentPoll.mergeIn(result);

        metricName = previousPoll.getString(Metric.METRIC_PLUGIN);

        metrics = new JsonArray().add("system.disk.io.per.sec").add("system.disk.io.operation.rate");

        previousPoll.put("assertion.required", true);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, previousPoll);

        TimeUnit.SECONDS.sleep(2);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, currentPoll);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, DISABLE_METRIC.name()).put(ID, metricId));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testNetworkInterfaceMetricRate(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        vertxTestContext = testContext;

        instanceType = NMSConstants.INTERFACE;

        metrics = new JsonArray().add("interface~instance.name").add("interface~in.traffic.utilization.percent").add("interface~out.traffic.utilization.percent").add("interface~in.traffic.bytes.per.sec").add("interface~out.traffic.bytes.per.sec");

        var previousPoll = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var currentPoll = TestConstants.prepareParams(testInfo.getTestMethod().get().getName() + "1");

        var pollResponse = TestConstants.prepareParams(testInfo.getTestMethod().get().getName() + "2");

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(previousPoll.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                metricId = result.getLong(ID);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        previousPoll.mergeIn(result);

        currentPoll.mergeIn(result);

        pollResponse.mergeIn(result);

        metricName = previousPoll.getString(Metric.METRIC_PLUGIN);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, previousPoll);

        TimeUnit.SECONDS.sleep(2);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, currentPoll);

        TimeUnit.SECONDS.sleep(2);

        pollResponse.put("assertion.required", true);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, pollResponse);

        TimeUnit.SECONDS.sleep(2);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, DELETE_OBJECT.name()).put(ID, metricId).put(NMSConstants.OBJECTS, new JsonArray().add("Gi0")));

        TimeUnit.SECONDS.sleep(2);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, SUSPEND_METRIC.name()).put(ID, metricId));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCorrelatedMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var pollResponse = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var object = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.LINUX.getName())).getJsonObject(0);

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(pollResponse.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                metricId = result.getLong(ID);

                break;

            }
        }

        Assertions.assertFalse(result.isEmpty());

        pollResponse.mergeIn(result);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, pollResponse);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, DELETE_METRIC.name()).put(ID, metricId));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testVirtualizationVMMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        vertxTestContext = testContext;

        metrics = new JsonArray().add("total.vms");

        var pollResponse = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var object = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.VMWARE_ESXI.getName())).getJsonObject(0);

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        objectId = object.getLong(ID);

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(pollResponse.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        pollResponse.mergeIn(result);

        metricName = pollResponse.getString(Metric.METRIC_PLUGIN);

        pollResponse.put("assertion.required", true);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, pollResponse);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, DELETE_OBJECT.name()).put(ID, objectId));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testVirtualizationCitrixVMMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        vertxTestContext = testContext;

        metrics = new JsonArray().add("total.vms");

        var pollResponse = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var object = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.CITRIX_XEN.getName())).getJsonObject(0);

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(pollResponse.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        pollResponse.mergeIn(result);

        metricName = pollResponse.getString(Metric.METRIC_PLUGIN);

        pollResponse.put("assertion.required", true);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, pollResponse);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testAddNewLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(TEMP_ENRICHER + ".add.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(TEMP_ENRICHER + "." + 2, reply.result().body());

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testRemoveLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(TEMP_ENRICHER + ".remove.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(TEMP_ENRICHER + "." + 2, reply.result().body());

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testVirtualizationHyperVVMMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        vertxTestContext = testContext;

        metrics = new JsonArray().add("total.vms");

        var pollResponse = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var object = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.HYPER_V.getName())).getJsonObject(0);

        Assertions.assertNotNull(object);

        var result = new JsonObject();

        for (var item : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, new JsonArray().add(object.getLong(ID))))
        {
            var metric = JsonObject.mapFrom(item);

            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase(pollResponse.getString(Metric.METRIC_NAME)))
            {
                result.mergeIn(metric);

                break;
            }
        }

        Assertions.assertFalse(result.isEmpty());

        pollResponse.mergeIn(result);

        metricName = pollResponse.getString(Metric.METRIC_PLUGIN);

        pollResponse.put("assertion.required", true);

        Bootstrap.vertx().eventBus().send(TEMP_ENRICHER, pollResponse);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(60 * 1000)
    void testSendPollingErrorNotification(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        var object = ObjectConfigStore.getStore().getItem(id);

        object.put(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS, "yes").put(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(OBJECT_SMS_NOTIFICATION_RECIPIENTS, new JsonArray().add("9876543211")).put(OBJECT_MONITOR_POLLING_FAILED_RENOTIFICATION_TIMER_SECONDS, 900);

        TestAPIUtil.put(OBJECT_API_ENDPOINT + "/" + CommonUtil.getLong(object.getString(ID)), object, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            assertEquals(SC_OK, result.getInteger(RESPONSE_CODE));

            assertEquals(STATUS_SUCCEED, result.getString(STATUS));

            if (result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
            {
                var polledContext = new JsonObject("{\"cli.enabled\":\"no\",\"event.id\":8152074825579,\"event.timestamp\":1710313313,\"id\":8152074890900,\"metric.category\":\"Server\",\"metric.name\":\"LinuxDirectory\",\"metric.object\":8152074890896,\"metric.plugin\":\"linuxdirectory\",\"metric.state\":\"ENABLE\",\"metric.type\":\"Linux\",\"object.category\":\"Server\",\"object.creation.time.seconds\":1710312999,\"object.email.notification.recipients\":[\"<EMAIL>\"],\"object.groups\":[10000000000019,10000000000017],\"object.host\":\"motadata1098\",\"object.id\":1,\"object.ip\":\"***********\",\"object.monitor.polling.failed.notification.status\":\"yes\",\"object.monitor.polling.failed.notification.timer.seconds\":900,\"object.name\":\"motadata1098\",\"object.scheduler.status\":\"no\",\"object.sms.notification.recipients\":[\"9876543211\"],\"object.target\":\"************\",\"object.type\":\"Linux\",\"object.user.tags\":[],\"password\":\"motadata1\",\"plugin.id\":91,\"remote.address\":\"0:0:0:0:0:0:0:1\",\"result\":{},\"status\":\"fail\",\"user.name\":\"admin\",\"username\":\"motadata1\",\"error.code\":\"MD003\",\"message\":\"Metricpollingfailed.Possiblereason:Invalidcredentials\",\"event.type\":\"metric.poll\",\"copy.required\":false}").put("metric.object", CommonUtil.getLong(object.getString(ID)));

                TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENRICHER, polledContext);

                messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
                {

                    var notification = message.body();

                    if (!notification.isEmpty() && notification.getJsonArray("email.notification.recipients").contains("<EMAIL>"))
                    {
                        Assertions.assertEquals(Notification.NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                        messageConsumer.unregister(asyncResult -> testContext.completeNow());
                    }
                });
            }
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollingErrorWithinNotificationDuration(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        var object = ObjectConfigStore.getStore().getItem(id);

        var polledContext = new JsonObject("{\"cli.enabled\":\"no\",\"event.id\":8152074825579,\"event.timestamp\":1710313313,\"id\":8152074890900,\"metric.category\":\"Server\",\"metric.name\":\"LinuxDirectory\",\"metric.object\":8152074890896,\"metric.plugin\":\"linuxdirectory\",\"metric.state\":\"ENABLE\",\"metric.type\":\"Linux\",\"object.category\":\"Server\",\"object.creation.time.seconds\":1710312999,\"object.email.notification.recipients\":[\"<EMAIL>\"],\"object.groups\":[10000000000019,10000000000017],\"object.host\":\"motadata1098\",\"object.id\":1,\"object.ip\":\"***********\",\"object.monitor.polling.failed.notification.status\":\"yes\",\"object.monitor.polling.failed.notification.timer.seconds\":900,\"object.name\":\"motadata1098\",\"object.scheduler.status\":\"no\",\"object.sms.notification.recipients\":[\"9876543211\"],\"object.target\":\"************\",\"object.type\":\"Linux\",\"object.user.tags\":[],\"password\":\"motadata1\",\"plugin.id\":91,\"remote.address\":\"0:0:0:0:0:0:0:1\",\"result\":{},\"status\":\"fail\",\"user.name\":\"admin\",\"username\":\"motadata1\",\"error.code\":\"MD003\",\"message\":\"Metricpollingfailed.Possiblereason:Invalidcredentials\",\"event.type\":\"metric.poll\",\"copy.required\":false}").put("metric.object", CommonUtil.getLong(object.getString(ID)));

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENRICHER, polledContext);

        TestUtil.vertx().setTimer(5000L, handler ->
                TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_POLLING_ERROR_NOTIFICATION_TEST, new JsonObject(), reply ->
                {

                    var pollingErrors = reply.result().body();

                    if (!pollingErrors.isEmpty())
                    {
                        Assertions.assertTrue(pollingErrors.containsKey(id.toString()));

                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow("Polling Error Notification sent for : " + id);
                    }

                }));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollingFailedTimestampAndDuration(VertxTestContext testContext)
    {
        try
        {

            var uuid = UUID.randomUUID().toString();

            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (message.body().getString(EVENT_TYPE) != null && message.body().getString(EVENT_TYPE).equals(UI_ACTION_OBJECT_POLLING_ERROR_QUERY) &&
                            !eventContext.isEmpty() && eventContext.containsKey(UI_EVENT_UUID) && eventContext.getString(UI_EVENT_UUID).equalsIgnoreCase(uuid)
                            && eventContext.getString(STATUS) != null && message.body().containsKey(EVENT_TYPE))
                    {
                        testContext.verify(() ->
                        {
                            var errors = eventContext.getJsonArray(RESULT);

                            for (var i = 0; i < errors.size(); i++)
                            {
                                var error = errors.getJsonObject(i);

                                if (error.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("***********"))
                                {
                                    Assertions.assertNotNull(error.getString(EVENT_TIMESTAMP));

                                    Assertions.assertTrue(CommonUtil.getLong(error.getString(DURATION)) > 0);

                                    messageConsumer.unregister(result -> testContext.completeNow());
                                }
                            }
                        });
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });

            Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_OBJECT_POLLING_ERROR_QUERY).put(SESSION_ID, TestUtil.getSessionId())
                    .put(EVENT_CONTEXT, new JsonObject().put(UI_EVENT_UUID, uuid).put(SESSION_ID, TestUtil.getSessionId())));
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }


    }
}
