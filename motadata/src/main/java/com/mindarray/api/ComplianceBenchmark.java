/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.ComplianceBenchmarkConfigStore;
import com.mindarray.store.CompliancePolicyConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class ComplianceBenchmark extends AbstractAPI
{
    public static final String COMPLIANCE_BENCHMARK_NAME = "compliance.benchmark.name";
    public static final String COMPLIANCE_BENCHMARK_TAGS = "compliance.benchmark.tags";
    public static final String COMPLIANCE_BENCHMARK_RULE_GROUPS = "compliance.benchmark.rule.groups";
    public static final String COMPLIANCE_BENCHMARK_RULES = "compliance.benchmark.rules";
    public static final String COMPLIANCE_BENCHMARK_RULE_IDS = "compliance.benchmark.rule.ids";
    private static final Logger LOGGER = new Logger(ComplianceBenchmark.class, MOTADATA_API, "Compliance Benchmark API");

    protected ComplianceBenchmark()
    {
        super("compliance-benchmarks", ComplianceBenchmarkConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var entities = new JsonArray();

            var items = CompliancePolicyConfigStore.getStore().getItems();

            if (!items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var entity = new JsonObject();

                    var item = items.getJsonObject(index);

                    if (item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).containsKey(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK) && item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getString(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK).equalsIgnoreCase(routingContext.request().getParam(ID)))
                    {
                        entity.put(CompliancePolicy.COMPLIANCE_POLICY_NAME, item.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME));

                        entity.put(CompliancePolicy.COMPLIANCE_POLICY_DESCRIPTION, item.getString(CompliancePolicy.COMPLIANCE_POLICY_DESCRIPTION));

                        entity.put(CompliancePolicy.COMPLIANCE_POLICY_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_TAGS)));

                        entities.add(entity);
                    }
                }

                if (!entities.isEmpty())
                {
                    response.put(APIConstants.Entity.COMPLIANCE_POLICY.getName(), entities);
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var benchmarks = ComplianceBenchmarkConfigStore.getStore().getItems();

            var policies = CompliancePolicyConfigStore.getStore().getItems();

            for (var i = 0; i < benchmarks.size(); i++)
            {
                var count = 0;

                var id = benchmarks.getJsonObject(i).getString(ID);

                for (var j = 0; j < policies.size(); j++)
                {
                    var policy = policies.getJsonObject(j);

                    if (policy.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).containsKey(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK) && policy.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getString(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK).equalsIgnoreCase(id))
                    {
                        count += 1;
                    }
                }

                response.put(id, count);
            }

            promise.complete(response);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_CREATE);
    }

    private Future<JsonObject> qualify(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        var requestBody = routingContext.body().asJsonObject();

        if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE) || requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE))
        {
            requestBody.put(ComplianceBenchmark.COMPLIANCE_BENCHMARK_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(ComplianceBenchmark.COMPLIANCE_BENCHMARK_TAGS), Tag.TagType.COMPLIANCE.getName(), DBConstants.ENTITY_TYPE_USER));

            promise.complete(requestBody);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_UPDATE);
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray items, RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                item.put(COMPLIANCE_BENCHMARK_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_BENCHMARK_TAGS)));

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            item = new JsonObject().mergeIn(item);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item.put(COMPLIANCE_BENCHMARK_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(COMPLIANCE_BENCHMARK_TAGS)))));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }
}
