{"entity": "Compliance Benchmark", "table": "tbl_config_compliance_benchmark", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "compliance.benchmark.name", "title": "Benchmark Name", "type": "string", "rules": ["required", "unique"]}, {"name": "compliance.benchmark.description", "title": "Description", "type": "string"}, {"name": "compliance.benchmark.tags", "title": "Tags", "type": "list"}, {"name": "compliance.benchmark.category", "title": "Category", "type": "string", "rules": ["required"], "values": ["Network"]}, {"name": "compliance.benchmark.rule.groups", "title": "Rule Groups", "type": "list", "rules": ["required"]}, {"name": "compliance.benchmark.rule.ids", "title": "Rule ids", "type": "list", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"compliance.benchmark.name": "CIS_Cisco_IOS_XE_16.x_Benchmark_v2.1.0", "compliance.benchmark.tags": [1682219345, -2082102542, 2113825355], "compliance.benchmark.category": "Network", "compliance.benchmark.rule.ids": [**************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, 10000000000029, 10000000000030, 10000000000031, 10000000000032, 10000000000033, 10000000000034, 10000000000035, 10000000000036, 10000000000037, 10000000000038, 10000000000047, 10000000000048, 10000000000049, 10000000000050, 10000000000046, 10000000000039, 10000000000040, 10000000000041, 10000000000042, 10000000000043, 10000000000044, 10000000000045, 10000000000051, 10000000000052, 10000000000053, 10000000000054, 10000000000055, 10000000000056, 10000000000057, 10000000000058, **************, **************, **************, **************, 10000000000059, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************], "compliance.benchmark.rule.groups": [{"compliance.benchmark.rule.name": "Management Plane", "compliance.benchmark.rules": [], "compliance.benchmark.parent": ""}, {"compliance.benchmark.rule.name": "Local Authentication, Authorization and Accounting (AAA) Rules", "compliance.benchmark.rules": [**************, **************, **************, **************, **************, **************, **************, **************, **************, **************], "compliance.benchmark.parent": "Management Plane"}, {"compliance.benchmark.rule.name": "Access Rules", "compliance.benchmark.rules": [**************, **************, **************, **************, **************, **************, **************, **************, **************, **************, **************], "compliance.benchmark.parent": "Management Plane"}, {"compliance.benchmark.rule.name": "Banner Rules", "compliance.benchmark.rules": [**************, **************, **************, **************], "compliance.benchmark.parent": "Management Plane"}, {"compliance.benchmark.rule.name": "Password Rules", "compliance.benchmark.rules": [**************, **************, **************], "compliance.benchmark.parent": "Management Plane"}, {"compliance.benchmark.rule.name": "SNMP Rules", "compliance.benchmark.rules": [10000000000029, 10000000000030, 10000000000031, 10000000000032, 10000000000033, 10000000000034, 10000000000035, 10000000000036, 10000000000037, 10000000000038], "compliance.benchmark.parent": "Management Plane"}, {"compliance.benchmark.rule.name": "Control Plane", "compliance.benchmark.rules": [], "compliance.benchmark.parent": ""}, {"compliance.benchmark.rule.name": "Global Service Rules", "compliance.benchmark.rules": [10000000000039, 10000000000040, 10000000000041, 10000000000042, 10000000000043, 10000000000044, 10000000000045], "compliance.benchmark.parent": "Control Plane"}, {"compliance.benchmark.rule.name": "Setup SSH", "compliance.benchmark.rules": [10000000000046], "compliance.benchmark.parent": "Global Service Rules"}, {"compliance.benchmark.rule.name": "Configure Prerequisites for the SSH Service", "compliance.benchmark.rules": [10000000000047, 10000000000048, 10000000000049, 10000000000050], "compliance.benchmark.parent": "Setup SSH"}, {"compliance.benchmark.rule.name": "Logging Rules", "compliance.benchmark.rules": [10000000000051, 10000000000052, 10000000000053, 10000000000054, 10000000000055, 10000000000056, 10000000000057, 10000000000058], "compliance.benchmark.parent": "Control Plane"}, {"compliance.benchmark.rule.name": "NTP Rules", "compliance.benchmark.rules": [10000000000059], "compliance.benchmark.parent": "Control Plane"}, {"compliance.benchmark.rule.name": "Require Encryption Keys for NTP", "compliance.benchmark.rules": [**************, **************, **************, **************], "compliance.benchmark.parent": "NTP Rules"}, {"compliance.benchmark.rule.name": "Loopback Rules", "compliance.benchmark.rules": [**************, **************, **************, **************], "compliance.benchmark.parent": "Control Plane"}, {"compliance.benchmark.rule.name": "Data Plane", "compliance.benchmark.rules": [], "compliance.benchmark.parent": ""}, {"compliance.benchmark.rule.name": "Routing Rules", "compliance.benchmark.rules": [**************, **************, **************, **************], "compliance.benchmark.parent": "Data Plane"}, {"compliance.benchmark.rule.name": "Border Router Filtering", "compliance.benchmark.rules": [**************, **************], "compliance.benchmark.parent": "Data Plane"}, {"compliance.benchmark.rule.name": "Neighbor Authentication", "compliance.benchmark.rules": [], "compliance.benchmark.parent": "Data Plane"}, {"compliance.benchmark.rule.name": "Require EIGRP Authentication if Protocol is Used", "compliance.benchmark.rules": [**************, **************, **************, **************, **************, **************, **************, **************, **************], "compliance.benchmark.parent": "Neighbor Authentication"}, {"compliance.benchmark.rule.name": "Require OSPF Authentication if Protocol is Used", "compliance.benchmark.rules": [**************, **************], "compliance.benchmark.parent": "Neighbor Authentication"}, {"compliance.benchmark.rule.name": "Require BGP Authentication if Protocol is Used", "compliance.benchmark.rules": [**************], "compliance.benchmark.parent": "Neighbor Authentication"}], "compliance.benchmark.description": "Please see the below link for CIS's current terms of use: https://www.cisecurity.org/cis-securesuite/cis-securesuite-membership-terms-of-use/", "id": **************}], "version": "1.5"}]}