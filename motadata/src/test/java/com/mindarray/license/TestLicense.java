/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.license;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Discovery;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.agent.AgentConstants.AGENT_RESTART_REQUIRED;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_OBJECT_PROVISION;
import static com.mindarray.eventbus.EventBusConstants.EVENT_REPLY;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestLicense
{

    private static final Logger LOGGER = new Logger(TestLicense.class, "license-test", "License Test");
    private static int agents = 0;
    private static long newLicense = 0;
    private static long totalObjects = 0;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            LOGGER.trace(String.format("Size of Object-Config-Store: %s, and Metric-Config-Store: %s", ObjectConfigStore.getStore().getItems().size(), MetricConfigStore.getStore().getItems().size()));

            LOGGER.trace(String.format("License details: %s", LicenseUtil.getLicenseDetails()));

            LOGGER.trace(String.format("Total consume License: %s", LicenseCacheStore.getStore().getConsumedLicenses()));

            LOGGER.trace(String.format("Remaining objects %s", LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS)));

            var vms = MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.VMS);

            LOGGER.info("vms: " + MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.VMS));

            var apps = MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.APPS);

            LOGGER.info("apps: " + MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.APPS));

            agents = AgentConfigStore.getStore().getProvisionedItems() - AgentConfigStore.getStore().getReservedMetricAgents();

            LOGGER.info("agents: " + (AgentConfigStore.getStore().getProvisionedItems() - AgentConfigStore.getStore().getReservedMetricAgents()));

            var objects = ObjectConfigStore.getStore().getProvisionedItems();

            LOGGER.info("objects: " + ObjectConfigStore.getStore().getProvisionedItems());

            var accessPoints = MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.ACCESS_POINTS);

            LOGGER.info("accessPoints: " + MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.ACCESS_POINTS));

            totalObjects = (objects +
                    vms +
                    apps +
                    agents) +
                    Math.round(Math.ceil(CommonUtil.getDouble(accessPoints) / LicenseUtil.InstanceLicense.ACCESS_POINT.getValue()));

            LOGGER.trace(String.format("objects= %s, vms= %s, apps= %s, agents= %s, accessPoints= %s", objects, vms, apps, agents, accessPoints));

            LicenseUtil.load(new JsonObject().put("license.expiry.time", LicenseUtil.class.getDeclaredField("LICENSE_EXPIRY_DATE").get(LicenseUtil.class)).put("license.edition", 2).put("monitoring.enabled", "yes").put("licensed.monitors", CommonUtil.getInteger(LicenseCacheStore.getStore().getConsumedLicenses() + 5))); //for testing, we are adjusting license

            newLicense = LicenseCacheStore.getStore().getConsumedLicenses() + 5;

            LOGGER.trace(String.format("after load remaining objects %s", LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS)));

            LOGGER.trace(String.format("after License details: %s", LicenseUtil.getLicenseDetails()));

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    private static Future<Void> assertProvisionTestResult(long discoveryId, Promise<Void> promise)
    {
        try
        {
            var discovery = DiscoveryConfigStore.getStore().getItem(discoveryId);

            Bootstrap.configDBService().getAll(DBConstants.TBL_DISCOVERY_RESULT + discovery.getLong(ID), asyncResult ->
            {
                try
                {
                    if (asyncResult.failed() || asyncResult.result().isEmpty())
                    {
                        promise.fail(String.format("failed to get discovery object %s", discovery.getString(Discovery.DISCOVERY_NAME)));
                    }
                    else
                    {
                        var futures = new ArrayList<Future<Void>>();

                        var results = asyncResult.result();

                        var errors = new StringBuilder(0);

                        if (discovery.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                        {
                            discovery.remove(ID);
                        }

                        for (var index = 0; index < results.size(); index++)
                        {
                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            var objectId = NOT_AVAILABLE;

                            var discoveryResult = results.getJsonObject(index);

                            var type = NMSConstants.Type.valueOfName(discoveryResult.getString(AIOpsObject.OBJECT_TYPE));

                            discoveryResult.mergeIn(discovery).put(SESSION_ID, TestUtil.getSessionId());

                            assertNotNull(type);

                            assertTrue(Arrays.asList(NMSConstants.Type.values()).contains(type));

                            if (type.toString().equals("PORT") || type.toString().equals("URL"))
                            {
                                var objects = ObjectConfigStore.getStore().flatItemsByMapValueField(type, AIOpsObject.OBJECT_IP, discoveryResult.getString(AIOpsObject.OBJECT_TARGET), AIOpsObject.OBJECT_CONTEXT, PORT, discoveryResult.getJsonObject(Discovery.DISCOVERY_CONTEXT).getInteger(PORT));

                                objectId = !objects.isEmpty() ? objects.getJsonObject(0).getInteger(AIOpsObject.OBJECT_ID) : objectId;
                            }
                            else
                            {
                                objectId = ObjectConfigStore.getStore().getObjectIdByTarget(discoveryResult.getString(AIOpsObject.OBJECT_TARGET), type);

                                if (objectId == NOT_AVAILABLE)
                                {
                                    objectId = ObjectConfigStore.getStore().getObjectIdByIP(discoveryResult.getString(AIOpsObject.OBJECT_IP), type);

                                    if (objectId == NOT_AVAILABLE)
                                    {
                                        objectId = ObjectConfigStore.getStore().getObjectIdByObjectName(discoveryResult.getString(AIOpsObject.OBJECT_NAME));
                                    }
                                }
                            }

                            if (objectId != NOT_AVAILABLE)
                            {
                                errors.append("Object ").append(discoveryResult.getString(AIOpsObject.OBJECT_TARGET)).append(" is already provisioned").append(SEPARATOR);

                                future.fail(errors.toString());
                            }
                            else
                            {
                                LOGGER.trace(String.format("Request send for object provision: %s", discoveryResult.getString(AIOpsObject.OBJECT_IP)));

                                TestUtil.vertx().eventBus().<JsonObject>request(EVENT_OBJECT_PROVISION, discoveryResult.put(EVENT_REPLY, YES),
                                        new DeliveryOptions().setSendTimeout(300000L), reply ->
                                        {
                                            try
                                            {
                                                if (reply.failed())
                                                {
                                                    future.fail(reply.cause());
                                                }
                                                else
                                                {
                                                    var result = reply.result().body();

                                                    Assertions.assertNotNull(result);

                                                    Assertions.assertTrue(result.containsKey(STATUS));

                                                    Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                                                    LOGGER.info(String.format("%s : %s object provisioned successfully", result.getString(AIOpsObject.OBJECT_IP), result.getString(AIOpsObject.OBJECT_NAME)));

                                                    future.complete();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                future.fail(exception);
                                            }

                                        });
                            }
                        }

                        Future.all(futures).onComplete(result ->
                        {

                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause());
                            }

                        });

                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            });
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            LOGGER.error(exception);
        }

        return promise.future();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        try
        {
            LicenseUtil.load(new JsonObject().put("license.expiry.time", LicenseUtil.class.getDeclaredField("LICENSE_EXPIRY_DATE").get(LicenseUtil.class)).put("license.edition", 2).put("monitoring.enabled", "yes").put("licensed.monitors", 2000));

            LOGGER.trace(String.format("After all remaining objects :%s", LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS)));

            var objects = ObjectConfigStore.getStore().flatItems(ID);

            TestAPIUtil.put(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.PING.getName()).getLong(ID) + "/unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(response ->
                    testContext.verify(() ->
                    {
                        assertEquals(String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()), response.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                        assertEquals(HttpStatus.SC_OK, response.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                        TestAPIUtil.put(RUNBOOK_PLUGIN_API_ENDPOINT + "/" + RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.TRACE_ROUTE.getName()).getLong(ID) + "/unassign", new JsonObject().put(APIConstants.REQUEST_PARAM_IDS, objects), testContext.succeeding(result ->
                                testContext.verify(() ->
                                {
                                    assertEquals(String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.RUNBOOK_PLUGIN.getName()), result.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                    assertEquals(HttpStatus.SC_OK, result.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                    TestAPIUtil.deleteAll(OBJECT_API_ENDPOINT, new JsonObject()
                                            .put(REQUEST_PARAM_IDS, objects), testContext.succeeding(asyncResult -> testContext.verify(() ->
                                    {
                                        assertEquals(InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED, asyncResult.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                        assertEquals(SC_OK, asyncResult.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                        testContext.completeNow();
                                    })));
                                })));
                    })));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(10000)
    public void testValidateLicense(VertxTestContext testContext)
    {
        try
        {
            var field = LicenseCacheStore.class.getDeclaredField("provisionedObjects");

            field.setAccessible(true);

            var provisionedObjects = (AtomicInteger) field.get(LicenseCacheStore.getStore());

            LOGGER.trace(String.format("After update cacheLicense is: %s", provisionedObjects));

            Assertions.assertEquals(totalObjects, provisionedObjects.get());

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id -> testContext.completeNow());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testDisableAgentMetricStatus(VertxTestContext testContext)
    {
        var cipherUtil = new CipherUtil();

        try (var producer = Bootstrap.zcontext().socket(SocketType.PUSH))
        {
            producer.connect("tcp://*:" + CommonUtil.getEventSubscriberPort());

            var context = new JsonObject().put("object.ip", "************")
                    .put("agent.version", MotadataConfigUtil.getVersion())
                    .put("agent.os.name", "linux")
                    .put("objects", new JsonArray().add("************"))
                    .put("agent.id", "WEFGHI")
                    .put("event.type", "agent.registration")
                    .put("object.host", "agent-test")
                    .put("agent.configs", new JsonObject().put("agent", new JsonObject()
                            .put("agent.id", "WEFGHI")
                            .put("agent.deactivation.status", "no")
                            .put("metric.agent.status", "no")
                            .put("agent.state", "ENABLE")));

            producer.send(Buffer.buffer().appendShortLE(CommonUtil.getShort(EventBusConstants.AGENT_TOPIC.length())).appendString(EventBusConstants.AGENT_TOPIC).appendBytes(cipherUtil.encrypt(CodecUtil.compress(context.encode()))).getBytes());

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
            {
                try
                {
                    var field = LicenseCacheStore.class.getDeclaredField("provisionedObjects");

                    field.setAccessible(true);

                    var provisionedObjects = (AtomicInteger) field.get(LicenseCacheStore.getStore());

                    LOGGER.trace(String.format("after metric disable status: %s", provisionedObjects));

                    Assertions.assertNotNull(AgentConfigStore.getStore().getItems(), "Agent should be registered");

                    if (AgentConfigStore.getStore().getItems() != null)
                    {
                        assertFalse(AgentConfigStore.getStore().getItems().isEmpty());
                    }

                    Assertions.assertEquals(agents, provisionedObjects.get(), "Due to disable metric status, License should not be consumed");

                    testContext.completeNow();
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    public void testEnableAgentMetricStatus(VertxTestContext testContext)
    {
        try
        {
            var payload = new JsonObject().put("agent.configs", new JsonObject().put("agent", new JsonObject()
                    .put("agent.id", "")
                    .put("agent.deactivation.status", "no")
                    .put("metric.agent.status", "yes")
                    .put("agent.state", "ENABLE")).encode()).put(AGENT_RESTART_REQUIRED, YES);

            TestAPIUtil.put(AGENT_API_ENDPOINT + "/" + AgentConfigStore.getStore().getIds().getLong(0) + "/configure", payload, testContext.succeeding(response -> testContext.verify(() ->
            {

                var field = LicenseCacheStore.class.getDeclaredField("provisionedObjects");

                field.setAccessible(true);

                var provisionedObjects = (AtomicInteger) field.get(LicenseCacheStore.getStore());

                LOGGER.trace(String.format("after metric enable status: %s", provisionedObjects));

                Assertions.assertEquals(agents + 1, provisionedObjects.get(), "License should be consumed, after enabled metric status");

                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id -> testContext.completeNow());

            })));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    public void testRemainingObjects(VertxTestContext testContext)
    {
        LOGGER.trace(String.format("Remaining Objects: %s", LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS)));

        LOGGER.trace(String.format("Remaining Access points: %s", LicenseUtil.getRemainingObjects(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName())));

        Assertions.assertEquals(newLicense - (totalObjects + 1), LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS));

        Assertions.assertEquals(LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS) * LicenseUtil.InstanceLicense.ACCESS_POINT.getValue(), LicenseUtil.getRemainingObjects(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName()), "Remaining count for Access point should be greater ");

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    public void testMultipleProvision(VertxTestContext testContext) throws InterruptedException
    {
        var items = DiscoveryConfigStore.getStore().getItemByValue("discovery.target", "************");

        assertProvisionTestResult(items.getLong(ID), Promise.promise()).onComplete(result ->
        {

            try
            {
                if (result.succeeded())
                {
                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id -> testContext.completeNow());
                }
                else
                {
                    testContext.failNow("unable to provision");
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    public void testValidateLicenseAfterProvision(VertxTestContext testContext)
    {
        try
        {
            var field = LicenseCacheStore.class.getDeclaredField("provisionedObjects");

            field.setAccessible(true);

            var provisionedObjects = (AtomicInteger) field.get(LicenseCacheStore.getStore());

            LOGGER.trace(String.format("After Provision %s", provisionedObjects));

            LOGGER.trace(String.format("After Provision Consumed license %s", LicenseCacheStore.getStore().getConsumedLicenses()));

            LOGGER.trace(String.format("After Provision Remaining license %s", LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS)));

            Assertions.assertEquals(newLicense, LicenseCacheStore.getStore().getConsumedLicenses(), "After provision, consume license value should not be greater than license");

            Assertions.assertEquals(0, LicenseUtil.getRemainingObjects(NMSConstants.OBJECTS));

            testContext.completeNow();

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

}
