[{"_type": "0", "id": 10000000000129, "visualization.name": "Request", "visualization.description": "EBS Request", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.application.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.application.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Request", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elasticbeanstalk.application.requests.last"}]}}}}, {"_type": "0", "id": 10000000000130, "visualization.name": "Running", "visualization.description": "EBS Running", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.launch.time.days", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.launch.time.days"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.launch.time.days", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "Running", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elasticbeanstalk.launch.time.days.last"}]}}}}, {"_type": "0", "id": 10000000000131, "visualization.name": "HTTP Response", "visualization.description": "EBS HTTP Response", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.2xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.4xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.application.2xx.requests"}, {"type": "metric", "data.point": "aws.elasticbeanstalk.application.4xx.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.application.2xx.requests", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "HTTP Response", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "2xx", "value": "aws.elasticbeanstalk.application.2xx.requests.last"}, {"label": "4xx", "value": "aws.elasticbeanstalk.application.4xx.requests.last"}]}}}}, {"_type": "0", "id": 10000000000132, "visualization.name": "P99.9 Latency", "visualization.description": "EBS P99.9 Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.p999.latency.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.application.p999.latency.seconds"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.application.p999.latency.seconds", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "P99.9 Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elasticbeanstalk.application.p999.latency.seconds.last"}]}}}}, {"_type": "0", "id": 10000000000133, "visualization.name": "CPU", "visualization.description": "EC2 CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.cpu.idle.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.cpu.io.wait.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.cpu.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.cpu.idle.percent"}, {"type": "metric", "data.point": "aws.elasticbeanstalk.cpu.io.wait.percent"}, {"type": "metric", "data.point": "aws.elasticbeanstalk.cpu.user.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.cpu.idle.percent.last", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "Idle", "value": "aws.elasticbeanstalk.cpu.idle.percent.last"}, {"label": "IO Wait", "value": "aws.elasticbeanstalk.cpu.io.wait.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elasticbeanstalk.cpu.user.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000134, "visualization.name": "Load", "visualization.description": "EBS Load", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.load.avg1.min", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.load.avg5.min", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elasticbeanstalk.load.avg1.min"}, {"type": "metric", "data.point": "aws.elasticbeanstalk.load.avg5.min"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elasticbeanstalk.load.avg5.min", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Load", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "1 m", "value": "aws.elasticbeanstalk.load.avg1.min.last"}, {"label": "5 m", "value": "aws.elasticbeanstalk.load.avg5.min.last"}]}}}}, {"id": 10000000000135, "visualization.name": "CPU Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor"}, {"data.point": "aws.elasticbeanstalk.cpu.user.percent", "aggregator": "avg", "entity.type": "monitor"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000000136, "visualization.name": "Load", "visualization.description": "EBS Load", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.load.avg1.min", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.load.avg5.min", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000137, "visualization.name": "Request Details", "visualization.granularity": "1 h", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.requests", "aggregator": "sum", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": "1", "unit": "h"}}, {"id": 10000000000138, "visualization.name": "Http Request", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.2xx.requests", "aggregator": "last", "entity.type": "monitor"}, {"data.point": "aws.elasticbeanstalk.application.3xx.requests", "aggregator": "last", "entity.type": "monitor"}, {"data.point": "aws.elasticbeanstalk.application.4xx.requests", "aggregator": "last", "entity.type": "monitor"}, {"data.point": "aws.elasticbeanstalk.application.5xx.requests", "aggregator": "last", "entity.type": "monitor"}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "yes", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.elasticbeanstalk.application.5xx.requests.last"}}}, "granularity": {"value": "1", "unit": "h"}}, {"id": 10000000000139, "visualization.name": "Average Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elasticbeanstalk.application.p99.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p10.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p50.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p85.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p75.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p90.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p95.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elasticbeanstalk.application.p999.latency.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "yes", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.elasticbeanstalk.application.p75.latency.seconds.avg"}}}, "granularity": {"value": "1", "unit": "h"}}]