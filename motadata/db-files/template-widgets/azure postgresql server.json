[{"_type": "0", "id": 10000000002172, "visualization.name": "IO Percent", "visualization.description": "Azure PostgreSQL IO Percent", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.io.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.io.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "IO Percent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.io.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "rtt"}, "color.data.point": "azure.postgresql.server.io.percent"}}}}, {"_type": "0", "id": 10000000002173, "visualization.name": "Memory", "visualization.description": "Azure PostgreSQL Memory Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.memory.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.memory.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.memory.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002174, "visualization.name": "CPU", "visualization.description": "Azure PostgreSQL CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002175, "visualization.name": "Connections", "visualization.description": "Azure PostgreSQL Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.failed.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.active.connections"}, {"type": "metric", "data.point": "azure.postgresql.server.failed.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Active Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.active.connections.last"}]}}}}, {"_type": "0", "id": 10000000002176, "visualization.name": "Storage", "visualization.description": "Azure PostgreSQL Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.storage.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.storage.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.storage.used.percent", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.storage.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002177, "visualization.name": "Network", "visualization.description": "Azure PostgreSQL Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.network.in.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.network.out.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.postgresql.server.network.in.bytes"}, {"type": "metric", "data.point": "azure.postgresql.server.network.out.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.network.in.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "IN", "value": "azure.postgresql.server.network.in.bytes.last"}, {"label": "OUT", "value": "azure.postgresql.server.network.out.bytes.last"}]}}}}, {"_type": "0", "id": 10000000002178, "visualization.name": "CPU", "visualization.description": "Azure PostgreSQL CPU", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.cpu.percent.avg"}}}}, {"_type": "0", "id": 10000000002179, "visualization.name": "Network Details", "visualization.description": "Azure PostgreSQL Network Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.network.in.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.network.out.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002180, "visualization.name": "Connection Details", "visualization.description": "Azure PostgreSQL Connection Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002181, "visualization.name": "Replication Lag", "visualization.description": "Azure PostgreSQL Replication Lag", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.replica.lag.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002182, "visualization.name": "Server Storage Utilization", "visualization.description": "Azure PostgreSQL Server Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002183, "visualization.name": "Server Storage Utilization", "visualization.description": "Azure PostgreSQL Server Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002184, "visualization.name": "Log Storage Utilization", "visualization.description": "Azure PostgreSQL Log Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.log.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.log.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002185, "visualization.name": "Log Storage Utilization", "visualization.description": "Azure PostgreSQL Log Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.log.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]