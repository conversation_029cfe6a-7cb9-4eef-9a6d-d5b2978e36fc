/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/*
This class is required when all test cases in one suite is ignored with @org.junit.jupiter.api.condition.EnabledIfSystemProperty, the suite fails with,
@org.junit.platform.suite.engine.NoTestsDiscoveredException

this issue occurs in case of @io.github.artsok.RepeatedIfExceptionsTest only
https://github.com/artsok/rerunner-jupiter/issues/72
 */
@ExtendWith(VertxExtension.class)
public class TestInit
{
    private static final Logger LOGGER = new Logger(TestInit.class, "test-util", "Test Init");

    @Test
    void testInitSuite(VertxTestContext testContext) throws IOException
    {

        System.out.println("Free memory (bytes): " +
                Runtime.getRuntime().freeMemory());

        var maxMemory = Runtime.getRuntime().maxMemory();

        System.out.println("Maximum memory (bytes): " +
                (maxMemory == Long.MAX_VALUE ? "no limit" : maxMemory));

        System.out.println("Total memory available to JVM (bytes): " +
                Runtime.getRuntime().totalMemory());

        Runtime.getRuntime().gc();

        LOGGER.trace("Current Timestamp : " + DateTimeUtil.timestamp());

        // Define the source and destination file paths
        Path sourcePath = Paths.get(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "test-plugin-metrics" + GlobalConstants.PATH_SEPARATOR + "/report-engine");

        Path destinationPath = Paths.get(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "/report-engine");

        Files.copy(sourcePath, destinationPath, StandardCopyOption.REPLACE_EXISTING);

        testContext.completeNow();
    }
}
