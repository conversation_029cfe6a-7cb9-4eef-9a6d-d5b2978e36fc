/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  21-Mar-2025     Vismit              MOTADATA-5094: Added a case to handle CONFIG_UPGRADE_OPERATION
 *  19-Mar-2025     <PERSON><PERSON> Sharma        Added Status Flap Dump for Job Scheduler
 *  24-Mar-2025     Chandresh           MOTADATA-5426: Docker discovery and polling support added
 *  2-Jun-2025      Smit Prajapati      MOTADATA-6418: EventPolicyInspector/EventPolicyAggregator Support
 *  23-Jun-2025     Vismit              MOTADATA-6312: Updated scheduler flow to support one scheduler on bulk devices
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.util.List;
import java.util.Objects;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.BackupProfile.BACKUP_PROFILE_TYPE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * The CustomJob class is responsible for executing user-defined jobs in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and handles the execution of various types of
 * scheduled jobs based on their configuration. It processes different job types differently,
 * dispatching them to the appropriate handlers via the event bus.
 * <p>
 * The job execution flow is as follows:
 * <ol>
 *   <li>Retrieve job context from the job execution context</li>
 *   <li>Load the scheduler configuration from the store</li>
 *   <li>Prepare the job context by merging configuration and job data</li>
 *   <li>Check if the job is enabled and not already running</li>
 *   <li>Execute the job based on its type</li>
 * </ol>
 * <p>
 * The class handles various job types including database compaction, license quota reset,
 * system log retention, discovery, maintenance, runbook execution, topology, rediscovery,
 * policy operations, reports, database backups, and compliance policy checks.
 */
public class CustomJob implements Job
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(CustomJob.class, GlobalConstants.MOTADATA_JOB, "Custom Job");

    /**
     * Executes the job when triggered by the scheduler.
     * <p>
     * This method is called by the Quartz scheduler when a job is triggered. It retrieves
     * the job configuration from the scheduler store, prepares the execution context,
     * and dispatches the job to the appropriate handler based on its type.
     *
     * @param jobExecutionContext the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext)
    {
        try
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug("executing custom scheduler...");
            }

            // Get job data from the execution context
            var jobContext = jobExecutionContext.getJobDetail().getJobDataMap();

            if (jobContext.containsKey(ID))
            {
                // Remove one-time triggered jobs after execution
                if (jobContext.containsKey("job.type"))
                {
                    JobScheduler.removeJob(jobExecutionContext.getJobDetail().getKey());
                }

                // Get the scheduler configuration from the store
                var scheduler = SchedulerConfigStore.getStore().getItem(jobContext.getLong(ID));

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("executing custom scheduler for job " + scheduler.getString(Scheduler.SCHEDULER_JOB_TYPE));
                }

                // Remove any previous results
                scheduler.remove(RESULT);

                // Add session ID if available (for UI notifications)
                if (jobContext.containsKey(APIConstants.SESSION_ID))
                {
                    scheduler.put(APIConstants.SESSION_ID, jobContext.getString(APIConstants.SESSION_ID));
                }

                var jobType = scheduler.getString(Scheduler.SCHEDULER_JOB_TYPE);

                // Merge scheduler context into the main configuration
                if (scheduler.containsKey(Scheduler.SCHEDULER_CONTEXT))
                {
                    scheduler.mergeIn(scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT));
                    scheduler.remove(Scheduler.SCHEDULER_CONTEXT);
                }

                // Add scheduler ID and user information
                scheduler.put(EventBusConstants.EVENT_SCHEDULER, scheduler.getLong(ID));
                scheduler.put(USER_NAME, jobContext.containsKey(USERNAME) ? jobContext.getString(USERNAME) : DEFAULT_USER);
                scheduler.put(REMOTE_ADDRESS, jobContext.containsKey(REMOTE_ADDRESS) ? jobContext.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS);

                // Check if the scheduler is enabled
                if (scheduler.getString(Scheduler.SCHEDULER_STATE).equalsIgnoreCase(GlobalConstants.YES))
                {
                    // Check if the job is already running
                    if (SchedulerCacheStore.getStore().getSchedulerPendingProbes(scheduler.getLong(GlobalConstants.ID)) != 0)
                    {
                        LOGGER.warn(String.format("failed to execute scheduler for job type %s , reason : already running", jobType.equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName())
                                ? scheduler.getString(NMSConstants.REDISCOVER_JOB) : jobType));

                        // Send error notification for rediscover jobs
                        if (scheduler.getString(APIConstants.SESSION_ID) != null && jobType.equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName()))
                        {
                            // We are not broadcasting rediscover start event so this scenario might be created by other user
                            publish(scheduler.getString(APIConstants.SESSION_ID),
                                    UI_NOTIFICATION_REDISCOVER_ERROR,
                                    new JsonObject().put(MESSAGE, ErrorMessageConstants.REDISCOVERY_RUN_FAILED_ALREADY_RUNNING));
                        }
                    }
                    else
                    {
                        switch (JobScheduler.JobType.valueOfName(jobType))
                        {
                            case CONFIG_DB_COMPACT ->
                            {
                                LOGGER.info("config db compact started successfully....");

                                Bootstrap.vertx().eventBus().send(EVENT_DB_COMPACT, new JsonObject());
                            }

                            case LICENSE_QUOTA_RESET ->
                            {
                                LOGGER.info("resetting log/flow traffic usage quota....");

                                LicenseUtil.resetUsedLogQuota();

                                LicenseUtil.resetUsedFlowQuota();
                            }

                            case SYSTEM_LOG_RETENTION ->
                            {
                                LOGGER.info("system log retention started successfully....");

                                Bootstrap.configDBService().getOne(DBConstants.TBL_SYSTEM,
                                        result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                LogUtil.runLogRetention(result.result().getInteger(SYSTEM_LOG_RETENTION_DAYS));
                                            }
                                            else
                                            {
                                                LOGGER.error(result.cause());
                                            }
                                        });
                            }

                            case DISCOVERY ->
                            {
                                var discoveryId = scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0);

                                if (!DiscoveryCacheStore.getStore().discoveryRunning(discoveryId))
                                {
                                    SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                    DiscoveryCacheStore.getStore().startDiscovery(discoveryId);

                                    DiscoveryConfigStore.getStore().markDiscoveryStatusAsRunning(discoveryId);

                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DISCOVERY_RUN, new JsonObject().put(EventBusConstants.EVENT_SCHEDULER, scheduler.getLong(ID)).put(ID, discoveryId)
                                            .put(USER_NAME, scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER));
                                }

                                else
                                {

                                    LOGGER.warn(String.format(ErrorMessageConstants.DISCOVERY_RUN_FAILED_ALREADY_RUNNING, DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId)));
                                }
                            }

                            case MAINTENANCE ->
                            {
                                SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                for (var index = 0; index < scheduler.getJsonArray(NMSConstants.OBJECTS).size(); index++)
                                {
                                    var id = scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(index);

                                    Bootstrap.vertx().eventBus().request(jobContext.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.MAINTENANCE.name()) ? EventBusConstants.EVENT_OBJECT_MAINTENANCE : EventBusConstants.EVENT_OBJECT_ENABLE,
                                            scheduler.put(AIOpsObject.OBJECT_STATE, jobContext.getString(AIOpsObject.OBJECT_STATE)).put(ID, id),
                                            result -> Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_SCHEDULER_COMPLETE, new JsonObject().put(EVENT_SCHEDULER, jobContext.getLong(ID)).put(ID, id)));
                                }
                            }

                            case RUNBOOK ->
                            {
                                var runbook = RunbookPluginConfigStore.getStore().getItem(scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0));

                                if (runbook != null)
                                {
                                    if (runbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT))
                                    {
                                        runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                                        runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);
                                    }

                                    SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, scheduler.mergeIn(runbook));
                                }
                                else
                                {
                                    LOGGER.warn(String.format("failed to run runbook scheduler %s, reason: items not found in store", scheduler.getLong(ID)));
                                }
                            }

                            case TOPOLOGY ->
                            {
                                if (!TopologyCacheStore.getStore().topologyRunning(scheduler.getLong(ID)))
                                {
                                    SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                    TopologyCacheStore.getStore().startTopology(scheduler.getLong(ID));

                                    EventBusConstants.publish(EventBusConstants.UI_ACTION_TOPOLOGY_START, scheduler);

                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_TOPOLOGY_RUN, scheduler);
                                }

                                else
                                {
                                    LOGGER.warn(String.format("failed to run topology, reason: topology is already running for scheduler %s", scheduler.getLong(ID)));
                                }
                            }

                            case REDISCOVER ->
                                    rediscover(scheduler.put(APIConstants.SESSION_ID, jobContext.getString(APIConstants.SESSION_ID)));

                            case POLICY_SUPPRESSION ->
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, scheduler.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UNSUPPRESS_POLICY.name()));

                            case REPORT ->
                                    Bootstrap.vertx().eventBus().send(EVENT_REPORT, new JsonObject().put(ID, scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0)).put(Scheduler.SCHEDULER_CONTEXT, scheduler));

                            case POLICY_ACTION_TRIGGER_DISABLE ->
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, scheduler.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DISABLE_POLICY_ACTION_TRIGGER.name()));

                            case EVENT_POLICY ->
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_EVENT_POLICY_INSPECT, scheduler.put("schedule", YES).put(PolicyEngineConstants.POLICY_ID, scheduler.getLong(ID)));

                            case CONFIG_BACKUP_OPERATION, CONFIG_UPGRADE_OPERATION ->
                            {
                                if (scheduler.containsKey(NMSConstants.OBJECTS) && !MotadataConfigUtil.devMode())
                                {
                                    Bootstrap.vertx().eventBus().send(jobType.equalsIgnoreCase(JobScheduler.JobType.CONFIG_BACKUP_OPERATION.getName()) ? EventBusConstants.EVENT_CONFIG_BACKUP : EVENT_CONFIG_UPGRADE, scheduler);
                                }
                                else
                                {
                                    LOGGER.warn("Not able to schedule config job as, objects are not present : " + scheduler.getString(Scheduler.SCHEDULER_JOB_TYPE));
                                }
                            }

                            case DATABASE_BACKUP ->
                            {
                                var promise = Promise.<JsonObject>promise();

                                var item = BackupProfileConfigStore.getStore().getItem(scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0));

                                var tick = DateTimeUtil.currentMilliSeconds();

                                var context = new JsonObject().mergeIn(item)
                                        .put(EVENT_SCHEDULER, scheduler.getLong(ID))
                                        .put(DBConstants.BACKUP_START_TIME, tick);

                                SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                EventBusConstants.publish(UI_ACTION_BACKUP_START, scheduler);

                                LOGGER.info(String.format("%s backup started ....", item.getString(BACKUP_PROFILE_TYPE)));

                                if (item.getString(BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName()))
                                {
                                    backupDB(context).onComplete(result ->
                                    {
                                        try
                                        {
                                            if (result.succeeded())
                                            {
                                                promise.complete(result.result());
                                            }
                                            else
                                            {
                                                promise.fail(result.cause());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);
                                        }
                                    });
                                }
                                else
                                {
                                    promise.complete(context);
                                }

                                promise.future().onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        var event = new JsonObject().mergeIn(result.result());

                                        LOGGER.info(String.format("%s backup completed ", event.getString(BACKUP_PROFILE_TYPE)));

                                        var storageProfile = StorageProfileConfigStore.getStore().getItem(event.getLong(BackupProfile.BACKUP_STORAGE_PROFILE));

                                        var protocol = storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL);

                                        storageProfile.mergeIn(storageProfile.getJsonObject(StorageProfile.STORAGE_PROFILE_CONTEXT));

                                        storageProfile.remove(StorageProfile.STORAGE_PROFILE_CONTEXT);

                                        event.mergeIn(storageProfile).mergeIn(scheduler);

                                        if (MotadataConfigUtil.devMode())
                                        {
                                            EventBusConstants.publish(UI_ACTION_BACKUP_STOP, event);
                                        }

                                        if (!protocol.equalsIgnoreCase(StorageProfile.StorageProtocol.LOCAL.getName()))
                                        {
                                            var id = Runbook.getStorageProfileRunbookId(StorageProfile.StorageProtocol.valueOfName(storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL)));

                                            if (id != DUMMY_ID)
                                            {
                                                var runbook = RunbookPluginConfigStore.getStore().getItem(id);

                                                runbook.mergeIn(runbook.getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT));

                                                runbook.remove(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

                                                event.mergeIn(runbook);
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Invalid Protocol for runbook %s", storageProfile.getString(StorageProfile.STORAGE_PROFILE_PROTOCOL)));
                                            }
                                        }

                                        event.put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(DELETE_SRC_FILE, YES)
                                                .put(SYSTEM_BOOTSTRAP_TYPE, MotadataConfigUtil.getSystemBootstrapType());

                                        if (event.getString(BACKUP_PROFILE_TYPE).equalsIgnoreCase(BackupProfile.BackupProfileType.CONFIG_DB.getName()))
                                        {
                                            event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId());
                                        }
                                        else
                                        {
                                            var id = RemoteEventProcessorConfigStore.getStore().getItemByMode(InstallationMode.valueOf(Bootstrap.getInstallationMode()));

                                            if (id != null)
                                            {
                                                event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID));
                                            }
                                        }

                                        LOGGER.info(String.format("sending event %s for backup", CommonUtil.removeSensitiveFields(event, false)));

                                        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, event);
                                    }
                                    else
                                    {
                                        LOGGER.info(String.format("Config DB backup failed , reason : %s ", result.cause()));

                                        Bootstrap.vertx().eventBus().send(EVENT_MANAGER_RESPONSE_PROCESSOR, new JsonObject().put(BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName()).put(EVENT_TYPE, EVENT_DATABASE_BACKUP)
                                                .put(STATUS, STATUS_FAIL).put(DBConstants.BACKUP_START_TIME, tick).put(DBConstants.BACKUP_END_TIME, DateTimeUtil.currentMilliSeconds())
                                                .put(ERRORS, new JsonArray().add(new JsonObject()
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(ERROR, result.cause().getMessage()))));
                                    }
                                });

                            }

                            case COMPLIANCE_POLICY ->
                            {
                                if (scheduler.containsKey(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES) && !scheduler.getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES).isEmpty() && !MotadataConfigUtil.devMode())
                                {
                                    SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.getString(USER_NAME));

                                    Bootstrap.vertx().eventBus().send(EVENT_COMPLIANCE_MANAGER, scheduler);
                                }
                                else if (scheduler.containsKey(CompliancePolicy.COMPLIANCE_POLICY_JOB) && !MotadataConfigUtil.devMode())//in case every night we dump data in motastore to get historical aggregate of compliance percent
                                {
                                    Bootstrap.vertx().eventBus().send(EVENT_COMPLIANCE_POLICY_DATASTORE_DUMP, scheduler);
                                }
                                else
                                {
                                    LOGGER.warn("Not able to schedule compliance policy job as, objects are not present : " + scheduler.getString(Scheduler.SCHEDULER_JOB_TYPE));
                                }
                            }

                            default ->
                            {
                                // do nothing
                            }
                        }

                        LOGGER.info(String.format("completing custom scheduler for job type %s", jobType.equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName())
                                ? scheduler.getString(NMSConstants.REDISCOVER_JOB) : jobType));
                    }
                }
                else
                {
                    LOGGER.warn(String.format("Failed to execute scheduler for job type %s , reason : scheduler state is disabled", jobType.equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName())
                            ? scheduler.getString(NMSConstants.REDISCOVER_JOB) : jobType));

                    if (scheduler.getString(APIConstants.SESSION_ID) != null && jobType.equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName()))
                    {
                        //we are not broadcasting rediscover start event so this scenario might be created by some other user

                        publish(scheduler.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_REDISCOVER_ERROR,
                                new JsonObject().put(MESSAGE, String.format("Failed to execute scheduler for job type %s , reason : scheduler state is disabled", jobType)));
                    }
                    else if (scheduler.getString(APIConstants.SESSION_ID) != null && jobType.equalsIgnoreCase(JobScheduler.JobType.TOPOLOGY.getName()))
                    {
                        //we are not broadcasting topology start event so this scenario might be created by some other user

                        publish(scheduler.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_TOPOLOGY_ERROR,
                                new JsonObject().put(MESSAGE, String.format("Failed to execute scheduler for job type %s , reason : scheduler state is disabled", jobType)));

                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Handles rediscovery jobs based on the job type.
     * <p>
     * This method processes different types of rediscovery jobs, such as network interface
     * rediscovery, access point rediscovery, virtual machine rediscovery, etc. It retrieves
     * the appropriate metrics from the store and sends them for rediscovery.
     *
     * @param scheduler the JSON object containing rediscovery job configuration
     */
    private void rediscover(JsonObject scheduler)
    {
        try
        {
            var rediscoverJob = scheduler.getString(NMSConstants.REDISCOVER_JOB);

            // Handle different types of rediscovery jobs
            switch (NMSConstants.RediscoverJob.valueOfName(rediscoverJob))
            {
                case NETWORK_INTERFACE ->
                        send(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()),
                                scheduler.put(MESSAGE, ErrorMessageConstants.NETWORK_OBJECT_NOT_FOUND));
                case ACCESS_POINT ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.CISCO_WIRELESS_ACCESS_POINT.getName())
                                .add(NMSConstants.MetricPlugin.ARUBA_WIRELESS_ACCESS_POINT.getName())
                                .add(NMSConstants.MetricPlugin.RUCKUS_WIRELESS_ACCESS_POINT.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.NETWORK_OBJECT_NOT_FOUND));
                case VIRTUAL_MACHINE ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.HYPER_V_VM.getName())
                                .add(NMSConstants.MetricPlugin.VMWARE_ESXI_VM.getName())
                                .add(NMSConstants.MetricPlugin.CITRIX_XEN_VM.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.VIRTUAL_MACHINE_OBJECT_NOT_FOUND));

                case VIRTUAL_MACHINE_HCI ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.NUTANIX_VM.getName())), scheduler.put(MESSAGE, String.format("No %s type monitor found", NMSConstants.Type.NUTANIX.getName())));

                case NETWORK_SERVICE ->
                        send(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()),
                                scheduler.put(MESSAGE, ErrorMessageConstants.VIRTUAL_MACHINE_OBJECT_NOT_FOUND));

                case FILE_DIRECTORY ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.WINDOWS_DIR.getName())
                                .add(NMSConstants.MetricPlugin.WINDOWS_FILE.getName())
                                .add(NMSConstants.MetricPlugin.LINUX_DIR.getName())
                                .add(NMSConstants.MetricPlugin.LINUX_FILE.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.SERVER_OBJECT_NOT_FOUND));

                case PROCESS ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName())
                                .add(NMSConstants.MetricPlugin.LINUX_PROCESS.getName())
                                .add(NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName())
                                .add(NMSConstants.MetricPlugin.HP_UX_PROCESS.getName())
                                .add(NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.SERVER_OBJECT_NOT_FOUND));

                case WINDOWS_SERVICE ->
                        send(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName())
                                , scheduler.put(MESSAGE, ErrorMessageConstants.WINDOWS_SERVER_OBJECT_NOT_FOUND));

                case CLOUD ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.AWS_CLOUD.getName())
                                .add(NMSConstants.MetricPlugin.AZURE_CLOUD.getName())
                                .add(NMSConstants.MetricPlugin.OFFICE_365.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.CLOUD_OBJECT_NOT_FOUND));

                case CONTAINER ->
                        send(MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.DOCKER_CONTAINER.getName())), scheduler.put(MESSAGE, ErrorMessageConstants.CONTAINER_OBJECT_NOT_FOUND));

                case APP ->
                {
                    var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_PLUGIN, new JsonArray().add(NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName())
                            .add(NMSConstants.MetricPlugin.LINUX_PROCESS.getName())
                            .add(NMSConstants.MetricPlugin.IBM_AIX_PROCESS.getName())
                            .add(NMSConstants.MetricPlugin.HP_UX_PROCESS.getName())
                            .add(NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName())
                            .add(NMSConstants.MetricPlugin.WINDOWS_SERVICE.getName()));

                    var qualified = false;

                    scheduler.put(MESSAGE, ErrorMessageConstants.SERVER_OBJECT_NOT_FOUND);

                    if (!items.isEmpty())
                    {
                        var applications = new JsonArray();

                        // If predefined objects available go with that for application rediscovery else check in master collection of process/service
                        if (scheduler.getJsonArray(NMSConstants.OBJECTS) != null && !scheduler.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                        {
                            applications.addAll(scheduler.getJsonArray(NMSConstants.OBJECTS));
                        }
                        else
                        {
                            var values = new JsonArray().add(NMSConstants.Type.WINDOWS.getName())
                                    .add(NMSConstants.Type.LINUX.getName())
                                    .add(NMSConstants.Type.IBM_AIX.getName())
                                    .add(NMSConstants.Type.HP_UX.getName())
                                    .add(NMSConstants.Type.SOLARIS.getName());

                            SystemProcessConfigStore.getStore().flatItemsByValues(SystemProcess.SYSTEM_PROCESS_OS, values, SystemProcess.SYSTEM_PROCESS_APP_TYPE)
                                    .stream().distinct().forEach(applications::add);

                            SystemServiceConfigStore.getStore().flatItemsByValues(SystemService.SYSTEM_SERVICE_OS, values, SystemService.SYSTEM_SERVICE_APP_TYPE)
                                    .stream().filter(item -> !applications.contains(item)).distinct().forEach(applications::add);
                        }

                        var autoProvision = scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS) != null ?
                                scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS) : NO;

                        SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER);

                        if (!items.isEmpty())
                        {
                            var context = new JsonObject()
                                    .put(EventBusConstants.EVENT_SCHEDULER, scheduler.getLong(ID))
                                    .put(NMSConstants.AUTO_PROVISION_STATUS, autoProvision)
                                    .put(USER_NAME, scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER)
                                    .put(NMSConstants.REDISCOVER_JOB, rediscoverJob)
                                    .put(APIConstants.SESSION_ID, scheduler.getString(APIConstants.SESSION_ID));

                            var qualifiedObjects = qualifyObjects(scheduler); // for data security

                            for (var index = 0; index < items.size(); index++)
                            {
                                var object = ObjectConfigStore.getStore().getItem(items.getJsonObject(index).getLong(Metric.METRIC_OBJECT));

                                if ((object != null && object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name())
                                        && (ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) == null || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).equalsIgnoreCase(STATUS_UP))) && qualifiedObjects.contains(object.getLong(ID)))
                                {
                                    if (!qualified && scheduler.getString(APIConstants.SESSION_ID) != null)
                                    {
                                        EventBusConstants.publish(scheduler.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_REDISCOVER_START, scheduler.put(NMSConstants.REDISCOVER_JOB, rediscoverJob));
                                    }

                                    qualified = true;

                                    var metrics = MetricConfigStore.getStore().getItemsByObjectId(items.getJsonObject(index).getLong(Metric.METRIC_OBJECT));

                                    var qualifiedApplications = applications.copy();

                                    var provisionApplications = new JsonArray();

                                    // filter already provision applications
                                    for (var token = 0; token < metrics.size(); token++)
                                    {
                                        var metricType = metrics.getJsonObject(token).getString(Metric.METRIC_TYPE);

                                        if (qualifiedApplications.contains(metricType))
                                        {
                                            provisionApplications.add(metricType);

                                            qualifiedApplications.remove(metricType);
                                        }
                                    }

                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_REDISCOVER, new JsonObject().mergeIn(context).mergeIn(items.getJsonObject(index))
                                            .put(NMSConstants.APPS, provisionApplications)
                                            .put(NMSConstants.OBJECTS, qualifiedApplications));
                                }
                            }
                        }
                    }

                    complete(qualified, scheduler);
                }

                default ->
                {
                    // do nothing
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    /**
     * Sends rediscovery requests for the specified metrics.
     * <p>
     * This method processes a list of metrics and sends rediscovery requests for eligible ones.
     * It checks if each metric's object is enabled, up, and accessible to the current user
     * before sending the rediscovery request.
     *
     * @param items     the array of metric items to rediscover
     * @param scheduler the JSON object containing rediscovery job configuration
     */
    private void send(JsonArray items, JsonObject scheduler)
    {
        var qualified = false;

        // Create context for rediscovery requests
        var context = new JsonObject().put(EventBusConstants.EVENT_SCHEDULER, scheduler.getLong(ID))
                .put(NMSConstants.REDISCOVER_JOB, scheduler.getString(NMSConstants.REDISCOVER_JOB))
                .put(USER_NAME, scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER)
                .put(NMSConstants.AUTO_PROVISION_STATUS, scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS) != null ? scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS) : NO)
                .put(APIConstants.SESSION_ID, scheduler.getString(APIConstants.SESSION_ID));

        var probes = 0;

        // Initialize scheduler context in the cache store
        SchedulerCacheStore.getStore().initSchedulerContext(scheduler.getLong(ID), scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER);

        // Get objects that the current user has access to
        var qualifiedObjects = qualifyObjects(scheduler); // for data security

        if (items != null && !items.isEmpty())
        {
            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                // Check if the item is in the specified objects (if any)
                if (scheduler.getValue(NMSConstants.OBJECTS) == null || scheduler.getJsonArray(NMSConstants.OBJECTS).contains(item.getLong(Metric.METRIC_OBJECT)))
                {
                    // Check if the object is eligible for rediscovery
                    qualified = eligible(qualifiedObjects, ObjectConfigStore.getStore().getItem(item.getLong(Metric.METRIC_OBJECT)), item, context);

                    // If this is the first qualified object, publish a rediscovery start notification
                    if (qualified && probes == 0)
                    {
                        probes++;

                        if (scheduler.getString(APIConstants.SESSION_ID) != null)
                        {
                            EventBusConstants.publish(scheduler.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_REDISCOVER_START,
                                    scheduler.put(NMSConstants.REDISCOVER_JOB, scheduler.getString(NMSConstants.REDISCOVER_JOB)));
                        }
                    }
                }
            }
        }

        // Complete the rediscovery process
        complete(probes > 0, scheduler);
    }

    /**
     * Gets the list of objects that the current user has access to.
     * <p>
     * This method retrieves the user's group memberships and returns a list of objects
     * that belong to those groups. It's used for data security to ensure that users
     * can only rediscover objects they have access to.
     *
     * @param scheduler the JSON object containing the user information
     * @return a JSON array of object IDs that the user has access to
     */
    private JsonArray qualifyObjects(JsonObject scheduler)
    {
        var items = new JsonArray();

        // Get the user from the store
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, scheduler.containsKey(USER_NAME) ? scheduler.getString(USER_NAME) : DEFAULT_USER);

        if (item != null)
        {
            if (item.getLong(ID).equals(DEFAULT_ID)) // Default user has access to all objects
            {
                items = ObjectConfigStore.getStore().getItemsByGroups(GroupConfigStore.getStore().flatItems(ID));
            }
            else // Non-default user has access to objects in their groups
            {
                items = ObjectConfigStore.getStore().getItemsByGroups(item.getJsonArray(User.USER_GROUPS));
            }
        }

        return items;
    }

    /**
     * Checks if an object is eligible for rediscovery and sends a rediscovery request if it is.
     * <p>
     * An object is eligible for rediscovery if:
     * <ul>
     *   <li>It exists</li>
     *   <li>It is enabled</li>
     *   <li>It is in UP status (or has no status)</li>
     *   <li>The current user has access to it</li>
     * </ul>
     * If the object is eligible, this method sends a rediscovery request for its metric.
     *
     * @param qualifiedObjects the array of object IDs that the user has access to
     * @param object           the object to check
     * @param metric           the metric to rediscover
     * @param context          the context for the rediscovery request
     * @return true if the object is eligible and a rediscovery request was sent, false otherwise
     */
    private boolean eligible(JsonArray qualifiedObjects, JsonObject object, JsonObject metric, JsonObject context)
    {
        var qualified = false;

        // Check if the object exists, is enabled, is up, and the user has access to it
        if ((object != null && object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name())
                && (ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) == null
                || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).equalsIgnoreCase(STATUS_UP))) && qualifiedObjects.contains(object.getLong(ID)))
        {
            qualified = true;

            // Add agent information to the metric if available
            if (object.containsKey(AIOpsObject.OBJECT_AGENT))
            {
                metric.put(AIOpsObject.OBJECT_AGENT, object.getLong(AIOpsObject.OBJECT_AGENT))
                        .put(AIOpsObject.OBJECT_NAME, object.getString(AIOpsObject.OBJECT_NAME))
                        .put(AIOpsObject.OBJECT_TYPE, object.getString(AIOpsObject.OBJECT_TYPE));
            }

            // Send the rediscovery request
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_REDISCOVER, metric.mergeIn(context));
        }

        return qualified;
    }

    /**
     * Completes the rediscovery process.
     * <p>
     * If no objects were qualified for rediscovery, this method clears the scheduler context
     * and sends a notification to the UI with the reason.
     *
     * @param qualified true if at least one object was qualified for rediscovery, false otherwise
     * @param scheduler the JSON object containing rediscovery job configuration
     */
    private void complete(boolean qualified, JsonObject scheduler)
    {
        if (!qualified) // means not any object is qualified for rediscover or metrics not found
        {
            // Clear the scheduler context
            SchedulerCacheStore.getStore().clearSchedulerContext(scheduler.getLong(ID));

            // Send a notification to the UI
            if (scheduler.getString(APIConstants.SESSION_ID) != null)
            {
                EventBusConstants.publish(scheduler.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_REDISCOVER_PROGRESS,
                        new JsonObject().put(EventBusConstants.EVENT_SCHEDULER, scheduler.getLong(ID)).put(MESSAGE, scheduler.getString(MESSAGE)));
            }
        }
    }

    private Future<JsonObject> backupDB(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        Bootstrap.vertx().<JsonObject>executeBlocking(future ->
        {
            try
            {
                var configDir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

                // Create backup directory with versioned name: ConfigDB-backup-8.0.0-timestamp
                var backupDir = new File(DBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR + "ConfigDB-backup" + DASH_SEPARATOR + MotadataConfigUtil.getVersion() + DASH_SEPARATOR + context.getLong(DBConstants.BACKUP_START_TIME));

                backupDir.mkdirs();

                // Copy required configuration files to backup directory
                try
                {
                    for (var currentFile : Objects.requireNonNull(configDir.listFiles()))
                    {
                        // Skip excluded files and position files
                        if (!DBConstants.BACKUP_EXCLUSION_FILES.contains(currentFile.getName()) && !currentFile.getName().endsWith("position"))
                        {
                            var backupFile = new File(backupDir.getAbsolutePath() + PATH_SEPARATOR + currentFile.getName());

                            if (backupFile.createNewFile())
                            {
                                FileUtils.copyFile(currentFile, backupFile);

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("%s file copied for backup", currentFile.getName()));
                                }
                            }
                        }
                    }

                    // Copy version file for backup integrity
                    FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), backupDir, false);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                // Create PostgreSQL database backup
                backupDB(backupDir.getAbsolutePath());

                // Create compressed ZIP archive and cleanup
                if (backupDir.exists() && backupDir.length() > 0)
                {
                    var zipFile = new File(backupDir.getAbsolutePath() + ".zip");

                    ZipUtil.pack(backupDir, zipFile, false);

                    FileUtils.deleteQuietly(backupDir);

                    // Update context with backup information
                    context.put(GlobalConstants.SRC_FILE_PATH, zipFile.getAbsolutePath());

                    context.put(DatastoreConstants.BACKUP_SIZE_BYTES, zipFile.length() / (1024 * 1024));

                    future.complete(context);
                }
                else
                {
                    future.fail("backup directory does not exist");
                }
            }
            catch (Exception exception)
            {
                future.fail(exception);

                LOGGER.error(exception);
            }

        }, true, result ->
        {
            if (result.succeeded())
            {
                promise.complete(result.result());
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Takes a backup of PostgreSQL database using pg_dump command.
     *
     * <p>This method creates a compressed PostgreSQL backup using the pg_dump utility.
     * It uses custom format (-F c) for better compression and faster restore times.
     * The backup includes the complete motadata database structure and data.</p>
     *
     * @param backupDir Directory where the backup file should be created
     * @throws Exception If the backup process fails or pg_dump returns non-zero exit code
     */
    private void backupDB(String backupDir) throws Exception
    {
        try
        {
            var backupFile = backupDir + PATH_SEPARATOR + "backup.dump";

            // Configure process builder with PostgreSQL credentials
            var processBuilder = new ProcessBuilder();

            processBuilder.environment().put("PGPASSWORD", "TRACEorg@2025");

            // Build pg_dump command with custom format for compression
            var command = List.of(
                    "pg_dump",
                    "-U", "motadata",
                    "-h", "127.0.0.1",
                    "-F", "c",  // Custom format for compression
                    "-f", backupFile,
                    "-d", "motadata"
            );

            processBuilder.command(command);

            // Execute backup process and wait for completion
            var process = processBuilder.start();

            var exitCode = process.waitFor();

            if (exitCode == 0)
            {
                LOGGER.info("PostgreSQL database backup completed successfully at: " + backupFile);
            }
            else
            {
                var errorStream = new String(process.getErrorStream().readAllBytes());

                LOGGER.warn("PostgreSQL backup failed with exit code: " + exitCode + ", Error: " + errorStream);

                throw new Exception("PostgreSQL backup failed with exit code: " + exitCode);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            throw exception;
        }
    }
}
