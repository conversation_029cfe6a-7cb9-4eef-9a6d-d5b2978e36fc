/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	17-Mar-2025		Pruthviraj		    MOTADATA-5331: added event to get monitor severity count
    June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.

*/

package com.mindarray.policy;

/**
 * SeverityChangeEventProcessor manages policy-triggered notifications and severity change events.
 * <p>
 * This class:
 * 1. Tracks active user sessions and their notification preferences
 * 2. Processes policy-triggered events and distributes notifications
 * 3. Manages notification counts and limits per user
 * 4. Filters notifications based on user preferences and data access rights
 * 5. Provides real-time severity change information to the UI
 * <p>
 * The processor runs as a Vert.x verticle and communicates with other components
 * through the event bus to coordinate notification delivery.
 */

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Metric;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.APIConstants.Entity.NETROUTE;
import static com.mindarray.api.User.USER_PREFERENCES;
import static com.mindarray.api.User.USER_PREFERENCE_NOTIFICATION_POPUP_STATUS;
import static com.mindarray.db.DBConstants.TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;

public class SeverityChangeEventProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(SeverityChangeEventProcessor.class, GlobalConstants.MOTADATA_POLICY, "Severity Change Event Processor");
    private static final int MAX_NOTIFICATIONS = MotadataConfigUtil.getMaxPolicyNotifications();
    private final Map<String, JsonObject> eventsBySessionId = new HashMap<>();
    private final Map<String, Long> ticksBySessionId = new HashMap<>();
    private final Map<Long, Integer> notificationCounts = new HashMap<>();     // userId -> count (for alert notification)
    private final Map<Long, Integer> recentNotificationCounts = new HashMap<>();           // userId -> count (for alert pop-up)
    private final Map<String, Long> activeSessions = new HashMap<>();                 // sessionId -> userId  ( to get userId from sessionId,because every time ui sends us sessionId )
    private final Map<Long, Set<String>> activeUsers = new HashMap<>();          // userId -> sessionId
    private final List<JsonObject> triggeredNotifications = new LinkedList<>();
    private final Map<PolicyEngineConstants.PolicyType, String> POLICY_QUERIES_BY_TYPE = Map.ofEntries(
            Map.entry(PolicyEngineConstants.PolicyType.STATIC, EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY),
            Map.entry(PolicyEngineConstants.PolicyType.NETROUTE, EVENT_NETROUTE_POLICY_TRIGGER_DURATION_QUERY));

    /**
     * Determines if a policy is an event policy.
     * Event policies include log, flow, and trap policies, which don't
     * require data security filtering for notifications.
     *
     * @param item The policy item to check
     * @return true if the policy is an event policy, false otherwise
     */
    private static boolean isEventPolicy(JsonObject item)
    {
        //as in case of event policy will not apply any kind of data security so will be sending all events of event policy
        return item.getString(PolicyEngineConstants.POLICY_TYPE).
                equalsIgnoreCase(PolicyEngineConstants.PolicyType.LOG.getName()) || item.getString(PolicyEngineConstants.POLICY_TYPE)
                .equalsIgnoreCase(PolicyEngineConstants.PolicyType.FLOW.getName()) || item.getString(PolicyEngineConstants.POLICY_TYPE)
                .equalsIgnoreCase(PolicyEngineConstants.PolicyType.TRAP.getName()) || (item.containsKey(PolicyEngineConstants.POLICY_EVALUATION_TYPE) && item.getString(PolicyEngineConstants.POLICY_EVALUATION_TYPE)
                .equalsIgnoreCase(NetRouteConstants.NetRouteType.HOP_BY_HOP.getName()));
    }

    private static void addPolicies(JsonObject item, JsonObject durations, Set<Object> policies, JsonArray items, JsonObject policy, String type)
    {
        var policyKey = CommonUtil.getString(item.getLong(ENTITY_ID) + SEPARATOR + item.getLong(ID) + SEPARATOR + (item.getValue(INSTANCE, null) != null && !CommonUtil.getString(item.getValue(INSTANCE)).isEmpty() ? item.getString(GlobalConstants.METRIC) + SEPARATOR + item.getValue(INSTANCE) : item.getString(GlobalConstants.METRIC)));

        var duration = durations.getJsonObject(type).containsKey(policyKey) ? durations.getJsonObject(type).getJsonObject(policyKey) : null;

        if (duration != null && duration.getString(SEVERITY).equalsIgnoreCase(item.getString(SEVERITY)) && !policies.contains(policyKey.hashCode()))
        {
            if (type.equalsIgnoreCase(NETROUTE.getName()))
            {
                items.add(new JsonObject().mergeIn(item).mergeIn(duration).mergeIn(NetRouteConfigStore.getStore().getItem(item.getLong(ENTITY_ID), false)).put(PolicyEngineConstants.POLICY_NAME, policy.getString(PolicyEngineConstants.POLICY_NAME)));
            }
            else
            {
                items.add(new JsonObject().mergeIn(item).mergeIn(duration).mergeIn(ObjectConfigStore.getStore().getItem(item.getLong(ENTITY_ID), false)).put(PolicyEngineConstants.POLICY_NAME, policy.getString(PolicyEngineConstants.POLICY_NAME)));
            }

            policies.add(policyKey.hashCode());
        }
    }

    // this will return qualified alerts by user, based on the count/position

    /**
     * Initializes the SeverityChangeEventProcessor verticle.
     * This method:
     * 1. Loads user notification preferences
     * 2. Sets up event bus handlers for user sessions, policy notifications, and severity changes
     * 3. Initializes periodic timers for notification processing
     * 4. Registers handlers for user preference updates
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var items = UserConfigStore.getStore().getIds();

        for (var index = 0; index < items.size(); index++)
        {
            var item = UserConfigStore.getStore().getItem(items.getLong(index)).getJsonObject(USER_PREFERENCES);

            if (item.containsKey(USER_PREFERENCE_NOTIFICATION_POPUP_STATUS) && item.getString(USER_PREFERENCE_NOTIFICATION_POPUP_STATUS).equalsIgnoreCase(YES))
            {
                recentNotificationCounts.put(items.getLong(index), 0);
            }
        }

        // when user gets updated, we'll check notification state and update cache
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(CHANGE_NOTIFICATION_TYPE) && EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.UPDATE_USER)
                {
                    var item = UserConfigStore.getStore().getItem(event.getLong(ID)).getJsonObject(USER_PREFERENCES);

                    if (item.containsKey(USER_PREFERENCE_NOTIFICATION_POPUP_STATUS))
                    {
                        if (item.getString(USER_PREFERENCE_NOTIFICATION_POPUP_STATUS).equalsIgnoreCase(YES))
                        {
                            recentNotificationCounts.putIfAbsent(event.getLong(ID), 0);
                        }
                        else
                        {
                            recentNotificationCounts.remove(event.getLong(ID));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        // when user click on alert notification
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_ACTIVE_NOTIFICATION_QUERY, message ->
        {
            var event = message.body();

            var userId = activeSessions.get(event.getString(SESSION_ID));

            if (notificationCounts.containsKey(userId) && notificationCounts.get(userId) > 0 && !triggeredNotifications.isEmpty())
            {
                filterNotifications(notificationCounts.get(userId), true, userId.equals(DEFAULT_ID) ? ObjectConfigStore.getStore().getObjectIds() : ObjectConfigStore.getStore().getObjectIdsByGroups(UserConfigStore.getStore().getItem(userId, false).getJsonArray(User.USER_GROUPS))).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.EVENT_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESULT, result.result()));
                    }
                    else
                    {
                        EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.EVENT_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, result.cause()));
                    }
                });
            }
            else
            {
                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.EVENT_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(STATUS, STATUS_FAIL));
            }
        });

        // when user clears the alert
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_ACTIVE_NOTIFICATION_CLEAR, message ->
        {
            try
            {
                var userId = activeSessions.get(message.body().getString(SESSION_ID));

                if (userId != null)
                {
                    notificationCounts.put(userId, 0);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            message.reply(new JsonObject());
        });

        // when alert flaps, we'll update user count/position
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_POLICY_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                notificationCounts.forEach((key, value) ->
                {
                    if (notificationCounts.getOrDefault(key, 0) < MAX_NOTIFICATIONS)
                    {
                        notificationCounts.put(key, notificationCounts.getOrDefault(key, 0) + 1);
                    }
                });

                recentNotificationCounts.forEach((key, value) ->
                {
                    if (recentNotificationCounts.getOrDefault(key, 0) < MAX_NOTIFICATIONS)
                    {
                        recentNotificationCounts.put(key, recentNotificationCounts.getOrDefault(key, 0) + 1);
                    }
                });

                if (triggeredNotifications.size() >= MAX_NOTIFICATIONS)
                {
                    triggeredNotifications.removeFirst();
                }

                triggeredNotifications.add(event);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SEVERITY_QUERY, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                eventsBySessionId.put(sessionId, new JsonObject());

                if (event.containsKey(TYPE) && event.getString(TYPE).equalsIgnoreCase(NETROUTE.getName()))
                {
                    publish(sessionId, EventBusConstants.EVENT_SEVERITY_QUERY, NetRoutePolicyCacheStore.getStore().getSeverities());
                }
                else
                {
                    publish(sessionId, EventBusConstants.EVENT_SEVERITY_QUERY, MetricPolicyCacheStore.getStore().getSeverities());
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // this event to get the severity count only by entity id
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SEVERITY_COUNT_QUERY, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                publish(sessionId, EventBusConstants.EVENT_SEVERITY_QUERY, MetricPolicyCacheStore.getStore().getSeveritiesByObject(event.getJsonArray(ENTITIES)));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SEVERITY_CHANGE, message ->
        {
            var event = message.body();

            if (CommonUtil.traceEnabled())
            {

                LOGGER.trace(event.encode());
            }

            try
            {
                eventsBySessionId.forEach((key, value) ->
                        value.put(event.getString(PolicyEngineConstants.POLICY_KEY),
                                new JsonObject().put(event.getString(PolicyEngineConstants.POLICY_KEY), event.getString(SEVERITY)).put(PolicyEngineConstants.SEVERITY_TYPE, event.getString(PolicyEngineConstants.SEVERITY_TYPE))));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER_PING, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                eventsBySessionId.computeIfAbsent(sessionId, value -> new JsonObject());

                if (!eventsBySessionId.get(sessionId).isEmpty())
                {
                    var iterator = eventsBySessionId.get(sessionId).iterator();

                    var response = new JsonObject();

                    while (iterator.hasNext())
                    {
                        var context = JsonObject.mapFrom(iterator.next().getValue());

                        var severityType = CommonUtil.getString(context.remove(PolicyEngineConstants.SEVERITY_TYPE));

                        if (severityType != null)
                        {
                            if (response.containsKey(severityType))
                            {
                                response.getJsonObject(severityType).mergeIn(context);
                            }
                            else
                            {
                                response.put(severityType, new JsonObject().mergeIn(context));
                            }
                        }
                    }

                    EventBusConstants.publish(sessionId, EventBusConstants.EVENT_SEVERITY_QUERY, response);

                    eventsBySessionId.get(sessionId).clear();
                }

                activeSessions.computeIfAbsent(sessionId, value -> UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME)).getLong(GlobalConstants.ID));

                var userId = activeSessions.get(sessionId);

                activeUsers.computeIfAbsent(userId, value -> new HashSet<>()).add(sessionId);

                notificationCounts.putIfAbsent(userId, 0);

                if (!recentNotificationCounts.containsKey(userId) && UserConfigStore.getStore().getItem(userId, false).getJsonObject(USER_PREFERENCES).getString(USER_PREFERENCE_NOTIFICATION_POPUP_STATUS, NO).equalsIgnoreCase(YES))
                {
                    recentNotificationCounts.put(userId, 0);
                }

                var qualifiedObjects = userId.equals(DEFAULT_ID) ? ObjectConfigStore.getStore().getObjectIds() : ObjectConfigStore.getStore().getObjectIdsByGroups(UserConfigStore.getStore().getItem(userId, false).getJsonArray(User.USER_GROUPS));

                if (recentNotificationCounts.containsKey(userId) && recentNotificationCounts.get(userId) > 0 && !triggeredNotifications.isEmpty())
                {
                    filterNotifications(recentNotificationCounts.get(userId), false, qualifiedObjects).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            // reset count
                            recentNotificationCounts.put(userId, 0);

                            // publish user's all active session
                            activeUsers.get(userId).forEach(item -> EventBusConstants.publish(item, EventBusConstants.EVENT_SEVERITY_CHANGE, new JsonObject().put(RESULT, asyncResult.result())));
                        }
                    });
                }

                // to show updated count on notification icon ,user wise. so every user will have different alert count according the groups
                var count = getActiveNotificationCount(notificationCounts.get(userId), qualifiedObjects);

                activeUsers.get(userId).forEach(item -> EventBusConstants.publish(item, EventBusConstants.UI_NOTIFICATION_ACTIVE_NOTIFICATION_QUERY, new JsonObject().put(ENTITY_PROPERTY_COUNT, count)));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.setPeriodic(TimeUnit.MINUTES.toMillis(MotadataConfigUtil.getUISessionTimeoutMinutes()), timer ->
        {
            var iterator = ticksBySessionId.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                if (entry.getValue() < System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5)) // user is inactive for more than 5 minutes
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug("removing " + entry.getKey());
                    }

                    eventsBySessionId.remove(entry.getKey());

                    if (activeSessions.containsKey(entry.getKey()))
                    {
                        activeUsers.get(activeSessions.get(entry.getKey())).remove(entry.getKey());
                    }

                    iterator.remove();
                }
            }
        });

        promise.complete();
    }

    /**
     * Filters notifications based on user data access rights and preferences.
     * This method applies data security rules to ensure users only receive
     * notifications for objects they have access to.
     *
     * @param count            The maximum number of notifications to return
     * @param notification     Flag indicating if this is for notification popup (true) or alert list (false)
     * @param qualifiedObjects The objects that have triggered notifications
     * @return A Future containing the filtered list of notifications
     */
    private Future<JsonArray> filterNotifications(int count, boolean notification, JsonArray qualifiedObjects)
    {
        var promise = Promise.<JsonArray>promise();

        var items = new JsonArray();

        try
        {
            var durations = new JsonObject();

            var futures = new ArrayList<Future<Void>>();

            for (var type : POLICY_QUERIES_BY_TYPE.keySet())
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                // if it is alert notification then prepare context
                // else for pop-up we don't need this
                if (notification)
                {
                    Bootstrap.vertx().eventBus().<JsonObject>request(POLICY_QUERIES_BY_TYPE.get(type), new JsonObject(), reply ->
                    {
                        if (reply.succeeded())
                        {
                            durations.put(type.getName(), reply.result().body());

                            future.complete();
                        }
                        else
                        {
                            LOGGER.error(reply.cause());

                            future.fail(reply.cause());
                        }
                    });
                }
                else
                {
                    future.complete();
                }
            }

            Future.join(futures).onComplete(asyncResult ->
            {
                try
                {
                    if (asyncResult.succeeded())
                    {
                        var policies = new HashSet<>();

                        for (var index = triggeredNotifications.size() - count; index < triggeredNotifications.size(); index++)
                        {
                            var item = triggeredNotifications.get(index);

                            if (isEventPolicy(item))
                            {
                                items.add(item);
                            }
                            else if (item.getString(PolicyEngineConstants.POLICY_TYPE).equalsIgnoreCase(NETROUTE.getName()))
                            {
                                if (notification)
                                {
                                    addPolicies(item, durations, policies, items, NetRoutePolicyConfigStore.getStore().getItem(item.getLong(ID), false), PolicyEngineConstants.PolicyType.NETROUTE.getName());
                                }

                                else
                                {
                                    items.add(new JsonObject().mergeIn(item).put(PolicyEngineConstants.POLICY_NAME, NetRoutePolicyConfigStore.getStore().getItem(item.getLong(ID), false).getString(PolicyEngineConstants.POLICY_NAME)));
                                }
                            }
                            else
                            {
                                // check if user has access of alert or not
                                if ((qualifiedObjects != null && qualifiedObjects.contains(ObjectConfigStore.getStore().getObjectId(item.getLong(ENTITY_ID)))))
                                {
                                    var policy = MetricPolicyConfigStore.getStore().getItem(item.getLong(ID), false);

                                    if (notification)
                                    {
                                        addPolicies(item, durations, policies, items, policy, PolicyEngineConstants.PolicyType.STATIC.getName());
                                    }
                                    else
                                    {
                                        items.add(new JsonObject().mergeIn(item).put(PolicyEngineConstants.POLICY_NAME, policy.getString(PolicyEngineConstants.POLICY_NAME)).put(INSTANCE, NMSConstants.APPLICATION_TYPES.contains(item.getString(Metric.METRIC_TYPE)) ? item.getString(Metric.METRIC_TYPE) : item.getString(INSTANCE)));
                                    }
                                }
                            }

                        }

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format(" qualified items : %s , count : %s , notification : %s ", items, items.size(), notification));
                        }

                        promise.complete(items);
                    }
                    else
                    {
                        promise.fail(asyncResult.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    // this is to return alert count , so we can update count on alert notification

    /**
     * Counts the number of active notifications for qualified objects.
     * This method calculates the total number of distinct notifications
     * that match the specified criteria.
     *
     * @param count            The maximum number of notifications to consider
     * @param qualifiedObjects The objects that have triggered notifications
     * @return The count of active notifications
     */
    private long getActiveNotificationCount(int count, JsonArray qualifiedObjects)
    {
        return triggeredNotifications.subList(triggeredNotifications.size() - count, triggeredNotifications.size()).stream()
                .filter(item -> isEventPolicy(item) || (qualifiedObjects != null && qualifiedObjects.contains(ObjectConfigStore.getStore().getObjectId(item.getLong(ENTITY_ID)))))
                .map(item -> isEventPolicy(item) ? CommonUtil.getString(item.getLong(EVENT_TIMESTAMP)) : CommonUtil.getString(item.getLong(ENTITY_ID) + SEPARATOR + item.getLong(ID) + SEPARATOR + (item.getValue(INSTANCE, null) != null && !CommonUtil.getString(item.getValue(INSTANCE)).isEmpty() ? item.getString(GlobalConstants.METRIC) + SEPARATOR + item.getValue(INSTANCE) : item.getString(GlobalConstants.METRIC))))
                .distinct().count();

    }

    /**
     * Stops the SeverityChangeEventProcessor verticle.
     * This method completes the provided promise to signal successful shutdown.
     *
     * @param promise Promise to be completed when shutdown is done
     * @throws Exception If an error occurs during shutdown
     */
    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        promise.complete();
    }
}
