[{"_type": "0", "id": 10000000000670, "visualization.name": "Connection", "visualization.description": "PostgreSQL Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.maximum.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.maximum.connections"}, {"type": "metric", "data.point": "postgresql.active.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Maximum", "value": "postgresql.maximum.connections.last"}, {"label": "Active", "value": "postgresql.active.connections.last"}]}}}}, {"_type": "0", "id": 10000000000671, "visualization.name": "Transaction", "visualization.description": "PostgreSQL Transaction", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.ideal.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.rollbacks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.ideal.transactions"}, {"type": "metric", "data.point": "postgresql.commits"}, {"type": "metric", "data.point": "postgresql.rollbacks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.ideal.transactions", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": [{"label": "Ideal Transactions", "value": "postgresql.ideal.transactions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Commits", "value": "postgresql.commits.last"}, {"label": "Rollback", "value": "postgresql.rollbacks.last"}]}}}}, {"_type": "0", "id": 10000000000672, "visualization.name": "Query", "visualization.description": "PostgreSQL Query", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.inserted.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.waiting.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.active.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.inserted.rows"}, {"type": "metric", "data.point": "postgresql.waiting.queries"}, {"type": "metric", "data.point": "postgresql.active.queries"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.inserted.rows", "icon": {"name": "query", "placement": "prefix"}}, "header": {"title": "Query", "style": {"font.size": "medium"}, "data.points": [{"label": "Inserted Rows", "value": "postgresql.inserted.rows.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Waiting", "value": "postgresql.waiting.queries.last"}, {"label": "Active", "value": "postgresql.active.queries.last"}]}}}}, {"_type": "0", "id": 10000000000673, "visualization.name": "Checkpoint", "visualization.description": "PostgreSQL Checkpoint", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.checkpoint.write.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.requested.checkpoints", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.scheduled.checkpoints", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.checkpoint.write.time.ms"}, {"type": "metric", "data.point": "postgresql.requested.checkpoints"}, {"type": "metric", "data.point": "postgresql.scheduled.checkpoints"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.checkpoint.write.time.ms", "icon": {"name": "check-point", "placement": "prefix"}}, "header": {"title": "Checkpoint", "style": {"font.size": "medium"}, "data.points": [{"label": "Write Time", "value": "postgresql.checkpoint.write.time.ms.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Requested", "value": "postgresql.requested.checkpoints.last"}, {"label": "Scheduled", "value": "postgresql.scheduled.checkpoints.last"}]}}}}, {"_type": "0", "id": 10000000000674, "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "PostgreSQL Buffer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.shared.buffer.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.allocated.buffers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.checkpoint.buffers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.shared.buffer.bytes"}, {"type": "metric", "data.point": "postgresql.allocated.buffers"}, {"type": "metric", "data.point": "postgresql.checkpoint.buffers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.shared.buffer.bytes", "icon": {"name": "buffer", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Shared <PERSON><PERSON><PERSON>", "value": "postgresql.shared.buffer.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Allocated", "value": "postgresql.allocated.buffers.last"}, {"label": "Checkpoint", "value": "postgresql.checkpoint.buffers.last"}]}}}}, {"_type": "0", "id": 10000000000675, "visualization.name": "<PERSON><PERSON>", "visualization.description": "PostgreSQL Cache", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.effective.cache.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "postgresql.effective.cache.size.bytes"}, {"type": "metric", "data.point": "postgresql.cache.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "postgresql.cache.hit.ratio.percent", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Effective <PERSON><PERSON>", "value": "postgresql.effective.cache.size.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "postgresql.cache.hit.ratio.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000676, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000677, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000678, "visualization.name": "Database Details", "visualization.description": "PostgreSQL Database Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.database~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.database", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.database~size.bytes.last", "alias": "system.disk.volume.used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000679, "visualization.name": "Buffer Statistics", "visualization.description": "PostgreSQL Buffer Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.backend.buffers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.backend.fsync.buffers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.checkpoint.buffers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000680, "visualization.name": "Query Details", "visualization.description": "PostgreSQL Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.inserted.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.updated.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.fetched.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.deleted.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000681, "visualization.name": "Transaction Details", "visualization.description": "PostgreSQL Transaction Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.commits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.rollbacks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.ideal.transactions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000682, "visualization.name": "Lock Details", "visualization.description": "PostgreSQL Lock Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.deadlocks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.wait.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.held.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000683, "visualization.name": "Index Scan Rate", "visualization.description": "PostgreSQL Index Scan Rate", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.index.scans.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.read.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.fetched.rows.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000684, "visualization.name": "Session Statistics", "visualization.description": "PostgreSQL Session Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgres.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.idle.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000685, "visualization.name": "<PERSON><PERSON>", "visualization.description": "PostgreSQL Cache Hit Ratio", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000686, "visualization.name": "Triggers", "visualization.description": "PostgreSQL Triggers", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.triggers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000687, "visualization.name": "Waiting Query", "visualization.description": "PostgreSQL Waiting Query", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.waiting.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000688, "visualization.name": "Disk Block Details", "visualization.description": "PostgreSQL Disk Block Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.block.reads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.block.hits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000689, "visualization.name": "Checkpoint", "visualization.description": "PostgreSQL Checkpoint", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.requested.checkpoints", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.scheduled.checkpoints", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000690, "visualization.name": "Table Scan Details", "visualization.description": "PostgreSQL Table Scan Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.sequential.scans", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.sequential.row.reads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000691, "visualization.name": "Session Details", "visualization.description": "PostgreSQL Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgres.session.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.username", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.remote.client", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.query", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgres.session.id", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "postgres.session.username.last", "title": "Username", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "question", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "postgres.session.state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "postgres.session.remote.client.last", "title": "Remote Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "question", "placement": "prefix", "classes": ["text-secondary-green"]}}}, {"name": "postgres.session.start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "question", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "postgres.session.query.last", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "question", "placement": "prefix", "classes": ["text-primary"]}}}]}}}, {"_type": "0", "id": 10000000000692, "visualization.name": "Session Lock Details", "visualization.description": "PostgreSQL Session Lock Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgres.session.lock.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.lock.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.lock.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgres.session.lock.granted", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgres.session.lock.id.last", "title": "Lock", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgres.session.lock.type.last", "title": "Lock Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgres.session.lock.mode.last", "title": "Lock Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgres.session.lock.granted.last", "title": "Lock Granted", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000693, "visualization.name": "Scanned Indexes", "visualization.description": "PostgreSQL Scanned Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.scans", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "clipboard", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "postgresql.index.scans.last", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "sort-amount-down", "placement": "prefix", "classes": ["text-neutral-light"]}}}]}}}, {"_type": "0", "id": 10000000000695, "visualization.name": "Index Details", "visualization.description": "PostgreSQL Index Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.index.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.rows", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.scans", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.fetched.tuples", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgressql.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.rows.last", "title": "Total Rows", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.size.bytes.last", "title": "Index Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.scans.last", "title": "Index Scans count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.fetched.tuples.last", "title": "<PERSON><PERSON> Fetched", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000696, "visualization.name": "Unused Indexes", "visualization.description": "PostgreSQL Unused Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.unused.index.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.unused.index.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.unused.index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.unused.index.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.unused.index.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.unused.index.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.unused.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.unused.index.size.bytes.last", "title": "Index Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000697, "visualization.name": "Tablespace Details", "visualization.description": "PostgreSQL Tablespace Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.table.space~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.table.space~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.table.space~owner", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.table.space.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "postgresql.table.space~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "hdd", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "postgresql.table.space~size.bytes.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "server", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "postgresql.table.space~owner.last", "title": "Owner", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "user", "placement": "prefix", "classes": ["text-secondary-green"]}}}]}}}]