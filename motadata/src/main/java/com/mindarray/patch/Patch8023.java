/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  10-Jun-2025		Pruthvi		    Patch added to remove column
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.MOTADATA_PATCH;
import static com.mindarray.eventbus.EventBusConstants.CHANGE_NOTIFICATION_TYPE;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.DELETE_METRIC_COLUMN;

/**
 * Implementation of the Patch interface for version 8.0.23.
 * <p>
 * This patch migrates IP/IP Range mappings into domain mappings. It processes
 * IP groups from the FlowIPGroupConfigStore, handles various IP formats including
 * individual IPs, IP ranges (e.g., ***********-***********0), partial ranges
 * (e.g., ***********-55), and wildcard notations (using 'xx').
 * <p>
 * The patch converts these IP groups into standardized domain mappings and saves
 * them to the FlowDomainMapperConfigStore with the category "IP/IP Range".
 */
public class Patch8023 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8023.class, MOTADATA_PATCH, "Patch 8.0.23");

    private static final String VERSION = "8.0.23";

    /**
     * Returns the version number of this patch.
     *
     * @return The version string "8.0.23"
     */
    @Override
    public String getVersion()
    {
        return VERSION;
    }

    /**
     * Executes the patch operations for version 8.0.23.
     * <p>
     * This method orchestrates the patch execution by:
     * 1. Creating a list of futures for asynchronous operations
     * 2. Adding the IP group patching operation to the futures list
     * 3. Joining all futures and handling the completion result
     *
     * @return A Future that completes when the patch is successfully applied or fails with an exception
     */
    @Override
    public Future<Void> doPatch()
    {
        LOGGER.info("executing patch 8.0.23");

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject().put(CHANGE_NOTIFICATION_TYPE, DELETE_METRIC_COLUMN.name()).put(DatastoreConstants.MAPPER, "system.cpu.cores"));

        return Future.succeededFuture();
    }
}
