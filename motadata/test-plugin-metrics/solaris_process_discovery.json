{"************": {"metric.timeout": 60, "object.ip": "************", "objects": [{"object.name": "sched|sched", "object.type": "system.process"}, {"object.name": "zpool-rp|zpool-rpool", "object.type": "system.process"}, {"object.name": "kmem_tas|kmem_task", "object.type": "system.process"}, {"object.name": "init|/sbin/init", "object.type": "system.process"}, {"object.name": "pageout|pageout", "object.type": "system.process"}, {"object.name": "fsflush|fsflush", "object.type": "system.process"}, {"object.name": "vmtasks|vmtasks", "object.type": "system.process"}, {"object.name": "utmpd|/usr/lib/utmpd", "object.type": "system.process"}, {"object.name": "svc.star|/lib/svc/bin/svc.startd", "object.type": "system.process"}, {"object.name": "svc.conf|/lib/svc/bin/svc.configd", "object.type": "system.process"}, {"object.name": "iscsi-in|/lib/svc/method/iscsi-initiator", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron", "object.type": "system.process"}, {"object.name": "nfsmapid|/usr/lib/nfs/nfsmapid", "object.type": "system.process"}, {"object.name": "clock-ap|/usr/lib/clock-applet --oaf-activate-iid=OAFIID:GNOME_ClockApplet_Factory --oaf", "object.type": "system.process"}, {"object.name": "sac|/usr/lib/saf/sac -t 300", "object.type": "system.process"}, {"object.name": "dhcpagen|/sbin/dhcpagent", "object.type": "system.process"}, {"object.name": "devfsadm|devfsadmd", "object.type": "system.process"}, {"object.name": "rpcbind|/usr/sbin/rpcbind", "object.type": "system.process"}, {"object.name": "sysevent|/usr/lib/sysevent/syseventd", "object.type": "system.process"}, {"object.name": "gconfd-2|/usr/lib/gconfd-2 15", "object.type": "system.process"}, {"object.name": "ttymon|/usr/lib/saf/ttymon", "object.type": "system.process"}, {"object.name": "nfs4cbd|/usr/lib/nfs/nfs4cbd", "object.type": "system.process"}, {"object.name": "kcfd|/usr/lib/crypto/kcfd", "object.type": "system.process"}, {"object.name": "nscd|/usr/sbin/nscd", "object.type": "system.process"}, {"object.name": "powerd|/usr/lib/power/powerd", "object.type": "system.process"}, {"object.name": "lockd|/usr/lib/nfs/lockd", "object.type": "system.process"}, {"object.name": "picld|/usr/lib/picl/picld", "object.type": "system.process"}, {"object.name": "rpc.mdco|/usr/sbin/rpc.mdcommd", "object.type": "system.process"}, {"object.name": "statd|/usr/lib/nfs/statd", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/sendmail -Ac -q15m", "object.type": "system.process"}, {"object.name": "rpc.ttdb|/usr/openwin/bin/rpc.ttdbserverd", "object.type": "system.process"}, {"object.name": "ttymon|/usr/lib/saf/ttymon -g -d /dev/console -l console -m ldterm,ttcompat -h -p sola", "object.type": "system.process"}, {"object.name": "inetd|/usr/lib/inet/inetd start", "object.type": "system.process"}, {"object.name": "smcboot|/usr/sadm/lib/smc/bin/smcboot", "object.type": "system.process"}, {"object.name": "automoun|/usr/lib/autofs/automountd", "object.type": "system.process"}, {"object.name": "vold|/usr/sbin/vold -f /etc/vold.conf", "object.type": "system.process"}, {"object.name": "in.ndpd|/usr/lib/inet/in.ndpd", "object.type": "system.process"}, {"object.name": "sshd|/usr/lib/ssh/sshd", "object.type": "system.process"}, {"object.name": "rpc-ttdb|/bin/sh /lib/svc/method/rpc-ttdbserverd", "object.type": "system.process"}, {"object.name": "gnome-vo|gnome-volcheck -i 30 -z 3 -m cdrom,floppy,zip,jaz,dvdrom --sm-client-id default", "object.type": "system.process"}, {"object.name": "Xorg|/usr/X11/bin/Xorg :0 -depth 24 -nobanner -auth /var/dt/A:0-RgayFb", "object.type": "system.process"}, {"object.name": "dtlogin|/usr/dt/bin/dtlogin -daemon", "object.type": "system.process"}, {"object.name": "gnome-vf|/usr/lib/gnome-vfs-daemon --oaf-activate-iid=OAFIID:GNOME_VFS_Daemon_Factory --", "object.type": "system.process"}, {"object.name": "rpc.meta|/usr/sbin/rpc.metad", "object.type": "system.process"}, {"object.name": "syslogd|/usr/sbin/syslogd", "object.type": "system.process"}, {"object.name": "snmpdx|/usr/lib/snmp/snmpdx -y -c /etc/snmp/conf", "object.type": "system.process"}, {"object.name": "gnome-pa|gnome-panel --sm-client-id default2", "object.type": "system.process"}, {"object.name": "fmd|/usr/lib/fm/fmd/fmd", "object.type": "system.process"}, {"object.name": "fbconsol|/usr/openwin/bin/fbconsole -n -d :0", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/sendmail -bl -q15m", "object.type": "system.process"}, {"object.name": "dmispd|/usr/lib/dmi/dmispd", "object.type": "system.process"}, {"object.name": "snmpd|/usr/sfw/sbin/snmpd", "object.type": "system.process"}, {"object.name": "Xsession|/bin/ksh /usr/dt/config/Xsession2.jds", "object.type": "system.process"}, {"object.name": "Xsession|/bin/ksh /usr/dt/bin/Xsession", "object.type": "system.process"}, {"object.name": "java|/usr/java/bin/java -server -Xmx128m -XX:+UseParallelGC -XX:ParallelGCThreads=4", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/lib/gnome-settings-daemon --oaf-activate-iid=OAFIID:GNOME_SettingsDaemon -", "object.type": "system.process"}, {"object.name": "metacity|/usr/bin/metacity --sm-client-id=default1", "object.type": "system.process"}, {"object.name": "xfs|/usr/openwin/bin/xfs", "object.type": "system.process"}, {"object.name": "bash|-bash -c unset DT; DISPLAY=:0; /usr/dt/bin/dtsession_res -mer", "object.type": "system.process"}, {"object.name": "mapping-|/usr/lib/mapping-daemon", "object.type": "system.process"}, {"object.name": "bonobo-a|/usr/lib/bonobo-activation-server --ac-activate --ior-output-fd=23", "object.type": "system.process"}, {"object.name": "wnck-app|/usr/lib/wnck-applet --oaf-activate-iid=OAFIID:GNOME_Wncklet_Factory --oaf-ior-", "object.type": "system.process"}, {"object.name": "java|/usr/jdk/latest/bin/java -version:1.5+ -jar /usr/lib/patch/swupna.jar -wait", "object.type": "system.process"}, {"object.name": "gnome-se|/usr/bin/gnome-session", "object.type": "system.process"}, {"object.name": "gnome-pt|gnome-pty-helper", "object.type": "system.process"}, {"object.name": "sdt_shel|/usr/dt/bin/sdt_shell -c unset DT; DISPLAY=:0; /usr/dt/bin/dt", "object.type": "system.process"}, {"object.name": "gnome-sm|gnome-smproxy --sm-client-id default0", "object.type": "system.process"}, {"object.name": "dsdm|/usr/dt/bin/dsdm", "object.type": "system.process"}, {"object.name": "nautilus|nautilus --no-default-window --sm-client-id default3", "object.type": "system.process"}, {"object.name": "gnome-ke|/usr/bin/gnome-keyring-daemon", "object.type": "system.process"}, {"object.name": "gnome-ne|/usr/lib/gnome-netstatus-applet --oaf-activate-iid=OAFIID:GNOME_NetstatusApplet", "object.type": "system.process"}, {"object.name": "mixer_ap|/usr/lib/mixer_applet2 --oaf-activate-iid=OAFIID:GNOME_MixerApplet_Factory --oa", "object.type": "system.process"}, {"object.name": "notifica|/usr/lib/notification-area-applet --oaf-activate-iid=OAFIID:GNOME_NotificationA", "object.type": "system.process"}, {"object.name": "bash|bash", "object.type": "system.process"}, {"object.name": "gnome-te|/usr/bin/gnome-terminal", "object.type": "system.process"}], "password": "Mind@123", "port": 22, "status": "succeed", "username": "testuser"}, "************": {"metric.timeout": 60, "object.ip": "************", "objects": [{"object.name": "sched|sched", "object.type": "system.process"}, {"object.name": "zpool-rp|zpool-rpool", "object.type": "system.process"}, {"object.name": "kmem_tas|kmem_task", "object.type": "system.process"}, {"object.name": "init|/usr/sbin/init", "object.type": "system.process"}, {"object.name": "pageout|pageout", "object.type": "system.process"}, {"object.name": "fsflush|fsflush", "object.type": "system.process"}, {"object.name": "intrd|intrd", "object.type": "system.process"}, {"object.name": "vmtasks|vmtasks", "object.type": "system.process"}, {"object.name": "postwait|postwaittq", "object.type": "system.process"}, {"object.name": "dlmgmtd|/usr/sbin/dlmgmtd", "object.type": "system.process"}, {"object.name": "svc.star|/lib/svc/bin/svc.startd", "object.type": "system.process"}, {"object.name": "svc.conf|/lib/svc/bin/svc.configd", "object.type": "system.process"}, {"object.name": "in.ndpd|/lib/inet/in.ndpd", "object.type": "system.process"}, {"object.name": "ipmgmtd|/lib/inet/ipmgmtd", "object.type": "system.process"}, {"object.name": "hald|/usr/lib/hal/hald --daemon=yes", "object.type": "system.process"}, {"object.name": "ibmgmtd|/usr/sbin/ibmgmtd", "object.type": "system.process"}, {"object.name": "sstored|/usr/lib/sstore/bin/sstored --events --repo-path /var/share/sstore/repo --max-repo-size 2048", "object.type": "system.process"}, {"object.name": "login|/usr/bin/login", "object.type": "system.process"}, {"object.name": "utmpd|/usr/lib/utmpd", "object.type": "system.process"}, {"object.name": "nscd|/usr/sbin/nscd", "object.type": "system.process"}, {"object.name": "devchass|/usr/lib/devchassis/devchassisd", "object.type": "system.process"}, {"object.name": "fmd|/usr/lib/fm/fmd/fmd", "object.type": "system.process"}, {"object.name": "in.mpath|/lib/inet/in.mpathd", "object.type": "system.process"}, {"object.name": "kcfd|/lib/crypto/kcfd", "object.type": "system.process"}, {"object.name": "sysobjd|/usr/sbin/sysobjd -d 0 -b 300 -t 300 -n 5", "object.type": "system.process"}, {"object.name": "pfexecd|/usr/lib/pfexecd", "object.type": "system.process"}, {"object.name": "smbclntd|/usr/lib/smbfs/smbclntd", "object.type": "system.process"}, {"object.name": "picld|/usr/lib/picl/picld", "object.type": "system.process"}, {"object.name": "labeld|/usr/lib/labeld", "object.type": "system.process"}, {"object.name": "syslogd|/usr/sbin/syslogd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/lib/dbus-daemon --system", "object.type": "system.process"}, {"object.name": "hald-run|hald-runner", "object.type": "system.process"}, {"object.name": "vtdaemon|/usr/lib/vtdaemon -c 16", "object.type": "system.process"}, {"object.name": "sysevent|/usr/lib/sysevent/syseventd", "object.type": "system.process"}, {"object.name": "zonestat|/usr/lib/zones/zonestatd", "object.type": "system.process"}, {"object.name": "bash|-bash", "object.type": "system.process"}, {"object.name": "iscsid|/lib/svc/method/iscsid", "object.type": "system.process"}, {"object.name": "inetd|/usr/lib/inet/inetd start", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron", "object.type": "system.process"}, {"object.name": "rpcbind|/usr/sbin/rpcbind -w", "object.type": "system.process"}, {"object.name": "svc.zone|/usr/lib/zones/svc.zones", "object.type": "system.process"}, {"object.name": "devfsadm|/usr/lib/devfsadm/devfsadmd", "object.type": "system.process"}, {"object.name": "nwamd|/lib/inet/nwamd", "object.type": "system.process"}, {"object.name": "dhcpagen|/usr/sbin/dhcpagent", "object.type": "system.process"}, {"object.name": "rad|/usr/lib/rad/rad -sp", "object.type": "system.process"}, {"object.name": "auditd|/usr/sbin/auditd", "object.type": "system.process"}, {"object.name": "httpd|/usr/apache2/2.4/bin/httpd -f /var/webui/conf/webui.conf -k start", "object.type": "system.process"}, {"object.name": "sshd|/usr/lib/ssh/sshd", "object.type": "system.process"}, {"object.name": "svc.peri|/lib/svc/bin/svc.periodicd", "object.type": "system.process"}, {"object.name": "vbiosd|/usr/sbin/vbiosd", "object.type": "system.process"}, {"object.name": "hotplugd|/usr/lib/hotplugd", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/4 -l console -m ldterm,ttcompat -h -p solaris vt4 login:", "object.type": "system.process"}, {"object.name": "automoun|/usr/lib/autofs/automountd", "object.type": "system.process"}, {"object.name": "hald-add|/usr/lib/hal/hald-addon-acpi", "object.type": "system.process"}, {"object.name": "hald-add|/usr/lib/hal/hald-addon-storage", "object.type": "system.process"}, {"object.name": "cupsd|/usr/sbin/cupsd -C /etc/cups/cupsd.conf", "object.type": "system.process"}, {"object.name": "rmvolmgr|/usr/lib/rmvolmgr -s", "object.type": "system.process"}, {"object.name": "rotatelo|/usr/apache2/2.4/bin/rotatelogs -l -f -n 5 /var/webui/logs/error_log 10M", "object.type": "system.process"}, {"object.name": "rotatelo|/usr/apache2/2.4/bin/rotatelogs -l -f -n 5 /var/webui/logs/access_log 10M", "object.type": "system.process"}, {"object.name": "su|su", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/inet/sendmail -bl -q15m", "object.type": "system.process"}, {"object.name": "sshd|/usr/lib/ssh/sshd -R", "object.type": "system.process"}, {"object.name": "sudo|sudo su", "object.type": "system.process"}, {"object.name": "bash|bash", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/6 -l console -m ldterm,ttcompat -h -p solaris vt6 login:", "object.type": "system.process"}, {"object.name": "sysstatd|/usr/lib/sstore/bin/sysstatd --max-process-size=268435456", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/2 -l console -m ldterm,ttcompat -h -p solaris vt2 login:", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/3 -l console -m ldterm,ttcompat -h -p solaris vt3 login:", "object.type": "system.process"}, {"object.name": "asr-noti|/usr/lib/fm/notify/asr-notify", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/5 -l console -m ldterm,ttcompat -h -p solaris vt5 login:", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/inet/sendmail -Ac -q15m", "object.type": "system.process"}, {"object.name": "smtp-not|/usr/lib/fm/notify/smtp-notify", "object.type": "system.process"}, {"object.name": "coremond|/usr/bin/coremond", "object.type": "system.process"}], "password": "Mind@123", "port": 22, "status": "succeed", "username": "motadata"}, "************": {"metric.timeout": 60, "object.ip": "************", "objects": [{"object.name": "sched|sched", "object.type": "system.process"}, {"object.name": "zpool-rp|zpool-rpool", "object.type": "system.process"}, {"object.name": "kmem_tas|kmem_task", "object.type": "system.process"}, {"object.name": "init|/usr/sbin/init", "object.type": "system.process"}, {"object.name": "pageout|pageout", "object.type": "system.process"}, {"object.name": "fsflush|fsflush", "object.type": "system.process"}, {"object.name": "intrd|intrd", "object.type": "system.process"}, {"object.name": "vmtasks|vmtasks", "object.type": "system.process"}, {"object.name": "postwait|postwaittq", "object.type": "system.process"}, {"object.name": "dlmgmtd|/usr/sbin/dlmgmtd", "object.type": "system.process"}, {"object.name": "svc.star|/lib/svc/bin/svc.startd", "object.type": "system.process"}, {"object.name": "svc.conf|/lib/svc/bin/svc.configd", "object.type": "system.process"}, {"object.name": "in.ndpd|/lib/inet/in.ndpd", "object.type": "system.process"}, {"object.name": "ipmgmtd|/lib/inet/ipmgmtd", "object.type": "system.process"}, {"object.name": "hald|/usr/lib/hal/hald --daemon=yes", "object.type": "system.process"}, {"object.name": "ibmgmtd|/usr/sbin/ibmgmtd", "object.type": "system.process"}, {"object.name": "sstored|/usr/lib/sstore/bin/sstored --events --repo-path /var/share/sstore/repo --max-repo-size 2048", "object.type": "system.process"}, {"object.name": "login|/usr/bin/login", "object.type": "system.process"}, {"object.name": "utmpd|/usr/lib/utmpd", "object.type": "system.process"}, {"object.name": "nscd|/usr/sbin/nscd", "object.type": "system.process"}, {"object.name": "devchass|/usr/lib/devchassis/devchassisd", "object.type": "system.process"}, {"object.name": "fmd|/usr/lib/fm/fmd/fmd", "object.type": "system.process"}, {"object.name": "in.mpath|/lib/inet/in.mpathd", "object.type": "system.process"}, {"object.name": "kcfd|/lib/crypto/kcfd", "object.type": "system.process"}, {"object.name": "sysobjd|/usr/sbin/sysobjd -d 0 -b 300 -t 300 -n 5", "object.type": "system.process"}, {"object.name": "pfexecd|/usr/lib/pfexecd", "object.type": "system.process"}, {"object.name": "smbclntd|/usr/lib/smbfs/smbclntd", "object.type": "system.process"}, {"object.name": "picld|/usr/lib/picl/picld", "object.type": "system.process"}, {"object.name": "labeld|/usr/lib/labeld", "object.type": "system.process"}, {"object.name": "syslogd|/usr/sbin/syslogd", "object.type": "system.process"}, {"object.name": "dbus-dae|/usr/lib/dbus-daemon --system", "object.type": "system.process"}, {"object.name": "hald-run|hald-runner", "object.type": "system.process"}, {"object.name": "vtdaemon|/usr/lib/vtdaemon -c 16", "object.type": "system.process"}, {"object.name": "sysevent|/usr/lib/sysevent/syseventd", "object.type": "system.process"}, {"object.name": "zonestat|/usr/lib/zones/zonestatd", "object.type": "system.process"}, {"object.name": "bash|-bash", "object.type": "system.process"}, {"object.name": "iscsid|/lib/svc/method/iscsid", "object.type": "system.process"}, {"object.name": "inetd|/usr/lib/inet/inetd start", "object.type": "system.process"}, {"object.name": "cron|/usr/sbin/cron", "object.type": "system.process"}, {"object.name": "rpcbind|/usr/sbin/rpcbind -w", "object.type": "system.process"}, {"object.name": "svc.zone|/usr/lib/zones/svc.zones", "object.type": "system.process"}, {"object.name": "devfsadm|/usr/lib/devfsadm/devfsadmd", "object.type": "system.process"}, {"object.name": "nwamd|/lib/inet/nwamd", "object.type": "system.process"}, {"object.name": "dhcpagen|/usr/sbin/dhcpagent", "object.type": "system.process"}, {"object.name": "rad|/usr/lib/rad/rad -sp", "object.type": "system.process"}, {"object.name": "auditd|/usr/sbin/auditd", "object.type": "system.process"}, {"object.name": "httpd|/usr/apache2/2.4/bin/httpd -f /var/webui/conf/webui.conf -k start", "object.type": "system.process"}, {"object.name": "sshd|/usr/lib/ssh/sshd", "object.type": "system.process"}, {"object.name": "svc.peri|/lib/svc/bin/svc.periodicd", "object.type": "system.process"}, {"object.name": "vbiosd|/usr/sbin/vbiosd", "object.type": "system.process"}, {"object.name": "hotplugd|/usr/lib/hotplugd", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/4 -l console -m ldterm,ttcompat -h -p solaris vt4 login:", "object.type": "system.process"}, {"object.name": "automoun|/usr/lib/autofs/automountd", "object.type": "system.process"}, {"object.name": "hald-add|/usr/lib/hal/hald-addon-acpi", "object.type": "system.process"}, {"object.name": "hald-add|/usr/lib/hal/hald-addon-storage", "object.type": "system.process"}, {"object.name": "cupsd|/usr/sbin/cupsd -C /etc/cups/cupsd.conf", "object.type": "system.process"}, {"object.name": "rmvolmgr|/usr/lib/rmvolmgr -s", "object.type": "system.process"}, {"object.name": "rotatelo|/usr/apache2/2.4/bin/rotatelogs -l -f -n 5 /var/webui/logs/error_log 10M", "object.type": "system.process"}, {"object.name": "rotatelo|/usr/apache2/2.4/bin/rotatelogs -l -f -n 5 /var/webui/logs/access_log 10M", "object.type": "system.process"}, {"object.name": "su|su", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/inet/sendmail -bl -q15m", "object.type": "system.process"}, {"object.name": "sshd|/usr/lib/ssh/sshd -R", "object.type": "system.process"}, {"object.name": "sudo|sudo su", "object.type": "system.process"}, {"object.name": "bash|bash", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/6 -l console -m ldterm,ttcompat -h -p solaris vt6 login:", "object.type": "system.process"}, {"object.name": "sysstatd|/usr/lib/sstore/bin/sysstatd --max-process-size=268435456", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/2 -l console -m ldterm,ttcompat -h -p solaris vt2 login:", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/3 -l console -m ldterm,ttcompat -h -p solaris vt3 login:", "object.type": "system.process"}, {"object.name": "asr-noti|/usr/lib/fm/notify/asr-notify", "object.type": "system.process"}, {"object.name": "ttymon|/usr/sbin/ttymon -g -d /dev/vt/5 -l console -m ldterm,ttcompat -h -p solaris vt5 login:", "object.type": "system.process"}, {"object.name": "sendmail|/usr/lib/inet/sendmail -Ac -q15m", "object.type": "system.process"}, {"object.name": "smtp-not|/usr/lib/fm/notify/smtp-notify", "object.type": "system.process"}, {"object.name": "coremond|/usr/bin/coremond", "object.type": "system.process"}], "password": "Mind@123", "port": 22, "status": "succeed", "username": "motadata"}}