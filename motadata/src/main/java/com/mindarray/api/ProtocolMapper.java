/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ProtocolMapperConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class ProtocolMapper extends AbstractAPI
{

    public static final String PROTOCOL_MAPPER_NAME = "protocol.mapper.name";

    public static final String PROTOCOL_MAPPER_NUMBER = "protocol.mapper.number";


    public ProtocolMapper()
    {
        super("protocol-mappers", ProtocolMapperConfigStore.getStore(), new Logger(ProtocolMapper.class, MOTADATA_API, "Protocol Mapper API"));
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_PROTOCOL.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_PROTOCOL.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_PROTOCOL.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }
}
