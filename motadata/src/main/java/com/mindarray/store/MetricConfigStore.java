/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

public class MetricConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(MetricConfigStore.class, GlobalConstants.MOTADATA_STORE, "Metric Config Store");
    private static final MetricConfigStore STORE = new MetricConfigStore();
    private final Map<Long, JsonArray> metricsByObject = new ConcurrentHashMap<>();
    private final Map<Long, JsonObject> instancesByObject = new ConcurrentHashMap<>(); // id -> instances like apps,vm's, interfaces, processes, services, network.services
    private final Map<Integer, JsonObject> instancesByPlugin = new ConcurrentHashMap<>();// id -> instances like apps,vm's, interfaces, processes, services, network.services
    private final Map<Long, JsonObject> apps = new ConcurrentHashMap<>();// id -> MetricObject from Metrics , value -> Process , Process Name ::: Example {182 -> {"system.process~Tomcat~192 : "Apache Tomcat"}}

    private MetricConfigStore()
    {
        super(DBConstants.TBL_METRIC, LOGGER, true);
    }

    public static MetricConfigStore getStore()
    {
        return STORE;
    }

    public JsonObject getAppsByObjectId(long objectId)
    {
        return apps.containsKey(objectId) ? apps.get(objectId).copy() : null;
    }

    public JsonObject getMetricInstances(long objectId)
    {
        return instancesByObject.containsKey(objectId) ? instancesByObject.get(objectId).copy() : null;
    }

    public JsonArray getMetricInstancesByInstanceType(long objectId, String instanceType)
    {
        return instancesByObject.containsKey(objectId) && instancesByObject.get(objectId).containsKey(instanceType) ? instancesByObject.get(objectId).getJsonArray(instanceType).copy() : new JsonArray(new ArrayList<>(1));
    }

    // why List<JsonObject> instead of JsonArray ?
    // reason is that this method return value is not used to pass to UI or any other consumer and only used for manipulation and metrics is always in bulk size and JsonArray lead to multiple time JsonObject Map so to avoid that multiple copy creation will pass List<JsonObject>
    public List<JsonObject> getItemsByObject(long objectId)
    {
        return metricsByObject.containsKey(objectId) ? metricsByObject.get(objectId).stream().map(JsonObject::mapFrom).collect(Collectors.toList()) : new ArrayList<>(1);
    }

    public List<JsonObject> getItemsByObjectState(long objectId, String state)
    {
        return metricsByObject.containsKey(objectId) ? metricsByObject.get(objectId).stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_STATE).equalsIgnoreCase(state)).map(JsonObject::mapFrom).collect(Collectors.toList()) : new ArrayList<>(1);
    }

    public long getMetricObjectId(long id)
    {
        return items.containsKey(id) ? items.get(id).getLong(Metric.METRIC_OBJECT) : GlobalConstants.NOT_AVAILABLE;
    }

    public String getMetricName(long id)
    {
        return items.containsKey(id) ? items.get(id).getString(Metric.METRIC_NAME) : GlobalConstants.EMPTY_VALUE;
    }

    public String getMetricObjectName(long id)
    {
        return items.containsKey(id) ? ObjectConfigStore.getStore().getObjectName(items.get(id).getLong(Metric.METRIC_OBJECT)) : GlobalConstants.EMPTY_VALUE;
    }

    public Map<String, JsonObject> getObjects(long id)
    {
        Map<String, JsonObject> result = null;

        if (existItem(id))
        {
            result = getObjects(getItem(id).getJsonObject(Metric.METRIC_CONTEXT));
        }

        return result;
    }

    public JsonObject getObjects(long id, String key, String value)
    {
        var result = new JsonObject();

        if (existItem(id))
        {
            result = getObjects(getItem(id).getJsonObject(Metric.METRIC_CONTEXT), key, value);

        }

        return result;
    }

    public JsonObject getObjects(JsonObject context, String key, String value)
    {
        var result = new JsonObject();

        if (context != null && context.containsKey(NMSConstants.OBJECTS))
        {
            var objects = context.getJsonArray(NMSConstants.OBJECTS);

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                if (object.getValue(key) != null && object.getValue(value) != null)
                {
                    result.put(object.getString(key), object.getValue(value));
                }

            }
        }

        return result;
    }

    public Map<String, JsonObject> getObjects(JsonObject context)
    {
        Map<String, JsonObject> result = null;

        if (context != null && context.containsKey(NMSConstants.OBJECTS))
        {
            var objects = context.getJsonArray(NMSConstants.OBJECTS);

            result = new HashMap<>();

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                result.put(object.getString(AIOpsObject.OBJECT_NAME), object);
            }
        }

        return result;
    }


    public JsonArray getObjects(NMSConstants.Type type)
    {
        var items = new JsonArray();

        this.items.forEach((key, value) ->
        {

            var context = value.getJsonObject(Metric.METRIC_CONTEXT);

            if (context != null && context.containsKey(NMSConstants.OBJECTS))
            {
                var objects = context.getJsonArray(NMSConstants.OBJECTS);

                for (var index = 0; index < objects.size(); index++)
                {
                    if (objects.getJsonObject(index).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(type.getName()))
                    {
                        items.add(objects.getJsonObject(index).put(ID, key));
                    }
                }
            }

        });

        return items;
    }

    public JsonArray getItemsByObjectId(long id)
    {
        return metricsByObject.containsKey(id) ? metricsByObject.get(id).copy() : new JsonArray();
    }

    public void deleteItemByObject(long id)
    {
        var metric = items.remove(id);

        if (metric != null)
        {
            if (metricsByObject.containsKey(metric.getLong(Metric.METRIC_OBJECT)))
            {
                var iterator = metricsByObject.get(metric.getLong(Metric.METRIC_OBJECT)).iterator();

                while (iterator.hasNext())
                {
                    if (JsonObject.mapFrom(iterator.next()).getLong(ID).equals(id))
                    {
                        iterator.remove();
                    }
                }
            }

            if (instancesByObject.containsKey(metric.getLong(Metric.METRIC_OBJECT)))
            {
                if (NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.containsKey(metric.getString(Metric.METRIC_PLUGIN)))
                {
                    instancesByObject.get(metric.getLong(Metric.METRIC_OBJECT)).remove(NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.get(metric.getString(Metric.METRIC_PLUGIN)));
                }

                if (NMSConstants.APPLICATION_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)) && instancesByObject.get(metric.getLong(Metric.METRIC_OBJECT)).getJsonArray(NMSConstants.APPS) != null)
                {
                    var iterator = instancesByObject.get(metric.getLong(Metric.METRIC_OBJECT)).getJsonArray(NMSConstants.APPS).iterator();

                    while (iterator.hasNext())
                    {
                        if (metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(CommonUtil.getString(iterator.next())))
                        {
                            iterator.remove();
                        }
                    }
                }
            }

            var pluginId = metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(GlobalConstants.PLUGIN_ID);

            if (instancesByPlugin.containsKey(pluginId) && !instancesByPlugin.get(pluginId).isEmpty())
            {
                instancesByPlugin.get(pluginId).remove(CommonUtil.getString(metric.getLong(Metric.METRIC_OBJECT)));
            }

            if (NMSConstants.APPLICATION_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)))
            {
                var object = ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT));

                if (object != null)
                {
                    if (metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT) != null)
                    {
                        if (apps.get(metric.getLong(Metric.METRIC_OBJECT)).containsKey(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT).getString(AIOpsObject.OBJECT_NAME)))
                        {
                            apps.get(metric.getLong(Metric.METRIC_OBJECT)).remove(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT).getString(AIOpsObject.OBJECT_NAME));
                        }
                    }

                    for (var dependencyType : AIOpsConstants.DependencyType.values())
                    {
                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType.getName(),
                                new JsonObject().mergeIn(object).put(AIOpsConstants.DEPENDENCY_OPERATION, AIOpsConstants.DependencyOperation.REMOVE.getName())
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.LEVELS_BY_APPLICATIONS.get(metric.getString(Metric.METRIC_TYPE)))
                                        .put(AIOpsConstants.DEPENDENCY_FILTER, metric.getString(Metric.METRIC_TYPE)) // application name
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP))
                                        .put(AIOpsConstants.DEPENDENCY_TYPE, dependencyType.getName())
                                        .put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, metric.getString(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD)));
                    }
                }
            }
        }
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                updateItems(items.get(id), true, false);
            }
        });
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                updateItems(items.get(id), true, true);
            }
        });
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_METRIC, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    metricsByObject.clear();

                    instancesByObject.clear();

                    instancesByPlugin.clear();

                    apps.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                            }

                            if (!metricsByObject.containsKey(item.getLong(Metric.METRIC_OBJECT)))
                            {
                                metricsByObject.put(item.getLong(Metric.METRIC_OBJECT), new JsonArray());
                            }

                            metricsByObject.get(item.getLong(Metric.METRIC_OBJECT)).add(item);

                            if (item.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                            {
                                var object = ObjectConfigStore.getStore().getItem(item.getLong(Metric.METRIC_OBJECT));

                                if (object != null && object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && !item.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.INLINE.name()))
                                {
                                    MetricCacheStore.getStore().addMetric(item.getLong(ID), item.getInteger(Metric.METRIC_POLLING_TIME));
                                }
                            }

                            updateItems(item, false, true);
                        }
                    }

                    TagCacheStore.getStore().initStore();

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    private void update(long id)
    {
        metricsByObject.put(id, MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_OBJECT, id));
    }

    private void updateItems(JsonObject item, boolean update, boolean init)
    {
        try
        {
            if (update)
            {
                update(item.getLong(Metric.METRIC_OBJECT));
            }

            var pluginId = item.getJsonObject(Metric.METRIC_CONTEXT).getInteger(GlobalConstants.PLUGIN_ID);

            instancesByObject.computeIfAbsent(item.getLong(Metric.METRIC_OBJECT), value -> new JsonObject());

            instancesByPlugin.computeIfAbsent(pluginId, value -> new JsonObject());

            var instances = instancesByObject.get(item.getLong(Metric.METRIC_OBJECT));

            var pluginInstances = this.instancesByPlugin.get(pluginId);

            if (NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.containsKey(item.getString(Metric.METRIC_PLUGIN)) && item.getJsonObject(Metric.METRIC_CONTEXT) != null
                    && item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
            {
                instances.put(NMSConstants.DISCOVERABLE_INSTANCE_PLUGINS.get(item.getString(Metric.METRIC_PLUGIN)),
                        new JsonArray(item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).stream().map(object -> JsonObject.mapFrom(object).getValue(AIOpsObject.OBJECT_NAME)).toList()));

                pluginInstances.put(CommonUtil.getString(item.getLong(Metric.METRIC_OBJECT)), item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).stream().map(object -> CommonUtil.getString(item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()) ? JsonObject.mapFrom(object).getValue(NMSConstants.INTERFACE) : JsonObject.mapFrom(object).getValue(AIOpsObject.OBJECT_NAME))).toList());

            }

            // in case of cisco ipsla, we are dumping all data in main plugin i.e. ipsla so we also need to add instance to ipsla plugin
            if (NMSConstants.isWANLinkMetric(item.getString(Metric.METRIC_PLUGIN)))
            {
                this.instancesByPlugin.computeIfAbsent(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.IPSLA.getName()), value -> new JsonObject());

                pluginInstances = this.instancesByPlugin.get(ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(NMSConstants.MetricPlugin.IPSLA.getName()));

                var items = new ArrayList<String>();

                NMSConstants.IPSLA_METRIC_PLUGINS.forEach(plugin ->
                {
                    var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(item.getLong(Metric.METRIC_OBJECT), plugin));

                    if (metric != null && metric.getJsonObject(Metric.METRIC_CONTEXT).containsKey(NMSConstants.OBJECTS))
                    {
                        var objects = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

                        for (var index = 0; index < objects.size(); index++)
                        {
                            items.add(objects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME));
                        }
                    }
                });

                pluginInstances.put(CommonUtil.getString(item.getLong(Metric.METRIC_OBJECT)), items);
            }

            //#24692 add plugin id when generate same kind of issue again
            //for bug of ruckus wireless which also polls interface multiple instances in one plugin so by passing plugin with empty value..
            if (NMSConstants.DUMMY_INSTANCE_PLUGINS.contains(item.getString(Metric.METRIC_PLUGIN)))
            {
                //as in passover instance plugins is where for visualization manager entity keys is required but in this case it does not have instance data upfront so will have to add blank value
                //so from db it will be able to prepare keys data will be retrieved..
                pluginInstances.put(CommonUtil.getString(item.getLong(Metric.METRIC_OBJECT)), new JsonArray());
            }

            if (item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
            {
                // discovered interfaces
                var discoveredObjects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.DISCOVERED_OBJECTS);

                if (discoveredObjects != null && !discoveredObjects.isEmpty())
                {
                    instances.put(NMSConstants.DISCOVERED_OBJECTS, new JsonArray(discoveredObjects.stream().map(object -> JsonObject.mapFrom(object).getString(NMSConstants.INTERFACE)).toList()));
                }
            }

            if (NMSConstants.APPLICATION_PLUGINS.contains(item.getString(Metric.METRIC_PLUGIN)))
            {
                if (!instances.containsKey(NMSConstants.APPS))
                {
                    instances.put(NMSConstants.APPS, new JsonArray());
                }

                if (!instances.getJsonArray(NMSConstants.APPS).contains(item.getString(Metric.METRIC_TYPE)))
                {
                    instances.getJsonArray(NMSConstants.APPS).add(item.getString(Metric.METRIC_TYPE));
                }

                if (item.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT) != null)
                {
                    if (apps.containsKey(item.getLong(Metric.METRIC_OBJECT)))
                    {
                        apps.get(item.getLong(Metric.METRIC_OBJECT)).put(item.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT).getString(AIOpsObject.OBJECT_NAME), item.getString(Metric.METRIC_TYPE));
                    }
                    else
                    {
                        apps.put(item.getLong(Metric.METRIC_OBJECT), new JsonObject().put(item.getJsonObject(Metric.METRIC_CONTEXT).getJsonObject(NMSConstants.OBJECT).getString(AIOpsObject.OBJECT_NAME), item.getString(Metric.METRIC_TYPE)));
                    }
                }

            }

            ObjectConfigStore.getStore().updateItemPlugin(pluginId, item.getLong(Metric.METRIC_OBJECT));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public long getItemByMetricPlugin(long value, String field)
    {
        return metricsByObject.containsKey(value) ? metricsByObject.get(value).stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(field))
                .map(item -> JsonObject.mapFrom(item).getLong(ID))
                .findFirst().orElse(CommonUtil.getLong(GlobalConstants.NOT_AVAILABLE)) : GlobalConstants.NOT_AVAILABLE;
    }

    public void deleteMetricObject(long id, long objectId)
    {
        metricsByObject.remove(id);

        instancesByObject.remove(id);

        apps.remove(id);

        for (var item : instancesByPlugin.values())
        {
            if (item.containsKey(CommonUtil.getString(id)))
            {
                item.remove(CommonUtil.getString(id));
            }
        }

        TagCacheStore.getStore().deleteTags(objectId);
    }

    public List<JsonObject> flatItemsByMultipleValues(String field, Object value, String flatField, JsonArray flatValues)
    {
        return items.values().parallelStream()
                .filter(item -> item.getValue(field).equals(value) && flatValues.contains(item.getValue(flatField)))
                .map(JsonObject::copy)
                .collect(Collectors.toList());
    }

    @Override
    public Future<Void> updateItems(JsonArray ids)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var oldItems = MetricConfigStore.getStore().getItems(ids);

            var oldItem = new HashMap<Long, String>();

            if (oldItems != null)
            {
                for (var index = 0; index < oldItems.size(); index++)
                {
                    oldItem.put(oldItems.getJsonObject(index).getLong(ID), oldItems.getJsonObject(index).getString(Metric.METRIC_STATE));
                }
            }

            Bootstrap.configDBService().get(DBConstants.TBL_METRIC,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                    result ->
                    {
                        if (result.succeeded())
                        {
                            for (var index = 0; index < result.result().size(); index++)
                            {
                                try
                                {
                                    var metric = result.result().getJsonObject(index);

                                    this.items.put(metric.getLong(ID), metric);

                                    if (oldItem.get(metric.getLong(ID)) != null)
                                    {
                                        if (oldItem.get(metric.getLong(ID)).equalsIgnoreCase(NMSConstants.State.DISABLE.name()) &&
                                                metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                                        {
                                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENABLE, metric);
                                        }
                                        else if (oldItem.get(metric.getLong(ID)).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) &&
                                                metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.DISABLE.name()))
                                        {
                                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_DISABLE, metric);
                                        }
                                        else if (metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) && !metric.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.INLINE.name()))
                                        {
                                            MetricCacheStore.getStore().addMetric(metric.getLong(ID), metric.getInteger(Metric.METRIC_POLLING_TIME));
                                        }
                                    }

                                    var metrics = metricsByObject.get(metric.getLong(Metric.METRIC_OBJECT));

                                    if (metrics != null && !metrics.isEmpty())
                                    {
                                        for (var i = 0; i < metrics.size(); i++)
                                        {
                                            if (metrics.getJsonObject(i).getLong(ID).equals(metric.getLong(ID)))
                                            {
                                                metrics.getJsonObject(i).getMap().putAll(metric.getMap());
                                            }
                                        }
                                    }

                                    updateItems(metric, false, false);
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            promise.complete();
                        }

                        else
                        {
                            promise.fail(result.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    public JsonArray getMetricTypes()
    {
        return new JsonArray(this.items.values().parallelStream()
                .filter(item -> !NMSConstants.CUSTOM_METRIC_TYPES.contains(item.getString(Metric.METRIC_TYPE)))
                .map(item -> item.getString(Metric.METRIC_TYPE)).distinct().collect(Collectors.toList()));
    }

    // reason to return List<JsonObject> is that it is only used for manipulation and instances are always in bulk size and JsonArray lead to multiple time JsonObject Map from so to avoid that multiple copy creation will pass List<JsonObject>
    public List<JsonObject> getInstanceObjectsByPlugins(Set<Integer> objects, JsonArray pluginIds)
    {
        var items = new ArrayList<JsonObject>();

        pluginIds.forEach(pluginId ->
        {
            if (instancesByPlugin.containsKey(CommonUtil.getInteger(pluginId)))
            {
                instancesByPlugin.get(CommonUtil.getInteger(pluginId)).getMap().entrySet().stream().filter(item -> objects.contains(ObjectConfigStore.getStore().getObjectId(CommonUtil.getLong(item.getKey())))).forEach(item -> items.add(new JsonObject().put(AIOpsObject.OBJECT_ID, ObjectConfigStore.getStore().getObjectId(CommonUtil.getLong(item.getKey()))).put(GlobalConstants.PLUGIN_ID, pluginId).put(NMSConstants.OBJECTS, item.getValue())));
            }
        });

        return items;
    }

    // used to get plugin wise provisioned instances for specified object
    public JsonObject getInstancesByPlugins(long id, JsonArray plugins)
    {
        var items = new JsonObject();

        plugins.forEach(plugin ->
        {
            var pluginId = ObjectManagerCacheStore.getStore().getPluginIdByMetricPlugin(plugin.toString());

            if (instancesByPlugin.containsKey(pluginId))
            {
                for (var entry : instancesByPlugin.get(pluginId))
                {
                    if (CommonUtil.getLong(entry.getKey()) == id)
                    {
                        items.put(plugin.toString(), entry.getValue());
                    }
                }
            }
        });

        return items;
    }

    public JsonArray getNetworkInterfacesBySource(String ipAddress)
    {
        var id = ObjectConfigStore.getStore().getItemByIP(ipAddress);

        if (id != null)
        {
            var objects = MetricConfigStore.getStore().getItemsByObjectId(id);

            var items = new JsonArray();

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                if (NMSConstants.RediscoverJob.NETWORK_INTERFACE.getName().equalsIgnoreCase(object.getString(Metric.METRIC_NAME)) &&
                        object.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null)
                {
                    items.addAll(object.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));
                }
            }

            return items;
        }

        return new JsonArray();
    }

    public JsonArray getMetricPluginsByObjects(JsonArray objects)
    {
        var items = new JsonArray();

        objects.forEach(objectId -> metricsByObject.get(CommonUtil.getLong(objectId)).forEach(metric -> items.add(JsonObject.mapFrom(metric).getJsonObject(Metric.METRIC_CONTEXT).getInteger(GlobalConstants.PLUGIN_ID))));

        return items;
    }

    public JsonArray getMetricNamesByObject(long id)
    {
        var items = new JsonArray();

        metricsByObject.get(id).forEach(metric -> items.add(JsonObject.mapFrom(metric).getString(Metric.METRIC_NAME)));

        return items;
    }

    public int getProvisionedInstances(String key)
    {
        return instancesByObject.values().stream().mapToInt(instance -> instance.containsKey(key) ? instance.getJsonArray(key).size() : 0).sum();
    }

    //#25690
    // this method is used to map vm's ip to its name
    public void getObjectsByMetricPlugin(long id, String metricPlugin, JsonObject items)
    {
        if (!metricPlugin.equalsIgnoreCase(EMPTY_VALUE))
        {
            var context = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(id, metricPlugin)).getJsonObject(Metric.METRIC_CONTEXT);

            if (context.containsKey(NMSConstants.OBJECTS))
            {
                for (var index = 0; index < context.getJsonArray(NMSConstants.OBJECTS).size(); index++)
                {
                    var item = context.getJsonArray(NMSConstants.OBJECTS).getJsonObject(index);

                    if (CommonUtil.isNotNullOrEmpty(item.getString(AIOpsObject.OBJECT_NAME)) && CommonUtil.isNotNullOrEmpty(item.getString(AIOpsObject.OBJECT_IP)))
                    {
                        items.put(item.getString(AIOpsObject.OBJECT_IP), item.getString(AIOpsObject.OBJECT_NAME));
                    }
                }
            }
        }
    }

    public String getMetricName(int id)
    {
        return ObjectManagerCacheStore.getStore().getMetricName(id);
    }

    /*
     *  method returns total count of instances(vm, access.point, interfaces, process) in monitor
     * */
    public int getInstances(long objectId)
    {
        var count = 0;

        if (instancesByObject.containsKey(objectId))
        {
            var instances = instancesByObject.get(objectId);

            for (var instance : instances)
            {
                if (instance.getKey().contains(NMSConstants.ACCESS_POINTS))
                {
                    count = instances.getJsonArray(NMSConstants.ACCESS_POINTS).size();

                    return count;

                }
                else if (instance.getKey().contains(NMSConstants.INTERFACES))
                {
                    count = instances.getJsonArray(NMSConstants.INTERFACES).size();

                    return count;
                }
                else if (instance.getKey().contains(NMSConstants.VMS))
                {
                    count = instances.getJsonArray(NMSConstants.VMS).size();

                    return count;
                }
                else if (instance.getKey().contains(NMSConstants.PROCESSES))
                {
                    count = instances.getJsonArray(NMSConstants.PROCESSES).size();

                    return count;
                }
            }
        }

        return count;
    }
}
