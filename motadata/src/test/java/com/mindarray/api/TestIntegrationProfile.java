/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.REQUEST_PARAM_TYPE;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestIntegrationProfile
{
    private static final JsonObject context = new JsonObject();

    private static final Logger LOGGER = new Logger(TestIntegrationProfile.class, MOTADATA_API, "Test Integration Profile");

    private static MessageConsumer<JsonObject> messageConsumer = null;

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetDefaultServiceNowEventIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(INTEGRATION_PROFILE_API_ENDPOINT + "/" + DEFAULT_ID, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETRequestTestResult(response, DEFAULT_ID, IntegrationProfileConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testDeleteDefaultServiceNowEventIntegrationProfile(VertxTestContext testContext)
    {
        TestAPIUtil.delete(INTEGRATION_PROFILE_API_ENDPOINT + "/" + DEFAULT_ID, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

            Assertions.assertTrue(result.containsKey(MESSAGE));

            Assertions.assertTrue(String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, APIConstants.Entity.INTEGRATION_PROFILE.getName()).equalsIgnoreCase(result.getString(MESSAGE)));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateDefaultServiceNowEventIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"integration.profile.context\": { \"category\": \"network\", \"subcategory\": \"ip address\", \"assignment_group\": \"8a5055c9c61122780043563ef53438e3\", \"assigned_to\": \"46d44a23a9fe19810012d100cca80666\", \"impact\": \"1be6c35bd12302104f34ba60d8b7c141\", \"urgency\": \"53e6c35bd12302104f34ba60d8b7c140\", \"business_service\": \"26da329f0a0a0bb400f69d8159bc753d\", \"auto.close.ticket.status\": \"yes\" }}");

        TestAPIUtil.put(INTEGRATION_PROFILE_API_ENDPOINT + "/" + DEFAULT_ID, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(IntegrationProfileConfigStore.getStore(), payload, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"integration.profile.name\": \"DemoServiceNowIntegrationProfile\", \"integration\": 10000000000002, \"integration.profile.context\": { \"category\": \"network\", \"subcategory\": \"ip address\", \"assignment_group\": \"8a5055c9c61122780043563ef53438e3\", \"assigned_to\": \"46d44a23a9fe19810012d100cca80666\", \"impact\": \"1be6c35bd12302104f34ba60d8b7c141\", \"urgency\": \"53e6c35bd12302104f34ba60d8b7c140\", \"business_service\": \"26da329f0a0a0bb400f69d8159bc753d\", \"auto.close.ticket.status\": \"yes\" }}");

        TestAPIUtil.post(INTEGRATION_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(IntegrationProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            context.put(ID, response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllIntegrationProfiles(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_PROFILE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, IntegrationProfileConfigStore.getStore(), null);

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testAssignIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{ \"policy.actions\": { \"Integration\": { \"CRITICAL\": [ { \"id\": " + context.getLong(ID) + " } ] } } }");

        TestAPIUtil.put(METRIC_POLICY_API_ENDPOINT + "/" + DEFAULT_ID, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(MetricPolicyConfigStore.getStore(), payload, response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetIntegrationProfileUsedCount(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(INTEGRATION_PROFILE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityUsedCountTestResult(response, context.getLong(ID), 1, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetIntegrationProfileReferences(VertxTestContext testContext)
    {
        TestAPIUtil.get(INTEGRATION_PROFILE_API_ENDPOINT + "/" + context.getLong(ID) + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.METRIC_POLICY.getName(), 1);

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDeleteIntegrationProfileInUse(VertxTestContext testContext)
    {
        TestAPIUtil.delete(INTEGRATION_PROFILE_API_ENDPOINT + "/" + context.getLong(ID), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, APIConstants.Entity.INTEGRATION_PROFILE.getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateServiceOpsIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{ \"integration.profile.name\": \"DemoServiceOpsIntegrationProfile\", \"integration\": 10000000000001, \"integration.profile.context\": { \"urgencyName\": \"Low\", \"impactName\": \"On department\", \"auto.close.ticket.status\": \"yes\", \"ticket.status\": \"resolved\" } }");

        TestAPIUtil.post(INTEGRATION_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(IntegrationProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            context.put(ID, response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testTestMicrosoftTeamsIntegrationProfileInvalid(VertxTestContext testContext)
    {
        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000003L, new JsonObject("{\n  \"integration.type\": \"Microsoft Teams\",\n  \"integration.context\": {\n    \"integration.credential.profile\": 9999999999,\n    \"timeout\": 60,\n    \"auto.sync\": \"yes\",\n    \"sync.interval\": 4,\n    \"target\": \"https://graph.microsoft.com/v1.0/\"\n  },\n  \"integration.attributes\": {},\n  \"id\": 10000000000003,\n  \"_type\": \"0\"\n}"), response ->
        {
            if (response.succeeded())
            {
                try
                {
                    TestUtil.vertx().setTimer(10_000, timer ->
                    {
                        var context = new JsonObject().put(EVENT_TYPE, UI_INTEGRATION_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                                .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                                .put(IntegrationProfile.INTEGRATION, 10000000000003L)
                                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()))
                                .put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, new JsonObject("{\n    \"handle.name\": \"/Microsoft Integration Profile\",\n    \"team\": \"3e2d0d24-c3f7-4ad2-b278-6f110ba65f3c\",\n    \"channel.type\": \"public\",\n    \"channel\": \"19:IjNPCDcz1bHSUd_O6q7gZTF_3ylPo4JEdbJu1i-t6dI1@thread.tacv2\"\n}"));

                        assertIntegrationProfileTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND);

                        TestUtil.vertx().eventBus().send(UI_INTEGRATION_PROFILE_TEST, context);
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception);
                }
            }
            else
            {
                LOGGER.error(response.cause());

                testContext.failNow(response.cause());
            }
        });
    }

    /**
     * This test is currently disabled because the Microsoft Teams subscription has expired.
     * Previous build screenshots indicate that this test was running successfully at that time.
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testTestMicrosoftTeamsIntegrationProfile(VertxTestContext testContext)
    {
        var credential = TestConstants.prepareParams("testMicrosoftTeamCredentialProfile");

        Assertions.assertNotNull(credential);

        credential.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credential.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                var payload = new JsonObject("{\n  \"integration.type\": \"Microsoft Teams\",\n  \"integration.context\": {\n    \"integration.credential.profile\": 64238975866,\n    \"timeout\": 60,\n    \"auto.sync\": \"yes\",\n    \"sync.interval\": 4,\n    \"target\": \"https://graph.microsoft.com/v1.0/\"\n  },\n  \"integration.attributes\": {},\n  \"id\": 10000000000003,\n  \"_type\": \"0\"\n}");

                if (credentialId != null && credentialId != 0)
                {
                    payload.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialId);
                }

                TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000003L, payload, response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            testContext.awaitCompletion(5, TimeUnit.SECONDS);

                            var context = new JsonObject().put(EVENT_TYPE, UI_INTEGRATION_PROFILE_TEST).put(REQUEST_PARAM_TYPE, APIConstants.REQUEST_CREATE)
                                    .put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                                    .put(IntegrationProfile.INTEGRATION, 10000000000003L)
                                    .put(EventBusConstants.EVENT_CONTEXT, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()))
                                    .put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, new JsonObject("{\n    \"handle.name\": \"/Microsoft Integration Profile\",\n    \"team\": \"e9fd04d0-db82-4dec-82af-3e2f887cb76c\",\n    \"channel.type\": \"public\",\n    \"channel\": \"19:_lITuo4V2uJ1nLSvxldRgfAzYtrpieaAW3P66v39j1c1@thread.tacv2\"\n}"));

                            assertIntegrationProfileTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

                            TestUtil.vertx().eventBus().send(UI_INTEGRATION_PROFILE_TEST, context);
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateMicrosoftTeamsIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\n    \"integration.profile.name\": \"DemoMicrosoftTeamsIntegrationProfile\",\n    \"integration\": 10000000000003,\n    \"integration.profile.context\": {\n        \"handle.name\": \"/DemoMicrosoftTeamsIntegrationProfile\",\n        \"team\": \"547fa76e-64dc-4182-92fe-f27c79628a03\",\n        \"channel.type\": \"private\",\n        \"channel\": \"19:49f636af7b564b3786272afca6c11858@thread.tacv2\"\n    }\n}");

        payload.put(IntegrationProfile.INTEGRATION_PROFILE_NAME, payload.getString(IntegrationProfile.INTEGRATION_PROFILE_NAME) + System.currentTimeMillis());

        TestAPIUtil.post(INTEGRATION_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(IntegrationProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            context.put(ID, response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testCreateAtlassianJiraIntegrationProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var payload = new JsonObject("{\"integration.profile.name\":\"DemoAtlassianJiraIntegrationProfile\",\"integration.profile.description\":\"My own Jira\",\"integration\":10000000000004,\"integration.profile.context\":{\"auto.close.ticket.status\":\"yes\",\"auto.close.ticket.state\":\"pending\",\"alert.reoccurrence.status\":\"done\",\"project\":{\"id\":\"10000\"},\"issuetype\":{\"id\":\"10202\"},\"components\":[{\"id\":\"10100\"}],\"assignee\":{\"name\":\"Administrator\"},\"reporter\":{\"name\":\"Administrator\"},\"priority\":{\"id\":\"3\"},\"labels\":[\"8.0\",\"aiops\"]}}");

        TestAPIUtil.post(INTEGRATION_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(IntegrationProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.INTEGRATION_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            context.put(ID, response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    private void assertIntegrationProfileTestResult(VertxTestContext testContext, String status, String errorCode)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (context.containsKey(EVENT_STATE) && context.getString(EVENT_STATE).equalsIgnoreCase(EVENT_STATE_COMPLETED) && context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(UI_INTEGRATION_PROFILE_TEST))
                {
                    Assertions.assertEquals(context.getString(STATUS), status);

                    if (errorCode != null)
                    {
                        Assertions.assertEquals(context.getString(ERROR_CODE), errorCode);
                    }

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister(result -> testContext.failNow(exception));

            }
        });
    }
}
