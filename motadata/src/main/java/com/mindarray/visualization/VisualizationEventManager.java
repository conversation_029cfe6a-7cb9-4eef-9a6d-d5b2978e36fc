/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date          Author            Notes
 *   3-Mar-2025      Deven        MOTADATA-4973: Converted long object.id into the small object.id in RUNBOOK_WORKLOG request.
 *   5-Mar-2025      Chaitas      MOTADATA-4074 : Stream Widget | When editing the stream widget, data does not appear.
 *   5-Mar-2025      Chaitas      MOTADATA-4334 : The system failed to display the actual preview based on the applied filter conditions in the Event History widget preview.
 *   10-Mar-2025     Pruthviraj   MOTADATA-5331 : NetRoute alert related code added for policy,policy.flap,policy stream
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 * */

package com.mindarray.visualization;

/**
 * Specialized manager for processing event data visualization requests.
 * <p>
 * VisualizationEventManager is responsible for:
 * <ul>
 *   <li>Processing visualization requests for event data (alerts, logs, traps, etc.)</li>
 *   <li>Retrieving event data from the datastore based on specified criteria</li>
 *   <li>Formatting event data for different visualization types (grids, charts, streams)</li>
 *   <li>Managing streaming event data for real-time visualizations</li>
 *   <li>Filtering events based on severity, source, category, and other attributes</li>
 *   <li>Handling event history and correlation for visualization purposes</li>
 * </ul>
 * <p>
 * This class maintains several maps to track query status, execution time, and query contexts.
 * It supports various event visualization types including event grids, event streams, and
 * event history visualizations.
 * <p>
 * The manager communicates with the datastore to retrieve event data and processes it
 * according to the visualization requirements specified in the request. It also handles
 * special cases like NetRoute events and policy-related events.
 */

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.datastore.DatastoreConstants.PluginId.POLICY_NETROUTE;
import static com.mindarray.datastore.DatastoreConstants.PluginId.POLICY_RESULT;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationEventManager extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(VisualizationEventManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Event Manager");

    /**
     * Stores column definitions for event data
     */
    private final JsonObject eventColumns = new JsonObject();

    /**
     * Maps event source types to their categories
     * Key: source type (e.g., "LOG"), Value: Set of categories
     */
    private final Map<String, Set<String>> eventCategories = new HashMap<>();

    /**
     * Maps query IDs to their subquery IDs and status information
     * Key: queryId, Value: Map of (subQueryId -> status)
     */
    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();

    /**
     * Stores context information for subqueries
     * Key: subQueryId, Value: subquery context
     */
    private final Map<Long, JsonObject> subQueries = new HashMap<>();

    /**
     * Stores original request context for queries
     * Key: queryId, Value: original query context
     */
    private final Map<Long, JsonObject> queryContexts = new HashMap<>();

    /**
     * Stores results from external queries
     * Key: queryId, Value: query result
     */
    private final Map<Long, JsonObject> queryResults = new HashMap<>();

    /**
     * Stores UI context information for queries
     * Key: queryId, Value: UI context
     */
    private final Map<Long, JsonObject> queryUIContexts = new HashMap<>();

    /**
     * Maps query IDs to their string representations
     * Key: queryId, Value: query string
     */
    private final Map<Long, String> queries = new HashMap<>();

    /**
     * Maps query IDs for streaming queries
     * Key: queryId, Value: Map of (subQueryId -> status)
     */
    private final Map<Long, Map<Long, String>> streamingQueries = new HashMap<>();

    /**
     * Tracks progress of queries
     * Key: queryId, Value: progress percentage (0-100)
     */
    private final Map<Long, Short> queryTrackers = new HashMap<>();

    /**
     * Tracks progress of subqueries
     * Key: queryId, Value: Map of (subQueryId -> progress)
     */
    private final Map<Long, Map<Long, Short>> subQueryTrackers = new HashMap<>();

    /**
     * Stores custom or join queries that require special processing
     * Key: queryId, Value: Map of (key -> query context)
     * Used for queries that need result decoding for manipulation and adding special columns
     */
    private final Map<Long, Map<String, JsonObject>> customQueries = new HashMap<>();

    /**
     * Maps main custom subqueries to their parent queries
     * Key: queryId, Value: List of subQueryIds for the main custom subqueries
     */
    private final Map<Long, List<Long>> customSubQueries = new HashMap<>();

    /**
     * List of currently running query IDs
     */
    private final List<Long> runningQueries = new ArrayList<>();

    /**
     * List of query IDs waiting to be processed
     */
    private final List<Long> queuedQueries = new ArrayList<>();

    /**
     * Stores context information for queued queries
     * Key: queryId, Value: query context
     */
    private final Map<Long, JsonObject> queuedQueryContexts = new HashMap<>();

    /**
     * Tracks query timeouts in seconds
     * Key: queryId, Value: remaining time in seconds before query is aborted
     */
    private final Map<Long, Integer> queryTickers = new HashMap<>();

    /**
     * Reusable StringBuilder for constructing query strings and error messages
     */
    private final StringBuilder builder = new StringBuilder();

    /**
     * Set of object IDs being processed
     */
    private final Set<Integer> objectIds = new HashSet<>();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            VisualizationConstants.loadCategories(eventCategories, LOGGER);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, "", reply ->
            {
                if (reply.succeeded())
                {
                    eventColumns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS));

                    var ids = ObjectConfigStore.getStore().getObjectIds();

                    for (var i = 0; i < ids.size(); i++)
                    {
                        objectIds.add(ids.getInteger(i));
                    }

                    //invalid queries kindly remove it as response already received or query aborted
                    vertx.setPeriodic(30 * 1000L, periodicTimer ->
                    {
                        var iterator = queryTickers.entrySet().iterator();

                        while (iterator.hasNext())
                        {
                            var entry = iterator.next();

                            entry.setValue(entry.getValue() - 30);

                            if (entry.getValue() <= 0)
                            {
                                vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                        .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                        .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, entry.getKey()).encode().getBytes()).getBytes()));

                                if (queryStatuses.containsKey(entry.getKey()))
                                {
                                    for (var subQueryId : queryStatuses.get(entry.getKey()).keySet())
                                    {
                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));

                                        Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));
                                    }
                                }

                                iterator.remove();
                            }
                        }

                        if (runningQueries.removeIf(query -> !queries.containsKey(query)) && !queuedQueries.isEmpty())
                        {
                            send();
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
                    {
                        try
                        {
                            var event = message.body();

                            var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                            if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                            {
                                VisualizationConstants.update(eventColumns, tokens, false);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<String>localConsumer(EVENT_EVENT_CATEGORY_UPDATE, message ->
                    {
                        try
                        {
                            if (!message.body().isEmpty())
                            {
                                var tokens = message.body().split(SEPARATOR_WITH_ESCAPE);

                                eventCategories.computeIfAbsent(tokens[0], value -> new HashSet<>()).add(tokens[1]);
                            }
                        }
                        catch (Exception exception)
                        {
                            message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                            LOGGER.error(exception);
                        }
                    }).exceptionHandler(LOGGER::error);

                    vertx.eventBus().<byte[]>localConsumer(EVENT_DATASTORE_QUERY_RESPONSE, message ->
                    {
                        try
                        {

                            var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                            var widgetId = 0L;

                            var queryId = buffer.getLongLE(0);

                            var subQueryId = buffer.getLongLE(8);

                            var subQueryCalculatedProgress = buffer.getUnsignedByte(16);

                            var subQueryProgress = buffer.getUnsignedByte(16);

                            var status = buffer.getUnsignedByte(33);

                            var validResult = true;

                            //there are certain times it happens due to ZMQ 50 percent progress is received after 100 so ignoring 50 percent result
                            if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                            {
                                subQueryTrackers.computeIfAbsent(queryId, value -> new HashMap<>()).computeIfAbsent(subQueryId, val -> subQueryCalculatedProgress);

                                if (subQueryTrackers.get(queryId).get(subQueryId) <= subQueryCalculatedProgress)
                                {
                                    subQueryTrackers.get(queryId).put(subQueryId, subQueryCalculatedProgress);
                                }
                                else
                                {
                                    validResult = false;
                                }
                            }

                            if (validResult)
                            {
                                if (!customQueries.containsKey(queryId) && queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                                {
                                    var succeeded = true;

                                    short queryProgress = 0;

                                    if (queryStatuses.get(queryId).size() > 1)
                                    {
                                        queryProgress = CommonUtil.getShort(subQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                                        buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                                        queryTrackers.put(queryId, queryProgress);
                                    }

                                    else
                                    {
                                        queryTrackers.put(queryId, subQueryCalculatedProgress);
                                    }

                                    if (queries.containsKey(queryId))
                                    {
                                        var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                                        if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                                        {
                                            widgetId = CommonUtil.getLong(tokens[0]);
                                        }
                                    }

                                    if (status == 0)//fail
                                    {
                                        var errorLength = 38 + buffer.getIntLE(34);

                                        var errorMessages = buffer.getString(38, errorLength);

                                        if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                                        {
                                            succeeded = false;
                                        }

                                        queryStatuses.get(queryId).put(subQueryId, errorMessages);

                                        vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessages)).put(STATUS, STATUS_FAIL));
                                    }
                                    else
                                    {
                                        queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                                    }

                                    EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryCalculatedProgress, queryProgress, DateTimeUtil.timestamp()));

                                    if (subQueries.containsKey(subQueryId) || queryContexts.containsKey(queryId))
                                    {
                                        var context = subQueries.containsKey(subQueryId) ? subQueries.get(subQueryId) : queryContexts.get(queryId);

                                        if (context.containsKey(EVENT_TYPE))
                                        {
                                            var publish = queryTrackers.get(queryId) >= 100;

                                            if (context.containsKey(PUBLISH_SUB_QUERY_PROGRESS) && context.getBoolean(PUBLISH_SUB_QUERY_PROGRESS))
                                            {
                                                publish = true;
                                            }

                                            if (context.containsKey(VISUALIZATION_DECODE_RESPONSE) && context.getString(VISUALIZATION_DECODE_RESPONSE).equalsIgnoreCase(YES))
                                            {
                                                Bootstrap.vertx().eventBus().send(context.getString(EVENT_TYPE), context.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS)).put(RESULT, VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId), context.getBoolean(DISCARD_DUMMY_ROWS, false))));
                                            }
                                            else
                                            {
                                                VisualizationConstants.publish(context.put(QUERY_PROGRESS, queryTrackers.get(queryId)).put("publish", publish), LOGGER, queryResults, queryUIContexts, queryContexts, queryId, buffer);
                                            }
                                        }
                                        else if (!streamingQueries.containsKey(queryId))
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));

                                            if (CommonUtil.traceEnabled())
                                            {
                                                LOGGER.trace("Response received from DB:" + VisualizationConstants.unpack(buffer, LOGGER, false, queryContexts.get(queryId), true, true));
                                            }
                                        }
                                    }

                                    if (streamingQueries.containsKey(queryId) && streamingQueries.get(queryId).size() > 1 && subQueryProgress == 100)
                                    {
                                        vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                    }

                                    if (subQueryProgress == 100)
                                    {
                                        subQueries.remove(subQueryId);
                                    }

                                    if (queryTrackers.get(queryId) >= 100)
                                    {
                                        runningQueries.remove(queryId);

                                        if (!queuedQueries.isEmpty())
                                        {
                                            send();
                                        }

                                        queryStatuses.get(queryId).forEach((key, value) ->
                                        {
                                            if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                            {
                                                builder.append(value).append(NEW_LINE);
                                            }
                                        });

                                        if (succeeded)
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                                        }

                                        if (streamingQueries.containsKey(queryId) && queryStatuses.get(queryId).size() == 1)
                                        {
                                            vertx.eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(buffer.getBytes()));
                                        }

                                        builder.setLength(0);

                                        if (queryContexts.containsKey(queryId) && queryContexts.get(queryId).containsKey(VISUALIZATION_DRILL_DOWN))
                                        {
                                            vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                                    .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                    .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, queryId).encode().getBytes()).getBytes()));

                                        }

                                        cleanUp(queryId);

                                        if (CommonUtil.traceEnabled())
                                        {
                                            //as of now dumping trace log will be having query explorer to track each and every query
                                            LOGGER.trace("Query Stats:" + new JsonObject().put("queued.background.queries", queuedQueries.size()).put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("running.background.queries", runningQueries.size())
                                                    .put("query.trackers", queryTrackers.size()).put("sub.query.trackers", subQueryTrackers.size()).put("queued.background.queries.context", queuedQueryContexts.size()).put("custom.sub.queries", customSubQueries.size()).put("query.contexts", queryContexts.size())
                                                    .put("custom.queries", customQueries.size()).put("queries", queries.size()).put("streaming.queries", streamingQueries.size()));
                                        }
                                    }
                                }
                                else
                                {
                                    if (customQueries.containsKey(queryId))
                                    {
                                        var valid = true;

                                        if (queryContexts.containsKey(queryId) && queryContexts.get(queryId).getBoolean("composite.query"))
                                        {
                                            valid = customSubQueries.get(queryId).contains(subQueryId);
                                        }

                                        if (valid)
                                        {
                                            var completes = 0;

                                            var voidResponses = 0;

                                            customQueries.get(queryId).put(CommonUtil.getString(subQueryId), VisualizationConstants.unpack(buffer, LOGGER, queryContexts.get(queryId).containsKey(JOIN_ALIAS), queryContexts.get(queryId), false, true));

                                            for (var entry : customQueries.get(queryId).entrySet())
                                            {
                                                if (entry.getValue().containsKey(VisualizationConstants.QUERY_PROGRESS) &&
                                                        (entry.getValue().getInteger(VisualizationConstants.QUERY_PROGRESS) == 100 ||
                                                                (subQueries.containsKey(subQueryId) && subQueries.get(subQueryId).containsKey(VISUALIZATION_CATEGORY) && subQueries.get(subQueryId).getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName()) || (subQueries.get(subQueryId).containsKey(JOIN_RESULT) && (subQueries.get(subQueryId).getString(JOIN_RESULT).equalsIgnoreCase("integration.ticket") || subQueries.get(subQueryId).getString(JOIN_RESULT).equalsIgnoreCase("config.event") || subQueries.get(subQueryId).getString(JOIN_RESULT).equalsIgnoreCase("log.event"))))))//in case of stream or log event raw message export widget it will be incremental result so should not wait for 100 percent send it incrementally
                                                {
                                                    completes++;

                                                    if (!entry.getValue().containsKey(RESULT))
                                                    {
                                                        voidResponses++;
                                                    }
                                                }
                                            }

                                            if (completes == customQueries.get(queryId).size())
                                            {
                                                if (customSubQueries.containsKey(queryId) && subQueries.get(customSubQueries.get(queryId).getFirst()) != null)
                                                {
                                                    vertx.eventBus().send(EVENT_VISUALIZATION_EVENT_RESPONSE_PROCESSOR, new JsonObject()
                                                            .put(QUERY_PROGRESS, subQueryTrackers.containsKey(queryId) && subQueryTrackers.get(queryId).containsKey(customSubQueries.get(queryId).getFirst()) ? subQueryTrackers.get(queryId).get(customSubQueries.get(queryId).getFirst()) : subQueryCalculatedProgress)
                                                            .put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(customSubQueries.get(queryId).getFirst()).getString(VisualizationConstants.VISUALIZATION_CATEGORY))
                                                            .put(VisualizationConstants.SUB_QUERY_ID, customSubQueries.get(queryId).getFirst())
                                                            .put(QUERY_CONTEXT, subQueries.get(customSubQueries.get(queryId).getFirst()))
                                                            .put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                                    customQueries.get(queryId).keySet().stream().filter(subQuery -> !Objects.equals(CommonUtil.getLong(subQuery), customSubQueries.get(queryId).getFirst())).forEach(subQuery -> cleanUp(queryId, CommonUtil.getLong(subQuery)));
                                                }
                                                else if (subQueries.containsKey(subQueryId) && completes == voidResponses)//means result not available need to provide error to UI
                                                {
                                                    vertx.eventBus().send(EVENT_VISUALIZATION_EVENT_RESPONSE_PROCESSOR, new JsonObject().put(QUERY_PROGRESS, 100).put(VisualizationConstants.VISUALIZATION_CATEGORY, subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_CATEGORY)).put(VisualizationConstants.SUB_QUERY_ID, subQueryId).put(QUERY_CONTEXT, subQueries.get(subQueryId)).put(RESULT, customQueries.get(queryId)).put(VisualizationConstants.QUERY_ID, queryId));

                                                    customQueries.get(queryId).keySet().stream().filter(subQuery -> !Objects.equals(CommonUtil.getLong(subQuery), subQueryId)).forEach(subQuery -> cleanUp(queryId, CommonUtil.getLong(subQuery)));
                                                }

                                                customSubQueries.remove(queryId);

                                                customQueries.remove(queryId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                    });

                    vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
                    {
                        var queryId = new AtomicLong();

                        try
                        {
                            var currentTime = System.currentTimeMillis();

                            var event = message.body();

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("Visualization Event Request received:" + event);
                            }

                            queryId.set(event.getLong(VisualizationConstants.QUERY_ID));

                            queryUIContexts.computeIfAbsent(queryId.get(), value -> new JsonObject().mergeIn(event));

                            var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                            if (event.containsKey(VisualizationConstants.VISUALIZATION_STREAMING) && (
                                    (!event.containsKey(JOIN_RESULT) || !event.getString(JOIN_RESULT).equalsIgnoreCase("log.event")) &&
                                            (!event.containsKey(VISUALIZATION_CATEGORY) || !event.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName()))))
                            {
                                streamingQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());
                            }

                            Set<Integer> qualifiedObjects;

                            JsonArray qualifiedSources;

                            var adminRole = false;

                            if (user.getLong(ID).equals(DEFAULT_ID))//default
                            {
                                qualifiedObjects = objectIds;

                                qualifiedSources = EventSourceConfigStore.getStore().flatItems(EVENT_SOURCE);

                                user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));

                                adminRole = true;
                            }
                            else
                            {
                                qualifiedObjects = ObjectConfigStore.getStore().getUniqueObjectIdsByGroups(user.getJsonArray(User.USER_GROUPS));

                                qualifiedSources = EventSourceConfigStore.getStore().flatItemsByMultiValueFieldAny(LogEngineConstants.SOURCE_GROUPS, user.getJsonArray(User.USER_GROUPS), EVENT_SOURCE);

                                if (qualifiedObjects == null)
                                {
                                    qualifiedObjects = new HashSet<>();
                                }
                            }

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                                    .put(EventBusConstants.EVENT_ID, queryId.get())
                                    .put(USER_NAME, event.getString(USER_NAME))
                                    .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                                    .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_VISUALIZATION));

                            EventBusConstants.updateEvent(queryId.get(),
                                    String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_STARTED, DateTimeUtil.timestamp()),
                                    EventBusConstants.EVENT_STATE_RUNNING);

                            var filters = event.containsKey(FILTERS) && !event.getJsonObject(FILTERS).isEmpty() ? JsonObject.mapFrom(event.remove(FILTERS)) : new JsonObject();

                            var dataSources = new JsonArray().addAll((JsonArray) event.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                            var replaceableColumn = false;

                            var visualizationDataSource = dataSources.getJsonObject(0);

                            if (event.containsKey("composite.query") && event.getBoolean("composite.query") && visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && !visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                            {
                                event.put(VisualizationConstants.VISUALIZATION_RESULT_BY, visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY));
                            }

                            if (event.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && !event.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                            {
                                for (var column : event.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY))
                                {
                                    if (VisualizationConstants.REPLACEABLE_COLUMNS.containsKey(CommonUtil.getString(column)))
                                    {
                                        replaceableColumn = true;
                                    }
                                }
                            }

                            if (event.containsKey(VisualizationConstants.JOIN_TYPE))
                            {
                                customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());

                                replaceableColumn = false;
                            }

                            if (replaceableColumn || event.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.MAP.getName()))
                            {
                                customQueries.computeIfAbsent(queryId.get(), value -> new HashMap<>());
                            }

                            var buffer = Buffer.buffer();

                            var entities = new HashMap<String, Object>();

                            var filteredGroupEntities = new HashSet<>();

                            var filteredTagEntities = new HashSet<>();

                            var cache = false;

                            var metricCategory = true;

                            var netRouteCategory = false;

                            var plugins = new HashSet<>();

                            var aggregations = new HashSet<String>();

                            var aggregationFuncs = 0;

                            var columns = new HashSet<String>();

                            var error = EMPTY_VALUE;

                            var subQueryId = event.getLong(SUB_QUERY_ID);

                            queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(subQueryId, EMPTY_VALUE);

                            if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE))
                            {
                                event.remove(VisualizationConstants.VISUALIZATION_TIMELINE);

                                DateTimeUtil.buildTimeline(visualizationDataSource, event, user);

                                event.put(VisualizationConstants.VISUALIZATION_TIMELINE, visualizationDataSource.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE));

                                visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_TIMELINE);
                            }

                            if (!filters.isEmpty())
                            {
                                visualizationDataSource.put(FILTERS, filters);
                            }

                            if (streamingQueries.containsKey(queryId.get()))
                            {
                                streamingQueries.get(queryId.get()).put(subQueryId, YES);
                            }

                            VisualizationConstants.validateFilters(visualizationDataSource.getJsonObject(FILTERS));//for safe side if filter size gets > 3 so will be removing extra groups as DB not able to handle

                            queryContexts.put(queryId.get(), new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource)));

                            if (customQueries.containsKey(queryId.get()))
                            {
                                customQueries.get(queryId.get()).put(CommonUtil.getString(subQueryId), new JsonObject());

                                if (replaceableColumn || visualizationDataSource.containsKey(VisualizationConstants.JOIN_TYPE) || event.getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.MAP.getName()))
                                {
                                    customSubQueries.computeIfAbsent(queryId.get(), value -> new ArrayList<>()).add(subQueryId);
                                }
                            }

                            switch (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                            {
                                case AUDIT, USER_NOTIFICATION, FLOW, TRAP, LOG, TRAP_FLAP, RUNBOOK_WORKLOG,
                                     HEALTH_METRIC, STATIC_METRIC, COMPLIANCE ->
                                {
                                    if (event.containsKey(VisualizationConstants.FILTER_KEYS))//for interface drilldown will be providing that particular keys only..
                                    {
                                        visualizationDataSource.put(VisualizationConstants.FILTER_KEYS, event.getJsonArray(VisualizationConstants.FILTER_KEYS));
                                    }

                                    event.put(VisualizationConstants.ADMIN_ROLE, visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()) || visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.COMPLIANCE.getName()) || adminRole);//as in policy trigger action data security will not be applied

                                    error = VisualizationConstants.prepareEventDataSource(eventColumns, event, visualizationDataSource, entities, user, qualifiedSources, filteredGroupEntities, LOGGER, eventCategories);

                                    if (!visualizationDataSource.containsKey(LogEngineConstants.EVENT_CATEGORY) || visualizationDataSource.getString(LogEngineConstants.EVENT_CATEGORY).isEmpty())
                                    {
                                        visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, visualizationDataSource.getString(TYPE).toLowerCase());
                                    }

                                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                                    for (var i = 0; i < dataPoints.size(); i++)
                                    {
                                        var visualizationDataPoint = dataPoints.getJsonObject(i);

                                        aggregationFuncs = getAggregationFuncs(visualizationDataPoint, aggregations, aggregationFuncs);
                                    }

                                    if (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)).getName().equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()))
                                    {
                                        VisualizationConstants.enrichFilters(visualizationDataSource.getJsonObject(FILTERS)); // need to convert long object.id into the small object.id for the filter
                                    }
                                }

                                case POLICY, POLICY_FLAP, POLICY_ACKNOWLEDGEMENT, POLICY_RESULT ->
                                {
                                    var severities = new HashSet<>();

                                    var policies = new HashSet<>();

                                    var visualizationDataPoints = new JsonArray();

                                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                                    if (((visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC.getName()))
                                            && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GAUGE.getName()))
                                            || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HEAT_MAP.getName()))
                                    //data will be fetched from in memory PolicyCache Manager
                                    {
                                        cache = true;

                                        for (var i = 0; i < dataPoints.size(); i++)
                                        {
                                            var visualizationDataPoint = dataPoints.getJsonObject(i);

                                            if (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()))
                                            {
                                                netRouteCategory = true;

                                                metricCategory = false;
                                            }
                                            else
                                            {
                                                var qualifiedEntities = qualifyEntities(false, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, filteredGroupEntities, filteredTagEntities, LOGGER, false);

                                                if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                {
                                                    setEntities(visualizationDataPoint, qualifiedEntities, entities, plugins, visualizationDataPoints);
                                                }
                                            }
                                        }

                                        applyFilter(VisualizationDataSource.PERFORMANCE_METRIC.getName(), event, true, visualizationDataSource, severities, policies, entities.keySet());

                                        visualizationDataSource.put(ENTITIES, entities);

                                        visualizationDataSource.put(SEVERITIES, new ArrayList<>(severities));

                                        visualizationDataSource.put(POLICIES, new ArrayList<>(policies));

                                        visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                                    }
                                    else
                                    {
                                        if (visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(METRIC) || visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()))
                                        {
                                            //as we have removed policy type will need to change policy type to policy flap
                                            if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY.getName()))
                                            {
                                                visualizationDataSource.put(VisualizationConstants.TYPE, VisualizationDataSource.POLICY_FLAP.getName());//backward compatibility handling
                                            }

                                            var filterObject = false;

                                            for (var i = 0; i < dataPoints.size(); i++)
                                            {
                                                if (visualizationDataPoints.size() < 10)
                                                {
                                                    var visualizationDataPoint = dataPoints.getJsonObject(i);

                                                    if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                                    {
                                                        visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                                        visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                                    }

                                                    columns.add(visualizationDataPoint.getString(VisualizationConstants.DATA_POINT));

                                                    if (visualizationDataPoint.containsKey(ENTITY_TYPE) && (visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()) || visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || visualizationDataPoint.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName())) && !visualizationDataPoint.getJsonArray(ENTITIES).isEmpty())
                                                    {
                                                        filterObject = true;
                                                    }

                                                    if (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()))
                                                    {
                                                        filterObject = false;

                                                        var qualifiedEntities = qualifyNetRouteEntities(visualizationDataPoint, DatastoreConstants.PluginId.NETROUTE_POLICY_DURATION_METRIC.getName(), VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName());

                                                        if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                        {
                                                            visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                                            entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

                                                            plugins.addAll(entities.values());

                                                            visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                                                            visualizationDataPoints.add(visualizationDataPoint);
                                                        }
                                                    }
                                                    else
                                                    {

                                                        var qualifiedEntities = qualifyEntities(false, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, filteredGroupEntities, filteredTagEntities, LOGGER, false);

                                                        if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                        {
                                                            aggregationFuncs = getAggregationFuncs(visualizationDataPoint, aggregations, aggregationFuncs);

                                                            setEntities(visualizationDataPoint, qualifiedEntities, entities, plugins, visualizationDataPoints);
                                                        }
                                                    }

                                                }
                                            }

                                            if (!visualizationDataPoints.isEmpty())
                                            {
                                                if (aggregationFuncs > VisualizationConstants.MAX_AGGREGATION_FUNCTIONS)// combination of unique counters with selected aggregation should be less than 16
                                                {
                                                    error = ErrorMessageConstants.DATA_POINT_LIMIT_EXCEEDED;
                                                }
                                                else
                                                {
                                                    event.put(VisualizationConstants.ADMIN_ROLE, adminRole);

                                                    applyFilter(visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) ? VisualizationDataSource.NETROUTE_METRIC.getName() : VisualizationDataSource.PERFORMANCE_METRIC.getName(), event.put(OBJECT_FILTER, filterObject), false, visualizationDataSource, severities, policies, entities.keySet());

                                                    visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataPoints);

                                                    visualizationDataSource.put(ENTITIES, entities);

                                                    visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                                                }
                                            }

                                            else
                                            {
                                                error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                            }
                                        }
                                        else if (visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))
                                        {
                                            adminRole = true;

                                            event.put(VisualizationConstants.ADMIN_ROLE, true);

                                            for (var i = 0; i < dataPoints.size(); i++)
                                            {
                                                var visualizationDataPoint = dataPoints.getJsonObject(i);

                                                Map<String, Object> qualifiedEntities;

                                                if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_RESULT.getName()))
                                                {
                                                    qualifiedEntities = qualifyNetRouteEntities(visualizationDataPoint, POLICY_RESULT.getName(), VisualizationDataSource.POLICY_RESULT.getName());
                                                }
                                                else
                                                {
                                                    qualifiedEntities = qualifyNetRouteEntities(visualizationDataPoint, POLICY_NETROUTE.getName(), VisualizationDataSource.POLICY.getName());
                                                }

                                                if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                {
                                                    visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                                    entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

                                                    plugins.addAll(entities.values());

                                                    visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                                                    visualizationDataPoints.add(visualizationDataPoint);
                                                }
                                            }

                                            event.put(VisualizationConstants.ADMIN_ROLE, adminRole);

                                            applyFilter(VisualizationDataSource.NETROUTE_EVENT.getName(), event.put(OBJECT_FILTER, false), false, visualizationDataSource, severities, policies, entities.keySet());

                                            visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataPoints);

                                            visualizationDataSource.put(ENTITIES, entities);

                                            visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                                        }
                                        else
                                        {
                                            adminRole = true;

                                            event.put(VisualizationConstants.ADMIN_ROLE, true);

                                            error = VisualizationConstants.prepareEventDataSource(eventColumns, event, visualizationDataSource, entities, user, qualifiedSources, filteredGroupEntities, LOGGER, eventCategories);

                                            applyFilter(visualizationDataSource.getString(CATEGORY), event, false, visualizationDataSource, severities, policies, entities.keySet());
                                        }

                                        visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, visualizationDataSource.getString(VisualizationConstants.TYPE).toLowerCase());
                                    }
                                }

                                case POLICY_STREAM, POLICY_TRIGGER_TICK ->
                                {
                                    cache = true;

                                    var severities = new HashSet<>();

                                    var policies = new HashSet<>();

                                    var visualizationDataPoints = new JsonArray();

                                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                                    if (visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_STREAM.getName()) && event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.ACTIVE_ALERT.getName()))
                                    {
                                        if (dataPoints.getJsonObject(0).containsKey(ENTITIES) && !dataPoints.getJsonObject(0).getJsonArray(ENTITIES).isEmpty())
                                        {
                                            var alertDataPoints = new JsonObject(ACTIVE_POLICY_DATA_POINTS).getJsonArray(DATA_POINTS);

                                            for (var i = 0; i < alertDataPoints.size(); i++)
                                            {
                                                alertDataPoints.getJsonObject(i).put(ENTITY_TYPE, dataPoints.getJsonObject(0).getString(ENTITY_TYPE)).put(ENTITIES, dataPoints.getJsonObject(0).getJsonArray(ENTITIES));
                                            }

                                            dataPoints = alertDataPoints;
                                        }
                                        else
                                        {
                                            dataPoints = new JsonObject(ACTIVE_POLICY_DATA_POINTS).getJsonArray(DATA_POINTS);
                                        }

                                        visualizationDataSource.put(VisualizationConstants.DATA_POINTS, dataPoints);

                                        if (!visualizationDataSource.containsKey(SEVERITY) || visualizationDataSource.getJsonArray(SEVERITY).isEmpty())
                                        {
                                            visualizationDataSource.put(SEVERITY, new JsonArray().add(Severity.CRITICAL.name()).add(Severity.MAJOR.name()).add(Severity.WARNING.name()).add(Severity.UNREACHABLE.name()).add(Severity.DOWN.name()));
                                        }

                                        visualizationDataSource.put(VISUALIZATION_RESULT_BY, new JsonArray());
                                    }

                                    if (event.containsKey(FILTER_KEYS))//request from template with entity type to replace in filter
                                    {
                                        visualizationDataSource.put(FILTER_KEYS, event.getValue(FILTER_KEYS));
                                    }

                                    if (visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.LOG.getName()) || visualizationDataSource.getString(VisualizationConstants.CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.FLOW.getName()))
                                    {
                                        metricCategory = false;

                                        for (var i = 0; i < dataPoints.size(); i++)
                                        {
                                            var visualizationDataPoint = dataPoints.getJsonObject(i);

                                            visualizationDataPoint.put(VisualizationConstants.PLUGINS, new JsonArray().add(DatastoreConstants.PluginId.POLICY_EVENT.getName()));

                                            visualizationDataPoints.add(visualizationDataPoint);
                                        }
                                    }

                                    else
                                    {
                                        for (var i = 0; i < dataPoints.size(); i++)
                                        {
                                            var visualizationDataPoint = dataPoints.getJsonObject(i);

                                            if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                            {
                                                visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                                visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                            }

                                            if (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()) || visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_EVENT.getName()))
                                            {
                                                metricCategory = false;

                                                netRouteCategory = true;

                                                Map<String, Object> qualifiedEntities;

                                                if (visualizationDataSource.getString(CATEGORY).equalsIgnoreCase(VisualizationDataSource.NETROUTE_METRIC.getName()))
                                                {
                                                    qualifiedEntities = qualifyNetRouteEntities(visualizationDataPoint, DatastoreConstants.PluginId.NETROUTE_POLICY_DURATION_METRIC.getName(), VisualizationConstants.VisualizationDataSource.POLICY_FLAP.getName());
                                                }
                                                else
                                                {
                                                    qualifiedEntities = qualifyNetRouteEntities(visualizationDataPoint, DatastoreConstants.PluginId.POLICY_NETROUTE.getName(), VisualizationConstants.VisualizationDataSource.POLICY.getName());
                                                }

                                                if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                {
                                                    visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

                                                    entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

                                                    plugins.addAll(entities.values());

                                                    visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

                                                    visualizationDataPoints.add(visualizationDataPoint);
                                                }
                                            }
                                            else
                                            {
                                                var qualifiedEntities = qualifyEntities(false, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, filteredGroupEntities, filteredTagEntities, LOGGER, false);

                                                if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                                {
                                                    setEntities(visualizationDataPoint, qualifiedEntities, entities, plugins, visualizationDataPoints);
                                                }
                                            }
                                        }
                                    }

                                    applyFilter(VisualizationDataSource.PERFORMANCE_METRIC.getName(), event, true, visualizationDataSource, severities, policies, entities.keySet());

                                    visualizationDataSource.put(ENTITIES, entities);

                                    visualizationDataSource.put(SEVERITIES, new ArrayList<>(severities));

                                    if (event.containsKey(INSTANCE))
                                    {
                                        visualizationDataSource.put(INSTANCE, event.getString(INSTANCE));
                                    }

                                    if (event.containsKey(NMSConstants.APPLICATION))
                                    {
                                        visualizationDataSource.put(Metric.METRIC_TYPE, event.getString(NMSConstants.APPLICATION));
                                    }

                                    visualizationDataSource.put(POLICIES, new ArrayList<>(policies));

                                    visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                                }

                                case CORRELATED_METRIC ->
                                {
                                    cache = true;

                                    var visualizationDataPoints = new JsonArray();

                                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                                    var groupName = EMPTY_VALUE;

                                    for (var i = 0; i < dataPoints.size(); i++)
                                    {
                                        var visualizationDataPoint = dataPoints.getJsonObject(i);

                                        var dataPoint = visualizationDataPoint.getString(VisualizationConstants.DATA_POINT);

                                        if (event.containsKey(ENTITY_TYPE))//request from template with entity type to replace in filter
                                        {
                                            visualizationDataPoint.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));

                                            visualizationDataPoint.put(ENTITIES, event.getJsonArray(ENTITIES));
                                        }

                                        if (eventColumns.containsKey(dataPoint))
                                        {
                                            var qualifiedEntities = qualifyEntities(true, visualizationDataPoint, user.getJsonArray(User.USER_GROUPS), qualifiedObjects, filteredGroupEntities, filteredTagEntities, LOGGER, false);

                                            if (qualifiedEntities != null && !qualifiedEntities.isEmpty())
                                            {
                                                if (visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(EMPTY_VALUE) || visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()) || visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()))
                                                {

                                                    //we will be fetching data from DB as we only save last data so we will not be having historical data..
                                                    cache = false;

                                                    break;
                                                }

                                                else
                                                {
                                                    aggregations.add(visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR));

                                                    aggregationFuncs++;
                                                }

                                                setEntities(visualizationDataPoint, qualifiedEntities, entities, plugins, visualizationDataPoints);

                                                if (eventColumns.getJsonObject(dataPoint).containsKey(DatastoreConstants.MAPPER_GROUP))
                                                {
                                                    groupName = eventColumns.getJsonObject(dataPoint).getString(DatastoreConstants.MAPPER_GROUP);
                                                }
                                            }
                                        }
                                    }

                                    if (cache)
                                    {
                                        if (!visualizationDataPoints.isEmpty() && !groupName.isEmpty())
                                        {
                                            applyFilter(VisualizationDataSource.PERFORMANCE_METRIC.getName(), event.put(OBJECT_FILTER, true), true, visualizationDataSource, null, null, entities.keySet());

                                            visualizationDataSource.put(ENTITIES, entities);

                                            visualizationDataSource.put(DatastoreConstants.MAPPER_GROUP, groupName);

                                            visualizationDataSource.put(VisualizationConstants.PLUGINS, new ArrayList<>(plugins));
                                        }

                                        else
                                        {
                                            error = ErrorMessageConstants.NO_ENTITY_QUALIFIED;
                                        }
                                    }

                                    else
                                    {
                                        event.put(VisualizationConstants.ADMIN_ROLE, adminRole);

                                        error = VisualizationConstants.prepareEventDataSource(eventColumns, event, visualizationDataSource, entities, user, qualifiedSources, filteredGroupEntities, LOGGER, eventCategories);

                                    }
                                }

                                case TRAP_ACKNOWLEDGEMENT ->
                                {
                                    cache = true;

                                    metricCategory = false;

                                    event.put(VisualizationConstants.ADMIN_ROLE, visualizationDataSource.getString(TYPE).equalsIgnoreCase(VisualizationDataSource.RUNBOOK_WORKLOG.getName()) || adminRole);//as in policy trigger action data security will not be applied

                                    error = VisualizationConstants.prepareEventDataSource(eventColumns, event, visualizationDataSource, entities, user, qualifiedSources, filteredGroupEntities, LOGGER, eventCategories);

                                    if (!visualizationDataSource.containsKey(LogEngineConstants.EVENT_CATEGORY) || visualizationDataSource.getString(LogEngineConstants.EVENT_CATEGORY).isEmpty())
                                    {
                                        visualizationDataSource.put(LogEngineConstants.EVENT_CATEGORY, visualizationDataSource.getString(TYPE).toLowerCase());
                                    }

                                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                                    for (var i = 0; i < dataPoints.size(); i++)
                                    {
                                        var visualizationDataPoint = dataPoints.getJsonObject(i);

                                        aggregationFuncs = getAggregationFuncs(visualizationDataPoint, aggregations, aggregationFuncs);
                                    }
                                }
                            }

                            if (error.isEmpty())
                            {
                                if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY))
                                {
                                    if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                                    {
                                        visualizationDataSource.remove(VisualizationConstants.VISUALIZATION_RESULT_BY);
                                    }

                                    else
                                    {
                                        if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationConstants.VisualizationGrouping.GROUP.getName()) && !visualizationDataSource.containsKey(VisualizationConstants.VisualizationGrouping.GROUP.getName()))
                                        {
                                            visualizationDataSource.put(VisualizationConstants.VisualizationGrouping.GROUP.getName(), VisualizationConstants.filterGroups(visualizationDataSource.getString(VisualizationConstants.TYPE), !filteredGroupEntities.isEmpty() ? new ArrayList<>(filteredGroupEntities) : user.getJsonArray(User.USER_GROUPS).getList()));
                                        }
                                    }
                                }

                                var subQueryContext = new JsonObject().mergeIn(event).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonObject().mergeIn(visualizationDataSource)).put(VisualizationConstants.ADMIN_ROLE, adminRole ? YES : NO).put(VisualizationConstants.QUERY_ID, queryId.get()).put(VisualizationConstants.SUB_QUERY_ID, subQueryId);

                                if (!cache)
                                {
                                    setQueryParameters(event, subQueryContext, aggregations, queryContexts.get(queryId.get()));
                                }

                                if (event.containsKey(EVENT_TYPE))
                                {
                                    queryTickers.put(queryId.get(), INTERVAL_SECONDS);
                                }

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Visualization Event Request received with event:" + event.encode() + " for query id:" + queryId.get() + " with context::" + subQueryContext);
                                }

                                buffer.appendByte(DatastoreConstants.OperationType.DATA_READ.getName()).appendBytes(subQueryContext.encode().getBytes());

                                EventBusConstants.updateEvent(queryId.get(), String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_REQUEST_DISPATCHED, subQueryId, DateTimeUtil.timestamp()));

                                var queryContext = new JsonObject().mergeIn(subQueryContext).put("cache", cache || (event.containsKey("cache") && event.getBoolean("cache", Boolean.FALSE)) || subQueryContext.getString("container.type", "dashboard").equalsIgnoreCase("Template") || subQueryContext.getString("drill.down.type", "no").equalsIgnoreCase("yes")).put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource));

                                subQueries.put(subQueryId, queryContext);

                                if (!event.containsKey(EVENT_TYPE))
                                {
                                    vertx.eventBus().publish(EVENT_VISUALIZATION_SUB_QUERY_CONTEXT, queryContext);
                                }

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Visualization Event Request generated time taken:" + (System.currentTimeMillis() - currentTime) + " ms for query id:" + queryId.get());
                                }

                                if (!cache)
                                {
                                    if (subQueryContext.getInteger(QUERY_PRIORITY) == QueryPriority.P0.getName())
                                    {
                                        vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject()
                                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                    }

                                    else
                                    {
                                        send(queryId.get(), new JsonObject()
                                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                                .put(EventBusConstants.EVENT_CONTEXT, buffer.getBytes()));
                                    }
                                }

                                else
                                {
                                    if (metricCategory)
                                    {
                                        visualizationDataSource.put(ENTITIES, ObjectConfigStore.getStore().getItemsByObjectIds(entities.keySet().stream().map(CommonUtil::getInteger).collect(Collectors.toList())));
                                    }
                                    else if (netRouteCategory)
                                    {
                                        visualizationDataSource.put(ENTITIES, NetRouteConfigStore.getStore().getIds());
                                    }

                                    if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GAUGE.getName())
                                            || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName())
                                            || event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.TOP_N.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.POLICY_STREAM.getName()) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_TRIGGER_TICK.getName())
                                    )
                                    {
                                        switch (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                        {
                                            case POLICY ->
                                                    Bootstrap.policyCacheService().getPoliciesByGroup(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });
                                            case POLICY_STREAM ->
                                                    Bootstrap.policyCacheService().getActivePolicies(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), EMPTY_VALUE, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });
                                            case POLICY_TRIGGER_TICK ->
                                                    Bootstrap.policyCacheService().getPolicyLastTriggeredTicks(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), EMPTY_VALUE, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });

                                            case CORRELATED_METRIC ->
                                                    Bootstrap.cacheService().getCorrelatedMetrics(visualizationDataSource, result ->
                                                    {
                                                        if (result.succeeded() && result.result() != null)
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), null, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                        else
                                                        {
                                                            VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);
                                                        }
                                                    });

                                            case TRAP_ACKNOWLEDGEMENT -> Bootstrap.cacheService().getTrapAcks(result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), "", queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                                }
                                                else
                                                {
                                                    VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);
                                                }
                                            });
                                        }
                                    }
                                    else if (event.getString(VisualizationConstants.VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationConstants.VisualizationCategory.HEAT_MAP.getName()))
                                    {
                                        Bootstrap.policyCacheService().getPolicyHeatmap(visualizationDataSource, result ->
                                        {
                                            if (result.succeeded() && result.result() != null)
                                            {
                                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), EMPTY_VALUE, queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                            }
                                            else
                                            {
                                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), result.result(), String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), subQueryId, LOGGER, EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE);
                                            }
                                        });
                                    }
                                }

                            }

                            else
                            {
                                customQueries.remove(queryId.get());

                                customSubQueries.remove(queryId.get());

                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), event.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getLong(ID) > 0 ? event.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", error), queryId.get(), subQueryId, LOGGER, EVENT_DATASTORE_QUERY_RESPONSE);

                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });

                    vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
                    {
                        var event = message.body();

                        switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                        {
                            case ADD_OBJECT ->
                                    objectIds.add(ObjectConfigStore.getStore().getObjectIdById(event.getLong(ID)));

                            case DELETE_OBJECT ->
                            {
                                if ((!event.containsKey(METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null) && (!event.containsKey(OBJECTS) || event.getValue(OBJECTS) == null))
                                {
                                    objectIds.remove(event.getInteger(AIOpsObject.OBJECT_ID));
                                }
                            }

                            default ->
                            {
                            }
                        }
                    }).exceptionHandler(LOGGER::error);
                }
            });

            /*
            Following Local Consumer Purpose is to get acknowledgment from db and then to set the timer as per the ack in queryTicker.
             */
            Bootstrap.vertx().eventBus().<Long>localConsumer(EVENT_DATASTORE_ACKNOWLEDGEMENT, message ->
            {
                try
                {
                    if (message.body() != null)
                    {
                        var queryId = message.body();

                        if (queryTickers.containsKey(queryId))
                        {
                            queryTickers.put(queryId, INTERVAL_SECONDS);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    /**
     * Qualifies NetRoute entities for visualization.
     * <p>
     * This method creates a mapping of NetRoute entity IDs to plugin names for use in
     * NetRoute-specific visualizations. It either uses the entities specified in the
     * visualization data point or retrieves all NetRoute IDs if none are specified.
     *
     * @param visualizationDataPoint The visualization data point containing entity information
     * @param plugin                 The plugin ID for NetRoute data
     * @param name                   The name of the NetRoute metric or data type
     * @return A map containing qualified entities and plugin information, or null if no entities are found
     */
    private Map<String, Object> qualifyNetRouteEntities(JsonObject visualizationDataPoint, int plugin, String name)
    {
        Map<String, Object> qualifiedEntities = null;

        try
        {
            // Create a map to store entity ID to plugin name mappings
            var entities = new HashMap<>();

            // Get entity IDs from the visualization data point or from the NetRoute store if none specified
            var items = visualizationDataPoint.containsKey(ENTITIES) && !visualizationDataPoint.getJsonArray(ENTITIES).isEmpty()
                    ? visualizationDataPoint.getJsonArray(ENTITIES)
                    : NetRouteConfigStore.getStore().getIds();

            // Create a list of plugin identifiers (format: "pluginId-name")
            var plugins = new ArrayList<String>();
            plugins.add(plugin + DASH_SEPARATOR + name);

            if (items != null && !items.isEmpty())
            {
                // Map each entity ID to the plugin identifier
                for (var item : items)
                {
                    entities.put(CommonUtil.getString(item), plugins.getFirst());
                }

                // Create the result map with entities and plugins
                qualifiedEntities = new HashMap<>();
                qualifiedEntities.put(ENTITIES, entities);
                qualifiedEntities.put(VisualizationConstants.PLUGINS, plugins);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedEntities;
    }

    /**
     * Processes aggregation functions for visualization data points.
     * <p>
     * This method adds appropriate aggregators to the provided set based on the
     * aggregation type specified in the visualization data point. It handles special
     * cases like AVG (average) aggregation, which requires both SUM and COUNT
     * aggregators to be added.
     * <p>
     * The method also updates and returns the count of aggregation functions.
     *
     * @param visualizationDataPoint The visualization data point containing aggregation information
     * @param aggregations           A set to which aggregation function names will be added
     * @param aggregationFuncs       The current count of aggregation functions
     * @return The updated count of aggregation functions after adding new ones
     */
    private int getAggregationFuncs(JsonObject visualizationDataPoint, Set<String> aggregations, int aggregationFuncs)
    {
        // Special handling for AVG aggregation, which requires both SUM and COUNT
        if (visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR).equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
        {
            // Add SUM aggregator
            aggregations.add(DatastoreConstants.AggregationType.SUM.getName());

            // Add COUNT aggregator
            aggregations.add(DatastoreConstants.AggregationType.COUNT.getName());

            // Increment count by 2 since we added two aggregators
            aggregationFuncs += 2;
        }
        else
        {
            // For other aggregation types, just add the specified aggregator
            aggregations.add(visualizationDataPoint.getString(VisualizationConstants.AGGREGATOR));

            // Increment count by 1
            aggregationFuncs++;
        }

        return aggregationFuncs;
    }

    /*
        This function is called when to set Data in Entities , VisualizationDataPoint, Plugins.
     */
    private void setEntities(JsonObject visualizationDataPoint, Map<String, Object> qualifiedEntities, Map<String, Object> entities, Set<Object> plugins, JsonArray visualizationDataPoints)
    {
        visualizationDataPoint.put(ENTITIES, qualifiedEntities.get(ENTITIES));

        entities.putAll((Map<? extends String, ? extends Integer>) qualifiedEntities.get(ENTITIES));

        plugins.addAll(entities.values());

        visualizationDataPoint.put(VisualizationConstants.PLUGINS, qualifiedEntities.remove(VisualizationConstants.PLUGINS));

        visualizationDataPoints.add(visualizationDataPoint);
    }

    private void send()
    {
        var id = queuedQueries.removeFirst();

        send(id, queuedQueryContexts.remove(id));
    }

    private void send(long id, JsonObject context)
    {
        if (runningQueries.size() == MotadataConfigUtil.getEventQueryQueueSize())
        {
            queuedQueryContexts.put(id, context);

            queuedQueries.add(id);
        }

        else
        {
            runningQueries.add(id);

            vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, context.put(EventBusConstants.EVENT_COPY_REQUIRED, false));
        }
    }

    private void cleanUp(long queryId, long subQueryId)
    {
        //will be removing all subquerycontext

        if (queryStatuses.containsKey(queryId))
        {
            queryStatuses.get(queryId).remove(subQueryId);
        }

        if (customSubQueries.containsKey(queryId))
        {
            customSubQueries.get(queryId).remove(subQueryId);
        }

        subQueries.remove(subQueryId);
    }

    private void cleanUp(long queryId)
    {
        queryTrackers.remove(queryId);

        if (queryStatuses.containsKey(queryId))
        {
            queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
        }

        queries.remove(queryId);

        subQueryTrackers.remove(queryId);

        streamingQueries.remove(queryId);

        queryContexts.remove(queryId);

        customQueries.remove(queryId);

        customSubQueries.remove(queryId);

        queryTickers.remove(queryId);

        queryResults.remove(queryId);

        queryUIContexts.remove(queryId);
    }
}
