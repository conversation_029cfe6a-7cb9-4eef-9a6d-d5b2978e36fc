/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.visualization;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.EventOrdinalCacheStore;
import com.mindarray.store.LogPatternCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationEventHistoryResponseProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationEventHistoryResponseProcessor.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Event History Response Processor");

    private static final JsonArray INTERVALS = new JsonArray().add(5).add(15).add(30).add(60).add(90).add(120).add(180).add(240).add(300).add(600).add(1200).add(1800).add(3600).add(3600 * 6).add(3600 * 12).add(3600 * 18).add(3600 * 24);

    private final Map<Long, TreeMap<Long, Integer>> queryTicks = new HashMap<>();

    private final Map<Long, Integer> queryIntervals = new HashMap<>();

    private final Map<Long, Map<String, TopKUtil>> queryTopKObjects = new HashMap<>();

    private final Map<Long, Map<String, Integer>> queryCounts = new HashMap<>();

    private final Map<Long, Integer> queryTickers = new HashMap<>();//query timer after time complete query not completed removing its context

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            vertx.setPeriodic(30 * 1000L, periodicTimer ->
            {
                var iterator = queryTickers.entrySet().iterator();

                while (iterator.hasNext())
                {
                    var entry = iterator.next();

                    entry.setValue(entry.getValue() - 30);

                    if (entry.getValue() <= 0)
                    {
                        iterator.remove();

                        vertx.eventBus().publish(EVENT_VISUALIZATION_QUERY_ABORT, entry.getKey());

                        if (CommonUtil.traceEnabled())
                        {

                            LOGGER.trace("aborting query context for event history query " + entry.getKey());
                        }
                    }
                }
            });

            vertx.eventBus().<Long>localConsumer(EVENT_VISUALIZATION_QUERY_ABORT, message ->
            {
                try
                {

                    queryIntervals.remove(message.body());

                    queryTicks.remove(message.body());

                    queryTopKObjects.remove(message.body());

                    queryCounts.remove(message.body());
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EVENT_VISUALIZATION_EVENT_HISTORY_RESPONSE_PROCESSOR, message ->
            {
                if (!message.body().isEmpty())
                {
                    var event = message.body();

                    try
                    {
                        var context = event.getJsonObject(QUERY_CONTEXT);

                        if (context.containsKey(PAGINATION_QUERY))
                        {
                            var result = composePaginationResponse(event.getJsonObject(RESULT).getJsonObject(CommonUtil.getString(event.getValue(SUB_QUERY_ID))).getJsonArray(RESULT));

                            if (!result.isEmpty())
                            {
                                vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.pack(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), event.getLong(VisualizationConstants.QUERY_ID), event.getLong(SUB_QUERY_ID)).getBytes()));
                            }

                            else
                            {
                                //send back empty result

                                VisualizationConstants.send(event.getString(VisualizationConstants.VISUALIZATION_CATEGORY), VisualizationConstants.VisualizationCategory.GRID.getName(), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), event.getLong(VisualizationConstants.QUERY_ID), event.getLong(SUB_QUERY_ID), LOGGER, EventBusConstants.EVENT_VISUALIZATION_RESPONSE);
                            }
                        }

                        else if (context.getString(VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationDataSource.EVENT_HISTORY.getName()))
                        {
                            //will be merging log pattern here

                            composeEventHistoryResponse((event.getJsonObject(RESULT).getJsonObject(CommonUtil.getString(event.getValue(SUB_QUERY_ID)))).getJsonArray(RESULT), event);
                        }
                        else
                        {
                            composeQueryResponse(event.getJsonObject("column.mappers", new JsonObject()), event.getInteger(QUERY_PROGRESS), event.getJsonObject(RESULT), event.getString(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), event.getLong(QUERY_ID), event.getLong(VisualizationConstants.SUB_QUERY_ID), context, LOGGER, event.getJsonObject(QUERY_CONTEXT).containsKey(DUMMY_FIELDS) ? event.getJsonObject(QUERY_CONTEXT).getBoolean(DUMMY_FIELDS) : false);
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void composeEventHistoryResponse(JsonArray rows, JsonObject context)
    {
        var result = new JsonObject();

        if (rows != null)
        {
            queryIntervals.putIfAbsent(context.getLong(QUERY_ID), 0);

            queryTickers.putIfAbsent(context.getLong(QUERY_ID), INTERVAL_SECONDS);

            var ticks = queryTicks.computeIfAbsent(context.getLong(QUERY_ID), value -> new TreeMap<>());

            var intervalIndex = queryIntervals.get(context.getLong(QUERY_ID));

            var topKObjects = queryTopKObjects.computeIfAbsent(context.getLong(QUERY_ID), value -> new HashMap<>());

            var counts = queryCounts.computeIfAbsent(context.getLong(QUERY_ID), value -> new HashMap<>());

            var valid = false;

            for (var i = 0; i < rows.size(); i++)
            {
                var row = rows.getJsonObject(i);

                if (row.containsKey("Timestamp"))
                {
                    valid = true;

                    try
                    {
                        if (row.containsKey(EVENT + CARET_SEPARATOR + VALUE) && !row.getString(EVENT + CARET_SEPARATOR + VALUE).isEmpty())
                        {
                            var event = new JsonObject();

                            extractEvent(Buffer.buffer(row.getBinary(EVENT + CARET_SEPARATOR + VALUE)), event, topKObjects, counts);

                            var pattern = EMPTY_VALUE;

                            var patternId = NOT_AVAILABLE;

                            if (row.containsKey(LogEngineConstants.EVENT_PATTERN_ID + CARET_SEPARATOR + VALUE))
                            {
                                var value = row.getLong(LogEngineConstants.EVENT_PATTERN_ID + CARET_SEPARATOR + VALUE);

                                if (value != Long.MIN_VALUE)
                                {
                                    patternId = CommonUtil.getInteger(row.remove(LogEngineConstants.EVENT_PATTERN_ID + CARET_SEPARATOR + VALUE));

                                    if (patternId > 0)
                                    {
                                        pattern = LogPatternCacheStore.getStore().getPattern(patternId);
                                    }
                                }
                            }

                            row.put(LogEngineConstants.EVENT_PATTERN, pattern);

                            row.put(LogEngineConstants.EVENT_PATTERN_ID, patternId);

                            row.remove(EVENT + CARET_SEPARATOR + VALUE);

                            VisualizationConstants.compose(row, result);
                        }

                        var millis = DateTimeUtil.roundOffSeconds(row.getLong("Timestamp"), INTERVALS.getInteger(intervalIndex));

                        ticks.put(millis, ticks.getOrDefault(millis, 0) + 1);

                        if (ticks.size() > MAX_BAR_CHART_LIMIT)
                        {
                            intervalIndex += 1;

                            ticks = composeHistogramResponse(ticks, intervalIndex);
                        }
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            if (valid)
            {
                queryTicks.get(context.getLong(QUERY_ID)).putAll(ticks);

                composeResult(result, ticks, context, intervalIndex, topKObjects, counts);
            }
        }
    }

    private TreeMap<Long, Integer> composeHistogramResponse(TreeMap<Long, Integer> ticks, int index)
    {
        var result = new TreeMap<Long, Integer>();

        try
        {
            var iterator = ticks.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                var millis = DateTimeUtil.roundOffSeconds(entry.getKey(), INTERVALS.getInteger(index));

                result.put(millis, result.getOrDefault(millis, 0) + entry.getValue());

                iterator.remove();
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private void composeResult(JsonObject result, TreeMap<Long, Integer> ticks, JsonObject context, int intervalIndex, Map<String, TopKUtil> topKObjects, Map<String, Integer> counts)
    {
        try
        {
            var progress = CommonUtil.getInteger(context.getLong(QUERY_PROGRESS) / 3);

            var topKField = new JsonObject();

            topKField.put("field", new JsonArray()).put("value", new JsonArray()).put("count", new JsonArray()).put("percent", new JsonArray());

            for (var entry : topKObjects.entrySet())
            {
                var key = entry.getKey();

                for (var value : entry.getValue().getTopK())
                {
                    if (value != null)
                    {
                        topKField.getJsonArray("field").add(key);

                        topKField.getJsonArray("value").add(value.getValue());

                        topKField.getJsonArray("count").add(value.getFrequency());

                        topKField.getJsonArray("percent").add(CommonUtil.getFloat((value.getFrequency() * 100)) / CommonUtil.getFloat(counts.get(key)));
                    }
                }
            }

            topKField.put(QUERY_PROGRESS, progress);

            vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.pack(topKField, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(VisualizationConstants.QUERY_ID), context.getLong(SUB_QUERY_ID)).getBytes()));

            var tickCount = new JsonObject();

            tickCount.put(TIME_STAMP, new JsonArray()).put("message^count", new JsonArray());

            for (var entry : ticks.entrySet())
            {
                tickCount.getJsonArray(TIME_STAMP).add(entry.getKey());

                tickCount.getJsonArray("message^count").add(entry.getValue());
            }

            tickCount.put(QUERY_PROGRESS, progress);

            vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.pack(tickCount, VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal(), context.getLong(VisualizationConstants.QUERY_ID), context.getLong(SUB_QUERY_ID)).getBytes()));

            result.put(QUERY_PROGRESS, context.getLong(QUERY_PROGRESS));

            vertx.eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.pack(result, VisualizationCategoryOrdinal.VISUALIZATION_EVENT_HISTORY.ordinal(), context.getLong(VisualizationConstants.QUERY_ID), context.getLong(SUB_QUERY_ID)).getBytes()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            if (context.getLong(QUERY_PROGRESS) >= 100)
            {
                queryTicks.remove(context.getLong(QUERY_ID));

                queryIntervals.remove(context.getLong(QUERY_ID));

                queryTopKObjects.remove(context.getLong(QUERY_ID));

                queryCounts.remove(context.getLong(QUERY_ID));

                queryTickers.remove(context.getLong(QUERY_ID));

                if (CommonUtil.traceEnabled())
                {

                    LOGGER.trace("removing query context for event history query " + context.getLong(QUERY_ID));
                }
            }

            else
            {
                queryIntervals.put(context.getLong(QUERY_ID), intervalIndex);
            }
        }
    }

    private JsonObject composePaginationResponse(JsonArray rows)
    {
        var result = new JsonObject();

        for (var i = 0; i < rows.size(); i++)
        {
            try
            {
                var row = rows.getJsonObject(i);

                if (row.containsKey(EVENT + CARET_SEPARATOR + VALUE) && !row.getString(EVENT + CARET_SEPARATOR + VALUE).isEmpty())
                {
                    var event = new JsonObject();

                    extractEvent(Buffer.buffer(row.getBinary(EVENT + CARET_SEPARATOR + VALUE)), event, null, null);

                    event.remove(LogEngineConstants.EVENT_PATTERN_ID);

                    row.put(EVENT + CARET_SEPARATOR + VALUE, event);

                    VisualizationConstants.compose(row, result);
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }

        return result;
    }

    private void extractEvent(Buffer buffers, JsonObject event, Map<String, TopKUtil> topKObjects, Map<String, Integer> counts)
    {
        var position = 0;

        for (var j = 0; j < buffers.length(); j = position)
        {
            try
            {
                var ordinal = buffers.getIntLE(position);

                if (EventOrdinalCacheStore.getStore().getValue(ordinal) != null)
                {
                    position += 4;
                }

                else
                {
                    position += 2;

                    ordinal = buffers.getIntLE(position);

                    position += 4;
                }


                var key = EventOrdinalCacheStore.getStore().getValue(ordinal).split(SEPARATOR_WITH_ESCAPE)[1];

                var length = buffers.getShortLE(position);

                position += 2;

                var value = buffers.getString(position, position + length);

                position += length;

                if (topKObjects != null)
                {
                    topKObjects.computeIfAbsent(key, val -> new TopKUtil(10));

                    topKObjects.get(key).add(value);

                    counts.put(key, counts.getOrDefault(key, 0) + 1);
                }

                event.put(key, value);
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);

                break;
            }
        }
    }
}
