/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*   19-May-2025     Sankalp             MOTADATA-5939: Cisco ACI Topology support
*   24-Jun-2025     <PERSON><PERSON>wari         MOTADATA-6528 : added support for topology view filters hence added setFilterTargets method & parent child hierarchy fetch
*/
package com.mindarray.aiops;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.AsyncResult;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.aiops.AIOpsConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_IP;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * Processes dependency queries and builds dependency hierarchies.
 * <p>
 * The DependencyQueryProcessor is responsible for:
 * <ul>
 *   <li>Processing dependency queries received through the event bus</li>
 *   <li>Building different types of dependency hierarchies (bottom-top, top-bottom)</li>
 *   <li>Handling different categories of objects (network, cloud, virtualization, etc.)</li>
 *   <li>Building connections between objects based on their dependencies</li>
 *   <li>Supporting different dependency formats (flat, hierarchical)</li>
 * </ul>
 * <p>
 * This processor works with the DependencyManager to retrieve dependency information
 * and build appropriate dependency structures based on query parameters. The resulting
 * dependency hierarchies are used for topology visualization, correlation, and root cause analysis.
 * <p>
 * The processor supports three main dependency formats:
 * <ul>
 *   <li>FLAT: Raw dependency connections, primarily used for root cause analysis</li>
 *   <li>BOTTOM_TOP_HIERARCHY: Child-to-parent relationships, showing dependencies upward</li>
 *   <li>TOP_BOTTOM_HIERARCHY: Parent-to-child relationships, showing dependencies downward</li>
 * </ul>
 */

public class DependencyQueryProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DependencyQueryProcessor.class, MOTADATA_AIOPS, "Dependency Query Processor");
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(120000L);
    // network category contains network/server/wireless devices
    private final Map<String, JsonArray> probes = Map.ofEntries(
            Map.entry(NMSConstants.Category.NETWORK.name(), new JsonArray().add(NMSConstants.Type.SNMP_DEVICE.getName()).add(NMSConstants.Type.SWITCH.getName())
                    .add(NMSConstants.Type.ROUTER.getName()).add(NMSConstants.Type.FIREWALL.getName()).add(NMSConstants.Type.UPS.getName())
                    .add(NMSConstants.Type.PRINTER.getName()).add(NMSConstants.Type.LOAD_BALANCER.getName()).add(NMSConstants.Type.WIRELESS_CONTROLLER.getName())
                    .add(NMSConstants.Type.WINDOWS.getName()).add(NMSConstants.Type.WINDOWS_CLUSTER.getName()).add(NMSConstants.Type.LINUX.getName())
                    .add(NMSConstants.Type.IBM_AIX.getName()).add(NMSConstants.Type.HP_UX.getName()).add(NMSConstants.Type.SOLARIS.getName())
                    .add(NMSConstants.Type.CISCO_WIRELESS.getName()).add(NMSConstants.Type.RUCKUS_WIRELESS.getName()).add(NMSConstants.Type.ARUBA_WIRELESS.getName())),
            Map.entry(NMSConstants.Category.SDN.getName(), new JsonArray().add(NMSConstants.Type.CISCO_VMANAGE.getName()).add(NMSConstants.Type.CISCO_VSMART.getName())
                    .add(NMSConstants.Type.CISCO_VBOND.getName()).add(NMSConstants.Type.CISCO_VEDGE.getName()).add(NMSConstants.Type.CISCO_ACI.getName())),
            Map.entry(NMSConstants.Category.CLOUD.name(), new JsonArray().add(NMSConstants.Type.AWS_CLOUD.getName()).add(NMSConstants.Type.AZURE_CLOUD.getName())
                    .add(NMSConstants.Type.AMAZON_CLOUD_FRONT.getName()).add(NMSConstants.Type.AWS_AUTO_SCALING.getName()).add(NMSConstants.Type.AMAZON_DOCUMENTDB.getName())
                    .add(NMSConstants.Type.AWS_LAMBDA.getName()).add(NMSConstants.Type.AMAZON_SQS.getName()).add(NMSConstants.Type.AWS_ELASTIC_BEANSTALK.getName())
                    .add(NMSConstants.Type.AMAZON_DYNAMO_DB.getName()).add(NMSConstants.Type.AMAZON_SNS.getName()).add(NMSConstants.Type.AMAZON_S3.getName()).add(NMSConstants.Type.AMAZON_RDS.getName())
                    .add(NMSConstants.Type.AMAZON_EC2.getName()).add(NMSConstants.Type.AWS_ELB.getName()).add(NMSConstants.Type.AMAZON_EBS.getName())
                    .add(NMSConstants.Type.AZURE_COSMOS_DB.getName()).add(NMSConstants.Type.AZURE_VM.getName()).add(NMSConstants.Type.AZURE_WEB_APP.getName())
                    .add(NMSConstants.Type.AZURE_MYSQL.getName()).add(NMSConstants.Type.AZURE_POSTGRESQL.getName())
                    .add(NMSConstants.Type.AZURE_SERVICE_BUS.getName()).add(NMSConstants.Type.AZURE_APPLICATION_GATEWAY.getName()).add(NMSConstants.Type.AZURE_FUNCTION.getName())
                    .add(NMSConstants.Type.AZURE_LOAD_BALANCER.getName()).add(NMSConstants.Type.AZURE_VM_SCALE_SET.getName()).add(NMSConstants.Type.AZURE_CDN.getName())
                    .add(NMSConstants.Type.AZURE_STORAGE.getName()).add(NMSConstants.Type.AZURE_SQL_DATABASE.getName())),
            Map.entry(NMSConstants.Category.VIRTUALIZATION.name(), new JsonArray().add(NMSConstants.Type.VCENTER.getName()).add(NMSConstants.Type.VMWARE_ESXI.getName())
                    .add(NMSConstants.Type.HYPER_V_CLUSTER.getName()).add(NMSConstants.Type.HYPER_V.getName())
                    .add(NMSConstants.Type.CITRIX_XEN_CLUSTER.getName()).add(NMSConstants.Type.CITRIX_XEN.getName())),
            Map.entry(NMSConstants.Category.HCI.getName(), new JsonArray().add(NMSConstants.Type.PRISM.getName()).add(NMSConstants.Type.NUTANIX.getName())),
            Map.entry(NMSConstants.Category.OTHER.name(), new JsonArray().add(NMSConstants.Type.PING.getName()).add(NMSConstants.Type.CISCO_UCS.getName()).add(NMSConstants.Type.IBM_TAPE_LIBRARY.getName())));
    private final JsonArray ciscoUCSDependencyLevels = new JsonArray(new ArrayList<Byte>(4)).add(AIOpsConstants.DependencyLevel.TWELVE.getName()).add(AIOpsConstants.DependencyLevel.TEN.getName()).add(AIOpsConstants.DependencyLevel.NINE.getName()).add(AIOpsConstants.DependencyLevel.EIGHT.getName());
    private final JsonArray cloudDependencyLevels = new JsonArray(new ArrayList<Byte>(1)).add(AIOpsConstants.DependencyLevel.TEN.getName());
    private final JsonArray networkDependencyLevels = new JsonArray(new ArrayList<Byte>(1)).add(AIOpsConstants.DependencyLevel.FOUR.getName());
    private final JsonArray sdnDependencyLevels = new JsonArray(new ArrayList<Byte>(3)).add(DependencyLevel.FOUR.getName()).add(DependencyLevel.SEVEN.getName()).add(AIOpsConstants.DependencyLevel.TEN.getName()).add(DependencyLevel.SIX.getName());

    private final JsonArray virtualizationClusterDependencyLevels = new JsonArray(new ArrayList<Byte>(4)).add(AIOpsConstants.DependencyLevel.TWELVE.getName()).add(AIOpsConstants.DependencyLevel.TEN.getName()).add(AIOpsConstants.DependencyLevel.NINE.getName()).add(AIOpsConstants.DependencyLevel.EIGHT.getName());
    private final JsonArray virtualizationESXiDependencyLevels = new JsonArray(new ArrayList<Byte>(2)).add(AIOpsConstants.DependencyLevel.EIGHT.getName()).add(AIOpsConstants.DependencyLevel.SEVEN.getName());

    private final JsonArray hciClusterDependencyLevels = new JsonArray(new ArrayList<Byte>(4)).add(AIOpsConstants.DependencyLevel.TWELVE.getName()).add(AIOpsConstants.DependencyLevel.TEN.getName()).add(AIOpsConstants.DependencyLevel.NINE.getName()).add(AIOpsConstants.DependencyLevel.EIGHT.getName());
    private final JsonArray hciHostDependencyLevels = new JsonArray(new ArrayList<Byte>(2)).add(AIOpsConstants.DependencyLevel.EIGHT.getName()).add(AIOpsConstants.DependencyLevel.SEVEN.getName());

    private final Map<String, NMSConstants.Type> virtualizationClusterTypes = Map.ofEntries(Map.entry(NMSConstants.Type.VCENTER.getName(), NMSConstants.Type.VMWARE_ESXI), Map.entry(NMSConstants.Type.HYPER_V_CLUSTER.getName(), NMSConstants.Type.HYPER_V),
            Map.entry(NMSConstants.Type.CITRIX_XEN_CLUSTER.getName(), NMSConstants.Type.CITRIX_XEN));

    private final Map<String, NMSConstants.Type> hciClusterTypes = Map.ofEntries(Map.entry(NMSConstants.Type.PRISM.getName(), NMSConstants.Type.NUTANIX));

    private final Map<Long, Set<Long>> hierarchies = new HashMap<>(); // this map is used to maintain the hierarchy of dependencies that helps UI to render the left hierarchy tree

    /**
     * Initializes the DependencyQueryProcessor and sets up the event bus consumer.
     * <p>
     * This method registers a consumer for dependency query events on the event bus.
     * When a dependency query event is received, it processes the query based on the
     * dependency type, object category, and requested format.
     *
     * @param promise Promise to be completed when initialization is done
     */
    @Override
    public void start(Promise<Void> promise)
    {
        updateHierarchies();

        vertx.eventBus().<JsonObject>localConsumer(EVENT_DEPENDENCY_QUERY, message ->
        {
            try
            {
                var event = message.body();

                var dependencyType = event.getString(AIOpsConstants.DEPENDENCY_TYPE);

                if (CommonUtil.isNotNullOrEmpty(dependencyType))
                {
                    var item = ObjectConfigStore.getStore().getItem(event.getLong(APIConstants.ENTITY_ID));

                    if (item != null)
                    {
                        // For cross domain we need this raw results in Root Cause Analysis
                        var connections = new JsonArray();

                        // For topology and parent-child related correlation like Availability Correlation
                        var root = new JsonObject();

                        var category = event.getString(AIOpsObject.OBJECT_CATEGORY);

                        // For Cisco UCS there is no relation for parent-child, hence in topology we also send raw results in Cisco UCS topology
                        var dependencyFormat = item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_UCS.getName()) ? AIOpsConstants.DependencyFormat.FLAT.getName() : event.getString(DEPENDENCY_FORMAT);

                        if (probes.get(category).contains(item.getString(AIOpsObject.OBJECT_TYPE)))
                        {
                            var objects = new JsonArray();

                            cleanUp(event);

                            var pendingItems = new HashMap<String, Integer>();

                            var id = item.getLong(ID);

                            var future = Promise.<Void>promise();

                            var filter = category.equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) ? AIOpsObject.OBJECT_TARGET : AIOpsObject.OBJECT_IP;

                            event.put(filter, item.getString(filter));

                            pendingItems.put(item.getString(filter), 1);

                            // for child->parent relation
                            // result will be dependency.parent [upto end level]
                            if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.BOTTOM_TOP_HIERARCHY.getName()))
                            {
                                var parents = new JsonArray();

                                root.put(ID, id).put(AIOpsConstants.DEPENDENCY_PARENT, parents);

                                buildHierarchies(parents, item, pendingItems, future, event, filter, objects);
                            }
                            else
                            {
                                switch (NMSConstants.Category.valueOf(category))
                                {
                                    case NETWORK ->
                                    {
                                        if (valid(objects, item, AIOpsObject.OBJECT_IP))
                                        {
                                            // Special handling for Windows Cluster objects
                                            if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.WINDOWS_CLUSTER.getName()))
                                            {
                                                // Query dependencies for Windows Cluster and build specialized hierarchy
                                                vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
                                                        new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter)),
                                                        DELIVERY_OPTIONS,
                                                        reply ->
                                                        {
                                                            try
                                                            {
                                                                root.put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                                event.put(NMSConstants.OBJECT, item);

                                                                buildWindowsClusterDependencies(reply, event, root, connections, objects, pendingItems, future);
                                                            }
                                                            catch (Exception exception)
                                                            {
                                                                LOGGER.error(exception);
                                                            }

                                                            tryToComplete(future, pendingItems, item.getString(filter));
                                                        });
                                            }
                                            // Handle hierarchical format (parent-to-child relationships)
                                            else if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                                            {
                                                root.put(ID, id).put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                // Build parent -> child relation hierarchy
                                                // Result will be dependency.child structure down to leaf nodes
                                                buildHierarchies(item, objects, root, future, pendingItems, event, new HashSet<>(), event.containsKey(Explorer.ID) ? setFilterTargets(event) : null);
                                            }
                                            // Handle flat format (direct connections)
                                            else
                                            {
                                                // Build flat connection list without hierarchy
                                                buildConnections(item, objects, connections, future, pendingItems, event);
                                            }
                                        }

                                    }

                                    case CLOUD ->
                                        // For cloud objects, query dependencies with specific cloud dependency levels
                                            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
                                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter)).put(AIOpsConstants.DEPENDENCY_LEVEL, cloudDependencyLevels),
                                                    DELIVERY_OPTIONS,
                                                    reply ->
                                                    {
                                                        try
                                                        {
                                                            var connected = false;

                                                            if (reply.succeeded())
                                                            {
                                                                var result = reply.result().body();

                                                                var dependencies = result.getJsonObject(dependencyType);

                                                                if (dependencies != null)
                                                                {
                                                                    if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                                                                    {
                                                                        var children = new JsonArray();

                                                                        root.put(ID, id).put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                                .put(AIOpsConstants.DEPENDENCY_CHILDREN, children);

                                                                        for (var dependency : dependencies)
                                                                        {
                                                                            if (dependency.getKey().contains(VALUE_SEPARATOR))
                                                                            {
                                                                                var source = dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim();

                                                                                var connectedChildren = new JsonArray();

                                                                                for (var connection : JsonObject.mapFrom(dependency.getValue()))
                                                                                {
                                                                                    var level = CommonUtil.getByteValue(connection.getKey());

                                                                                    for (var destination : getDestinations((JsonArray) connection.getValue(), filter))
                                                                                    {
                                                                                        var cloudObject = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(destination));

                                                                                        var server = false;

                                                                                        if (cloudObject != null)
                                                                                        {
                                                                                            var object = CommonUtil.isNotNullOrEmpty(cloudObject.getString(AIOpsObject.OBJECT_IP)) && ObjectConfigStore.getStore().getItemByIP(cloudObject.getString(AIOpsObject.OBJECT_IP)) != null
                                                                                                    ? ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(cloudObject.getString(AIOpsObject.OBJECT_IP))) : null;

                                                                                            if (object != null) // means connected instances of azure vm / aws ec2 is provisioned in system in server category...so we will go upto end level and check if any applications are connected with them or not
                                                                                            {
                                                                                                server = true;

                                                                                                var connectedServer = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, source)
                                                                                                        .put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                                                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, object.getLong(ID))
                                                                                                        .put(DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                                                                connectedChildren.add(connectedServer);

                                                                                                if (valid(objects, object, AIOpsObject.OBJECT_IP))
                                                                                                {
                                                                                                    event.put(AIOpsObject.OBJECT_IP, event.getString(AIOpsObject.OBJECT_TARGET));

                                                                                                    pendingItems.put(event.getString(AIOpsObject.OBJECT_TARGET), pendingItems.get(event.getString(AIOpsObject.OBJECT_TARGET)) + 1);

                                                                                                    buildHierarchies(object, objects, connectedServer, future, pendingItems, event, null, null);
                                                                                                }
                                                                                            }

                                                                                        }

                                                                                        if (!server) //means only cloud instances
                                                                                        {
                                                                                            connectedChildren.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, source)
                                                                                                    .put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                                                                                    .put(DEPENDENCY_CONNECTED_LINK, UNKNOWN));
                                                                                        }
                                                                                    }
                                                                                }

                                                                                //#24675
                                                                                if (!connectedChildren.isEmpty())
                                                                                {

                                                                                    children.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                                                            .put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName())
                                                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, source)
                                                                                            .put(DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_CHILDREN, connectedChildren));

                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                    else
                                                                    {
                                                                        for (var dependency : dependencies)
                                                                        {
                                                                            if (dependency.getKey().contains(VALUE_SEPARATOR))
                                                                            {
                                                                                var link = new JsonObject().put(ID, id).put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, new JsonArray(new ArrayList<>(1)).add(dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim()))
                                                                                        .put(DEPENDENCY_CONNECTED_LINK, UNKNOWN);

                                                                                for (var connection : JsonObject.mapFrom(dependency.getValue()))
                                                                                {
                                                                                    link.put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(connection.getKey()));

                                                                                    var destinations = getDestinations((JsonArray) connection.getValue(), filter);

                                                                                    if (!destinations.isEmpty())
                                                                                    {
                                                                                        connected = true;

                                                                                        connections.add(new JsonObject().mergeIn(link).put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations));
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            else
                                                            {
                                                                LOGGER.error(reply.cause());
                                                            }

                                                            if (!connected && dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.FLAT.getName()))
                                                            {
                                                                connections.add(new JsonObject().put(ID, id).put(AIOpsObject.OBJECT_VENDOR, item.getString(AIOpsObject.OBJECT_VENDOR))
                                                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, id));
                                                            }
                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(exception);
                                                        }

                                                        tryToComplete(future, pendingItems, item.getString(filter));
                                                    });


                                    case VIRTUALIZATION ->
                                        // For virtualization objects, query dependencies with appropriate dependency levels
                                        // based on whether it's a cluster (vCenter, Hyper-V Cluster, etc.) or a host (ESXi, etc.)
                                            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
                                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter))
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, NMSConstants.VIRTUALIZATION_CLUSTER_TYPES.contains(item.getString(AIOpsObject.OBJECT_TYPE)) ? virtualizationClusterDependencyLevels : virtualizationESXiDependencyLevels),
                                                    DELIVERY_OPTIONS,
                                                    reply ->
                                                    {
                                                        try
                                                        {
                                                            root.put(ID, id).put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                            event.put(NMSConstants.OBJECT, item);

                                                            buildVirtualizationDependencies(reply, event, root, connections, objects, future, pendingItems);
                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(exception);
                                                        }

                                                        tryToComplete(future, pendingItems, item.getString(filter));
                                                    });

                                    case HCI ->
                                        // For Hyper-Converged Infrastructure (HCI) objects, query dependencies with appropriate levels
                                        // based on whether it's a cluster (Prism) or a host (Nutanix)
                                            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
                                                    new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter))
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, NMSConstants.HCI_CLUSTER_TYPES.contains(item.getString(AIOpsObject.OBJECT_TYPE)) ? hciClusterDependencyLevels : hciHostDependencyLevels),
                                                    DELIVERY_OPTIONS,
                                                    reply ->
                                                    {
                                                        try
                                                        {
                                                            root.put(ID, id).put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                            event.put(NMSConstants.OBJECT, item);

                                                            buildVirtualizationDependencies(reply, event, root, connections, objects, future, pendingItems);
                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(exception);
                                                        }

                                                        tryToComplete(future, pendingItems, item.getString(filter));
                                                    });

                                    case SDN ->
                                        // For Software-Defined Networking (SDN) objects, query dependencies with SDN-specific levels
                                        // SDN dependencies include controllers, vEdge routers, and other SDN components
                                            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
                                                    new JsonObject().put(DEPENDENCY_SOURCE, item.getString(filter))
                                                            .put(DEPENDENCY_LEVEL, sdnDependencyLevels),
                                                    DELIVERY_OPTIONS,
                                                    reply ->
                                                    {
                                                        try
                                                        {
                                                            root.put(ID, id).put(DEPENDENCY_CHILDREN, new JsonArray());

                                                            event.put(NMSConstants.OBJECT, item);

                                                            buildSDNDependencies(reply, event, root, objects, future, pendingItems);
                                                        }
                                                        catch (Exception exception)
                                                        {
                                                            LOGGER.error(exception);
                                                        }

                                                        tryToComplete(future, pendingItems, item.getString(filter));
                                                    });

                                    case OTHER ->
                                    {
                                        // AS of now CISCO UCS not supported. below code is for future purpose

                                        // for cisco ucs we don't identify parent child relations hence UI will draw topology based on raw result
//                                        event.put(DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName());
//
//                                        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + dependencyType + EventBusConstants.EVENT_QUERY,
//                                                new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter)).put(AIOpsConstants.DEPENDENCY_LEVEL, ciscoUCSDependencyLevels),
//                                                DELIVERY_OPTIONS,
//                                                handler ->
//                                                {
//                                                    try
//                                                    {
//                                                        buildOtherDependencies(handler, dependencyType, item, connections);
//                                                    }
//                                                    catch (Exception exception)
//                                                    {
//                                                        LOGGER.error(exception);
//                                                    }
//
//                                                    tryToComplete(future, pendingItems, item.getString(filter));
//                                                });
                                    }

                                    default ->
                                            LOGGER.warn(String.format("Unexpected value %s", NMSConstants.Category.valueOf(category)));
                                }
                            }

                            future.future().onComplete(result -> message.reply(event.put(RESULT, dependencyFormat.equalsIgnoreCase(DependencyFormat.FLAT.getName()) ? connections : root)));
                        }
                        else
                        {
                            message.reply(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                    .put(MESSAGE, "Invalid monitor type...."));
                        }
                    }
                    else
                    {
                        message.reply(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                .put(MESSAGE, ErrorMessageConstants.OBJECT_NOT_FOUND));
                    }
                }
                else
                {
                    message.reply(event.put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                            .put(MESSAGE, "Dependency type is null or empty...."));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, exception.getMessage());
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            try
            {
                switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_DEPENDENCY_MAPPER, UPDATE_DEPENDENCY_MAPPER, DELETE_DEPENDENCY_MAPPER, COMPLETE_TOPOLOGY, STOP_TOPOLOGY -> updateHierarchies();

                    default ->
                    {
                        // do nothing
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<Void>localConsumer(EVENT_PARENT_CHILD_HIERARCHY_FETCH, message ->
        {
            try
            {
                message.reply(JsonObject.mapFrom(hierarchies));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.fail(NOT_AVAILABLE, String.format("Failed to fetch parent-child hierarchies , reason > %s ", exception.getMessage()));
            }
        }).exceptionHandler(LOGGER::error);

        promise.complete();
    }

    /**
     * Builds dependencies for Software-Defined Networking (SDN) objects.
     * <p>
     * This method processes dependencies for SDN components like Cisco vManage controllers
     * and vEdge routers. It extracts SDN dependencies and builds the appropriate dependency
     * structure based on the object type.
     * <p>
     * For Cisco vManage controllers, it builds connections to other SDN components like
     * vSmart controllers, vBond orchestrators, and vEdge routers. For Cisco vEdge routers,
     * it builds tunnel connections to other vEdge routers.
     * <p>
     * This method handles different dependency levels:
     * <ul>
     *   <li>Level 4: Interface/tunnel level dependencies</li>
     *   <li>Level 7: Device level dependencies</li>
     * </ul>
     *
     * @param reply        The result of the dependency query
     * @param event        The original dependency query event
     * @param parent       The parent object to which children will be added
     * @param objects      Array of objects already processed
     * @param future       Promise to complete when the dependency building is done
     * @param pendingItems Map tracking pending items for completion handling
     */
    private void buildSDNDependencies(AsyncResult<Message<JsonObject>> reply, JsonObject event, JsonObject parent, JsonArray objects, Promise<Void> future, Map<String, Integer> pendingItems)
    {
        var item = event.getJsonObject(NMSConstants.OBJECT);

        event.remove(NMSConstants.OBJECT);

        var currentSource = item.getString(AIOpsObject.OBJECT_IP);

        var hierarchies = parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

        var instances = new HashMap<String, JsonObject>();

        if (reply.succeeded())
        {
            var dependencies = reply.result().body();

            if (dependencies != null && !dependencies.isEmpty())
            {
                dependencies = dependencies.getJsonObject(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

                if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_VMANAGE.getName()))
                {
                    for (var dependency : dependencies.getJsonObject(currentSource))
                    {
                        var localConnections = (JsonArray) dependency.getValue();

                        for (var localConnection : localConnections)
                        {
                            if (ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(localConnection)) != null)
                            {
                                var connectedChild = new JsonObject()
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, ObjectConfigStore.getStore().getItemByIP(currentSource))
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(localConnection)))
                                        .put(DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                        .put(DEPENDENCY_CHILDREN, new JsonArray());

                                hierarchies.add(connectedChild);

                                if (Objects.equals(Byte.valueOf(dependency.getKey()), DependencyLevel.SEVEN.getName()) && !objects.contains(CommonUtil.getString(localConnection)))
                                {
                                    objects.add(CommonUtil.getString(localConnection));

                                    pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                    buildHierarchies(ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(localConnection))), objects, connectedChild, future, pendingItems, event, null, null);
                                }
                            }
                        }
                    }
                }
                else if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_VEDGE.getName()))
                {
                    if (dependencies.containsKey(currentSource))
                    {
                        instances.computeIfAbsent(currentSource, value -> new JsonObject());

                        if (dependencies.getJsonObject(currentSource).containsKey(CommonUtil.getString(DependencyLevel.FOUR.getName())))
                        {
                            for (var localConnections : dependencies.getJsonObject(currentSource).getJsonArray(CommonUtil.getString(DependencyLevel.FOUR.getName())))
                            {
                                var key = currentSource + VALUE_SEPARATOR + CommonUtil.getString(localConnections);

                                if (dependencies.containsKey(key))
                                {
                                    for (var dependency : dependencies.getJsonObject(key).getJsonArray(CommonUtil.getString(DependencyLevel.SEVEN.getName())))
                                    {
                                        var destination = CommonUtil.getString(dependency).split(VALUE_SEPARATOR_WITH_ESCAPE)[0];

                                        if (!objects.contains(key + VALUE_SEPARATOR + dependency) && !objects.contains(dependency + VALUE_SEPARATOR + key) && ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(destination)) != null)
                                        {
                                            // passing this link to UI to show the visualization data and filter
                                            var connectedLink = key.replace(VALUE_SEPARATOR, COLON_SEPARATOR) + DASH_SEPARATOR + CommonUtil.getString(dependency).replace(VALUE_SEPARATOR, COLON_SEPARATOR);

                                            hierarchies.add(new JsonObject()
                                                    .put(AIOpsConstants.DEPENDENCY_SOURCE, ObjectConfigStore.getStore().getItemByIP(currentSource))
                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(destination)))
                                                    .put(DEPENDENCY_CONNECTED_LINK, connectedLink)
                                                    .put(DEPENDENCY_CHILDREN, new JsonArray()));

                                            objects.add(key + VALUE_SEPARATOR + dependency);        // avoid duplicate links

                                            instances.get(currentSource).put(connectedLink, EMPTY_VALUE);
                                        }
                                    }
                                }
                            }
                        }

                        parent.put(INSTANCES, instances);
                    }
                }
                else if (item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_ACI.getName()))
                {
                    for (var dependency : dependencies.getJsonObject(currentSource))
                    {
                        for (var localConnection : (JsonArray) dependency.getValue())
                        {
                            var key = currentSource + VALUE_SEPARATOR + localConnection;

                            // Process only if current source is a valid object with existing dependencies
                            if (dependencies.containsKey(key) && ObjectConfigStore.getStore().getItemByIP(currentSource) != null)
                            {
                                var connectedChild = new JsonObject()
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, ObjectConfigStore.getStore().getItemByIP(currentSource))
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, localConnection)
                                        .put(DEPENDENCY_LEVEL, DependencyLevel.SEVEN.getName())
                                        .put(DEPENDENCY_CONNECTED_LINK, key)
                                        .put(DEPENDENCY_CHILDREN, new JsonArray());

                                hierarchies.add(connectedChild);

                                buildCiscoACILeafDependencies(dependencies, key, connectedChild, pendingItems, event, objects, future);

                            }
                            //If the current source is a leaf (i.e., not present in the ObjectConfigStore)
                            else if (ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(currentSource)) == null)
                            {
                                hierarchies.add(new JsonObject()
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, currentSource.split(VALUE_SEPARATOR)[0] + VALUE_SEPARATOR + currentSource.split(VALUE_SEPARATOR)[3])
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, localConnection.toString().split(VALUE_SEPARATOR)[0] + VALUE_SEPARATOR + localConnection.toString().split(VALUE_SEPARATOR)[3])
                                        .put(DEPENDENCY_LEVEL, DependencyLevel.SEVEN.getName())
                                        .put(DEPENDENCY_CONNECTED_LINK, key)
                                        .put(DEPENDENCY_CHILDREN, new JsonArray()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Builds a bottom-to-top dependency hierarchy (child-to-parent relationships).
     * <p>
     * This method queries the dependency manager for dependencies of the given item,
     * identifies parent objects, and adds them to the hierarchy. It then recursively
     * calls itself for each parent object to build the complete hierarchy upward.
     * <p>
     * This is used for the BOTTOM_TOP_HIERARCHY dependency format, which shows
     * dependencies from a child object up to its parents and ancestors.
     *
     * @param parents      The JsonArray to populate with parent objects
     * @param item         The current item for which to find parents
     * @param pendingItems Map tracking pending items for completion handling
     * @param future       Promise to complete when the hierarchy building is done
     * @param event        The original dependency query event
     * @param filter       The filter field (IP or target) to use for dependency lookup
     * @param objects      Array of objects already processed
     */
    private void buildHierarchies(JsonArray parents, JsonObject item, Map<String, Integer> pendingItems, Promise<Void> future, JsonObject event, String filter, JsonArray objects)
    {
        var currentSource = event.getString(filter);

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + event.getString(AIOpsConstants.DEPENDENCY_TYPE) + EventBusConstants.EVENT_QUERY,
                new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getString(filter)),
                DELIVERY_OPTIONS,
                reply ->
                {
                    try
                    {
                        if (reply.succeeded())
                        {
                            var result = reply.result().body();

                            var dependencies = result.getJsonObject(event.getString(AIOpsConstants.DEPENDENCY_TYPE));

                            if (dependencies != null)
                            {
                                var qualifiedParents = new JsonArray();

                                for (var dependency : dependencies)
                                {
                                    var connections = JsonObject.mapFrom(dependency.getValue());

                                    if (connections.containsKey(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())) && !connections.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())).isEmpty())
                                    {
                                        for (var destination : getDestinations(connections.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())), filter))
                                        {
                                            //filter if destination is not same as requested monitor and also not qualified already
                                            if (!destination.equals(item.getLong(ID)) && !qualifiedParents.contains(destination))
                                            {
                                                qualifiedParents.add(destination);

                                                var parent = new JsonArray();

                                                parents.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                        .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                                        .put(AIOpsConstants.DEPENDENCY_PARENT, parent));

                                                if (!event.containsKey(AIOpsConstants.RECURSIVE_DEPENDENCIES) || event.getString(AIOpsConstants.RECURSIVE_DEPENDENCIES).equalsIgnoreCase(YES))
                                                {
                                                    var object = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(destination));

                                                    if (object != null && valid(objects, object, filter))
                                                    {
                                                        pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                                        buildHierarchies(parent, object, pendingItems, future, event, filter, objects);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    tryToComplete(future, pendingItems, currentSource);
                });
    }

    /**
     * Builds a top-to-bottom dependency hierarchy (parent-to-child relationships).
     * <p>
     * This method queries the dependency manager for dependencies of the given object,
     * identifies child objects, and adds them to the hierarchy. It then recursively
     * calls itself for each child object to build the complete hierarchy downward.
     * <p>
     * This is used for the TOP_BOTTOM_HIERARCHY dependency format, which shows
     * dependencies from a parent object down to its children and descendants.
     * <p>
     * The method adapts its behavior based on the object's category (network, virtualization, HCI, SDN)
     * and uses different dependency levels for different types of objects.
     *
     * @param object               The current object for which to find children
     * @param objects              Array of objects already processed
     * @param parent               The parent object to which children will be added
     * @param promise              Promise to complete when the hierarchy building is done
     * @param pendingItems         Map tracking pending items for completion handling
     * @param event                The original dependency query event
     * @param qualifiedConnections Set of connections already processed (to avoid duplicates)
     */
    private void buildHierarchies(JsonObject object, JsonArray objects, JsonObject parent, Promise<Void> promise, Map<String, Integer> pendingItems, JsonObject event, Set<String> qualifiedConnections, Set<String> filters)
    {
        var hierarchies = parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

        var category = object.getString(AIOpsObject.OBJECT_CATEGORY);

        var type = object.getString(AIOpsObject.OBJECT_TYPE);

        var queryLevels = networkDependencyLevels;

        if (category.equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()))
        {
            queryLevels = NMSConstants.VIRTUALIZATION_CLUSTER_TYPES.contains(type) ? virtualizationClusterDependencyLevels : virtualizationESXiDependencyLevels;
        }
        else if (category.equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
        {
            queryLevels = NMSConstants.HCI_CLUSTER_TYPES.contains(type) ? hciClusterDependencyLevels : hciHostDependencyLevels;
        }
        else if (category.equalsIgnoreCase(NMSConstants.Category.SDN.getName()))
        {
            queryLevels = sdnDependencyLevels;
        }

        var currentSource = event.getString(AIOpsObject.OBJECT_IP);

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("building hierarchies for %s source with %s category and type %s", currentSource, category, type));
        }

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + event.getString(AIOpsConstants.DEPENDENCY_TYPE) + EventBusConstants.EVENT_QUERY,
                new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_LEVEL, queryLevels),
                DELIVERY_OPTIONS,
                reply ->
                {
                    try
                    {
                        if (category.equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()) || category.equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
                        {
                            event.put(NMSConstants.OBJECT, object);

                            buildVirtualizationDependencies(reply, event, parent, null, objects, promise, pendingItems);
                        }
                        else if (category.equalsIgnoreCase(NMSConstants.Category.SDN.getName()))
                        {
                            event.put(NMSConstants.OBJECT, object);

                            buildSDNDependencies(reply, event, parent, objects, promise, pendingItems);
                        }
                        else if (NMSConstants.isWireless(NMSConstants.Type.valueOfName(type)))
                        {
                            buildWirelessDependencies(reply, event.getString(AIOpsConstants.DEPENDENCY_TYPE), AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), object, parent, null);
                        }
                        else if (category.equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                        {
                            if (type.equalsIgnoreCase(NMSConstants.Type.WINDOWS_CLUSTER.getName()))
                            {
                                event.put(NMSConstants.OBJECT, object);

                                buildWindowsClusterDependencies(reply, event, parent, null, objects, pendingItems, promise);
                            }
                            else
                            {
                                buildApplicationDependencies(reply, event.getString(AIOpsConstants.DEPENDENCY_TYPE), AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), object, parent, null);
                            }
                        }
                        else if (category.equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                        {
                            if (reply.succeeded())
                            {
                                var result = reply.result().body();

                                var dependencies = result.getJsonObject(event.getString(AIOpsConstants.DEPENDENCY_TYPE));

                                if (dependencies != null)
                                {
                                    // qualified levels
                                    var levels = getLevels(event);

                                    var links = new HashMap<Long, List<JsonObject>>();

                                    var filter = parent.getString(AIOpsConstants.DEPENDENCY_FILTER);

                                    for (var dependency : dependencies)
                                    {
                                        if (dependency.getKey().contains(GlobalConstants.VALUE_SEPARATOR))
                                        {
                                            var connections = JsonObject.mapFrom(dependency.getValue());

                                            JsonArray parents = null;

                                            for (var connection : connections)
                                            {
                                                var level = CommonUtil.getByteValue(connection.getKey());

                                                if (level.equals(AIOpsConstants.DependencyLevel.MINUS_ONE.getName()))
                                                {
                                                    parents = (JsonArray) connection.getValue();
                                                }

                                                /* condition 1 -> qualified levels contains dependency level
                                                 condition 2 -> qualify (1) if level is 5 (L3) as we don't have parent child for L3 devices
                                                 condition 2(1) ->  case of reverse link where we need to skip parent evaluation i.e. passover parent check
                                                 condition 2(2) -> check parent is same as requested monitor
                                                 condition 2(3) -> check whether present object request is current source
                                                 */
                                                if (levels.contains(level)
                                                        && (level.equals(AIOpsConstants.DependencyLevel.FIVE.getName())
                                                        || parent.containsKey("passover.parent")
                                                        || (connections.containsKey(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())) && connections.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())).contains(object.getString(AIOpsObject.OBJECT_IP)))
                                                        || (connections.containsKey(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())) && connections.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.MINUS_ONE.getName())).contains(currentSource))))
                                                {
                                                    var connectedLink = dependency.getKey().split(GlobalConstants.VALUE_SEPARATOR_WITH_ESCAPE)[1].trim();

                                                    if (filter == null || (filter.equalsIgnoreCase(CommonUtil.getString(level) + VALUE_SEPARATOR + connectedLink) || (parents == null || parents.contains(object.getString(AIOpsObject.OBJECT_IP)))))
                                                    {
                                                        var destinations = (JsonArray) connection.getValue();

                                                        for (var index = 0; index < destinations.size(); index++)
                                                        {
                                                            var destinationLink = "";

                                                            var destination = destinations.getString(index);

                                                            if (destination.contains(VALUE_SEPARATOR))
                                                            {
                                                                var tokens = destination.split(VALUE_SEPARATOR_WITH_ESCAPE);

                                                                destination = tokens[0];

                                                                destinationLink = tokens[1];
                                                            }

                                                            var id = ObjectConfigStore.getStore().getItemByIP(destination);

                                                            if (id != null && (filters == null || filters.isEmpty() || (event.getString("topology.filter.target.type", EMPTY_VALUE).contains("include") == filters.contains(destination))))
                                                            {
                                                                var item = ObjectConfigStore.getStore().getItem(id);

                                                                if (item != null)
                                                                {
                                                                    var link = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                                                            .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, connectedLink)
                                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, level).put(AIOpsConstants.DEPENDENCY_DESTINATION, id)
                                                                            .put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                                    if (CommonUtil.isNotNullOrEmpty(destinationLink))
                                                                    {
                                                                        link.put(AIOpsConstants.DEPENDENCY_FILTER, CommonUtil.getString(level) + VALUE_SEPARATOR + destinationLink);

                                                                        qualifiedConnections.add(object.getString(AIOpsObject.OBJECT_IP) + VALUE_SEPARATOR + destinationLink + VALUE_SEPARATOR + connectedLink + VALUE_SEPARATOR + connection.getKey());
                                                                    }

                                                                    // for ui event of topology rendering need to send interface index and interface name both as
                                                                    // and also for availability correlation in alert dashboard while showing correlation map.

                                                                    if (CommonUtil.isNotNullOrEmpty(event.getString(EventBusConstants.EVENT_TYPE)) && (event.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH) || event.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_AVAILABILITY_CORRELATION)))
                                                                    {
                                                                        var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID), NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()));

                                                                        if (metric != null)
                                                                        {
                                                                            var items = MetricConfigStore.getStore().getObjects(metric.getJsonObject(Metric.METRIC_CONTEXT), NMSConstants.INTERFACE_INDEX, NMSConstants.INTERFACE);

                                                                            if (CommonUtil.isNotNullOrEmpty(items.getString(connectedLink)))
                                                                            {
                                                                                link.put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, items.getString(connectedLink));
                                                                            }
                                                                        }
                                                                    }

                                                                    links.computeIfAbsent(id, value -> new ArrayList<>()).add(link);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (var connectedChildren : links.values())
                                    {
                                        for (var connectedChild : connectedChildren)
                                        {
                                            if (!(CommonUtil.isNullOrEmpty(parent.getString(AIOpsConstants.DEPENDENCY_FILTER)) && connectedChild.getLong(AIOpsConstants.DEPENDENCY_DESTINATION).equals(parent.getLong(AIOpsConstants.DEPENDENCY_SOURCE))))
                                            {
                                                var item = ObjectConfigStore.getStore().getItem(connectedChild.getLong(AIOpsConstants.DEPENDENCY_DESTINATION));

                                                if (item != null)
                                                {
                                                    /* if fresh link found i.e. not a reverse connection than adding key passover.parent so that next time parent evaluation can be skipped.
                                                    what's the need for parent evaluation ? answer is that whenever user click to device except seed IP on left hierarchy in topology then
                                                    we need to consider parent to child connections & reject child to parent connections.
                                                    */
                                                    if (parent.containsKey(AIOpsConstants.DEPENDENCY_SOURCE) && !Objects.equals(connectedChild.getLong(AIOpsConstants.DEPENDENCY_DESTINATION), parent.getLong(AIOpsConstants.DEPENDENCY_SOURCE)))
                                                    {
                                                        connectedChild.put("passover.parent", true);
                                                    }

                                                    hierarchies.add(connectedChild);

                                                    filter = CommonUtil.isNotNullOrEmpty(connectedChild.getString(AIOpsConstants.DEPENDENCY_FILTER)) ? connectedChild.getString(AIOpsConstants.DEPENDENCY_FILTER).split(VALUE_SEPARATOR_WITH_ESCAPE)[1] : "";

                                                    var connection = EMPTY_VALUE;

                                                    if (connectedChild.getString(DEPENDENCY_CONNECTED_LINK).contains(DASH_SEPARATOR))
                                                    {
                                                        connection = item.getString(AIOpsObject.OBJECT_IP) + VALUE_SEPARATOR + connectedChild.getString(DEPENDENCY_CONNECTED_LINK).split(DASH_SEPARATOR)[1] + VALUE_SEPARATOR + filter + VALUE_SEPARATOR + connectedChild.getString(AIOpsConstants.DEPENDENCY_LEVEL);
                                                    }
                                                    else
                                                    {
                                                        connection = item.getString(AIOpsObject.OBJECT_IP) + VALUE_SEPARATOR + connectedChild.getString(DEPENDENCY_CONNECTED_LINK) + VALUE_SEPARATOR + filter + VALUE_SEPARATOR + connectedChild.getString(AIOpsConstants.DEPENDENCY_LEVEL);
                                                    }

                                                    if ((valid(objects, item, AIOpsObject.OBJECT_IP) || !qualifiedConnections.contains(connection))
                                                            && !item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_UCS.getName()) && (!event.containsKey(AIOpsConstants.RECURSIVE_DEPENDENCIES) || event.getString(AIOpsConstants.RECURSIVE_DEPENDENCIES).equalsIgnoreCase(YES)))
                                                    {
                                                        qualifiedConnections.add(connection);

                                                        pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                                        // last params means both L2 and L3 link connected so need to pass current level and recursive dependencies will build for that level only else pass null so both level dependencies build
                                                        buildHierarchies(item, objects, connectedChild, promise, pendingItems, event, qualifiedConnections, filters);
                                                    }

                                                    /* if fresh connection link found but it that link already exist then we need to ignore it.
                                                    why we need this condition? to avoid ring topology
                                                    For ex : if link 10.47(19) -> 10.43(10119) already processed by DQP & now link 10.43(10119) -> 10.47(19) comes then ignore it
                                                    */
                                                    else if (connectedChild.containsKey("passover.parent") && qualifiedConnections.contains(connection))
                                                    {
                                                        hierarchies.remove(connectedChild);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                LOGGER.error(reply.cause());
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    tryToComplete(promise, pendingItems, currentSource);
                });
    }

    /**
     * Builds flat connection lists for dependencies.
     * <p>
     * This method queries the dependency manager for dependencies of the given object
     * and builds a flat list of connections without hierarchy. It adapts its behavior
     * based on the object's category (network, virtualization, HCI, server, etc.) and
     * delegates to specialized methods for specific object types.
     * <p>
     * This is used for the FLAT dependency format, which shows raw dependency connections
     * primarily used for root cause analysis.
     *
     * @param object       The object for which to build connections
     * @param objects      Array of objects already processed
     * @param connections  The JsonArray to populate with connection information
     * @param promise      Promise to complete when the connection building is done
     * @param pendingItems Map tracking pending items for completion handling
     * @param event        The original dependency query event
     */
    private void buildConnections(JsonObject object, JsonArray objects, JsonArray connections, Promise<Void> promise, Map<String, Integer> pendingItems, JsonObject event)
    {
        var currentSource = event.getString(AIOpsObject.OBJECT_IP);

        var category = object.getString(AIOpsObject.OBJECT_CATEGORY);

        var type = object.getString(AIOpsObject.OBJECT_TYPE);

        var queryLevels = networkDependencyLevels;

        if (category.equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()))
        {
            queryLevels = NMSConstants.VIRTUALIZATION_CLUSTER_TYPES.contains(type) ? virtualizationClusterDependencyLevels : virtualizationESXiDependencyLevels;
        }
        if (category.equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
        {
            queryLevels = NMSConstants.HCI_CLUSTER_TYPES.contains(type) ? hciClusterDependencyLevels : hciHostDependencyLevels;
        }
        else if (type.equalsIgnoreCase(NMSConstants.Type.CISCO_UCS.getName()))
        {
            queryLevels = ciscoUCSDependencyLevels;
        }

        vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + event.getString(AIOpsConstants.DEPENDENCY_TYPE) + EventBusConstants.EVENT_QUERY,
                new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_LEVEL, queryLevels),
                DELIVERY_OPTIONS,
                reply ->
                {
                    try
                    {
                        if (category.equalsIgnoreCase(NMSConstants.Category.VIRTUALIZATION.getName()) || category.equalsIgnoreCase(NMSConstants.Category.HCI.getName()))
                        {
                            event.put(NMSConstants.OBJECT, object);

                            buildVirtualizationDependencies(reply, event, null, connections, objects, promise, pendingItems);
                        }
                        else if (NMSConstants.isWireless(NMSConstants.Type.valueOfName(type)))
                        {
                            buildWirelessDependencies(reply, event.getString(AIOpsConstants.DEPENDENCY_TYPE), AIOpsConstants.DependencyFormat.FLAT.getName(), object, null, connections);
                        }
                        else if (type.equalsIgnoreCase(NMSConstants.Type.CISCO_UCS.getName()))
                        {
//                            buildOtherDependencies(handler, event.getString(AIOpsConstants.DEPENDENCY_TYPE), object, connections);
                        }
                        else if (category.equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                        {
                            if (type.equalsIgnoreCase(NMSConstants.Type.WINDOWS_CLUSTER.getName()))
                            {
                                event.put(NMSConstants.OBJECT, object);

                                buildWindowsClusterDependencies(reply, event, null, connections, objects, pendingItems, promise);
                            }
                            else
                            {
                                buildApplicationDependencies(reply, event.getString(AIOpsConstants.DEPENDENCY_TYPE), AIOpsConstants.DependencyFormat.FLAT.getName(), object, null, connections);
                            }
                        }
                        else
                        {
                            var connected = false;

                            if (reply.succeeded())
                            {
                                var result = reply.result().body();

                                var dependencies = result.getJsonObject(event.getString(AIOpsConstants.DEPENDENCY_TYPE));

                                if (dependencies != null)
                                {
                                    var qualifiedLevels = getLevels(event);

                                    for (var dependency : dependencies)
                                    {
                                        if (dependency.getKey().contains(GlobalConstants.VALUE_SEPARATOR))
                                        {
                                            for (var connection : JsonObject.mapFrom(dependency.getValue()))
                                            {
                                                if (qualifiedLevels.contains(CommonUtil.getByteValue(connection.getKey())))
                                                {
                                                    var link = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(connection.getKey()));

                                                    // for network category destination would be network / server device and for server category it would be applications
                                                    var destinations = new JsonArray();

                                                    var valid = false;

                                                    if (category.equalsIgnoreCase(NMSConstants.Category.NETWORK.getName()))
                                                    {
                                                        destinations = getDestinations((JsonArray) connection.getValue(), AIOpsObject.OBJECT_IP);

                                                        if (!destinations.isEmpty())
                                                        {
                                                            valid = true;

                                                            link.put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, dependency.getKey().split(GlobalConstants.VALUE_SEPARATOR_WITH_ESCAPE)[1].trim());
                                                        }
                                                    }

                                                    if (valid && !destinations.isEmpty())
                                                    {
                                                        connected = true;

                                                        connections.add(link.put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations));

                                                        if (!event.containsKey(AIOpsConstants.RECURSIVE_DEPENDENCIES) || event.getString(AIOpsConstants.RECURSIVE_DEPENDENCIES).equalsIgnoreCase(YES))
                                                        {
                                                            for (var index = 0; index < destinations.size(); index++)
                                                            {
                                                                var item = ObjectConfigStore.getStore().getItem(destinations.getLong(index));

                                                                if (item != null && valid(objects, item, AIOpsObject.OBJECT_IP))
                                                                {
                                                                    pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                                                    buildConnections(item, objects, connections, promise, pendingItems, event);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to map dependency for monitor %s with dependency level %s , reason : %s", object.getString(AIOpsObject.OBJECT_IP), link.getValue(AIOpsConstants.DEPENDENCY_LEVEL), "Either interface not present in store or connected targets not found"));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                LOGGER.error(reply.cause());
                            }

                            if (!connected)
                            {
                                connections.add(new JsonObject().put(ID, object.getLong(ID)).put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                        .put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(networkDependencyLevels.getValue(0))).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }

                    tryToComplete(promise, pendingItems, currentSource);
                });
    }

    /**
     * Extracts destination objects from a JsonArray based on a filter.
     * <p>
     * This method takes a JsonArray of object identifiers (IPs or targets) and
     * converts them to object IDs by looking them up in the ObjectConfigStore.
     * It filters out any identifiers that don't correspond to valid objects.
     *
     * @param objects The JsonArray of object identifiers to process
     * @param filter  The filter field to use (OBJECT_IP or OBJECT_TARGET)
     * @return A JsonArray of object IDs corresponding to the valid identifiers
     */
    private JsonArray getDestinations(JsonArray objects, String filter)
    {
        return filter.equalsIgnoreCase(AIOpsObject.OBJECT_TARGET)
                ? new JsonArray(objects.stream().filter(object -> ObjectConfigStore.getStore().getItemByTarget(CommonUtil.getString(object)) != null)
                .map(target -> ObjectConfigStore.getStore().getItemByTarget(CommonUtil.getString(target))).collect(Collectors.toList()))
                : new JsonArray(objects.stream().filter(object -> ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(object)) != null)
                .map(target -> ObjectConfigStore.getStore().getItemByIP(CommonUtil.getString(target))).collect(Collectors.toList()));
    }

    /**
     * Gets the dependency levels to use for a query based on the event.
     * <p>
     * This method determines which dependency levels should be included in a query
     * based on the event type. It always includes level 6 (L2 network dependencies),
     * and conditionally includes level 5 (L3 network dependencies) if the event is
     * not from the availability correlation engine.
     *
     * @param event The event containing context information for the query
     * @return A list of dependency levels to include in the query
     */
    private List<Byte> getLevels(JsonObject event)
    {
        var qualifiedLevels = new ArrayList<Byte>(2);

        qualifiedLevels.add(AIOpsConstants.DependencyLevel.SIX.getName());

        // for availability correlation no need to get L3 dependencies
        if (!event.containsKey(EventBusConstants.EVENT_TYPE) || !event.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_AVAILABILITY_CORRELATION))
        {
            qualifiedLevels.add(AIOpsConstants.DependencyLevel.FIVE.getName());
        }

        return qualifiedLevels;
    }

    /**
     * Removes temporary fields from a context object.
     * <p>
     * This method cleans up a context JsonObject by removing temporary fields
     * that were added during processing and are no longer needed. This includes
     * the object IP, target, and dependency level fields.
     *
     * @param context The context JsonObject to clean up
     */
    private void cleanUp(JsonObject context)
    {
        context.remove(AIOpsObject.OBJECT_IP);

        context.remove(AIOpsObject.OBJECT_TARGET);

        context.remove(AIOpsConstants.DEPENDENCY_LEVEL);
    }

    /**
     * Validates an object against a filter and adds it to the processed objects array.
     * <p>
     * This method checks if an object is valid for processing by verifying that:
     * <ul>
     *   <li>It hasn't already been processed (not in the objects array)</li>
     *   <li>It exists in the ObjectConfigStore with the specified filter value</li>
     * </ul>
     * If the object is valid, it adds the object's filter value to the objects array
     * to prevent processing it again in the future.
     *
     * @param objects The array of objects already processed
     * @param object  The object to validate
     * @param filter  The filter field to use (OBJECT_IP or OBJECT_TARGET)
     * @return true if the object is valid and hasn't been processed before, false otherwise
     */
    private boolean valid(JsonArray objects, JsonObject object, String filter)
    {
        var valid = !objects.contains(object.getString(filter)) && (filter.equalsIgnoreCase(AIOpsObject.OBJECT_IP)
                ? ObjectConfigStore.getStore().getItemByIP(object.getString(AIOpsObject.OBJECT_IP)) != null
                : ObjectConfigStore.getStore().getItemByTarget(object.getString(AIOpsObject.OBJECT_TARGET)) != null);

        if (valid)
        {
            objects.add(object.getString(filter));
        }
        return valid;
    }

    /**
     * Decrements the pending items count and completes the promise when all items are processed.
     * <p>
     * This method is called after processing a dependency to decrement the count of pending
     * items for a source. When the count reaches zero, it completes the promise to signal
     * that all dependencies for the source have been processed.
     * <p>
     * This method is essential for managing asynchronous dependency processing and ensuring
     * that the promise is completed only when all dependencies have been processed.
     *
     * @param promise      The promise to complete when all items are processed
     * @param pendingItems Map tracking pending items for completion handling
     * @param source       The source for which to decrement the pending items count
     */
    private void tryToComplete(Promise<Void> promise, Map<String, Integer> pendingItems, String source)
    {
        pendingItems.put(source, pendingItems.containsKey(source) ? pendingItems.get(source) - 1 : 0);

        if (pendingItems.get(source) == 0)
        {
            promise.complete();
        }
    }

    /**
     * Builds dependencies for Windows Cluster objects.
     * <p>
     * This method processes dependencies for Windows Cluster objects, which have special
     * handling requirements. It extracts cluster nodes and builds the appropriate dependency
     * structure based on the requested dependency format (flat or hierarchical).
     * <p>
     * For TOP_BOTTOM_HIERARCHY format, it builds a parent-child relationship between the
     * cluster and its nodes. For FLAT format, it adds direct connections between the cluster
     * and its nodes to the connections array.
     *
     * @param asyncResult  The result of the dependency query
     * @param event        The original dependency query event
     * @param parent       The parent object (for hierarchical format) or null (for flat format)
     * @param connections  The connections array to populate (for flat format)
     * @param objects      Array of objects already processed
     * @param pendingItems Map tracking pending items for completion handling
     * @param future       Promise to complete when the dependency building is done
     */
    private void buildWindowsClusterDependencies(AsyncResult<Message<JsonObject>> asyncResult, JsonObject event, JsonObject parent, JsonArray connections, JsonArray objects, Map<String, Integer> pendingItems, Promise<Void> future)
    {
        var connected = false;

        var dependencyType = event.getString(AIOpsConstants.DEPENDENCY_TYPE);

        var dependencyFormat = event.getString(DEPENDENCY_FORMAT);

        var currentSource = event.getString(AIOpsObject.OBJECT_IP);

        var item = event.getJsonObject(NMSConstants.OBJECT);

        event.remove(NMSConstants.OBJECT);

        if (asyncResult.succeeded())
        {
            var result = asyncResult.result().body();

            var dependencies = result.getJsonObject(dependencyType);

            if (dependencies != null)
            {
                for (var dependency : dependencies)
                {
                    if (dependency.getKey().equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_IP)))
                    {
                        for (var connection : JsonObject.mapFrom(dependency.getValue()))
                        {
                            if (CommonUtil.getByteValue(connection.getKey()).equals(AIOpsConstants.DependencyLevel.SEVEN.getName()))
                            {
                                if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                                {
                                    for (var destination : (JsonArray) connection.getValue())
                                    {
                                        var server = CommonUtil.getString(destination);

                                        var id = ObjectConfigStore.getStore().getItemByIP(server);

                                        if (id == null)
                                        {
                                            id = ObjectConfigStore.getStore().getIdByObjectName(server);

                                            if (id == null)
                                            {
                                                id = ObjectConfigStore.getStore().getItemByTarget(server);
                                            }
                                        }

                                        if (id != null)
                                        {
                                            var connectedServer = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, id)
                                                    .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, connection.getKey())
                                                    .put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                            parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).add(connectedServer);

                                            var object = ObjectConfigStore.getStore().getItem(id);

                                            if (valid(objects, object, AIOpsObject.OBJECT_IP))
                                            {
                                                pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                                buildHierarchies(object, objects, connectedServer, future, pendingItems, event, null, null);
                                            }
                                        }
                                        else
                                        {
                                            parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, server)
                                                    .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, connection.getKey()));
                                        }

                                    }
                                }
                                else
                                {
                                    var destinations = (JsonArray) connection.getValue();

                                    if (!destinations.isEmpty())
                                    {
                                        connected = true;

                                        connections.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations)
                                                .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.SEVEN.getName()));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        else
        {
            LOGGER.error(asyncResult.cause());
        }

        if (!connected && dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.FLAT.getName()))
        {
            connections.add(new JsonObject().put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN));
        }
    }

    /**
     * Builds dependencies for application objects.
     * <p>
     * This method processes dependencies for application objects like databases, web servers,
     * and other application services. It extracts application dependencies and builds the
     * appropriate dependency structure based on the requested dependency format.
     * <p>
     * For TOP_BOTTOM_HIERARCHY format, it builds a parent-child relationship between the
     * application and its dependencies. For FLAT format, it adds direct connections between
     * the application and its dependencies to the connections array.
     * <p>
     * This method handles application-level dependencies (levels 0-3) which represent
     * different types of applications like databases, middleware, web servers, and services.
     *
     * @param asyncResult      The result of the dependency query
     * @param dependencyType   The type of dependency (local.domain or cross.domain)
     * @param dependencyFormat The format of the dependency (flat or hierarchical)
     * @param item             The application object for which to build dependencies
     * @param parent           The parent object (for hierarchical format) or null (for flat format)
     * @param connections      The connections array to populate (for flat format)
     */
    private void buildApplicationDependencies(AsyncResult<Message<JsonObject>> asyncResult, String dependencyType, String dependencyFormat, JsonObject item, JsonObject parent, JsonArray connections)
    {
        var connected = false;

        if (asyncResult.succeeded())
        {
            var result = asyncResult.result().body();

            var dependencies = result.getJsonObject(dependencyType);

            if (dependencies != null)
            {
                var applications = new HashSet<String>();

                Map.Entry<String, Object> serverContext = null;

                for (var dependency : dependencies)
                {
                    if (dependency.getKey().contains(VALUE_SEPARATOR))
                    {
                        for (var connection : JsonObject.mapFrom(dependency.getValue()))
                        {
                            var level = CommonUtil.getByteValue(connection.getKey());

                            if (AIOpsConstants.hasApplicationLevel(level))
                            {
                                if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                                {
                                    for (var application : (JsonArray) connection.getValue())
                                    {
                                        applications.add(CommonUtil.getString(application));

                                        parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level)
                                                .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, dependency.getKey().split(GlobalConstants.VALUE_SEPARATOR_WITH_ESCAPE)[1].trim())
                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, application));
                                    }
                                }
                                else
                                {
                                    var destinations = (JsonArray) connection.getValue();

                                    if (!destinations.isEmpty())
                                    {
                                        destinations.stream().map(CommonUtil::getString).forEach(applications::add);

                                        connected = true;

                                        connections.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations)
                                                .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, dependency.getKey().split(GlobalConstants.VALUE_SEPARATOR_WITH_ESCAPE)[1].trim())
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, level));
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        serverContext = dependency;
                    }
                }

                // for windows service application mapping (in dependency we don't track service  -> application hence for windows service will directly map server -> app with unknown from server itself dependency)
                if (serverContext != null && item.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()))
                {
                    if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                    {
                        var destinations = new JsonArray();

                        for (var connection : JsonObject.mapFrom(serverContext.getValue()))
                        {
                            if (AIOpsConstants.hasApplicationLevel(CommonUtil.getByteValue(connection.getKey())))
                            {
                                for (var application : (JsonArray) connection.getValue())
                                {
                                    if (!applications.contains(CommonUtil.getString(application))) // ignore already qualified applications
                                    {
                                        destinations.add(application);
                                    }
                                }
                            }
                        }

                        if (!destinations.isEmpty())
                        {
                            for (var destination : destinations)
                            {
                                parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                        .put(AIOpsConstants.DEPENDENCY_DESTINATION, destination)
                                        .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.LEVELS_BY_APPLICATIONS.get(CommonUtil.getString(destination))));
                            }
                        }
                    }
                    else
                    {
                        for (var connection : JsonObject.mapFrom(serverContext.getValue()))
                        {
                            if (AIOpsConstants.hasApplicationLevel(CommonUtil.getByteValue(connection.getKey())))
                            {
                                var destinations = new JsonArray();

                                for (var application : (JsonArray) connection.getValue())
                                {
                                    if (!applications.contains(CommonUtil.getString(application))) // ignore already qualified applications
                                    {
                                        destinations.add(application);
                                    }
                                }

                                if (!destinations.isEmpty())
                                {
                                    connections.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID))
                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(connection.getKey()))
                                            .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations));
                                }
                            }
                        }
                    }
                }
            }
        }
        else
        {
            LOGGER.error(asyncResult.cause());
        }

        if (!connected && dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.FLAT.getName()))
        {
            connections.add(new JsonObject().put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN));
        }
    }

//    private void buildOtherDependencies(AsyncResult<Message<JsonObject>> handler, String dependencyType, JsonObject item, JsonArray connections)
//    {
//        var connected = false;
//
//        if (handler.succeeded())
//        {
//            var result = handler.result().body();
//
//            var dependencies = result.getJsonObject(dependencyType);
//
//            if (dependencies != null)
//            {
//                for (var dependency : dependencies)
//                {
//                    if (dependency.getKey().contains(GlobalConstants.VALUE_SEPARATOR))
//                    {
//                        var source = dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim();
//
//                        for (var connection : JsonObject.mapFrom(dependency.getValue()))
//                        {
//                            var links = new HashMap<String, JsonArray>();
//
//                            for (var target : (JsonArray) connection.getValue())
//                            {
//                                var destination = CommonUtil.getString(target);
//
//                                if (destination.contains(VALUE_SEPARATOR))
//                                {
//                                    links.computeIfAbsent(destination.split(VALUE_SEPARATOR_WITH_ESCAPE)[0].trim(), value -> new JsonArray()).add(destination.split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim());
//                                }
//                            }
//
//                            if (!links.isEmpty())
//                            {
//                                connected = true;
//
//                                for (var link : links.entrySet())
//                                {
//                                    connections.add(new JsonObject().put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(connection.getKey()))
//                                            .put(AIOpsConstants.DEPENDENCY_SOURCE, source).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, link.getKey()).put(AIOpsConstants.DEPENDENCY_DESTINATION, link.getValue()));
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        else
//        {
//            LOGGER.error(handler.cause());
//        }
//
//        if (!connected)
//        {
//            connections.add(new JsonObject().put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_SOURCE, ObjectConfigStore.getStore().getItemByIP(item.getString(AIOpsObject.OBJECT_IP))));
//        }
//    }

    /**
     * Builds dependencies for virtualization and HCI objects.
     * <p>
     * This method processes dependencies for virtualization objects (vCenter, ESXi, Hyper-V, etc.)
     * and Hyper-Converged Infrastructure (HCI) objects (Prism, Nutanix, etc.). It extracts
     * virtualization dependencies and builds the appropriate dependency structure based on
     * the requested dependency format.
     * <p>
     * For TOP_BOTTOM_HIERARCHY format, it builds a multi-level hierarchy:
     * <ul>
     *   <li>Cluster -> Hypervisor -> VM -> Server (if VM is provisioned as a server)</li>
     *   <li>Hypervisor -> VM -> Server (if VM is provisioned as a server)</li>
     * </ul>
     * <p>
     * For FLAT format, it adds direct connections between virtualization components to the
     * connections array.
     *
     * @param asyncResult  The result of the dependency query
     * @param event        The original dependency query event
     * @param parent       The parent object (for hierarchical format) or null (for flat format)
     * @param connections  The connections array to populate (for flat format)
     * @param objects      Array of objects already processed
     * @param future       Promise to complete when the dependency building is done
     * @param pendingItems Map tracking pending items for completion handling
     */
    private void buildVirtualizationDependencies(AsyncResult<Message<JsonObject>> asyncResult, JsonObject event, JsonObject parent, JsonArray connections, JsonArray objects, Promise<Void> future, Map<String, Integer> pendingItems)
    {
        var currentSource = event.getString(AIOpsObject.OBJECT_IP);

        var connected = false;

        var object = event.getJsonObject(NMSConstants.OBJECT);

        event.remove(NMSConstants.OBJECT);

        var instances = new HashMap<String, JsonObject>();// to resolve vm's ip to name

        if (asyncResult.succeeded())
        {
            var result = asyncResult.result().body();

            var dependencies = result.getJsonObject(event.getString(AIOpsConstants.DEPENDENCY_TYPE));

            if (dependencies != null)
            {
                if (event.getString(DEPENDENCY_FORMAT).equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                {
                    var dependency = dependencies.getJsonObject(object.getString(AIOpsObject.OBJECT_IP));

                    var hierarchies = parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                    instances.computeIfAbsent(object.getString(AIOpsObject.OBJECT_IP), key -> new JsonObject());

                    if (dependency != null)
                    {
                        // for vcenter / hyperV cluster and citrix cluster -> vcenter -> esxi -> vm will be result
                        if (NMSConstants.isVirtualizationCluster(object.getString(AIOpsObject.OBJECT_TYPE)) || NMSConstants.isHCICluster(object.getString(AIOpsObject.OBJECT_TYPE)))
                        {
                            var hypervisors = dependency.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.NINE.getName()));

                            if (hypervisors != null)
                            {
                                for (var index = 0; index < hypervisors.size(); index++)
                                {
                                    var hypervisor = hypervisors.getString(index);

                                    var hypervisorId = ObjectConfigStore.getStore().getItemByIP(hypervisor);

                                    if (hypervisorId != null)
                                    {
                                        MetricConfigStore.getStore().getObjectsByMetricPlugin(hypervisorId, AIOpsConstants.getMetricPlugin(ObjectConfigStore.getStore().getItem(hypervisorId).getString(AIOpsObject.OBJECT_TYPE)), instances.get(object.getString(AIOpsObject.OBJECT_IP)));
                                    }

                                    var key = object.getString(AIOpsObject.OBJECT_IP) + VALUE_SEPARATOR + hypervisor;

                                    if (dependencies.containsKey(key))
                                    {
                                        var vms = dependencies.getJsonObject(key).getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.EIGHT.getName()));

                                        if (vms != null)
                                        {
                                            var connectedChildren = new JsonArray();

                                            var type = NMSConstants.isVirtualizationCluster(object.getString(AIOpsObject.OBJECT_TYPE)) ? virtualizationClusterTypes.get(object.getString(AIOpsObject.OBJECT_TYPE)) : hciClusterTypes.get(object.getString(AIOpsObject.OBJECT_TYPE));

                                            var item = ObjectConfigStore.getStore().getItemByIP(hypervisor, type);

                                            if (item == null)
                                            {
                                                item = ObjectConfigStore.getStore().getItemByObjectName(hypervisor);

                                                if (item == null)
                                                {
                                                    item = ObjectConfigStore.getStore().getItemByTarget(hypervisor, type);
                                                }
                                            }

                                            hierarchies.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                                    .put(AIOpsConstants.DEPENDENCY_DESTINATION, item != null ? item.getLong(ID) : hypervisor)
                                                    .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                    .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.NINE.getName())
                                                    .put(AIOpsConstants.DEPENDENCY_CHILDREN, connectedChildren));

                                            for (var row = 0; row < vms.size(); row++)
                                            {
                                                var server = qualifiedServer(vms.getString(row));

                                                if (server != null)
                                                {
                                                    var connectedChild = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item != null ? item.getLong(ID) : hypervisor)
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, server.getLong(ID))
                                                            .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName())
                                                            .put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                                    connectedChildren.add(connectedChild);

                                                    if (valid(objects, server, AIOpsObject.OBJECT_IP))
                                                    {
                                                        pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                                        buildHierarchies(server, objects, connectedChild, future, pendingItems, event, null, null);
                                                    }
                                                }
                                                else
                                                {
                                                    connectedChildren.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item != null ? item.getLong(ID) : hypervisor)
                                                            .put(AIOpsConstants.DEPENDENCY_DESTINATION, vms.getString(row))
                                                            .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                            .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            // esxi -> vm will be the result
                            var vms = dependency.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.EIGHT.getName()));

                            MetricConfigStore.getStore().getObjectsByMetricPlugin(object.getLong(ID), AIOpsConstants.getMetricPlugin(object.getString(AIOpsObject.OBJECT_TYPE)), instances.get(object.getString(AIOpsObject.OBJECT_IP)));

                            if (vms != null)
                            {
                                for (var index = 0; index < vms.size(); index++)
                                {
                                    var server = qualifiedServer(vms.getString(index));

                                    if (server != null)
                                    {
                                        var connectedChild = new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, server.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName())
                                                .put(AIOpsConstants.DEPENDENCY_CHILDREN, new JsonArray());

                                        hierarchies.add(connectedChild);

                                        if (valid(objects, server, AIOpsObject.OBJECT_IP))
                                        {
                                            pendingItems.put(currentSource, pendingItems.get(currentSource) + 1);

                                            buildHierarchies(server, objects, connectedChild, future, pendingItems, event, null, null);
                                        }
                                    }
                                    else
                                    {
                                        hierarchies.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID))
                                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, vms.getString(index))
                                                .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN)
                                                .put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.EIGHT.getName()));
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    for (var dependency : dependencies)
                    {
                        var link = new JsonObject().put(ID, object.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN);

                        if (dependency.getKey().contains(VALUE_SEPARATOR))
                        {
                            link.put(AIOpsConstants.DEPENDENCY_SOURCE, ObjectConfigStore.getStore().getItemByIP(dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim()) != null
                                    ? ObjectConfigStore.getStore().getItemByIP(dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim())
                                    : dependency.getKey().split(VALUE_SEPARATOR_WITH_ESCAPE)[1].trim());

                            for (var connection : JsonObject.mapFrom(dependency.getValue()))
                            {
                                link.put(AIOpsConstants.DEPENDENCY_LEVEL, CommonUtil.getByteValue(connection.getKey()));

                                var dependents = (JsonArray) connection.getValue();

                                if (!dependents.isEmpty())
                                {
                                    connected = true;

                                    var destinations = new JsonArray();

                                    for (var count = 0; count < dependents.size(); count++)
                                    {
                                        destinations.add(ObjectConfigStore.getStore().getItemByIP(dependents.getString(count)) != null
                                                ? ObjectConfigStore.getStore().getItemByIP(dependents.getString(count)) : dependents.getString(count));
                                    }

                                    connections.add(new JsonObject().mergeIn(link).put(AIOpsConstants.DEPENDENCY_DESTINATION, destinations));
                                }
                            }
                        }

                        else
                        {
                            link.put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID));

                            for (var connection : JsonObject.mapFrom(dependency.getValue()))
                            {
                                var level = CommonUtil.getByteValue(connection.getKey());

                                if (level.equals(AIOpsConstants.DependencyLevel.TWELVE.getName()) || level.equals(AIOpsConstants.DependencyLevel.NINE.getName()) || level.equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                                {
                                    connected = true;

                                    connections.add(new JsonObject().mergeIn(link).put(AIOpsConstants.DEPENDENCY_DESTINATION, connection.getValue()).put(AIOpsConstants.DEPENDENCY_LEVEL, level));
                                }
                            }
                        }
                    }
                }
            }

            parent.put(AIOpsConstants.INSTANCES, instances);

            parent.put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP));          // to resolve instances in UI, specially in network + virtualization topology
        }
        else
        {
            LOGGER.error(asyncResult.cause());
        }

        if (!connected && event.getString(DEPENDENCY_FORMAT).equalsIgnoreCase(AIOpsConstants.DependencyFormat.FLAT.getName()))
        {
            connections.add(new JsonObject().put(ID, object.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_SOURCE, object.getLong(ID)));
        }
    }

    /**
     * Finds a server object that corresponds to a virtual machine.
     * <p>
     * This method attempts to find a server object in the ObjectConfigStore that
     * corresponds to the given VM identifier. It tries several lookup methods:
     * <ol>
     *   <li>First by IP address in the SERVER category</li>
     *   <li>Then by object name</li>
     *   <li>Finally by target in the SERVER category</li>
     * </ol>
     * <p>
     * This method is used to establish connections between virtualization components
     * and the server objects that represent them in the monitoring system.
     *
     * @param vm The virtual machine identifier (IP, name, or target)
     * @return The server JsonObject if found, null otherwise
     */
    private JsonObject qualifiedServer(String vm)
    {
        var server = ObjectConfigStore.getStore().getItemByIP(vm, NMSConstants.Category.SERVER);

        if (server == null)
        {
            server = ObjectConfigStore.getStore().getItemByObjectName(vm);

            if (server == null)
            {
                server = ObjectConfigStore.getStore().getItemByTarget(vm, NMSConstants.Category.SERVER);
            }
        }

        return server;
    }

    /**
     * Builds dependencies for wireless controller objects.
     * <p>
     * This method processes dependencies for wireless controllers and their access points.
     * It extracts wireless dependencies and builds the appropriate dependency structure
     * based on the requested dependency format.
     * <p>
     * For TOP_BOTTOM_HIERARCHY format, it builds a parent-child relationship between the
     * wireless controller and its access points. For FLAT format, it adds direct connections
     * between the controller and its access points to the connections array.
     * <p>
     * This method handles wireless dependencies at level 10, which represents the relationship
     * between wireless controllers and their managed access points.
     *
     * @param asyncResult      The result of the dependency query
     * @param dependencyType   The type of dependency (local.domain or cross.domain)
     * @param dependencyFormat The format of the dependency (flat or hierarchical)
     * @param item             The wireless controller object for which to build dependencies
     * @param parent           The parent object (for hierarchical format) or null (for flat format)
     * @param connections      The connections array to populate (for flat format)
     */
    private void buildWirelessDependencies(AsyncResult<Message<JsonObject>> asyncResult, String dependencyType, String dependencyFormat, JsonObject item, JsonObject parent, JsonArray connections)
    {
        var connected = false;

        if (asyncResult.succeeded())
        {
            var result = asyncResult.result().body();

            var dependencies = result.getJsonObject(dependencyType);

            if (dependencies != null)
            {
                var object = dependencies.getJsonObject(item.getString(AIOpsObject.OBJECT_IP));

                var children = parent.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                if (object != null)
                {
                    var accessPoints = object.getJsonArray(CommonUtil.getString(AIOpsConstants.DependencyLevel.TEN.getName()));

                    if (accessPoints != null)
                    {
                        if (dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName()))
                        {
                            for (var accessPoint : accessPoints)
                            {
                                children.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_DESTINATION, accessPoint)
                                        .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()));
                            }
                        }
                        else
                        {
                            connected = true;

                            connections.add(new JsonObject().put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_DESTINATION, accessPoints)
                                    .put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_LEVEL, AIOpsConstants.DependencyLevel.TEN.getName()));
                        }
                    }
                }
            }
        }
        else
        {
            LOGGER.error(asyncResult.cause());
        }

        if (!connected && dependencyFormat.equalsIgnoreCase(AIOpsConstants.DependencyFormat.FLAT.getName()))
        {
            connections.add(new JsonObject().put(ID, item.getLong(ID)).put(AIOpsConstants.DEPENDENCY_CONNECTED_LINK, UNKNOWN).put(AIOpsConstants.DEPENDENCY_SOURCE, item.getLong(ID)));
        }
    }

    /**
     * Builds dependencies for Cisco ACI leaf nodes.
     * <p>
     * This method processes level 4 and level 6 dependencies for Cisco ACI components.
     * It constructs the dependency structure for leaf nodes and their connections to
     * endpoints and interfaces.
     * <p>
     * For level 6, endpoints are stored directly with the leaf as the source. For level 4,
     * the method triggers recursive hierarchy building by identifying interfaces
     * connected to the leaf.
     * <p>
     * This method handles the following dependency levels:
     * <p>
     * Level 4: Interface-level dependencies
     * Level 6: Endpoint-level dependencies
     *
     * @param dependencies   The full dependency map with levels as keys and associated values
     * @param key            The unique key identifying the current leaf object
     * @param connectedChild The JSON object representing the current leaf's dependency children
     * @param pendingItems   Map tracking the number of pending items for each object IP
     * @param event          The event that triggered dependency resolution
     * @param objects        Array of objects already being processed
     * @param future         Promise to complete once dependency building is finished
     */

    private void buildCiscoACILeafDependencies(JsonObject dependencies, String key, JsonObject connectedChild, Map<String, Integer> pendingItems, JsonObject event, JsonArray objects, Promise<Void> future)
    {
        try
        {
            for (var dependency : dependencies.getJsonObject(key))
            {
                if (Objects.equals(Byte.valueOf(dependency.getKey()), DependencyLevel.SIX.getName()))
                {
                    var localConnections = (JsonArray) dependency.getValue();

                    for (var localConnection : localConnections)
                    {
                        var tokens = key.split(VALUE_SEPARATOR);

                        var currentSource = tokens[3] + VALUE_SEPARATOR + tokens[6];

                        connectedChild.getJsonArray(DEPENDENCY_CHILDREN).add(new JsonObject()
                                .put(AIOpsConstants.DEPENDENCY_SOURCE, currentSource)
                                .put(AIOpsConstants.DEPENDENCY_DESTINATION, localConnection)
                                .put(DEPENDENCY_LEVEL, DependencyLevel.SIX.getName())
                                .put(DEPENDENCY_CONNECTED_LINK, currentSource + VALUE_SEPARATOR + localConnection)
                                .put(DEPENDENCY_CHILDREN, new JsonArray()));
                    }
                }
                else if (Objects.equals(Byte.valueOf(dependency.getKey()), DependencyLevel.FOUR.getName()))
                {
                    for (var localConnection : (JsonArray) dependency.getValue())
                    {
                        var tokens = key.split(VALUE_SEPARATOR);

                        var currentSource = tokens[3] + VALUE_SEPARATOR + tokens[6] + VALUE_SEPARATOR + localConnection;

                        pendingItems.put(event.getString(AIOpsObject.OBJECT_IP), pendingItems.get(event.getString(AIOpsObject.OBJECT_IP)) + 1);

                        buildHierarchies(ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP(event.getString(AIOpsObject.OBJECT_IP))).put(AIOpsObject.OBJECT_IP, currentSource), objects, connectedChild, future, pendingItems, event, null, null);

                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private Set<String> setFilterTargets(JsonObject event)
    {
        var filters = new HashSet<String>();

        if (event.containsKey(Explorer.ID))
        {
            var view = ExplorerConfigStore.getStore().getItem(event.getLong(Explorer.ID));

            if (view != null)
            {
                try
                {
                    var source = ObjectConfigStore.getStore().getItem(event.getLong(APIConstants.ENTITY_ID)).getString(OBJECT_IP);

                    var context = view.getJsonObject(Explorer.EXPLORER_CONTEXT);

                    var targetType = context.getString("topology.filter.target.type", EMPTY_VALUE);

                    if (targetType.split(DASH_SEPARATOR).length > 0 && !context.getJsonArray("topology.filter.targets").isEmpty())
                    {
                        var targets = context.getJsonArray("topology.filter.targets");

                        switch (NMSConstants.TopologyFilterType.valueOfName(context.getString("topology.filter.target.type").split(DASH_SEPARATOR)[1]))
                        {
                            case IP_ADDRESS -> targets.forEach(target -> filters.add(CommonUtil.getString(target)));

                            case IP_ADDRESS_RANGE ->
                            {
                                for (var index = 0; index < targets.size(); index++)
                                {
                                    var range = targets.getString(index);

                                    var start = range.split("-")[0].trim();

                                    var end = start.substring(0, start.lastIndexOf('.')) + "." + range.split("-")[1].trim();

                                    if (APIUtil.validateRange(start, end))
                                    {
                                        CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).forEach(ip -> filters.add(CommonUtil.getString(ip)));
                                    }
                                }
                            }

                            case GROUP -> ObjectConfigStore.getStore().getItemsByGroups(targets).forEach(id -> filters.add(ObjectConfigStore.getStore().getItem(CommonUtil.getLong(id)).getString(OBJECT_IP, EMPTY_VALUE)));

                            case TAG -> ObjectConfigStore.getStore().getObjectIdsByTags(targets).forEach(id -> filters.add(ObjectConfigStore.getStore().getItemByObjectId(CommonUtil.getInteger(id)).getString(OBJECT_IP, EMPTY_VALUE)));

                            default ->
                            {
                            }
                        }

                        if (targetType.contains("include"))
                        {
                            filters.add(source);
                        }
                        else
                        {
                            filters.remove(source);
                        }

                        event.put("topology.filter.target.type", targetType);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }

        return filters;
    }

    private void updateHierarchies()
    {
        hierarchies.clear();

        var items = DependencyMapperConfigStore.getStore().getItems();

        if (!items.isEmpty())
        {
            var qualifiedObjects = new HashSet<String>();

            for (var index = 0; index < items.size(); index++)
            {
                try
                {
                    var item = items.getJsonObject(index);

                    if (!hierarchies.containsKey(ObjectConfigStore.getStore().getItemByIP(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))))
                    {
                        var parentItems = DependencyMapperConfigStore.getStore().getItemsByObject(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT));

                        // If this item has no parent, it is a top-most parent itself. so build hierarchies from top most parents
                        if (TopologyCacheStore.getStore().containsParent(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT)) || parentItems.get(DependencyMapper.CHILDREN).isEmpty())
                        {
                            var parent = ObjectConfigStore.getStore().getItemByIP(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT));

                            hierarchies.computeIfAbsent(parent, value -> new HashSet<>());

                            buildHierarchies(parent, parentItems, qualifiedObjects);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }
    }

    private void buildHierarchies(long root, Map<String, List<JsonObject>> items, Set<String> qualifiedObjects)
    {
        items.get(DependencyMapper.PARENTS).forEach(item ->
        {
            var child = item.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD);

            var childItems = DependencyMapperConfigStore.getStore().getItemsByObject(child);

            /*  Why we need 3rd condition? Let's understand it by an example :
                if current hierarchy building parent is 14.7 and childItems only has one entry where parent is 14.3 and child is 14.7,
                then 14.7 is only being referenced by 14.3, and 14.7 is actually a parent of 14.3.
                if we add 14.3 to hierarchies then during topology rendering from 14.3 it can create single node issue
            */
            if (!qualifiedObjects.contains(child) && !ObjectConfigStore.getStore().getItemByIP(child).equals(root) && !childItems.get(DependencyMapper.PARENTS).stream().allMatch(entry -> entry.getString(DependencyMapper.DEPENDENCY_MAPPER_CHILD).equalsIgnoreCase(item.getString(DependencyMapper.DEPENDENCY_MAPPER_PARENT))))
            {
                qualifiedObjects.add(child);

                // Add this child to the hierarchy of the current IP
                hierarchies.computeIfAbsent(root, value -> new HashSet<>()).add(ObjectConfigStore.getStore().getItemByIP(child));

                // Recursively build hierarchy for this child
                buildHierarchies(root, childItems, qualifiedObjects);
            }
        });
    }
}
