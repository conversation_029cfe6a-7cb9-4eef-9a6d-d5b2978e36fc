{"***********": {"result": {"nsxt.distributed.firewall.rule": [{"nsxt.distributed.firewall.rule": "9594cd72-5ef8-4e3e-8c22-fc3142c217f2-5", "nsxt.distributed.firewall.rule.bytes": 0, "nsxt.distributed.firewall.rule.hits": 0, "nsxt.distributed.firewall.rule.id": "5", "nsxt.distributed.firewall.rule.name": "Malicious IP at Source Rule", "nsxt.distributed.firewall.rule.packets": 0, "nsxt.distributed.firewall.rule.sessions": 0, "nsxt.distributed.firewall.rule.target.name": "DefaultMaliciousIpGroup,demo", "nsxt.distributed.firewall.rule.target.type": "NSGroup,denot", "nsxt.distributed.firewall.section.id": "9594cd72-5ef8-4e3e-8c22-fc3142c217f2"}, {"nsxt.distributed.firewall.rule": "9594cd72-5ef8-4e3e-8c22-fc3142c217f2-6", "nsxt.distributed.firewall.rule.bytes": 0, "nsxt.distributed.firewall.rule.hits": 0, "nsxt.distributed.firewall.rule.id": "6", "nsxt.distributed.firewall.rule.name": "Malicious IP at Destination Rule", "nsxt.distributed.firewall.rule.packets": 0, "nsxt.distributed.firewall.rule.sessions": 0, "nsxt.distributed.firewall.rule.target.name": "DefaultMaliciousIpGroup", "nsxt.distributed.firewall.rule.target.type": "NSGroup", "nsxt.distributed.firewall.section.id": "9594cd72-5ef8-4e3e-8c22-fc3142c217f2"}]}, "errors": []}}