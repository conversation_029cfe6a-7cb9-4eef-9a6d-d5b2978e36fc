[{"_type": "0", "id": 10000000001693, "visualization.name": "Running Instance", "visualization.description": "Running Instance Azure CosmoDB", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmosdb.instances", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmosdb.instances", "icon": {"name": "running-instance", "placement": "prefix"}}, "header": {"title": "Running Instance", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmosdb.instances.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001694, "visualization.name": "Total Request", "visualization.description": "Total Request Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Total Request", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.requests.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001695, "visualization.name": "Request Units Consumed", "visualization.description": "Request Units Consumed Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.request.units", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.request.units", "icon": {"name": "consumed-lcus", "placement": "prefix"}}, "header": {"title": "Request Units Consumed", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.request.units.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001696, "visualization.name": "Storage", "visualization.description": "Storage Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.available.storage.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.cosmos.db.data.usage.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.cosmos.db.index.usage.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.available.storage.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": [{"label": "Available Storage", "value": "azure.cosmos.db.available.storage.bytes.avg"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Data", "value": "azure.cosmos.db.data.usage.bytes.avg"}, {"label": "Index", "value": "azure.cosmos.db.index.usage.bytes.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001697, "visualization.name": "Documents", "visualization.description": "Documents Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.documents", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.documents", "icon": {"name": "document", "placement": "prefix"}}, "header": {"title": "Documents", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.documents.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001698, "visualization.name": "Service Availability", "visualization.description": "Service Availability Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.service.availability.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.cosmos.db.service.availability.percent", "icon": {"name": "availability", "placement": "prefix"}}, "header": {"title": "Service Availability", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.cosmos.db.service.availability.percent.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001699, "visualization.name": "Top resources By Request Rate", "visualization.description": "Top resources By Request Rate Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.requests.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001700, "visualization.name": "Top resources By Total Requests", "visualization.description": "Top resources By Total Request Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.request.units", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.request.units.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001701, "visualization.name": "Top resources By Status Codes", "visualization.description": "Top resources By Status Codes Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": []}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": ""}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001702, "visualization.name": "Data and Index Storage Consumption", "visualization.description": " Data and Index Storage Consumption Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.data.usage.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.cosmos.db.index.usage.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.index.usage.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001703, "visualization.name": "Top resources By Provisioned Throughput", "visualization.description": "Top resources By Provisioned Throughput Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.provisioned.throughput", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.provisioned.throughput.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001704, "visualization.name": "Top resources By Service Availability", "visualization.description": "Top resources By Service Availability Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.service.availability.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.service.availability.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001705, "visualization.name": "Top resource By MongoDB Request and Charges", "visualization.description": "Top resource By MongoDB Request and Charges Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.mongo.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.cosmos.db.mongo.request.charge", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.mongo.requests.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001706, "visualization.name": "Top resource By Latency", "visualization.description": "Top resource By Latency Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.replication.latency.ms", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": ""}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001707, "visualization.name": "Top resource By Azure Table Details", "visualization.description": "Top resource By Azure Table Details Azure CosmoDB", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.cosmos.db.created.azure.tables", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.cosmos.db.created.azure.tables.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]