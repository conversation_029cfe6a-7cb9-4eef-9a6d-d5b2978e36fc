{"************": {"metric.timeout": 10, "object.ip": "************", "object.type": "Switch", "object.vendor": "Cisco Systems", "objects": [{"object.ip": "************"}], "port": 161, "rediscover.job": "Network Metric", "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "fd00:1:1:1::47": {"plugin.id": 222, "metric.timeout": 10, "object.ip": "fd00:1:1:1::47", "object.type": "Switch", "object.vendor": "Hewlett Packard Enterprise", "objects": [{"object.ip": "fd00:1:1:1::47"}], "port": 161, "rediscover.job": "Network Metric", "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}, "***********": {"metric.timeout": 10, "object.ip": "***********", "object.type": "Switch", "object.vendor": "D-Link", "objects": [{"object.ip": "***********"}], "port": 161, "rediscover.job": "Network Metric", "snmp.community": "public", "snmp.version": "v2c", "status": "succeed"}}