[{"_type": "0", "id": 10000000001397, "visualization.name": "Active Flows", "visualization.description": "ELB Network Active Flow", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.active.flows", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.active.flows"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.active.flows", "icon": {"name": "active-flow", "placement": "prefix"}}, "header": {"title": "Active Flow", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.active.flows.last"}]}}}}, {"_type": "0", "id": 10000000001398, "visualization.name": "New Flows", "visualization.description": "ELB Network New Flow", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.new.flows", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.new.flows"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.new.flows", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "New Flow", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.new.flows.last"}]}}}}, {"_type": "0", "id": 10000000001399, "visualization.name": "Processed Bytes", "visualization.description": "ELB Network Processed Bytes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.tcp.processed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.tcp.processed.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.tcp.processed.bytes", "icon": {"name": "processes", "placement": "prefix"}}, "header": {"title": "Processed Bytes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.tcp.processed.bytes.last"}]}}}}, {"_type": "0", "id": 10000000001400, "visualization.name": "RST Packets", "visualization.description": "ELB Network RST Packets", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.tcp.client.resets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.tcp.target.elb.resets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.tcp.elb.resets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.tcp.client.resets"}, {"type": "metric", "data.point": "aws.elb.tcp.target.elb.resets"}, {"type": "metric", "data.point": "aws.elb.tcp.elb.resets"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.tcp.elb.resets", "icon": {"name": "node-count", "placement": "prefix"}}, "header": {"title": "RST Packets", "style": {"font.size": "medium"}, "data.points": [{"label": "Resets", "value": "aws.elb.tcp.elb.resets.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Client", "value": "aws.elb.tcp.client.resets.last"}, {"label": "Hosts", "value": "aws.elb.tcp.target.elb.resets.last"}]}}}}, {"_type": "0", "id": 10000000001401, "visualization.name": "Hosts", "visualization.description": "ELB Hosts", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.healthy.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.unhealthy.hosts", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.healthy.hosts"}, {"type": "metric", "data.point": "aws.elb.unhealthy.hosts"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.healthy.hosts", "icon": {"name": "host", "placement": "prefix"}}, "header": {"title": "Hosts", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Healthy", "value": "aws.elb.healthy.hosts.last"}, {"label": "Unhealthy", "value": "aws.elb.unhealthy.hosts.last"}]}}}}, {"_type": "0", "id": 10000000001402, "visualization.name": "Consumed Lcus", "visualization.description": "ELB Consumed Lcus", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.consumed.lcus", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.elb.consumed.lcus"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.elb.consumed.lcus", "icon": {"name": "consumed-lcus", "placement": "prefix"}}, "header": {"title": "Consumed LCUs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.elb.consumed.lcus.last"}]}}}}, {"_type": "0", "id": 10000000001403, "visualization.name": "Healthy/Unhealthy Host Count", "visualization.description": "ELB Healthy/Unhealthy Host Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.healthy.hosts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.unhealthy.hosts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001404, "visualization.name": "New Flows", "visualization.description": "ELB HTTP Error Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.new.flows", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001405, "visualization.name": "Processed Bytes", "visualization.description": "ELB Processed Bytes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.processed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001406, "visualization.name": "RST Packets Resets", "visualization.description": "ELB Packets Resets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.tcp.client.resets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.tcp.target.elb.resets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.tcp.elb.resets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001407, "visualization.name": "Active Flows", "visualization.description": "ELB Active Flows", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.active.flows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.elb.tcp.active.flows", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001408, "visualization.name": "ELB Consumed LCUs", "visualization.description": "ELB Consumed Lcus", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.elb.consumed.lcus", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]