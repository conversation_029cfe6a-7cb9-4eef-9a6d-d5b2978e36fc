/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  27-May-2025		Bharat		        MOTADATA-6243: Ha Sync Fix
 */
package com.mindarray.integration;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.IntegrationCacheStore;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.MetricPolicy.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * The IntegrationEngine is the central component that manages all integrations with external systems.
 * <p>
 * This class is responsible for:
 * - Initializing and managing integration profiles
 * - Handling integration events from the event bus
 * - Routing events to the appropriate integration handlers
 * - Persisting integration state to disk
 * - Managing the integration cache
 * <p>
 * The engine supports various integration types including ServiceNow, Jira, ServiceOps, and Microsoft Teams.
 * It acts as a bridge between the Motadata system and external ticketing/notification systems.
 */
public class IntegrationEngine extends AbstractVerticle
{
    // Constants used throughout the integration engine
    /**
     * Filename for storing integration configurations on disk
     */
    public static final String INTEGRATIONS = "integrations";

    /**
     * Filename for storing pending integration events on disk
     * These events will be processed when the system restarts
     */
    public static final String INTEGRATIONS_PENDING_EVENTS = "integrations-pending-events";

    /**
     * Key for the acknowledgement ID in event data
     * Used to track and correlate integration events with their responses
     */
    public static final String ACK_ID = "ack.id";

    /**
     * Flag indicating whether to create a ticket in the external system
     */
    public static final String CREATE_TICKET = "create.ticket";
    /**
     * Logger instance for the Integration Engine
     * Used for logging information, warnings, and errors related to integration operations
     */
    private static final Logger LOGGER = new Logger(IntegrationEngine.class, INTEGRATION_DIR, "Integration Engine");

    /**
     * Map of integration profiles indexed by their IDs
     * Contains configuration details for each integration profile
     */
    private final Map<Long, JsonObject> integrationProfiles = new HashMap<>();

    /**
     * Initializes the Integration Engine.
     * <p>
     * This method:
     * 1. Sets up a periodic timer to flush the integration cache to disk
     * 2. Loads integration profiles from the configuration store
     * 3. Registers event bus handlers for integration events
     * 4. Processes any pending integration events from previous sessions
     * 5. Completes the initialization promise when all setup is complete
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var refreshTimer = new AtomicInteger(MotadataConfigUtil.getIntegrationCacheFlushTimerSeconds());

        vertx.setPeriodic(30 * 1000L, timer ->
        {
            if (IntegrationCacheStore.getStore().updated())
            {
                IntegrationCacheStore.getStore().setUpdate(false);

                IntegrationCacheStore.getStore().setDirty(true);

                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INTEGRATIONS,
                        Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", new HashMap<>(IntegrationCacheStore.getStore().getItems())).put("history", new HashMap<>(IntegrationCacheStore.getStore().getMappers())).put("profiles", new HashMap<>(IntegrationCacheStore.getStore().getProfiles())).encode().getBytes())));

                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INTEGRATIONS_PENDING_EVENTS,
                        Buffer.buffer(CodecUtil.compress(new JsonObject().put("pendingEvents", new HashMap<>(IntegrationCacheStore.getStore().getPendingEvents())).encode().getBytes())));
            }

            refreshTimer.set(refreshTimer.get() - 30);

            if (refreshTimer.get() <= 0)
            {
                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INTEGRATIONS,
                        Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", new HashMap<>(IntegrationCacheStore.getStore().getItems())).put("history", new HashMap<>(IntegrationCacheStore.getStore().getMappers())).put("profiles", new HashMap<>(IntegrationCacheStore.getStore().getProfiles())).encode().getBytes())));

                Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INTEGRATIONS_PENDING_EVENTS,
                        Buffer.buffer(CodecUtil.compress(new JsonObject().put("pendingEvents", new HashMap<>(IntegrationCacheStore.getStore().getPendingEvents())).encode().getBytes())));

                refreshTimer.set(MotadataConfigUtil.getIntegrationCacheFlushTimerSeconds());
            }
        });

        var items = IntegrationProfileConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            integrationProfiles.put(item.getLong(ID), item);
        }

        // here we will sync the integrations file with observer and manage IntegrationCacheStore in case of policy is deleted
        vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
        {
            switch (EventBusConstants.ChangeNotificationType.valueOf(message.body().getString(CHANGE_NOTIFICATION_TYPE)))
            {
                case UPDATE_POLICY ->
                {
                    try
                    {
                        var event = message.body();

                        if (event.containsKey(ID))
                        {
                            var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(ID));

                            if (item != null && (!item.containsKey(POLICY_ACTIONS) || item.getValue(POLICY_ACTIONS) == null
                                    || !item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.INTEGRATION.getName())
                                    || item.getJsonObject(POLICY_ACTIONS).getValue(PolicyTriggerActionType.INTEGRATION.getName()) == null
                                    || item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).isEmpty()))
                            {
                                IntegrationCacheStore.getStore().removeProfiles(event.getString(ID));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                case DELETE_POLICY ->
                {
                    try
                    {
                        var event = message.body();

                        if (event.containsKey(ID))
                        {
                            IntegrationCacheStore.getStore().remove(event.getString(ID));

                            IntegrationCacheStore.getStore().removeProfiles(event.getString(ID));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                case UPDATE_CACHE ->
                {
                    if (IntegrationCacheStore.getStore().dirty())
                    {
                        IntegrationCacheStore.getStore().setDirty(false);

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, INTEGRATIONS).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + INTEGRATIONS)));

                        HAConstants.notifyObserver(new JsonObject().put(HAConstants.CACHE_NAME, INTEGRATIONS_PENDING_EVENTS).put(RESULT, vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + INTEGRATIONS_PENDING_EVENTS)));
                    }
                }

                case ADD_INTEGRATION_PROFILE, UPDATE_INTEGRATION_PROFILE ->
                {
                    try
                    {
                        var event = message.body();

                        if (event.containsKey(ID) && event.getValue(ID) != null && event.containsKey(IntegrationProfile.INTEGRATION))
                        {
                            integrationProfiles.put(event.getLong(ID), IntegrationProfileConfigStore.getStore().getItem(event.getLong(ID)));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                case DELETE_INTEGRATION_PROFILE ->
                {
                    try
                    {
                        var event = message.body();

                        if (event.containsKey(ID) && event.getValue(ID) != null && event.containsKey(IntegrationProfile.INTEGRATION))
                        {
                            integrationProfiles.remove(event.getLong(ID));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                default ->
                {
                    // do nothing
                }
            }
        }).exceptionHandler(LOGGER::error);

        // for each type of integration first we will receive the event here.
        vertx.eventBus().<JsonObject>localConsumer(EVENT_INTEGRATION, message ->
        {
            try
            {
                var event = message.body();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("received event : %s", event.encode()));
                }

                // in case of manually declare incident
                if (!event.containsKey(EVENT_TIMESTAMP))
                {
                    event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (!event.containsKey(MESSAGE))
                {
                    var item = MetricPolicyConfigStore.getStore().getItem(event.getLong(PolicyEngineConstants.POLICY_ID));

                    var context = item.getJsonObject(POLICY_CONTEXT);

                    var policyMessage = EMPTY_VALUE;

                    if (item.getString(POLICY_TYPE).equals(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()))
                    {
                        policyMessage = String.format(InfoMessageConstants.AVAILABILITY_POLICY_DEFAULT_MESSAGE, ObjectConfigStore.getStore().getObjectName(event.getLong(ENTITY_ID)), event.getString(INSTANCE) != null ? event.getString(INSTANCE) : EMPTY_VALUE, event.getString(VALUE).equalsIgnoreCase(STATUS_UP) ? "Available" : "Unavailable");
                    }
                    else if (item.getString(POLICY_TYPE).equals(PolicyType.STATIC.getName()))
                    {
                        policyMessage = String.format(InfoMessageConstants.METRIC_THRESHOLD_POLICY_DEFAULT_MESSAGE, event.getString(GlobalConstants.METRIC), event.getString(INSTANCE) != null ? event.getString(INSTANCE) : EMPTY_VALUE, ObjectConfigStore.getStore().getObjectName(event.getLong(ENTITY_ID)), context.getJsonObject(POLICY_SEVERITY).getJsonObject(event.getString(SEVERITY)) != null ? context.getJsonObject(POLICY_SEVERITY).getJsonObject(event.getString(SEVERITY)).getString(POLICY_CONDITION) : EMPTY_VALUE, context.getJsonObject(POLICY_SEVERITY).getJsonObject(event.getString(SEVERITY)) != null ? context.getJsonObject(POLICY_SEVERITY).getJsonObject(event.getString(SEVERITY)).getString(POLICY_THRESHOLD) : EMPTY_VALUE, context.getInteger(POLICY_TRIGGER_TIME) / 60, context.getInteger(POLICY_TRIGGER_OCCURRENCES));
                    }

                    event.put(MESSAGE, PolicyEngineConstants.replaceMetricPolicyPlaceholders(item, event, item.getString(POLICY_MESSAGE) != null && !item.getString(POLICY_MESSAGE).isEmpty() ? item.getString(POLICY_MESSAGE) : EMPTY_VALUE, policyMessage));
                }

                if (!event.containsKey(POLICY_KEY))
                {
                    event.put(POLICY_KEY, event.getLong(ENTITY_ID) + SEPARATOR + (event.getValue(INSTANCE, null) != null && !event.getString(INSTANCE).isEmpty() ? event.getString(POLICY_TYPE) + SEPARATOR + event.getString(GlobalConstants.METRIC) + SEPARATOR + event.getValue(INSTANCE) : event.getString(POLICY_TYPE) + SEPARATOR + event.getString(GlobalConstants.METRIC)));
                }

                Long id = null;

                var key = event.getString(POLICY_ID) + SEPARATOR + event.getString(POLICY_KEY);

                /*
                if we've already created a ticket for the respective policy then we've associated profile id present heere,
                So, in case we receive any update for the same policy, we'll use the existing profile until that policy gets clear
                 */
                if (IntegrationCacheStore.getStore().getProfileId(key) != NOT_AVAILABLE)
                {
                    id = IntegrationCacheStore.getStore().getProfileId(key);

                    if (event.containsKey(SEVERITY) && event.getString(SEVERITY).equalsIgnoreCase(Severity.CLEAR.name()))
                    {
                        IntegrationCacheStore.getStore().removeItem(key);
                    }
                }
                else if (event.containsKey(ID) && event.getValue(ID) != null)
                {
                    /*
                    if we've not created any ticket for the respective policy, then we should check that we've received
                    any profile id on which we've to create ticket, then we'll use that integration profile id and will cache it.
                     */
                    event.put(CREATE_TICKET, true);

                    id = event.getLong(ID);

                    IntegrationCacheStore.getStore().updateItem(key, id);
                }
                else if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("not generating ticket for severity : %s", event.getString(SEVERITY)));

                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().send("test.integration", event);
                    }
                }

                if (id != null)
                {
                    var item = integrationProfiles.get(id);

                    if (item != null && !item.isEmpty())
                    {

                        var integration = item.containsKey(IntegrationProfile.INTEGRATION) ? item.getLong(IntegrationProfile.INTEGRATION) : null;

                        if (integration != null)
                        {
                            switch (IntegrationConstants.IntegrationId.valueOfName(item.getLong(IntegrationProfile.INTEGRATION)))
                            {
                                case SERVICENOW ->
                                        vertx.eventBus().send(IntegrationConstants.IntegrationType.SERVICENOW.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, event.put(ID, id));

                                case SERVICEOPS ->
                                        vertx.eventBus().send(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, event.put(ID, id));

                                case ATLASSIAN_JIRA ->
                                        vertx.eventBus().send(IntegrationConstants.IntegrationType.ATLASSIAN_JIRA.getName() + DOT_SEPARATOR + EVENT_INTEGRATION, event.put(ID, id));
                            }
                        }
                        else
                        {
                            IntegrationCacheStore.getStore().removeItem(key);

                            LOGGER.warn(String.format("invalid integration request received for integration profile with integration id: %s", item.getLong(IntegrationProfile.INTEGRATION)));

                            if (MotadataConfigUtil.devMode())
                            {
                                vertx.eventBus().send("test.integration", event.put(MESSAGE, "invalid integration request received"));
                            }
                        }
                    }
                    else
                    {
                        IntegrationCacheStore.getStore().removeItem(key);

                        LOGGER.warn(String.format("invalid integration request received for integration profile id: %s", event.getLong(ID)));

                        if (MotadataConfigUtil.devMode())
                        {
                            vertx.eventBus().send("test.integration", event.put(MESSAGE, "invalid integration request received"));
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }
}
