/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The Network Route (NetRoute) package provides functionality for monitoring and analyzing network paths between devices.
 * <p>
 * This package contains components that work together to provide comprehensive network path monitoring capabilities:
 * <p>
 * <strong>Core Components:</strong>
 * <ul>
 *   <li>{@link com.mindarray.netroute.NetRouteScheduler} - Schedules network route polling operations based on configured intervals</li>
 *   <li>{@link com.mindarray.netroute.NetRouteResponseProcessor} - Processes responses from network route polling operations</li>
 *   <li>{@link com.mindarray.netroute.NetRouteStatusCalculator} - Calculates and tracks the status of network routes</li>
 * </ul>
 * <p>
 * <strong>Constants and Enumerations:</strong>
 * <ul>
 *   <li>{@link com.mindarray.netroute.NetRouteConstants} - Provides constants, enumerations, and utility methods used throughout the NetRoute package</li>
 *   <li>{@link com.mindarray.netroute.NetRouteConstants.NetRouteType} - Defines different types of network route monitoring (source-to-destination and hop-by-hop)</li>
 *   <li>{@link com.mindarray.netroute.NetRouteConstants.NetRouteOrdinal} - Stores duplicate keys as ordinals for performance optimization</li>
 *   <li>{@link com.mindarray.netroute.NetRouteConstants.NetRouteMetrics} - Defines metrics used for extracting values from raw results</li>
 * </ul>
 * <p>
 * The NetRoute package enables monitoring of network paths between source and destination devices, providing valuable
 * insights into network performance, latency, packet loss, and connectivity issues. It supports both source-to-destination
 * monitoring (end-to-end path analysis) and hop-by-hop monitoring (detailed analysis of each network hop).
 * <p>
 * Key features of the NetRoute package include:
 * <ul>
 *   <li>Scheduled polling of network routes based on configured intervals</li>
 *   <li>Processing and enrichment of network route data</li>
 *   <li>Calculation of network route status and performance metrics</li>
 *   <li>Detection of network path changes and issues</li>
 *   <li>Support for various network protocols and configurations</li>
 * </ul>
 * <p>
 * The package integrates with other components of the system, such as the event bus, data store, and policy engine,
 * to provide a complete solution for network path monitoring and analysis.
 */
package com.mindarray.netroute;