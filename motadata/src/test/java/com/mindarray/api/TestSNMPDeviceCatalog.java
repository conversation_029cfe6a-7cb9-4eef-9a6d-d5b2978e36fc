/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.SNMPDeviceCatalog.*;
import static com.mindarray.api.SNMPOIDGroup.OID_NAME;
import static com.mindarray.api.SNMPOIDGroup.OID_TYPE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PASSWORD;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestSNMPDeviceCatalog
{
    private static final Logger LOGGER = new Logger(TestSNMPDeviceCatalog.class, MOTADATA_NMS, "SNMP Device Catalog Test");

    private static final JsonObject CONTEXT = new JsonObject();
    private static final JsonArray METRIC_IDS = new JsonArray();
    private static MessageConsumer<JsonObject> messageConsumer;
    private static MessageConsumer<JsonObject> byteMessageConsumer;

    @AfterAll
    static void cleanUp(VertxTestContext testContext)
    {
//        testContext.awaitCompletion(10,TimeUnit.SECONDS);

        if (TestSNMPDeviceCatalog.CONTEXT.containsKey("object"))
        {
            ObjectStatusCacheStore.getStore().updateItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"), STATUS_UP, DateTimeUtil.currentSeconds());
        }


        if (byteMessageConsumer != null)
        {
            byteMessageConsumer.unregister();
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {

        if (byteMessageConsumer != null)
        {
            byteMessageConsumer.unregister();
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(value = 70, timeUnit = TimeUnit.SECONDS)
    void testGetAllSNMPDeviceCatalog(VertxTestContext testContext)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SNMPDeviceCatalogConfigStore.getStore(), null);

                            CONTEXT.put("snmp.device.catalog", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetAllVendor(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(SNMP_DEVICE_VENDOR_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            Assertions.assertEquals(response.bodyAsJsonObject().getJsonArray(RESULT).size(), SNMPDeviceCatalogConfigStore.getStore().flatItems(SNMPDeviceCatalog.SNMP_DEVICE_CATALOG_VENDOR).size());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateSNMPDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SNMP_DEVICE_CATALOG_MODEL, "test").put(SNMP_DEVICE_CATALOG_OID, ".1.3.6.1.4.1.171.10.76.20.1").put(SNMP_DEVICE_CATALOG_TYPE, "SNMP Device").put(SNMP_DEVICE_CATALOG_VENDOR, "Vendor").put("_type", "1");

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPDeviceCatalogConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Device Catalog"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.device.catalog", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateSNMPOIDGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, "scalar").put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.2.3.5.9", "counter"));

        TestAPIUtil.post(SNMP_OID_GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPOIDGroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP OID Group"), new JsonArray()
                                            .add(SNMP_OID_GROUP_ID).add(SNMP_OID_GROUP_DEVICE_TYPE), LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.oid.group", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetSNMPOIDGroup(VertxTestContext testContext)
    {

        var filter = new JsonObject().put(KEY, SNMP_DEVICE_CATALOG_ID).put(GlobalConstants.VALUE, new JsonArray().add(CONTEXT.getLong("snmp.device.catalog")));

        TestAPIUtil.get(SNMP_OID_GROUP_API_ENDPOINT + "/?filter=" + filter
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, SNMPOIDGroupConfigStore.getStore(), null);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetSNMPDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.device.catalog")
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("snmp.device.catalog"), SNMPDeviceCatalogConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateSNMPOIDGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group1").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, "tabular").put(SNMP_OID_GROUP_OIDS, new JsonObject()
                        .put(".1.2.3.5.9.1.1", "counter1").put(".1.2.3.5.6.8", "counter2"));

        TestAPIUtil.put(SNMP_OID_GROUP_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.oid.group"), context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(SNMPOIDGroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, "SNMP OID Group"), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testCreateDuplicateSNMPOIDGroup(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(SNMP_OID_GROUP_NAME, "Metric Group1").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, "scalar").put(SNMP_OID_GROUP_OIDS, new JsonObject()
                        .put(".1.2.3.5.9", "counter"));

        TestAPIUtil.post(SNMP_OID_GROUP_API_ENDPOINT, context,

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), "SNMP OID Group Metric Group1 is not unique for type SNMP Device",
                                    SNMPOIDGroupConfigStore.getStore(), SNMP_OID_GROUP_NAME, context.getString(SNMP_OID_GROUP_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testGetGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(GROUP_API_ENDPOINT
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            items = GroupConfigStore.getStore().getItems();

                            Assertions.assertNotNull(items);

                            Assertions.assertFalse(items.isEmpty());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testSearchScalarSNMPOID(VertxTestContext testContext, TestInfo testInfo)
    {
//        testContext.awaitCompletion(2,TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send(UI_ACTION_SNMP_OID_SEARCH, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(FILTER, new JsonObject().put(SNMPOIDGroup.VENDOR, "Juniper Networks").put(OID_NAME, "cpu").put(OID_TYPE, OIDGroupType.SCALAR.getName())));

        assertSNMPOIDGroupTestResult(testContext, STATUS_SUCCEED, null, null, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSearchTabularSNMPOID(VertxTestContext testContext, TestInfo testInfo)
    {
//        testContext.awaitCompletion(2,TimeUnit.SECONDS);

        Bootstrap.vertx().eventBus().send(UI_ACTION_SNMP_OID_SEARCH, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(FILTER, new JsonObject().put(SNMPOIDGroup.VENDOR, "Juniper Networks").put(OID_NAME, "cpu").put(OID_TYPE, OIDGroupType.TABULAR.getName())));

        assertSNMPOIDGroupTestResult(testContext, STATUS_SUCCEED, null, null, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGetAllObjectByNetworkCategory(VertxTestContext testContext)
    {
        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + OBJECT_CATEGORY + "=" + new JsonArray().add(Category.NETWORK.getName())
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestFilterTestResult(response, ObjectConfigStore.getStore(), OBJECT_CATEGORY, new JsonArray().add(Category.NETWORK.getName()), new JsonArray().add(OBJECT_SCHEDULER).add(OBJECT_INSTANCES));

                            var objects = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_IP, "**********");

                            Assertions.assertNotNull(objects);

                            Assertions.assertFalse(objects.isEmpty());

                            var object = objects.getJsonObject(0);

                            CONTEXT.put("object", object.getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAssignObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(TestSNMPDeviceCatalog.CONTEXT.getLong("object")));

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog") + "/assign", context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("Monitor assigned to SNMP Device Catalog successfully", response.bodyAsJsonObject().getString(MESSAGE));

                            TimeUnit.SECONDS.sleep(1);

                            var metrics = MetricConfigStore.getStore().getItemsByObject(TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

                            for (var metric : metrics)
                            {
                                if (metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName()) || metric.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_TABULAR_METRIC.getName()))
                                {
                                    METRIC_IDS.add(metric.getLong(ID));

                                    //Task - 3534
                                    Assertions.assertTrue(metric.getJsonObject(Metric.METRIC_CONTEXT).getInteger(PLUGIN_ID) >= 300);
                                }
                            }

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    @Timeout(value = 70, timeUnit = TimeUnit.SECONDS)
    void testGetReferencesSNMPDeviceCatalog(VertxTestContext testContext)
    {
        var item = UserConfigStore.getStore().getItemByValue(USER_NAME, "MinimalAccess");

        Assertions.assertNotNull(item);

        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, item.getString(USER_NAME))
                .put(USER_PASSWORD, item.getString(USER_PASSWORD)), asyncResponse ->
        {
            if (asyncResponse.succeeded())
            {
                var response = asyncResponse.result();

                assertEquals(HttpStatus.SC_OK, response.statusCode());

                var body = response.bodyAsJsonObject();

                Assertions.assertNotNull(body);

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                Assertions.assertTrue(body.containsKey(APIConstants.AUTH_REFRESH_TOKEN));

                TestAPIUtil.get(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.device.catalog") + "/references", new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + body.getString(APIConstants.AUTH_ACCESS_TOKEN))
                        , testContext.succeeding(httpresponse ->
                                testContext.verify(() ->
                                {
                                    LOGGER.info("testGetReferencesSNMPDeviceCatalog : response: " + httpresponse.bodyAsJsonObject().encode());

                                    assertEquals(SC_OK, httpresponse.statusCode());

                                    var responseBody = httpresponse.bodyAsJsonObject();

                                    Assertions.assertNotNull(responseBody);

                                    assertEquals(SC_OK, responseBody.getInteger(RESPONSE_CODE));

                                    Assertions.assertNotNull(responseBody.getJsonObject(RESULT).getJsonArray(Entity.OBJECT.getName()));

                                    testContext.completeNow();

                                })));
            }
            else
            {
                LOGGER.error(asyncResponse.cause());

                testContext.failNow(asyncResponse.cause());
            }

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
        //bug-4482
    void testCreateSecondSNMPOIDGroupAndCheckInMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group Second").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, "scalar").put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.2.3.5.9.1.2", "counter-2"));

        TestAPIUtil.post(SNMP_OID_GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPOIDGroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP OID Group"), new JsonArray()
                                            .add(SNMP_OID_GROUP_ID).add(SNMP_OID_GROUP_DEVICE_TYPE), LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.oid.group.second", response.bodyAsJsonObject().getLong(ID));

                            Bootstrap.vertx().setPeriodic(1000, timer ->
                            {
                                var retries = 0;

                                var metrics = MetricConfigStore.getStore().getItemsByObject(TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

                                for (var metric : metrics)
                                {
                                    if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase("Metric Group Second") || metric.getString(Metric.METRIC_NAME).equalsIgnoreCase("Metric Group1"))
                                    {
                                        retries++;
                                    }
                                }

                                if (retries > 0)
                                {
                                    Bootstrap.vertx().cancelTimer(timer);

                                    testContext.completeNow();
                                }
                            });
                        })));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    @Timeout(value = 100, timeUnit = TimeUnit.SECONDS)
    void testSNMPOIDGroupScalarInvalid(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, OIDGroupType.SCALAR.getName()).put(SNMP_OID_GROUP_OIDS, new JsonObject().put("3.4.99.1", "memory")).put(ID, TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        assertSNMPOIDGroupTestResult(testContext, STATUS_FAIL, new JsonArray().add(String.format(ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, "request timeout (after 0 retries)"))
                .add(String.format(ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, ErrorMessageConstants.PROCESS_TIMED_OUT)), ErrorCodes.ERROR_CODE_SNMP_OID_GROUP_TEST, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"), STATUS_UP, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_OID_GROUP_TEST, context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSNMPOIDGroupScalar(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, OIDGroupType.SCALAR.getName()).put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.3.6.1.4.1.9.2.1.58.0", "cpu").put(".1.3.6.1.4.1.9.9.48.1.1.1.6.1", "memory").put(".1.3.6.1.2.1.2.2.1.1", "interface").put(".1.3.6.1.4.1.9.9.48.1.1.1.6.2", "invalid")).put(ID, TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        assertSNMPOIDGroupTestResult(testContext, STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"), STATUS_UP, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_OID_GROUP_TEST, context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSNMPOIDGroupTabular(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, OIDGroupType.TABULAR.getName()).put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.3.6.1.2.1.2.2.1.1", "interface1")).put(ID, TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        assertSNMPOIDGroupTestResult(testContext, STATUS_SUCCEED, null, ErrorCodes.ERROR_CODE_SUCCESS, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"), STATUS_UP, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_OID_GROUP_TEST, context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testDeleteActiveSNMPDeviceCatalog(VertxTestContext testContext)
    {

        TestAPIUtil.delete(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + CONTEXT.getLong("snmp.device.catalog"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertEntityInUsedDeleteTestResult(response, "SNMP Device Catalog");

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testMetricToNewDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {
        var catalogContext = new JsonObject()
                .put(SNMP_DEVICE_CATALOG_MODEL, "demo catalog").put(SNMP_DEVICE_CATALOG_OID, ".1.3.6.1.4.1.171.10.76.7.2").put(SNMP_DEVICE_CATALOG_TYPE, "SNMP Device").put(SNMP_DEVICE_CATALOG_VENDOR, "Vendor").put("_type", "1");

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT, catalogContext,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPDeviceCatalogConfigStore.getStore(), catalogContext, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Device Catalog"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.device.catalog2", response.bodyAsJsonObject().getLong(ID));

                            var metricGroupContext = new JsonObject().put(SNMP_OID_GROUP_NAME, "Group2").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog2"))
                                    .put(SNMP_OID_GROUP_TYPE, "scalar").put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.3.6.1.4.1.9.2.1.58.0", "counter"));

                            TestAPIUtil.post(SNMP_OID_GROUP_API_ENDPOINT, metricGroupContext,
                                    testContext.succeeding(oidGroupResponse ->
                                            testContext.verify(() ->
                                            {
                                                TestAPIUtil.assertCreateEntityTestResult(SNMPOIDGroupConfigStore.getStore(), metricGroupContext, oidGroupResponse.bodyAsJsonObject(),
                                                        String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP OID Group"), new JsonArray()
                                                                .add(SNMP_OID_GROUP_ID).add(SNMP_OID_GROUP_DEVICE_TYPE), LOGGER, testInfo.getTestMethod().get().getName());

                                                TestSNMPDeviceCatalog.CONTEXT.put("snmp.oid.group2", oidGroupResponse.bodyAsJsonObject().getLong(ID));

                                                var assignObjectContext = new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(TestSNMPDeviceCatalog.CONTEXT.getLong("object")));

                                                TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog2") + "/assign", assignObjectContext,
                                                        testContext.succeeding(assignedObjectResponse ->
                                                                testContext.verify(() ->
                                                                {
                                                                    TestAPIUtil.assertValidResponseTestResult(assignedObjectResponse, LOGGER, testInfo.getTestMethod().get().getName());

                                                                    assertEquals("Monitor assigned to SNMP Device Catalog successfully", assignedObjectResponse.bodyAsJsonObject().getString(MESSAGE));

                                                                    var retries = new AtomicInteger(0);

                                                                    Bootstrap.vertx().setPeriodic(2000, timer ->
                                                                    {
                                                                        retries.getAndIncrement();

                                                                        var valid = false;

                                                                        var metrics = MetricConfigStore.getStore().getItemsByObject(TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

                                                                        for (var metric : metrics)
                                                                        {
                                                                            if (metric.getString(Metric.METRIC_NAME).equalsIgnoreCase("Group2"))
                                                                            {
                                                                                valid = true;

                                                                                break;
                                                                            }
                                                                        }

                                                                        if (valid)
                                                                        {
                                                                            Bootstrap.vertx().cancelTimer(timer);

                                                                            testContext.completeNow();
                                                                        }
                                                                    });
                                                                })));
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testPollMetric(VertxTestContext testContext)
    {
        var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_OBJECT, TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        Assertions.assertNotNull(metrics);

        Assertions.assertFalse(metrics.isEmpty());

        JsonObject metric = null;

        for (var index = 0; index < metrics.size(); index++)
        {
            if (metrics.getJsonObject(index).getString(Metric.METRIC_NAME).equalsIgnoreCase("Group2"))
            {
                metric = metrics.getJsonObject(index);

                break;
            }
        }

        if (metric != null)
        {
            var context = TestNMSUtil.prepareMetricPollContext(metric);

            byteMessageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
            {
                try
                {
                    var event = message.body();

                    if (event.getLong(EVENT_ID).equals(context.getLong(EVENT_ID)))
                    {
                        testContext.verify(() ->
                        {
                            byteMessageConsumer.unregister();

                            Assertions.assertTrue(event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED));

                            testContext.completeNow();
                        });
                    }
                }
                catch (Exception exception)
                {
                    byteMessageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });

            Bootstrap.vertx().eventBus().send(EVENT_ROUTER, context.put(GlobalConstants.TIMEOUT, 20));
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testScheduleCustomSNMPMetricGroupPoll(VertxTestContext testContext)
    {
//        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var retries = new AtomicInteger(0);

        var size = MetricConfigStore.getStore().getItemsByObject(TestSNMPDeviceCatalog.CONTEXT.getLong("object")).size();

        byteMessageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_METRIC_POLL_RESPONSE, message ->
        {
            try
            {
                var event = message.body();

                if (event.getLong(Metric.METRIC_OBJECT).equals(TestSNMPDeviceCatalog.CONTEXT.getLong("object")))
                {
                    retries
                            .getAndIncrement();

                    if (event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_SCALAR_METRIC.getName()) || event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_TABULAR_METRIC.getName()))
                    {
                        testContext.verify(() ->
                        {
                            byteMessageConsumer.unregister();

                            testContext.completeNow();
                        });
                    }
                    else if (retries
                            .get() == size)
                    {
                        byteMessageConsumer.unregister();

                        testContext.failNow(new Exception("failed to poll custom snmp device catalog metric group"));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                byteMessageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_POLL, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, DEFAULT_USER).put(ID, TestSNMPDeviceCatalog.CONTEXT.getLong("object")));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testSearchSpecialCharacter0(VertxTestContext testContext, TestInfo testInfo)
    {

        var filter = new JsonObject().put("vendor", "Agenzia per l'Italia Digitale").put(OID_NAME, "Power").put(OID_TYPE, OIDGroupType.SCALAR.getName());

        TestAPIUtil.get(SNMP_OID_GROUP_SEARCH_API_ENDPOINT + "?filter=" + filter
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertNotNull(response.bodyAsJsonObject());

                            assertNotNull(response.bodyAsJsonObject().getJsonArray(RESULT));

                            testContext.completeNow();
                        })));
    }

    @Disabled
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testSearchSpecialCharacter1(VertxTestContext testContext, TestInfo testInfo)
    {

        var filter = new JsonObject().put("vendor", "Hrvatska poštanska banka, dionicko društvo").put(OID_NAME, "Power").put(OID_TYPE, OIDGroupType.SCALAR.getName());

        TestAPIUtil.get(SNMP_OID_GROUP_SEARCH_API_ENDPOINT + "?filter=" + filter
                , testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertNotNull(response.bodyAsJsonObject());

                            assertNotNull(response.bodyAsJsonObject().getJsonArray(RESULT));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCreateNetworkSNMPDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SNMP_DEVICE_CATALOG_MODEL, "testassign").put(SNMP_DEVICE_CATALOG_OID, ".1.3.6.1.4.1.9.1.697").put(SNMP_DEVICE_CATALOG_TYPE, "Switch").put(SNMP_DEVICE_CATALOG_VENDOR, "Vendor").put("_type", "1");

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPDeviceCatalogConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Device Catalog"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.device.catalog", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testUpdateAssignObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(TestSNMPDeviceCatalog.CONTEXT.getLong("object")));

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog") + "/assign", context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("Monitor assigned to SNMP Device Catalog successfully", response.bodyAsJsonObject().getString(MESSAGE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testCreateSNMPOIDSecondGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group" + System.currentTimeMillis())
                .put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, "scalar").put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.2.3.5.9", "counter"));

        TestAPIUtil.post(SNMP_OID_GROUP_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPOIDGroupConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP OID Group"), new JsonArray()
                                            .add(SNMP_OID_GROUP_ID).add(SNMP_OID_GROUP_DEVICE_TYPE), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testDeleteSNMPDeviceCatalog(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        Assertions.assertNotNull(object);

        object.put(AIOpsObject.OBJECT_SNMP_DEVICE_CATALOG, 12345L);

        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                object,
                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                future ->
                {
                    if (future.failed())
                    {
                        testContext.failNow(future.cause());
                    }
                    else
                    {
                        ObjectConfigStore.getStore().updateItem(object.getLong(ID)).onComplete(result ->
                        {
                            if (result.failed())
                            {
                                testContext.failNow(result.cause());
                            }
                            else
                            {
                                TestAPIUtil.delete(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"),
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    assertEquals(SC_OK, response.statusCode());

                                                    var body = response.bodyAsJsonObject();

                                                    Assertions.assertNotNull(body);

                                                    Assertions.assertEquals(SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                                                    var retries = new AtomicInteger(0);

                                                    TestUtil.vertx().setPeriodic(1000, timer ->
                                                    {
                                                        var oidGroups = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID,
                                                                TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"));

                                                        if (oidGroups.isEmpty())
                                                        {
                                                            TestUtil.vertx().cancelTimer(timer);

                                                            testContext.completeNow();
                                                        }
                                                        else if (retries.get() > 30)
                                                        {
                                                            TestUtil.vertx().cancelTimer(timer);

                                                            testContext.failNow(new Exception("failed to delete oid metric group of snmp device catalogs"));
                                                        }
                                                        else
                                                        {
                                                            retries.getAndIncrement();
                                                        }
                                                    });
                                                    testContext.completeNow();
                                                })));
                            }
                        });
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testCreateCiscoNetworkSNMPDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SNMP_DEVICE_CATALOG_MODEL, "testassign2").put(SNMP_DEVICE_CATALOG_OID, ".1.3.6.1.4.1.9.1.697").put(SNMP_DEVICE_CATALOG_TYPE, "Switch").put(SNMP_DEVICE_CATALOG_VENDOR, "Vendor").put("_type", "1");

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPDeviceCatalogConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Device Catalog"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.device.catalog", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testUpdateSwitchAssignObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(TestSNMPDeviceCatalog.CONTEXT.getLong("object")));

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT + "/" + TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog") + "/assign", context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertEquals("Monitor assigned to SNMP Device Catalog successfully", response.bodyAsJsonObject().getString(MESSAGE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testSNMPOIDGroupWithDownObject(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(SNMP_OID_GROUP_NAME, "Metric Group").put(SNMP_DEVICE_CATALOG_ID, TestSNMPDeviceCatalog.CONTEXT.getLong("snmp.device.catalog"))
                .put(SNMP_OID_GROUP_TYPE, OIDGroupType.SCALAR.getName()).put(SNMP_OID_GROUP_OIDS, new JsonObject().put(".1.3.6.1.4.1.9.2.1.58.0", "cpu").put(".1.3.6.1.4.1.9.9.48.1.1.1.6.1", "memory").put(".1.3.6.1.2.1.2.2.1.1", "interface")).put(ID, TestSNMPDeviceCatalog.CONTEXT.getLong("object"));

        assertSNMPOIDGroupTestResult(testContext, STATUS_FAIL, new JsonArray().add(String.format(ErrorMessageConstants.SNMP_OID_GROUP_TEST_FAILED, ErrorMessageConstants.OBJECT_ERROR)), ErrorCodes.ERROR_CODE_NO_ITEM_FOUND, testInfo.getTestMethod().get().getName());

        ObjectStatusCacheStore.getStore().updateItem(TestSNMPDeviceCatalog.CONTEXT.getLong("object"), STATUS_DOWN, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_OID_GROUP_TEST, context.put(APIConstants.SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testCreateSecondSNMPDeviceCatalog(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(SNMP_DEVICE_CATALOG_MODEL, "test").put(SNMP_DEVICE_CATALOG_OID, ".1.3.6.1.4.1.171.10.76.20.1").put(SNMP_DEVICE_CATALOG_TYPE, "SNMP Device").put(SNMP_DEVICE_CATALOG_VENDOR, "Vendor").put("_type", "1");

        TestAPIUtil.post(SNMP_DEVICE_CATALOG_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(SNMPDeviceCatalogConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "SNMP Device Catalog"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestSNMPDeviceCatalog.CONTEXT.put("snmp.device.catalog", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testDeleteAllSNMPOIDGroups(VertxTestContext testContext)
    {
        var ids = SNMPOIDGroupConfigStore.getStore().getIds();

        Assertions.assertFalse(ids.isEmpty());

        TestAPIUtil.deleteAll(SNMP_OID_GROUP_API_ENDPOINT,
                new JsonObject().put(REQUEST_PARAM_IDS, ids),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_OK, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertEquals(SC_OK, body.getInteger(APIConstants.RESPONSE_CODE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testDeleteInvalidOIDGroups(VertxTestContext testContext)
    {
        var ids = new JsonArray().add(1234L).add(3455L);

        TestAPIUtil.deleteAll(SNMP_OID_GROUP_API_ENDPOINT,
                new JsonObject().put(REQUEST_PARAM_IDS, ids),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_BAD_REQUEST, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertEquals(SC_BAD_REQUEST, body.getInteger(APIConstants.RESPONSE_CODE));

                            Assertions.assertEquals(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, Entity.SNMP_OID_GROUP.getName(),
                                    String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.SNMP_OID_GROUP.getName())), body.getString(MESSAGE));

                            testContext.completeNow();

                        })));
    }


    private void assertSNMPOIDGroupTestResult(VertxTestContext testContext, String status, JsonArray messages, String errorCode, String methodName)
    {
        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                try
                {
                    LOGGER.debug(message.body());

                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_OID_GROUP_TEST) && eventContext.containsKey(STATUS))
                    {
                        testContext.verify(() ->
                        {
                            LOGGER.info(methodName + ": eventContext: " + eventContext.encode());

                            Assertions.assertEquals(eventContext.getString(STATUS), status);

                            if (messages != null)
                            {
                                Assertions.assertTrue(messages.contains(eventContext.getString(MESSAGE)));
                            }

                            Assertions.assertEquals(eventContext.getString(ERROR_CODE), errorCode);

                            if (eventContext.getString(SNMP_OID_GROUP_TYPE).equalsIgnoreCase("scalar") && eventContext.containsKey(RESULT) && !eventContext.getJsonArray(RESULT).isEmpty())
                            {
                                var items = eventContext.getJsonArray(RESULT);

                                for (var index = 0; index < items.size(); index++)
                                {
                                    var item = items.getJsonObject(index);

                                    Assertions.assertFalse(item.containsKey(SNMP_OID_GROUP_VALID_OIDS));
                                }
                            }

                            messageConsumer.unregister(result -> testContext.completeNow());
                        });
                    }

                    if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_SNMP_OID_SEARCH))
                    {
                        Assertions.assertEquals(SC_OK, eventContext.getInteger(RESPONSE_CODE));

                        Assertions.assertEquals(status, eventContext.getString(STATUS));

                        if (eventContext.getJsonObject(FILTER).getString(OID_TYPE).equalsIgnoreCase(OIDGroupType.TABULAR.getName()))
                        {
                            Assertions.assertEquals(4, eventContext.getJsonObject(RESULT).size());

                            testContext.completeNow();

                        }
                        else if (eventContext.getJsonObject(FILTER).getString(OID_TYPE).equalsIgnoreCase(OIDGroupType.SCALAR.getName()))
                        {
                            Assertions.assertEquals(2, eventContext.getJsonObject(RESULT).size());

                            testContext.completeNow();
                        }
                    }

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    messageConsumer.unregister(result -> testContext.failNow(exception));
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }
    }
}


