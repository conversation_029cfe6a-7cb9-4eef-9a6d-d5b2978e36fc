/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  20-May-2025     Aagam           MOTADATA-5847 : Added windows build support
 *
 */

package com.mindarray.util;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.EVENT_TRACKER_PING_CHECKED;
import static com.mindarray.util.WorkerUtil.ABNORMAL_PROCESS_EXIT_CODES;
import static com.mindarray.util.WorkerUtil.PLUGIN_CONTEXTS;


public final class PingUtil
{
    private static final Logger LOGGER = new Logger(PingUtil.class, GlobalConstants.MOTADATA_UTIL, "Ping Util");

    private PingUtil()
    {
    }

    public static JsonObject ping(String target, int timeout, int retry, long eventId)
    {
        return OS_WINDOWS ? pingWindows(target, timeout, retry, eventId) : pingLinux(target, timeout, retry, eventId);
    }

    private static JsonObject pingLinux(String target, int timeout, int retry, long eventId)
    {
        var result = new JsonObject().put(STATUS, STATUS_UNKNOWN);

        try
        {
            if (CommonUtil.isNotNullOrEmpty(target))
            {

                var arguments = new ArrayList<String>();

                arguments.add("ping");

                arguments.add("-c");

                arguments.add(CommonUtil.getString(retry));

                arguments.add("-W");

                arguments.add(CommonUtil.getString(timeout));

                arguments.add(target);

                var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

                var timedOut = new AtomicBoolean(false);

                var process = processBuilder.start();

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("ping process %s started for %s", process.pid(), target));

                }

                WorkerUtil.setupCleanupTimer(retry * timeout, process, processBuilder, eventId, null, timedOut);

                var exited = process.waitFor(CommonUtil.getLong(retry * timeout) + 1, TimeUnit.SECONDS);

                if (exited)
                {
                    var exitCode = process.exitValue();

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("ping process having PID %s exited with status code %s", process.pid(), exitCode));
                    }

                    if (ABNORMAL_PROCESS_EXIT_CODES.contains(exitCode))
                    {
                        timedOut.set(true);
                    }
                    else
                    {
                        try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                        {
                            var output = reader.lines().collect(Collectors.joining(System.lineSeparator()));

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("ping output %s of %s", output, target));
                            }

                            var found = false;

                            for (var line : output.split(NEW_LINE))
                            {
                                line = line.toLowerCase();

                                if (line.trim().contains("ping statistics"))
                                {
                                    found = true;
                                }

                                if (found)
                                {
                                    if (line.trim().contains("min/avg/max"))
                                    {
                                        result.put(STATUS, STATUS_UP);

                                        break;
                                    }
                                    else
                                    {
                                        result.put(STATUS, STATUS_DOWN);
                                    }
                                }
                            }
                        }
                    }
                }

                if (timedOut.get())
                {
                    throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
                }
            }
        }

        catch (Exception exception)
        {
            if (exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {
                result.put(STATUS, STATUS_TIME_OUT);
            }

            else
            {
                result.put(STATUS, STATUS_UNKNOWN);

                LOGGER.error(exception);
            }

        }

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("ping result %s of %s", result.encodePrettily(), target));
        }

        return result;

    }


    private static JsonObject pingWindows(String target, int timeout, int retry, long eventId)
    {
        var result = new JsonObject().put(STATUS, STATUS_UNKNOWN);

        try
        {
            if (CommonUtil.isNotNullOrEmpty(target))
            {

                var arguments = new ArrayList<String>();

                arguments.add("ping");

                arguments.add("-n");

                arguments.add(CommonUtil.getString(retry));

                arguments.add("-w");

                arguments.add(CommonUtil.getString(timeout));

                arguments.add(target);

                var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

                var timedOut = new AtomicBoolean(false);

                var process = processBuilder.start();

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("ping process %s started for %s", process.pid(), target));

                }

                WorkerUtil.setupCleanupTimer(retry * timeout, process, processBuilder, eventId, null, timedOut);

                var exited = process.waitFor(CommonUtil.getLong(retry * timeout) + 1, TimeUnit.SECONDS);

                if (exited)
                {
                    var exitCode = process.exitValue();

                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(String.format("ping process having PID %s exited with status code %s", process.pid(), exitCode));
                    }

                    if (ABNORMAL_PROCESS_EXIT_CODES.contains(exitCode))
                    {
                        timedOut.set(true);
                    }
                    else
                    {
                        try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                        {
                            var output = reader.lines().collect(Collectors.joining(System.lineSeparator()));

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("ping output %s of %s", output, target));
                            }

                            var found = false;

                            for (var line : output.split(NEW_LINE))
                            {
                                line = line.toLowerCase();

                                if (line.trim().contains("ping statistics"))
                                {
                                    found = true;
                                }

                                if (found)
                                {
                                    if (line.trim().contains("average") || line.trim().contains("minimum"))
                                    {
                                        result.put(STATUS, STATUS_UP);

                                        break;
                                    }
                                    else
                                    {
                                        result.put(STATUS, STATUS_DOWN);
                                    }
                                }
                            }
                        }
                    }
                }

                if (timedOut.get())
                {
                    throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
                }
            }
        }

        catch (Exception exception)
        {
            if (exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {
                result.put(STATUS, STATUS_TIME_OUT);
            }

            else
            {
                result.put(STATUS, STATUS_UNKNOWN);

                LOGGER.error(exception);
            }

        }

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("ping result %s of %s", result.encodePrettily(), target));
        }

        return result;

    }

    public static JsonObject ping(String target, long eventId)
    {
        return ping(target, 3, 3, eventId);
    }

    public static boolean isReachable(String target, long eventId)
    {
        var result = OS_WINDOWS ? pingWindows(target, 3, 3, eventId) : ping(target, 3, 3, eventId);

        EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PING_CHECKED, DateTimeUtil.timestamp()));

        return result.getString(STATUS).equalsIgnoreCase(STATUS_UP);
    }

    public static boolean isReachable(String target, int timeout, int retry, long eventId)
    {
        var result = OS_WINDOWS ? pingWindows(target, timeout, retry, eventId) : ping(target, timeout, retry, eventId);

        EventBusConstants.updateEvent(eventId, String.format(EVENT_TRACKER_PING_CHECKED, DateTimeUtil.timestamp()));

        return result.getString(STATUS).equalsIgnoreCase(STATUS_UP);
    }

    public static JsonArray ping(JsonArray targets, int timeout, int packets, Map<String, JsonObject> events, String thread) throws Exception
    {
        return OS_WINDOWS ? pingWindows(targets, timeout, packets, events, thread) : pingLinux(targets, timeout, packets, events, thread);
    }

    private static JsonArray pingLinux(JsonArray targets, int timeout, int packets, Map<String, JsonObject> events, String thread) throws Exception
    {
        var probes = new JsonArray();

        try
        {
            var arguments = new ArrayList<String>();

            arguments.add("fping");

            arguments.add("-c");

            arguments.add(CommonUtil.getString(packets));

            arguments.add("-t");

            arguments.add(CommonUtil.getString(timeout * 1000L));

            arguments.add("-q");

            arguments.add("-u");

            if (events != null)
            {
                for (var event : events.values())
                {
                    arguments.add(event.getString(AIOpsObject.OBJECT_IP));

                    EventBusConstants.startEvent(event.getLong(EventBusConstants.EVENT_ID), thread);
                }
            }
            else
            {
                targets.forEach(target -> arguments.add(CommonUtil.getString(target)));
            }

            var builder = new StringBuilder();

            var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(Boolean.TRUE);

            var process = processBuilder.start();

            var timedOut = new AtomicBoolean(false);

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("fping process %s started for %s", process.pid(), targets));
            }

            WorkerUtil.setupCleanupTimer((targets.size() * packets * timeout) + 1, process, processBuilder, NOT_AVAILABLE, null, timedOut);

            var exited = process.waitFor(CommonUtil.getLong(targets.size() * packets * timeout) + 2, TimeUnit.SECONDS);

            if (exited)
            {
                var exitCode = process.exitValue();

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("fping process having PID %s exited with status code %s", process.pid(), exitCode));
                }

                if (ABNORMAL_PROCESS_EXIT_CODES.contains(exitCode))
                {
                    timedOut.set(true);
                }
                else
                {
                    try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
                    {

                        String line;

                        while ((line = reader.readLine()) != null)
                        {
                            builder.append(line).append(NEW_LINE);
                        }
                    }
                }
            }

            if (timedOut.get())
            {
                throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
            }

            if (!builder.isEmpty())
            {
                for (var output : builder.toString().split(NEW_LINE))
                {

                    //sample output :: String output = "*******      : xmt/rcv/%loss = 3/3/0%, min/avg/max = 14.2/15.2/17.1";

                    if (!output.contains("duplicate"))
                    {
                        var probe = new JsonObject();

                        // #28175 - splitting with ": " instead of ":" will add support to ipv6 also
                        var tokens = output.split(": ");

                        probe.put(AIOpsObject.OBJECT_TARGET, tokens[0].trim());

                        tokens = tokens[1].split(",");

                        var splittedTokens = tokens[0].trim().split("=")[1].trim().replaceAll("%", EMPTY_VALUE).split("/");

                        probe.put(NMSConstants.PACKET_LOST_PERCENT, CommonUtil.getFloat(splittedTokens[2]));

                        probe.put(NMSConstants.PING_SENT_PACKETS, CommonUtil.getInteger(splittedTokens[0]));

                        probe.put(NMSConstants.PING_RECEIVED_PACKETS, CommonUtil.getInteger(splittedTokens[1]));

                        probe.put(NMSConstants.PING_LOST_PACKETS, probe.getInteger(NMSConstants.PING_SENT_PACKETS) - probe.getInteger(NMSConstants.PING_RECEIVED_PACKETS));

                        if (tokens.length > 1)
                        {
                            tokens = tokens[1].trim().split("=")[1].trim().replaceAll("%", EMPTY_VALUE).split("/");

                            probe.put(NMSConstants.PING_MIN_LATENCY, CommonUtil.getFloat(tokens[0]));

                            probe.put(NMSConstants.PING_LATENCY, CommonUtil.getFloat(tokens[1]));

                            probe.put(NMSConstants.PING_MAX_LATENCY, CommonUtil.getFloat(tokens[2]));

                        }

                        if (probe.getFloat(NMSConstants.PACKET_LOST_PERCENT) == 100.0)
                        {
                            probe.put(GlobalConstants.STATUS, GlobalConstants.STATUS_DOWN);
                        }

                        else
                        {
                            probe.put(GlobalConstants.STATUS, GlobalConstants.STATUS_UP);

                        }

                        probes.add(probe);
                    }
                }
            }
        }

        catch (Exception exception)
        {

            if (!exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {
                LOGGER.error(exception);

            }

            throw exception;
        }

        if (CommonUtil.debugEnabled())
        {

            LOGGER.debug(String.format("fping result %s of %s", probes.encodePrettily(), targets));

        }

        return probes;

    }

    /**
     * Executes ping operations on multiple targets in Windows environment.
     * 1. Creates a context file with ping parameters
     * 2. Invokes the .NET ping utility with the context file
     * 3. Processes the output to create probe results
     *
     * @param targets JsonArray of IP addresses to ping
     * @param timeout Timeout in seconds for each ping operation
     * @param packets Number of ping packets to send per target
     * @param events  Map of events associated with targets (optional)
     * @param thread  Name of the thread executing the ping operation
     * @return JsonArray containing ping results for each target
     * @throws Exception If ping operation fails or times out
     */
    private static JsonArray pingWindows(JsonArray targets, int timeout, int packets, Map<String, JsonObject> events, String thread) throws Exception
    {
        var probes = new JsonArray();

        try
        {
            var arguments = new ArrayList<String>();

            var fileName = UUID.randomUUID().toString();

            var contexts = new JsonArray().add(new JsonObject().put(Metric.METRIC_PLUGIN, "ping").put(TIMEOUT, timeout).put("packets", packets));

            if (events != null)
            {
                contexts.getJsonObject(0).put("events", events);

                for (var event : events.values())
                {
                    EventBusConstants.startEvent(event.getLong(EventBusConstants.EVENT_ID), thread);
                }
            }
            else
            {
                contexts.getJsonObject(0).put("targets", targets);
            }

            var context = Base64.getEncoder().encodeToString(new JsonObject().put(SYSTEM_LOG_LEVEL, CommonUtil.getLogLevel()).put(INSTALLATION_TYPE, MotadataConfigUtil.getInstallationType()).put(PLUGIN_CONTEXTS, contexts).encode().getBytes());

            FileUtils.writeStringToFile(new File(CURRENT_DIR + PATH_SEPARATOR + PLUGIN_CONTEXT_DIR + PATH_SEPARATOR + fileName), context, StandardCharsets.UTF_8);

            arguments.add(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + DOTNET_ENGINE_DIR + PATH_SEPARATOR + DOTNET_PING_UTILITY_BIN);

            arguments.add(fileName);

            LOGGER.debug(String.format("windows ping started with arguments %s", arguments));

            var builder = new StringBuilder();

            var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(Boolean.TRUE);

            var process = processBuilder.start();

            var timedOut = new AtomicBoolean(false);

            LOGGER.debug(String.format("windows ping process %s started for %s", process.pid(), targets));

            WorkerUtil.setupCleanupTimer((targets.size() * packets * timeout) + 1, process, processBuilder, NOT_AVAILABLE, null, timedOut);

            try (var stream = process.getInputStream())
            {
                var bytes = new byte[1024];

                var length = 0;

                while ((length = stream.read(bytes)) != -1)
                {
                    builder.append(new String(bytes, 0, length));  // append raw output
                }
            }

            if (timedOut.get())
            {
                throw new Exception(ErrorMessageConstants.PROCESS_TIMED_OUT);
            }

            if (!builder.isEmpty())
            {
                LOGGER.debug(String.format("windows ping output %s", builder));

                probes = new JsonArray(new String(Base64.getDecoder().decode(builder.toString().split(VALUE_SEPARATOR_WITH_ESCAPE)[0])));
            }
        }

        catch (Exception exception)
        {

            if (!exception.getMessage().contains(ErrorMessageConstants.PROCESS_TIMED_OUT))
            {
                LOGGER.error(exception);
            }

            throw exception;
        }

        if (CommonUtil.debugEnabled())
        {
            LOGGER.debug(String.format("bulk ping result %s of %s", probes.encodePrettily(), targets));

        }

        return probes;

    }
}
