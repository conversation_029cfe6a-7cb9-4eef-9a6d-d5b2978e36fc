[{"_type": "0", "id": 10000000000001, "visualization.name": "LDAP New Connection", "visualization.description": "ActiveDirectory LDAP New Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ldap.new.connections.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.ldap.new.connections.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.ldap.new.connections.per.sec", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "LDAP New Connection", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ad.ldap.new.connections.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000002, "visualization.name": "LDAP Searches", "visualization.description": "ActiveDirectory LDAP Searches", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ldap.searches.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.ldap.searches.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.ldap.searches.per.sec", "icon": {"name": "network-search", "placement": "prefix"}}, "header": {"title": "LDAP Searches", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ad.ldap.searches.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000003, "visualization.name": "LDAP", "visualization.description": "ActiveDirectory LDAP", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ldap.bind.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ldap.client.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ldap.active.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.ldap.searches.per.sec"}, {"type": "metric", "data.point": "ad.ldap.new.connections.per.sec"}, {"type": "metric", "data.point": "ad.atq.ldap.threads"}, {"type": "metric", "data.point": "ad.ldap.successful.binds.per.sec"}, {"type": "metric", "data.point": "ad.ldap.bind.time.ms"}, {"type": "metric", "data.point": "ad.ldap.client.sessions"}, {"type": "metric", "data.point": "ad.ldap.active.threads"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.ldap.bind.time.ms", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "LDAP", "style": {"font.size": "medium"}, "data.points": [{"label": "Bind Time", "value": "ad.ldap.bind.time.ms.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Client", "value": "ad.ldap.client.sessions.last"}, {"label": "Active", "value": "ad.ldap.active.threads.last"}]}}}}, {"_type": "0", "id": 10000000000004, "visualization.name": "Directory Services", "visualization.description": "ActiveDirectory Directory Services", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ds.directory.searches.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ds.directory.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ds.directory.writes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.ds.directory.searches.per.sec"}, {"type": "metric", "data.point": "ad.ds.directory.reads.per.sec"}, {"type": "metric", "data.point": "ad.ds.directory.writes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.ds.directory.searches.per.sec", "icon": {"name": "folder-network", "placement": "prefix"}}, "header": {"title": "Directory Services", "style": {"font.size": "medium"}, "data.points": [{"label": "Searches", "value": "ad.ds.directory.searches.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Reads", "value": "ad.ds.directory.reads.per.sec.last"}, {"label": "Writes", "value": "ad.ds.directory.writes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000005, "visualization.name": "Replication", "visualization.description": "ActiveDirectory Replication", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.dra.sync.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.dra.inbound.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.dra.outbound.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.dra.pending.replication.synchronizations"}, {"type": "metric", "data.point": "ad.dra.inbound.dns.values.per.sec"}, {"type": "metric", "data.point": "ad.dra.outbound.dns.values.per.sec"}, {"type": "metric", "data.point": "ad.dra.inbound.objects.per.sec"}, {"type": "metric", "data.point": "ad.ldra.outbound.objects.per.sec"}, {"type": "metric", "data.point": "ad.dra.sync.requests"}, {"type": "metric", "data.point": "ad.dra.inbound.bytes.per.sec"}, {"type": "metric", "data.point": "ad.dra.outbound.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.dra.sync.requests", "icon": {"name": "repeat", "placement": "prefix"}}, "header": {"title": "Replication", "style": {"font.size": "medium"}, "data.points": [{"label": "Sync Requests", "value": "ad.dra.sync.requests.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Inbound", "value": "ad.dra.inbound.bytes.per.sec.last"}, {"label": "Outbound", "value": "ad.dra.outbound.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000006, "visualization.name": "Database", "visualization.description": "ActiveDirectory Database", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.database.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.database.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.database.reads.average.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ad.database.reads.per.sec"}, {"type": "metric", "data.point": "ad.database.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "ad.database.reads.average.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ad.database.read.per.second", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database", "style": {"font.size": "medium"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "ad.database.cache.hit.ratio.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Reads", "value": "ad.database.reads.per.sec.last"}, {"label": "Latency", "value": "ad.database.reads.average.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000000007, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000008, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000009, "visualization.name": "Datastore Details", "visualization.description": "ActiveDirectory Datastore Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.database.cache.size.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.database.page.faults.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000010, "visualization.name": "Replication Status", "visualization.description": "ActiveDirectory Replication Status", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.replication~destination", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.replication~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.replication~naming.context", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.replication~site", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.replication~last.attempted", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.replication~error.code", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~destination.last", "title": "Destination DC", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~naming.context.last", "title": "Naming Context", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~site.last", "title": "Site", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~last.attempted.last", "title": "Last Attempted", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.replication~error.code.last", "title": "Error Code", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000011, "visualization.name": "Replication Events", "visualization.description": "ActiveDirectory Replication Events", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.dra.sync.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.dra.outbound.objects.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ad.dra.inbound.dns.values.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.dra.sync.requests.last", "title": "Sync Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.dra.outbound.objects.per.sec.last", "title": "Outbound Objects (per sec)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ad.dra.inbound.dns.values.per.sec.last", "title": "Inbound DNS Values", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000012, "visualization.name": "LDAP Authentication", "visualization.description": "ActiveDirectory LDAP Authentication", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.kerberos.authentications.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ntlm.authentications.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000013, "visualization.name": "Replication Details", "visualization.description": "ActiveDirectory Replication Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.dra.inbound.objects.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ldra.outbound.objects.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.dra.pending.replication.synchronizations", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000014, "visualization.name": "Domain Server", "visualization.description": "ActiveDirectory Domain Server", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ds.server.binds.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ds.client.binds.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ds.active.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000015, "visualization.name": "LDAP Stats", "visualization.description": "ActiveDirectory LDAP Stats", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ldap.active.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ldap.client.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ldap.bind.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000016, "visualization.name": "Directory Services", "visualization.description": "ActiveDirectory Directory Services Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.ds.directory.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.ds.directory.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000017, "visualization.name": "Asynchronized Thread Queue", "visualization.description": "ActiveDirectory Asynchronized Thread Queue", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.atq.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.atq.estimated.queue.delay.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000018, "visualization.name": "Log Details", "visualization.description": "ActiveDirectory Log Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.log.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.log.waiting.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000019, "visualization.name": "SAM Details", "visualization.description": "ActiveDirectory SAM Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.sam.password.changes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.sam.successful.user.creations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.sam.user.creation.attempts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000020, "visualization.name": "Address Book", "visualization.description": "ActiveDirectory Address Book", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ad.address.book.client.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ad.address.book.searches.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]