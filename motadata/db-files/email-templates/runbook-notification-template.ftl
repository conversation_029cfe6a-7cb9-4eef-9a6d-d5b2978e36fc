<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Executed – Runbook</title>
    <style type="text/css">
        @media screen and (max-width:599px){
            .mobile-block{display:block}
            .mobile-100p{width:100%;height:auto}
            .mobile-hide{display:none}
            .mobile-space{padding-top:8px!important;padding-bottom:8px!important}
            .space16{height:16px}
            .side-spacing{padding-left:16px!important;padding-right:16px!important}
            .message-width{width:65%}
        }
        .major{color:#f58518}
        .critical{color:#f04e3e}
        .clear{color:#89c540}
        .down{color:#ed7c70}
        .warning{color:#f5bc18}
        .major.border{border-color:#f58518!important}
        .critical.border{border-color:#f04e3e!important}
        .clear.border{border-color:#89c540!important}
        .down.border{border-color:#ed7c70!important}
        .warning.border{border-color:#f5bc18!important}
    </style>
</head>
<body bgcolor="#F3F6F8" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table width="100%" bgcolor="#F3F6F8" border="0" cellpadding="0" cellspacing="0" align="center" style="border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;">
    <tr><td height="40"></td></tr>

    <#include "rebranding-header.ftl">

    <tr><td height="24"></td></tr>
    <tr>
        <td valign="top" align="center">
            <table width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffffff" style="padding:0 24px; border-top: 4px solid #009DDC; background:#fff;">
                <tr><td height="24"></td></tr>
                <tr><td style="text-align: center;"><img width="36" height="36" style="width:36px; height:36px;" src="cid:information.png" alt="clear"/></td></tr>
                <tr><td height="10"></td></tr>
                <tr>
                    <td style="text-align: center;">
                        <h1 style="font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;">Executed – Runbook</h1>
                    </td>
                </tr>
                <tr><td height="32"></td></tr>
                <tr><td><hr style="border: 1px solid #EEF2F6;"></td></tr>
                <tr><td height="16"></td></tr>
                <tr>
                    <td>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
                            <tr valign="top">
                                <td width="50%" style="padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Runbook</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["runbook.plugin.name"]??>${.data_model["runbook.plugin.name"]}</#if>
                                    </p>
                                </td>
                                <td width="50%" style="padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Triggered Policy</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["policy.name"]??>${.data_model["policy.name"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr valign="top">
                                <td width="33.333%" style="padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Severity</p>
                                    <#if .data_model["severity"]??>
                                        <p class="${.data_model["severity"]}" style="font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["severity"]}</p>
                                    </#if>
                                </td>
                                <td width="50%" style="padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Executed At</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["timestamp"]??>${.data_model["timestamp"]}</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr><td colspan="3"><hr style="border: 1px solid #EEF2F6;"></td></tr>
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr>
                                <td colspan="3" style="padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Message</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        Kindly find the output of the executed runbook attached with this email.
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr><td height="16"></td></tr>
                <tr><td><hr style="border: 1px solid #EEF2F6;"></td></tr>
                <tr>
                    <td>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
                            <tr><td colspan="3" height="16"></td></tr>
                            <tr>
                                <td colspan="3">
                                    <p style="color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;">
                                        <strong>Disclaimer:</strong>  This is an auto-generated message. Do not respond to this email.<br/>
                                        If you have any questions, please contact your System Administrator.
                                    </p>
                                </td>
                            </tr>
                            <tr><td colspan="3" height="24"></td></tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr><td height="24"></td></tr>
    <#if .data_model["rebranding"]?? && !.data_model["rebranding"]>
    <tr align="center">
        <td>
            <p style="font-size:10px; color:#7B8FA5;">For more details, visit
                <a style="color:#009DDC; text-decoration: none;" href="https://www.motadata.com/" target="_blank">www.motadata.com</a>
            </p>
        </td>
    </tr>
    </#if>
    <tr><td height="16"></td></tr>
    <#include "rebranding-footer.ftl">
    <tr><td height="32"></td></tr>
</table>
</body>
</html>
