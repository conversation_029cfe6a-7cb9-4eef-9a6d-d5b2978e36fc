/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.ha.HAConstants.CACHE_NAME;
import static com.mindarray.ha.HAConstants.HA_SYNC_OPERATION;

public class ObserverDatastore extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ObserverDatastore.class, MOTADATA_DATASTORE, "Observer Datastore");
    private final String uuid;
    private DatastoreEngine eventEngine;
    private String path;

    public ObserverDatastore(String uuid)
    {
        this.uuid = uuid;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            path = CURRENT_DIR + PATH_SEPARATOR + EVENT_DIR + PATH_SEPARATOR + EventBusConstants.replace(EVENT_HA_CONFIG_MANGER_SYNC) + DASH_SEPARATOR + uuid;

            vertx.eventBus().<JsonObject>localConsumer(EVENT_HA_CACHE_MANAGER_SYNC + DOT_SEPARATOR + uuid, message ->
            {
                var event = message.body();

                switch (HAConstants.HASyncOperation.valueOfName(CommonUtil.getByteValue(event.getValue(HA_SYNC_OPERATION))))
                {
                    case WRITE ->
                    {
                        try
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("received updated cache file %s ", event.getString(CACHE_NAME)));

                            }

                            Bootstrap.vertx().fileSystem().writeFileBlocking(path + PATH_SEPARATOR + event.getString(CACHE_NAME), Buffer.buffer(event.getBinary(RESULT)));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    case READ ->
                    {
                        var caches = new File(path).listFiles();

                        if (caches != null)
                        {
                            for (var cache : caches)
                            {
                                if (!cache.getName().equalsIgnoreCase("event-files") && !cache.getName().contains("bookmarks") && !cache.getName().endsWith(".dat"))
                                {
                                    var bytes = vertx.fileSystem().readFileBlocking(path + PATH_SEPARATOR + cache.getName());

                                    vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_OBSERVER, new JsonObject()
                                            .put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC)
                                            .put(EVENT_COPY_REQUIRED, false)
                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                                            .put(EVENT_TYPE, EVENT_HA_CACHE_OBSERVER_SYNC)
                                            .put(HA_SYNC_OPERATION, HAConstants.HASyncOperation.READ.getName())
                                            .put(CACHE_NAME, cache.getName())
                                            .put(RESULT, bytes));

                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("cache %s , size %s bytes , read by uuid %s ", cache.getName(), bytes.length(), event.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)));
                                    }

                                    vertx.fileSystem().deleteBlocking(path + PATH_SEPARATOR + cache.getName());
                                }
                            }
                        }
                    }
                }
            });

            eventEngine = new DatastoreEngine().setEventType(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + uuid)
                    .setPersistEventOffset(true).setStatus(false).setLogger(LOGGER).setEventHandler(this::send).start(vertx, promise);

            vertx.eventBus().<Void>localConsumer(EVENT_HA_CONFIG_MANGER_SYNC + DOT_SEPARATOR + uuid + ".inactive", message -> eventEngine.setStatus(false));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    private void send(JsonObject event)
    {
        vertx.eventBus().send(EVENT_PUBLICATION_MOTADATA_OBSERVER, event.put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, uuid).put(EVENT_TOPIC, REMOTE_EVENT_PROCESSOR_TOPIC).put(EVENT_TYPE, EVENT_HA_CONFIG_OBSERVER_SYNC));

        if (CommonUtil.traceEnabled())
        {
            LOGGER.trace(String.format("event sent to publication motadata observer %s ", uuid));
        }
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        try
        {
            if (eventEngine != null)
            {
                eventEngine.stop(vertx, promise);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}

