/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  3-Mar-2025      Chandresh       MOTADATA-4822: ldap.server.host -> ldap.server.primary.host
 *  24-Jun-2025     Pruthvi		    .encode method removes. added equals method for comparison.
 */

package com.mindarray.streaming;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.streaming.StreamingEngine.STREAMING_TYPE;

/**
 * The StreamingBroadcaster is responsible for broadcasting streaming data to connected clients.
 * <p>
 * This class works in conjunction with the StreamingEngine to:
 * <ul>
 *   <li>Manage streaming sessions for different types of streaming data</li>
 *   <li>Apply filtering logic to streaming data based on client preferences</li>
 *   <li>Enrich streaming data with additional context information</li>
 *   <li>Broadcast streaming data to the appropriate clients</li>
 *   <li>Enforce limits on the number of concurrent streaming sessions</li>
 * </ul>
 * <p>
 * The StreamingBroadcaster supports multiple types of streaming (defined in {@link StreamingEngine.StreamingType})
 * and maintains separate session maps for each type to ensure proper isolation and management.
 * <p>
 * This class is implemented as a Vert.x verticle and uses the Vert.x event bus for communication
 * with other components in the system.
 */
public class StreamingBroadcaster extends AbstractVerticle
{
    /**
     * Maximum number of concurrent streaming sessions allowed per streaming type
     */
    public static final int MAX_STREAMING_SESSIONS = 5;

    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(StreamingBroadcaster.class, GlobalConstants.MOTADATA_STREAMING, "Streaming Broadcaster");

    /**
     * Cache of column mappings for metric data
     */
    private final JsonObject columns = new JsonObject();

    /**
     * Map of active streaming sessions organized by streaming type
     */
    private final Map<StreamingEngine.StreamingType, Map<String, JsonObject>> sessionsByType = new EnumMap<>(StreamingEngine.StreamingType.class); // stream type ->  uuid -> object id

    /**
     * Initializes the StreamingBroadcaster verticle and sets up event bus handlers.
     * <p>
     * This method:
     * <ul>
     *   <li>Initializes session maps for each streaming type</li>
     *   <li>Sets up event bus handlers for column mapper updates</li>
     *   <li>Sets up event bus handlers for streaming events (start, stop, broadcast)</li>
     *   <li>Loads initial column mappings for metric data</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Initialize session maps for each streaming type
        for (var type : StreamingEngine.StreamingType.values())
        {
            sessionsByType.put(type, new HashMap<>());
        }

        // Set up handler for column mapper updates
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
        {
            try
            {
                var event = message.body();

                // Update column mappings when metric columns are updated
                if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
                {
                    update(event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        // Set up handler for streaming events
        vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
        {
            try
            {
                var event = message.body();
                var eventType = event.getString(EVENT_TYPE);

                // Route events to appropriate handlers based on event type
                if (eventType.equalsIgnoreCase(EVENT_STREAMING_START))
                {
                    start(event);  // Start a new streaming session
                }
                else if (eventType.equalsIgnoreCase(EVENT_STREAMING_STOP))
                {
                    stop(event);   // Stop an existing streaming session
                }
                else if (eventType.equalsIgnoreCase(EVENT_STREAMING_BROADCAST))
                {
                    broadcast(event);  // Broadcast data to streaming clients
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        }).exceptionHandler(LOGGER::error);

        // Load initial column mappings for metric data
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
        {
            if (reply.succeeded())
            {
                columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));
            }
        });

        // Initialization complete
        promise.complete();
    }

    /**
     * Broadcasts streaming data to clients based on their session configuration and filtering preferences.
     * <p>
     * This method:
     * <ul>
     *   <li>Determines the streaming type from the context</li>
     *   <li>Retrieves the session configuration for the client</li>
     *   <li>Applies filtering logic based on the streaming type and session configuration</li>
     *   <li>Enriches the data with additional context if needed</li>
     *   <li>Publishes the data to the client if it passes all filters</li>
     * </ul>
     *
     * @param context The streaming data context to broadcast
     */
    private void broadcast(JsonObject context)
    {
        try
        {
            // Default to qualified (will pass through filters unless disqualified)
            var qualified = true;

            // Get the streaming type from the context
            var type = StreamingEngine.StreamingType.valueOfName(context.getString(STREAMING_TYPE));

            // Get the session configuration for this client
            var session = sessionsByType.get(type).get(context.getString(APIConstants.SESSION_ID));

            // Merge event context into the main context if available
            if (context.getJsonObject(EVENT_CONTEXT) != null)
            {
                context.mergeIn(context.getJsonObject(EVENT_CONTEXT));
            }

            // Apply different filtering and broadcasting logic based on streaming type
            switch (type)
            {
                case LOG_TAIL ->
                {
                    // Get event sources filter from session
                    var eventSources = session.getJsonArray(EVENT_SOURCE, null);

                    // Get keywords filter from session
                    var keywords = session.getJsonArray("keywords", null);

                    // Filter by event source if specified
                    if (eventSources != null && !eventSources.isEmpty())
                    {
                        qualified = eventSources.contains(context.getString(EVENT_SOURCE));
                    }

                    // Filter by keywords if specified and still qualified
                    if (qualified && keywords != null && !keywords.isEmpty())
                    {
                        // Apply "any" or "all" matching logic based on match condition
                        qualified = session.getString("match.condition").equalsIgnoreCase("any")
                                ? keywords.stream().map(CommonUtil::getString).toList().stream().anyMatch(context.getString(EVENT)::contains)
                                : keywords.stream().map(CommonUtil::getString).toList().stream().allMatch(context.getString(EVENT)::contains);
                    }

                    // If passed all filters, publish to client
                    if (qualified)
                    {
                        EventBusConstants.publish(session.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST, context.put(EventBusConstants.UI_EVENT_UUID, session.getString(EventBusConstants.UI_EVENT_UUID)));
                    }
                }

                // For TRAP_TAIL, no filtering is applied - all traps are broadcast
                case TRAP_TAIL ->
                        EventBusConstants.publish(session.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST, context.put(EventBusConstants.UI_EVENT_UUID, session.getString(EventBusConstants.UI_EVENT_UUID)));

                case EVENT_ENGINE_STATS ->
                {
                    // Get the engine type from the context
                    var engineType = context.getString(ENGINE_TYPE);

                    // Add volume information based on engine type
                    if (EVENT_LOG.equals(engineType))
                    {
                        // Add log quota usage for log events
                        context.getJsonObject(EVENT_CONTEXT).put("volume", LicenseUtil.USED_LOG_QUOTA_BYTES.get());
                    }
                    else if (EVENT_FLOW.equals(engineType))
                    {
                        // Add flow quota usage for flow events
                        context.getJsonObject(EVENT_CONTEXT).put("volume", LicenseUtil.USED_FLOW_QUOTA_BYTES.get());
                    }

                    // Publish engine stats to client
                    EventBusConstants.publish(session.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST, context.put(UI_EVENT_UUID, session.getString(UI_EVENT_UUID)));
                }

                case EVENT_TRACKER ->
                {
                    // Get filters from session
                    var filter = session.getJsonObject(FILTER, new JsonObject());

                    // Apply event state filter if specified
                    if (CommonUtil.isNotNullOrEmpty(filter.getString(EVENT_STATE)))
                    {
                        qualified = context.getString(EVENT_STATE).equalsIgnoreCase(filter.getString(EVENT_STATE));
                    }

                    // Apply username filter if specified and still qualified
                    if (qualified && CommonUtil.isNotNullOrEmpty(filter.getString(USER_NAME)))
                    {
                        qualified = context.getString(USER_NAME).equalsIgnoreCase(filter.getString(USER_NAME));
                    }

                    // Apply object ID filter if specified and still qualified
                    if (qualified && filter.getLong(AIOpsObject.OBJECT_ID) != null)
                    {
                        qualified = context.getJsonObject(EVENT_CONTEXT).getLong(Metric.METRIC_OBJECT, 1L).equals(filter.getLong(AIOpsObject.OBJECT_ID));
                    }

                    // Apply status filter if specified and still qualified
                    if (qualified && CommonUtil.isNotNullOrEmpty(filter.getString(STATUS)))
                    {
                        qualified = context.getString(STATUS).equalsIgnoreCase(filter.getString(STATUS));
                    }

                    // Apply timestamp filter if specified and still qualified
                    if (qualified && filter.getInteger(EVENT_TIMESTAMP) != null)
                    {
                        // Check if event is within the specified time window
                        qualified = context.getLong(EVENT_TIMESTAMP) >= DateTime.now().minusHours(filter.getInteger(EVENT_TIMESTAMP)).getMillis();
                    }

                    // Apply event name filter if specified and still qualified
                    if (qualified && CommonUtil.isNotNullOrEmpty(filter.getString(EVENT_NAME)))
                    {
                        qualified = filter.getString(EVENT_NAME).equals(enrich(context).getString(EVENT_NAME));
                    }

                    // If passed all filters, enrich and publish to client
                    if (qualified)
                    {
                        var result = enrich(context);

                        EventBusConstants.publish(session.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST, new JsonObject().mergeIn(context)
                                .put(EVENT_TIMESTAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds() * 1000)))
                                .put(EVENT_TYPE, result.getString(EVENT_TYPE))
                                .put(EVENT_NAME, result.getString(EVENT_NAME)).put(UI_EVENT_UUID, session.getString(UI_EVENT_UUID)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Starts a new streaming session based on the provided context.
     * <p>
     * This method:
     * <ul>
     *   <li>Determines the streaming type from the context</li>
     *   <li>Checks if a session already exists for the client</li>
     *   <li>Enforces session limits to prevent resource exhaustion</li>
     *   <li>Creates a new session if allowed and notifies relevant components</li>
     *   <li>Sends error messages to the client if session limits are exceeded</li>
     * </ul>
     *
     * @param context The context containing information about the streaming session to start
     */
    private void start(JsonObject context)
    {
        try
        {
            // Get the streaming type from the context
            var type = StreamingEngine.StreamingType.valueOfName(context.getString(STREAMING_TYPE));

            // Get the sessions map for this streaming type
            var sessions = sessionsByType.get(type);

            switch (type)
            {
                // Handle LOG_TAIL streaming sessions
                case LOG_TAIL ->
                {
                    // Check if session already exists
                    if (!sessions.containsKey(context.getString(APIConstants.SESSION_ID)))
                    {
                        // Check if session limit is not exceeded
                        if (sessions.size() < MAX_STREAMING_SESSIONS)
                        {
                            // Notify that log tail streaming is starting
                            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                                    new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                            EventBusConstants.ChangeNotificationType.START_LOG_TAIL.name()));

                            // Create new session with composite key (session ID + UI event UUID)
                            sessions.put(context.getString(APIConstants.SESSION_ID) + GlobalConstants.SEPARATOR +
                                    context.getString(EventBusConstants.UI_EVENT_UUID), new JsonObject().mergeIn(context));
                        }
                        else
                        {
                            // Stop the streaming attempt if session limit exceeded
                            vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, context);

                            // Notify client about session limit exceeded
                            EventBusConstants.publish(context.getString(APIConstants.SESSION_ID),
                                    EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST,
                                    context.put(GlobalConstants.ERROR, ErrorMessageConstants.LOG_TAIL_SESSION_LIMIT_EXCEEDED));
                        }
                    }
                }

                // Handle TRAP_TAIL streaming sessions
                case TRAP_TAIL ->
                {
                    // Check if session already exists
                    if (!sessions.containsKey(context.getString(APIConstants.SESSION_ID)))
                    {
                        // Check if session limit is not exceeded
                        if (sessions.size() < MAX_STREAMING_SESSIONS)
                        {
                            // Notify that trap tail streaming is starting
                            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                                    new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                            ChangeNotificationType.START_TRAP_TAIL.name()));

                            // Create new session with composite key (session ID + UI event UUID)
                            sessions.put(context.getString(APIConstants.SESSION_ID) + GlobalConstants.SEPARATOR +
                                    context.getString(EventBusConstants.UI_EVENT_UUID), new JsonObject().mergeIn(context));
                        }
                        else
                        {
                            // Stop the streaming attempt if session limit exceeded
                            vertx.eventBus().send(EventBusConstants.EVENT_STREAMING_STOP, context);

                            // Notify client about session limit exceeded
                            EventBusConstants.publish(context.getString(APIConstants.SESSION_ID),
                                    EventBusConstants.UI_NOTIFICATION_STREAMING_BROADCAST,
                                    context.put(GlobalConstants.ERROR, ErrorMessageConstants.TRAP_TAIL_SESSION_LIMIT_EXCEEDED));
                        }
                    }
                }

                // Handle EVENT_TRACKER streaming sessions
                case EVENT_TRACKER ->
                {
                    // Create or update session if it doesn't exist or has different configuration
                    if (!sessions.containsKey(context.getString(APIConstants.SESSION_ID)) ||
                            !sessions.get(context.getString(APIConstants.SESSION_ID)).equals(context))
                    {
                        // Create or update session
                        sessions.put(context.getString(APIConstants.SESSION_ID), new JsonObject().mergeIn(context));

                        // Notify that event tracker streaming is starting
                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                                new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                        ChangeNotificationType.START_EVENT_TRACKER_STREAMING.name()));
                    }
                }

                // Handle EVENT_ENGINE_STATS streaming sessions
                case EVENT_ENGINE_STATS ->
                {
                    // Create session if it doesn't exist
                    if (!sessions.containsKey(context.getString(APIConstants.SESSION_ID)))
                    {
                        // Create new session
                        sessions.put(context.getString(APIConstants.SESSION_ID), new JsonObject().mergeIn(context));

                        // Notify that event engine stats streaming is starting
                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                                new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                        ChangeNotificationType.START_EVENT_STAT_STREAMING.name()));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Stops an existing streaming session based on the provided event.
     * <p>
     * This method:
     * <ul>
     *   <li>Determines the streaming type from the event</li>
     *   <li>Notifies relevant components that streaming is stopping</li>
     *   <li>Removes the session from the appropriate session map</li>
     * </ul>
     * <p>
     * Different streaming types may have different session key formats and cleanup requirements.
     *
     * @param event The event containing information about the streaming session to stop
     */
    private void stop(JsonObject event)
    {
        try
        {
            // Get the streaming type from the event
            var type = StreamingEngine.StreamingType.valueOfName(event.getString(STREAMING_TYPE));

            switch (type)
            {
                case EVENT_TRACKER ->
                {
                    // Notify that event tracker streaming is stopping
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                    ChangeNotificationType.STOP_EVENT_TRACKER_STREAMING.name()));

                    // Remove the session using session ID as key
                    sessionsByType.get(type).remove(event.getString(APIConstants.SESSION_ID));
                }

                case LOG_TAIL ->
                {
                    // Notify that log tail streaming is stopping
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                    EventBusConstants.ChangeNotificationType.STOP_LOG_TAIL.name()));

                    // Remove the session using composite key (session ID + UI event UUID)
                    sessionsByType.get(type).remove(event.getString(APIConstants.SESSION_ID) +
                            SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID));
                }

                case TRAP_TAIL ->
                {
                    // Notify that trap tail streaming is stopping
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                    ChangeNotificationType.STOP_TRAP_TAIL.name()));

                    // Remove the session using composite key (session ID + UI event UUID)
                    sessionsByType.get(type).remove(event.getString(APIConstants.SESSION_ID) +
                            SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID));
                }

                case EVENT_ENGINE_STATS ->
                {
                    // Notify that event engine stats streaming is stopping
                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                            new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                    ChangeNotificationType.STOP_EVENT_STAT_STREAMING.name()));

                    // Remove the session using session ID as key
                    sessionsByType.get(type).remove(event.getString(APIConstants.SESSION_ID));
                }

                // Default case for any other streaming types
                default ->
                    // Remove the session using composite key (session ID + UI event UUID)
                        sessionsByType.get(type).remove(event.getString(APIConstants.SESSION_ID) +
                                SEPARATOR + event.getString(EventBusConstants.UI_EVENT_UUID));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enriches event data with human-readable event types and names for display purposes.
     * <p>
     * This method processes the raw event data and generates user-friendly event type and name
     * information based on the event context. It handles a wide variety of event types and
     * formats the display information appropriately for each type.
     * <p>
     * The enriched information is used primarily for displaying events in the user interface,
     * making it easier for users to understand the nature and source of each event.
     *
     * @param event The event data to enrich
     * @return A JsonObject containing enriched event type and name information
     */
    private JsonObject enrich(JsonObject event)
    {
        var result = new JsonObject();

        try
        {
            // Only process events that have context information
            if (event.getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = event.getJsonObject(EVENT_CONTEXT);

                // Generate appropriate display information based on event type
                switch (event.getString(EVENT_TYPE))
                {
                    // Discovery events
                    case EVENT_DISCOVERY -> result.put(EVENT_TYPE, context.containsKey(AIOpsObject.OBJECT_TARGET)
                            ? context.getString(AIOpsObject.OBJECT_TARGET) + ": Object Discovery"
                            : context.getString(Discovery.DISCOVERY_NAME) + ": Object Discovery").put(EVENT_NAME, "Discovery");

                    case EVENT_DISCOVERY_RUN ->
                            result.put(EVENT_TYPE, context.getString(Discovery.DISCOVERY_NAME) + ": Discovery Job").put(EVENT_NAME, "Discovery");

                    // Topology events
                    case EVENT_TOPOLOGY_RUN -> result.put(EVENT_TYPE, "Topology Run").put(EVENT_NAME, "Topology");

                    case EVENT_TOPOLOGY ->
                    {
                        var type = String.format("%s %s : Topology", NMSConstants.TopologyPluginType.valueOfName(context.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE)), context.getString(NMSConstants.OBJECT));

                        result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) != null ? context.getString(AIOpsObject.OBJECT_TARGET) + "-" + type : type).put(EVENT_NAME, "Topology");
                    }

                    // Monitoring events
                    case EVENT_METRIC_POLL ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(Metric.METRIC_NAME) + ": Monitor Poll").put(EVENT_NAME, "Monitor Poll");

                    case EVENT_REDISCOVER ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(NMSConstants.REDISCOVER_JOB) + ": Rediscover Job").put(EVENT_NAME, "Rediscover");

                    // Plugin engine events
                    case EVENT_PLUGIN_ENGINE ->
                    {
                        var eventName = "Plugin Engine";

                        result.put(EVENT_NAME, eventName);

                        switch (PluginEngineConstants.PluginEngineRequest.valueOfName(context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST)))
                        {
                            // Network metric plugin events
                            case NETWORK_METRIC ->
                                    result.put(EVENT_TYPE, context.getJsonObject(NMSConstants.SNMP_OID_GROUP) != null
                                            ? context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getJsonObject(NMSConstants.SNMP_OID_GROUP).getString(NMSConstants.SNMP_OID_GROUP_NAME) + String.format(": %s", eventName)
                                            : context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST) + String.format(": %s", eventName));

                            // Streaming plugin events
                            case STREAMING ->
                                    result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST) + "-" + String.format(": %s", eventName));

                            // Other plugin events
                            default ->
                            {
                                if (context.containsKey(ConfigConstants.CONFIG_OPERATION))
                                {
                                    // Configuration operation events
                                    result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + String.format(" : Config Operation - %s", context.getString(ConfigConstants.CONFIG_OPERATION)));
                                }
                                else
                                {
                                    // General plugin events
                                    result.put(EVENT_TYPE, context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE) != null && !context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST).equalsIgnoreCase(context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE))
                                            ? context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST) + "-" + context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE) + String.format(": %s", eventName)
                                            : context.getString(AIOpsObject.OBJECT_TARGET) + "-" + context.getString(PluginEngineConstants.PLUGIN_ENGINE_REQUEST) + String.format(": %s", eventName));
                                }
                            }
                        }
                    }

                    // Notification events
                    case EVENT_NOTIFICATION ->
                            result.put(EVENT_TYPE, context.getString(Notification.NOTIFICATION_TYPE) + ": Notification").put(EVENT_NAME, "Notification");

                    // Runbook events
                    case EVENT_RUNBOOK ->
                            result.put(EVENT_TYPE, context.getString(RunbookPlugin.RUNBOOK_PLUGIN_TYPE) + ": Runbook").put(EVENT_NAME, "Runbook");

                    // Object provision events
                    case EVENT_OBJECT_PROVISION ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": Object Provision").put(EVENT_NAME, "Object Provision");

                    // UI action test events
                    case UI_ACTION_OID_GROUP_TEST ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": SNMP OID Group Test").put(EVENT_NAME, "SNMP OID Group Test");

                    case UI_ACTION_CONFIGURATION_TEST ->
                            result.put(EVENT_TYPE, context.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST) + ": LDAP Test").put(EVENT_NAME, "LDAP");

                    case UI_ACTION_LDAP_SYNC ->
                            result.put(EVENT_TYPE, context.getString(LDAPServer.LDAP_SERVER_PRIMARY_HOST) + ": LDAP Sync").put(EVENT_NAME, "LDAP");

                    case UI_ACTION_MAIL_SERVER_CONFIGURATION_TEST ->
                            result.put(EVENT_TYPE, context.getString(MailServerConfiguration.MAIL_SERVER_HOST) + ": Mail Server Test").put(EVENT_NAME, "Mail Server Test");

                    case UI_ACTION_SMS_GATEWAY_CONFIGURATION_TEST ->
                            result.put(EVENT_TYPE, context.getString(SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL) + ": SMS Gateway Test").put(EVENT_NAME, "SMS Gateway Test");

                    case UI_ACTION_CREDENTIAL_PROFILE_TEST ->
                            result.put(EVENT_TYPE, context.getString(TARGET) + ": Credential Test").put(EVENT_NAME, "Credential Test");

                    case UI_ACTION_METRIC_PLUGIN_TEST ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": Metric Plugin Test").put(EVENT_NAME, "Metric Plugin Test");

                    case UI_ACTION_TOPOLOGY_PLUGIN_TEST ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": Topology Plugin Test").put(EVENT_NAME, "Topology Plugin Test");

                    case UI_ACTION_RUNBOOK_PLUGIN_TEST ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": Runbook Plugin Test").put(EVENT_NAME, "Runbook Plugin Test");

                    case UI_ACTION_PROXY_SERVER_TEST ->
                            result.put(EVENT_TYPE, context.getString(ProxyServer.PROXY_SERVER_HOST) + ":" + context.getInteger(ProxyServer.PROXY_SERVER_PORT) + ": Proxy Server Test").put(EVENT_NAME, "Proxy Server Test");

                    case UI_ACTION_DNS_SERVER_CONFIGURATION_TEST ->
                            result.put(EVENT_TYPE, context.getString(TARGET) + ": DNS Server Test").put(EVENT_NAME, "DNS Server Test");

                    // Agent-related events
                    case EVENT_AGENT_DOWNLOAD_LOG ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Log").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_CONFIGURATION_CHANGE ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Config Update").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_UPGRADE ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Upgrade").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_START ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Start").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_STOP ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Stop").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_RESTART ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getItemByAgentId(context.getLong(ID)).getString(AIOpsObject.OBJECT_NAME) + ": Agent Restart").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    case EVENT_AGENT_DELETE ->
                            result.put(EVENT_TYPE, ObjectConfigStore.getStore().getAgentObjectName(context.getLong(ID)) + ": Agent Delete").put(EVENT_NAME, APIConstants.Entity.AGENT.getName());

                    // Visualization events
                    case EVENT_VISUALIZATION ->
                            result.put(EVENT_TYPE, EVENT_VISUALIZATION).put(EVENT_NAME, APIConstants.Entity.WIDGET.getName());

                    // Configuration events
                    case EVENT_CONFIG_CREATE ->
                            result.put(EVENT_TYPE, context.getString(AIOpsObject.OBJECT_TARGET) + ": Configuration Create").put(EVENT_NAME, "Configuration Create");

                    // Default case for any other event types
                    default -> result.put(EVENT_TYPE, event.getString(EVENT_TYPE));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Updates column mappings for metric data based on the provided tokens.
     * <p>
     * This method processes column mapping information received from the column mapper
     * and updates the local cache of column mappings. These mappings are used to interpret
     * metric data correctly when streaming it to clients.
     * <p>
     * The method handles various types of mappings including plugin IDs, data categories,
     * correlation status, and instance information.
     *
     * @param tokens An array of tokens containing column mapping information
     */
    private void update(String[] tokens)
    {
        try
        {
            // Get the column name from the tokens
            var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

            // Create a new mapping entry if one doesn't exist for this column
            if (!columns.containsKey(column))
            {
                columns.put(column, new JsonObject());
            }

            // Get the mapper object for this column
            var mapper = columns.getJsonObject(column);

            // Get or create the plugin IDs array for this column
            var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);
            if (plugins == null)
            {
                plugins = new JsonArray(new ArrayList<>(1));
            }

            // Add the plugin ID to the array if it's not already there
            if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
            {
                mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
            }

            // Get or create the data categories array for this column
            var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);
            if (categories == null)
            {
                categories = new JsonArray(new ArrayList<>(1));
            }

            // Add the data category to the array if it's not already there
            if (!categories.contains(CommonUtil.getInteger(tokens[0])))
            {
                mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
            }

            // Set correlation or status information based on plugin type
            if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))
            {
                // For correlated metrics, set the correlation flag
                mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);
            }
            else
            {
                // For regular metrics, set the status
                mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);
            }

            // If the column name contains an instance separator, extract and store the instance name
            if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
            {
                mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);
            }

            // For correlated metrics that are marked as correlated, set the group information
            if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) &&
                    mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))
            {
                mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

}
