/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashSet;
import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.DISCOVERY_USER_NOTIFICATION_MESSAGE;
import static com.mindarray.InfoMessageConstants.METRIC_POLLING_USER_NOTIFICATION_MESSAGE;
import static com.mindarray.api.Discovery.*;
import static com.mindarray.datastore.DatastoreConstants.DATASTORE_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;

public class UserNotificationUtil extends AbstractVerticle
{
    public static final String USER_NOTIFICATION_MESSAGE = "user.notification.message";
    public static final String USER_NOTIFICATION_TYPE = "user.notification.type";
    public static final String USER_NOTIFICATION_SEVERITY = "user.notification.severity";
    public static final String USER_NOTIFICATION_TYPE_SYSTEM = "system";
    private static final Logger LOGGER = new Logger(UserNotificationUtil.class, GlobalConstants.MOTADATA_UTIL, "User Notification Util");
    private static final String USER_NOTIFICATION_ID = "user.notification.id";
    private final JsonObject userNotifications = new JsonObject();
    private final StringBuilder builder = new StringBuilder(0);
    private boolean timerHandler = false;
    private Set<String> mappers;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
            {
                promise.fail("failed to start user notification engine, reason: invalid boot sequence...");
            }
            else
            {
                mappers = new HashSet<>();

                UserConfigStore.getStore().flatMap().forEach((key, value) -> userNotifications.put(CommonUtil.getString(key), 0));

                //as we will have to dynamically add user created into map so that he can receive notification count for add user
                //as user deleted we will not be needing its count so removing that particular user from map
                vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
                {
                    var event = message.body();

                    try
                    {
                        if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.ADD_USER.name()))
                        {
                            userNotifications.put(CommonUtil.getString(event.getJsonObject(EVENT_CONTEXT).getLong(ID)), 0);
                        }
                        else if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.DELETE_USER.name()))
                        {
                            userNotifications.remove(CommonUtil.getString(event.getJsonObject(EVENT_CONTEXT).getLong(ID)));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }).exceptionHandler(LOGGER::error);

                vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER_NOTIFICATION, message -> process(message.body())
                ).exceptionHandler(LOGGER::error);

                vertx.eventBus().<JsonObject>localConsumer(EVENT_USER_NOTIFICATION_COUNT, message -> message.reply(userNotifications.getLong(message.body().getString(ID)))
                ).exceptionHandler(LOGGER::error);

                vertx.eventBus().<JsonObject>localConsumer(EVENT_USER_NOTIFICATION_CLEAR, message ->
                {

                    if (userNotifications.containsKey(message.body().getString(User.USER_ID)))
                    {
                        userNotifications.put(message.body().getString(User.USER_ID), 0);

                        message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESULT, 0).put(User.USER_ID, message.body().getString(User.USER_ID)));
                    }
                    else
                    {
                        message.reply(new JsonObject().put(STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.USER_NOT_FOUND));
                    }

                });
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    //event received for notification purpose will be checked if qualified then its message will be taken and its dumped into datastore
    private void process(JsonObject event)
    {
        try
        {
            var message = switch (event.getString(EVENT_TYPE) != null ? event.getString(EVENT_TYPE) : event.getString(APIConstants.ENTITY_NAME))
            {
                case EventBusConstants.EVENT_DISCOVERY ->
                        event.containsKey(NMSConstants.REDISCOVER_JOB) && event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.APP.getName()) //for application discovery notification
                                ? event.getString(MESSAGE)
                                : String.format(DISCOVERY_USER_NOTIFICATION_MESSAGE, DiscoveryConfigStore.getStore().getItem(event.getLong(ID)).getString(Discovery.DISCOVERY_NAME), event.getValue(DISCOVERY_TOTAL_OBJECTS, 0), event.getValue(DISCOVERY_DISCOVERED_OBJECTS, 0), event.getValue(DISCOVERY_FAILED_OBJECTS, 0), event.getValue(DISCOVERY_PROGRESS, 0));

                case EVENT_METRIC_POLL ->
                        String.format(METRIC_POLLING_USER_NOTIFICATION_MESSAGE, event.getString(AIOpsObject.OBJECT_NAME), event.getString(Metric.METRIC_NAME), event.getString(MESSAGE));

                default -> event.getString(MESSAGE);
            };

            if (message != null)
            {
                //as notification received need to increase its count in all users and publish its event to UI
                //for showcase purpose of notification count
                userNotifications.getMap().replaceAll((k, v) -> CommonUtil.getInteger(v) + 1);

                if (!timerHandler)
                {
                    timerHandler = true;

                    vertx.setTimer(2000, timer ->
                    {
                        EventBusConstants.publish(UI_NOTIFICATION_USER_NOTIFICATION, userNotifications);

                        timerHandler = false;

                        vertx.cancelTimer(timer);
                    });
                }

                DatastoreConstants.write(new JsonObject()
                        .put(PLUGIN_ID, DatastoreConstants.PluginId.NOTIFICATION_EVENT.getName())
                        .put(DATASTORE_TYPE, DatastoreConstants.DatastoreType.NOTIFICATION.ordinal())
                        .put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                        .put(EVENT_SOURCE, SYSTEM_REMOTE_ADDRESS)
                        .put(USER_NOTIFICATION_TYPE, event.getString(USER_NOTIFICATION_TYPE) != null ? event.getString(USER_NOTIFICATION_TYPE) : USER_NOTIFICATION_TYPE_SYSTEM)
                        .put(USER_NOTIFICATION_SEVERITY, event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED) ? INFO : ERROR).put(USER_NOTIFICATION_MESSAGE, message).put(USER_NOTIFICATION_ID, CommonUtil.newEventId()), VisualizationConstants.VisualizationDataSource.USER_NOTIFICATION.getName(), mappers, builder);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
