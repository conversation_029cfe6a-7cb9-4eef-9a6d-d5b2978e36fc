[{"id": 10000000000021, "_type": "0", "visualization.name": "Tomcat Connections", "visualization.description": "Apache Tomcat Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.errors"}, {"type": "metric", "data.point": "tomcat.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "tomcat.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "Errors", "value": "tomcat.errors.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "tomcat.connections.last"}]}}}}, {"id": 10000000000022, "_type": "0", "visualization.name": "Session", "visualization.description": "Tomcat Session", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.expired.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.rejected.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.expired.sessions"}, {"type": "metric", "data.point": "tomcat.active.sessions"}, {"type": "metric", "data.point": "tomcat.rejected.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Session", "style": {"font.size": "medium"}, "data.points": [{"label": "Expired Sessions", "value": "tomcat.expired.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "tomcat.active.sessions.last"}, {"label": "Rejected", "value": "tomcat.rejected.sessions.last"}]}}}}, {"id": 10000000000023, "_type": "0", "visualization.name": "Memory", "visualization.description": "Tomcat Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.heap.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.heap.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.non.heap.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.heap.memory.used.percent"}, {"type": "metric", "data.point": "tomcat.non.heap.memory.used.bytes"}, {"type": "metric", "data.point": "tomcat.heap.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "tomcat.heap.memory.used.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Heap Memory Used", "value": "tomcat.heap.memory.used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "tomcat.heap.memory.used.bytes.last"}, {"label": "Non Heap", "value": "tomcat.non.heap.memory.used.bytes.last"}]}}}}, {"id": 10000000000024, "_type": "0", "visualization.name": "Thread Pool", "visualization.description": "Tomcat Thread Pool", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.thread.pool~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.thread.pool~connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.thread.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.thread.pool~threads"}, {"type": "metric", "data.point": "tomcat.thread.pool~used.percent"}, {"type": "metric", "data.point": "tomcat.thread.pool~connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "tomcat.thread.pool~threads", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Thread Pool", "style": {"font.size": "medium"}, "data.points": [{"label": "Thread Pool Used", "value": "tomcat.thread.pool~used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "tomcat.thread.pool~threads.last"}, {"label": "Connections", "value": "tomcat.thread.pool~connections.last"}]}}}}, {"id": 10000000000025, "_type": "0", "visualization.name": "Servlet Details", "visualization.description": "Tomcat Servlet Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.jsp.unloads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jsp.accesses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jsp.reloads", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.jsp.unloads"}, {"type": "metric", "data.point": "tomcat.jsp.accesses"}, {"type": "metric", "data.point": "tomcat.jsp.reloads"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "tomcat.jsp.accesses", "icon": {"name": "java", "placement": "prefix"}}, "header": {"title": "Servlet Details", "style": {"font.size": "medium"}, "data.points": [{"label": "jsp.unloads", "value": "tomcat.jsp.unloads.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Accesses", "value": "tomcat.jsp.accesses.last"}, {"label": "Reloads", "value": "tomcat.jsp.reloads.last"}]}}}}, {"id": 10000000000026, "_type": "0", "visualization.name": "Request Details", "visualization.description": "Tomcat Request Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "tomcat.requests.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "tomcat.requests.per.sec", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Request Details", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "tomcat.requests.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000027, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000028, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"id": 10000000000029, "_type": "0", "visualization.name": "Memory Utilization", "visualization.description": "Tomcat Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.heap.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.heap.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.non.heap.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000030, "_type": "0", "visualization.name": "Session Details", "visualization.description": "Tomcat Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.rejected.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.expired.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.created.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000031, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "<PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.cache.hits", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.cache.accesses", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000032, "_type": "0", "visualization.name": "Request Details", "visualization.description": "Tomcat Request Details Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000033, "_type": "0", "visualization.name": "Thread Pool Details", "visualization.description": "Tomcat Thread Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.thread.pool~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.thread.pool~max.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.thread.pool~busy.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.thread.pool", "title": "Pool", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.thread.pool~threads.last", "title": "Pool Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.thread.pool~max.threads.last", "title": "<PERSON>eads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.thread.pool~busy.threads.last", "title": "Busy Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000034, "_type": "0", "visualization.name": "Threads", "visualization.description": "<PERSON><PERSON> Threads", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000035, "_type": "0", "visualization.name": "JDBC Pool Details", "visualization.description": "Tomcat JDBC Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.jdbc.pool~active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jdbc.pool~idle.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jdbc.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.jdbc.pool", "title": "Pool Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.jdbc.pool~idle.connections.last", "title": "Idle Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.jdbc.pool~active.connections.last", "title": "Active Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "tomcat.jdbc.pool~used.percent.last", "title": "JDBC Pool Used Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000036, "_type": "0", "visualization.name": "Thread Pool Utilization", "visualization.description": "Tomcat Thread Pool Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.overall.thread.pool.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000037, "_type": "0", "visualization.name": "Servlet Details", "visualization.description": "Tomcat Servlet Details Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.jsp.accesses", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jsp.reloads", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.jsp.unloads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000038, "_type": "0", "visualization.name": "Network Traffic Throughput", "visualization.description": "Tomcat Network Traffic Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tomcat.sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "tomcat.received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]