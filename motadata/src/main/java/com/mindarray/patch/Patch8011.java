/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.db.DBConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.RunbookPluginConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.DBConstants.FIELD_NAME;


public class Patch8011 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8011.class, MOTADATA_PATCH, "Patch 8.0.11");

    private static final String VERSION = "8.0.11";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    /**
     * The patch updates the runbook configuration in the database by setting the
     * runbook category for each  if it is not already set.
     * This patch will run for the user created runbook.
     */
    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var items = RunbookPluginConfigStore.getStore().getItems();

            if (items != null && !items.isEmpty())
            {

                for (var index = 0; index < items.size(); index++)
                {

                    var item = items.getJsonObject(index);

                    var valid = false;

                    if (!item.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY) || item.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY).isEmpty())
                    {
                        var runbookCategory = item.containsKey("runbook.plugin.log.collection") && item.getString("runbook.plugin.log.collection").equalsIgnoreCase(YES) ?
                                Runbook.RunbookCategory.LOG_COLLECTION : Runbook.RunbookCategory.OTHER;

                        item.put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, runbookCategory.getName());

                        valid = true;
                    }

                    if (valid)
                    {
                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        Bootstrap.configDBService().update(DBConstants.TBL_RUNBOOK_PLUGIN,
                                new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                item, DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                {
                                    if (result.succeeded())
                                    {
                                        RunbookPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                LOGGER.info(String.format("Successfully updated runbook plugin object %s", item.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)));

                                                future.complete();
                                            }
                                            else
                                            {
                                                LOGGER.warn(String.format("Unable to update runbook plugin config store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                                future.fail(asyncResult.cause().getMessage());
                                            }
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("Unable to update runbook plugin config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                        future.fail(result.cause().getMessage());
                                    }
                                });
                    }
                }

                Future.join(futures).onComplete(result -> promise.complete());
            }
            else
            {
                LOGGER.warn("Runbook plugin item not found");

                promise.fail("Runbook plugin item not found");
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
