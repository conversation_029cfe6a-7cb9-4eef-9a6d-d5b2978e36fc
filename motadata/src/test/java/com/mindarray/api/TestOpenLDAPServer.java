/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.LDAPSyncJob;
import com.mindarray.store.LDAPServerConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.LDAPUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.github.artsok.ParameterizedRepeatedIfExceptionsTest;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.LDAP_SERVER_SYNC_STARTED;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.LDAPServer.*;
import static com.mindarray.api.User.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(160 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestOpenLDAPServer
{

    private static final JsonObject Context = new JsonObject();
    private static final JsonObject LDAP_CONTEXT = new JsonObject()
            .put(LDAP_SERVER_PRIMARY_HOST, "************")
            .put(LDAP_SERVER_FQDN, "rhel.local")
            .put(LDAP_SERVER_PORT, 389)
            .put(LDAP_SERVER_USERNAME, "admin")
            .put(LDAP_SERVER_PASSWORD, "Mind@123")
            .put(LDAP_AUTHENTICATION, NO)
            .put(LDAP_AUTO_SYNC, NO)
            .put(LDAP_SERVER_TYPE, LDAP_SERVER_TYPE_OPEN)
            .put(LDAP_SERVER_PROTOCOL, LDAP_SERVER_PROTOCOL_PLAIN);

    private static final Logger LOGGER = new Logger(TestOpenLDAPServer.class, MOTADATA_API, "Test Open LDAP Server");
    private static MessageConsumer<JsonObject> messageConsumer;
    private static int ldapUsers = 0;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        LOGGER.info(LicenseUtil.getLicenseDetails());

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    public static void assertOpenLDAPServerConfigTestResult(VertxTestContext testContext, String eventState, String errorCode)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (eventContext.getString(STATUS) != null && eventContext.getString(MESSAGE) != null)
                {
                    messageConsumer.unregister();

                    assertEquals(errorCode, eventContext.getString(ERROR_CODE));

                    assertEquals(eventState, eventContext.getString(STATUS));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    @BeforeEach
    void clean(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(0)
    void testCreateOpenLDAPServerConfiguration(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT, LDAP_CONTEXT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(LDAPServerConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, "LDAP Server"), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetOpenLDAPServerConfiguration(VertxTestContext testContext)
    {
        TestAPIUtil.get(LDAP_SERVER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, LDAPServerConfigStore.getStore(), null);

            Context.put("ldap.server", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testOpenLDAPServerBeforeSync(VertxTestContext testContext)
    {
        ldapUsers = 0;

        TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_LDAP))
                {
                    ldapUsers = ldapUsers + 1;
                }
            }

            assertEquals(0, ldapUsers);

            testContext.completeNow();
        })));
    }

    @ParameterizedRepeatedIfExceptionsTest(suspend = 3000L)
    @ValueSource(ints = {8, 12, 24, 48})
    @Order(3)
    void testUpdateOpenLDAPServerConfigurationAutoSyncOn(int interval, VertxTestContext testContext, TestInfo testInfo)
    {
        var context = LDAP_CONTEXT.copy().put(LDAP_AUTO_SYNC, YES).put(LDAP_SYNC_JOB_SCHEDULE_INTERVAL, interval);

        TestAPIUtil.put(LDAP_SERVER_API_ENDPOINT + "/" + TestOpenLDAPServer.Context.getLong("ldap.server"), context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertUpdateEntityTestResult(LDAPServerConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_UPDATED, "LDAP Server"), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 1000L)
    @Order(4)
    void testOpenLDAPServerSync(VertxTestContext testContext, TestInfo testInfo)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT + "/sync/" + Context.getLong("ldap.server"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                    assertEquals(LDAP_SERVER_SYNC_STARTED, response.bodyAsJsonObject().getString(MESSAGE));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testOpenLDAPSyncJob(VertxTestContext testContext)
    {
        try
        {
            new LDAPSyncJob().execute(null);

            TimeUnit.SECONDS.sleep(1); //TO allow the execute blocking thread running the sync operation to complete
        }
        catch (Exception exception)
        {
            testContext.failNow(exception);
        }

        TestAPIUtil.get(TestAPIConstants.USER_API_ENDPOINT, testContext.succeeding(result ->
        {
            List<Object> activeLDAPUsers = result.bodyAsJsonObject().getJsonArray(GlobalConstants.RESULT).stream()
                    .filter(user -> User.USER_TYPE_LDAP.equalsIgnoreCase((JsonObject.mapFrom(user)).getString(User.USER_TYPE))).toList();

            testContext.verify(() -> assertFalse(activeLDAPUsers.isEmpty()));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testOpenLDAPServerSyncForExistingUser(VertxTestContext testContext, TestInfo testInfo)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT + "/sync/" + Context.getLong("ldap.server"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                    assertEquals(LDAP_SERVER_SYNC_STARTED, response.bodyAsJsonObject().getString(MESSAGE));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testOpenLDAPServerAfterSync(VertxTestContext testContext)
    {
        ldapUsers = 0;

        TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_LDAP))
                {
                    ldapUsers = ldapUsers + 1;
                }

                assertEquals(YES, item.getString(USER_STATUS));
            }

            assertTrue(ldapUsers >= 1);

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testOpenLDAPServerSync2(VertxTestContext testContext)
    {

        assertOpenLDAPServerConfigTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        //Sending to server event handler which will forward  it to handler registered on event type

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy()).put(SESSION_ID, TestUtil.getSessionId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testOpenLDAPRemovePreviousUserServerSync(VertxTestContext testContext) throws Exception
    {
        var ldapUtil = new LDAPUtil();

        var method = ldapUtil.getClass().getDeclaredMethod("updateLDAPUsers", JsonArray.class, String.class, Promise.class, String.class, List.class, JsonObject.class);

        var users = UserConfigStore.getStore().getItemsByValue(USER_TYPE, USER_TYPE_LDAP);

        var user = users.remove(0);

        var promise = Promise.<JsonObject>promise();

        method.setAccessible(true);

        method.invoke(ldapUtil, users, DEFAULT_USER, promise, SYSTEM_REMOTE_ADDRESS, new ArrayList<>(), LDAPServerConfigStore.getStore().getItem(Context.getLong("ldap.server")));

        TimeUnit.SECONDS.sleep(1);

        assertNull(UserConfigStore.getStore().getItem(JsonObject.mapFrom(user).getLong(ID)));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testOpenLDAPServerConfigurationInvalidFQDN(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy().put(LDAP_SERVER_FQDN, "mind@12")).put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testOpenLDAPServerConfigurationInvalidUserName(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy().put(LDAP_SERVER_USERNAME, "mind@12")).put(SESSION_ID, TestUtil.getSessionId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testOpenLDAPServerConfigurationInvalidPassword(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy().put(LDAP_SERVER_PASSWORD, "mind@12")).put(SESSION_ID, TestUtil.getSessionId()));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testOpenLDAPServerConfigurationInvalidPort(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy().put(LDAP_SERVER_PORT, 4321)).put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testOpenLDAPServerConfigurationInvalidIP(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_LDAP_CONFIG_TEST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, LDAP_CONTEXT.copy().put(LDAP_SERVER_PRIMARY_HOST, "************")).put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testAuditMessagesOpenLDAPServer(VertxTestContext testContext)
    {

        TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put("users", new JsonArray().add(DEFAULT_USER)),
                testContext.succeeding(httpResponse ->
                        testContext.verify(testContext::completeNow)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testOpenLDAPServerConfigurationEmptyHost(VertxTestContext testContext)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_FAIL, ErrorCodes.ERROR_CODE_BAD_REQUEST);

        var context = LDAP_CONTEXT.copy().put(LDAP_SERVER_FQDN, "mind@12");

        context.remove(LDAP_SERVER_PRIMARY_HOST);

        Bootstrap.vertx().eventBus().send(EVENT_SERVER, new JsonObject().put(EVENT_TYPE, UI_ACTION_CONFIGURATION_TEST).put(SESSION_ID, TestUtil.getSessionId())
                .put(EVENT_CONTEXT, context).put(SESSION_ID, TestUtil.getSessionId()));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testOpenLDAPSServerSync(VertxTestContext testContext, TestInfo testInfo)
    {
        assertOpenLDAPServerConfigTestResult(testContext, STATUS_SUCCEED, ErrorCodes.ERROR_CODE_SUCCESS);

        TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT + "/sync/" + Context.getLong("ldap.server"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                    assertEquals(LDAP_SERVER_SYNC_STARTED, response.bodyAsJsonObject().getString(MESSAGE));

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testOpenLDAPSServerAfterSync(VertxTestContext testContext)
    {
        ldapUsers = 0;

        TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

            for (var index = 0; index < items.size(); index++)
            {

                var item = items.getJsonObject(index);

                if (item.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_LDAP))
                {
                    ldapUsers = ldapUsers + 1;
                }
                assertEquals(YES, item.getString(USER_STATUS));
            }

            assertTrue(ldapUsers >= 2);

            testContext.completeNow();

        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testAddDuplicateOpenLDAPServer(VertxTestContext testContext)
    {
        var item = LDAP_CONTEXT.copy();


        TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT, item, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    var result = response.bodyAsJsonObject();

                    assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(APIConstants.RESPONSE_CODE));

                    assertEquals(String.format(ErrorMessageConstants.LDAP_SERVER_ALREADY_EXISTS, item.getString(LDAP_SERVER_PRIMARY_HOST, EMPTY_VALUE)), result.getString(MESSAGE));

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testDeleteOpenLDAPServer(VertxTestContext testContext)
    {
        TestAPIUtil.delete(LDAP_SERVER_API_ENDPOINT + "/" + Context.getLong("ldap.server"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(LDAPServerConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_DELETED, "LDAP Server"));

            var retries = new AtomicInteger();

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                if (UserConfigStore.getStore().getItemsByValue(USER_TYPE, USER_TYPE_LDAP).isEmpty())
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.completeNow();
                }
                else if (retries.get() > 5)
                {
                    TestUtil.vertx().cancelTimer(timer);

                    testContext.failNow("Expected 0 LDAP users, but found");
                }
                else
                {
                    retries.incrementAndGet();
                }
            });
        })));
    }
}
