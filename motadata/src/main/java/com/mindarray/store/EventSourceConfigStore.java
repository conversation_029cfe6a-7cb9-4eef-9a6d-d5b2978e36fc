/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class EventSourceConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(EventSourceConfigStore.class, GlobalConstants.MOTADATA_STORE, "Event Source Config Store");

    private static final EventSourceConfigStore STORE = new EventSourceConfigStore();
    private final Map<String, JsonObject> itemsBySource = new ConcurrentHashMap<>();

    protected EventSourceConfigStore()
    {
        super(DBConstants.TBL_EVENT_SOURCE, LOGGER, true);
    }

    public static EventSourceConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_EVENT_SOURCE,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            items.clear();

                            itemsBySource.clear();

                            if (result.result() != null && !result.result().isEmpty())
                            {
                                for (var index = 0; index < result.result().size(); index++)
                                {
                                    var item = result.result().getJsonObject(index);

                                    items.put(item.getLong(GlobalConstants.ID), item);

                                    if (item.getString(EventBusConstants.EVENT_SOURCE) != null)
                                    {
                                        LOGGER.info("adding items::" + item);

                                        itemsBySource.put(item.getString(EventBusConstants.EVENT_SOURCE), item);
                                    }
                                }
                            }

                            promise.complete();

                            LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                            promise.fail(result.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result ->
        {

            if (result.succeeded())
            {
                try
                {
                    var item = items.get(id).copy();

                    if (item.getString(EventBusConstants.EVENT_SOURCE) != null)
                    {
                        itemsBySource.put(item.getString(EventBusConstants.EVENT_SOURCE), item);
                    }

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        });
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result ->
        {
            var item = items.get(id).copy();

            if (item.getString(EventBusConstants.EVENT_SOURCE) != null)
            {
                itemsBySource.put(item.getString(EventBusConstants.EVENT_SOURCE), item);
            }
        });
    }

    public JsonObject getItemByHost(String eventSource, boolean copy)
    {
        return itemsBySource.containsKey(eventSource) ? (copy ? itemsBySource.get(eventSource).copy() : itemsBySource.get(eventSource)) : null;
    }

    public JsonArray getItemsByMultiValueFields(String field1, Object value, String flatField, String field2, int pluginId)
    {

        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.getJsonArray(field1) != null && item.getJsonArray(field2) != null && !item.getJsonArray(field2).isEmpty() && item.getJsonArray(field1).contains(value) && item.getJsonArray(GlobalConstants.PLUGIN_ID).contains(pluginId))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray getItemsByMultiValueFieldAny(String field, JsonArray values, String flatField)
    {

        return new JsonArray(items.values().stream()
                .filter(item -> item.getJsonArray(field) != null && !item.getJsonArray(field).isEmpty() && item.getJsonArray(field).stream().anyMatch(values::contains) && item.getJsonArray(LogEngineConstants.EVENT_CATEGORY) != null && !item.getJsonArray(LogEngineConstants.EVENT_CATEGORY).isEmpty())
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }

    public JsonArray flatItemsByMultiValueFields(String field1, Object value, String flatField, String field2, JsonArray values)
    {

        return new JsonArray(items.values().parallelStream()
                .filter(item -> item.getJsonArray(field1) != null && item.getJsonArray(field2) != null && !item.getJsonArray(field2).isEmpty() && item.getJsonArray(field1).contains(value) && item.getJsonArray(field2).stream().anyMatch(values::contains))
                .map(item -> item.getValue(flatField))
                .collect(Collectors.toList()));

    }
}
