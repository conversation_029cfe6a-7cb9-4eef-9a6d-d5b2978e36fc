/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.LogForwarderConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;


public class LogForwarder extends AbstractAPI
{

    public static final String LOG_FORWARDER_TYPE = "log.forwarder.type";
    public static final String LOG_FORWARDER_NAME = "log.forwarder.name";
    public static final String LOG_FORWARDER_DESTINATION_IP = "log.forwarder.destination.ip";
    public static final String LOG_FORWARDER_DESTINATION_PORT = "log.forwarder.destination.port";
    public static final String LOG_FORWARDER_CONTEXT = "log.forwarder.context";
    public static final String LOG_FORWARDER_LOG_TYPE = "log.forwarder.log.type";
    public static final String LOG_FORWARDER_TYPE_TCP = "tcp";
    public static final String LOG_FORWARDER_TYPE_UDP = "udp";
    public static final String LOG_FORWARDER_STATE = "log.forwarder.state";
    public static final String LOG_FORWARDER_LOG_TYPE_JSON = "json";
    public static final String LOG_FORWARDER_LOG_TYPE_RAW = "raw";
    private static final Logger LOGGER = new Logger(LogForwarder.class, GlobalConstants.MOTADATA_API, "Log Forwarder API");

    public LogForwarder()
    {
        super("log-forwarders", LogForwarderConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router);

        router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update log forwarder state API
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_LOG_FORWARDER.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_LOG_FORWARDER.name()));

        return Future.succeededFuture();
    }

    private void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(LOG_FORWARDER_STATE);

        var Id = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(DBConstants.TBL_LOG_FORWARDER, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, Id),
                    new JsonObject().put(LOG_FORWARDER_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            LogForwarderConfigStore.getStore().updateItem(Id).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, Id).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_LOG_FORWARDER.name()));
                                }
                            });

                            this.send(routingContext, new JsonObject().put(MESSAGE, LogForwarderConfigStore.getStore().getItem(Id).getString(LOG_FORWARDER_NAME) + (state.equalsIgnoreCase(YES) ? " Enabled" : " Disabled") + " successfully...").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, Id));
                        }
                        else
                        {

                            this.send(routingContext, new JsonObject().put(MESSAGE, "Failed to " + (state.equalsIgnoreCase(YES) ? "Enable" : "Disable") + " log forwarder profile " + LogForwarderConfigStore.getStore().getItem(Id).getString(LOG_FORWARDER_NAME)).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, Id));
                        }
                    });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
