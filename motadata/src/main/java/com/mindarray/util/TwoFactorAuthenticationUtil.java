/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     4-Mar-2025      Bharat      MOTADATA-4740: Two factor authentication 2FA | Initial Version
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.ext.auth.User;
import io.vertx.ext.auth.otp.Authenticator;
import io.vertx.ext.auth.otp.OtpCredentials;
import io.vertx.ext.auth.otp.OtpKey;
import io.vertx.ext.auth.otp.OtpKeyGenerator;
import io.vertx.ext.auth.otp.totp.TotpAuth;

import java.security.SecureRandom;

public class TwoFactorAuthenticationUtil
{
    private static final Logger LOGGER = new Logger(TwoFactorAuthenticationUtil.class, GlobalConstants.MOTADATA_UTIL, "Two Factor Authentication Util");
    private static final OtpKeyGenerator keyGenerator = OtpKeyGenerator.create();
    private static final TotpAuth authProvider = TotpAuth.create().authenticatorUpdater(auth -> Future.succeededFuture());
    private static final String ISSUER = "MOTADATA";


    /**
     * Generates a QR code URI for the given username and secret key.
     * Simple String Manipulation no need for blocking operation
     */
    public static String generateQRCode(String username, OtpKey secretKey)
    {
        return TotpAuth.create().generateUri(secretKey, ISSUER, username);
    }

    /**
     * Generates a new authenticator key.
     * use cryptographic operations so it is a blocking operation
     */
    public static OtpKey generateAuthenticatorKey()
    {
        return keyGenerator.generate();
    }

    /**
     * Validate time based one time password using secrete key
     */
    public static Future<User> validateToken(String secretKey, String token)
    {
        Promise<User> promise = Promise.promise();

        Bootstrap.vertx().<User>executeBlocking(future ->
        {
            try
            {
                // Now set the authenticator fetcher and authenticate
                authProvider.authenticatorFetcher(identifier -> Future.succeededFuture(new Authenticator().setIdentifier(ISSUER).setKey(secretKey)))
                        .authenticate(new OtpCredentials(ISSUER, token)).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete(result.result());
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                future.fail(result.cause());
                            }
                        });

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception);
            }
        }, false, result ->
        {
            if (result.succeeded())
            {
                promise.complete(result.result());
            }
            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();

    }

    public static int generateOTP()
    {
        return 100000 + new SecureRandom().nextInt(900000);
    }
}