{"*************": {"_type": "1", "cli.enabled": "no", "credential.profile.name": "motadata - motadata", "credential.profile.protocol": "SSH", "errors": [{"error": "ssh: handshake failed: ssh: unable to authenticate, attempted methods [none password], no supported methods remain", "error.code": "MD003", "message": "Authentication Error: Invalid Credentials for *************:22"}], "event.id": 14511663658905, "event.timestamp": 1709041909, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": 14511663685009, "metric.category": "Server", "metric.credential.profile": 14511663685006, "metric.credential.profile.protocol": "SSH", "metric.discovery.method": "REMOTE", "metric.name": "Linux Network Interface", "metric.object": 14511663685008, "metric.plugin": "linuxnetworkinterface", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Linux", "object.business.hour.profile": 10000000000001, "object.category": "Server", "object.creation.time": "2024/02/27 18:01:47", "object.creation.time.seconds": 1709037107, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [10000000000019, 10000000000017], "object.host": "motadata12145", "object.id": 1, "object.ip": "*************", "object.name": "motadata12145", "object.state": "ENABLE", "object.target": "*************", "object.type": "Linux", "object.user.tags": [], "password": "motadata", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 94, "port": 22, "remote.address": "0:0:0:0:0:0:0:1", "remote.event.processor.uuid": "asdf", "result": {}, "ss.bin.path": "/bin/ss", "status": "fail", "timeout": 60, "ui.event.uuid": "456a4aaa-595a-4cb5-86aa-776c98852625", "user.name": "admin", "username": "motadat", "error": "[ {\n  \"error\" : \"ssh: handshake failed: ssh: unable to authenticate, attempted methods [none password], no supported methods remain\",\n  \"error.code\" : \"MD003\",\n  \"message\" : \"Authentication Error: Invalid Credentials for *************:22\"\n} ]", "latency.ms": 30887.0, "error.code": "MD003", "message": "Metric polling failed. Possible reason: Invalid credentials"}}