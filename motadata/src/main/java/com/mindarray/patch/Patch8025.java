/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  02-Jul-2025		Ya<PERSON>wari     MOTADATA-6528 : added support for topology view filters hence added setFilterTargets method & parent child hierarchy fetch
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  24-Jun-2025     Yash <PERSON>     MOTADATA-6528 : Added patch 8.0.25 to migrate metric explorer configuration data
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Explorer;
import com.mindarray.db.DBConstants;
import com.mindarray.store.ExplorerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;

public class Patch8025 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8025.class, MOTADATA_PATCH, "Patch 8.0.25");

    private static final String VERSION = "8.0.25";

    /**
     * Returns the version number of this patch.
     *
     * @return The version string "8.0.25"
     */
    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.25");

        futures.add(executeMetricExplorerPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.25");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    /**
     * Migrates metric explorer configuration data by updating key names.
     * This method:
     * 1. Retrieves all records from the metric explorer configuration table
     * 2. For each record, renames keys by replacing "metric.explorer" prefix with "explorer"
     * 3. Handles nested context objects by updating their keys as well
     * 4. Saves the updated records to the explorer table
     * 5. Clears and reinitialize the ExplorerConfigStore to reflect changes
     *
     * @return A Future that completes when the migration is successful or fails with an exception
     */
    private Future<Void> executeMetricExplorerPatch()
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService().getAll("tbl_config_metric_explorer", result ->
        {
            if (result.succeeded())
            {
                var items = result.result();

                LOGGER.info(String.format("items : %s", items.encode()));

                var updatedItems = new JsonArray();

                for (var index = 0; index < items.size(); index++)
                {
                    try
                    {
                        var oldItem = items.getJsonObject(index);

                        var item = new JsonObject().put(Explorer.EXPLORER_TYPE, APIConstants.ExplorerType.METRIC.getName());

                        oldItem.getMap().keySet().forEach(key ->
                        {
                            try
                            {
                                if (key.startsWith("metric.explorer"))
                                {
                                    item.put(key.replace("metric.explorer", "explorer"), oldItem.getValue(key));
                                }
                                else
                                {
                                    item.put(key, oldItem.getValue(key));
                                }

                                if (key.equalsIgnoreCase("metric.explorer.context"))
                                {
                                    var oldContext = oldItem.getJsonObject("metric.explorer.context");

                                    var context = new JsonObject();

                                    oldContext.getMap().keySet().forEach(entry -> context.put(entry.replace("metric.explorer", "explorer"), oldContext.getValue(entry)));

                                    item.put(Explorer.EXPLORER_CONTEXT, context);
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });

                        updatedItems.add(item);
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                if (!updatedItems.isEmpty())
                {
                    Bootstrap.configDBService().saveAll(DBConstants.TBL_EXPLORER, updatedItems, SYSTEM_USER, MOTADATA_SYSTEM, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            ExplorerConfigStore.getStore().clear();

                            ExplorerConfigStore.getStore().initStore().onComplete(response ->
                            {
                                if (response.succeeded())
                                {
                                    LOGGER.info("successfully executed patch for metric explorer");

                                    promise.complete();
                                }
                                else
                                {
                                    LOGGER.error(response.cause());

                                    promise.fail(response.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            promise.fail(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    promise.complete();

                    LOGGER.info("no metric explorer items found to patch");
                }
            }
            else
            {
                LOGGER.error(new Exception());

                promise.fail(result.cause());
            }
        });

        return promise.future();
    }
}
