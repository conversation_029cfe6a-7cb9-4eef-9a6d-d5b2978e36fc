/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     25-Mar-2025     Arpit           MOTADATA-5549: VAPT, unsafe-inline execution removed
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.User;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ActiveUserCacheStore;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.UserConfigStore;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.auth.impl.UserImpl;
import io.vertx.ext.auth.oauth2.OAuth2Auth;
import io.vertx.ext.auth.oauth2.OAuth2FlowType;
import io.vertx.ext.auth.oauth2.OAuth2Options;
import io.vertx.ext.auth.oauth2.Oauth2Credentials;
import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_ROLE;
import static com.mindarray.db.DBConstants.FIELD_NAME;

public class OAuthUtil extends AbstractVerticle
{
    public static final String AUTH_METHOD_OAUTH = "XOAUTH2";
    public static final String HTML_CLOSE_SCRIPT = "<html><body><script src='/OAuthUtil.js'></script></body></html>";
    public static final String CLIENT_ID = "client.id";
    public static final String CLIENT_SECRET = "client.secret";
    public static final String TOKEN_URL = "token.url";
    public static final String AUTHENTICATION_URL = "authentication.url";
    public static final String TOKEN_TYPE = "token_type";
    public static final String REDIRECT_URL = "redirect.url";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String REFRESH_TOKEN = "refresh_token";
    public static final String CODE = "code";
    public static final int DEFAULT_TIMEOUT = 5000;
    public static final String AUTHENTICATION_TYPE = "authentication.type";
    public static final String GRANT_TYPE = "grant.type";
    static final Set<String> OAUTH_PROVIDER_ERROR_CODES = new HashSet<>(MotadataConfigUtil.getOAuthProviderErrors());
    private static final Logger LOGGER = new Logger(OAuthUtil.class, GlobalConstants.MOTADATA_UTIL, "OAuth Util");
    private final Map<String, JsonObject> contextsBySession = new HashMap<>();

    @Override
    public void start() throws Exception
    {

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OAUTH_CONTEXT_UPDATE, message ->
        {
            var context = message.body();

            contextsBySession.put(context.getString(APIConstants.SESSION_ID), context);

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OAUTH_CONTEXT_FETCH, message ->
                message.reply(contextsBySession.remove(message.body().getString(APIConstants.SESSION_ID)))).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OAUTH_CONTEXT_GENERATE, message ->
        {
            var sessionId = message.body().getString(APIConstants.SESSION_ID);

            var code = message.body().getString(CODE);

            var context = contextsBySession.get(sessionId);

            if (code != null && !code.trim().isEmpty() && context != null)
            {
                try
                {
                    var oauth2Options = new OAuth2Options()
                            .setClientId(context.getString(CLIENT_ID))
                            .setClientSecret(context.getString(CLIENT_SECRET))
                            .setTokenPath(context.getString(TOKEN_URL))
                            .setAuthorizationPath(context.getString(AUTHENTICATION_URL));

                    oauth2Options.getHttpClientOptions()
                            .setTcpFastOpen(true)
                            .setTcpNoDelay(true)
                            .setConnectTimeout(DEFAULT_TIMEOUT)
                            .setIdleTimeout(DEFAULT_TIMEOUT);

                    var oauth2 = OAuth2Auth.create(vertx, oauth2Options);

                    oauth2.authenticate(new Oauth2Credentials().setCode(code)
                                    .setRedirectUri(context.getString(REDIRECT_URL)).setFlow(OAuth2FlowType.AUTH_CODE)
                            , result ->
                            {
                                if (result.succeeded())
                                {
                                    if (result.result().principal().containsKey(OAuthUtil.REFRESH_TOKEN))
                                    {
                                        context.mergeIn(result.result().principal());

                                        message.reply(context);
                                    }
                                    else
                                    {
                                        message.fail(HttpStatus.SC_NOT_FOUND, context.put(MESSAGE, "Failed to receive the refresh token from the authentication provider. Please ensure that the provider is configured correctly and try again.").encode());
                                    }
                                }
                                else
                                {
                                    LOGGER.warn(String.format("generating access token failed. Reason: %s", result.cause()));

                                    message.fail(HttpStatus.SC_BAD_REQUEST, context.put(MESSAGE, String.format(ErrorMessageConstants.OAUTH_ACCESS_TOKEN_FETCH_ERROR, result.cause().getMessage())).encode());
                                }

                            });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, context.put(MESSAGE, String.format(ErrorMessageConstants.OAUTH_ACCESS_TOKEN_FETCH_ERROR, exception.getMessage())).encode());
                }
            }
            else
            {
                LOGGER.warn(String.format(ErrorMessageConstants.OAUTH_CONTEXT_NOT_FOUND_ERROR, sessionId));

                message.fail(HttpStatus.SC_NOT_FOUND, new JsonObject().put(MESSAGE, String.format(ErrorMessageConstants.OAUTH_CONTEXT_NOT_FOUND_ERROR, sessionId)).encode());
            }
        });


        /**
         * generate a new access token based on refresh token
         * <p>
         * Depending on the OAuth2 provider (e.g., Google, Microsoft), the returned access token can
         * be in different formats, such as a JWT, opaque or another token format.
         * </p>
         */
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OAUTH_TOKEN_GENERATE, message ->
        {
            var item = message.body();

            try
            {
                var context = item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                if (context.containsKey(OAuthUtil.REFRESH_TOKEN) && !context.getString(OAuthUtil.REFRESH_TOKEN).trim().isEmpty())
                {
                    var oauth2Options = new OAuth2Options()
                            .setClientId(context.getString(OAuthUtil.CLIENT_ID))
                            .setClientSecret(context.getString(OAuthUtil.CLIENT_SECRET))
                            .setTokenPath(context.getString(OAuthUtil.TOKEN_URL))
                            .setAuthorizationPath(context.getString(OAuthUtil.AUTHENTICATION_URL));

                    oauth2Options.getHttpClientOptions()
                            .setTcpFastOpen(true)
                            .setTcpNoDelay(true)
                            .setConnectTimeout(OAuthUtil.DEFAULT_TIMEOUT)
                            .setIdleTimeout(OAuthUtil.DEFAULT_TIMEOUT);

                    var oauth2 = OAuth2Auth.create(Bootstrap.vertx(), oauth2Options);

                    var user = new UserImpl(new JsonObject(), new JsonObject());

                    user.principal()
                            .put(OAuthUtil.REFRESH_TOKEN, context.getString(OAuthUtil.REFRESH_TOKEN))
                            .put(OAuthUtil.TOKEN_TYPE, context.getString(OAuthUtil.TOKEN_TYPE));

                    oauth2.refresh(user)
                            .onComplete(response ->
                            {
                                if (response.succeeded())
                                {
                                    context.put(OAuthUtil.ACCESS_TOKEN, response.result().principal().getString(OAuthUtil.ACCESS_TOKEN));

                                    Bootstrap.configDBService().update(DBConstants.TBL_CREDENTIAL_PROFILE,
                                            new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                            item, DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    CredentialProfileConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                    {
                                                        if (result.succeeded())
                                                        {
                                                            message.reply(context.getString(OAuthUtil.ACCESS_TOKEN));
                                                        }
                                                        else
                                                        {
                                                            LOGGER.warn(String.format("credential profile config update failed. Reason : %s", result.cause()));

                                                            message.reply(EMPTY_VALUE);
                                                        }
                                                    });
                                                }
                                                else
                                                {
                                                    LOGGER.warn(String.format("credential profile update failed. Reason : %s", result.cause()));

                                                    message.reply(EMPTY_VALUE);
                                                }
                                            });
                                }
                                else
                                {
                                    // refresh token is expired or revoked
                                    if (response.cause() != null && response.cause().getMessage() != null && response.cause().getMessage().contains("invalid_grant"))
                                    {
                                        var credential = CredentialProfileConfigStore.getStore().getItem(item.getLong(ID));

                                        if (credential != null && !credential.isEmpty())
                                        {
                                            for (var entry : ActiveUserCacheStore.getStore().getItems().entrySet())
                                            {
                                                var userInfo = UserConfigStore.getStore().getItem(CommonUtil.getLong(entry.getValue().getValue(User.USER_ID)));

                                                // send notifications to users with admin role only
                                                if (userInfo != null && !userInfo.isEmpty() && userInfo.getLong(USER_ROLE).equals(CommonUtil.getLong(DEFAULT_ID)))
                                                {
                                                    // send notification UI.
                                                    EventBusConstants.publish(entry.getKey(), EventBusConstants.UI_NOTIFICATION_REFRESH_TOKEN_EXPIRE, new JsonObject().put(ID, credential.getLong(ID)).put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credential.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME)));
                                                }
                                            }
                                        }
                                    }

                                    LOGGER.warn(String.format("fetching valid auth token failed. Reason : %s", response.cause()));

                                    message.reply(EMPTY_VALUE);
                                }
                            });
                }
                else
                {
                    message.reply(EMPTY_VALUE);

                    LOGGER.warn("no refresh token has been found in the profile context");
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(EMPTY_VALUE);
            }
        });
    }
}
