/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	June-28-2025    <PERSON><PERSON>		MOTADATA-6574 Clone Dashboard with all associated widget.
*/

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.DashboardConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.User.USER_PREFERENCES;

public class Dashboard extends AbstractAPI
{
    public static final String DASHBOARD_NAME = "dashboard.name";
    public static final String DASHBOARD_CATEGORY = "dashboard.category";
    public static final String DASHBOARD_ACCESS_TYPE = "dashboard.access.type";
    public static final String DASHBOARD_ACCESS_TYPE_PUBLIC = "public";
    public static final String DASHBOARD_ACCESS_TYPE_PRIVATE = "private";
    public static final String DASHBOARD_CONTEXT = "dashboard.context";
    public static final String DASHBOARD_WIDGETS = "dashboard.widgets"; // dashboard.context
    public static final String DASHBOARD_USERS = "dashboard.users"; // dashboard.context
    public static final String DASHBOARD_CREATION_TIME = "dashboard.creation.time"; // dashboard.context
    public static final String DASHBOARD_MODIFICATION_TIME = "dashboard.modification.time"; // dashboard.context
    public static final String DASHBOARD_OWNER = "dashboard.owner"; // dashboard.context
    public static final String DASHBOARD_CATEGORY_HOME_SCREENS = "Home Screens";// home screen default category
    private static final Logger LOGGER = new Logger(Dashboard.class, MOTADATA_API, "Dashboard API");

    public Dashboard()
    {
        super("dashboards", DashboardConfigStore.getStore(), LOGGER);
    }

    //add current user if security is private and no user is selected
    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        var context = requestBody.getJsonObject(DASHBOARD_CONTEXT);

        var futures = new ArrayList<Future<Void>>();

        //Context would be empty if its new dashboard, unless it is cloned
        if (context.containsKey(Dashboard.DASHBOARD_WIDGETS) && !context.getJsonArray(Dashboard.DASHBOARD_WIDGETS).isEmpty())
        {
            var dashboardWidgets = context.getJsonArray(Dashboard.DASHBOARD_WIDGETS);

            for (var i = 0; i < dashboardWidgets.size(); i++)
            {
                var item = dashboardWidgets.getJsonObject(i);

                var widgetContext = WidgetConfigStore.getStore().getItem(item.getLong(ID));

                widgetContext.put(VisualizationConstants.VISUALIZATION_NAME, requestBody.getString(DASHBOARD_NAME) + " " + widgetContext.getString(VisualizationConstants.VISUALIZATION_NAME));

                widgetContext.put(DBConstants.FIELD_TYPE,  DBConstants.ENTITY_TYPE_USER);

                widgetContext.remove(ID);

                var promise = Promise.<Void>promise();

                Bootstrap.configDBService().save(DBConstants.TBL_WIDGET, widgetContext, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        WidgetConfigStore.getStore().addItem(asyncResult.result()).onComplete(handler ->
                        {
                            if (handler.succeeded())
                            {
                                item.put(ID, asyncResult.result());

                                promise.complete();
                            }
                            else
                            {
                                LOGGER.trace(handler.cause().getMessage());

                                promise.fail(handler.cause());
                            }
                        });
                    }
                    else
                    {
                        LOGGER.info(asyncResult.cause().getMessage());

                        promise.fail(asyncResult.cause());
                    }
                });

                futures.add(promise.future());
            }
        }

        return Future.all(futures)
                .compose(asyncResult ->
                {
                    context.put(DASHBOARD_CREATION_TIME, DateTimeUtil.currentMilliSeconds()).put(DASHBOARD_OWNER, routingContext.user().principal().getLong(ID));

                    return Future.succeededFuture(requestBody);
                });
    }

    //add current user if security is private and no user is selected
    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        var context = requestBody.getJsonObject(DASHBOARD_CONTEXT);

        if (context != null && !context.containsKey(DASHBOARD_WIDGETS) && requestBody.containsKey(DASHBOARD_NAME))
        {
            context.put(DASHBOARD_WIDGETS, new JsonArray());

            context.put(DASHBOARD_MODIFICATION_TIME, DateTimeUtil.currentMilliSeconds());
        }

        return Future.succeededFuture(requestBody);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        updateUserHomeScreen(entity.getLong(ID), routingContext);//after dashboard deleted need to check if its default dashboard so removing it from default..

        return Future.succeededFuture();
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var result = new JsonObject();

            var items = DashboardConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var category = item.getString(Dashboard.DASHBOARD_CATEGORY);

                if (!item.getString(DASHBOARD_CATEGORY).equalsIgnoreCase(DASHBOARD_CATEGORY_HOME_SCREENS))//default we will not be allowing HomeScreens category in listing
                {
                    item.getJsonObject(DASHBOARD_CONTEXT).put(DASHBOARD_CREATION_TIME, item.getJsonObject(DASHBOARD_CONTEXT).getLong(DASHBOARD_CREATION_TIME, DateTimeUtil.currentMilliSeconds()));

                    if (!result.containsKey(category))
                    {
                        result.put(category, new JsonArray());
                    }

                    if (item.containsKey(DASHBOARD_ACCESS_TYPE) && item.getString(DASHBOARD_ACCESS_TYPE).equalsIgnoreCase(DASHBOARD_ACCESS_TYPE_PRIVATE))
                    {

                        if (item.getJsonArray(DASHBOARD_USERS).contains(routingContext.user().principal().getLong(ID)))
                        {
                            result.getJsonArray(category).add(item);
                        }

                    }

                    else
                    {

                        result.getJsonArray(category).add(item);
                    }
                }
            }

            super.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, result));

        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void updateUserHomeScreen(long id, RoutingContext routingContext)
    {
        try
        {
            var items = UserConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var user = items.getJsonObject(index);

                var userPreferences = user.getJsonObject(USER_PREFERENCES, new JsonObject());

                if (userPreferences.getLong(User.USER_HOME_SCREEN, 0L).equals(id))
                {
                    userPreferences.put(User.USER_HOME_SCREEN, DEFAULT_ID);//if current user's home screen is removed so will assign default dashboard..

                    Bootstrap.configDBService().update(DBConstants.TBL_USER
                            , new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, user.getLong(ID))
                            , user.put(User.USER_PREFERENCES, userPreferences),
                            routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(), result ->
                            {
                                if (result.succeeded())
                                {
                                    UserConfigStore.getStore().updateItem(user.getLong(ID));
                                }
                            });
                }
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

}
