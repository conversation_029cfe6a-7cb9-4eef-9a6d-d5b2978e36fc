[{"_type": "0", "id": 10000000000604, "visualization.name": "Mailbox Status Count", "visualization.description": "Exchange Mailbox Status Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.online.mailbox~quota.mailboxes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "exchange.online.mailbox~quota.mailboxes.avg"}}}}, {"_type": "0", "id": 10000000000627, "visualization.name": "File Count By Activity Type", "visualization.description": "Sharepoint File Count By Activity Type", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sharepoint.file.activity.count", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "4", "order": "desc", "column": "sharepoint.file.activity.count.avg"}}}}, {"_type": "0", "id": 10000000000638, "visualization.name": "User Activity By Category", "visualization.description": "Teams User Activity By Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "teams.web.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "teams.windows.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "teams.android.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "teams.ios.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "teams.mac.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "teams.windows.users.avg"}}}}, {"_type": "0", "id": 10000000000694, "visualization.name": "Least Frequently Scanned Indexes", "visualization.description": "PostgreSQL Least Frequently Scanned Indexes", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "postgresql.index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "postgresql.index.scans", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "postgresql.index.last", "title": "Index Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "clipboard", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "postgresql.index.scans.last", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "sort-amount-up-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}], "sorting": {"limit": 5, "order": "asc", "column": "postgresql.index.scans.last"}}}}, {"_type": "0", "id": 10000000000988, "visualization.name": "Client Summary By Access Point", "visualization.description": "Client Summary By Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}, "visualization.result.by": ["cisco.wireless.client~ap"]}, "data.points": [{"data.point": "cisco.wireless.client~ap", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~snr", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client~ap.last", "title": "AP Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "cisco.wireless.client~snr.last", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "cisco.wireless.client~signal.strength.dbm.last", "title": "Signal Strength", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 5, "style": {}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 5, "formula": {"operation": "combine", "columns": ["cisco.wireless.client~traffic.sent.bytes.per.sec.last", "cisco.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}]}}}, {"_type": "0", "id": 10000000000989, "visualization.name": "Wireless Client Details", "visualization.description": "Wireless Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~ap.mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~ap", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~snr", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~sent.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~received.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"icon": {"name": "check-circle", "placement": "prefix", "conditions": [{"icon": "check-circle", "operator": "=", "value": "Associated"}, {"icon": "times-circle", "operator": "=", "value": "Dissociated"}]}}}, {"name": "cisco.wireless.client~ap.last", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "cisco.wireless.client~snr.last", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "cisco.wireless.client~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "formula": {"operation": "combine", "columns": ["cisco.wireless.client~traffic.sent.bytes.per.sec.last", "cisco.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.wireless.client~traffic.sent.bytes.per.sec.last", "title": "TX Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "cisco.wireless.client~traffic.received.bytes.per.sec.last", "title": "RX Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "Packets", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 9, "formula": {"operation": "combine", "columns": ["cisco.wireless.client~sent.packets.per.sec.last", "cisco.wireless.client~received.packets.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.wireless.client~sent.packets.per.sec.last", "title": "Sent Packets", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "cisco.wireless.client~received.packets.per.sec.last", "title": "Received Packets", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "cisco.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 12, "style": {}}, {"name": "cisco.wireless.client~ap.mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}, {"name": "cisco.wireless.client~signal.strength.dbm.last", "title": "Signal Strength", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 14, "style": {}}, {"name": "cisco.wireless.client~started.time.seconds.last", "title": "Connected Since", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 15, "style": {}}]}}}, {"_type": "0", "id": 10000000000990, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Heatmap", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001006, "visualization.name": "Ruckus Client Details", "visualization.description": "Ruckus Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~ap", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~snr", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold", "text-primary"]}}, {"name": "ruckus.wireless.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "ruckus.wireless.client~ap.last", "title": "AP Name", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~snr.last", "title": "SNR", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 7, "formula": {"operation": "combine", "columns": ["ruckus.wireless.client~traffic.sent.bytes.per.sec.last", "ruckus.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ruckus.wireless.client~traffic.sent.bytes.per.sec.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~traffic.received.bytes.per.sec.last", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-right", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "type": "label", "style": {"classes": ["font-bold"], "icon": {"name": "network", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "ruckus.wireless.client~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "calendar-alt", "placement": "prefix", "classes": ["text-secondary-orange"]}}}]}}}, {"_type": "0", "id": 10000000001007, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Heatmap", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ruckus.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ruckus.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001023, "visualization.name": "Aruba Client Details", "visualization.description": "Aruba Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~ap.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~ap.mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.client", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "aruba.wireless.client~status.last", "width.percent": 10, "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 4, "formula": {"operation": "combine", "columns": ["aruba.wireless.client~traffic.sent.bytes.per.sec.last", "aruba.wireless.client~traffic.received.bytes.per.sec.last"]}, "style": {"width.percent": 20, "classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.client~traffic.sent.bytes.per.sec.last", "title": "Sent Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.client~traffic.received.bytes.per.sec.last", "title": "Received Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.client~ap.ip.address.last", "title": "IP Addresses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 8, "style": {"classes": ["font-bold"], "icon": {"name": "network", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "aruba.wireless.client~ap.mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.client~started.time.last", "title": "Connected Since", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000001024, "visualization.name": "Wireless Signal Strength", "visualization.description": "Wireless Signal Strength Heatmap", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Wireless Signal Strength", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~signal.strength.dbm", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "aruba.wireless.client", "alias": "wireless.client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "aruba.wireless.client~signal.strength.dbm.last", "alias": "wireless.client.signal.strength.dbm.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001100, "visualization.name": "System Disk IO Read/Write", "visualization.description": "System Disk IO Read/Write Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001101, "visualization.name": "Interface Details", "visualization.description": "Interface Details Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.network.interface~bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "system.network.interface~in.bytes.per.sec.avg", "title": "In Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix"}}}, {"name": "system.network.interface~out.bytes.per.sec.avg", "title": "Out Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix"}}}, {"name": "system.network.interface~bytes.per.sec.avg", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "traffic", "placement": "prefix"}}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001102, "visualization.name": "Process Details", "visualization.description": "Process Details Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.process", "title": "Process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "process", "style": {}}, {"name": "system.process~cpu.percent.last", "title": "CPU(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "system.process~memory.used.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "system.process~threads.last", "title": "Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001103, "visualization.name": "Application Status", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Application Status", "visualization.data.sources": [{"type": "availability", "object.type": "system.process", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "up", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "down", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "unreachable", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "suspend", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "maintenance", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "disable", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "unknown", "aggregator": "percentage", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "up.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "down.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "maintenance.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "disable.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unknown.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unreachable.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "suspend.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "total", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "up.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "down.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unreachable.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "suspend.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "maintenance.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "disable.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unknown.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.result.by": ["monitor"], "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001121, "visualization.name": "System Disk IO Read/Write", "visualization.description": "System Disk IO Read/Write Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001122, "visualization.name": "Interface Details", "visualization.description": "Interface Details Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.network.interface~bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "system.network.interface~in.bytes.per.sec.avg", "title": "In Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix"}}}, {"name": "system.network.interface~out.bytes.per.sec.avg", "title": "Out Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix"}}}, {"name": "system.network.interface~bytes.per.sec.avg", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "traffic", "placement": "prefix"}}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001123, "visualization.name": "Process Details", "visualization.description": "Process Details Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.process", "title": "Process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "process", "style": {}}, {"name": "system.process~cpu.percent.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "system.process~memory.used.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "system.process~threads.last", "title": "Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001124, "visualization.name": "Application Status", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Application Status", "visualization.data.sources": [{"type": "availability", "object.type": "system.process", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "up", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "down", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "unreachable", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "suspend", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "maintenance", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "disable", "aggregator": "percentage", "entity.type": "monitor", "entities": []}, {"data.point": "unknown", "aggregator": "percentage", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "up.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "down.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "maintenance.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "disable.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unknown.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unreachable.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "suspend.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "total", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "up.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "down.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unreachable.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "suspend.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "maintenance.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "disable.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "unknown.percentage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.result.by": ["monitor"], "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001152, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001157, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"id": 10000000001158, "_type": "0", "visualization.name": "Domain Latency", "visualization.description": "Domain Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "domain.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "domain.latency.ms", "icon": {"name": "traffic"}}, "header": {"title": "Latency", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "domain.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000001162, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001166, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001170, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001174, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001178, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001182, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001187, "visualization.name": "Active Alerts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "title": "monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "Instance", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001287, "visualization.name": "VLAN Details", "visualization.category": "Grid", "visualization.type": "VLAN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "view": "vlan", "style": {"header.font.size": "medium", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor.last", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "vlan", "title": "VLAN", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"], "width.percent": 5}}, {"name": "vlan~name.last", "title": "VLAN Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "topology", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "vlan~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {"icon": {"placement": "prefix", "conditions": [{"value": "Active", "operator": "=", "icon": "check-circle"}, {"value": "Inactive", "operator": "=", "icon": "times-circle"}]}, "classes": ["font-bold"]}}, {"name": "vlan~port.count.last", "title": "Port Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "vlan~port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5, "type": "port", "style": {"classes": ["font-bold"]}}, {"name": "traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "computed": "yes", "formula": {"operation": "combine", "columns": ["interface~in.traffic.bits.per.sec.last", "interface~out.traffic.bits.per.sec.last"]}, "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~in.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-right", "classes": ["text-neutral-light"]}}}, {"name": "interface~out.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-left", "classes": ["text-neutral-light"]}}}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 10}]}}}, {"_type": "0", "id": 10000000001474, "visualization.name": "Top 10 Interface By Traffic Utilization (%)", "visualization.description": "Top 10 Interface By Traffic Utilization (%)", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "type": "monitor", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "type": "interface"}, {"name": "interface~in.traffic.utilization.percent.avg", "title": "Interface IN", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.avg", "title": "Interface Out", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "gauge"}}}, {"name": "interface~traffic.utilization.percent.avg", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~in.traffic.utilization.percent.avg"}}}}, {"_type": "0", "id": 10000000001475, "visualization.name": "Top 10 Interface By Traffic Utilization", "visualization.description": "Top 10 Interface By Traffic Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~traffic.bits.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "position": 1, "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "type": "interface"}, {"name": "interface~in.traffic.bits.per.sec.avg", "title": "Interface IN", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "interface~out.traffic.bits.per.sec.avg", "title": "Interface Out", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "interface~traffic.bits.per.sec.avg", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~in.traffic.bits.per.sec.avg"}}}}, {"_type": "0", "id": 10000000001476, "visualization.name": "Top 10 Interfaces By Error Packet", "visualization.description": "Top 10 Interfaces By Error Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~received.error.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~sent.error.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "interface", "style": {}}, {"name": "interface~received.error.packets.avg", "title": "Errors In", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "interface~sent.error.packets.avg", "title": "Errors Out", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~received.error.packets.avg"}}}}, {"_type": "0", "id": 10000000001477, "visualization.name": "Top 10 Interfaces By Discards", "visualization.description": "Top 10 Interfaces By Discards", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~received.discard.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "interface~sent.discard.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "interface", "style": {}}, {"name": "interface~received.discard.packets.avg", "title": "Discards In", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "interface~sent.discard.packets.avg", "title": "Discards Out", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~received.discard.packets.avg"}}}}, {"_type": "0", "id": 10000000001478, "visualization.name": "Active Alerts", "visualization.description": "Active Alerts Interface", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Active Alerts", "visualization.data.sources": [{"type": "policy", "category": "metric", "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Monitor", "object.type": ["interface"], "entities": [860607394145]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "policy.name", "title": "Policy Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "alert", "position": 1, "style": {"width.percent": 10, "color.conditions": []}}, {"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "type": "monitor", "position": 2}, {"name": "instance", "title": "interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 3}, {"name": "severity", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 4}, {"name": "counter", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5}, {"name": "value", "title": "Threshold | Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6}, {"name": "interval", "title": "Triggered Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7}, {"name": "duration", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8}, {"name": "message", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"width.percent": 20, "color.conditions": []}}]}}}, {"_type": "0", "id": 10000000001816, "visualization.name": "Interface In Traffic Utilization", "visualization.description": "Interface In Traffic Utilization", "container.type": "Template", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "SolidGauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~in.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "circle", "font.size": "medium", "classes": ["bottom-title"], "color.conditions": [{"color": "var(--secondary-green)", "operator": ">", "value": 0.1}, {"color": "#f58518"}, {"color": "#f5bc18"}]}}}}, {"_type": "0", "id": 10000000001817, "visualization.name": "Interface Out Traffic Utilization", "visualization.description": "Interface Out Traffic Utilization", "container.type": "Template", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "SolidGauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~out.traffic.utilization.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "circle", "classes": ["bottom-title"], "font.size": "medium", "color.conditions": [{"color": "var(--secondary-green)", "operator": ">", "value": 0.1}, {"color": "#f58518"}, {"color": "#f5bc18"}]}}}}, {"_type": "0", "id": 10000000001822, "visualization.name": "Top 5 Applications", "visualization.description": "Network Traffic By Applications", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.port", "destination.port"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["50%", "100%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001823, "visualization.name": "Top 5 Applications", "visualization.description": "Network Traffic By Source and Destination Ports", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.port", "destination.port"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "source.port", "title": "Source", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.port", "title": "Destination", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "volume.bytes.sum", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "type": "relative_percentage"}], "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001824, "visualization.name": "Top 5 Conversation", "visualization.description": "Network Traffic By Source and Destination IPs", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.ip", "destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": [1]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["50%", "100%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001825, "visualization.name": "Top 5 Conversation", "visualization.description": "Network Traffic By Source and Destination IPs", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.ip", "destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "source.ip", "title": "Source", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.ip", "title": "Destination", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "volume.bytes.sum", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "relative_percentage", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001826, "visualization.name": "Top 5 Protocols", "visualization.description": "Network Traffic By Protocols", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["protocol"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": [1]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["50%", "100%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001827, "visualization.name": "Top 5 Protocols", "visualization.description": "Network Traffic By Protocols", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["protocol"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": [1]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "protocol", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"color.conditions": []}}, {"name": "volume.bytes.sum", "title": "Utilization", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "relative_percentage"}], "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001857, "visualization.name": "Connected Users", "visualization.description": "Connected Users", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.connected.users", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.connected.users", "icon": {"name": "user", "placement": "prefix"}}, "header": {"title": "Connected Users", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.connected.users.last"}]}}}}, {"_type": "0", "id": 10000000001858, "visualization.name": "CPU", "visualization.description": "CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.cpu.percent", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001859, "visualization.name": "Disk Used", "visualization.description": "Disk Used", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.disk.used.percent", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.disk.used.percent", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.disk.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001860, "visualization.name": "Memory", "visualization.description": "Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.physical.memory.percent", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.physical.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Disk Used", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.physical.memory.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001861, "visualization.name": "Running Process", "visualization.description": "Running Process", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.running.processes", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.running.processes", "icon": {"name": "list", "placement": "prefix"}}, "header": {"title": "Running Process", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.running.processes.last"}]}}}}, {"_type": "0", "id": 10000000001862, "visualization.name": "Swap Memory", "visualization.description": "Swap Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.swap.memory.bytes", "aggregator": "last", "entity.type": "Monitor"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "smg.swap.memory.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Swap Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "smg.swap.memory.bytes.last"}]}}}}, {"id": 10000000001863, "visualization.name": "Queue Messages Per Second", "visualization.description": "Queue Messages Per Second", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.queue~messages.per.sec", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000001864, "visualization.name": "CPU Utilization", "visualization.description": "CPU Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.user.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.system.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.idle.cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000001865, "visualization.name": "Disk Volume Details", "visualization.description": "Disk Volume Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.disk.volume~free.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.disk.volume~used.bytes", "aggregator": "last", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~capacity.bytes.last", "alias": "system.disk.volume~capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~free.bytes.last", "alias": "system.disk.volume~free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "smg.disk.volume~used.bytes.last", "alias": "system.disk.volume~used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000001866, "visualization.name": "Memory Utilization", "visualization.description": "Memory Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "smg.physical.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.virtual.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "smg.cached.memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000001873, "_type": "0", "visualization.name": "Operational Server", "visualization.description": "Microsoft Exchange Operational Server", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.operational.servers", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "server", "placement": "prefix"}}, "header": {"title": "Operational Server", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.mailbox.role.operational.servers"}]}}}}, {"id": 10000000001874, "_type": "0", "visualization.name": "Mailbox Count", "visualization.description": "Microsoft Exchange Mailbox Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "dns.received.queries.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "add-messages", "placement": "prefix"}}, "header": {"title": "Mailbox Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "dns.received.queries.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000001875, "visualization.name": "Database Latency", "visualization.description": "Microsoft Exchange Database Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.database.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.database.writes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Database Latency", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "exchange.edge.transport.role.database.reads.per.sec.last"}, {"label": "Write", "value": "exchange.edge.transport.role.database.writes.per.sec.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "exchange.edge.transport.role.database.writes.per.sec"}}}}, {"_type": "0", "id": 10000000001876, "visualization.name": "Maibox Size", "visualization.description": "Microsoft Exchange Maibox Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.online.mailbox~used.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Maibox Size", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total Size", "value": "exchange.online.mailbox~used.size.bytes.last"}, {"label": "Used Size", "value": "exchange.online.mailbox~used.size.bytes.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "exchange.online.mailbox.used.size.bytes"}}}}, {"_type": "0", "id": 10000000001877, "visualization.name": "Network", "visualization.description": "Microsoft Exchange Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.online.mailbox~used.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Failures", "value": "exchange.online.mailbox~used.size.bytes.last"}, {"label": "Resets", "value": "exchange.online.mailbox~used.size.bytes.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "exchange.online.mailbox.used.size.bytes"}}}}, {"_type": "0", "id": 10000000001878, "visualization.name": "RPC", "visualization.description": "Microsoft Exchange RPC", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.rpc.users", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.rpc.active.users", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "RPC", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "User Counts", "value": "exchange2016.rpc.users.last"}, {"label": "Active Users", "value": "exchange2016.rpc.active.users.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "exchange2016.rpc.active.users"}}}}, {"_type": "0", "id": 10000000001879, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001880, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"id": 10000000001881, "_type": "0", "visualization.name": "RPC Details", "visualization.description": "Microsoft Exchange RPC Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.rpc.client.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.rpc.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001882, "_type": "0", "visualization.name": "Microsoft CPU & Memory Details", "visualization.description": "Microsoft Exchange CPU & Memory Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mailbox.assistant.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.search.service.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.mailbox.assistant.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.search.service.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001883, "_type": "0", "visualization.name": "Mailbox Processing Time", "visualization.description": "Microsoft Exchange Mailbox Processing Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.calendar.attendant.processing.time", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.resource.booking.attendant.processing.time", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001884, "_type": "0", "visualization.name": "Availability Requests", "visualization.description": "Microsoft Exchange Availability Request", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.availability.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001885, "_type": "0", "visualization.name": "RPC Operations", "visualization.description": "Microsoft Exchange RPC Operations", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.rpc.operations.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001886, "_type": "0", "visualization.name": "Active Sync Details", "visualization.description": "Microsoft Exchange Active Sync Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.sync.active.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.sync.pending.commands", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001887, "_type": "0", "visualization.name": "Web Service Connections", "visualization.description": "Microsoft Exchange Web Service Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.web.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.web.connection.attempts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001888, "_type": "0", "visualization.name": "RPC Latency", "visualization.description": "Microsoft Exchange RPC Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.rpc.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001889, "_type": "0", "visualization.name": "Restart Details", "visualization.description": "Microsoft Exchange Restart Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.application.restarts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.worker.process.restarts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001890, "_type": "0", "visualization.name": "Exchange Control Panel", "visualization.description": "Microsoft Exchange Control Panel", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.control.panel.outbound.proxy.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.control.panel.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001891, "_type": "0", "visualization.name": "Autodiscover Service Requests", "visualization.description": "Microsoft Exchange Autodiscover Service Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.auto.discovery.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001892, "_type": "0", "visualization.name": "Address Book Service", "visualization.description": "Microsoft Exchange Address Book Service", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.nspi.rpc.browse.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.nspi.rpc.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001893, "_type": "0", "visualization.name": "OWA Details", "visualization.description": "Microsoft Exchange OWA Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange2016.owa.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.owa.search.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange2016.owa.unique.users", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002017, "visualization.name": "Top 5 Applications", "visualization.description": "Flow Traffic By Applications", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["destination.port"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["40%", "110%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002018, "visualization.name": "Top 5 Applications", "visualization.description": "Flow Traffic By Source and Destination Ports", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["destination.port"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "destination.port", "title": "Application", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "Ingress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.volume.bytes.sum", "title": "Egress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "ingress.packets.sum", "title": "Ingress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.packets.sum", "title": "Egress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "ingress.volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002019, "visualization.name": "Top 5 Conversation", "visualization.description": "Flow Traffic By Source and Destination IPs", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.ip", "destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["40%", "110%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002020, "visualization.name": "Top 5 Conversation", "visualization.description": "Flow Traffic By Source and Destination IPs", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["source.ip", "destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "source.ip", "show": "yes", "title": "Source IP", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes"}, {"name": "destination.ip", "show": "yes", "title": "Destination IP", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes"}, {"name": "ingress.volume.bytes.sum", "title": "Ingress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.volume.bytes.sum", "title": "Egress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "ingress.packets.sum", "title": "Ingress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.packets.sum", "title": "Egress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "ingress.volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002021, "visualization.name": "Top 5 End Points", "visualization.description": "Flow Traffic By End Points", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["40%", "110%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002022, "visualization.name": "Top 5 End Points", "visualization.description": "Flow Traffic By End Points", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["destination.ip"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "destination.ip", "title": "Hostname", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "Ingress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.volume.bytes.sum", "title": "Egress Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "volume.bytes.sum", "title": "Volume Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "ingress.packets.sum", "title": "Ingress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "egress.packets.sum", "title": "Egress Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002023, "visualization.name": "Top 5 Protocols", "visualization.description": "Flow Traffic By Protocols", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["protocol"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": [1]}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {"plotOptions": {"pie": {"startAngle": -90, "endAngle": 90, "innerSize": "70%", "size": "200%", "center": ["40%", "110%"]}}, "legend": {"align": "right", "verticalAlign": "middle", "layout": "vertical", "padding": 3, "itemMarginTop": 8, "itemMarginBottom": 8}}, "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002024, "visualization.name": "Top 5 Protocols", "visualization.description": "Flow Traffic By Protocols", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "visualization.result.by": ["protocol"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "packets", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "flows", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "row.height.px": 46}, "columns": [{"name": "protocol", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"color.conditions": []}}, {"name": "flows.sum", "title": "Flow", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "volume.bytes.sum", "title": "Volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "packets.sum", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "volume.bytes.sum"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002239, "visualization.name": "", "visualization.description": "Log Total Events", "visualization.timeline": {"relative.timeline": "-1h", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "log", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"aggregator": "count", "data.point": "message", "entity.type": "all", "entities": [], "event.category": "Linux Login Audit"}]}], "visualization.properties": {"chart": {"vertical.legend": "no", "highchart.settings": {"yAxis": {"startOnTick": false, "endOnTick": false, "gridLineWidth": 0, "title": {"text": null}, "tickPositions": [0], "labels": {"enabled": false}}, "xAxis": {"startOnTick": false, "visible": false, "gridLineWidth": 0, "endOnTick": false, "title": {"text": null}, "tickPositions": [], "labels": {"enabled": false}}, "tooltip": {"outside": true}, "chart": {"backgroundColor": null, "margin": [10, 2, 2, 2], "borderWidth": 0, "style": {"overflow": "visible"}, "height": 70}}, "chart.legend": "no", "sorting": {"limit": 10, "order": "desc"}, "chart.label": "no", "rotation.angle": 0}}, "visualization.result.by": []}, {"id": 10000000002245, "visualization.name": "Server Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.disk.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.os.name", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.os.version", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.model", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}, {"data.point": "system.vendor", "aggregator": "last", "entity.type": "category", "entities": ["Server"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002246, "visualization.name": "Network Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "total.interfaces", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.disk.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.serial.number", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.os.version", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.model", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}, {"data.point": "system.model.number", "aggregator": "last", "entity.type": "category", "entities": ["Network"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002247, "visualization.name": "Service Check Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "service.check.latency.ms", "aggregator": "last", "entity.type": "category", "entities": ["Service Check"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002248, "visualization.name": "Virtualization Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "esxi.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "esxi.os.version", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "esxi.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "esxi.disk.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "citrix.xen.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "citrix.xen.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "citrix.xen.disk.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "citrix.xen.os.version", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "hyperv.version", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "hyperv.os.version", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "hyperv.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "hyperv.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "hyperv.disk.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}, {"data.point": "total.vms", "aggregator": "last", "entity.type": "category", "entities": ["Virtualization"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002249, "visualization.name": "System Service Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "system.service"], "data.points": [{"data.point": "system.service~status", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor", "system.service"], "container.type": "dashboard"}, {"id": 10000000002250, "visualization.name": "System Process Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "system.process"], "data.points": [{"data.point": "system.process~name", "aggregator": "last"}, {"data.point": "system.process~status", "aggregator": "last"}, {"data.point": "system.process~cpu.percent", "aggregator": "last"}, {"data.point": "system.process~memory.used.bytes", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor", "system.process"], "container.type": "dashboard"}, {"id": 10000000002251, "visualization.name": "Interface Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "interface"], "data.points": [{"data.point": "interface~name", "aggregator": "last"}, {"data.point": "interface~index", "aggregator": "last"}, {"data.point": "interface~alias", "aggregator": "last"}, {"data.point": "interface~status", "aggregator": "last"}, {"data.point": "interface~traffic.bits.per.sec", "aggregator": "last"}, {"data.point": "interface~error.packets", "aggregator": "last"}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last"}, {"data.point": "interface~ip.address", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor", "interface"], "container.type": "dashboard"}, {"id": 10000000002252, "visualization.name": "Other Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "category", "entities": ["Other"]}, {"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "category", "entities": ["Other"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"_type": "0", "id": 10000000002295, "visualization.name": "Log Type Hierarchy", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "log", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["event.source.type", "event.category", "event.source"], "data.points": [{"data.point": "event.category", "aggregator": "count"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["event.source.type", "event.category", "event.source"], "container.type": "dashboard"}, {"_type": "0", "id": 10000000002296, "visualization.name": "Log Group Hierarchy", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "log", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["group", "event.source"], "data.points": [{"data.point": "event.category", "aggregator": "count"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["group", "event.source"], "container.type": "dashboard"}, {"_type": "0", "id": 10000000002297, "visualization.name": "Log Total Events", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "Grid", "visualization.data.sources": [{"type": "log", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "event.category", "aggregator": "count"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "left", "color.conditions": [{}, {}, {}]}}}, "visualization.result.by": [], "container.type": "dashboard"}, {"_type": "0", "id": 10000000002306, "visualization.name": "SSID Details", "visualization.description": "SSID Details Cisco Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "SSID Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "No Of Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002316, "visualization.name": "SSID Details", "visualization.description": "SSID Details Aruba Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "SSID Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "No Of Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000002326, "visualization.name": "SSID Details", "visualization.description": "SSID Details Ruckus Wireless Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "SSID Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "No Of Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "", "title": "Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000002253, "visualization.name": "WAN Link Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "ipsla"], "data.points": [{"data.point": "ipsla~name", "aggregator": "last"}, {"data.point": "ipsla~operation.type", "aggregator": "last"}, {"data.point": "ipsla~status", "aggregator": "last"}, {"data.point": "ipsla~latency.ms", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor", "ipsla"], "container.type": "dashboard"}, {"id": 10000000002254, "visualization.name": "HCI Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "nutanix.cpu.percent", "aggregator": "last"}, {"data.point": "nutanix.memory.used.percent", "aggregator": "last"}, {"data.point": "nutanix.cluster", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002255, "visualization.name": "SDN Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "cisco.vmanage.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vmanage.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vbond.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vbond.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vsmart.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vsmart.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vedge.cpu.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}, {"data.point": "cisco.vedge.memory.used.percent", "aggregator": "last", "entity.type": "category", "entities": ["SDN"]}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "join.type": "custom", "join.result": "inventory", "join.columns": ["monitor"], "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000002256, "visualization.name": "Container Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor", "docker.container"], "data.points": [{"data.point": "docker.container~image", "aggregator": "last"}, {"data.point": "docker.container~status", "aggregator": "last"}, {"data.point": "docker.container~memory.used.bytes", "aggregator": "last"}, {"data.point": "docker.container~processes", "aggregator": "last"}, {"data.point": "docker.container~cpu.percent", "aggregator": "last"}, {"data.point": "docker.container~restarts", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor", "docker.container"], "container.type": "dashboard"}, {"id": 10000000002257, "visualization.name": "Container Orchestration Inventory Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "tanzu.kubernetes.health.status", "aggregator": "last"}, {"data.point": "tanzu.kubernetes.cpu.percent", "aggregator": "last"}, {"data.point": "tanzu.kubernetes.memory.percent", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}, "visualization.result.by": ["monitor"], "container.type": "dashboard"}, {"id": 10000000100039, "container.type": "dashboard", "visualization.name": "Top Conversation", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "destination.ip"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.packets", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "destination.ip", "title": "DESTINATION IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "volume.bytes.sum", "title": "VOLUME BYTES SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTES SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTES SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ingress.packets.sum", "title": "INGRESS PACKETS SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "egress.packets.sum", "title": "EGRESS PACKETS SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["source.ip", "destination.ip"]}, {"id": 10000000100040, "container.type": "dashboard", "visualization.name": "Top Interface Details By Volume", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "flow", "category": "flow", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["source.ip", "source.if.index"], "data.points": [{"data.point": "volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "egress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}, {"data.point": "ingress.volume.bytes", "aggregator": "sum", "entity.type": "Source", "entities": []}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "volume.bytes.sum", "title": "VOLUME BYTES SUM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "source.ip", "title": "SOURCE IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "source.if.index", "title": "SOURCE IF INDEX", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "egress.volume.bytes.sum", "title": "EGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ingress.volume.bytes.sum", "title": "INGRESS VOLUME BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "volume.bytes.sum"}}}, "visualization.result.by": ["source.ip", "source.if.index"]}]