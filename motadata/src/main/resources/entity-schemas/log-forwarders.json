{"entity": "Log Forwarder", "table": "tbl_config_log_forwarder", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.forwarder.name", "title": "Forwarder Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.forwarder.description", "title": "Description", "type": "string"}, {"name": "log.forwarder.type", "title": "Forwarder Type", "rules": ["required"], "type": "string", "values": ["tcp", "udp"]}, {"name": "log.forwarder.destination.ip", "title": "Destination IP", "type": "string", "rules": ["required"]}, {"name": "log.forwarder.destination.port", "title": "Destination Port", "type": "numeric"}, {"name": "log.forwarder.log.type", "title": "Forward Log as", "rules": ["required"], "values": ["raw", "json"], "type": "string"}]}