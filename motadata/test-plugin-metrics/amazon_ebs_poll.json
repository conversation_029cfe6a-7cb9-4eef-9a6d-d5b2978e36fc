{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EBS", "metric.object": ***************, "metric.plugin": "amazonebs", "metric.polling.min.time": 300, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Amazon EBS", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.273 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 22, "object.name": "vol-033a217b9b307a8fe(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "vol-033a217b9b307a8fe(ap-south-1)", "object.type": "Amazon EBS", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 7, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.ebs.volume": [{"aws.availability.zone": "ap-south-1b", "aws.ebs.volume": "vol-033a217b9b307a8fe", "aws.ebs.volume.attached.time": " 819 days 4 hours 23 minutes 31 seconds", "aws.ebs.volume.attached.time.sec": ********, "aws.ebs.volume.attachment.status": "attached", "aws.ebs.volume.creation.time": " 819 days 4 hours 23 minutes 31 seconds", "aws.ebs.volume.creation.time.seconds": ********, "aws.ebs.volume.device": "/dev/sda1", "aws.ebs.volume.ec2.instance.id": "i-08aca528c807a46f0", "aws.ebs.volume.max.io.ops.per.sec": 100, "aws.ebs.volume.region": "ap-south-1", "aws.ebs.volume.snapshot.id": "snap-0c635284a54ad3364", "aws.ebs.volume.type": "gp2", "aws.state": "in-use", "status": "Up"}]}, "status": "succeed", "timeout": 60}}