/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The policy package provides a comprehensive framework for defining, evaluating, and executing
 * policies across the Motadata platform. Policies are rule-based mechanisms that enable automated
 * monitoring, alerting, and action execution based on predefined conditions.
 * <p>
 * This package contains the following key components:
 * <ul>
 *   <li>{@link com.mindarray.policy.PolicyEngineConstants} - Core constants, enums, and utility methods
 *       used throughout the policy system, including policy types, operators, threshold types, and
 *       action types.</li>
 *   <li>{@link com.mindarray.policy.MetricPolicyInspector} - Responsible for inspecting and evaluating
 *       metric-based policies, handling both scalar and instance metrics, and triggering appropriate
 *       actions when conditions are met.</li>
 *   <li>{@link com.mindarray.policy.EventPolicyAggregator} - Manages event-based policies, evaluating
 *       incoming events against defined policy conditions and executing actions when triggered.</li>
 *   <li>{@link com.mindarray.policy.NetRoutePolicyInspector} - Handles network route policies,
 *       monitoring network paths and connectivity, and triggering actions based on route conditions.</li>
 *   <li>{@link com.mindarray.policy.AIOpsMetricPolicyInspector} - Extends policy evaluation with AI/ML
 *       capabilities for advanced metric analysis and anomaly detection.</li>
 *   <li>{@link com.mindarray.policy.EventPolicyQualifier} - Qualifies events against policy conditions
 *       to determine if actions should be triggered.</li>
 *   <li>{@link com.mindarray.policy.SeverityChangeEventProcessor} - Processes events related to
 *       severity changes and manages the appropriate responses.</li>
 * </ul>
 * <p>
 * The policy system architecture follows an event-driven model using Vert.x, where:
 * <ol>
 *   <li>Policies are defined with specific conditions and actions</li>
 *   <li>Inspectors continuously monitor metrics, events, and system states</li>
 *   <li>When conditions are met, actions are triggered (notifications, escalations, etc.)</li>
 *   <li>Policy states are tracked for flapping detection and auto-clearing</li>
 * </ol>
 * <p>
 * The system supports various policy types including:
 * <ul>
 *   <li>Metric Policies - Based on metric thresholds and conditions</li>
 *   <li>Event Policies - Triggered by specific system or application events</li>
 *   <li>Network Route Policies - Based on network connectivity and path conditions</li>
 *   <li>AIOps Policies - Leveraging AI/ML for advanced pattern recognition and anomaly detection</li>
 * </ul>
 * <p>
 * Policy actions can include notifications (email, SMS, integration channels), severity changes,
 * auto-clearing, and custom actions. The policy system also supports features like flapping detection,
 * re-notification, and correlation with other system components.
 * <p>
 * This package is designed to be extensible, allowing new policy types and actions to be added
 * with minimal changes to the core components.
 */
package com.mindarray.policy;