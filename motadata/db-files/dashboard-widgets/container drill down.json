[{"id": 55820265119946, "visualization.name": "Container Overview", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "docker.container~id", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~image", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~image.id", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~network.mode", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~created.time", "aggregator": "last", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"layout": "overview", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "docker.container.last", "title": "Container", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "docker.container~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "docker.container~image.last", "title": "Image", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "docker.container~image.id.last", "title": "Image ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "docker.container~network.mode.last", "title": "Network", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "docker.container~created.time.last", "title": "Created At", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"_type": "0", "id": 55820265119947, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": [], "drill.down.type": "yes"}, {"_type": "0", "id": 55820265119948, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "docker.container~uptime.percent.avg"}}}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119950, "visualization.name": "CPU Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~cpu.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~cpu.system.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~cpu.user.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119951, "visualization.name": "Memory Utilization", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~memory.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~memory.rss.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~memory.cache.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119952, "visualization.name": "IO Summary", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~io.read.bytes", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~io.write.bytes", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119953, "visualization.name": "Network Traffic Summary", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~received.bytes", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~sent.bytes", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119954, "visualization.name": "Network Packet Received Summary", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~received.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~received.errors", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~received.dropped.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}, {"id": 55820265119955, "visualization.name": "Network Packet Transmitted Summary", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~sent.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~sent.errors", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "docker.container~sent.dropped.packets", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "template", "drill.down.type": "yes"}]