{"172.16.8.135": {"result": {"mongodb.available.connections": 51197, "mongodb.cache.configured.size.bytes": 7651459072, "mongodb.cache.current.size.bytes": 149549, "mongodb.cache.read.size.bytes": 202315, "mongodb.cache.size.bytes": 149549, "mongodb.cache.write.size.bytes": 736767, "mongodb.commits": 84, "mongodb.created.connections": 43, "mongodb.db.operation.scan": 0, "mongodb.db.read.lock": 5, "mongodb.db.write.conflicts": 0, "mongodb.db.write.lock": 40, "mongodb.delete.operations": 0, "mongodb.dirty.cache.size.bytes": 0, "mongodb.getmore.operations": 0, "mongodb.global.lock.active.clients": 0, "mongodb.global.lock.queue": 0, "mongodb.global.read.lock": 3866, "mongodb.global.read.lock.active.clients": 0, "mongodb.global.read.lock.queue": 0, "mongodb.global.write.lock": 1947, "mongodb.global.write.lock.active.clients": 0, "mongodb.global.write.lock.queue": 0, "mongodb.insert.operations": 0, "mongodb.msg.asserts": 0, "mongodb.network.in.bytes": 12397, "mongodb.network.out.bytes": 372577, "mongodb.network.requests": 68, "mongodb.operations": 7, "mongodb.page.faults": 600, "mongodb.pages.evicted": 0, "mongodb.pages.read": 38, "mongodb.pages.requested": 1029, "mongodb.pages.write": 120, "mongodb.query.operations": 7, "mongodb.read.latency.ms": 0, "mongodb.read.rate": 7, "mongodb.regular.asserts": 0, "mongodb.resident.memory.bytes": 158334976, "mongodb.timed.out.cursors": 0, "mongodb.update.operations": 4, "mongodb.uptime.seconds": 1907, "mongodb.used.connections": 3, "mongodb.user.asserts": 124, "mongodb.version": "7.0.20", "mongodb.virtual.memory.bytes": 589299712, "mongodb.warning.asserts": 0, "mongodb.write.latency.ms": 0, "mongodb.write.rate": 4}, "errors": []}}