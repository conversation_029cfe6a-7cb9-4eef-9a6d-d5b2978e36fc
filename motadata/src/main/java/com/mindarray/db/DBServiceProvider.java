/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.pgclient.PgBuilder;
import io.vertx.pgclient.PgConnectOptions;
import io.vertx.serviceproxy.ServiceBinder;
import io.vertx.sqlclient.Pool;
import io.vertx.sqlclient.PoolOptions;

public class DBServiceProvider extends AbstractVerticle
{

    private static final Logger LOGGER = new Logger(DBServiceProvider.class, GlobalConstants.MOTADATA_DB, "DB Service Provider");

    private Pool pool;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Initialize PostgreSQL connection pool with configuration settings
        // Reconnect attempts: 3 with 10 sec interval
        pool = PgBuilder
                .pool()
                .with(new PoolOptions()
                        .setMaxSize(MotadataConfigUtil.getDBMaximumConnections()))
                .connectingTo(new PgConnectOptions()
                        .setPort(MotadataConfigUtil.getDBPort())
                        .setHost(MotadataConfigUtil.getDBHost())
                        .setDatabase("motadata")
                        .setUser("motadata")
                        .setPassword("TRACEorg@2025")
                        .setReconnectAttempts(3).setReconnectInterval(10 * 1000L)) // Reconnect attempts: 3 with 10 sec interval
                .using(vertx)
                .build();

        LOGGER.info("db pool created...");

        // postgres DB vacuum job
        vertx.eventBus().localConsumer(EventBusConstants.EVENT_DB_COMPACT, message ->
                pool.query("VACUUM FULL ANALYZE;").execute(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info("db compacted successfully!");
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                }));

        pool.query("SELECT 1").execute(asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var asyncPromise = Promise.<Void>promise();

                // Clear the config database if running in development/test mode
                if (MotadataConfigUtil.devMode())
                {
                    pool.query("DROP SCHEMA public CASCADE;").execute()
                            .compose(future -> pool.query("CREATE SCHEMA public;").execute())
                            .onComplete(result -> asyncPromise.complete());
                }
                else
                {
                    asyncPromise.complete();
                }

                asyncPromise.future().onComplete(asyncResponse ->
                        registerConfigDBService().compose(future ->
                                registerComplianceDBService()).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause());
                            }
                        }));
            }
            else
            {
                LOGGER.error(asyncResult.cause());

                promise.fail(asyncResult.cause());
            }
        });
    }

    @Override
    public void stop(Promise<Void> promise)
    {
        LOGGER.info("closing db pool...");

        pool.close(result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("db pool closed...");
            }
            else
            {
                LOGGER.error(result.cause());
            }

            promise.complete();
        });
    }

    private Future<Void> registerConfigDBService()
    {
        var promise = Promise.<Void>promise();

        ConfigDBService.create(vertx, pool, result ->
        {
            if (result.succeeded())
            {
                try
                {

                    new ServiceBinder(vertx).setAddress("config.db.service").registerLocal(ConfigDBService.class, result.result());

                    promise.complete();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    LOGGER.fatal("failed to start config db service...");

                    promise.fail(exception.getCause());
                }

            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> registerComplianceDBService()
    {
        var promise = Promise.<Void>promise();

        ComplianceDBService.create(vertx, pool, result ->
        {
            if (result.succeeded())
            {
                try
                {
                    new ServiceBinder(vertx).setAddress("compliance.db.service").registerLocal(ComplianceDBService.class, result.result());

                    promise.complete();
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    LOGGER.fatal("failed to start compliance db service...");

                    promise.fail(exception.getCause());
                }

            }

            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });

        return promise.future();
    }
}
