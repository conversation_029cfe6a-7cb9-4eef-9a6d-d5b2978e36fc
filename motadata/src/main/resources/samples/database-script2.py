#  Copyright (c) Motadata 2022. All rights reserved.

import argparse
import json
from base64 import b64encode

from motadatasdk.constants import Constant
from motadatasdk.logger import Logger
from motadatasdk.util import Util


#"command": "SELECT APPL1.AGENT_ID, APPL_NAME, APPL_STATUS, ROWS_READ, ROWS_WRITTEN, POOL_DATA_L_READS, POOL_DATA_P_READS, POOL_INDEX_L_READS, POOL_INDEX_P_READS, POOL_DATA_WRITES, (POOL_DATA_WRITES + POOL_DATA_P_READS + POOL_DATA_L_READS) AS DISK_IO, POOL_INDEX_WRITES, POOL_READ_TIME, POOL_WRITE_TIME, LOCKS_HELD, LOCK_WAITS, LOCK_TIMEOUTS, LOCK_WAIT_TIME, DEADLOCKS, TOTAL_SORTS, <PERSON><PERSON>IT_SQL_STMTS, R<PERSON><PERSON><PERSON><PERSON><PERSON>_SQL_STMTS, <PERSON>OW<PERSON>_DELETED, ROW<PERSON>_INSERTED, ROW<PERSON>_UPDATED, <PERSON>OW<PERSON>_SELECTED, NUM_AGENTS, AGENT_USR_CPU_TIME_S, AGENT_SYS_CPU_TIME_S, (AGENT_USR_CPU_TIME_S + AGENT_SYS_CPU_TIME_S) AS TOTAL_CPU, ELAPSED_EXEC_TIME_S, EXECUTION_ID, INBOUND_COMM_ADDRESS FROM SYSIBMADM.SNAPAPPL APPL1 JOIN SYSIBMADM.SNAPAPPL_INFO APPL2 ON APPL1.AGENT_ID = APPL2.AGENT_ID ORDER BY DISK_IO DESC LIMIT 10",

class DB2SessionByDiskIOMetricPlugin:

    def collect(self, context):

        result = {Constant.STATUS: Constant.STATUS_FAIL, Constant.RESULT: {}}

        sessions = []

        try:

            for row in context.get(Constant.RESULT):

                session = {}

                if row.get("AGENT_ID") is not None:
                    session["db2.session.agent.id"] = row.get("AGENT_ID")

                if row.get("APPL_NAME") is not None:
                    session["db2.session.application"] = row.get("APPL_NAME")

                if row.get("APPL_STATUS") is not None:
                    session["db2.session.application.status"] = row.get("APPL_STATUS")

                if row.get("ROWS_READ") is not None:
                    session["db2.session.read.rows"] = row.get("ROWS_READ")

                if row.get("ROWS_WRITTEN") is not None:
                    session["db2.session.written.rows"] = row.get("ROWS_WRITTEN")

                logical_read = physical_read = 0

                if row.get("POOL_DATA_L_READS") is not None:
                    logical_read = row.get("POOL_DATA_L_READS")

                if row.get("POOL_DATA_P_READS") is not None:
                    physical_read = row.get("POOL_DATA_P_READS")

                session["db2.session.data.reads.rate"] = logical_read + physical_read

                logical_read = physical_read = 0

                if row.get("POOL_INDEX_L_READS") is not None:
                    logical_read = row.get("POOL_INDEX_L_READS")

                if row.get("POOL_INDEX_P_READS") is not None:
                    physical_read = row.get("POOL_INDEX_P_READS")

                session["db2.session.index.reads"] = logical_read + physical_read

                if row.get("POOL_DATA_WRITES") is not None:
                    session["db2.session.data.writes.rate"] = row.get("POOL_DATA_WRITES")

                if row.get("DISK_IO") is not None:
                    session["db2.session.disk.io"] = row.get("DISK_IO")

                if row.get("POOL_INDEX_WRITES") is not None:
                    session["db2.session.index.writes"] = row.get("POOL_INDEX_WRITES")

                if row.get("POOL_READ_TIME") is not None:
                    session["db2.session.physical.read.time.ms"] = row.get("POOL_READ_TIME")

                if row.get("POOL_WRITE_TIME") is not None:
                    session["db2.session.physical.write.time.ms"] = row.get("POOL_WRITE_TIME")

                if row.get("LOCKS_HELD") is not None:
                    session["db2.session.lock.held"] = row.get("LOCKS_HELD")

                if row.get("LOCK_WAITS") is not None:
                    session["db2.session.lock.wait"] = row.get("LOCK_WAITS")

                if row.get("LOCK_WAIT_TIME") is not None:
                    session["db2.session.lock.wait.time.ms"] = row.get("LOCK_WAIT_TIME")

                if row.get("DEADLOCKS") is not None:
                    session["db2.session.deadlocks"] = row.get("DEADLOCKS")

                if row.get("TOTAL_SORTS") is not None:
                    session["db2.session.total.sports"] = row.get("TOTAL_SORTS")

                if row.get("COMMIT_SQL_STMTS") is not None:
                    session["db2.session.commit.sql.statements"] = row.get("COMMIT_SQL_STMTS")

                if row.get("ROLLBACK_SQL_STMTS") is not None:
                    session["db2.session.rollback.sql.statements"] = row.get("ROLLBACK_SQL_STMTS")

                if row.get("ROWS_DELETED") is not None:
                    session["db2.session.deleted.rows"] = row.get("ROWS_DELETED")

                if row.get("ROWS_INSERTED") is not None:
                    session["db2.session.inserted.rows"] = row.get("ROWS_INSERTED")

                if row.get("ROWS_UPDATED") is not None:
                    session["db2.session.updated.rows"] = row.get("ROWS_UPDATED")

                if row.get("ROWS_SELECTED") is not None:
                    session["db2.session.selected.rows"] = row.get("ROWS_SELECTED")

                if row.get("LOCK_TIMEOUTS") is not None:
                    session["db2.session.timedout.locks"] = row.get("LOCK_TIMEOUTS")

                if row.get("NUM_AGENTS") is not None:
                    session["db2.session.total.agent"] = row.get("NUM_AGENTS")

                if row.get("TOTAL_CPU") is not None:
                    session["db2.session.cpu.time.sec"] = row.get("TOTAL_CPU")

                if row.get("ELAPSED_EXEC_TIME_S") is not None:
                    session["db2.session.statement.execution.elapsed.time.sec"] = row.get("ELAPSED_EXEC_TIME_S")

                if row.get("EXECUTION_ID") is not None:
                    session["db2.session.user.id"] = row.get("EXECUTION_ID")

                if row.get("INBOUND_COMM_ADDRESS") is not None and len(row.get("INBOUND_COMM_ADDRESS")) > 0:

                    session["db2.session.remote.client"] = row.get("INBOUND_COMM_ADDRESS").split(" ")[0].strip()

                else:

                    session["db2.session.remote.client"] = Constant.BLANK_STRING

                if len(session) > 0:
                    sessions.append(session)

            if len(sessions) > 0:
                result[Constant.STATUS] = Constant.STATUS_SUCCEED

                result[Constant.RESULT]["db2.session"] = sessions

        except Exception as exception:

            result[Constant.ERRORS] = [
                {Constant.ERROR: str(Logger.get_stack_trace()), Constant.MESSAGE: str(exception),
                 Constant.ERROR_CODE: Constant.ERROR_CODE_INTERNAL_ERROR}]

        return result

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument("--context")

    args, leftovers = parser.parse_known_args()

    print(b64encode(json.dumps(DB2SessionByDiskIOMetricPlugin().collect(Util.load_plugin_context(str(args.context))).encode()).decode(), end=Constant.BLANK_STRING)
