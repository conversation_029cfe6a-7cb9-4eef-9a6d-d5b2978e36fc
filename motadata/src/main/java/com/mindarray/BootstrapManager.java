/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray;

import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.RemoteEventForwarder;
import com.mindarray.eventbus.RemoteEventSubscriber;
import com.mindarray.eventbus.RemoteSessionManager;
import com.mindarray.manager.MotadataAppManager;
import com.mindarray.util.*;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import static com.mindarray.eventbus.EventBusConstants.EVENT_REMOTE;

public class BootstrapManager
{
    private static final Logger LOGGER = new Logger(BootstrapManager.class, GlobalConstants.MOTADATA_SYSTEM, "Bootstrap Manager");

    public void start()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json");

            if (file.exists())
            {
                LOGGER.info("motadata manager is getting started ...");

                if (MotadataConfigUtil.getSystemBootstrapType().equalsIgnoreCase(GlobalConstants.BootstrapType.AGENT.name()))
                {
                    file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "agent.json");

                    AgentConfigUtil.loadAgentConfigs(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));
                }

                WebClientUtil.init();

                EventBusConstants.setManagerRegistrationId();

                Bootstrap.startEngine(new RemoteEventForwarder(MotadataConfigUtil.getMotadataManagerEventPublisherPort()), RemoteEventForwarder.class.getSimpleName() + " " + MotadataConfigUtil.getMotadataManagerEventSubscriberPort(), null)
                        .compose(future -> Bootstrap.startEngine(new RemoteEventSubscriber(MotadataConfigUtil.getMotadataManagerEventSubscriberPort()), RemoteEventSubscriber.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new MotadataAppManager(), MotadataAppManager.class.getSimpleName(), null, EVENT_REMOTE))
                        .compose(future -> Bootstrap.startEngine(new RemoteSessionManager(), RemoteSessionManager.class.getSimpleName(), null))
                        .onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                LogUtil.resetLogLevel(MotadataConfigUtil.getLogLevelResetTimerSeconds());

                                LOGGER.info(String.format(InfoMessageConstants.MOTADATA_ENGINE_START_SUCCEEDED, Bootstrap.bootstrapType()));
                            }
                            else
                            {
                                LOGGER.warn(String.format("Motadata manager started to failed : reason %s ", result.cause()));
                            }
                        });
            }
            else
            {
                LOGGER.warn(String.format("failed to find %s configuration file, hence aborting motadata boot process....", file.getName()));

                System.exit(0);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}


