/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  27-Feb-2025		Chopra Deven	MOTADATA-5143: Added support for forecast report of virtualization hosts and VMs/instance level report.
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  19-Mar-2025		Umang Sharma		Added Support for Availability Flap Summary Report
 *  18-June-2025    Aagam Salot     MOTADATA-6523: custom scripts reports support for windows build
 */
package com.mindarray.report;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Report.REPORT_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.report.ReportConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class ReportManager extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ReportManager.class, GlobalConstants.MOTADATA_REPORTING, "Report Manager");

    private final Map<Long, JsonObject> trackers = new HashMap<>();

    private final Map<Long, Long> tickers = new HashMap<>();

    private final Map<Long, Process> processTrackers = new HashMap<>();

    private final JsonObject columns = new JsonObject();

    /*
        This Method is used to qualify Entities as per EntityType for Report.
     */
    private static Set<Long> qualifyObjects(JsonObject context)
    {
        var objects = new HashSet<Long>();

        if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
        {
            var items = ObjectConfigStore.getStore().getItems(context.getJsonArray(ENTITIES));

            for (var index = 0; index < items.size(); index++)
            {
                objects.add(items.getJsonObject(index).getLong(ID));

            }
        }
        else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
        {
            var items = ObjectConfigStore.getStore().getItemsByGroups(context.getJsonArray(ENTITIES));

            for (var index = 0; index < items.size(); index++)
            {
                objects.add(items.getLong(index));
            }
        }
        else if (context.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
        {
            var items = ObjectConfigStore.getStore().getItemsByMultiValueFieldAny(AIOpsObject.OBJECT_TAGS,
                    new JsonArray(TagConfigStore.getStore().getIdsByItems(context.getJsonArray(ENTITIES))
                            .stream().map(CommonUtil::getInteger).toList()));

            for (var index = 0; index < items.size(); index++)
            {
                objects.add(items.getJsonObject(index).getLong(ID));
            }
        }

        return objects;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        if (Bootstrap.bootstrapType() == BootstrapType.APP)
        {
            vertx.eventBus().localConsumer(config().getString(EVENT_TYPE), this::render);

            vertx.eventBus().localConsumer(config().getString(EVENT_TYPE) + ".process", this::process);

            vertx.eventBus().localConsumer(config().getString(EVENT_TYPE) + ".process.close", this::close);

            vertx.eventBus().localConsumer(config().getString(EVENT_TYPE) + ".tracker", this::update);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_COLUMN_MAPPER_QUERY, "", reply ->
            {
                if (reply.succeeded())
                {
                    columns.mergeIn(reply.result().body().getJsonObject(DatastoreConstants.METRIC_COLUMNS));
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EVENT_COLUMN_MAPPER_UPDATE, message -> updateColumnMapper(message.body()));

            vertx.setPeriodic(30000, timer ->
            {
                try
                {
                    var iterator = tickers.entrySet().iterator();

                    while (iterator.hasNext())
                    {
                        var item = iterator.next();

                        if (item.getValue() + (MotadataConfigUtil.devMode() ? 20000 : 600000) <= System.currentTimeMillis())
                        {
                            trackers.remove(item.getKey());

                            vertx.eventBus().send(config().getString(EVENT_TYPE) + ".process.close", new JsonObject().put(REPORT_ID, item.getKey()).put(REASON, ErrorMessageConstants.TIMED_OUT));

                            iterator.remove();

                            LOGGER.warn(String.format("%s is cleaned up due to timeout", item.getKey()));

                            if (MotadataConfigUtil.devMode())
                            {
                                vertx.eventBus().send("test.report.timeout", new JsonObject().put(STATUS, STATUS_SUCCEED));
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            promise.complete();
        }
        else
        {
            promise.fail("failed to start Report Manager, reason: invalid boot sequence...");
        }
    }

    /**
     * IF: Report == custom
     * send individual request to Visualize manager
     * ELSE:
     * send report.widgets[0] to Visualize manager
     * MOTADATA-1454, passing the logged in ID as parameter to set his/her preferences. In case of, custom script test report user id is not received for that the user who created
     * the report will be considered
     *
     * @param message Message
     */
    private void render(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            if (event.getLong(REPORT_ID, -1L) != NOT_AVAILABLE)
            {
                var report = new JsonObject();

                if (event.getLong(REPORT_ID).equals(DUMMY_ID))
                {
                    // Creating dummy data for custom script report testing purpose

                    report.mergeIn(event);

                    var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, report.getString(User.USER_NAME));

                    report.put(ID, CommonUtil.newId()).put(Report.REPORT_USER, user.getLong(ID)).put(Report.REPORT_NAME, "Test Custom Report");
                }
                else
                {
                    report.mergeIn(ReportConfigStore.getStore().existItem(event.getLong(REPORT_ID)) ? ReportConfigStore.getStore().getItem(event.getLong(REPORT_ID)) : new JsonObject());
                }

                if (!report.isEmpty())
                {
                    var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                    if (Objects.isNull(item))
                    {
                        item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, report.getString(User.USER_NAME));
                    }

                    var type = report.getString(REPORT_TYPE);

                    if ((report.containsKey(Report.REPORT_CONTEXT) && report.getJsonObject(Report.REPORT_CONTEXT).containsKey(VISUALIZATION_TYPE)) || type.equals(ReportConstants.REPORT_TYPE_CUSTOM_SCRIPT))
                    {
                        var widgets = report.getJsonArray(Report.REPORT_WIDGETS);

                        var queryId = new AtomicLong(CommonUtil.newEventId());

                        if (event.containsKey(QUERY_ID))
                        {
                            queryId.set(event.getLong(QUERY_ID));
                        }

                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                        tickers.put(report.getLong(ID), System.currentTimeMillis());

                        trackers.put(report.getLong(ID), new JsonObject().put(Report.REPORT_WIDGETS, type.equalsIgnoreCase(ReportConstants.REPORT_TYPE_CUSTOM_SCRIPT) ? 1 : widgets.size())
                                .put(QUERY_ID, queryId)
                                .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                .put(SESSION_ID, event.getString(APIConstants.SESSION_ID)));

                        if (type.equalsIgnoreCase(ReportConstants.REPORT_TYPE_CUSTOM_SCRIPT))
                        {
                            executeCustomScript(report, event, queryId.get(), item);
                        }
                        else if (type.equalsIgnoreCase(REPORT_TYPE_CAPACITY_PLANNING) || type.equalsIgnoreCase(REPORT_TYPE_FORECAST)) // Added separate condition for the capacity planning and forecast report to have batch support in histogram request
                        {
                            var category = NMSConstants.Category.SERVER.getName();

                            widgets.remove(0); // Removing first widget as it is used for UI to render not used for processing in backend

                            JsonArray userGroups;

                            var batchSize = MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST) ? event.getInteger(TEST_REQUEST_BATCH_SIZE) : DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE;

                            if (!MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST) && type.equalsIgnoreCase(REPORT_TYPE_FORECAST))
                            {
                                batchSize = 50;  // DB can process 50 entities only at a time in case of forecast type request
                            }

                            if (item.getLong(ID).equals(DEFAULT_ID))
                            {
                                userGroups = GroupConfigStore.getStore().flatItems(ID);
                            }
                            else
                            {
                                userGroups = item.getJsonArray(User.USER_GROUPS);
                            }

                            List<Long> ids;

                            if (widgets.getJsonObject(0).containsKey(VISUALIZATION_INVENTORY_TYPES) && !widgets.getJsonObject(0).getJsonArray(VISUALIZATION_INVENTORY_TYPES).isEmpty())
                            {
                                ids = ObjectConfigStore.getStore().getItemsByGroups(userGroups, widgets.getJsonObject(0).getJsonArray(VISUALIZATION_INVENTORY_TYPES)).stream().map(CommonUtil::getLong).collect(Collectors.toSet()).stream().toList();
                            }
                            else
                            {
                                ids = ObjectConfigStore.getStore().getItemsByGroups(userGroups, AIOpsObject.OBJECT_CATEGORY, new JsonArray().add(category)).stream().map(CommonUtil::getLong).collect(Collectors.toList());
                            }

                            if (!ids.isEmpty())
                            {
                                trackers.put(report.getLong(ID), new JsonObject().put(Report.REPORT_WIDGETS, Math.ceil((double) ids.size() / batchSize) * widgets.size())
                                        .put(QUERY_ID, queryId)
                                        .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                        .put(SESSION_ID, event.getString(APIConstants.SESSION_ID))
                                        .put(REPORT_TYPE_CAPACITY_PLANNING, REPORT_TYPE_CAPACITY_PLANNING)
                                        .put(REPORT_TYPE, type));

                                for (var i = 0; i < widgets.size(); i++)
                                {
                                    var widget = widgets.getJsonObject(i);

                                    var dataPoints = widget.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(DATA_POINTS);

                                    // Making histogram data limit configurable using datastore.request.batch.size field
                                    widget.getJsonObject(VISUALIZATION_PROPERTIES).getJsonObject(VisualizationCategory.HISTOGRAM.getName().toLowerCase()).getJsonObject(SORTING).put(LIMIT, batchSize);

                                    // Adding "passover.step1.query" in the DB query request. IN DB, It is required to have performance improvements by ignoring first step
                                    widget.put(DatastoreConstants.DATASTORE_PASSOVER_STEP_1_QUERY, YES);

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace("Batch Size : " + batchSize + " | Total qualified batches : " + ((ids.size() / batchSize) + 1) + " | Size of Entities : " + ids.size());
                                    }

                                    /*
                                     * For forecast reports at the instance level, the database can process only 50 instances at a time.
                                     * Therefore, the request must be split into multiple batches, ensuring the number of VMs in each batch does not exceed the batch size.
                                     */
                                    if (report.getJsonObject(Report.REPORT_CONTEXT).containsKey(INSTANCE) && !report.getJsonObject(Report.REPORT_CONTEXT).getString(INSTANCE).isEmpty())
                                    {
                                        widget.put(REPORT_ID, report.getLong(ID))
                                                .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                                                .put(SESSION_ID, event.getString(SESSION_ID))
                                                .put(User.USER_NAME, item.getString(User.USER_NAME));

                                        var instanceKeys = getInstanceKeys(ids, report.getJsonObject(Report.REPORT_CONTEXT).getString(INSTANCE), dataPoints.getJsonObject(0).getString(VisualizationConstants.DATA_POINT));

                                        if (CommonUtil.traceEnabled())
                                        {
                                            LOGGER.trace("Batch Size : " + batchSize + " | Total batches : " + ((instanceKeys.size() / batchSize) + 1) + " | Size of InstanceKeys : " + instanceKeys.size());
                                        }

                                        trackers.get(report.getLong(ID)).put(Report.REPORT_WIDGETS, Math.ceil((double) instanceKeys.size() / batchSize) * widgets.size());

                                        dataPoints.getJsonObject(0).put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName()).put(ENTITIES, ids);

                                        widget.getJsonArray(VISUALIZATION_DATA_SOURCES).getJsonObject(0).put(VisualizationConstants.VISUALIZATION_RESULT_BY, new JsonArray().add(VisualizationConstants.VisualizationGrouping.MONITOR.getName()).add(dataPoints.getJsonObject(0).getString(VisualizationConstants.DATA_POINT).split(INSTANCE_SEPARATOR)[0]));

                                        if (instanceKeys.size() > batchSize)
                                        {
                                            for (var index = 0; index < instanceKeys.size(); index += batchSize)
                                            {
                                                var entityKeys = new HashMap<>();

                                                // preparing batch of the instance keys as per the batch-size
                                                instanceKeys.subList(index, Math.min(index + batchSize, instanceKeys.size())).forEach(entityKey -> entityKeys.put(entityKey.split(SEPARATOR_WITH_ESCAPE)[0], entityKey.split(SEPARATOR_WITH_ESCAPE)[1]));

                                                dataPoints.getJsonObject(0).put(VisualizationConstants.ENTITY_KEYS, entityKeys);

                                                if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
                                                {
                                                    vertx.eventBus().publish("test.visualization.render", event.mergeIn(widget));
                                                }
                                                else
                                                {
                                                    vertx.eventBus().send(EVENT_VISUALIZATION, event.mergeIn(widget));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var entityKeys = new HashMap<>();

                                            instanceKeys.forEach(entityKey -> entityKeys.put(entityKey.split(SEPARATOR_WITH_ESCAPE)[0], entityKey.split(SEPARATOR_WITH_ESCAPE)[1]));

                                            dataPoints.getJsonObject(0).put(VisualizationConstants.ENTITY_KEYS, entityKeys);

                                            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
                                            {
                                                vertx.eventBus().publish("test.visualization.render", event.mergeIn(widget));
                                            }
                                            else
                                            {
                                                vertx.eventBus().send(EVENT_VISUALIZATION, event.mergeIn(widget));
                                            }

                                        }
                                    }
                                    else if (ids.size() > batchSize)
                                    {
                                        for (var j = 0; j < ids.size(); j += batchSize)
                                        {
                                            dataPoints.getJsonObject(0).put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName()).put(ENTITIES, ids.subList(j, Math.min(j + batchSize, ids.size())));

                                            widget.put(REPORT_ID, report.getLong(ID))
                                                    .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                                                    .put(SESSION_ID, event.getString(SESSION_ID))
                                                    .put(User.USER_NAME, item.getString(User.USER_NAME));

                                            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
                                            {
                                                vertx.eventBus().publish("test.visualization.render", event.mergeIn(widget));
                                            }
                                            else
                                            {
                                                vertx.eventBus().send(EVENT_VISUALIZATION, event.mergeIn(widget));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        dataPoints.getJsonObject(0).put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName()).put(ENTITIES, ids);

                                        widget.put(REPORT_ID, report.getLong(ID))
                                                .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                                                .put(SESSION_ID, event.getString(SESSION_ID))
                                                .put(User.USER_NAME, item.getString(User.USER_NAME));


                                        if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
                                        {
                                            vertx.eventBus().publish("test.visualization.render", event.mergeIn(widget));
                                        }
                                        else
                                        {
                                            vertx.eventBus().send(EVENT_VISUALIZATION, event.mergeIn(widget));
                                        }
                                    }
                                }
                            }
                            else
                            {
                                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, event.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), 0L).getBytes()));
                            }
                        }
                        else if (type.equalsIgnoreCase(REPORT_TYPE_UNHEALTHY_MONITORS))
                        {
                            NMSConstants.getFaultyObjects(true).onComplete(result ->
                            {

                                if (result.succeeded())
                                {
                                    trackers.get(event.getLong(REPORT_ID)).put(EVENT_CONTEXT, result.result());

                                    vertx.eventBus().send(EVENT_REPORT_RESPONSE_PROCESSOR, trackers.get(event.getLong(REPORT_ID)).put(ID, event.getLong(REPORT_ID)));

                                    // cleanup
                                    trackers.remove(event.getLong(REPORT_ID));

                                    tickers.remove(event.getLong(REPORT_ID));
                                }

                            });
                        }
                        else
                        {
                            for (var index = 0; index < widgets.size(); index++)
                            {
                                var widget = widgets.getJsonObject(index);

                                widget.put(REPORT_ID, report.getLong(ID))
                                        .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                                        .put(SESSION_ID, event.getString(SESSION_ID))
                                        .put(User.USER_NAME, item.getString(User.USER_NAME));

                                ReportConstants.setMaxRecords(event, widget);

                                if (MotadataConfigUtil.devMode())
                                {
                                    vertx.eventBus().publish("test.visualization.render", widget);
                                }
                                else
                                {
                                    vertx.eventBus().send(EVENT_VISUALIZATION, widget);
                                }
                            }
                        }
                    }
                    else if (type.equalsIgnoreCase(REPORT_TYPE_AVAILABILITY_FLAP_SUMMARY))
                    {
                        if (report.getJsonArray(Report.REPORT_WIDGETS) != null && !report.getJsonArray(Report.REPORT_WIDGETS).isEmpty())
                        {
                            var widgets = report.getJsonArray(Report.REPORT_WIDGETS);

                            tickers.put(report.getLong(ID), System.currentTimeMillis());

                            var queryId = new AtomicLong(CommonUtil.newEventId());

                            if (event.containsKey(QUERY_ID))
                            {
                                queryId.set(event.getLong(QUERY_ID));
                            }

                            trackers.put(report.getLong(ID), new JsonObject().put(QUERY_ID, queryId.get())
                                    .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                                    .put(SESSION_ID, event.getString(APIConstants.SESSION_ID))
                                    .put(REPORT_TYPE_AVAILABILITY_FLAP_SUMMARY, REPORT_TYPE_AVAILABILITY_FLAP_SUMMARY)
                                    .put(REPORT_TYPE, type));

                            var widget = widgets.getJsonObject(0);

                            var dataPoints = widget.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(DATA_POINTS);

                            var objects = qualifyObjects(dataPoints.getJsonObject(0)).stream().toList();

                            if (!objects.isEmpty())
                            {
                                var batchSize = MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST) ? event.getInteger(TEST_REQUEST_BATCH_SIZE) : DatastoreConstants.DATASTORE_REQUEST_BATCH_SIZE;

                                if (!dataPoints.getJsonObject(0).getString(DATA_POINT).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName().toLowerCase()))
                                {
                                    batchSize = batchSize / 2;
                                }

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace("Batch Size : " + batchSize + " | Total qualified batches : " + ((objects.size() / batchSize) + 1) + " | Size of Entities : " + objects.size());
                                }

                                var batches = Math.ceil((double) objects.size() / batchSize);

                                trackers.get(report.getLong(ID)).put(Report.REPORT_WIDGETS, batches * widgets.size());

                                EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                                if (objects.size() > batchSize)
                                {
                                    for (int i = 0, batchCount = 0; i < objects.size(); i += batchSize, batchCount++)
                                    {
                                        setBatch(objects.subList(i, Math.min(i + batchSize, objects.size())), widget.copy(), report, event.copy(), item);
                                    }

                                    send((JsonObject) trackers.get(report.getLong(ID)).getJsonArray(REQUEST_BATCHES).remove(0));
                                }
                                else
                                {
                                    setBatch(objects, widget, report, event, item);

                                    send((JsonObject) trackers.get(report.getLong(ID)).getJsonArray(REQUEST_BATCHES).remove(0));
                                }
                            }
                            else
                            {
                                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, event.mergeIn(widget).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, event.getString(VisualizationConstants.VISUALIZATION_NAME, "Preview Widget"), ErrorMessageConstants.NO_ENTITY_QUALIFIED), queryId.get(), 0L).getBytes()));
                            }
                        }

                    }
                    else
                    {
                        if (report.getJsonArray(Report.REPORT_WIDGETS) != null && !report.getJsonArray(Report.REPORT_WIDGETS).isEmpty())
                        {
                            var widget = report.getJsonArray(Report.REPORT_WIDGETS).getJsonObject(0);

                            ReportConstants.setMaxRecords(event, widget);

                            event.remove(ID);

                            vertx.eventBus().send(MotadataConfigUtil.devMode() ? "test.visualization.render" : EVENT_VISUALIZATION, event.mergeIn(widget)
                                    .put(User.USER_NAME, item.getString(User.USER_NAME)));

                        }

                        else
                        {
                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("Report widgets are null or empty for, %s", report.encode()));
                            }
                        }
                    }
                }

                else
                {
                    LOGGER.warn(String.format("Report not found for id: %d", event.getLong(REPORT_ID)));
                }
            }

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Sends the given event to the visualization event bus.
     *
     * @param event the event to be sent
     */
    private void send(JsonObject event)
    {
        if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
        {
            vertx.eventBus().publish("test.visualization.render", event);
        }
        else
        {
            vertx.eventBus().send(EVENT_VISUALIZATION, event);
        }
    }

    /**
     * Sets up a batch of entities for processing.
     *
     * @param entities List of qualified entities.
     * @param widget   The widget configuration.
     * @param report   The report configuration.
     * @param event    The event data.
     * @param item     The user item data.
     */
    private void setBatch(List<Long> entities, JsonObject widget, JsonObject report, JsonObject event, JsonObject item)
    {
        widget.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(DATA_POINTS).getJsonObject(0).put(ENTITY_TYPE, VisualizationGrouping.MONITOR.getName()).put(ENTITIES, entities);

        widget.put(REPORT_ID, report.getLong(ID))
                .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                .put(SESSION_ID, event.getString(SESSION_ID))
                .put(User.USER_NAME, item.getString(User.USER_NAME));

        ((JsonArray) trackers.get(report.getLong(ID)).getMap().computeIfAbsent(REQUEST_BATCHES, value -> new JsonArray())).add(event.mergeIn(widget));
    }

    private void executeCustomScript(JsonObject report, JsonObject event, long queryId, JsonObject user)
    {
        var script = EMPTY_VALUE;

        try
        {
            var reportEvent = new JsonObject()
                    .put(REPORT_ID, report.getLong(ID))
                    .put(QUERY_ID, queryId)
                    .put(EVENT_TYPE, config().getString(EVENT_TYPE) + ".process")
                    .put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID))
                    .put(SESSION_ID, event.getString(SESSION_ID))
                    .put(User.USER_NAME, user.getString(User.USER_NAME));

            var eventContext = enrich(event);//will be checking whether custom context is selected for custom script

            if (!eventContext.isEmpty())
            {
                reportEvent.put(EVENT_CONTEXT, eventContext);
            }

            var arguments = new ArrayList<String>();

            var reportContext = report.getJsonObject(Report.REPORT_CONTEXT);

            var reportScriptType = report.containsKey(ReportConstants.REPORT_SCRIPT_TYPE) ? report.getString(ReportConstants.REPORT_SCRIPT_TYPE) : reportContext.getString(ReportConstants.REPORT_SCRIPT_TYPE);

            var fileName = CommonUtil.getString(System.nanoTime());

            if (reportScriptType.equalsIgnoreCase(PluginEngineConstants.PluginEngine.GO.getName()))
            {
                arguments.add(GO_BIN);

                arguments.add(GO_RUN_BIN);

                script = CURRENT_DIR + PATH_SEPARATOR + CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + fileName + ".go";
            }
            else if (reportScriptType.equalsIgnoreCase(PluginEngineConstants.PluginEngine.PYTHON.getName()))
            {
                arguments.add(PYTHON_BIN);

                script = CURRENT_DIR + PATH_SEPARATOR + CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + fileName + ".py";
            }
            else if (reportScriptType.equalsIgnoreCase(PluginEngineConstants.PluginEngine.NODE.getName()))
            {
                arguments.add(NODE_BIN);

                script = CURRENT_DIR + PATH_SEPARATOR + CUSTOM_PLUGIN_SCRIPT_DIR + PATH_SEPARATOR + fileName + ".js";
            }

            FileUtils.writeStringToFile(new File(script), report.containsKey(ReportConstants.REPORT_SCRIPT) ? report.getString(ReportConstants.REPORT_SCRIPT) : reportContext.getString(REPORT_SCRIPT), StandardCharsets.UTF_8);

            arguments.add(script);

            arguments.add("--context");

            arguments.add(OS_WINDOWS ? reportEvent.encode().replace("\"", "\\\"") : reportEvent.encode());

            var processBuilder = new ProcessBuilder(arguments).redirectErrorStream(true);

            var process = processBuilder.start();

            var pid = process.pid();

            if (CommonUtil.traceEnabled())
            {
                LOGGER.info(String.format("Report : %s, Process started with %s timeout having pid %s", report.getString(Report.REPORT_NAME), 60, pid));
            }

            processTrackers.put(report.getLong(ID), process);

            WorkerUtil.setupCleanupTimer(MotadataConfigUtil.getReportManagerCustomScriptTimeoutSeconds(), process, processBuilder, NOT_AVAILABLE, null, null);

            report.put("script", script).put(QUERY_ID, queryId).put(REPORT_ID, report.getLong(ID));

            // starting thread to read from stream
            new Thread(() -> read(process, event, user, report), String.format("Report Custom Process (%s)", report.getLong(ID))).start();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void read(Process process, JsonObject event, JsonObject user, JsonObject report)
    {
        var output = new StringBuilder(1000);

        var errors = new StringBuilder(1000);

        try
        {
            var pid = process.pid();

            try (var reader = new BufferedReader(new InputStreamReader(process.getInputStream())))
            {
                String line;

                while ((line = reader.readLine()) != null)
                {
                    output.append(line);

                    try
                    {
                        if (!output.isEmpty())
                        {
                            var response = output.toString();

                            if (response.contains(VALUE_SEPARATOR))
                            {
                                LOGGER.info("Response successfully received from script !! ");

                                var values = response.split(VALUE_SEPARATOR_WITH_ESCAPE);

                                var context = new JsonObject(values[0]);

                                if (context.containsKey(DatastoreConstants.BATCHES))
                                {
                                    var item = new JsonObject().put(Report.REPORT_WIDGETS, context.getInteger(DatastoreConstants.BATCHES)).put(REPORT_ID, report.getLong(REPORT_ID));

                                    if (context.containsKey(ACKNOWLEDGED))
                                    {
                                        item.put(ACKNOWLEDGED, context.getBoolean(ACKNOWLEDGED));
                                    }

                                    vertx.eventBus().send(config().getString(EVENT_TYPE) + ".tracker", item);
                                }

                                if (context.containsKey(ERROR))
                                {
                                    errors.append(context.getString(ERROR));

                                    break;
                                }

                                if (context.containsKey(RESULT))
                                {
                                    var records = context.getJsonObject(RESULT).getJsonArray(RESULT);

                                    var result = new JsonObject();

                                    for (var index = 0; index < records.size(); index++)
                                    {
                                        records.getJsonObject(index).getMap().forEach((key, value) ->
                                                ((JsonArray) result.getMap().computeIfAbsent(key, val -> new JsonArray())).add(value));
                                    }

                                    if (context.containsKey("query.progress.required") && context.getString("query.progress.required").equalsIgnoreCase("yes"))
                                    {
                                        result.put(QUERY_PROGRESS, context.getInteger(QUERY_PROGRESS));
                                    }

                                    pack(context, result);

                                    // Need to pack the result and then publish the result
                                    EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, context.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO));
                                }
                                else
                                {
                                    ReportConstants.setMaxRecords(event, context);

                                    vertx.eventBus().send(MotadataConfigUtil.devMode() ? "test.visualization.render" : EVENT_VISUALIZATION, context.put(User.USER_NAME, user.getString(User.USER_NAME)));
                                }

                                output.setLength(0);
                            }
                            else
                            {
                                errors.append(response);
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        errors.append(String.format("Report : %s, Got exception while processing result %s", report.getString(Report.REPORT_NAME), exception.getMessage()));

                        LOGGER.error(new Exception(exception.getMessage()));
                    }
                }
            }

            if (!process.isAlive())
            {
                var exitCode = process.exitValue();

                LOGGER.info(String.format("Report : %s with pid  %s exited with status code : %s", report.getString(Report.REPORT_NAME), pid, exitCode));

                if (!WorkerUtil.ABNORMAL_PROCESS_EXIT_CODES.contains(exitCode))
                {
                    LOGGER.info(String.format("Report Process %s exited at %s for the report %s", process.pid(), DateTimeUtil.timestamp(), report.getString(Report.REPORT_NAME)));
                }
            }
        }
        catch (Exception exception)
        {
            errors.append(String.format("Report : %s, Got exception while generating report %s", report.getString(Report.REPORT_NAME), exception.getMessage()));

            LOGGER.error(exception);
        }
        finally
        {
            if (!errors.isEmpty())
            {
                errors.insert(0, "Error from script : ");

                LOGGER.warn(errors.toString());

                EventBusConstants.publish(event.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, event.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(errors.toString(), report.getLong(QUERY_ID), event.getLong(SUB_QUERY_ID, 0L)).getBytes()));

                report.put(REASON, errors.toString());
            }

            vertx.eventBus().send(config().getString(EVENT_TYPE) + ".process.close", report);

            if (!report.getString("script").equals(EMPTY_VALUE))
            {
                FileUtils.deleteQuietly(new File(report.getString("script")));
            }
        }
    }

    private void close(Message<JsonObject> message)
    {
        var event = message.body();

        if (event.containsKey(REPORT_ID) && processTrackers.containsKey(event.getLong(REPORT_ID)))
        {
            var process = processTrackers.remove(event.getLong("report.id"));

            if (process != null && process.isAlive())
            {
                try
                {
                    LOGGER.info(String.format("Killing process for report with pid : %s, reason : %s", process.pid(), event.containsKey(REASON) ? event.getString(REASON) : UNKNOWN));

                    process.destroyForcibly();
                }
                catch (Exception ignored)
                {
                    // ignore
                }
            }
        }
    }

    private void update(Message<JsonObject> message)
    {
        var event = message.body();

        if (event.containsKey(REPORT_ID) && trackers.containsKey(event.getLong(REPORT_ID)))
        {
            trackers.put(event.getLong(REPORT_ID), trackers.get(event.getLong(REPORT_ID)).mergeIn(event));
        }
    }

    /**
     * Added support for more visualisation category in the custom script report if any use case come from users in the future.
     * Before : We have only provided support for Chart and Grid not for TOP N
     *
     * @param context Context
     * @param result  Result
     */
    private void pack(JsonObject context, JsonObject result)
    {
        try
        {
            var category = context.getString(VISUALIZATION_CATEGORY);

            var type = context.getString(VISUALIZATION_TYPE);

            switch (VisualizationConstants.VisualizationCategory.valueOfName(category))
            {
                case TOP_N ->
                {
                    if (type.equalsIgnoreCase(VisualizationConstants.VisualizationCategory.GRID.getName()))
                    {
                        context.put(RESULT, VisualizationConstants.pack(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes());
                    }
                    else
                    {
                        context.put(RESULT, VisualizationConstants.pack(result, VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal(), context.getLong(QUERY_ID), -1).getBytes());
                    }
                }

                case HISTOGRAM ->
                        context.put(RESULT, VisualizationConstants.pack(result, VisualizationCategoryOrdinal.VISUALIZATION_HISTOGRAM.ordinal(), context.getLong(QUERY_ID), -1).getBytes());

                default ->
                        context.put(RESULT, VisualizationConstants.pack(result, VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), context.getLong(QUERY_ID), -1).getBytes());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private JsonObject enrich(JsonObject event)
    {
        var context = new JsonObject();

        if (event.containsKey(ENTITY_TYPE))
        {
            context.put(ENTITY_TYPE, event.getString(ENTITY_TYPE));
        }
        if (event.containsKey(ENTITIES))
        {
            context.put(ENTITIES, event.getJsonArray(ENTITIES));
        }
        if (event.containsKey(VISUALIZATION_RESULT_BY))
        {
            context.put(VISUALIZATION_RESULT_BY, event.getString(VISUALIZATION_RESULT_BY));
        }
        if (event.containsKey(VISUALIZATION_TIMELINE))
        {
            context.put(VISUALIZATION_TIMELINE, event.getJsonObject(VISUALIZATION_TIMELINE));
        }

        return context;
    }

    private void send(JsonObject event, long reportId)
    {
        var errors = new StringBuilder();

        var process = processTrackers.get((reportId));

        var context = event.mergeIn(trackers.get(reportId));

        var outputStream = process.getOutputStream();

        try
        {
            event.remove(QUERY_ID);

            if (event.containsKey(RESULT))
            {
                var result = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(GlobalConstants.RESULT)), LOGGER, false, null, false, true);

                if (result.containsKey(RESULT))
                {
                    event.put(RESULT, result);

                    var output = new JsonObject().mergeIn(trackers.get(reportId)).mergeIn(event);

                    output.remove(EVENT_CONTEXT);

                    outputStream.write((output.encode() + VALUE_SEPARATOR).getBytes());

                    outputStream.flush();
                }
                else
                {
                    errors.append(result.containsKey(ERROR) ? result.getString(ERROR) : "Failed to execute query for widget Preview Widget, Possible reason: No entity qualified");
                }
            }
            else
            {
                errors.append("Failed to execute query for widget Preview Widget, Possible reason: Result not available");
            }
        }
        catch (Exception exception)
        {
            errors.append("Exception occurred while executing query");

            LOGGER.error(exception);
        }
        finally
        {
            try
            {
                if (!errors.isEmpty())
                {
                    LOGGER.warn(errors.toString());

                    event.put(ERROR, errors.toString());

                    var output = new JsonObject().mergeIn(trackers.get(reportId)).mergeIn(event);

                    output.remove(EVENT_CONTEXT);

                    outputStream.write((output.encode() + VALUE_SEPARATOR).getBytes());

                    outputStream.flush();

                    if (!trackers.get(reportId).containsKey(ACKNOWLEDGED))
                    {
                        EventBusConstants.publish(context.getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, context.put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, packError(errors.toString(), context.getLong(QUERY_ID), context.getLong(SUB_QUERY_ID)).getBytes()));
                    }
                }

                if (!event.containsKey(STATUS_PENDING))
                {
                    LOGGER.info("closing the stream for report id : " + reportId);

                    outputStream.close();
                }
            }
            catch (Exception ignored)
            {
                // ignored
            }
        }
    }

    /**
     * This method is used to get the instance keys for the given ids and instance type
     *
     * @param ids          List of qualified entities
     * @param instanceType Instance type
     * @param metric       metric type
     * @return List of instance keys
     */
    private List<String> getInstanceKeys(List<Long> ids, String instanceType, String metric)
    {

        var instanceKeys = new ArrayList<String>();

        if (!columns.isEmpty() && columns.containsKey(metric))
        {
            for (var id : ids)
            {
                for (var instance : MetricConfigStore.getStore().getMetricInstancesByInstanceType(CommonUtil.getLong(id), instanceType))
                {
                    instanceKeys.add(ObjectConfigStore.getStore().getObjectId(CommonUtil.getLong(id)) + CARET_SEPARATOR + instance + CARET_SEPARATOR + metric +
                            SEPARATOR + columns.getJsonObject(metric).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).getInteger(0) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(CommonUtil.getInteger(columns.getJsonObject(metric).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS).getInteger(0))));
                }
            }
        }

        return instanceKeys;
    }

    private void updateColumnMapper(JsonObject event)
    {
        try
        {
            if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(ChangeNotificationType.UPDATE_METRIC_COLUMN.name()))
            {
                var tokens = event.getString(DatastoreConstants.MAPPER).split(COLUMN_SEPARATOR, -1);

                if (!columns.containsKey(tokens[2]))
                {
                    columns.put(tokens[2], new JsonObject());
                }

                var mapper = columns.getJsonObject(tokens[2]);

                var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

                if (plugins == null)
                {
                    plugins = new JsonArray(new ArrayList<>(1));
                }

                var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

                if (categories == null)
                {
                    categories = new JsonArray(new ArrayList<>(1));
                }

                if (!categories.contains(CommonUtil.getInteger(tokens[0])))
                {
                    mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
                }

                mapper.put(DatastoreConstants.MAPPER_INSTANCE, tokens[3]);

                if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
                {
                    mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Visualize manager sends response to this method,
     * Here we collect all the request and on receiving all the request send it to ReportResponseProcessor
     *
     * @param message Message
     */
    private void process(Message<JsonObject> message)
    {
        try
        {
            var event = message.body();

            var reportId = event.getLong(REPORT_ID);

            if (trackers.containsKey(reportId))
            {
                var tracker = trackers.get(reportId);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("Received response of : " + reportId + " and Query Progress : " + event.getInteger(QUERY_PROGRESS));
                }

                if (tracker.containsKey(REPORT_TYPE) && tracker.getString(REPORT_TYPE).equalsIgnoreCase(REPORT_TYPE_AVAILABILITY_FLAP_SUMMARY))
                {
                    var buffer = Buffer.buffer(event.getBinary(RESULT));

                    var progress = Math.round(CommonUtil.getFloat(100 / (tracker.getInteger(Report.REPORT_WIDGETS, 0) > 0 ? tracker.getInteger(Report.REPORT_WIDGETS) : 1))); //calculate progress as we are sending multiple widgets

                    buffer.setLongLE(0, tracker.getLong(QUERY_ID));

                    // overwrite progress as from db it would be 100 percent for each request as it is incremental data we are calculating progress at backend
                    buffer.setShortLE(16, CommonUtil.getShort(progress));

                    EventBusConstants.publish(tracker.put(ID, reportId).getString(SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, event.put(RESULT, buffer.getBytes()).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO));

                    if (tracker.containsKey(REQUEST_BATCHES) && !tracker.getJsonArray(REQUEST_BATCHES).isEmpty())
                    {
                        send((JsonObject) tracker.getJsonArray(REQUEST_BATCHES).remove(0));
                    }

                    tracker.put(Report.REPORT_WIDGETS, tracker.getInteger(Report.REPORT_WIDGETS) - 1);

                    if (tracker.getInteger(Report.REPORT_WIDGETS) <= 0)
                    {
                        vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, tracker.getLong(QUERY_ID)).encode().getBytes()).getBytes()));

                        trackers.remove(reportId);

                        tickers.remove(reportId);
                    }
                }
                else
                {
                    tracker.getMap().computeIfAbsent(EVENT_CONTEXT, value -> new JsonArray());

                    tracker.getJsonArray(EVENT_CONTEXT).add(event);

                    // if acknowledged all request

                    if (tracker.getInteger(Report.REPORT_WIDGETS) == tracker.getJsonArray(EVENT_CONTEXT).size())
                    {
                        //sending response to script
                        if (processTrackers.containsKey(reportId))
                        {
                            send(event, reportId);
                        }
                        else
                        {
                            vertx.eventBus().send(EVENT_REPORT_RESPONSE_PROCESSOR, tracker.put(ID, reportId));
                        }

                        // cleanup
                        trackers.remove(reportId);

                        tickers.remove(reportId);
                    }
                    else if (tracker.containsKey(ACKNOWLEDGED) && tracker.getBoolean(ACKNOWLEDGED).equals(true))
                    {
                        //sending response to script
                        if (processTrackers.get(reportId) != null)
                        {
                            send(event.put(GlobalConstants.STATUS_PENDING, true), reportId);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void stop(Promise<Void> stop) throws Exception
    {
        super.stop(stop);
    }
}
