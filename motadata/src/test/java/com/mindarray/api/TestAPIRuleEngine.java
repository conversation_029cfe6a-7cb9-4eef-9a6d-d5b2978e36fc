/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.TestConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.UserRoleConfigStore;
import com.mindarray.util.CommonUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Group.FIELD_GROUP_NAME;
import static com.mindarray.api.Group.FIELD_PARENT_GROUP;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.api.UserRole.USER_ROLE_CONTEXT;
import static com.mindarray.api.UserRole.USER_ROLE_NAME;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestAPIRuleEngine
{

    private static final String EXPIRY_DAYS = "password.policy.password.expiry.days";

    private static final JsonArray PREREQUISITES = new JsonArray().add(new JsonObject().put("rule", PasswordPolicy.PASSWORD_POLICY_EXPIRY)
            .put(GlobalConstants.VALUE, GlobalConstants.YES));

    private static final JsonObject INVALID_RANGE_REQUEST_PARAMETERS = new JsonObject().put(EXPIRY_DAYS, 62);

    private static final JsonObject VALID_RANGE_REQUEST_PARAMETERS = new JsonObject().put(EXPIRY_DAYS, 30);


    private static final JsonObject INVALID_REQUEST_PARAMETERS = new JsonObject().put(PasswordPolicy.PASSWORD_POLICY_EXPIRY, GlobalConstants.NO);

    private static final JsonArray PASSWORD_RANGE = new JsonArray().add(1).add(60);

    private static final JsonObject INVALID_VALUE_EXIST_PARAMETERS = new JsonObject().put("name", "credential.profile.protocol")
            .put("title", "Protocol")
            .put("type", "list")
            .put("rules", new JsonArray().add("required"))
            .put("values", new JsonArray().add("Powershell").add("SNMP V1/V2c").add("SNMP V3")
                    .add("SSH").add("JDBC").add("HTTP").add("HTTPS").add("JMX").add("JMS"));

    private static final JsonObject INVALID_EXIST_VALUE = new JsonObject().put("credential.profile.protocol", new JsonArray().add("abc"))
            .put("credential.profile.context", new JsonObject().put("username", "motadata").put("ssh.key", "id_rsa"))
            .put("credential.profile.name", "Linux/Unix-Test-1595091984535");

    private static final String TYPE = "numeric";

    JsonObject response = null;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testValidValueExist(VertxTestContext testContext)
    {

        var params = new JsonObject().put("name", "credential.profile.protocol")
                .put("title", "Protocol")
                .put("type", "string")
                .put("rules", new JsonArray().add("required"))
                .put("values", new JsonArray().add("Powershell").add("SNMP V1/V2c").add("SNMP V3")
                        .add("SSH").add("JDBC").add("HTTP").add("HTTPS").add("JMX").add("JMS"));

        response = APIUtil.testValueExist(params.getString(ENTITY_PROPERTY_NAME), params.getString(ENTITY_PROPERTY_TITLE), params.getString(ENTITY_PROPERTY_TYPE), TestConstants.prepareParams("testLinuxKeyBasedDiscovery"), (JsonArray) params.getValue(ENTITY_PROPERTY_VALUES));

        assertNull(response);

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testInvalidListValueExist(VertxTestContext testContext)
    {
        response = APIUtil.testValueExist(INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_NAME), INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_TITLE), "list", INVALID_EXIST_VALUE, (JsonArray) INVALID_VALUE_EXIST_PARAMETERS.getValue(ENTITY_PROPERTY_VALUES));

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_INVALID_VALUE_RULE, INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_TITLE)), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testInvalidRequestParametersValueExist(VertxTestContext testContext)
    {
        var params = INVALID_EXIST_VALUE.put("credential.profile.protocol", "abc");

        response = APIUtil.testValueExist(INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_NAME), INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_TITLE), "string", params, (JsonArray) INVALID_VALUE_EXIST_PARAMETERS.getValue(ENTITY_PROPERTY_VALUES));

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_INVALID_VALUE_RULE, INVALID_VALUE_EXIST_PARAMETERS.getString(ENTITY_PROPERTY_TITLE)), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidValidateRange(VertxTestContext testContext)
    {
        assertTrue(APIUtil.validateRange("172.18.8.10", "172.18.8.20"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidValidateRange(VertxTestContext testContext)
    {

        assertFalse(APIUtil.validateRange("172.18.8.30 ", "172.18.8.20"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidRemoveDefaultParameters(VertxTestContext testContext)
    {
        var params = new JsonObject().put(DBConstants.FIELD_TYPE, 0)
                .put(GlobalConstants.ID, 530)
                .put(USER_NAME, "user2");

        APIUtil.removeDefaultParameters(params);

        assertNull(params.getInteger(GlobalConstants.ID));

        assertNull(params.getInteger(DBConstants.FIELD_TYPE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidPrerequisites(VertxTestContext testContext)
    {
        assertTrue(APIUtil.testPrerequisites(PREREQUISITES, VALID_RANGE_REQUEST_PARAMETERS, TYPE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidTypePrerequisites(VertxTestContext testContext)
    {
        var listType = "list";

        assertFalse(APIUtil.testPrerequisites(PREREQUISITES, INVALID_REQUEST_PARAMETERS, listType));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidPrerequisites(VertxTestContext testContext)
    {
        var prerequisites = new JsonArray().add(new JsonObject().put("rule", PasswordPolicy.PASSWORD_POLICY_EXPIRY)
                .put(GlobalConstants.VALUE, new JsonArray()));

        assertFalse(APIUtil.testPrerequisites(prerequisites, INVALID_REQUEST_PARAMETERS, TYPE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidRangeRule(VertxTestContext testContext)
    {

        response = APIUtil.testRangeRule(EXPIRY_DAYS, "Password expiry days", PASSWORD_RANGE, VALID_RANGE_REQUEST_PARAMETERS);

        assertNull(response);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidRangeRule(VertxTestContext testContext)
    {

        response = APIUtil.testRangeRule(EXPIRY_DAYS, "Password expiry days", new JsonArray(), INVALID_RANGE_REQUEST_PARAMETERS);

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_FIELD_REQUIRED, GlobalConstants.VALUE), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidRequiredRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(FIELD_GROUP_NAME, "grouptest").put(FIELD_PARENT_GROUP, 0);

        response = APIUtil.testRequiredRule(FIELD_GROUP_NAME, "Group Name", "string", params);

        assertNull(response);

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidRequiredRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(FIELD_PARENT_GROUP, 0);

        response = APIUtil.testRequiredRule(FIELD_GROUP_NAME, "Group Name", "string", params);

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_FIELD_REQUIRED, "Group Name"), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidNumericRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(FIELD_GROUP_NAME, "grouptest").put(FIELD_PARENT_GROUP, 0);

        response = APIUtil.testNumericRule(FIELD_PARENT_GROUP, "Parent Group", params);

        assertNull(response);

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidNumericRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(FIELD_GROUP_NAME, "grouptest").put(FIELD_PARENT_GROUP, "0");

        response = APIUtil.testNumericRule(FIELD_PARENT_GROUP, "Parent Group", params);

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, "Parent Group", FIELD_TYPE_NUMERIC), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidListRule(VertxTestContext testContext)
    {

        var params = new JsonObject().put(USER_ROLE_NAME, "test").put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:create"));

        response = APIUtil.testListRule(USER_ROLE_CONTEXT, "User Role", params);

        assertNull(response);

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidListRule(VertxTestContext testContext)
    {

        var params = new JsonObject().put(USER_ROLE_NAME, "test").put(USER_ROLE_CONTEXT, "user-settings:create");

        response = APIUtil.testListRule(USER_ROLE_CONTEXT, "User Role", params);

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, "User Role", FIELD_TYPE_LIST), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidMapRule(VertxTestContext testContext)
    {

        var params = new JsonObject().put(USER_ROLE_NAME, new JsonObject().put("name", "testuser")).put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:create"));

        response = APIUtil.testMapRule(USER_ROLE_NAME, "User Role", params);

        assertNull(response);

        testContext.completeNow();

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidMapRule(VertxTestContext testContext)
    {

        var params = new JsonObject().put(USER_ROLE_NAME, "user");

        response = APIUtil.testMapRule(USER_ROLE_NAME, "User Role", params);

        assertNotNull(response);

        assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        assertEquals(GlobalConstants.STATUS_FAIL, response.getString(GlobalConstants.STATUS));

        assertEquals(String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, "User Role", FIELD_TYPE_MAP), response.getString(GlobalConstants.MESSAGE));

        testContext.completeNow();

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidUniqueRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(USER_ROLE_NAME, "testuser").put(USER_ROLE_CONTEXT, new JsonArray().add("user-settings:create"));

        (APIUtil.testUniqueRule(null, "create", USER_ROLE_NAME, "Role Name", params, UserRoleConfigStore.getStore())).onComplete(result ->
        {
            if (result.succeeded())
            {

                assertEquals(0, result.result().size());

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidUniqueRule(VertxTestContext testContext)
    {
        var params = new JsonObject().put(USER_NAME, "admin");

        (APIUtil.testUniqueRule(null, "create", USER_NAME, "User Name", params, UserConfigStore.getStore())).onComplete(result ->
        {
            if (result.succeeded())
            {

                assertEquals(SC_BAD_REQUEST, result.result().getInteger(RESPONSE_CODE));

                assertEquals(GlobalConstants.STATUS_FAIL, result.result().getString(GlobalConstants.STATUS));

                assertEquals(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "User Name"), result.result().getString(GlobalConstants.MESSAGE));

                testContext.completeNow();
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidRangeDivisibleRule(VertxTestContext testContext)
    {

        response = APIUtil.testRangeDivideRule("worker.max.page.size.bytes", "Worker Maximum Page Size (bytes)", new JsonArray().add(4096).add(1048576), new JsonObject().put("worker.max.page.size.bytes", 4096), 4096);

        assertNull(response);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidRangeDivisibleRule(VertxTestContext testContext)
    {
        response = APIUtil.testRangeDivideRule("worker.max.page.size.bytes", "Worker Maximum Page Size (bytes)", new JsonArray().add(4096).add(1048576), new JsonObject().put("worker.max.page.size.bytes", 4090), 4096);

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        response = APIUtil.testRangeDivideRule("worker.max.page.size.bytes", "Worker Maximum Page Size (bytes)", new JsonArray().add(4096).add(1048576), new JsonObject().put("worker.max.page.size.bytes", 5490), 4096);

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidMinimumRule(VertxTestContext testContext)
    {
        response = APIUtil.testMinimumRule("min.workers", "Min Workers", CommonUtil.getLong(2), new JsonObject().put("min.workers", 1));

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        response = APIUtil.testMinimumRule("min.workers", "Min Workers", null, new JsonObject().put("min.workers", 1));

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testInvalidMaximumRule(VertxTestContext testContext)
    {
        response = APIUtil.testMaximumRule("max.workers", "Max Workers", CommonUtil.getLong(2), new JsonObject().put("max.workers", 5));

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        response = APIUtil.testMaximumRule("max.workers", "Max Workers", null, new JsonObject().put("max.workers", 5));

        Assertions.assertNotNull(response);

        Assertions.assertTrue(response.containsKey(RESPONSE_CODE));

        Assertions.assertEquals(SC_BAD_REQUEST, response.getInteger(RESPONSE_CODE));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    void testValidMaximumRule(VertxTestContext testContext)
    {
        response = APIUtil.testMaximumRule("max.workers", "Max Workers", CommonUtil.getLong(10), new JsonObject().put("max.workers", 5));

        Assertions.assertNull(response);

        testContext.completeNow();
    }
}
