/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			Notes
 *  24-Jun-2025		Darshan Parmar  MOTADATA-6583: Availability policy patch for Container Orchestration category
 */
package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.MOTADATA_PATCH;

public class Patch8024 implements Patch
{

    private static final Logger LOGGER = new Logger(Patch8024.class, MOTADATA_PATCH, "Patch 8.0.24");

    private static final String VERSION = "8.0.24";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.24");

        futures.add(executeMetricPolicyPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.24");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> executeMetricPolicyPatch()
    {
        var promise = Promise.<Void>promise();

        // availability default policy
        var id = 10000000000012L;

        var item = MetricPolicyConfigStore.getStore().getItem(id);

        item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).add(10000000000127L);

        Bootstrap.configDBService().updateAll(DBConstants.TBL_METRIC_POLICY,
                new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(GlobalConstants.VALUE, id),
                item,
                GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        MetricPolicyConfigStore.getStore().updateItem(id).onComplete(asyncResult ->
                        {
                            LOGGER.info("successfully updated availability default policy");

                            promise.complete();
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.error(result.cause());
                    }
                });

        return promise.future();
    }
}
