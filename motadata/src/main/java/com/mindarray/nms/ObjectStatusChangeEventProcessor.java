/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.nms;

/**
 * ObjectStatusChangeEventProcessor is responsible for tracking and notifying clients about object status changes.
 * <p>
 * This class:
 * - Maintains session-based tracking of object status changes
 * - Responds to status queries from UI clients
 * - Processes status change events and notifies relevant clients
 * - Manages user sessions and cleans up inactive sessions
 * - Provides real-time status updates for monitored objects and their instances
 * <p>
 * The class operates as a Vert.x verticle and uses the event bus for communication with other components.
 * It acts as a bridge between the status monitoring system and the UI, ensuring that status changes
 * are efficiently communicated to interested clients.
 */

import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ObjectStatusCacheStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.publish;
import static com.mindarray.nms.NMSConstants.STATUS_KEY;

public class ObjectStatusChangeEventProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ObjectStatusChangeEventProcessor.class, GlobalConstants.MOTADATA_NMS, "Object Status Change Event Processor");

    private final Map<String, JsonObject> eventsBySessionId = new HashMap<>();

    private final Map<String, Long> ticksBySessionId = new HashMap<>();

    /**
     * Initializes the ObjectStatusChangeEventProcessor verticle.
     * <p>
     * This method sets up event bus consumers for:
     * - EVENT_OBJECT_STATUS_QUERY: Responds with current object and instance statuses
     * - EVENT_OBJECT_STATUS_CHANGE: Processes status change events and stores them by session
     * - EVENT_USER_PING: Updates session activity and sends pending status changes
     * <p>
     * It also starts a periodic timer to clean up inactive sessions (those with no activity
     * for more than 5 minutes) to prevent memory leaks.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        // Handle status query events from UI clients
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_STATUS_QUERY, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                // Update the last activity timestamp for this session
                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                // Initialize an empty event container for this session
                eventsBySessionId.put(sessionId, new JsonObject());

                // Create a response with both object and instance statuses
                var response = new JsonObject()
                        .put("object.status", JsonObject.mapFrom(ObjectStatusCacheStore.getStore().getItems()))
                        .put("instance.status", new JsonObject());

                // Process instance statuses from the cache store
                for (var entry : ObjectStatusCacheStore.getStore().getInstanceItems().entrySet())
                {
                    for (var valueEntry : entry.getValue().entrySet())
                    {
                        var tokens = valueEntry.getKey().split(INSTANCE_SEPARATOR);

                        // Only include specific instance types (VMs, processes, access points, services)
                        if (tokens[0].toLowerCase().contains("vm") ||
                                tokens[0].toLowerCase().contains("system.process") ||
                                tokens[0].toLowerCase().contains("access.point") ||
                                tokens[0].toLowerCase().contains("system.service"))
                        {
                            // Add the instance status to the response with a formatted key
                            response.getJsonObject("instance.status").mergeIn(
                                    new JsonObject().put(entry.getKey() + SEPARATOR + tokens[1], valueEntry.getValue())
                            );
                        }
                    }
                }

                // Send the response to the client
                publish(sessionId, EventBusConstants.EVENT_OBJECT_STATUS_QUERY, response);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Handle status change events from the monitoring system
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_OBJECT_STATUS_CHANGE, message ->
        {
            var event = message.body();

            // Log the event if trace is enabled
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(event.encode());
            }

            try
            {
                // Store the status change for all active sessions
                // This ensures all connected UI clients will be notified of the change
                eventsBySessionId.forEach((key, value) ->
                        value.put(event.getString(STATUS_KEY),
                                new JsonObject()
                                        .put(event.getString(STATUS_KEY), event.getString(STATUS))
                                        .put("status.type", event.getString("status.type"))));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Handle ping events from UI clients (heartbeat mechanism)
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER_PING, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                // Update the last activity timestamp for this session
                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                // Ensure the session has an event container
                eventsBySessionId.computeIfAbsent(sessionId, value -> new JsonObject());

                // If there are pending status changes for this session, send them
                if (!eventsBySessionId.get(sessionId).isEmpty())
                {
                    var iterator = eventsBySessionId.get(sessionId).iterator();

                    // Create a response object to hold all status changes
                    var response = new JsonObject();

                    // Process each status change
                    while (iterator.hasNext())
                    {
                        var status = JsonObject.mapFrom(iterator.next().getValue());

                        // Extract the status type (e.g., "object", "instance")
                        var statusType = CommonUtil.getString(status.remove("status.type"));

                        if (statusType != null)
                        {
                            // Group status changes by status type
                            if (response.containsKey(statusType))
                            {
                                // Merge with existing status changes of the same type
                                response.getJsonObject(statusType).mergeIn(status);
                            }
                            else
                            {
                                // Create a new entry for this status type
                                response.put(statusType, new JsonObject().mergeIn(status));
                            }
                        }
                    }

                    // Send the aggregated status changes to the client
                    EventBusConstants.publish(sessionId, EventBusConstants.EVENT_OBJECT_STATUS_QUERY, response);

                    // Clear the processed status changes
                    eventsBySessionId.get(sessionId).clear();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        // Set up a periodic timer to clean up inactive sessions
        // The timer runs based on the configured UI session timeout (in minutes)
        vertx.setPeriodic(TimeUnit.MINUTES.toMillis(MotadataConfigUtil.getUISessionTimeoutMinutes()), timer ->
        {
            // Iterate through all tracked sessions
            var iterator = ticksBySessionId.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                // Check if the session has been inactive for more than 5 minutes
                if (entry.getValue() < System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5))
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug("removing " + entry.getKey());
                    }

                    // Remove the session's event container
                    eventsBySessionId.remove(entry.getKey());

                    // Remove the session from the activity tracking map
                    iterator.remove();
                }
            }
        });

        promise.complete();
    }
}
