/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import javax.management.Attribute;
import javax.management.ObjectName;
import javax.management.openmbean.CompositeDataSupport;
import java.lang.management.ManagementFactory;
import java.util.concurrent.TimeUnit;

import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.util.HealthUtil.JVM_GC_STATS;
import static com.mindarray.util.HealthUtil.JVM_STATS;

public class JVMStatUtil extends AbstractVerticle
{
    public static final String JVM_GC_COLLECTIONS = "jvm.gc.collections";

    public static final String JVM_GC_NAME = "jvm.gc.name";
    public static final String JVM_THREADS = "jvm.threads";
    public static final String JVM_GC_COLLECTION_TIME_MS = "jvm.gc.collection.time.ms";
    public static final String JVM_PEAK_THREADS = "jvm.peak.threads";
    public static final String JVM_DAEMON_THREADS = "jvm.daemon.threads";
    public static final String JVM_HEAP_MEMORY_COMMITTED_BYTES = "jvm.heap.memory.committed.bytes";
    public static final String JVM_HEAP_MEMORY_USED_BYTES = "jvm.heap.memory.used.bytes";
    public static final String JVM_HEAP_MEMORY_MAX_BYTES = "jvm.heap.memory.max.bytes";
    public static final String JVM_HEAP_MEMORY_INIT_BYTES = "jvm.heap.memory.init.bytes";
    public static final String JVM_NON_HEAP_MEMORY_COMMITTED_BYTES = "jvm.non.heap.memory.committed.bytes";
    public static final String JVM_NON_HEAP_MEMORY_USED_BYTES = "jvm.non.heap.memory.used.bytes";
    public static final String JVM_NON_HEAP_MEMORY_INIT_BYTES = "jvm.non.heap.memory.init.bytes";
    private static final Logger LOGGER = new Logger(JVMStatUtil.class, GlobalConstants.MOTADATA_UTIL, "JVM Stat Util");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        var jvmStats = new JsonObject();

        var result = new JsonObject();

        var garbageCollections = new JsonArray();

        var platformMBeanServer = ManagementFactory.getPlatformMBeanServer();

        var gcAttributes = new String[]{"Name", "CollectionCount", "CollectionTime"};


        vertx.setPeriodic(TimeUnit.SECONDS.toMillis(MotadataConfigUtil.getJVMStatPollTimerSeconds()), timer ->
        {

            try
            {
                ObjectName objectName;

                if (platformMBeanServer != null)
                {
                    var gcBeans = ManagementFactory.getGarbageCollectorMXBeans();

                    for (var gcBean : gcBeans)
                    {
                        var garbageCollection = new JsonObject();

                        objectName = new ObjectName("java.lang:type=GarbageCollector,name=" + gcBean.getName());

                        for (var attribute : platformMBeanServer.getAttributes(objectName, gcAttributes).asList())
                        {
                            if (attribute.getName().equalsIgnoreCase("Name") && attribute.getValue() != null)
                            {
                                garbageCollection.put(JVM_GC_NAME, attribute.getValue());
                            }
                            else if (attribute.getName().equalsIgnoreCase("CollectionCount") && attribute.getValue() != null)
                            {
                                garbageCollection.put(JVM_GC_COLLECTIONS, attribute.getValue());
                            }
                            else if (attribute.getName().equalsIgnoreCase("CollectionTime") && attribute.getValue() != null)
                            {
                                garbageCollection.put(JVM_GC_COLLECTION_TIME_MS, attribute.getValue());
                            }
                        }

                        garbageCollections.add(garbageCollection);
                    }

                    objectName = ObjectName.getInstance(ManagementFactory.THREAD_MXBEAN_NAME);

                    for (var object : platformMBeanServer.getAttributes(objectName, new String[]{"ThreadCount", "PeakThreadCount", "DaemonThreadCount"}))
                    {
                        var attribute = (Attribute) object;

                        if (attribute.getName().equalsIgnoreCase("ThreadCount") && attribute.getValue() != null)
                        {
                            jvmStats.put(JVM_THREADS, attribute.getValue());
                        }
                        else if (attribute.getName().equalsIgnoreCase("PeakThreadCount") && attribute.getValue() != null)
                        {
                            jvmStats.put(JVM_PEAK_THREADS, attribute.getValue());
                        }
                        else if (attribute.getName().equalsIgnoreCase("DaemonThreadCount") && attribute.getValue() != null)
                        {
                            jvmStats.put(JVM_DAEMON_THREADS, attribute.getValue());
                        }
                    }

                    objectName = ObjectName.getInstance(ManagementFactory.MEMORY_MXBEAN_NAME);

                    for (var object : platformMBeanServer.getAttributes(objectName, new String[]{"HeapMemoryUsage", "NonHeapMemoryUsage"}))
                    {
                        var attribute = (Attribute) object;

                        if (attribute.getName().equalsIgnoreCase("HeapMemoryUsage") && attribute.getValue() != null)
                        {
                            var items = ((CompositeDataSupport) attribute.getValue()).values().toArray();

                            if (items.length > 0)
                            {
                                jvmStats.put(JVM_HEAP_MEMORY_COMMITTED_BYTES, items[0])
                                        .put(JVM_HEAP_MEMORY_USED_BYTES, items[3])
                                        .put(JVM_HEAP_MEMORY_MAX_BYTES, items[2])
                                        .put(JVM_HEAP_MEMORY_INIT_BYTES, items[1]);
                            }
                        }
                        else if (attribute.getName().equalsIgnoreCase("NonHeapMemoryUsage") && attribute.getValue() != null)
                        {
                            var items = ((CompositeDataSupport) attribute.getValue()).values().toArray();

                            if (items.length > 0)
                            {
                                jvmStats.put(JVM_NON_HEAP_MEMORY_COMMITTED_BYTES, items[0])
                                        .put(JVM_NON_HEAP_MEMORY_USED_BYTES, items[3])
                                        .put(JVM_NON_HEAP_MEMORY_INIT_BYTES, items[1]);
                            }

                        }
                    }

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(new JsonObject().put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                .put(JVM_GC_STATS, garbageCollections)
                                .put(JVM_STATS, jvmStats)
                                .put(EVENT_SOURCE, MotadataConfigUtil.getHost()));
                    }

                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP && (MotadataConfigUtil.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()) || MotadataConfigUtil.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name())) ? EventBusConstants.EVENT_JVM_STATS_RESPONSE : EventBusConstants.EVENT_REMOTE,
                                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_JVM_STATS)
                                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(EVENT_SOURCE, MotadataConfigUtil.getHost())
                                        .put(HealthUtil.HEALTH_STATS, result
                                                .put(JVM_GC_STATS, garbageCollections)
                                                .put(JVM_STATS, jvmStats)));
                    }
                    else
                    {
                        vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP && (MotadataConfigUtil.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()) || MotadataConfigUtil.getInstallationMode().equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name())) ? EventBusConstants.EVENT_JVM_STATS_RESPONSE : EventBusConstants.EVENT_REMOTE,
                                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_JVM_STATS)
                                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(EVENT_SOURCE, MotadataConfigUtil.getHost())
                                        .put(HealthUtil.HEALTH_STATS, result
                                                .put(JVM_GC_STATS, garbageCollections)
                                                .put(JVM_STATS, jvmStats)));
                    }

                    jvmStats.clear();

                    garbageCollections.clear();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

        });

        promise.complete();
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        super.stop(promise);

        promise.complete();
    }
}
