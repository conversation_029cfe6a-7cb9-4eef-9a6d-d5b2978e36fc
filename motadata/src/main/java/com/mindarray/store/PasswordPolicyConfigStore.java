/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.util.Logger;

public class PasswordPolicyConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(PasswordPolicyConfigStore.class, GlobalConstants.MOTADATA_STORE, "Password Policy Config Store");

    private static final PasswordPolicyConfigStore STORE = new PasswordPolicyConfigStore();

    private PasswordPolicyConfigStore()
    {
        super(DBConstants.TBL_PASSWORD_POLICY, LOGGER, false);
    }

    public static PasswordPolicyConfigStore getStore()
    {
        return STORE;
    }
}
