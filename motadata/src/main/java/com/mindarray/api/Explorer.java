/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author		     Notes
 *  18-Jun-2025     Ya<PERSON> T<PERSON>wari      MOTADATA-6528: Common Explorers implementation for metric, log, flow, etc | Initial Version
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.store.ExplorerConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class Explorer extends AbstractAPI
{
    public static final String ID = "explorer.id";
    public static final String EXPLORER_NAME = "explorer.name";
    public static final String EXPLORER_GROUP_NAME = "explorer.group.name";
    public static final String EXPLORER_TYPE = "explorer.type";
    public static final String EXPLORER_CONTEXT = "explorer.context";
    public static final String EXPLORER_OBJECT_ID = "explorer.object.id";
    public static final String EXPLORER_OBJECT_TYPE = "explorer.object.type";
    public static final String EXPLORER_GLOBAL_VIEW_ENABLED = "explorer.global.view.enabled";
    public static final String EXPLORER_GROUPS = "explorer.groups";
    public static final String EXPLORER_VIEWS = "explorer.views";
    public static final String EXPLORER_USERS = "explorer.users";

    private static final Logger LOGGER = new Logger(Explorer.class, MOTADATA_API, "Explorer API");

    public Explorer()
    {
        super("explorers", ExplorerConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        super.init(router);
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var context = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (routingContext.request().getParam(FILTER) != null)
            {
                var type = context.getString(EXPLORER_TYPE);

                switch (APIConstants.ExplorerType.valueOfName(type))
                {
                    case TOPOLOGY ->
                    {
                        var items = ExplorerConfigStore.getStore().getItemsByValue(EXPLORER_TYPE, type);

                        var result = new JsonObject();

                        for (var index = 0; index < items.size(); index++)
                        {
                            var item = items.getJsonObject(index);

                            // groups[0] will be the parent group, groups[1] will be the nested group if exists
                            var groups = item.getString(EXPLORER_GROUP_NAME).split(COLON_SEPARATOR);

                            result.getMap().computeIfAbsent(groups[0], value -> new JsonObject().put(EXPLORER_GROUPS, new JsonObject()).put(EXPLORER_VIEWS, new JsonObject()));

                            // parent group
                            var group = result.getJsonObject(groups[0]);

                            // if there are more than one group, we will create a nested structure
                            if (groups.length > 1)
                            {
                                group.getJsonObject(EXPLORER_GROUPS).getMap().computeIfAbsent(groups[1], value -> new JsonObject());

                                // in nested group we will add the view
                                group.getJsonObject(EXPLORER_GROUPS).getJsonObject(groups[1]).put(item.getString(EXPLORER_NAME), new JsonObject().put(ID, item.getLong(GlobalConstants.ID)).put(ENTITY_ID, item.getLong(ENTITY_ID)).put(EXPLORER_USERS, item.getJsonArray(EXPLORER_USERS, new JsonArray())));
                            }
                            else
                            {
                                // if there is no nested group, we will add the view directly to the group
                                group.getJsonObject(EXPLORER_VIEWS).put(item.getString(EXPLORER_NAME), new JsonObject().put(ID, item.getLong(GlobalConstants.ID)).put(ENTITY_ID, item.getLong(ENTITY_ID)).put(EXPLORER_USERS, item.getJsonArray(EXPLORER_USERS, new JsonArray())));
                            }
                        }

                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonArray().add(result)));
                    }

                    case METRIC ->
                    {
                        if (context.containsKey(EXPLORER_OBJECT_ID) && context.containsKey(EXPLORER_OBJECT_TYPE))
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, ExplorerConfigStore.getStore().getItems(context.getLong(EXPLORER_OBJECT_ID), context.getString(EXPLORER_OBJECT_TYPE))));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, ExplorerConfigStore.getStore().flatItemsByValue(EXPLORER_TYPE, APIConstants.ExplorerType.METRIC.getName(), EXPLORER_GLOBAL_VIEW_ENABLED, YES)));
                        }
                    }

                    default -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(MESSAGE, String.format("Invalid explorer type: %s", type)));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, ExplorerConfigStore.getStore().getItemsByValue(EXPLORER_GLOBAL_VIEW_ENABLED, YES)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
