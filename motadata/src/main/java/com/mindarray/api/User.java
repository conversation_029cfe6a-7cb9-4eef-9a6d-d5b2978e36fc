/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     4-Mar-2025      Bharat         MOTADATA-4740: Two factor authentication 2FA
 *     24-Jun-2025	   Pruthvi		  MOTADATA-6580 : removed encryption/decryption of sensitive field. Handled in configdbserviceimpl.
 */
package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.AuthTokenConfigStore;
import com.mindarray.store.PasswordPolicyConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.Objects;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.PersonalAccessToken.PERSONAL_ACCESS_TOKEN;
import static com.mindarray.api.PersonalAccessToken.PERSONAL_ACCESS_TOKEN_VALIDITY;
import static com.mindarray.db.DBConstants.*;
import static org.apache.http.HttpStatus.*;

public class User extends AbstractAPI
{

    public static final String USER_NAME = "user.name";

    public static final String USER_FIRST_NAME = "user.first.name";

    public static final String USER_LAST_NAME = "user.last.name";

    public static final String USER_EMAIL = "user.email";

    public static final String USER_MOBILE = "user.mobile";

    public static final String USER_PASSWORD = "user.password";

    public static final String USER_PASSWORD_LAST_UPDATED_TIME = "user.password.last.updated.time";

    public static final String USER_OLD_PASSWORD = "user.old.password";

    public static final String USER_PREVIOUS_STATUS = "user.previous.status";

    public static final String USER_STATUS = "user.status";

    public static final String USER_ROLE = "user.role";

    public static final String USER_GROUPS = "user.groups";

    public static final String USER_TYPE = "user.type";

    public static final String USER_TYPE_LDAP = "LDAP";

    public static final String USER_TYPE_SSO = "SSO";

    public static final String USER_LDAP_SERVER = "user.ldap.server";

    public static final String USER_TYPE_SYSTEM = "System";

    public static final String USER_AVATAR = "user.avatar";

    public static final String USER_PREFERENCES = "user.preferences";

    public static final String USER_HOME_SCREEN = "user.home.screen";

    public static final String USER_FORGOT_PASSWORD = "user.forgot.password";

    public static final String USER_TEMPORARY_PASSWORD = "user.temporary.password";

    public static final String USER_TEMPORARY_PASSWORD_CREATED_TIME = "user.temporary.password.created.time";

    public static final String USER_ID = "user.id";

    public static final String USER_PREFERENCE_DATE_TIME_FORMAT = "user.preference.date.time.format";

    public static final String USER_PREFERENCE_TIME_ZONE = "user.preference.time.zone";

    public static final String USER_PREFERENCE_NOTIFICATION_POPUP_STATUS = "user.preference.notification.popup.status";

    public static final String USER_TOTP_KEY = "user.totp.key";

    public static final String USER_ONE_TIME_PASSWORD = "user.one.time.password";

    public static final String USER_ONE_TIME_PASSWORD_CREATED_TIME = "user.one.time.password.created.time";

    private static final Logger LOGGER = new Logger(User.class, MOTADATA_API, "User API");

    public User()
    {
        super("users", UserConfigStore.getStore(), LOGGER, new CipherUtil());
    }

    @Override
    public void init(Router router)
    {
        try
        {
            router.put("/" + endpoint + "/:id/password").handler(this::resetPassword);

            router.post("/" + endpoint + "/:id/generate-token").handler(this::generateToken);

            router.get("/" + endpoint).handler(this::getAll);

            router.post("/" + endpoint + "/update").handler(this::updateAll);

            router.put("/" + endpoint + "/:id/reset2FA").handler(this::reset2FA);

            super.init(router);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    public void getAll(RoutingContext routingContext)
    {
        // if the logged-in user has only the permission of user-settings:read then in user screen will not show other user's information except him/her self - MOTADATA-3092
        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT,
                routingContext.user().principal().getJsonArray(APIConstants.USER_PERMISSIONS).contains(USER_SETTINGS + ":" + READ_WRITE_PERMISSION)
                        ? UserConfigStore.getStore().getItems()
                        : JsonArray.of(UserConfigStore.getStore().getItem(routingContext.user().attributes().getJsonObject("accessToken").getLong(ID)))));
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_USER.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject response, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_USER.name()).put(EventBusConstants.EVENT_CONTEXT, response));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_ACCESS_TOKEN.name()).put(ID, response.getLong(ID)));

        return super.afterDelete(response, routingContext);
    }

    /**
     * MOTADATA-1486 / MOTADATA-1692 when user is updated and if the status is changed from active to inactive remove the access token so that user will be logged out of the system
     *
     * @param entity
     * @param routingContext
     * @return
     */
    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        try
        {
            var userId = CommonUtil.getLong(routingContext.request().getParam(ID));

            LOGGER.info("Updating for user : " + routingContext.request().getParam(ID) + " from " + routingContext.user().principal().getString(USER_NAME));

            var authTokenId = AuthTokenConfigStore.getStore()
                    .getItemByValue(USER_ID, userId).getLong(ID);

            var currentStatus = routingContext.body().asJsonObject().getString(USER_STATUS);

            var previousStatus = routingContext.body().asJsonObject().getString(USER_PREVIOUS_STATUS);

            if ((!Objects.equals(previousStatus, currentStatus)
                    && NO.equalsIgnoreCase(currentStatus)) && Objects.nonNull(authTokenId) &&
                    AuthTokenConfigStore.getStore().existItem(authTokenId))
            {
                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_ACCESS_TOKEN.name()).put(ID, userId));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return super.afterUpdate(entity, routingContext);
    }

    /**
     * MOTADATA-1486 / MOTADATA-1692 when admin user changes password remove the access token, so that user will be logged out of the system
     * MOTADATA-4834: Now onwards allow any user to reset password if the user having the user:read-write permission
     *
     * @param routingContext
     */
    private void resetPassword(RoutingContext routingContext)
    {
        try
        {
            var user = UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (user != null && !user.isEmpty())
            {
                if (!user.getLong(ID).equals(DEFAULT_ID))
                {
                    if (user.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM))
                    {
                        var passwordPolicy = PasswordPolicyConfigStore.getStore().getItem();

                        if (passwordPolicy != null && !passwordPolicy.isEmpty())
                        {
                            var schema = CommonUtil.getEntitySchema(endpoint);

                            if (schema != null)
                            {
                                alterParameters(routingContext, schema); //to encrypt password to check with password policy

                                if (validate(routingContext.body().asJsonObject().getString(USER_PASSWORD), passwordPolicy))
                                {
                                    Bootstrap.configDBService().update(DBConstants.TBL_USER,
                                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(routingContext.request().getParam(ID))),
                                            new JsonObject().put(User.USER_PASSWORD, routingContext.body().asJsonObject().getString(User.USER_PASSWORD)).put(USER_PASSWORD_LAST_UPDATED_TIME, System
                                                            .currentTimeMillis())
                                                    .put(GARBAGE_FIELDS, new JsonArray().add(User.USER_TEMPORARY_PASSWORD).add(User.USER_TEMPORARY_PASSWORD_CREATED_TIME)), // Reset password will reset the Temporary Password,
                                            routingContext.user().principal().getString(User.USER_NAME),
                                            routingContext.request().remoteAddress().host(),
                                            result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (!Objects.equals(user.getString(USER_PASSWORD), routingContext.body().asJsonObject().getString(USER_PASSWORD)))
                                                    {
                                                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                                                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_ACCESS_TOKEN.name()).put(ID, user.getLong(ID)));
                                                    }

                                                    this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED)
                                                            .put(RESPONSE_CODE, SC_OK).put(MESSAGE, String.format(InfoMessageConstants.RESET_PASSWORD_SUCCEEDED, user.getString(User.USER_NAME))).put(ID, CommonUtil.getLong(routingContext.request().getParam(ID))));

                                                    UserConfigStore.getStore().updateItem(CommonUtil.getLong(routingContext.request().getParam(ID)));
                                                }

                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                                                            .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(MESSAGE, String.format(ErrorMessageConstants.RESET_PASSWORD_FAILED, result.cause().getMessage())));
                                                }
                                            });
                                }

                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                                            .put(STATUS, GlobalConstants.STATUS_FAIL)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_PASSWORD_POLICY_NOT_SATISFIED)
                                            .put(MESSAGE, ErrorMessageConstants.PASSWORD_POLICY_NOT_SATISFIED));
                                }
                            }

                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
                            }
                        }

                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                                    .put(STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                    .put(MESSAGE, ErrorMessageConstants.PASSWORD_POLICY_NOT_FOUND));
                        }
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                                .put(RESPONSE_CODE, SC_BAD_REQUEST)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_RESET_PASSWORD_LDAP_USER)
                                .put(MESSAGE, String.format(ErrorMessageConstants.RESET_PASSWORD_FAILED, ErrorMessageConstants.RESET_PASSWORD_LDAP_USER_ERROR)));
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                            .put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_RESET_PASSWORD_SUPER_ADMIN)
                            .put(MESSAGE, String.format(ErrorMessageConstants.RESET_PASSWORD_FAILED, ErrorMessageConstants.RESET_PASSWORD_SYSTEM_USER_ERROR)));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.USER_NOT_FOUND));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void reset2FA(RoutingContext routingContext)
    {
        try
        {
            var user = UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (user != null && !user.isEmpty())
            {
                Bootstrap.configDBService().update(TBL_USER,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, CommonUtil.getLong(user.getLong(ID))),
                        new JsonObject().put(GARBAGE_FIELDS, new JsonArray().add(USER_TOTP_KEY)),
                        user.getString(USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                UserConfigStore.getStore().updateItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

                                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_ACCESS_TOKEN.name()).put(ID, user.getLong(ID)));

                                this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED)
                                        .put(RESPONSE_CODE, SC_OK).put(MESSAGE, String.format(InfoMessageConstants.RESET_2FA_SUCCEEDED, user.getString(User.USER_NAME))).put(ID, CommonUtil.getLong(routingContext.request().getParam(ID))));
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                                        .put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(MESSAGE, String.format(ErrorMessageConstants.RESET_MFA_FAILED, result.cause())));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, ErrorMessageConstants.USER_NOT_FOUND));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        alterParameters(routingContext, CommonUtil.getEntitySchema(endpoint));

        if (assertPasswordPolicy(routingContext, promise, APIConstants.REQUEST_CREATE))
        {
            promise.complete(routingContext.body().asJsonObject().put(USER_PASSWORD_LAST_UPDATED_TIME, System.currentTimeMillis()).put(User.USER_PREFERENCES, new JsonObject()));
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var success = true;

        try
        {
            alterParameters(routingContext, CommonUtil.getEntitySchema(endpoint));

            var item = UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item != null && !item.isEmpty())
            {
                var requestBody = routingContext.body().asJsonObject();

                if (USER_TYPE_SYSTEM.equalsIgnoreCase(item.getString(USER_TYPE)))
                {

                    if (requestBody.getString(USER_PASSWORD) != null && !requestBody.getString(USER_PASSWORD).isEmpty()
                            && !assertPasswordPolicy(routingContext, promise, APIConstants.REQUEST_UPDATE))
                    {
                        success = false;
                    }

                    if (requestBody.getString(USER_PASSWORD) != null && !requestBody.getString(USER_PASSWORD).equalsIgnoreCase(item.getString(USER_PASSWORD)))
                    {
                        requestBody.put(User.USER_PASSWORD_LAST_UPDATED_TIME, System.currentTimeMillis());
                    }

                    requestBody.remove(USER_OLD_PASSWORD); // no need to save user.old.password in database
                }
                else if (USER_TYPE_SSO.equalsIgnoreCase(item.getString(USER_TYPE)))
                {
                    var iterator = requestBody.iterator();

                    while (iterator.hasNext())
                    {
                        var requestParameter = iterator.next();

                        if (requestParameter.getKey().equalsIgnoreCase(USER_PASSWORD) || requestParameter.getKey().equalsIgnoreCase(USER_NAME) || requestParameter.getKey().equalsIgnoreCase(USER_STATUS))
                        {
                            iterator.remove();
                        }
                    }

                    requestBody.put(USER_TYPE, item.getString(USER_TYPE));
                }
                else
                {
                    var iterator = requestBody.iterator();

                    while (iterator.hasNext())
                    {
                        var requestParameter = iterator.next();

                        if (!(requestParameter.getKey().equalsIgnoreCase(USER_ROLE) || (requestParameter.getKey().equalsIgnoreCase(USER_PREFERENCES)) || (requestParameter.getKey().equalsIgnoreCase(User.USER_AVATAR)) || requestParameter.getKey().equalsIgnoreCase(USER_GROUPS)))
                        {
                            iterator.remove();
                        }
                    }

                    requestBody.put(USER_TYPE, item.getString(USER_TYPE));
                }

                if (success)
                {
                    requestBody.put(USER_PREVIOUS_STATUS, UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).getString(USER_STATUS));

                    promise.complete(requestBody);
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                        .put(STATUS, GlobalConstants.STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(MESSAGE, ErrorMessageConstants.USER_NOT_FOUND));

                promise.fail(ErrorMessageConstants.USER_NOT_FOUND);
            }
        }
        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            APIUtil.sendResponse(exception, routingContext);
        }

        return promise.future();
    }

    private void updateAll(RoutingContext routingContext)
    {
        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var ids = requestBody.getJsonArray(REQUEST_PARAM_IDS);

            var iterator = ids.iterator();

            // filter out the default admin user
            while (iterator.hasNext())
            {
                var id = iterator.next();

                if (ENTITY_TYPE_SYSTEM.equalsIgnoreCase(UserConfigStore.getStore().getItem(CommonUtil.getLong(id)).getString(FIELD_TYPE)))
                {
                    iterator.remove();
                }
            }

            if (requestBody.containsKey(USER_GROUPS))
            {
                var userGroups = requestBody.getJsonArray(USER_GROUPS);

                var futures = new ArrayList<Future<Void>>();

                for (var id : ids)
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    var oldGroups = UserConfigStore.getStore().getItem(CommonUtil.getLong(id)).getJsonArray(USER_GROUPS, new JsonArray());

                    if (!oldGroups.isEmpty())
                    {
                        for (var group : userGroups)
                        {
                            if (!oldGroups.contains(group))
                            {
                                oldGroups.add(group);
                            }
                        }
                    }

                    Bootstrap.configDBService().update(TBL_USER,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, id),
                            new JsonObject().put(USER_GROUPS, oldGroups.isEmpty() ? userGroups : oldGroups),
                            routingContext.user().principal().getString(USER_NAME),
                            routingContext.request().remoteAddress().host(), result ->
                            {
                                if (result.succeeded() && !result.result().isEmpty())
                                {
                                    UserConfigStore.getStore().updateItem(CommonUtil.getLong(id)).onComplete(future -> promise.complete());
                                }
                                else
                                {
                                    promise.fail(result.cause());
                                }
                            });
                }

                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                .put(REQUEST_PARAM_IDS, ids)
                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.USER.getName())));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, APIConstants.Entity.USER.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                    }
                });
            }
            else
            {
                Bootstrap.configDBService().updateAll(TBL_USER,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, ids),
                        requestBody,
                        routingContext.user().principal().getString(USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                UserConfigStore.getStore().updateItems(result.result()).onComplete(future ->
                                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                                .put(REQUEST_PARAM_IDS, ids)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.USER.getName()))));
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                        .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, APIConstants.Entity.USER.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<Void> beforeDeleteAll(RoutingContext routingContext)
    {

        var requestBody = routingContext.body().asJsonObject();

        var ids = requestBody.getJsonArray(REQUEST_PARAM_IDS);

        var iterator = ids.iterator();

        // filter out the default admin user and ldap users
        while (iterator.hasNext())
        {
            var item = UserConfigStore.getStore().getItem(CommonUtil.getLong(iterator.next()));

            if (ENTITY_TYPE_SYSTEM.equalsIgnoreCase(item.getString(FIELD_TYPE)) || USER_TYPE_LDAP.equalsIgnoreCase(item.getString(USER_TYPE)))
            {
                iterator.remove();
            }
        }

        routingContext.setBody(Buffer.buffer(requestBody.toString()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDeleteAll(JsonObject response, RoutingContext routingContext)
    {
        var ids = response.getJsonArray(REQUEST_PARAM_IDS);

        for (var index = 0; index < ids.size(); index++)
        {
            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                    .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_ACCESS_TOKEN.name()).put(ID, ids.getLong(index)));
        }

        return super.afterDeleteAll(response, routingContext);
    }

    private boolean assertPasswordPolicy(RoutingContext routingContext, Promise<JsonObject> promise, String requestType)
    {
        var success = false;

        try
        {
            var item = PasswordPolicyConfigStore.getStore().getItem();

            if (item != null && !item.isEmpty())
            {
                if (validate(routingContext.body().asJsonObject().getString(USER_PASSWORD), item))
                {
                    if (requestType.equalsIgnoreCase(APIConstants.REQUEST_UPDATE) && routingContext.body().asJsonObject().containsKey(USER_OLD_PASSWORD))
                    {
                        if (!routingContext.body().asJsonObject().getString(USER_OLD_PASSWORD).equalsIgnoreCase(UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).getString(User.USER_PASSWORD)))
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                                    .put(STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INVALID_CREDENTIALS)
                                    .put(MESSAGE, ErrorMessageConstants.INCORRECT_OLD_PASSWORD));

                            promise.fail(ErrorMessageConstants.INCORRECT_OLD_PASSWORD);
                        }

                        else
                        {
                            success = true;
                        }
                    }

                    else
                    {
                        success = true;
                    }
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_PASSWORD_POLICY_NOT_SATISFIED)
                            .put(MESSAGE, ErrorMessageConstants.PASSWORD_POLICY_NOT_SATISFIED));

                    promise.fail(ErrorMessageConstants.PASSWORD_POLICY_NOT_SATISFIED);
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                        .put(STATUS, GlobalConstants.STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(MESSAGE, ErrorMessageConstants.PASSWORD_POLICY_NOT_FOUND));

                promise.fail(ErrorMessageConstants.PASSWORD_POLICY_NOT_FOUND);
            }
        }

        catch (Exception exception)
        {
            promise.fail(exception.getMessage());

            APIUtil.sendResponse(exception, routingContext);
        }

        return success;
    }

    private boolean validate(String password, JsonObject item)
    {
        var validPassword = false;

        try
        {

            var patternBuilder = new StringBuilder("^");

            if (item.getString(PasswordPolicy.PASSWORD_POLICY_UPPERCASE_CHECK).equalsIgnoreCase(YES))
            {
                patternBuilder.append("(?=.*[A-Z])");
            }
            if (item.getString(PasswordPolicy.PASSWORD_POLICY_LOWERCASE_CHECK).equalsIgnoreCase(YES))
            {
                patternBuilder.append("(?=.*[a-z])");
            }
            if (item.getString(PasswordPolicy.PASSWORD_POLICY_SPECIAL_CHARACTER_CHECK).equalsIgnoreCase(YES))
            {
                patternBuilder.append("(?=.*[!@#$%^&*+=])");
            }
            if (item.getString(PasswordPolicy.PASSWORD_POLICY_NUMBER_CHECK).equalsIgnoreCase(YES))
            {
                patternBuilder.append("(?=.*[0-9])");
            }

            patternBuilder.append(".{").append(item.getInteger(PasswordPolicy.PASSWORD_POLICY_MINIMUM_LENGTH)).append(",}");

            validPassword = password.matches(patternBuilder.append("$").toString());
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return validPassword;
    }

    private void generateToken(RoutingContext routingContext)
    {
        try
        {
            var requestBody = routingContext.body().asJsonObject();

            var validity = 24 * 60;

            if (requestBody.getString(PERSONAL_ACCESS_TOKEN_VALIDITY).equalsIgnoreCase("30 days"))
            {
                validity = validity * 30;
            }
            else if (requestBody.getString(PERSONAL_ACCESS_TOKEN_VALIDITY).equalsIgnoreCase("90 days"))
            {
                validity = validity * 90;
            }
            else if (requestBody.getString(PERSONAL_ACCESS_TOKEN_VALIDITY).equalsIgnoreCase("180 days"))
            {
                validity = validity * 180;
            }
            else if (requestBody.getString(PERSONAL_ACCESS_TOKEN_VALIDITY).equalsIgnoreCase("365 days"))
            {
                validity = validity * 365;
            }
            else if (requestBody.getString(PERSONAL_ACCESS_TOKEN_VALIDITY).equalsIgnoreCase("Never Expires"))
            {
                validity = validity * 365 * 20;
            }

            var item = UserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item.containsKey(User.USER_ROLE) && CommonUtil.getLong(item.getValue(User.USER_ROLE)) > DUMMY_ID)
            {
                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_PAT_CREATE, requestBody.put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_USER, CommonUtil.getLong(routingContext.request().getParam(ID))).put(PERSONAL_ACCESS_TOKEN_VALIDITY, validity), reply ->
                {
                    if (reply.succeeded())
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                .put(RESULT, reply.result().body().getString(PERSONAL_ACCESS_TOKEN)));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                .put(GlobalConstants.STATUS, STATUS_FAIL)
                                .put(MESSAGE, reply.cause().getMessage()).put(ERROR, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));
                    }
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, STATUS_FAIL)
                        .put(MESSAGE, String.format("Please assign role to the user : %s ", item.getString(User.USER_NAME))).put(ERROR, ErrorCodes.ERROR_CODE_INTERNAL_ERROR));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

}
