{"127.0.0.1": {"218": {"metadata.fields": {"name": "lb", "type": "loadbalance"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500114, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Load Balancer", "metric.object": ***************, "metric.plugin": "azureloadbalancer", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure Load Balancer", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.758 pm 14/06/2022", "object.custom.fields": {"***************": "loadbalance", "***************": "lb"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 50, "object.name": "lb(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.state": "ENABLE", "object.target": "lb(cloud-shell-storage-centralindia)", "object.type": "Azure Load Balancer", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 218, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"218": {"metadata.fields": {"name": "lb", "type": "loadbalance"}}, "azure.etag": "W/\"39da8c6c-91ba-47b7-ace6-45fbfca0dffe\"", "azure.loadbalancer.availability": 100, "azure.loadbalancer.bytes.rate": 27992, "azure.loadbalancer.dip.availability": 100, "azure.loadbalancer.packets.rate": 185, "azure.loadbalancer.snat.connections": 13, "azure.loadbalancer.snat.ports": 1024, "azure.loadbalancer.syn.packets": 7, "azure.loadbalancer.used.snat.ports": 0, "azure.location": "centralindia", "azure.name": "lb", "azure.sku.name": "Standard", "azure.status": "Succeeded", "azure.type": "Microsoft.Network/loadBalancers"}, "status": "succeed", "timeout": 60}}