package com.mindarray.visualization;

import java.util.concurrent.*;

public class demo {
    public static void main(String[] args) throws ExecutionException, InterruptedException {

        SumCalculator sumCalculator = new SumCalculator(10);

        ExecutorService executor = Executors.newFixedThreadPool(2);

        Runnable logger = new NumberLogger(10);
        Future<?> loggerFuture = executor.submit(logger);

        Callable<Integer> calculator = new SumCalculator(10);
        Future<Integer> sumFuture = executor.submit(calculator);

        loggerFuture.get();

        System.out.println("Printing callable....");

        System.out.println("--------" + sumFuture.get());

        executor.shutdown();

    }

    static class NumberLogger implements Runnable {

        private final int number;

        NumberLogger(int number) {
            this.number = number;
        }

        @Override
        public void run() {
            System.out.println(number);
        }
    }

    static class SumCalculator implements Callable<Integer> {

        private final int number;

        SumCalculator(int number) {
            this.number = number;
        }

        @Override
        public Integer call() throws Exception {

            Thread.sleep(3 * 1000);

            return number * (number - 1) / 2;
        }
    }
}
