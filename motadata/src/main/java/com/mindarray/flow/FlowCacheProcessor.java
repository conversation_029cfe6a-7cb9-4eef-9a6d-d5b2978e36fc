/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  25-Mar-2025     Smit Prajapati           MOTADATA-5435: Flow back-pressure mechanism.
 */

package com.mindarray.flow;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.FlowSettingsConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.LinkedList;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.FLOW_CACHE_DIRECTORY_PATH;
import static com.mindarray.eventbus.EventBusConstants.EVENT_VOLUME_BYTES;

/**
 * Manages the caching of flow data to improve performance and handle high volumes of flow information.
 * <p>
 * The FlowCacheProcessor is responsible for efficiently managing flow data in memory and on disk.
 * It implements mechanisms for storing, retrieving, and processing flow data to handle high volumes
 * of network traffic information without overwhelming system resources.
 * <p>
 * Key responsibilities of this class include:
 * <ul>
 *   <li>Initializing and managing flow cache files on disk</li>
 *   <li>Buffering flow data in memory before writing to disk</li>
 *   <li>Processing cached flow data in batches</li>
 *   <li>Monitoring flow rates and ensuring they stay within license limits</li>
 *   <li>Implementing efficient data structures for flow data storage</li>
 *   <li>Managing the lifecycle of flow cache files</li>
 * </ul>
 * <p>
 * The FlowCacheProcessor works closely with other components in the flow package:
 * <ul>
 *   <li>It receives flow data from the FlowProcessor</li>
 *   <li>It provides cached data to the FlowStatCalculator for statistical analysis</li>
 *   <li>It interacts with the license system to ensure flow processing stays within licensed limits</li>
 * </ul>
 * <p>
 * This class is deployed as a Vert.x verticle and integrates with the event bus system
 * for communication with other components.
 */
public class FlowCacheProcessor extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlowCacheProcessor.class, GlobalConstants.MOTADATA_FLOW, "Flow Cache Processor");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormat.forPattern("yyyyMMddHHmm");
    private static final int FLOW_RATE = MotadataConfigUtil.getFlowRate();
    private final LinkedList<String> cacheItems = new LinkedList<>();
    private final AtomicLong flowRate = new AtomicLong(0);
    private long tick = 0L;
    private int flows = 0;
    private BufferedReader reader = null;
    private String cacheItem = null;
    private long aggregationTimer;
    //periodic timer
    private long processorTimerId = -1L;
    private long queryTimerId = -1L;

    /**
     * Initializes the flow cache processor and sets up periodic timers and event handlers.
     * <p>
     * This method performs the following initialization tasks:
     * <ul>
     *   <li>Sets up a periodic timer to monitor and reset flow rate counters every second</li>
     *   <li>Retrieves the flow aggregation timer setting from the configuration</li>
     *   <li>Sets up an event bus handler for flow cache cleanup events</li>
     *   <li>Sets up an event bus handler for flow quota limit reset events</li>
     *   <li>Initializes the flow cache processing system</li>
     * </ul>
     * <p>
     * The method configures the flow cache processor to handle flow data efficiently,
     * with rate limiting to ensure that flow processing stays within the configured
     * flow events per second (EPS) limit.
     * <p>
     * It also sets up handlers to respond to system events such as cache cleanup
     * requests and quota limit resets, ensuring proper integration with the rest
     * of the system.
     *
     * @param promise the promise to complete when initialization is finished
     */
    @Override
    public void start(Promise<Void> promise)
    {
        LOGGER.info("Flow EPS Configured: " + FLOW_RATE);

        vertx.setPeriodic(1000, id ->
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("Flows Per Sec: " + flowRate.get());
            }

            flowRate.set(0);
        });

        if (FlowSettingsConfigStore.getStore().getItems().isEmpty())
        {
            aggregationTimer = MotadataConfigUtil.getFlowEngineAggregationTimerMinutes() + 1;
        }
        else
        {
            aggregationTimer = FlowSettingsConfigStore.getStore().getFlowEngineAggregationTimerMinutes() + 1;
        }

        /*
         *  when Flow Cleanup Job Executes, which runs daily to delete outdated files from the "flow-cache" directory.
         *   we will remove files form fileQueues, to ensure not any unnecessary files are stored in out cache list
         * */
        vertx.eventBus().<JsonArray>localConsumer(EventBusConstants.EVENT_FLOW_CACHE_CLEANUP, message ->
        {

            var items = message.body();

            LOGGER.info("event received to remove files from cache, files to delete: " + items);

            if (items != null && !items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    if (!items.getString(index).equalsIgnoreCase(cacheItem))
                    {
                        cacheItems.remove(items.getString(index));
                    }
                }
            }
        });

        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_FLOW_QUOTA_LIMIT_RESET, message -> init());

        init();

        promise.complete();
    }


    /**
     * The `init` method is responsible for continuously reading and processing files
     * from a predefined directory at regular intervals. The files contain flow data,
     * and each line in the file represents an individual flow record.
     * <p>
     * ## Execution Flow:
     * 1. Runs every second (`setPeriodic(1000, handler -> { ... })`) to check for new or ongoing file processing.
     * 2. Prevents concurrent execution by using the `isProcessing` flag, ensuring only one instance runs at a time.
     * 3. If no file is currently being processed (`file == null || fileReader == null`), it attempts to load a new file using the `load()` method.
     * 4. Processes files line by line, up to a defined `FPS` limit per execution cycle.
     * 5. Ensures that the flow data does not exceed a defined quota limit (`LicenseUtil.updateUsedFlowQuota(line.length())`).
     * 6. Parses each line into a JSON object (`new JsonObject(line)`) and forwards it to the event bus.
     * 7. Maintains a count of processed rows for each file and tracks processing time using `fileTimers`.
     * 8. Once the end of the file is reached, it closes the file and removes tracking references.
     * 9. Any errors encountered during processing are logged, ensuring traceability.
     * 10. Finally, the `isProcessing` flag is reset to `false`, allowing the next execution cycle to proceed.
     */
    private void init()
    {

        if (queryTimerId == -1L)
        {
            queryTimerId = vertx.setPeriodic(MotadataConfigUtil.getFlowCacheQueryTimerSeconds() * 1000, id ->
            {
                if (Bootstrap.vertx().fileSystem().existsBlocking(FLOW_CACHE_DIRECTORY_PATH))
                {
                    Bootstrap.vertx().fileSystem().readDir(FLOW_CACHE_DIRECTORY_PATH, result ->
                    {
                        if (result.succeeded())
                        {
                            var currentDateTime = TIME_FORMATTER.parseDateTime(TIME_FORMATTER.print(DateTime.now()));

                            for (var item : result.result())
                            {
                                // Extract the timestamp (last 12 characters in yyyyMMddHHmm format)
                                var time = TIME_FORMATTER.parseDateTime(item.substring(item.length() - 12));

                                // Ensure the file is not already processed and has fully written
                                if ((cacheItem == null || !cacheItem.equalsIgnoreCase(item)) && !cacheItems.contains(item) && currentDateTime.isAfter(time) && (currentDateTime.getMillis() - time.getMillis() >= aggregationTimer * 60 * 1000))
                                {

                                    if (CommonUtil.traceEnabled())
                                    {
                                        LOGGER.trace("adding new file: " + item);

                                    }

                                    // Add the file to the front of the queue
                                    cacheItems.addFirst(item);
                                }
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());
                        }
                    });
                }
            });
        }

        if (processorTimerId == -1L)
        {
            processorTimerId = vertx.setPeriodic(1000, id ->
            {
                try
                {
                    // Load a new file if none is currently being processed
                    if (cacheItem == null || reader == null)
                    {
                        //Initializes `file` and `fileReader`
                        load();

                        // Store the start time of file processing (if not already set)
                        tick = System.currentTimeMillis();

                        // Track the number of rows processed for this file
                        flows = 0;
                    }

                    if (reader != null)
                    {
                        var flowRate = 0;

                        String line = null;

                        // Read up to `FPS` lines from the file
                        while (flowRate < FLOW_RATE && (line = reader.readLine()) != null)
                        {
                            // Check if processing exceeds the allowed flow quota
                            if (!LicenseUtil.updateUsedFlowQuota(line.length()))
                            {
                                LOGGER.warn("Quota exceeded, stopping processing");

                                vertx.cancelTimer(processorTimerId);

                                vertx.cancelTimer(queryTimerId);

                                processorTimerId = -1L;

                                queryTimerId = -1L;

                                // Stop processing if quota is exceeded
                                break;
                            }

                            JsonObject flow = null;

                            try
                            {
                                flow = new JsonObject(line);
                            }
                            catch (Exception exception)
                            {
                                // Ignore parsing exceptions (invalid JSON lines are skipped)
                            }

                            // If no more lines to read, close file processing
                            if (flow != null)
                            {
                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_FLOW,
                                        flow.put(EVENT_VOLUME_BYTES, line.getBytes().length));
                            }

                            this.flowRate.incrementAndGet();

                            flowRate++;

                            flows++;
                        }

                        if (line == null)
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("End of file reached: " + cacheItem);

                                LOGGER.trace("time taken to process file: " + cacheItem + " with rows " + flows + " in ms: " + (System.currentTimeMillis() - tick));
                            }


                            // Close the file reader and cleanup
                            close();
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }


    }

    /**
     * The `load` method is responsible for switching to a new file for processing.
     * It picks the next available file from `fileQueues`, initializes a `BufferedReader`
     * to read the file, and prepares it for processing.
     * <p>
     * ## Execution Flow:
     * 1. Close Current File (if any):
     * - Calls `close()` to ensure that any previously opened file is properly closed.
     * - This prevents resource leaks and ensures the system does not read an outdated file.
     * 2. Check for Available Files:
     * - If `fileQueues` is empty, meaning there are no files left to process, the method returns immediately.
     * 3. Retrieve the Next File:
     * - Retrieves and removes the first file from `fileQueues` (FIFO order).
     * - Logs the file switch for debugging and traceability.
     * 4. Initialize File Reader:
     * - Attempts to open the retrieved file using `BufferedReader(new FileReader(file))`.
     * - If successful, the file is ready for reading and processing.
     * 5. Handle File Opening Failure:
     * - If an exception occurs (e.g., file not found, permission issue), it logs the error.
     * - The `file` variable is set to `null` to indicate that loading failed, preventing further processing on an invalid file.
     */
    private void load()
    {
        // Ensure any currently open file is properly closed before switching to a new one
        close();

        if (cacheItems.isEmpty())
        {
            return;
        }

        // Retrieve and remove the first file from the queue
        cacheItem = cacheItems.removeFirst();

        if (CommonUtil.traceEnabled())
        {

            LOGGER.trace("Switching to new file: " + cacheItem);
        }

        try
        {
            reader = new BufferedReader(new FileReader(cacheItem));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            cacheItem = null;
        }
    }

    /**
     * The `close` method is responsible for safely closing the currently open file and
     * deleting it from the file system once processing is complete.
     * <p>
     * ## Execution Flow:
     * 1. Check if a File is Open:
     * - If `fileReader` is `null`, it means no file is currently being read, so the method does nothing.
     * 2. Attempt to Close the File:
     * - Calls `fileReader.close()` inside a `try` block to release system resources.
     * - Logs a trace message indicating the file has been closed successfully.
     * 3. Delete the File from the File System:
     * - If `file` is not `null`, it retrieves its reference.
     * - Uses Vert.x asynchronous file system API to delete the file.
     * - Logs a success message if the file is deleted, or logs an error if deletion fails.
     * 4. Exception Handling:
     * - Catches any exceptions that may occur during file closing or deletion and logs the error.
     * 5. Cleanup in the `finally` Block:
     * - Ensures `fileReader` and `file` are set to `null` to free references and prevent accidental reuse.
     */
    private void close()
    {
        if (reader != null)
        {
            try
            {
                reader.close();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("Closed file: " + cacheItem);
                }

                if (cacheItem != null)
                {
                    var file = this.cacheItem;

                    Bootstrap.vertx().fileSystem().deleteBlocking(file);

                    LOGGER.trace("Deleted file: " + file);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
            finally
            {
                reader = null;

                cacheItem = null;
            }
        }
    }
}
