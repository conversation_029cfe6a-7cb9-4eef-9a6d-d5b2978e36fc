{"************": {"errors": [], "metric.timeout": 10, "object.ip": "************", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"vrf": [{"vrf": "A-1", "vrf.distribution.protocol": "B", "vrf.interface": "1", "vrf.row.status": "Not In Service", "vrf.status": "Up", "vrf.storage.type": "Permanent", "vrf.tag": 0}]}, "snmp.check.retries": 1, "snmp.community": "public", "snmp.version": "v2c"}, "**********": {"_type": "1", "credential.profile.name": "SNMP-Test-1654844603127", "credential.profile.protocol": "SNMP V3", "discovery.category": "Network", "discovery.context": {"interface.discovery": "yes", "ping.check.status": "yes", "port": 161}, "discovery.credential.profiles": [**************], "discovery.discovered.objects": 1, "discovery.event.processors": [58829800587748], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "SNMP-Test1654844603129", "discovery.object.type": "SNMP Device", "discovery.progress": 100, "discovery.status": "Last ran at 12:33:27.148 pm 10/06/2022", "discovery.target": "**********", "discovery.target.name": "**********", "discovery.total.objects": 1, "discovery.type": "ip.address", "errors": [], "event.id": **************, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": **************, "interface.discovery": "yes", "metric.category": "Network", "metric.credential.profile": **************, "metric.credential.profile.protocol": "SNMP V3", "metric.discovery.method": "REMOTE", "metric.name": "Virtual Routing & Forwarding (VRF)", "metric.object": **************, "metric.plugin": "vrf", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Switch", "object.business.hour.profile": **************, "object.category": "Network", "object.creation.time": "12:55:40.986 pm 10/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, **************, **************], "object.host": "cisco_core.motadata.local", "object.id": 9, "object.ip": "**********", "object.make.model": "Cisco Catalyst 93xx Switch Stack", "object.name": "cisco_core.motadata.local", "object.snmp.device.catalog": **************, "object.state": "ENABLE", "object.system.oid": ".*******.*******.2494", "object.target": "**********", "object.type": "Switch", "object.vendor": "Cisco Systems", "ping.check.status": "yes", "plugin.engine": "go", "plugin.id": 178, "port": 161, "remote.event.processor.uuid": "05858be5-be34-4ab7-a107-092aaa2d925a", "result": {"vrf": [{"vrf": "Mgmt-vrf", "vrf.distribution.protocol": "\u0000", "vrf.interface": "1", "vrf.row.status": "Not In Service", "vrf.status": "Down", "vrf.storage.type": "Permanent", "vrf.tag": 0}]}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "status": "succeed", "timeout": 60}}