/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.MACScanner;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MACScannerConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(MACScannerConfigStore.class, GlobalConstants.MOTADATA_STORE, "MAC Scanner Config Store");

    // 1 million records = 100-120 mb memory
    private static final Map<String, String> itemsByAddress = new ConcurrentHashMap<>(); // key -> mac address and value -> monitor ip

    // 1 million records = 100-120 mb memory
    private static final Map<String, String> itemsByIPAddress = new ConcurrentHashMap<>(); // key -> monitor/interface ip and value -> monitor ip

    private static final MACScannerConfigStore STORE = new MACScannerConfigStore();

    private MACScannerConfigStore()
    {
        super(DBConstants.TBL_MAC_SCANNER, LOGGER, false);
    }

    public static MACScannerConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_MAC_SCANNER,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            items.clear();

                            itemsByAddress.clear();

                            itemsByIPAddress.clear();

                            if (result.result() != null && !result.result().isEmpty())
                            {
                                for (var index = 0; index < result.result().size(); index++)
                                {
                                    var item = result.result().getJsonObject(index);

                                    items.put(item.getLong(GlobalConstants.ID), item);

                                    if (CommonUtil.debugEnabled())
                                    {
                                        LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                                    }

                                    itemsByAddress.put(item.getString(MACScanner.MAC_SCANNER_ADDRESS), item.getString(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS));

                                    if (item.getString(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS) != null)
                                    {
                                        itemsByIPAddress.put(item.getString(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS), item.getString(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS));
                                    }
                                }
                            }

                            promise.complete();

                            LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                            promise.fail(result.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    @Override
    public Future<Void> addItem(long id)
    {
        return super.addItem(id).onComplete(result -> update(id));
    }

    @Override
    public void deleteItem(long id)
    {
        itemsByAddress.remove(super.getItem(id).getString(MACScanner.MAC_SCANNER_ADDRESS));

        super.deleteItem(id);
    }

    @Override
    public Future<Void> updateItem(long id)
    {
        return super.updateItem(id).onComplete(result -> update(id));
    }

    @Override
    public Future<Void> updateItems(JsonArray ids)
    {
        return super.updateItems(ids).onComplete(result -> ids.forEach(id -> update(CommonUtil.getLong(id))));
    }

    private void update(long id)
    {
        var item = super.getItem(id);

        itemsByAddress.put(item.getString(MACScanner.MAC_SCANNER_ADDRESS), item.getString(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS));

        if (item.getString(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS) != null)
        {
            itemsByIPAddress.put(item.getString(MACScanner.MAC_SCANNER_INTERFACE_IP_ADDRESS), item.getString(MACScanner.MAC_SCANNER_DEVICE_IP_ADDRESS));
        }
    }

    public String getItemByMACAddress(String macAddress)
    {
        return itemsByAddress.getOrDefault(macAddress, null);
    }

    public String getItemByIPAddress(String ipAddress)
    {
        return itemsByIPAddress.getOrDefault(ipAddress, null);
    }
}
