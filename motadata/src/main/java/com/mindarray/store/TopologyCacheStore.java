/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author			Notes
 *  24-Jun-2025     <PERSON><PERSON>     MOTADATA-6528 : added parents set to maintain seed IPs that ran through topology scheduler
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Scheduler;
import com.mindarray.api.TopologyPlugin;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.impl.ConcurrentHashSet;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class TopologyCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(TopologyCacheStore.class, GlobalConstants.MOTADATA_STORE, "Topology Cache Store");

    private static final TopologyCacheStore STORE = new TopologyCacheStore();

    private final Map<String, JsonObject> items = new ConcurrentHashMap<>();

    private final Map<Long, JsonObject> runningTopologies = new ConcurrentHashMap<>();

    private final Map<Long, JsonObject> dependencies = new ConcurrentHashMap<>(); // used to maintain, filter out invalid dependencies

    private final Map<Long, JsonObject> connectedLinks = new ConcurrentHashMap<>(); // used to hold connections based on destination

    private final Set<String> parents = new ConcurrentHashSet<>(); // this set is used to maintain the seed IPs that ran through topology scheduler

    private TopologyCacheStore()
    {
    }

    public static TopologyCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
        {
            promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
        }

        else
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {

                try
                {
                    var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + "topology-config.db");

                    if (buffer != null && buffer.length() > 0)
                    {
                        var items = new JsonObject(new CipherUtil().decrypt(buffer.toString()));

                        if (!items.isEmpty())
                        {
                            for (var item : items)
                            {
                                var context = new JsonObject();

                                JsonObject.mapFrom(item.getValue()).forEach(entry -> update(context, entry));

                                if (!item.getKey().equalsIgnoreCase(NMSConstants.GENERIC_VENDOR))
                                {
                                    for (var entry : items.getJsonObject(NMSConstants.GENERIC_VENDOR))
                                    {
                                        if (!context.containsKey(entry.getKey()))
                                        {
                                            update(context, entry);
                                        }
                                    }
                                }

                                this.items.put(item.getKey(), context);
                            }

                            future.complete();
                        }
                        else
                        {
                            future.fail("failed to load topology config db...");
                        }
                    }
                    else
                    {
                        future.fail("failed to load topology config db...");
                    }
                }
                catch (Exception exception)
                {
                    future.fail(exception.getMessage());

                    LOGGER.error(exception);
                }

            }, result ->
            {

                if (result.succeeded())
                {
                    // Load seed IPs from scheduler config store having scheduler job type as TOPOLOGY & add it to parents set
                    SchedulerConfigStore.getStore().items.values().stream()
                            .filter(item -> item.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.TOPOLOGY.getName())
                                    && item.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS) != null)
                            .forEach(item -> item.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS)
                                    .forEach(entry -> parents.add(ObjectConfigStore.getStore().getItem(CommonUtil.getLong(entry)).getString(AIOpsObject.OBJECT_IP))));

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

                    promise.complete();
                }

                else
                {
                    promise.fail(result.cause());
                }
            });
        }

        return promise.future();
    }

    private void update(JsonObject context, Map.Entry<String, Object> entry)
    {
        try
        {
            var item = JsonObject.mapFrom(entry.getValue());

            if (item.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT))
            {
                item.mergeIn(item.getJsonObject(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT));

                item.remove(TopologyPlugin.TOPOLOGY_PLUGIN_CONTEXT);
            }

            var key = item.getString(TopologyPlugin.TOPOLOGY_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.TopologyPluginType.CUSTOM.getName()) ? PluginEngineConstants.SCRIPT : PluginEngineConstants.PARSING_SCRIPT;

            var file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.PLUGIN_SCRIPT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.TOPOLOGY_DIR + GlobalConstants.PATH_SEPARATOR + item.getString(key);

            if (Bootstrap.vertx().fileSystem().existsBlocking(file))
            {
                item.put(key, CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(file)).trim());

                context.put(entry.getKey(), item);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public JsonObject getItems(String vendor)
    {
        return items.containsKey(vendor) ? items.get(vendor).copy() : items.get(NMSConstants.GENERIC_VENDOR).copy();
    }

    public void startTopology(long id)
    {
        runningTopologies.computeIfAbsent(id, value -> new JsonObject().put(EventBusConstants.EVENT_ID, CommonUtil.newEventId()).put(EventBusConstants.EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));

        dependencies.putIfAbsent(id, new JsonObject());

        connectedLinks.putIfAbsent(id, new JsonObject());
    }

    public boolean topologyRunning(long id)
    {
        return runningTopologies.containsKey(id);
    }

    public long getEventId(long id)
    {
        return runningTopologies.get(id).getLong(EventBusConstants.EVENT_ID);
    }

    public long getTimeStamp(long id)
    {
        return runningTopologies.get(id).getLong(EventBusConstants.EVENT_TIMESTAMP);
    }

    public void completeTopology(long id)
    {
        runningTopologies.remove(id);

        dependencies.remove(id);

        connectedLinks.remove(id);
    }

    public void updateDependency(long id, String dependency, String interfaceName)
    {
        dependencies.get(id).put(dependency, interfaceName);
    }

    public String getInterfaceByDependency(long id, String dependency)
    {
        return dependencies.get(id).getString(dependency);
    }

    public boolean existDependency(long id, String dependency)
    {
        return dependencies.get(id).containsKey(dependency);
    }

    public JsonObject getDependencies(long id)
    {
        return dependencies.get(id);
    }

    public void addConnectedLink(long id, String destination, String port)
    {
        connectedLinks.get(id).getMap().putIfAbsent(destination, new JsonArray());

        connectedLinks.get(id).getJsonArray(destination).add(port);
    }

    public JsonArray getConnectedLinks(long id, String destination)
    {
        return connectedLinks.get(id).getJsonArray(destination);
    }

    public void addParent(String parent)
    {
        parents.add(parent);
    }

    public void removeParent(String parent)
    {
        parents.remove(parent);
    }

    public boolean containsParent(String parent)
    {
        return parents.contains(parent);
    }
}
