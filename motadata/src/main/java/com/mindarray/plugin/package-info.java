/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/**
 * The plugin package provides core functionality for the Motadata plugin system, which enables
 * extensible monitoring and management capabilities across the platform.
 * <p>
 * This package contains the following key components:
 * <ul>
 *   <li>{@link com.mindarray.plugin.PluginEngine} - The central engine responsible for processing plugin requests,
 *       managing worker allocation, and handling event queuing and execution.</li>
 *   <li>{@link com.mindarray.plugin.PluginEngineConstants} - Constants, enums, and utility methods used throughout
 *       the plugin system, including request types, plugin engine types, and configuration parameters.</li>
 *   <li>{@link com.mindarray.plugin.PluginEngineResponseProcessor} - Processes responses from the plugin engine,
 *       routing them to appropriate handlers and performing necessary data transformations.</li>
 * </ul>
 * <p>
 * The plugin system architecture follows an event-driven model using Vert.x, where:
 * <ol>
 *   <li>Events are received through the event bus and queued by the PluginEngine</li>
 *   <li>Worker executors process events asynchronously based on their type and priority</li>
 *   <li>Responses are processed by the PluginEngineResponseProcessor</li>
 *   <li>Results are routed back through the event bus to appropriate handlers</li>
 * </ol>
 * <p>
 * The system supports various plugin types including network monitoring, configuration management,
 * compliance checking, and runbook automation. It also provides batch processing capabilities
 * for supported plugin types to optimize resource utilization.
 * <p>
 * This package is designed to be extensible, allowing new plugin types to be added with minimal
 * changes to the core engine components.
 */
package com.mindarray.plugin;