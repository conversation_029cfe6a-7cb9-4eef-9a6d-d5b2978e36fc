[{"_type": "0", "id": 10000000001708, "visualization.name": "Scalesets", "visualization.description": "Scalesets Azure Vm Scalset", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.instances", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.instances", "icon": {"name": "vm-scaleset", "placement": "prefix"}}, "header": {"title": "Scalesets", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.vmscaleset.instances.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001709, "visualization.name": "CPU", "visualization.description": "CPU Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.vmscaleset.cpu.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001710, "visualization.name": "Disk Throughput", "visualization.description": "Disk Throughput Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.bytes.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vmscaleset.disk.read.bytes.per.sec.avg"}, {"label": "Write", "value": "azure.vmscaleset.disk.write.bytes.per.sec.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001711, "visualization.name": "Disk IOPS", "visualization.description": "Disk IOPS Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.disk.write.ops.per.sec", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "Disk IOPS", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.vmscaleset.disk.read.ops.per.sec.avg"}, {"label": "Write", "value": "azure.vmscaleset.disk.write.ops.per.sec.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001712, "visualization.name": "Network", "visualization.description": "Network Azure Vm Scalset", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.network.in.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.network.out.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.network.in.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "In Bytes", "value": "azure.vmscaleset.network.in.bytes.avg"}, {"label": "Out Bytes", "value": "azure.vmscaleset.network.out.bytes.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001713, "visualization.name": "Flows", "visualization.description": "Flows Azure Vm Scalset", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.inbound.flows.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.outbound.flows.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.vmscaleset.outbound.flows.per.sec", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "Flows", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Inbound", "value": "azure.vmscaleset.inbound.flows.per.sec.avg"}, {"label": "Outbound", "value": "azure.vmscaleset.outbound.flows.per.sec.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001714, "visualization.name": "Top Scaleset By Region", "visualization.description": "Top Scaleset By Region Azure Vm Scalset", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": []}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": ""}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001715, "visualization.name": "Top Scaleset By CPU Utilization", "visualization.description": "Top Scaleset By CPU Utilization Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.cpu.percent", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.cpu.consumed.credits", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Scaleset", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.vmscaleset", "title": "Scaleset ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "azure.vmscaleset.cpu.percent.avg", "title": "CPU Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "azure.vmscaleset.cpu.consumed.credits.avg", "title": "Consumed Credits", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.cpu.percent.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001716, "visualization.name": "Top Scaleset with Maximum Inbound Flows", "visualization.description": "Top Scaleset with Maximum Inbound Flows Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.maximum.inbound.flows.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.inbound.flows", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Scaleset", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.vmscaleset", "title": "Scaleset ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "azure.vmscaleset.maximum.inbound.flows.per.sec.avg", "title": "Flow/Sec", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "azure.vmscaleset.inbound.flows.avg", "title": "Flows", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.inbound.flows.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001717, "visualization.name": "Top Scaleset By Disk Operations", "visualization.description": "Top Scaleset By Disk Operations Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.write.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.disk.read.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.disk.write.ops.per.sec.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001718, "visualization.name": "Top Scaleset with Maximum Outbound Flows", "visualization.description": "Top Scaleset with Maximum Outbound Flows Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.maximum.outbound.flows.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.outbound.flows", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Scaleset", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "azure.vmscaleset", "title": "Scaleset ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "azure.vmscaleset.maximum.outbound.flows.per.sec.avg", "title": "Flow/Sec", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "azure.vmscaleset.outbound.flows.avg", "title": "Flows", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.outbound.flows.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001719, "visualization.name": "Top Subscriptions By Disk Queue Length", "visualization.description": "Top Subscriptions By Disk Queue Length Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.disk.queue.length.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001720, "visualization.name": "Top Scaleset By Disk Throughput", "visualization.description": "Top Scaleset By Disk Throughput Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.disk.read.bytes.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.disk.write.bytes.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.disk.read.bytes.per.sec.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001721, "visualization.name": "Top Scaleset By Network Traffic", "visualization.description": "Top Scaleset By Network Traffic Azure Vm Scalse", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.vmscaleset.network.out.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.vmscaleset.network.in.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.vmscaleset.network.in.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]