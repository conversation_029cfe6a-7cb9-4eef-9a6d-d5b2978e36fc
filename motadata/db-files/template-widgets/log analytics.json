[{"id": 10000000100003, "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "event.severity", "operator": "=", "value": "Error"}]}]}, "result.filter": {}, "drill.down.filter": {}}, "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100004, "visualization.name": "Total Events", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100005, "visualization.name": "Total Log Volume", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "log.volume.bytes", "aggregator": "sum", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100006, "container.type": "Template", "visualization.name": "Error Log Trend", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "event.severity", "operator": "=", "value": "Error"}]}]}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100007, "visualization.name": "Total Events Trend", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100008, "visualization.name": "Total Log Volume Trend", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "log.volume.bytes", "aggregator": "avg", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}}, {"id": 10000000100009, "container.type": "Template", "visualization.name": "Logs By Severity", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["event.severity"], "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "message.count"}}}, "visualization.result.by": ["event.severity"]}, {"id": 10000000100010, "container.type": "Template", "visualization.name": "Logs By Event Category", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["event.category"], "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "message.count"}}}, "visualization.result.by": ["event.category"]}, {"id": 10000000100011, "container.type": "Template", "visualization.name": "Event Summary", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "log", "category": "log", "filters": {"data.filter": {}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": ["event.severity", "event.category"], "data.points": [{"data.point": "message", "aggregator": "count", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "message.count", "title": "Message Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "event.severity", "title": "Event Severity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "event.category", "title": "Event Category", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}]}}, "visualization.result.by": ["event.severity", "event.category"]}, {"id": 10000000100012, "container.type": "Template", "visualization.name": "Critical Log Event", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Grid", "visualization.data.sources": [{"type": "log", "join.type": "custom", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "event.severity", "operator": "in", "value": ["Critical", "Error", "Fatal", "Emergency", "<PERSON><PERSON>"]}]}]}, "result.filter": {}, "drill.down.filter": {}}, "visualization.result.by": [], "data.points": [{"data.point": "event.source", "aggregator": "", "entities": [], "entity.type": "event.source"}, {"data.point": "event.category", "aggregator": "", "entities": [], "entity.type": "event.source"}, {"data.point": "event.severity", "aggregator": "", "entities": [], "entity.type": "event.source"}, {"data.point": "message", "aggregator": "", "entities": [], "entity.type": "event.source"}]}], "visualization.properties": {"grid": {"visualization.grid.properties.required": "no", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "timestamp", "title": "Timestamp", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "event.source.value", "title": "Event Source", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "message.value", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "event.category.value", "title": "Event Category", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "event.severity.value", "title": "Event Severity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}]}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "join.type": "custom", "join.result": "log.event", "max.records": 500, "secondery.category": "event.history"}]