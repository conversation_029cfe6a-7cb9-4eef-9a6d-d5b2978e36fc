[{"_type": "0", "id": 10000000000441, "visualization.name": "CPU", "visualization.description": "Websphere CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.process.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "websphere.process.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "websphere.process.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "websphere.process.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000442, "visualization.name": "Memory", "visualization.description": "Websphere Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.heap.memory.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.heap.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.heap.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "websphere.heap.memory.size.bytes"}, {"type": "metric", "data.point": "websphere.heap.memory.free.bytes"}, {"type": "metric", "data.point": "websphere.heap.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "websphere.heap.memory.size.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "heap.memory.size.bytes", "value": "websphere.heap.memory.size.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "websphere.heap.memory.free.bytes.last"}, {"label": "Used", "value": "websphere.heap.memory.used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000443, "visualization.name": "Session", "visualization.description": "Websphere Session", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.servlet.created.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.live.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "websphere.servlet.invalidated.sessions"}, {"type": "metric", "data.point": "websphere.servlet.discarded.session"}, {"type": "metric", "data.point": "websphere.servlet.cache.discarded.sessions"}, {"type": "metric", "data.point": "websphere.servlet.affinity.broken.sessions"}, {"type": "metric", "data.point": "websphere.servlet.invalid.timedout.sessions"}, {"type": "metric", "data.point": "websphere.servlet.created.sessions"}, {"type": "metric", "data.point": "websphere.servlet.active.sessions"}, {"type": "metric", "data.point": "websphere.servlet.live.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "websphere.servlet.created.sessions", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Session", "style": {"font.size": "medium"}, "data.points": [{"label": "Servlet Created Sessions", "value": "websphere.servlet.created.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "websphere.servlet.active.sessions.last"}, {"label": "Live", "value": "websphere.servlet.live.sessions.last"}]}}}}, {"_type": "0", "id": 10000000000444, "visualization.name": "Requests", "visualization.description": "Websphere Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.hits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "websphere.hit.ratio.percent"}, {"type": "metric", "data.point": "websphere.hits"}, {"type": "metric", "data.point": "websphere.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "websphere.hit.ratio.percent", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": [{"label": "Hit Ratio Percent :", "value": "websphere.hit.ratio.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Hits", "value": "websphere.hits.last"}, {"label": "Requests", "value": "websphere.requests.last"}]}}}}, {"_type": "0", "id": 10000000000445, "visualization.name": "Pool Utilization", "visualization.description": "Websphere Pool Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.overall.jdbc.pool.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "websphere.overall.jdbc.pool.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "websphere.overall.jdbc.pool.used.percent", "icon": {"name": "utilization", "placement": "prefix"}}, "header": {"title": "Pool Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "websphere.overall.jdbc.pool.used.percent.avg", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000446, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000447, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000448, "visualization.name": "Session Statistics", "visualization.description": "Websphere Session Statistics", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.servlet.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.live.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.created.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.discarded.session", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000449, "visualization.name": "Session Details", "visualization.description": "Websphere Session Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.servlet.created.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.discarded.session", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.affinity.broken.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.invalid.timedout.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.session.lifetime.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.session.external.read.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.servlet.session.external.write.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "websphere.servlet.created.sessions.last", "title": "Created Session", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "websphere.servlet.discarded.session.last", "title": "Discarded Session", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "websphere.servlet.affinity.broken.sessions.last", "title": "Affinity Broken Session", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "websphere.servlet.invalid.timedout.sessions.last", "title": "Invalid <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "websphere.servlet.session.lifetime.ms.last", "title": "Lifetime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "websphere.servlet.session.external.read.time.ms.last", "title": "Read Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "websphere.servlet.session.external.write.time.ms.last", "title": "Write Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}, {"_type": "0", "id": 10000000000450, "visualization.name": "JDBC Pool Details", "visualization.description": "Websphere JDBC Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.jdbc.pool~created.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~allocated.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~fault.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~waiting.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~size", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~websphere.jdbc.free.pool.size", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~percent.used", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~managed.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.jdbc.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~created.connections.last", "title": "Created", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~allocated.connections.last", "title": "Allocated", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~fault.connections.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~waiting.threads.last", "title": "Waiting", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~size.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~websphere.jdbc.free.pool.size.last", "title": "Free Pool", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~percent.used.last", "title": "Pool %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~managed.connections.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.jdbc.pool~used.percent.last", "title": "JDBC Pool Used Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000451, "visualization.name": "Thread Pool Details", "visualization.description": "Websphere Thread Pool Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "websphere.thread.pool~created.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~active.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~destroyed.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~declared.hung.threads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~size", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~maxed.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "websphere.thread.pool~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~created.threads.last", "title": "Created", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~active.threads.last", "title": "Active", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~destroyed.threads.last", "title": "Destroyed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~declared.hung.threads.last", "title": "Declared <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~size.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~maxed.percent.last", "title": "Maxed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "websphere.thread.pool~used.percent.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]