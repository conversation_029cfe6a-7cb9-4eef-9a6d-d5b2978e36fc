/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.visualization;


import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_VISUALIZATION;
import static com.mindarray.eventbus.EventBusConstants.UI_EVENT_UUID;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationMetricResponseProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(VisualizationMetricResponseProcessor.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Metric Response Processor");

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_METRIC_RESPONSE_PROCESSOR, message ->
            {
                var event = message.body();

                if (!event.isEmpty())
                {
                    try
                    {
                        var context = event.getJsonObject(QUERY_CONTEXT);

                        if (event.containsKey("child.query"))
                        {
                            composeSubQueryResponse(event.getLong(QUERY_ID), context, event.getJsonObject(RESULT), event.getString(VISUALIZATION_CATEGORY));
                        }

                        else
                        {
                            composeQueryResponse(event.getJsonObject("column.mappers", new JsonObject()), event.getInteger(QUERY_PROGRESS), event.getJsonObject(RESULT), event.getString(VISUALIZATION_CATEGORY, VisualizationCategory.GRID.getName()), event.getLong(QUERY_ID), event.getLong(VisualizationConstants.SUB_QUERY_ID), context, LOGGER, event.getJsonObject(QUERY_CONTEXT).containsKey(DUMMY_FIELDS) ? event.getJsonObject(QUERY_CONTEXT).getBoolean(DUMMY_FIELDS) : false);
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void composeSubQueryResponse(long queryId, JsonObject context, JsonObject response, String category)
    {
        try
        {
            JsonObject visualizationContext = null;

            var instance = response.getString(INSTANCE);

            var desc = true;

            if (response.containsKey(RESULT) && !response.getJsonArray(RESULT).isEmpty())
            {
                var rows = response.getJsonArray(RESULT);

                var visualizationProperties = context.getJsonObject(VISUALIZATION_PROPERTIES);

                for (var visualizationProperty : visualizationProperties.getMap().values())
                {
                    var value = JsonObject.mapFrom(visualizationProperty);

                    if (value.containsKey(SORTING))
                    {
                        desc = value.getJsonObject(SORTING).getString("order").equalsIgnoreCase("desc");
                    }
                }

                if (category.equalsIgnoreCase(VisualizationCategory.HISTOGRAM.getName()))
                {
                    visualizationContext = new JsonObject(VISUALIZATION_CHART_CONTEXT);

                    visualizationContext.getJsonObject(VISUALIZATION_PROPERTIES).getJsonObject(VisualizationCategory.HISTOGRAM.getName().toLowerCase()).getJsonObject(SORTING).put("order", desc ? "desc" : "asc");

                    visualizationContext.getJsonObject(VISUALIZATION_PROPERTIES).mergeIn(context.getJsonObject(VISUALIZATION_PROPERTIES));
                }

                var entityType = VisualizationConstants.VisualizationGrouping.MONITOR.getName();

                if (context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                {
                    context.remove(VisualizationConstants.VISUALIZATION_RESULT_BY);
                }

                else
                {
                    if (context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationConstants.VisualizationGrouping.GROUP.getName()))
                    {
                        entityType = VisualizationConstants.VisualizationGrouping.GROUP.getName();
                    }

                    else if (context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).contains(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                    {

                        entityType = VisualizationConstants.VisualizationGrouping.TAG.getName();
                    }
                }

                var entities = new HashSet<>();

                var entityKeys = new HashSet<String>();

                for (var i = 0; i < rows.size(); i++)
                {
                    var row = rows.getJsonObject(i);

                    if (!instance.isEmpty() && row.getValue(instance) != null)
                    {
                        entityKeys.add(row.getValue(entityType) + CARET_SEPARATOR + row.getValue(instance));
                    }

                    if (entityType.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()))
                    {
                        var id = ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(row.getValue("monitor")));

                        if (id != NOT_AVAILABLE)
                        {
                            entities.add(id);
                        }
                    }
                    else
                    {
                        entities.add(row.getValue(entityType));
                    }
                }

                if (visualizationContext != null)
                {
                    var dataSources = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                    for (var i = 0; i < dataSources.size(); i++)
                    {
                        var visualizationDataSource = dataSources.getJsonObject(i);

                        visualizationDataSource.remove(ENTITIES);

                        var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                        JsonObject externalGrouping = null;

                        if (!entityType.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.MONITOR.getName()))
                        {
                            externalGrouping = (JsonObject) visualizationDataSource.remove(entityType);

                            visualizationDataSource.put(entityType, new JsonObject());
                        }

                        for (var j = 0; j < dataPoints.size(); j++)
                        {
                            var dataPoint = dataPoints.getJsonObject(j);

                            Map<String, String> keys;

                            dataPoint.remove(ENTITY_KEYS);

                            if (externalGrouping != null)
                            {
                                var externalEntities = new HashSet<>();

                                for (var entity : entities)
                                {
                                    externalEntities.addAll(ObjectConfigStore.getStore().getItemsByObjectIds(externalGrouping.getJsonArray(CommonUtil.getString(entity)).getList()).getList());

                                    visualizationDataSource.getJsonObject(entityType).put(CommonUtil.getString(entity), externalGrouping.getJsonArray(CommonUtil.getString(entity)));
                                }

                                dataPoint.put(ENTITIES, new ArrayList<>(externalEntities));
                            }

                            else
                            {
                                dataPoint.put(ENTITIES, new ArrayList<>(entities));

                            }

                            dataPoint.put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName());

                            if (!entityKeys.isEmpty())
                            {
                                keys = new HashMap<>();

                                for (var plugin : visualizationDataSource.getJsonArray(PLUGINS))
                                {
                                    for (var key : entityKeys)
                                    {
                                        keys.put(key + CARET_SEPARATOR + dataPoint.getString(DATA_POINT), CommonUtil.getString(plugin));
                                    }
                                }

                                dataPoint.put(ENTITY_KEYS, keys);
                            }

                            visualizationDataSource.remove(PLUGINS);
                        }
                    }

                    Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION, visualizationContext.put(VISUALIZATION_TIMELINE, context.getValue(VISUALIZATION_TIMELINE)).put(VISUALIZATION_TYPE_CHILD_VISUALIZATION, context.getString(VISUALIZATION_TYPE_CHILD_VISUALIZATION)).put(UI_EVENT_UUID, context.getString(UI_EVENT_UUID)).put(VISUALIZATION_DATA_SOURCES, context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES)).put(QUERY_ID, queryId));
                }
            }
            else
            {
                VisualizationConstants.send(context.getString(VisualizationConstants.VISUALIZATION_CATEGORY), context.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, context.getLong(ID) > 0 ? context.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", "No Data Found"), queryId, response.getLong(SUB_QUERY_ID), LOGGER, EventBusConstants.EVENT_VISUALIZATION_RESPONSE);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
