{"172.16.14.93": {"result": {"tanzu.kubernetes.daemon.set": [{"tanzu.kubernetes.daemon.set": "default-cadvisor", "tanzu.kubernetes.daemon.set.available.replicas": 2, "tanzu.kubernetes.daemon.set.creation.time": "Mon, Apr 14 2025, 08:11", "tanzu.kubernetes.daemon.set.current.replicas": 2, "tanzu.kubernetes.daemon.set.desired.replicas": 2, "tanzu.kubernetes.daemon.set.name": "cadvisor", "tanzu.kubernetes.daemon.set.namespace": "default", "tanzu.kubernetes.daemon.set.ready.replicas": 2, "tanzu.kubernetes.daemon.set.updated.replicas": 2}, {"tanzu.kubernetes.daemon.set": "kube-system-kube-proxy", "tanzu.kubernetes.daemon.set.available.replicas": 3, "tanzu.kubernetes.daemon.set.creation.time": "Sun, Feb 09 2025, 05:14", "tanzu.kubernetes.daemon.set.current.replicas": 3, "tanzu.kubernetes.daemon.set.desired.replicas": 3, "tanzu.kubernetes.daemon.set.name": "kube-proxy", "tanzu.kubernetes.daemon.set.namespace": "kube-system", "tanzu.kubernetes.daemon.set.node.selector": "kubernetes.io/os=linux", "tanzu.kubernetes.daemon.set.ready.replicas": 3, "tanzu.kubernetes.daemon.set.updated.replicas": 3}, {"tanzu.kubernetes.daemon.set": "kube-system-weave-net", "tanzu.kubernetes.daemon.set.available.replicas": 3, "tanzu.kubernetes.daemon.set.creation.time": "Sun, Feb 09 2025, 05:47", "tanzu.kubernetes.daemon.set.current.replicas": 3, "tanzu.kubernetes.daemon.set.desired.replicas": 3, "tanzu.kubernetes.daemon.set.name": "weave-net", "tanzu.kubernetes.daemon.set.namespace": "kube-system", "tanzu.kubernetes.daemon.set.ready.replicas": 3, "tanzu.kubernetes.daemon.set.updated.replicas": 3}], "tanzu.kubernetes.daemon.sets": 3, "tanzu.kubernetes.deployment": [{"tanzu.kubernetes.deployment": "default-prometheus", "tanzu.kubernetes.deployment.available.replicas": 1, "tanzu.kubernetes.deployment.name": "prometheus", "tanzu.kubernetes.deployment.namespace": "default", "tanzu.kubernetes.deployment.ready.replicas": 1, "tanzu.kubernetes.deployment.total.replicas": 1}, {"tanzu.kubernetes.deployment": "kube-system-coredns", "tanzu.kubernetes.deployment.available.replicas": 2, "tanzu.kubernetes.deployment.name": "coredns", "tanzu.kubernetes.deployment.namespace": "kube-system", "tanzu.kubernetes.deployment.ready.replicas": 2, "tanzu.kubernetes.deployment.total.replicas": 2}, {"tanzu.kubernetes.deployment": "kube-system-metrics-server", "tanzu.kubernetes.deployment.available.replicas": 1, "tanzu.kubernetes.deployment.name": "metrics-server", "tanzu.kubernetes.deployment.namespace": "kube-system", "tanzu.kubernetes.deployment.ready.replicas": 1, "tanzu.kubernetes.deployment.total.replicas": 1}], "tanzu.kubernetes.deployments": 3, "tanzu.kubernetes.replica.set": [{"tanzu.kubernetes.replica.set": "default-prometheus-7f6cdfb75d", "tanzu.kubernetes.replica.set.current.replicas": 1, "tanzu.kubernetes.replica.set.desired.replicas": 1, "tanzu.kubernetes.replica.set.name": "prometheus-7f6cdfb75d", "tanzu.kubernetes.replica.set.namespace": "default", "tanzu.kubernetes.replica.set.ready.replicas": 1}, {"tanzu.kubernetes.replica.set": "kube-system-coredns-7c65d6cfc9", "tanzu.kubernetes.replica.set.current.replicas": 2, "tanzu.kubernetes.replica.set.desired.replicas": 2, "tanzu.kubernetes.replica.set.name": "coredns-7c65d6cfc9", "tanzu.kubernetes.replica.set.namespace": "kube-system", "tanzu.kubernetes.replica.set.ready.replicas": 2}, {"tanzu.kubernetes.replica.set": "kube-system-metrics-server-54bf7cdd6", "tanzu.kubernetes.replica.set.current.replicas": 0, "tanzu.kubernetes.replica.set.desired.replicas": 0, "tanzu.kubernetes.replica.set.name": "metrics-server-54bf7cdd6", "tanzu.kubernetes.replica.set.namespace": "kube-system"}, {"tanzu.kubernetes.replica.set": "kube-system-metrics-server-6794476995", "tanzu.kubernetes.replica.set.current.replicas": 1, "tanzu.kubernetes.replica.set.desired.replicas": 1, "tanzu.kubernetes.replica.set.name": "metrics-server-6794476995", "tanzu.kubernetes.replica.set.namespace": "kube-system", "tanzu.kubernetes.replica.set.ready.replicas": 1}], "tanzu.kubernetes.replica.sets": 4, "tanzu.kubernetes.stateful.set": [{"tanzu.kubernetes.stateful.set": "default-my-statefulset", "tanzu.kubernetes.stateful.set.creation.time": "Mon, Feb 24 2025, 13:36", "tanzu.kubernetes.stateful.set.current.replicas": 1, "tanzu.kubernetes.stateful.set.name": "my-statefulset", "tanzu.kubernetes.stateful.set.namespace": "default", "tanzu.kubernetes.stateful.set.replicas": 1, "tanzu.kubernetes.stateful.set.service": "my-service", "tanzu.kubernetes.stateful.set.updated.replicas": 1}], "tanzu.kubernetes.stateful.sets": 1}}}