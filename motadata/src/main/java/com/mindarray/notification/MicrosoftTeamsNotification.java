/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.notification;

import com.mindarray.*;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Integration;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.integration.TeamsIntegration;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.CredentialProfileConfigStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.WebClientUtil;
import io.vertx.circuitbreaker.CircuitBreaker;
import io.vertx.circuitbreaker.CircuitBreakerOptions;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;

/**
 * Implementation of the Notification abstract class for sending Microsoft Teams notifications.
 * <p>
 * This class handles Microsoft Teams notifications by sending HTTP requests to the
 * Microsoft Teams API. It supports:
 * <ul>
 *   <li>Sending messages to multiple Teams channels in parallel</li>
 *   <li>Circuit breaker pattern to handle Teams service failures</li>
 *   <li>OAuth token authentication with automatic token refresh</li>
 *   <li>Severity-based color coding of messages</li>
 *   <li>Configurable timeout for Teams API requests</li>
 * </ul>
 * <p>
 * The notification process:
 * <ol>
 *   <li>Retrieves Teams integration configuration from the configuration store</li>
 *   <li>Validates that channels are specified and Teams integration is configured</li>
 *   <li>For each channel, retrieves the channel configuration and credential profile</li>
 *   <li>Formats the message with severity-based color coding</li>
 *   <li>Sends HTTP requests to the Teams API for each channel</li>
 *   <li>Handles authentication failures by refreshing OAuth tokens</li>
 *   <li>Tracks successful deliveries and reports results</li>
 * </ol>
 * <p>
 * The notification event should contain:
 * <ul>
 *   <li>{@link PolicyEngineConstants#CHANNELS}: JsonArray of channel IDs to send the message to</li>
 *   <li>{@link Notification#CHANNEL_NOTIFICATION_CONTENT}: The message content in Teams card format</li>
 *   <li>{@link GlobalConstants#SEVERITY} (optional): The severity level for color coding</li>
 * </ul>
 * <p>
 * This implementation uses a circuit breaker to prevent cascading failures when
 * the Microsoft Teams service is experiencing issues.
 */
public class MicrosoftTeamsNotification extends Notification
{
    /**
     * Logger for Microsoft Teams notification operations
     */
    private static final Logger LOGGER = new Logger(MicrosoftTeamsNotification.class, GlobalConstants.MOTADATA_NOTIFICATION, "Microsoft Teams Notification");

    /**
     * Circuit breaker to handle Teams service failures.
     * <p>
     * The circuit breaker:
     * <ul>
     *   <li>Opens after 5 consecutive failures</li>
     *   <li>Considers a request failed if it takes more than 30 seconds</li>
     *   <li>Stays open for 60 seconds before attempting to reset</li>
     *   <li>Tracks failures over a 30-minute rolling window</li>
     *   <li>Calls the fallback handler when open</li>
     * </ul>
     */
    private final CircuitBreaker circuitBreaker = CircuitBreaker.create("teams-service-circuit-breaker", Bootstrap.vertx(),
            new CircuitBreakerOptions()
                    .setMaxFailures(5) // number of failure before opening the circuit
                    .setTimeout(30000) // consider a failure if the operation does not succeed in time
                    .setFallbackOnFailure(true) // do we call the fallback on failure
                    .setResetTimeout(60000) // time spent in open state before attempting to re-try
                    .setFailuresRollingWindow(TimeUnit.MINUTES.toMillis(30))
    ).openHandler(handler ->
    {
        // When circuit opens, notify the UI and log the service failure
        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject().put(STATUS, STATUS_FAIL)
                .put(MESSAGE, ErrorMessageConstants.TEAMS_SERVICE_FAILED)
                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_TEAMS_SERVICE_DOWN));

        LOGGER.warn(ErrorMessageConstants.TEAMS_SERVICE_FAILED);

        // Send a notification to users about the service failure
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject()
                .put(STATUS, STATUS_FAIL)
                .put(EventBusConstants.EVENT_TYPE, NotificationType.MICROSOFT_TEAMS.getName())
                .put(MESSAGE, ErrorMessageConstants.TEAMS_SERVICE_FAILED));

    }).closeHandler(handler ->
    {
        // When circuit closes, notify the UI and log the service restoration
        EventBusConstants.publish(EventBusConstants.UI_NOTIFICATION_SERVICE_HEALTH, new JsonObject()
                .put(STATUS, STATUS_SUCCEED)
                .put(MESSAGE, InfoMessageConstants.TEAMS_SERVICE_RESTORED)
                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS));

        LOGGER.info(InfoMessageConstants.TEAMS_SERVICE_RESTORED);

        // Send a notification to users about the service restoration
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject()
                .put(STATUS, STATUS_SUCCEED)
                .put(EventBusConstants.EVENT_TYPE, NotificationType.MICROSOFT_TEAMS.getName())
                .put(MESSAGE, InfoMessageConstants.TEAMS_SERVICE_RESTORED));

    }).fallback(handler -> handler.initCause(new Exception(ErrorMessageConstants.TEAMS_SERVICE_FAILED)));

    /**
     * Processes and sends Microsoft Teams notifications based on the provided event data.
     * <p>
     * This method:
     * <ul>
     *   <li>Retrieves Teams integration configuration</li>
     *   <li>Validates that channels are specified and Teams integration is configured</li>
     *   <li>For each channel, retrieves the channel configuration and credential profile</li>
     *   <li>Formats the message with severity-based color coding</li>
     *   <li>Sends HTTP requests to the Teams API for each channel</li>
     *   <li>Handles authentication failures by refreshing OAuth tokens</li>
     *   <li>Tracks successful deliveries and reports results</li>
     * </ul>
     * <p>
     * The method uses a circuit breaker to prevent cascading failures when
     * the Microsoft Teams service is experiencing issues.
     *
     * @param event A JsonObject containing the Microsoft Teams notification parameters
     *              (channels, message content, severity, etc.)
     * @return A Promise that resolves to a JsonObject containing the result of the
     * Microsoft Teams notification attempt, including success/failure status and any error details
     */
    @Override
    public Promise<JsonObject> notify(JsonObject event)
    {
        var promise = Promise.<JsonObject>promise();

        // Retrieve Teams integration configuration from the store
        var teams = IntegrationConfigStore.getStore().getItemsByValue(
                Integration.INTEGRATION_TYPE,
                IntegrationConstants.IntegrationType.MICROSOFT_TEAMS.getName()
        ).getJsonObject(0);

        // Execute the Teams sending logic with circuit breaker protection
        circuitBreaker.<JsonObject>execute(future ->
        {
            try
            {
                // Create collections to track futures and successful recipients
                var futures = new ArrayList<Future<Object>>();
                var recipients = new ArrayList<String>();

                // Validate that channels are specified and Teams integration is configured
                if (event.containsKey(PolicyEngineConstants.CHANNELS) &&
                        event.getJsonArray(PolicyEngineConstants.CHANNELS) != null &&
                        teams != null)
                {
                    // Get the list of channels to send the message to
                    var channels = event.getJsonArray(PolicyEngineConstants.CHANNELS);

                    // Process each channel in parallel
                    channels.forEach(channel ->
                    {
                        var result = Promise.promise();
                        futures.add(result.future());

                        // Retrieve the channel configuration
                        var item = IntegrationProfileConfigStore.getStore().getItem(Long.parseLong(channel.toString()));

                        if (item != null)
                        {
                            // Get the channel context and build the Teams API URL
                            var context = item.getJsonObject(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT);
                            var url = teams.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET, EMPTY_VALUE) +
                                    String.format(TeamsIntegration.MESSAGE_END_POINT,
                                            context.getString(TeamsIntegration.TEAM, EMPTY_VALUE),
                                            context.getString(TeamsIntegration.CHANNEL, EMPTY_VALUE));

                            // Get the credential profile for authentication
                            var credentialProfile = CredentialProfileConfigStore.getStore().getItem(
                                    teams.getJsonObject(Integration.INTEGRATION_CONTEXT)
                                            .getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE));

                            // Format the message content with severity-based color coding if severity is specified
                            var content = EMPTY_VALUE;
                            if (event.containsKey(SEVERITY) && !event.getString(SEVERITY).isEmpty())
                            {
                                // Apply color coding based on severity
                                content = new StringSubstitutor(
                                        new JsonObject()
                                                .put("severity.color",
                                                        MicrosoftTeamsColorCode.valueOf((event.getString(SEVERITY).toUpperCase()))
                                                                .getColorCode())
                                                .getMap())
                                        .replace(event.getString(Notification.CHANNEL_NOTIFICATION_CONTENT));
                            }
                            else
                            {
                                // Use the content as-is if no severity is specified
                                content = event.getString(Notification.CHANNEL_NOTIFICATION_CONTENT);
                            }

                            // Send the message to the Teams API
                            WebClientUtil.getWebClient().postAbs(url)
                                    // Set timeout from configuration or default to 60 seconds
                                    .timeout(TimeUnit.SECONDS.toMillis(
                                            teams.getJsonObject(Integration.INTEGRATION_CONTEXT)
                                                    .getLong(GlobalConstants.TIMEOUT, 60L)))
                                    // Set OAuth bearer token for authentication
                                    .bearerTokenAuthentication(
                                            credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT)
                                                    .getString("access_token"))
                                    // Send the message content as JSON
                                    .sendJson(new JsonObject(content))
                                    .onComplete(asyncResponse ->
                                    {
                                        // Handle the HTTP response
                                        if (asyncResponse.succeeded())
                                        {
                                            var httpResponse = asyncResponse.result();

                                            // If the HTTP request was successful with 200 OK or 201 Created status
                                            if (httpResponse.statusCode() == HttpStatus.SC_OK ||
                                                    httpResponse.statusCode() == HttpStatus.SC_CREATED)
                                            {
                                                // Add the channel to the list of successful deliveries
                                                recipients.add(channel.toString());
                                                // Mark this channel's request as complete
                                                result.complete();
                                            }
                                            // If the HTTP request failed with 401 Unauthorized status (token expired)
                                            else if (httpResponse.statusCode() == HttpStatus.SC_UNAUTHORIZED)
                                            {
                                                // Request a new OAuth token
                                                Bootstrap.vertx().eventBus().<String>request(
                                                        EventBusConstants.EVENT_OAUTH_TOKEN_GENERATE,
                                                        credentialProfile,
                                                        new DeliveryOptions().setSendTimeout(15 * 1000L),
                                                        asyncResult ->
                                                        {
                                                            // If token generation was successful
                                                            if (asyncResult.succeeded())
                                                            {
                                                                // Retry the request with the new token
                                                                WebClientUtil.getWebClient().postAbs(url)
                                                                        .timeout(TimeUnit.SECONDS.toMillis(
                                                                                teams.getJsonObject(Integration.INTEGRATION_CONTEXT)
                                                                                        .getLong(GlobalConstants.TIMEOUT, 60L)))
                                                                        .bearerTokenAuthentication(asyncResult.result().body())
                                                                        .sendJson(event.getValue(Notification.CHANNEL_NOTIFICATION_CONTENT),
                                                                                response ->
                                                                                {
                                                                                    // If the retry was successful
                                                                                    if (response.succeeded())
                                                                                    {
                                                                                        // Add the channel to the list of successful deliveries
                                                                                        recipients.add(channel.toString());
                                                                                        // Mark this channel's request as complete
                                                                                        result.complete();
                                                                                    }
                                                                                    // If the retry failed
                                                                                    else
                                                                                    {
                                                                                        // Log the failure
                                                                                        LOGGER.warn("failed to send message after generating token");
                                                                                        // Mark this channel's request as failed
                                                                                        result.fail("failed to send message after generating token");
                                                                                    }
                                                                                });
                                                            }
                                                            // If token generation failed
                                                            else
                                                            {
                                                                // Log the error
                                                                LOGGER.error(asyncResult.cause());
                                                                // Mark this channel's request as failed
                                                                result.fail(asyncResult.cause());
                                                            }
                                                        });
                                            }
                                            // If the HTTP request failed with another status code
                                            else
                                            {
                                                // Log the failure
                                                LOGGER.warn(String.format("invalid status code received in sending message : %s",
                                                        httpResponse.statusCode()));
                                                // Mark this channel's request as failed
                                                result.fail(String.format("invalid status code received in sending message : %s",
                                                        httpResponse.statusCode()));
                                            }
                                        }
                                        // If the HTTP request failed (connection error, timeout, etc.)
                                        else
                                        {
                                            // Log the error
                                            LOGGER.error(asyncResponse.cause());
                                            // Mark this channel's request as failed
                                            result.fail(asyncResponse.cause());
                                        }
                                    });
                        }
                    });

                    // Wait for any of the futures to complete (success or failure)
                    Future.any(futures).onComplete(result ->
                    {
                        // If at least one message was sent successfully
                        if (result.succeeded())
                        {
                            // Mark the notification as successful and include the list of successful recipients
                            future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_SUCCESS)
                                    .put(RESULT, GlobalConstants.STATUS_SUCCEED)
                                    .put(PolicyEngineConstants.RECIPIENTS, recipients));
                        }
                        // If all message sending attempts failed
                        else
                        {
                            // Propagate the failure
                            future.fail(result.cause());
                        }
                    });
                }
                // If no channels are specified or Teams integration is not configured
                else
                {
                    // Mark the notification as failed with a bad request error
                    future.complete(event.put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST));
                }
            }
            // Handle any exceptions that occur during Teams notification processing
            catch (Exception exception)
            {
                // Log the exception
                LOGGER.error(exception);

                // Mark the notification as failed and include error details
                future.complete(event.put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, exception.getMessage())
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
            }
        }).onComplete(result ->
        {
            // Handle the result of the circuit breaker execution
            if (result.succeeded())
            {
                // Complete the promise with the result
                promise.complete(result.result());
            }
            // If the circuit breaker execution failed
            else
            {
                // Complete the promise with a failure status and error details
                promise.complete(event.put(STATUS, STATUS_FAIL)
                        .put(MESSAGE, result.cause().getMessage())
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                        .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
            }
        });

        return promise;
    }

    /**
     * Enumeration of color codes used for Microsoft Teams message cards based on severity.
     * <p>
     * Microsoft Teams adaptive cards support different color themes that can be used
     * to visually indicate the severity of a notification. This enum maps Motadata
     * severity levels to Teams color codes.
     * <p>
     * The color codes are:
     * <ul>
     *   <li>default: Standard color (used for UNKNOWN severity)</li>
     *   <li>accent: Highlighted color (used for MAINTENANCE severity)</li>
     *   <li>good: Green color (used for CLEAR severity)</li>
     *   <li>warning: Yellow/orange color (used for WARNING and MAJOR severity)</li>
     *   <li>attention: Red color (used for CRITICAL, UNREACHABLE, and DOWN severity)</li>
     * </ul>
     */
    public enum MicrosoftTeamsColorCode
    {
        /**
         * Standard color for unknown severity
         */
        UNKNOWN("default"),

        /**
         * Highlighted color for maintenance notifications
         */
        MAINTENANCE("accent"),

        /**
         * Green color for cleared/resolved notifications
         */
        CLEAR("good"),

        /**
         * Yellow/orange color for warning notifications
         */
        WARNING("warning"),

        /**
         * Yellow/orange color for major notifications
         */
        MAJOR("warning"),

        /**
         * Red color for critical notifications
         */
        CRITICAL("attention"),

        /**
         * Red color for unreachable notifications
         */
        UNREACHABLE("attention"),

        /**
         * Red color for down notifications
         */
        DOWN("attention");

        /**
         * The Teams color code value
         */
        private final String colorCode;

        /**
         * Constructs a MicrosoftTeamsColorCode with the specified color code.
         *
         * @param colorCode The Teams color code value
         */
        MicrosoftTeamsColorCode(String colorCode)
        {
            this.colorCode = colorCode;
        }

        /**
         * Gets the Teams color code value.
         *
         * @return The color code string used in Teams message cards
         */
        public String getColorCode()
        {
            return colorCode;
        }
    }
}
