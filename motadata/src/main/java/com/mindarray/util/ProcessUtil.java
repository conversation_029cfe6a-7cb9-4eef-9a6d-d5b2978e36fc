/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.BootstrapAgent;
import com.mindarray.GlobalConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.Agent;
import com.mindarray.eventbus.EventBusConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;

public final class ProcessUtil
{
    private static final Logger LOGGER = new Logger(ProcessUtil.class, GlobalConstants.MOTADATA_UTIL, "Process Util");
    private static final Map<String, Process> processes = new ConcurrentHashMap<>();
    private static int processors;

    private ProcessUtil()
    {
    }

    public static Map<String, Process> getProcesses()
    {
        return processes;
    }

    public static int getProcessors()
    {
        return processors;
    }

    public static void setProcessors()
    {
        try
        {
            processors = Runtime.getRuntime().availableProcessors();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static Future<Process> start(String processName, List<String> arguments, String workingDir)
    {
        var result = Promise.<Process>promise();

        Bootstrap.vertx().<Process>executeBlocking(future ->
                {

                    try
                    {
                        var processBuilder = new ProcessBuilder(arguments);

                        processBuilder.directory(new File(workingDir));

                        var process = processBuilder.start();

                        if (process.isAlive())
                        {
                            if (processes.containsKey(processName))
                            {
                                processes.get(processName).destroy();
                            }

                            processes.put(processName, process);

                            LOGGER.info(String.format("%s process id is %s", processName, process.pid()));

                            future.complete(process);

                        }
                        else
                        {
                            future.fail(String.format("process not exist for %s", arguments));
                        }

                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        future.fail(exception.getCause());
                    }

                }

                , false, asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        result.complete(asyncResult.result());
                    }

                    else
                    {

                        result.fail(asyncResult.cause());
                    }
                });


        return result.future();
    }

    /*
     *  when process is started as daemon process, isAlive does not able to identify process state, hence removing isALive
     *  This behavior occurs because when a process is demonized, it effectively detaches from the parent process, meaning the Java process no longer has control or awareness of its state. The JVM's Process object is tied to the original child process it started. Once that process exits or detaches, the Process object will consider it terminated, leading process.isAlive() to return false.
     * */
    public static Future<Process> startDaemon(String processName, List<String> arguments, String workingDir)
    {
        var result = Promise.<Process>promise();

        Bootstrap.vertx().<Process>executeBlocking(future ->
                {

                    try
                    {
                        var processBuilder = new ProcessBuilder(arguments);

                        processBuilder.directory(new File(workingDir));

                        var process = processBuilder.start();

                        if (processes.containsKey(processName))
                        {
                            processes.get(processName).destroy();
                        }

                        processes.put(processName, process);

                        LOGGER.info(String.format("%s process id is %s", processName, process.pid()));

                        future.complete(process);
                    }

                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        future.fail(exception.getCause());
                    }

                }

                , false, asyncResult ->
                {

                    if (asyncResult.succeeded())
                    {
                        result.complete(asyncResult.result());
                    }

                    else
                    {

                        result.fail(asyncResult.cause());
                    }
                });


        return result.future();
    }

    public static void start(String agent, String eventType)
    {
        var arguments = new ArrayList<String>();

        var startFlag = false;

        try
        {
            var agentStatus = "$.agent.status".replace("$", agent.equalsIgnoreCase(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName()) ? AgentConstants.Agent.LOG.getName() : agent);

            startFlag = AgentConfigUtil.getAgentEnableStatus(agentStatus).equalsIgnoreCase(YES);

            LOGGER.info(String.format("%s agent start request flag is %s", agent, startFlag));

            if (startFlag && !processes.containsKey(agent))
            {
                var workingDir = CURRENT_DIR + PATH_SEPARATOR;

                if (GlobalConstants.OS_WINDOWS)
                {
                    if (agent.equalsIgnoreCase(AgentConstants.Agent.METRIC.getName()))
                    {
                        workingDir = CURRENT_DIR + PATH_SEPARATOR + "motadata-metric-agent" + PATH_SEPARATOR;

                        arguments.add("cmd.exe");

                        arguments.add("/C");

                        arguments.add("motadata-metric-agent.exe");
                    }
                    else if (agent.equalsIgnoreCase(AgentConstants.Agent.LOG.getName()))
                    {
                        arguments.add("motadata-log-agent.exe");
                    }
                    else if (agent.equalsIgnoreCase(AgentConstants.Agent.PACKET.getName()))
                    {
                        arguments.add("motadata-packet-agent.exe");
                    }
                    else if (agent.equalsIgnoreCase(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName()))
                    {
                        arguments.add("motadata-eventlog-agent.exe");
                    }
                }
                else
                {
                    if (agent.equalsIgnoreCase(AgentConstants.Agent.METRIC.getName()))
                    {
                        arguments.add("./motadata-metric-agent");
                    }
                    else if (agent.equalsIgnoreCase(AgentConstants.Agent.LOG.getName()))
                    {
                        arguments.add("./motadata-log-agent");
                    }
                    else if (agent.equalsIgnoreCase(AgentConstants.Agent.PACKET.getName()))
                    {
                        arguments.add("./motadata-packet-agent");
                    }
                }

                if (!arguments.isEmpty())
                {
                    start(agent, arguments, workingDir).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            //will send agent start success message to motadata server with configurations
                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(AgentConstants.AGENT_TYPE, agent)
                                    .put(EventBusConstants.EVENT_TYPE, eventType).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED));
                        }
                        else
                        {
                            LOGGER.fatal(String.format("failed to start %s agent with reason %s", agent, result.cause().getMessage()));

                            //will send agent start error message to motadata server with reason
                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject().put(GlobalConstants.MESSAGE, result.cause().getMessage())
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())).put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(AgentConstants.AGENT_TYPE, agent)
                                    .put(EventBusConstants.EVENT_TYPE, eventType).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static void killProcess(ProcessHandle process)
    {
        try
        {
            process.descendants().forEach(ProcessUtil::killProcess);

            process.destroy();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static boolean stop(String agent)
    {
        var valid = false;

        if (processes.containsKey(agent))
        {
            killProcess(processes.remove(agent).toHandle());

            valid = true;
        }

        if (agent.equalsIgnoreCase(AgentConstants.Agent.LOG.getName()) && processes.containsKey(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName()))
        {
            killProcess(processes.remove(AgentConstants.Agent.WINDOWS_EVENT_LOG.getName()).toHandle());

            valid = true;
        }

        return valid;
    }

    //Not usable so commenting will be used in future
    /*public static Promise<JsonObject> getMetrics(long processId)
    {
        var result =Promise.<JsonObject>promise();

        Bootstrap.vertx().<JsonObject>executeBlocking(future->
                {

                    try
                    {

                        future.complete(new JsonObject());

                    }

                    catch (Exception exception)
                    {
                        logger.error(exception);

                        future.fail(exception.getCause());
                    }

                }

                ,false,promiseResult->
                {

                    if(promiseResult.succeeded())
                    {
                        result.complete(promiseResult.result());
                    }

                    else
                    {

                        result.fail(promiseResult.cause());
                    }
                });



        return result;
    }

    public static Promise<Long> getId(String processName,String commandline)
    {
        var result =Promise.<Long>promise();

        Bootstrap.vertx().<Long>executeBlocking(future->
                {

                    try
                    {
                        long processId =0;

                        future.complete(processId);



                    }

                    catch (Exception exception)
                    {
                        logger.error(exception);

                        future.fail(exception.getCause());
                    }

                }

                ,false,promiseResult->
                {

                    if(promiseResult.succeeded())
                    {
                        result.complete(promiseResult.result());
                    }

                    else
                    {

                        result.fail(promiseResult.cause());
                    }
                });



        return result;
    }

    public static  void kill(String processName)
    {
        if(processes.containsKey(processName))
        {

            processes.get(processName).destroyForcibly();

        }
    }*/
}
