/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		<PERSON><PERSON> Singh		MOTADATA-5642: Agent Cache File Backlog Clearance Mechanism
 */

package com.mindarray.agent;

import com.mindarray.BootstrapAgent;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Agent;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.AgentConfigUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.PortUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.xerial.snappy.Snappy;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mindarray.GlobalConstants.VALUE_SEPARATOR_WITH_ESCAPE;
import static com.mindarray.agent.AgentConstants.AGENT_TYPE;

/**
 * The AgentCacheManager class is responsible for managing the cache files generated by agents.
 * <p>
 * This verticle handles the following operations:
 * <ul>
 *   <li>Periodically checking and processing cached agent data files</li>
 *   <li>Sending cached data to the Motadata server when connectivity is restored</li>
 *   <li>Managing cache directory size by implementing cleanup strategies</li>
 *   <li>Chunking large files for efficient processing</li>
 * </ul>
 * <p>
 * The cache manager ensures that agent data is not lost during network outages by storing it locally
 * and forwarding it when connectivity is restored. It also implements size-based cleanup to prevent
 * disk space issues.
 */
public class AgentCacheManager extends AbstractVerticle
{
    /**
     * The folder where agent cache files are stored
     */
    public static final File CACHE_FOLDER = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "cache");

    /**
     * The number of chunks to split cache files into for processing
     */
    public static final int CACHE_FILE_CHUNKS = AgentConfigUtil.getAgentCacheFileChunks();

    /**
     * Maximum allowed size of the cache directory in bytes
     */
    public static final long CACHE_DIRECTORY_MAX_SIZE_BYTES = AgentConfigUtil.getAgentCacheDirectoryMaxSizeBytes();

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(AgentCacheManager.class, GlobalConstants.MOTADATA_AGENT, "Agent Cache Manager");

    /**
     * Map to track cache files by agent, with file paths and timestamps
     */
    private final Map<String, Map<String, Long>> cacheFiles = new HashMap<>();  // agent -> (absoluteFilePath -> timestamp)

    /**
     * Initializes the AgentCacheManager and sets up a periodic timer to process cache files.
     * <p>
     * This method:
     * <ul>
     *   <li>Sets up a periodic timer to check and process cache files</li>
     *   <li>Checks connectivity to the Motadata server</li>
     *   <li>Processes cache files when connectivity is available</li>
     *   <li>Manages cache directory size by implementing cleanup strategies</li>
     * </ul>
     * <p>
     * The method handles different scenarios:
     * <ul>
     *   <li>When server is reachable: Processes files normally</li>
     *   <li>When server is not reachable: Manages cache size to prevent disk space issues</li>
     * </ul>
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.setPeriodic(AgentConfigUtil.getAgentCacheFlushTimerSeconds(), timer ->
        {
            try
            {
                var files = CACHE_FOLDER.listFiles();

                if (files != null && files.length > 0)
                {
                    if (PortUtil.isConnected(CommonUtil.getRemoteEventPublisher(), CommonUtil.getEventSubscriberPort(), 3))
                    {
                        LOGGER.debug("motadata server is up...");

                        populate(files); // preparing cacheFiles map

                        for (var entry : cacheFiles.entrySet())
                        {
                            var sortedFiles = new ArrayList<>(entry.getValue().entrySet()); // absolute filepath -> timestamp

                            sortedFiles.sort(Map.Entry.comparingByValue());

                            if (FileUtils.sizeOfDirectory(CACHE_FOLDER) < CACHE_DIRECTORY_MAX_SIZE_BYTES)
                            {
                                processFiles(sortedFiles, entry.getKey(), false);
                            }
                            else
                            {
                                processFiles(sortedFiles, entry.getKey(), true);

                                LOGGER.debug(String.format("Cache folder size crossed max threshold %s, Processing %s files at a time", CACHE_DIRECTORY_MAX_SIZE_BYTES, sortedFiles.size() / CACHE_FILE_CHUNKS));
                            }
                        }
                    }
                    else
                    {
                        LOGGER.warn("motadata server is not reachable...");

                        if (FileUtils.sizeOfDirectory(CACHE_FOLDER) > AgentConfigUtil.getAgentCacheFileMaxSizeBytes())
                        {
                            LOGGER.info(String.format("%s size is greater than %s bytes", CACHE_FOLDER.getAbsolutePath(), AgentConfigUtil.getAgentCacheFileMaxSizeBytes()));

                            populate(files); // preparing cacheFiles map

                            clear(); // clear cache files till
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
            finally
            {
                cacheFiles.clear();
            }
        });

        promise.complete();
    }

    /**
     * Processes cached files by reading and uncompressing their content,
     * and deleting the file afterward. Supports optional chunking for partial processing.
     *
     * @param sortedFiles List of cache files with their timestamps
     * @param agent       Agent metadata to include in the event
     * @param chunking    Whether to process only a portion of the files
     */
    private void processFiles(List<Map.Entry<String, Long>> sortedFiles, String agent, boolean chunking)
    {
        try
        {
            var chunkSize = chunking ? (sortedFiles.size() / CACHE_FILE_CHUNKS) : sortedFiles.size();

            for (var i = 0; i < chunkSize; i++)
            {
                var cacheFile = sortedFiles.get(i).getKey();

                if (vertx.fileSystem().existsBlocking(cacheFile))
                {
                    try
                    {
                        var bytes = vertx.fileSystem().readFileBlocking(cacheFile).getBytes();

                        if (bytes.length > 0)
                        {
                            var values = new String(Snappy.uncompress(bytes)).split(VALUE_SEPARATOR_WITH_ESCAPE);

                            for (var value : values)
                            {
                                vertx.eventBus().send(EventBusConstants.EVENT_AGENT, new JsonObject()
                                        .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESPONSE_STREAM)
                                        .put(Agent.AGENT_UUID, BootstrapAgent.getAgentUUID()).put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                        .put(AGENT_TYPE, agent).put(EventBusConstants.EVENT_CONTEXT, Snappy.compress(value)));
                            }

                            vertx.fileSystem().deleteBlocking(cacheFile);

                            LOGGER.debug(String.format("cache file %s is deleted", cacheFile));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.info(exception);
        }
    }

    /**
     * Prepares the in-memory cache file index by parsing and organizing valid cache files.
     * Process files with the naming format: {agent}-{?}-{timestamp}.cache
     *
     * @param files files
     */
    public void populate(File[] files)
    {
        try
        {
            for (var file : files)
            {
                var tokens = file.getName().split("-", 4); // Split max 3 times

                if (tokens.length < 3 && !AgentConstants.agents.contains(tokens[0])) continue; // Skip invalid files

                cacheFiles.computeIfAbsent(tokens[0], value -> new HashMap<>()).putIfAbsent(file.getAbsolutePath(), CommonUtil.getLong(tokens[2].replace(".cache", "")));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Clears cache files in chronological order (The oldest first) until the total cache directory size
     * falls below the configured maximum threshold.
     */
    public void clear()
    {
        try
        {
            var finished = false;

            for (var agent : cacheFiles.entrySet())
            {
                if (!cacheFiles.get(agent.getKey()).isEmpty())
                {
                    var sortedFiles = new ArrayList<>(agent.getValue().entrySet());

                    sortedFiles.sort(Map.Entry.comparingByValue());

                    for (var sortedFile : sortedFiles)
                    {
                        var file = sortedFile.getKey();

                        if (file != null)
                        {
                            vertx.fileSystem().deleteBlocking(file);

                            cacheFiles.get(agent.getKey()).remove(file);

                            LOGGER.info(String.format("cache file %s is deleted....", file));

                            if (FileUtils.sizeOfDirectory(CACHE_FOLDER) < AgentConfigUtil.getAgentCacheFileMaxSizeBytes())
                            {
                                finished = true;

                                break;
                            }
                        }
                    }
                }

                if (finished)
                {
                    break;
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
